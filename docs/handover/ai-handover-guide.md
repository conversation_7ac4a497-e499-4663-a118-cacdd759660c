# 🤖 PoorLow Tools AI开发交接文档

> 版本：v1.0  
> 更新时间：2025-07-19  
> 项目负责人：全栈工程师  
> 目标受众：AI辅助开发工具

## 📋 目录

1. [项目概述](#1-项目概述)
2. [技术架构](#2-技术架构)
3. [已完成工具清单](#3-已完成工具清单)
4. [标准化工具开发流程](#4-标准化工具开发流程)
5. [UI交互和质量标准](#5-ui交互和质量标准)
6. [代码规范和模板](#6-代码规范和模板)
7. [常见模式和解决方案](#7-常见模式和解决方案)
8. [测试和验证](#8-测试和验证)
9. [常见问题FAQ](#9-常见问题faq)
10. [附录：快速参考](#10-附录快速参考)

---

## 1. 项目概述

### 1.1 项目基本信息

**项目名称**：PoorLow Tools  
**项目描述**：全球化在线工具站，提供100+实用工具  
**开发语言**：TypeScript + React  
**框架版本**：Next.js 14 (App Router)  
**UI框架**：TailwindCSS + shadcn/ui  
**当前状态**：已完成100个工具开发  

### 1.2 项目目录结构

```
poorlow-tools/
├── src/
│   ├── app/                      # Next.js App Router页面
│   ├── components/              
│   │   ├── ui/                  # shadcn/ui基础组件
│   │   ├── layout/              # 布局组件
│   │   └── tools/               # 工具组件（重要：所有工具在此）
│   ├── lib/                     
│   │   ├── utils.ts             # 工具函数
│   │   └── tools/registry.ts    # 工具注册表
│   ├── hooks/                   # 自定义Hooks
│   ├── types/                   # TypeScript类型定义
│   └── i18n/                    # 国际化配置
├── public/                      # 静态资源
└── docs/                        # 项目文档
```

### 1.3 核心技术栈

```json
{
  "dependencies": {
    "next": "14.0.4",
    "react": "^18",
    "typescript": "^5",
    "tailwindcss": "^3.3.0",
    "@radix-ui/react-*": "最新版",
    "lucide-react": "^0.309.0",
    "clsx": "^2.1.0",
    "tailwind-merge": "^2.2.0"
  }
}
```

---

## 2. 技术架构

### 2.1 组件架构

```
工具页面 (src/app/tools/[id]/page.tsx)
    ↓
工具包装器 (ToolWrapper.tsx)
    ↓
具体工具组件 (如: Base64Tool.tsx)
    ↓
UI组件 (Button, Card, Input等)
```

### 2.2 工具注册系统

每个工具都需要在 `src/lib/tools/registry.ts` 中注册：

```typescript
export const tools = [
  {
    id: 'base64',
    name: 'Base64编解码',
    description: 'Base64字符串编码和解码工具',
    category: 'dev',
    icon: 'FileText',
    component: lazy(() => import('@/components/tools/Base64Tool'))
  },
  // ... 其他工具
];
```

### 2.3 路由结构

- 首页：`/`
- 工具页面：`/tools/[id]`
- 搜索页面：`/search`

---

## 3. 已完成工具清单

### 3.1 分类统计

| 分类 | 数量 | 说明 |
|------|------|------|
| 文本处理 | 15个 | 字数统计、文本对比、编码转换等 |
| 图像处理 | 10个 | 图片压缩、格式转换、水印等 |
| 开发工具 | 20个 | JSON格式化、代码压缩、正则测试等 |
| 加密安全 | 10个 | MD5、SHA、AES、RSA等 |
| 计算工具 | 10个 | 单位换算、科学计算器、统计等 |
| 网络工具 | 6个 | IP查询、端口扫描、DNS查询等 |
| 文件转换 | 10个 | PDF转换、文档转换、批量重命名等 |
| 生活助手 | 11个 | BMI计算、天气预报、倒计时等 |
| 生产力工具 | 5个 | 番茄钟、任务清单、笔记本等 |
| 数据分析 | 3个 | 图表生成、数据可视化、统计等 |

### 3.2 工具命名规范

工具文件名：`[工具名]Tool.tsx`  
示例：`Base64Tool.tsx`, `JsonFormatterTool.tsx`

---

## 4. 标准化工具开发流程

### 4.1 开发新工具的标准步骤

#### 步骤1：创建工具组件文件

位置：`src/components/tools/[工具名]Tool.tsx`

```typescript
'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Copy, Download, Upload } from 'lucide-react';

export default function [工具名]Tool() {
  // 1. 状态定义
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [error, setError] = useState('');

  // 2. 核心功能实现
  const handle[操作名] = () => {
    try {
      setError('');
      // 工具核心逻辑
      const result = // ... 处理逻辑
      setOutput(result);
    } catch (err) {
      setError('处理失败：' + err.message);
    }
  };

  // 3. 辅助功能
  const handleCopy = () => {
    navigator.clipboard.writeText(output);
    // 可选：显示复制成功提示
  };

  const handleClear = () => {
    setInput('');
    setOutput('');
    setError('');
  };

  // 4. UI渲染
  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-4">
      {/* 工具说明卡片 */}
      <Card>
        <CardHeader>
          <CardTitle>[工具中文名]</CardTitle>
          <CardDescription>
            [工具详细描述，说明功能和使用方法]
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 输入区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">输入</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="input">请输入[内容类型]</Label>
            <Textarea
              id="input"
              placeholder="在此输入..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="min-h-[200px] font-mono"
            />
          </div>
          
          <div className="flex gap-2">
            <Button onClick={handle[操作名]}>
              [操作名称]
            </Button>
            <Button variant="outline" onClick={handleClear}>
              清空
            </Button>
          </div>

          {error && (
            <div className="text-sm text-red-500">{error}</div>
          )}
        </CardContent>
      </Card>

      {/* 输出区域 */}
      {output && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">输出结果</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
              >
                <Copy className="h-4 w-4 mr-1" />
                复制
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Textarea
              value={output}
              readOnly
              className="min-h-[200px] font-mono"
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
```

#### 步骤2：注册工具

在 `src/lib/tools/registry.ts` 中添加：

```typescript
{
  id: '[工具id]',          // 唯一标识，用于URL
  name: '[工具名称]',       // 显示名称
  description: '[工具描述]', // 简短描述
  category: '[分类]',       // text/image/dev/crypto/calc/network/convert/life/productivity/data
  icon: '[图标名]',         // Lucide图标名称
  component: lazy(() => import('@/components/tools/[工具名]Tool'))
}
```

#### 步骤3：测试工具

1. 启动开发服务器：`npm run dev`
2. 访问：`http://localhost:3000/tools/[工具id]`
3. 测试所有功能是否正常

---

## 5. UI交互和质量标准

### 5.1 UI组件使用规范

#### 5.1.1 布局规范
- 最大宽度：`max-w-4xl`
- 内边距：`p-4`
- 组件间距：`space-y-4`
- 移动端适配：使用响应式类名

#### 5.1.2 常用组件

**卡片组件**：
```typescript
<Card>
  <CardHeader>
    <CardTitle>标题</CardTitle>
    <CardDescription>描述</CardDescription>
  </CardHeader>
  <CardContent>
    {/* 内容 */}
  </CardContent>
</Card>
```

**按钮组件**：
```typescript
<Button>主要按钮</Button>
<Button variant="outline">次要按钮</Button>
<Button variant="ghost">幽灵按钮</Button>
<Button variant="destructive">危险按钮</Button>
```

**输入组件**：
```typescript
<Input type="text" placeholder="输入文本" />
<Textarea placeholder="多行文本" className="min-h-[200px]" />
<Select>
  <SelectTrigger>
    <SelectValue placeholder="选择选项" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="1">选项1</SelectItem>
  </SelectContent>
</Select>
```

### 5.2 交互标准

#### 5.2.1 状态管理
- 使用 `useState` 管理本地状态
- 错误状态要有明确提示
- 加载状态使用 loading 按钮或进度条

#### 5.2.2 用户反馈
- 操作成功：显示结果或成功提示
- 操作失败：显示错误信息
- 复制成功：可选toast提示

#### 5.2.3 功能按钮
- 主要操作：使用默认按钮样式
- 清空/重置：使用outline样式
- 危险操作：使用destructive样式
- 图标按钮：图标在文字左侧，间距`mr-1`或`mr-2`

### 5.3 质量要求

#### 5.3.1 功能完整性
- [ ] 核心功能正常工作
- [ ] 错误处理完善
- [ ] 边界情况考虑周全
- [ ] 支持复制/清空等辅助功能

#### 5.3.2 用户体验
- [ ] 界面简洁清晰
- [ ] 操作流程顺畅
- [ ] 响应速度快速
- [ ] 移动端适配良好

#### 5.3.3 代码质量
- [ ] TypeScript类型完整
- [ ] 代码结构清晰
- [ ] 注释适当
- [ ] 无console.log等调试代码

---

## 6. 代码规范和模板

### 6.1 命名规范

| 类型 | 规范 | 示例 |
|------|------|------|
| 组件文件 | PascalCase + Tool.tsx | `Base64Tool.tsx` |
| 函数名 | camelCase | `handleEncode`, `processData` |
| 状态变量 | camelCase | `inputText`, `isLoading` |
| 常量 | UPPER_SNAKE_CASE | `MAX_FILE_SIZE` |
| CSS类名 | kebab-case | `tool-container` |

### 6.2 常用代码片段

#### 6.2.1 文件上传处理
```typescript
const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
  const file = event.target.files?.[0];
  if (!file) return;
  
  if (file.size > 10 * 1024 * 1024) { // 10MB限制
    setError('文件大小不能超过10MB');
    return;
  }
  
  const reader = new FileReader();
  reader.onload = (e) => {
    const content = e.target?.result as string;
    setInput(content);
  };
  reader.readAsText(file);
};
```

#### 6.2.2 复制到剪贴板
```typescript
const handleCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    // 可选：显示成功提示
  } catch (err) {
    setError('复制失败');
  }
};
```

#### 6.2.3 下载文件
```typescript
const handleDownload = (content: string, filename: string) => {
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
  URL.revokeObjectURL(url);
};
```

#### 6.2.4 防抖处理
```typescript
import { useCallback, useRef } from 'react';

const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  return useCallback((...args: any[]) => {
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};
```

### 6.3 TypeScript类型定义

```typescript
// 工具选项类型
interface ToolOptions {
  encoding?: 'utf-8' | 'base64' | 'hex';
  format?: 'json' | 'xml' | 'csv';
  algorithm?: 'md5' | 'sha1' | 'sha256';
}

// 处理结果类型
interface ProcessResult {
  success: boolean;
  data?: string;
  error?: string;
}

// 文件信息类型
interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}
```

---

## 7. 常见模式和解决方案

### 7.1 双向转换工具模式

适用于：编码/解码、加密/解密、格式转换等

```typescript
export default function ConverterTool() {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [mode, setMode] = useState<'encode' | 'decode'>('encode');
  
  const handleConvert = () => {
    if (mode === 'encode') {
      // 编码逻辑
    } else {
      // 解码逻辑
    }
  };
  
  const handleSwap = () => {
    setInput(output);
    setOutput(input);
    setMode(mode === 'encode' ? 'decode' : 'encode');
  };
  
  return (
    // UI with mode toggle and swap button
  );
}
```

### 7.2 实时处理模式

适用于：文本统计、格式化、预览等

```typescript
export default function RealtimeTool() {
  const [input, setInput] = useState('');
  
  // 实时计算结果
  const result = useMemo(() => {
    return processInput(input);
  }, [input]);
  
  return (
    // UI showing real-time results
  );
}
```

### 7.3 批量处理模式

适用于：批量转换、批量重命名等

```typescript
export default function BatchTool() {
  const [items, setItems] = useState<Item[]>([]);
  const [processing, setProcessing] = useState(false);
  
  const handleBatchProcess = async () => {
    setProcessing(true);
    const results = [];
    
    for (const item of items) {
      const result = await processItem(item);
      results.push(result);
    }
    
    setProcessing(false);
    // 显示结果
  };
  
  return (
    // UI with progress indicator
  );
}
```

### 7.4 配置型工具模式

适用于：生成器、计算器等需要多个参数的工具

```typescript
export default function ConfigurableTool() {
  const [config, setConfig] = useState({
    option1: 'default',
    option2: true,
    option3: 10
  });
  
  const updateConfig = (key: string, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };
  
  const handleGenerate = () => {
    const result = generateWithConfig(config);
    setOutput(result);
  };
  
  return (
    // UI with multiple config options
  );
}
```

---

## 8. 测试和验证

### 8.1 功能测试清单

每个工具都应该通过以下测试：

- [ ] **基本功能测试**
  - 正常输入产生正确输出
  - 空输入的处理
  - 边界值测试

- [ ] **错误处理测试**
  - 无效输入显示错误提示
  - 大文件/长文本处理
  - 特殊字符处理

- [ ] **UI交互测试**
  - 所有按钮可点击
  - 复制功能正常
  - 清空功能正常
  - 文件上传（如有）正常

- [ ] **响应式测试**
  - 移动端显示正常
  - 平板端显示正常
  - 桌面端显示正常

### 8.2 性能要求

- 处理时间：一般操作 < 1秒
- 大文件处理：显示进度或loading状态
- 内存使用：避免内存泄漏
- 响应性：UI不应冻结

### 8.3 兼容性要求

- 浏览器：Chrome, Firefox, Safari, Edge最新版
- 设备：支持触屏操作
- 屏幕：320px到4K分辨率

---

## 9. 常见问题FAQ

### Q1: 如何添加新的工具分类？

A: 在 `src/lib/tools/registry.ts` 中的分类列表添加新分类，并确保工具注册时使用正确的分类ID。

### Q2: 如何处理大文件？

A: 使用分块处理或Web Worker，避免阻塞主线程：
```typescript
// 分块处理示例
const CHUNK_SIZE = 1024 * 1024; // 1MB
for (let i = 0; i < file.size; i += CHUNK_SIZE) {
  const chunk = file.slice(i, i + CHUNK_SIZE);
  // 处理chunk
}
```

### Q3: 如何添加国际化支持？

A: 目前项目使用中文界面，如需国际化：
1. 使用 `useTranslations` hook
2. 在 `public/locales` 添加翻译文件
3. 使用翻译key替代硬编码文本

### Q4: 如何优化工具性能？

A: 
1. 使用 `useMemo` 缓存计算结果
2. 使用 `useCallback` 缓存函数
3. 大数据使用虚拟滚动
4. 复杂计算考虑Web Worker

### Q5: 如何处理工具的持久化存储？

A: 使用 localStorage 保存用户数据：
```typescript
// 保存
localStorage.setItem('tool-data', JSON.stringify(data));

// 读取
const saved = localStorage.getItem('tool-data');
if (saved) {
  const data = JSON.parse(saved);
}
```

---

## 10. 附录：快速参考

### 10.1 常用导入

```typescript
// UI组件
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

// 图标
import { Copy, Download, Upload, RefreshCw, Trash2, Save, FileText, Image, Code, Lock, Calculator, Network, FileType, Heart, BarChart } from 'lucide-react';

// React
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
```

### 10.2 工具分类对照表

| 分类ID | 中文名 | 图标建议 | 说明 |
|--------|--------|----------|------|
| text | 文本处理 | FileText | 文本相关操作 |
| image | 图像处理 | Image | 图片相关操作 |
| dev | 开发工具 | Code | 编程开发相关 |
| crypto | 加密安全 | Lock | 加密解密相关 |
| calc | 计算工具 | Calculator | 各类计算器 |
| network | 网络工具 | Network | 网络相关工具 |
| convert | 文件转换 | FileType | 格式转换相关 |
| life | 生活助手 | Heart | 日常生活工具 |
| productivity | 生产力 | BarChart | 效率工具 |
| data | 数据分析 | BarChart | 数据处理分析 |

### 10.3 Git提交规范

```bash
# 添加新工具
git commit -m "feat: 添加[工具名]工具"

# 修复bug
git commit -m "fix: 修复[工具名]的[问题描述]"

# 优化
git commit -m "perf: 优化[工具名]的性能"

# 文档
git commit -m "docs: 更新[工具名]的使用说明"
```

### 10.4 开发流程总结

1. **创建组件** → `src/components/tools/[工具名]Tool.tsx`
2. **注册工具** → `src/lib/tools/registry.ts`
3. **本地测试** → `npm run dev` → 访问 `/tools/[id]`
4. **提交代码** → 遵循Git规范
5. **更新文档** → 如有必要

---

## 结语

本文档为AI辅助开发PoorLow Tools项目提供了完整的指导。项目已经建立了成熟的开发模式和规范，新增工具只需要遵循既定的模式即可快速开发。

如有任何疑问，请参考已有的100个工具实现，它们涵盖了几乎所有的开发模式和最佳实践。

祝开发顺利！🚀
