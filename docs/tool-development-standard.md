# 📐 PoorLow Tools 标准化工具开发流程

> 统一的工具开发规范和质量标准  
> 版本：v1.0 | 更新时间：2025-07-19

## 🎯 开发流程概览

```mermaid
graph LR
    A[需求分析] --> B[创建组件]
    B --> C[实现功能]
    C --> D[UI设计]
    D --> E[注册工具]
    E --> F[测试验证]
    F --> G[提交代码]
```

## 📋 标准开发流程

### 第1步：需求分析（5分钟）

在开始编码前，明确以下问题：

- [ ] **工具用途**：解决什么具体问题？
- [ ] **目标用户**：开发者/设计师/普通用户？
- [ ] **核心功能**：必须实现的功能有哪些？
- [ ] **辅助功能**：复制/下载/清空/导入等
- [ ] **错误场景**：可能出现的错误情况？
- [ ] **性能要求**：是否处理大文件/大数据？

### 第2步：创建工具组件（10分钟）

#### 2.1 文件命名
```
src/components/tools/[工具名]Tool.tsx
```

示例：
- ✅ `Base64Tool.tsx`
- ✅ `JsonFormatterTool.tsx`
- ❌ `base64-tool.tsx`（错误：使用kebab-case）
- ❌ `Base64.tsx`（错误：缺少Tool后缀）

#### 2.2 基础结构
```typescript
'use client';

import React, { useState, useEffect, useMemo } from 'react';
// UI组件导入
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
// 图标导入
import { Copy, Download, Upload, RefreshCw, Trash2 } from 'lucide-react';

export default function [工具名]Tool() {
  // 状态定义
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  
  // 配置状态（如需要）
  const [options, setOptions] = useState({
    // 工具特定的配置项
  });

  // 核心功能实现
  const handle[主要操作] = async () => {
    setLoading(true);
    setError('');
    
    try {
      // 输入验证
      if (!input.trim()) {
        throw new Error('请输入内容');
      }
      
      // 核心处理逻辑
      const result = await process[操作](input, options);
      setOutput(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : '处理失败');
    } finally {
      setLoading(false);
    }
  };

  // 辅助功能
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(output);
      // 可选：显示成功提示
    } catch (err) {
      setError('复制失败');
    }
  };

  const handleClear = () => {
    setInput('');
    setOutput('');
    setError('');
  };

  const handleDownload = () => {
    const blob = new Blob([output], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `output-${Date.now()}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // UI渲染
  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-4">
      {/* 工具说明 */}
      <Card>
        <CardHeader>
          <CardTitle>[工具名称]</CardTitle>
          <CardDescription>
            [详细的工具描述，说明功能和使用方法]
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 配置区域（如需要） */}
      {/* ... */}

      {/* 输入区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">输入</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="input">请输入[内容类型]</Label>
            <Textarea
              id="input"
              placeholder="在此输入..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="min-h-[200px] font-mono"
            />
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={handle[主要操作]}
              disabled={loading || !input.trim()}
            >
              {loading ? '处理中...' : '[操作名称]'}
            </Button>
            <Button 
              variant="outline" 
              onClick={handleClear}
              disabled={loading}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              清空
            </Button>
          </div>

          {error && (
            <div className="text-sm text-red-500 mt-2">{error}</div>
          )}
        </CardContent>
      </Card>

      {/* 输出区域 */}
      {output && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">输出结果</CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopy}
                >
                  <Copy className="h-4 w-4 mr-1" />
                  复制
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownload}
                >
                  <Download className="h-4 w-4 mr-1" />
                  下载
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Textarea
              value={output}
              readOnly
              className="min-h-[200px] font-mono"
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
```

### 第3步：实现核心功能（20-60分钟）

#### 3.1 功能实现原则

1. **输入验证**
```typescript
// 始终验证输入
if (!input || input.trim() === '') {
  throw new Error('输入不能为空');
}

// 大小限制
if (input.length > 1024 * 1024) { // 1MB
  throw new Error('输入内容过大，请限制在1MB以内');
}

// 格式验证
if (!isValidFormat(input)) {
  throw new Error('输入格式不正确');
}
```

2. **错误处理**
```typescript
try {
  // 处理逻辑
} catch (err) {
  // 友好的错误提示
  if (err instanceof SyntaxError) {
    setError('格式错误：请检查输入内容');
  } else if (err instanceof RangeError) {
    setError('数值超出范围');
  } else {
    setError(err instanceof Error ? err.message : '处理失败');
  }
}
```

3. **性能优化**
```typescript
// 大数据分块处理
const CHUNK_SIZE = 1024 * 1024; // 1MB
const chunks = [];
for (let i = 0; i < data.length; i += CHUNK_SIZE) {
  chunks.push(data.slice(i, i + CHUNK_SIZE));
}

// 使用Web Worker处理复杂计算
const worker = new Worker('/workers/processor.js');
worker.postMessage({ data: input });
worker.onmessage = (e) => {
  setOutput(e.data.result);
};
```

### 第4步：UI设计规范（10分钟）

#### 4.1 布局规范

```typescript
// 容器布局
<div className="w-full max-w-4xl mx-auto p-4 space-y-4">

// 卡片间距
<Card className="space-y-4">

// 按钮组
<div className="flex gap-2">

// 表单元素间距
<div className="space-y-2">

// 网格布局（如需要）
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
```

#### 4.2 组件使用规范

| 场景 | 组件选择 | 示例 |
|------|---------|------|
| 单行输入 | Input | `<Input type="text" />` |
| 多行输入 | Textarea | `<Textarea className="min-h-[200px]" />` |
| 选项选择 | Select | `<Select>...</Select>` |
| 开关切换 | Switch | `<Switch checked={enabled} />` |
| 数值滑块 | Slider | `<Slider value={[value]} />` |
| 文件上传 | Input + Label | `<Input type="file" />` |

#### 4.3 颜色和样式

```typescript
// 按钮变体
<Button>主要操作</Button>
<Button variant="outline">次要操作</Button>
<Button variant="ghost">轻量操作</Button>
<Button variant="destructive">危险操作</Button>

// 文本样式
<p className="text-sm text-muted-foreground">说明文字</p>
<p className="text-sm text-red-500">错误提示</p>
<p className="text-sm text-green-500">成功提示</p>

// 代码样式
<pre className="font-mono text-sm">代码内容</pre>
<Textarea className="font-mono">代码输入</Textarea>
```

### 第5步：注册工具（2分钟）

在 `src/lib/tools/registry.ts` 中添加：

```typescript
{
  id: '[工具id]',              // URL路径，如：'base64'
  name: '[工具显示名称]',        // 如：'Base64编解码'
  description: '[工具简短描述]',  // 如：'Base64字符串编码和解码工具'
  category: '[分类]',           // text/image/dev/crypto/calc/network/convert/life/productivity/data
  icon: '[Lucide图标名]',       // 如：'FileText', 'Code', 'Lock'
  component: lazy(() => import('@/components/tools/[工具名]Tool'))
}
```

### 第6步：测试验证（10分钟）

#### 6.1 功能测试清单

- [ ] **基本功能**
  - [ ] 正常输入产生正确输出
  - [ ] 空输入有合适提示
  - [ ] 超大输入不会卡死
  
- [ ] **错误处理**
  - [ ] 无效输入显示错误
  - [ ] 错误信息清晰友好
  - [ ] 错误后可恢复操作

- [ ] **UI交互**
  - [ ] 所有按钮可点击
  - [ ] Loading状态正确
  - [ ] 复制功能正常
  - [ ] 下载功能正常（如有）
  - [ ] 清空功能正常

- [ ] **响应式**
  - [ ] 移动端显示正常
  - [ ] 平板端显示正常
  - [ ] 桌面端显示正常

#### 6.2 性能测试

```typescript
// 测试大数据处理
console.time('process');
handleProcess(largeData);
console.timeEnd('process'); // 应 < 1秒

// 内存使用检查
performance.memory.usedJSHeapSize
```

### 第7步：提交代码（5分钟）

#### 7.1 代码检查

```bash
# 运行linter
npm run lint

# 格式化代码
npm run format

# 类型检查
npm run type-check
```

#### 7.2 Git提交

```bash
# 添加新工具
git add .
git commit -m "feat: 添加[工具名]工具

- 实现[核心功能1]
- 支持[核心功能2]
- 添加错误处理和输入验证"

# 修复问题
git commit -m "fix: 修复[工具名]的[具体问题]"

# 优化性能
git commit -m "perf: 优化[工具名]的处理性能"
```

## 📊 质量标准检查表

### 必须满足的标准 ✅

- [ ] 核心功能正常工作
- [ ] 有清晰的错误提示
- [ ] UI布局规范统一
- [ ] 支持复制结果
- [ ] 支持清空操作
- [ ] 移动端正常显示
- [ ] 无TypeScript错误
- [ ] 无console.log

### 建议实现的功能 💡

- [ ] 支持文件上传
- [ ] 支持结果下载
- [ ] 实时预览结果
- [ ] 保存用户配置
- [ ] 键盘快捷键
- [ ] 批量处理
- [ ] 历史记录
- [ ] 撤销/重做

## 🚀 高级开发技巧

### 1. 实时处理模式

```typescript
// 使用防抖避免频繁计算
const debouncedProcess = useMemo(
  () => debounce((value: string) => {
    const result = processData(value);
    setOutput(result);
  }, 300),
  []
);

useEffect(() => {
  if (input) {
    debouncedProcess(input);
  }
}, [input, debouncedProcess]);
```

### 2. 大文件处理

```typescript
const handleLargeFile = (file: File) => {
  const reader = new FileReader();
  const chunkSize = 1024 * 1024; // 1MB
  let offset = 0;
  
  const readChunk = () => {
    const slice = file.slice(offset, offset + chunkSize);
    reader.readAsText(slice);
  };
  
  reader.onload = (e) => {
    // 处理chunk
    offset += chunkSize;
    if (offset < file.size) {
      readChunk(); // 继续读取
    }
  };
  
  readChunk();
};
```

### 3. 状态持久化

```typescript
// 自动保存用户输入
useEffect(() => {
  const timeoutId = setTimeout(() => {
    localStorage.setItem(`tool-${toolId}-input`, input);
  }, 1000);
  
  return () => clearTimeout(timeoutId);
}, [input, toolId]);

// 恢复上次输入
useEffect(() => {
  const saved = localStorage.getItem(`tool-${toolId}-input`);
  if (saved) {
    setInput(saved);
  }
}, [toolId]);
```

## 📝 总结

遵循这个标准化流程，可以确保：

1. **一致性**：所有工具具有统一的界面和交互
2. **质量**：通过检查表确保功能完整
3. **效率**：标准模板减少重复工作
4. **可维护**：规范的代码结构便于后期维护

记住：**参考已有的100个工具实现，它们是最好的范例！**

---

最后更新：2025-07-19 | 作者：PoorLow Tools Team
