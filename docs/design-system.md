# 🎨 PoorLow Tools - 设计系统文档

> 更新时间：2025-07-16 22:52

## 1. 设计理念

### 1.1 核心价值
- **简洁高效**：减少认知负担，提高操作效率
- **专业可靠**：体现工具的专业性和稳定性
- **现代美观**：符合当代审美，给人愉悦感受
- **包容友好**：适应不同用户群体和使用场景

### 1.2 设计原则
1. **清晰性优先**：功能明确，操作直观
2. **一致性保持**：视觉和交互的统一性
3. **响应式适配**：完美适配各种设备
4. **性能至上**：快速响应，流畅体验
5. **无障碍设计**：人人可用，平等体验

## 2. 视觉系统

### 2.1 色彩体系

#### 主色调
```css
:root {
  --primary-50: #EFF6FF;
  --primary-100: #DBEAFE;
  --primary-200: #BFDBFE;
  --primary-300: #93BBFC;
  --primary-400: #60A5FA;
  --primary-500: #3B82F6;  /* 主色 */
  --primary-600: #2563EB;
  --primary-700: #1D4ED8;
  --primary-800: #1E40AF;
  --primary-900: #1E3A8A;
}
```

#### 中性色
```css
:root {
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  --gray-950: #030712;
}
```

#### 语义色
```css
:root {
  /* 成功 */
  --success-light: #D1FAE5;
  --success: #10B981;
  --success-dark: #059669;
  
  /* 警告 */
  --warning-light: #FEF3C7;
  --warning: #F59E0B;
  --warning-dark: #D97706;
  
  /* 错误 */
  --error-light: #FEE2E2;
  --error: #EF4444;
  --error-dark: #DC2626;
  
  /* 信息 */
  --info-light: #DBEAFE;
  --info: #3B82F6;
  --info-dark: #2563EB;
}
```

#### 深色模式配色
```css
[data-theme="dark"] {
  --bg-primary: #0F172A;
  --bg-secondary: #1E293B;
  --bg-tertiary: #334155;
  --text-primary: #F1F5F9;
  --text-secondary: #CBD5E1;
  --text-tertiary: #94A3B8;
  --border-primary: #334155;
  --border-secondary: #475569;
}
```

### 2.2 字体系统

#### 字体栈
```css
:root {
  --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, 
               "Helvetica Neue", Arial, "Noto Sans", sans-serif, 
               "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", 
               "Noto Color Emoji", "PingFang SC", "Microsoft YaHei";
  
  --font-mono: "Fira Code", "SF Mono", Monaco, "Cascadia Code", 
               "Roboto Mono", Menlo, Consolas, "Courier New", monospace;
}
```

#### 字体大小
```css
:root {
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
}
```

#### 字重
```css
:root {
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

#### 行高
```css
:root {
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  --leading-loose: 2;
}
```

### 2.3 间距系统

基于 8px 网格系统：
```css
:root {
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
}
```

### 2.4 圆角系统

```css
:root {
  --radius-none: 0;
  --radius-sm: 0.125rem;    /* 2px */
  --radius-base: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;    /* 6px */
  --radius-lg: 0.5rem;      /* 8px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-3xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;
}
```

### 2.5 阴影系统

```css
:root {
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 
               0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
                 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 
               0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
               0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
```

## 3. 组件设计

### 3.1 按钮组件

#### 变体
```tsx
// 主按钮
<Button variant="primary">开始使用</Button>

// 次要按钮
<Button variant="secondary">了解更多</Button>

// 轮廓按钮
<Button variant="outline">取消</Button>

// 幽灵按钮
<Button variant="ghost">删除</Button>

// 链接按钮
<Button variant="link">查看详情</Button>
```

#### 尺寸
```tsx
<Button size="sm">小按钮</Button>
<Button size="md">中按钮</Button>
<Button size="lg">大按钮</Button>
```

#### 状态
- 默认状态
- 悬浮状态：背景色加深 10%
- 点击状态：缩放 95%
- 禁用状态：透明度 50%
- 加载状态：显示加载动画

### 3.2 输入框组件

#### 基础样式
```css
.input {
  height: 40px;
  padding: 0 16px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

#### 状态变化
- 默认：灰色边框
- 聚焦：主色边框 + 阴影
- 错误：红色边框
- 禁用：背景变灰

### 3.3 卡片组件

```css
.card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
```

### 3.4 模态框组件

- 背景遮罩：黑色 50% 透明度
- 内容区域：白色背景，大圆角
- 动画：淡入 + 缩放效果
- 关闭按钮：右上角

## 4. 动效设计

### 4.1 动画时长

```css
:root {
  --duration-fast: 150ms;
  --duration-base: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
}
```

### 4.2 缓动函数

```css
:root {
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
```

### 4.3 常用动效

#### 淡入淡出
```css
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}
```

#### 滑入滑出
```css
@keyframes slide-in {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slide-out {
  from { transform: translateY(0); opacity: 1; }
  to { transform: translateY(10px); opacity: 0; }
}
```

#### 缩放
```css
@keyframes scale-in {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
```

## 5. 响应式设计

### 5.1 断点系统

```css
:root {
  --screen-sm: 640px;
  --screen-md: 768px;
  --screen-lg: 1024px;
  --screen-xl: 1280px;
  --screen-2xl: 1536px;
}
```

### 5.2 容器宽度

```css
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container { max-width: 640px; }
}

@media (min-width: 768px) {
  .container { max-width: 768px; }
}

@media (min-width: 1024px) {
  .container { max-width: 1024px; }
}

@media (min-width: 1280px) {
  .container { max-width: 1280px; }
}
```

### 5.3 网格系统

```css
.grid {
  display: grid;
  gap: var(--space-4);
}

/* 移动端：1列 */
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }

/* 平板：2列 */
@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
}

/* 桌面：3列 */
@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

/* 大屏：4列 */
@media (min-width: 1280px) {
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}
```

## 6. 图标系统

### 6.1 图标尺寸

```css
.icon-xs { width: 16px; height: 16px; }
.icon-sm { width: 20px; height: 20px; }
.icon-base { width: 24px; height: 24px; }
.icon-lg { width: 32px; height: 32px; }
.icon-xl { width: 48px; height: 48px; }
```

### 6.2 图标使用原则

- 使用 Lucide Icons 作为主要图标库
- 保持图标风格统一（线条粗细、圆角等）
- 图标与文字搭配时保持适当间距
- 交互图标需要足够的点击区域（最小 44x44px）

## 7. 无障碍设计

### 7.1 颜色对比度

- 正常文本：最小对比度 4.5:1
- 大文本（18px+）：最小对比度 3:1
- 交互元素：最小对比度 3:1

### 7.2 焦点样式

```css
.focusable:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 仅键盘焦点显示 */
.focusable:focus:not(:focus-visible) {
  outline: none;
}
```

### 7.3 ARIA 标签

```html
<!-- 按钮 -->
<button aria-label="关闭对话框">×</button>

<!-- 加载状态 -->
<div role="status" aria-live="polite">
  <span class="sr-only">加载中...</span>
</div>

<!-- 表单 -->
<label for="email">邮箱</label>
<input id="email" type="email" aria-required="true" />
```

## 8. 主题定制

### 8.1 CSS 变量系统

```css
[data-theme="custom"] {
  /* 用户可自定义的变量 */
  --custom-primary: #custom-color;
  --custom-background: #custom-bg;
  --custom-foreground: #custom-fg;
  --custom-card: #custom-card;
  --custom-border: #custom-border;
  --custom-radius: 8px;
  --custom-font-family: 'Custom Font', sans-serif;
}
```

### 8.2 主题切换逻辑

```typescript
interface Theme {
  name: string;
  colors: {
    primary: string;
    background: string;
    foreground: string;
    // ...
  };
  fonts?: {
    sans?: string;
    mono?: string;
  };
  radius?: string;
}

// 应用主题
function applyTheme(theme: Theme) {
  const root = document.documentElement;
  Object.entries(theme.colors).forEach(([key, value]) => {
    root.style.setProperty(`--${key}`, value);
  });
}
```

## 9. 设计令牌

### 9.1 导出格式

```json
{
  "colors": {
    "primary": {
      "50": "#EFF6FF",
      "100": "#DBEAFE",
      // ...
    }
  },
  "spacing": {
    "1": "0.25rem",
    "2": "0.5rem",
    // ...
  },
  "typography": {
    "fontSize": {
      "xs": "0.75rem",
      "sm": "0.875rem",
      // ...
    }
  }
}
```

### 9.2 使用方式

- CSS：通过 CSS 变量使用
- JavaScript：导入 JSON 文件
- Sass/Less：通过预处理器变量
- Tailwind：通过配置文件扩展

## 10. 最佳实践

### 10.1 性能优化

- 使用 CSS 变量实现主题切换，避免重新加载样式
- 图片使用 WebP 格式，提供降级方案
- 关键 CSS 内联，非关键 CSS 异步加载
- 使用 CSS containment 优化重绘性能

### 10.2 可维护性

- 组件化设计，避免样式冲突
- 使用 BEM 或 CSS Modules 命名规范
- 保持设计令牌的单一数据源
- 定期审查和清理未使用的样式

### 10.3 协作规范

- 设计与开发使用相同的命名系统
- 建立设计评审流程
- 维护组件库文档
- 定期同步设计系统更新

## 11. 更新记录

- 2025-07-16 22:52：创建设计系统文档，定义视觉规范和组件标准
