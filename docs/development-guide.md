# 🚀 PoorLow Tools - 开发指南

## 1. 快速开始

### 1.1 环境准备

```bash
# 确保已安装 Node.js 18+
node --version

# 克隆项目
git clone [your-repo-url]
cd poorlow-tools

# 安装依赖
npm install

# 复制环境变量
cp .env.example .env.local

# 启动开发服务器
npm run dev
```

### 1.2 开发工具推荐

- **IDE**: VSCode
- **VSCode 插件**:
  - ESLint
  - Prettier
  - Tailwind CSS IntelliSense
  - TypeScript Vue Plugin
  - i18n Ally (多语言支持)

## 2. 项目初始化步骤

### 2.1 创建 Next.js 项目

```bash
# 使用 create-next-app 创建项目
npx create-next-app@latest poorlow-tools --typescript --tailwind --app

# 进入项目目录
cd poorlow-tools
```

### 2.2 安装核心依赖

```bash
# UI 组件库
npx shadcn-ui@latest init

# 状态管理
npm install zustand

# 国际化
npm install next-intl

# 图标库
npm install lucide-react

# 工具库
npm install clsx tailwind-merge
npm install @radix-ui/react-slot

# 开发依赖
npm install -D @types/node
npm install -D eslint-config-prettier prettier
npm install -D @testing-library/react @testing-library/jest-dom
npm install -D vitest @vitejs/plugin-react
```

### 2.3 配置 shadcn/ui

```bash
# 初始化 shadcn/ui
npx shadcn-ui@latest init

# 选择以下配置：
# - Would you like to use TypeScript? → Yes
# - Which style would you like to use? → Default
# - Which color would you like to use as base color? → Slate
# - Where is your global CSS file? → src/app/globals.css
# - Would you like to use CSS variables for colors? → Yes
# - Where is your tailwind.config.js located? → tailwind.config.ts
# - Configure the import alias for components? → @/components
# - Configure the import alias for utils? → @/lib/utils
```

### 2.4 安装基础组件

```bash
# 安装常用组件
npx shadcn-ui@latest add button
npx shadcn-ui@latest add card
npx shadcn-ui@latest add dialog
npx shadcn-ui@latest add dropdown-menu
npx shadcn-ui@latest add input
npx shadcn-ui@latest add label
npx shadcn-ui@latest add select
npx shadcn-ui@latest add separator
npx shadcn-ui@latest add switch
npx shadcn-ui@latest add tabs
npx shadcn-ui@latest add textarea
npx shadcn-ui@latest add toast
npx shadcn-ui@latest add tooltip
```

## 3. 开发规范

### 3.1 代码风格

#### TypeScript 规范
```typescript
// ✅ 好的做法
interface ToolProps {
  title: string;
  description: string;
  onComplete?: (result: string) => void;
}

export const Tool: React.FC<ToolProps> = ({ 
  title, 
  description, 
  onComplete 
}) => {
  // 组件逻辑
};

// ❌ 避免的做法
export const Tool = (props: any) => {
  // 不使用 any 类型
};
```

#### 命名规范
- **组件**: PascalCase (如 `ToolCard.tsx`)
- **工具函数**: camelCase (如 `formatDate.ts`)
- **常量**: UPPER_SNAKE_CASE (如 `MAX_FILE_SIZE`)
- **类型/接口**: PascalCase (如 `interface ToolConfig`)

### 3.2 目录组织

```
src/
├── app/[locale]/              # 页面文件
├── components/
│   ├── ui/                    # 基础 UI 组件
│   ├── layout/                # 布局组件
│   └── tools/                 # 工具相关组件
├── lib/
│   ├── tools/                 # 工具实现
│   │   └── text/
│   │       └── word-counter/
│   │           ├── index.tsx  # 工具组件
│   │           ├── logic.ts   # 业务逻辑
│   │           └── types.ts   # 类型定义
│   └── utils/                 # 工具函数
└── hooks/                     # 自定义 Hooks
```

## 4. 添加新工具指南

### 4.1 工具开发模板

创建新工具文件：`src/lib/tools/[category]/[tool-name]/index.tsx`

```typescript
'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useTranslations } from 'next-intl';
import { ToolLayout } from '@/components/tools/tool-layout';
import type { ToolComponent } from '@/types/tools';

export const WordCounter: ToolComponent = ({ 
  onStateChange, 
  initialState 
}) => {
  const t = useTranslations('tools.wordCounter');
  const [text, setText] = useState(initialState?.text || '');
  const [stats, setStats] = useState({
    characters: 0,
    charactersNoSpaces: 0,
    words: 0,
    lines: 0,
    paragraphs: 0
  });

  // 计算统计信息
  useEffect(() => {
    const characters = text.length;
    const charactersNoSpaces = text.replace(/\s/g, '').length;
    const words = text.trim() ? text.trim().split(/\s+/).length : 0;
    const lines = text.split('\n').length;
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim()).length;

    setStats({
      characters,
      charactersNoSpaces,
      words,
      lines,
      paragraphs
    });

    // 保存状态
    onStateChange?.({ text });
  }, [text, onStateChange]);

  return (
    <ToolLayout
      title={t('title')}
      description={t('description')}
    >
      <div className="grid gap-6 md:grid-cols-2">
        {/* 输入区域 */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">{t('input')}</h3>
          <Textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder={t('placeholder')}
            className="min-h-[400px] resize-none"
          />
        </Card>

        {/* 统计结果 */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">{t('statistics')}</h3>
          <div className="space-y-3">
            <StatItem label={t('characters')} value={stats.characters} />
            <StatItem label={t('charactersNoSpaces')} value={stats.charactersNoSpaces} />
            <StatItem label={t('words')} value={stats.words} />
            <StatItem label={t('lines')} value={stats.lines} />
            <StatItem label={t('paragraphs')} value={stats.paragraphs} />
          </div>
          
          <div className="mt-6 space-y-2">
            <Button 
              onClick={() => setText('')}
              variant="outline"
              className="w-full"
            >
              {t('clear')}
            </Button>
            <Button 
              onClick={() => navigator.clipboard.writeText(text)}
              className="w-full"
            >
              {t('copy')}
            </Button>
          </div>
        </Card>
      </div>
    </ToolLayout>
  );
};

// 统计项组件
const StatItem: React.FC<{ label: string; value: number }> = ({ 
  label, 
  value 
}) => (
  <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
    <span className="text-muted-foreground">{label}</span>
    <span className="text-2xl font-bold">{value.toLocaleString()}</span>
  </div>
);

// 工具配置
export const wordCounterConfig = {
  id: 'word-counter',
  category: 'text',
  icon: 'FileText',
  name: {
    zh: '字数统计器',
    en: 'Word Counter',
    ja: '文字数カウンター'
  },
  description: {
    zh: '统计文本的字符数、单词数、行数等信息',
    en: 'Count characters, words, lines and more',
    ja: 'テキストの文字数、単語数、行数などを統計'
  },
  keywords: ['word count', 'character count', 'text statistics']
};
```

### 4.2 添加翻译文件

创建翻译文件：`public/locales/[locale]/tools/word-counter.json`

```json
{
  "title": "字数统计器",
  "description": "快速统计文本的字符数、单词数、行数等信息",
  "input": "输入文本",
  "placeholder": "在此输入或粘贴文本...",
  "statistics": "统计结果",
  "characters": "字符数",
  "charactersNoSpaces": "字符数（不含空格）",
  "words": "单词数",
  "lines": "行数",
  "paragraphs": "段落数",
  "clear": "清空",
  "copy": "复制文本"
}
```

### 4.3 注册工具

在 `src/lib/constants/tools.ts` 中注册新工具：

```typescript
import { wordCounterConfig } from '@/lib/tools/text/word-counter';

export const TOOLS_REGISTRY = [
  wordCounterConfig,
  // ... 其他工具
];
```

### 4.4 创建工具页面

创建页面文件：`src/app/[locale]/tools/text/word-counter/page.tsx`

```typescript
import { Metadata } from 'next';
import dynamic from 'next/dynamic';
import { getTranslations } from 'next-intl/server';

// 动态导入工具组件
const WordCounter = dynamic(
  () => import('@/lib/tools/text/word-counter').then(mod => mod.WordCounter),
  { 
    ssr: false,
    loading: () => <div>Loading...</div>
  }
);

// 生成元数据
export async function generateMetadata({ 
  params: { locale } 
}: { 
  params: { locale: string } 
}): Promise<Metadata> {
  const t = await getTranslations({ locale, namespace: 'tools.wordCounter' });
  
  return {
    title: `${t('title')} - PoorLow Tools`,
    description: t('description'),
    keywords: ['字数统计', '文字计数', 'word counter'],
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
    },
    alternates: {
      languages: {
        'zh': '/zh/tools/text/word-counter',
        'en': '/en/tools/text/word-counter',
        'ja': '/ja/tools/text/word-counter',
      }
    }
  };
}

export default function WordCounterPage() {
  return <WordCounter />;
}
```

## 5. 测试指南

### 5.1 单元测试

创建测试文件：`tests/unit/tools/word-counter.test.ts`

```typescript
import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { WordCounter } from '@/lib/tools/text/word-counter';

describe('WordCounter', () => {
  it('应该正确统计字符数', () => {
    const { getByPlaceholderText } = render(<WordCounter />);
    const textarea = getByPlaceholderText(/输入或粘贴文本/);
    
    fireEvent.change(textarea, { target: { value: 'Hello World' } });
    
    expect(screen.getByText('11')).toBeInTheDocument(); // 字符数
    expect(screen.getByText('10')).toBeInTheDocument(); // 不含空格
    expect(screen.getByText('2')).toBeInTheDocument();  // 单词数
  });
});
```

### 5.2 运行测试

```bash
# 运行所有测试
npm run test

# 运行测试覆盖率
npm run test:coverage

# 监听模式
npm run test:watch
```

## 6. 性能优化

### 6.1 代码分割

```typescript
// 使用动态导入
const HeavyTool = dynamic(
  () => import('@/lib/tools/heavy-tool'),
  {
    loading: () => <ToolSkeleton />,
    ssr: false
  }
);
```

### 6.2 图片优化

```typescript
import Image from 'next/image';

// 使用 Next.js Image 组件
<Image
  src="/tool-icon.png"
  alt="Tool Icon"
  width={48}
  height={48}
  loading="lazy"
/>
```

### 6.3 状态持久化

```typescript
// 使用 localStorage 持久化工具状态
const useToolState = (toolId: string) => {
  const [state, setState] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(`tool-${toolId}`);
      return saved ? JSON.parse(saved) : null;
    }
    return null;
  });

  useEffect(() => {
    if (state) {
      localStorage.setItem(`tool-${toolId}`, JSON.stringify(state));
    }
  }, [state, toolId]);

  return [state, setState];
};
```

## 7. 部署流程

### 7.1 构建 Docker 镜像

```dockerfile
# Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine
WORKDIR /app
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/package*.json ./
RUN npm ci --production
EXPOSE 3000
CMD ["npm", "start"]
```

### 7.2 部署命令

```bash
# 构建镜像
docker build -t poorlow-tools .

# 运行容器
docker run -p 3000:3000 poorlow-tools

# 使用 Docker Compose
docker-compose up -d
```

## 8. 常见问题

### Q: 如何添加新的语言支持？
A: 
1. 在 `src/i18n/config.ts` 添加语言代码
2. 创建对应的翻译文件目录 `public/locales/[new-locale]/`
3. 翻译所有 JSON 文件

### Q: 如何自定义主题？
A: 
1. 修改 `tailwind.config.ts` 中的颜色配置
2. 使用 CSS 变量在 `globals.css` 中定义主题
3. 通过 `ThemeProvider` 切换主题

### Q: 如何优化 SEO？
A: 
1. 为每个工具页面设置独立的 metadata
2. 使用结构化数据 (JSON-LD)
3. 确保所有图片有 alt 标签
4. 使用语义化 HTML

## 9. 资源链接

- [Next.js 文档](https://nextjs.org/docs)
- [shadcn/ui 组件库](https://ui.shadcn.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [TypeScript 手册](https://www.typescriptlang.org/docs/)
- [next-intl 文档](https://next-intl-docs.vercel.app/)

## 10. 更新记录
- 2025-07-16：创建开发指南
