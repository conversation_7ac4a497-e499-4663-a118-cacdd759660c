# 翻译键值命名规范

## 基本原则

1. **使用小写字母和下划线**
   - 正确：`user_name`, `submit_button`
   - 错误：`userName`, `SubmitButton`

2. **使用点号分隔层级**
   - 正确：`home.hero.title`
   - 错误：`home_hero_title`

3. **动作使用动词开头**
   - 正确：`save_file`, `delete_item`
   - 错误：`file_save`, `item_deletion`

## 命名结构

### 1. 页面级别翻译
```
{page}.{section}.{element}
```
示例：
- `home.hero.title` - 首页英雄区标题
- `tools.filter.category` - 工具页筛选分类

### 2. 组件级别翻译
```
{component}.{state/action}.{description}
```
示例：
- `button.submit.text` - 提交按钮文本
- `modal.close.tooltip` - 模态框关闭提示

### 3. 工具特定翻译
```
tools.{tool_id}.{element}
```
示例：
- `tools.base64.encode_button` - Base64工具编码按钮
- `tools.json_formatter.error_invalid` - JSON格式化工具错误信息

## 常用前缀

### 动作类
- `add_` - 添加
- `create_` - 创建
- `delete_` - 删除
- `edit_` - 编辑
- `save_` - 保存
- `cancel_` - 取消
- `submit_` - 提交
- `search_` - 搜索
- `filter_` - 筛选
- `sort_` - 排序

### 状态类
- `loading_` - 加载中
- `error_` - 错误
- `success_` - 成功
- `warning_` - 警告
- `info_` - 信息

### 元素类
- `_title` - 标题
- `_description` - 描述
- `_placeholder` - 占位符
- `_tooltip` - 提示
- `_label` - 标签
- `_button` - 按钮
- `_link` - 链接
- `_message` - 消息

## 占位符使用

使用 `{variable}` 格式表示动态内容：

```json
{
  "welcome_message": "欢迎您，{name}！",
  "items_count": "共 {count} 个项目",
  "file_size_limit": "文件大小限制：{size}"
}
```

## 文件组织

```
public/locales/
├── zh/                    # 中文
│   ├── common.json       # 通用翻译
│   ├── navigation.json   # 导航相关
│   ├── home.json        # 首页
│   └── tools/           # 工具相关
│       ├── _common.json # 工具通用
│       └── {tool}.json  # 具体工具
├── en/                   # 英文
└── ja/                   # 日文
```

## 最佳实践

1. **保持一致性**
   - 同类翻译使用相同的命名模式
   - 整个项目使用统一的命名规范

2. **避免硬编码**
   - 不要在键名中包含具体的文本内容
   - 使用描述性的键名

3. **合理分组**
   - 相关的翻译放在同一个对象下
   - 避免过深的嵌套（最多3-4层）

4. **复用通用翻译**
   - 常用词汇放在 common.json
   - 避免重复定义相同的翻译

## 示例

### 好的命名
```json
{
  "form": {
    "validation": {
      "required_field": "此字段为必填项",
      "invalid_email": "邮箱格式不正确",
      "password_too_short": "密码长度至少为 {min} 位"
    }
  }
}
```

### 不好的命名
```json
{
  "RequiredFieldError": "此字段为必填项",
  "email-invalid-msg": "邮箱格式不正确",
  "pwd_short": "密码长度至少为 8 位"
}
```

## 工具翻译模板

每个工具的翻译文件应包含以下基本结构：

```json
{
  "title": "工具名称",
  "description": "工具描述",
  "keywords": ["关键词1", "关键词2"],
  "input": {
    "label": "输入标签",
    "placeholder": "输入占位符",
    "help": "输入帮助文本"
  },
  "output": {
    "label": "输出标签",
    "empty": "无输出时的提示"
  },
  "options": {
    "option1": {
      "label": "选项1标签",
      "description": "选项1描述"
    }
  },
  "actions": {
    "primary": "主要操作",
    "secondary": "次要操作"
  },
  "errors": {
    "invalid_input": "输入无效时的错误信息",
    "processing_failed": "处理失败时的错误信息"
  },
  "success": {
    "completed": "操作完成的成功信息"
  }
}
```

## 版本管理

- 添加新翻译时，在所有语言文件中同步添加
- 修改翻译时，记录修改原因
- 删除翻译前，确保代码中已无引用
