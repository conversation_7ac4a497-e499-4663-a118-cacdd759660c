# 🐧 宝塔面板 + OpenCloudOS 部署指南

> 创建时间：2025-07-20 16:19
> 适用环境：宝塔Linux面板 + OpenCloudOS 系统

## 一、关于您的服务器环境

### 服务器信息
- **操作系统**：OpenCloudOS（腾讯云定制的企业级Linux发行版）
- **控制面板**：宝塔Linux面板
- **配置**：2核4GB内存，60GB SSD，5Mbps带宽
- **地域**：广州三区

### OpenCloudOS 说明
OpenCloudOS 是基于 CentOS 8 的企业级 Linux 发行版，完全兼容 RHEL 生态。您**不需要更换操作系统**，可以直接使用。

## 二、宝塔面板部署优势

使用宝塔面板部署有以下优势：
- ✅ **图形化界面**：操作简单直观
- ✅ **一键安装**：软件环境一键配置
- ✅ **自动化部署**：支持 Git Webhook
- ✅ **监控完善**：资源监控、日志查看
- ✅ **安全防护**：内置防火墙、安全设置

## 三、部署步骤

### 步骤1：登录宝塔面板

1. 访问宝塔面板地址（通常是 `http://服务器IP:8888`）
2. 使用初始账号密码登录
3. 如果是首次登录，建议修改默认端口和密码

### 步骤2：安装必要软件

在宝塔面板的【软件商店】中安装：

1. **Nginx**（推荐 1.22 或更高版本）
2. **Node.js版本管理器**（在【运行环境】分类下）
   - 安装后选择 Node.js 18.x 或 20.x 版本
3. **PM2管理器**（在【运行环境】分类下）
4. **Git**（通常已预装）

### 步骤3：创建网站

1. 在宝塔面板点击【网站】→【添加站点】
2. 填写信息：
   - 域名：填写您的域名（如果没有可以先用IP）
   - 根目录：选择 `/www/wwwroot/poorlow-tools`
   - PHP版本：选择【纯静态】
   - 数据库：不创建

### 步骤4：部署项目代码

#### 方法1：使用宝塔的 Git 功能（推荐）

1. 进入网站目录：`/www/wwwroot/poorlow-tools`
2. 在宝塔文件管理器中，点击【Git】→【初始化】
3. 设置 Git 仓库：
   ```bash
   # 如果使用 Gitee 私有仓库
   git remote add origin https://gitee.com/您的用户名/poorlow-tools.git
   
   # 如果使用服务器本地仓库
   git remote add origin /www/server/git/poorlow-tools.git
   ```

#### 方法2：手动上传

1. 在本地打包项目（排除 node_modules）
2. 通过宝塔面板的文件管理器上传
3. 解压到网站目录

### 步骤5：安装依赖和构建

在宝塔面板的【终端】中执行：

```bash
# 进入项目目录
cd /www/wwwroot/poorlow-tools

# 安装依赖
npm install

# 构建项目
npm run build

# 使用 PM2 启动
pm2 start npm --name "poorlow-tools" -- start
pm2 save
pm2 startup
```

### 步骤6：配置 Nginx

在宝塔面板中，找到您的网站，点击【设置】→【配置文件】，修改为：

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名
    
    # 如果没有域名，这行改为：
    # server_name _;
    
    root /www/wwwroot/poorlow-tools;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/json application/xml+rss;
    
    # 反向代理到 Next.js
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location /_next/static {
        proxy_pass http://127.0.0.1:3000;
        expires 365d;
        add_header Cache-Control "public, immutable";
    }
}
```

### 步骤7：设置自动部署

#### 使用宝塔 Webhook（推荐）

1. 在宝塔面板【计划任务】中创建 Shell 脚本：

```bash
#!/bin/bash
# 自动部署脚本
LOG_FILE="/www/wwwlogs/poorlow-tools-deploy.log"
PROJECT_DIR="/www/wwwroot/poorlow-tools"

echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始部署..." >> $LOG_FILE

cd $PROJECT_DIR

# 拉取最新代码
git pull origin main >> $LOG_FILE 2>&1

# 安装依赖
npm ci >> $LOG_FILE 2>&1

# 构建项目
npm run build >> $LOG_FILE 2>&1

# 重启应用
pm2 restart poorlow-tools >> $LOG_FILE 2>&1

echo "$(date '+%Y-%m-%d %H:%M:%S') - 部署完成！" >> $LOG_FILE
```

2. 保存脚本，设置执行权限
3. 在 Git 仓库设置 Webhook，URL 指向宝塔的 Webhook 地址

#### 使用宝塔 Git 钩子

1. 在网站设置中找到【Git】→【WebHook】
2. 设置钩子脚本（同上面的部署脚本）
3. 获取 Webhook URL，配置到 Git 仓库

## 四、宝塔面板特有功能

### 1. SSL 证书配置

宝塔提供免费 SSL 证书：
1. 网站设置 → SSL → Let's Encrypt
2. 勾选域名，点击申请
3. 开启强制 HTTPS

### 2. 防火墙设置

在【安全】中配置：
- 放行 80、443 端口（网站访问）
- 放行 3000 端口（如果需要直接访问 Next.js）
- 修改 SSH 端口（提高安全性）

### 3. 定时备份

设置自动备份：
1. 【计划任务】→ 添加备份网站任务
2. 选择备份到服务器磁盘或对象存储
3. 设置每日凌晨自动备份

### 4. 监控告警

配置监控：
1. 【监控】→ 设置告警
2. CPU、内存、磁盘使用率告警
3. 网站异常告警

## 五、性能优化建议

### 1. 开启 OPcache（如果后续添加 PHP 功能）
### 2. 配置 Redis 缓存（可选）
### 3. 使用宝塔的网站加速功能
### 4. 定期清理日志和缓存

## 六、故障排查

### 常见问题

1. **网站无法访问**
   - 检查防火墙是否放行端口
   - 检查 Nginx 配置是否正确
   - 查看 PM2 进程是否正常

2. **部署失败**
   - 查看部署日志：`/www/wwwlogs/poorlow-tools-deploy.log`
   - 检查 Node.js 版本是否正确
   - 确认磁盘空间充足

3. **性能问题**
   - 使用宝塔监控查看资源使用
   - 检查是否有异常进程
   - 优化 Nginx 配置

### 日志位置

- Nginx 日志：`/www/wwwlogs/`
- PM2 日志：`pm2 logs poorlow-tools`
- 部署日志：`/www/wwwlogs/poorlow-tools-deploy.log`

## 七、安全建议

1. **修改默认设置**
   - 修改宝塔面板端口（默认8888）
   - 修改面板安全入口
   - 使用强密码

2. **定期更新**
   - 保持宝塔面板最新版本
   - 定期更新系统和软件

3. **备份策略**
   - 定期备份网站和数据库
   - 备份到远程存储

## 八、总结

使用宝塔面板部署的优势：
- ✅ 无需更换系统，OpenCloudOS 完全支持
- ✅ 图形化操作，降低技术门槛
- ✅ 集成度高，一站式管理
- ✅ 自动化部署简单配置即可实现

预计部署时间：1-2小时（包括熟悉宝塔面板）

如果您对命令行不熟悉，宝塔面板是最佳选择。大部分操作都可以通过图形界面完成，只有少量命令需要在终端执行。
