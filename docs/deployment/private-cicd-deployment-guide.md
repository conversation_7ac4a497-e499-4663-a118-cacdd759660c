# 🔐 PoorLow Tools 私有化自动部署方案

> 创建时间：2025-07-20 16:09
> 适用场景：私有代码仓库 + 自动化构建部署

## 一、方案概述

由于项目暂不开源，我们需要搭建一个私有的 CI/CD 流程，实现代码提交后自动构建部署。以下提供几种可行方案。

## 二、私有代码托管方案

### 方案1：自建 Git 服务器（推荐）

#### 使用 Gitea（轻量级）
```bash
# 在服务器上安装 Gitea
wget -O gitea https://dl.gitea.io/gitea/1.21.0/gitea-1.21.0-linux-amd64
chmod +x gitea

# 创建 git 用户
sudo adduser --system --shell /bin/bash --gecos 'Git Version Control' --group --disabled-password --home /home/<USER>

# 创建必要的目录
sudo mkdir -p /var/lib/gitea/{custom,data,log}
sudo chown -R git:git /var/lib/gitea/
sudo chmod -R 750 /var/lib/gitea/
sudo mkdir /etc/gitea
sudo chown root:git /etc/gitea
sudo chmod 770 /etc/gitea

# 配置为系统服务
sudo cp gitea /usr/local/bin/gitea
```

创建 systemd 服务文件 `/etc/systemd/system/gitea.service`：
```ini
[Unit]
Description=Gitea (Git with a cup of tea)
After=syslog.target
After=network.target

[Service]
RestartSec=2s
Type=simple
User=git
Group=git
WorkingDirectory=/var/lib/gitea/
ExecStart=/usr/local/bin/gitea web -c /etc/gitea/app.ini
Restart=always

[Install]
WantedBy=multi-user.target
```

#### 使用 GitLab CE（功能全面）
```bash
# 安装 GitLab
curl https://packages.gitlab.com/install/repositories/gitlab/gitlab-ce/script.deb.sh | sudo bash
sudo EXTERNAL_URL="http://your-domain.com" apt-get install gitlab-ce
```

### 方案2：使用私有 Git 仓库服务

- **Gitee 私有仓库**（国内访问快）
- **Bitbucket**（免费私有仓库）
- **Azure DevOps**（免费私有仓库）

### 方案3：直接在服务器上使用裸仓库

```bash
# 在服务器上创建裸仓库
mkdir -p /var/repo/poorlow-tools.git
cd /var/repo/poorlow-tools.git
git init --bare

# 设置 post-receive 钩子实现自动部署
```

## 三、自动化部署方案

### 方案1：Git Hooks + 脚本（最简单）

创建 `/var/repo/poorlow-tools.git/hooks/post-receive`：

```bash
#!/bin/bash
# Git Hook 自动部署脚本

# 配置
WORK_DIR="/var/www/poorlow-tools"
GIT_DIR="/var/repo/poorlow-tools.git"
BRANCH="main"
LOG_FILE="/var/log/poorlow-tools-deploy.log"

# 记录部署开始
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始部署..." >> $LOG_FILE

# 检查分支
while read oldrev newrev ref
do
    if [[ $ref = refs/heads/$BRANCH ]]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 检测到 $BRANCH 分支更新" >> $LOG_FILE
        
        # 更新代码
        cd $WORK_DIR || exit
        unset GIT_DIR
        git pull origin $BRANCH >> $LOG_FILE 2>&1
        
        # 安装依赖
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 安装依赖..." >> $LOG_FILE
        npm ci >> $LOG_FILE 2>&1
        
        # 构建项目
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 构建项目..." >> $LOG_FILE
        npm run build >> $LOG_FILE 2>&1
        
        # 重启应用
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 重启应用..." >> $LOG_FILE
        pm2 restart poorlow-tools >> $LOG_FILE 2>&1
        
        # 清理缓存
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 清理缓存..." >> $LOG_FILE
        rm -rf .next/cache
        
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 部署完成！" >> $LOG_FILE
        
        # 发送通知（可选）
        # curl -X POST "your-webhook-url" -d "message=部署成功"
    fi
done
```

设置权限：
```bash
chmod +x /var/repo/poorlow-tools.git/hooks/post-receive
```

### 方案2：Jenkins 私有部署

#### 1. 安装 Jenkins
```bash
# 安装 Java
sudo apt update
sudo apt install openjdk-11-jdk -y

# 安装 Jenkins
wget -q -O - https://pkg.jenkins.io/debian-stable/jenkins.io.key | sudo apt-key add -
sudo sh -c 'echo deb https://pkg.jenkins.io/debian-stable binary/ > /etc/apt/sources.list.d/jenkins.list'
sudo apt update
sudo apt install jenkins -y
```

#### 2. 配置 Jenkins Pipeline

创建 `Jenkinsfile`：
```groovy
pipeline {
    agent any
    
    environment {
        NODE_VERSION = '18'
        APP_DIR = '/var/www/poorlow-tools'
    }
    
    stages {
        stage('拉取代码') {
            steps {
                git branch: 'main', 
                    url: 'file:///var/repo/poorlow-tools.git'
            }
        }
        
        stage('安装依赖') {
            steps {
                sh '''
                    export NVM_DIR="$HOME/.nvm"
                    [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"
                    nvm use ${NODE_VERSION}
                    npm ci
                '''
            }
        }
        
        stage('构建') {
            steps {
                sh '''
                    export NVM_DIR="$HOME/.nvm"
                    [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"
                    nvm use ${NODE_VERSION}
                    npm run build
                '''
            }
        }
        
        stage('部署') {
            steps {
                sh '''
                    # 备份当前版本
                    if [ -d "${APP_DIR}/.next" ]; then
                        mv ${APP_DIR}/.next ${APP_DIR}/.next.backup
                    fi
                    
                    # 复制新版本
                    cp -r .next ${APP_DIR}/
                    cp -r public ${APP_DIR}/
                    cp package.json ${APP_DIR}/
                    cp package-lock.json ${APP_DIR}/
                    
                    # 重启应用
                    cd ${APP_DIR}
                    pm2 restart poorlow-tools || pm2 start npm --name "poorlow-tools" -- start
                '''
            }
        }
        
        stage('健康检查') {
            steps {
                sh '''
                    sleep 10
                    curl -f http://localhost:3000 || exit 1
                '''
            }
        }
    }
    
    post {
        success {
            echo '部署成功！'
            // 发送成功通知
        }
        failure {
            echo '部署失败！'
            // 回滚到备份版本
            sh '''
                if [ -d "${APP_DIR}/.next.backup" ]; then
                    rm -rf ${APP_DIR}/.next
                    mv ${APP_DIR}/.next.backup ${APP_DIR}/.next
                    cd ${APP_DIR}
                    pm2 restart poorlow-tools
                fi
            '''
        }
    }
}
```

### 方案3：使用 Drone CI（轻量级）

#### 1. 安装 Drone
```bash
# 使用 Docker 安装 Drone
docker run \
  --volume=/var/lib/drone:/data \
  --env=DRONE_GITEA_SERVER=https://your-gitea.com \
  --env=DRONE_GITEA_CLIENT_ID=your-oauth-client-id \
  --env=DRONE_GITEA_CLIENT_SECRET=your-oauth-client-secret \
  --env=DRONE_RPC_SECRET=your-rpc-secret \
  --env=DRONE_SERVER_HOST=drone.your-domain.com \
  --env=DRONE_SERVER_PROTO=https \
  --publish=80:80 \
  --publish=443:443 \
  --restart=always \
  --detach=true \
  --name=drone \
  drone/drone:2
```

#### 2. 配置 `.drone.yml`
```yaml
kind: pipeline
type: docker
name: default

steps:
- name: 安装依赖
  image: node:18
  commands:
  - npm ci

- name: 构建
  image: node:18
  commands:
  - npm run build

- name: 部署
  image: appleboy/drone-ssh
  settings:
    host: your-server.com
    username: deploy
    key:
      from_secret: ssh_key
    script:
      - cd /var/www/poorlow-tools
      - git pull origin main
      - npm ci
      - npm run build
      - pm2 restart poorlow-tools
```

## 四、本地开发配置

### 1. 配置 Git Remote
```bash
# 添加私有服务器作为 remote
git remote add production ssh://*******************/var/repo/poorlow-tools.git

# 或者使用 Gitea/GitLab
git remote add production https://your-gitea.com/username/poorlow-tools.git
```

### 2. 推送代码触发部署
```bash
# 提交代码
git add .
git commit -m "feat: 新功能"

# 推送到生产环境（触发自动部署）
git push production main
```

### 3. 设置部署脚本别名（可选）
在本地 `package.json` 添加：
```json
{
  "scripts": {
    "deploy": "git push production main",
    "deploy:force": "git push -f production main"
  }
}
```

## 五、安全建议

### 1. SSH 密钥配置
```bash
# 生成部署专用密钥
ssh-keygen -t ed25519 -C "deploy@poorlow-tools" -f ~/.ssh/deploy_key

# 添加到服务器
ssh-copy-id -i ~/.ssh/deploy_key.pub <EMAIL>
```

### 2. 环境变量管理
创建 `.env.production`：
```bash
# 生产环境变量
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.your-domain.com
```

### 3. 部署权限控制
```bash
# 创建部署专用用户
sudo adduser deploy
sudo usermod -aG www-data deploy

# 设置目录权限
sudo chown -R deploy:www-data /var/www/poorlow-tools
sudo chmod -R 755 /var/www/poorlow-tools
```

## 六、监控和日志

### 1. 部署日志查看
```bash
# 查看部署日志
tail -f /var/log/poorlow-tools-deploy.log

# PM2 日志
pm2 logs poorlow-tools
```

### 2. 设置部署通知
```bash
# 钉钉通知示例
curl -X POST "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "msgtype": "text",
    "text": {
      "content": "PoorLow Tools 部署成功！"
    }
  }'
```

## 七、故障恢复

### 1. 自动备份脚本
```bash
#!/bin/bash
# 部署前自动备份
BACKUP_DIR="/var/backups/poorlow-tools"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/backup_$TIMESTAMP.tar.gz \
  --exclude=node_modules \
  --exclude=.next/cache \
  /var/www/poorlow-tools
  
# 保留最近7天的备份
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete
```

### 2. 快速回滚
```bash
#!/bin/bash
# 回滚到上一个版本
cd /var/www/poorlow-tools
git log --oneline -n 10
echo "输入要回滚到的 commit hash:"
read COMMIT_HASH
git reset --hard $COMMIT_HASH
npm ci
npm run build
pm2 restart poorlow-tools
```

## 八、推荐方案总结

对于您的需求，我推荐：

1. **代码托管**：使用 Gitea（轻量、私有、易维护）
2. **自动部署**：Git Hooks + 部署脚本（简单可靠）
3. **进程管理**：PM2（自动重启、日志管理）
4. **监控通知**：简单的 Webhook 通知

这个方案的优点：
- ✅ 完全私有化，代码不外泄
- ✅ 部署简单，维护成本低
- ✅ 自动化程度高，提交即部署
- ✅ 可靠性好，支持回滚

预计搭建时间：2-3小时
