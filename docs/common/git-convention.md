# 📝 Git 提交规范

## Conventional Commits 规范

### 提交格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### Type 类型
- **feat**: 新功能
- **fix**: 修复 bug
- **docs**: 文档更新
- **style**: 代码格式修改（不影响代码运行的变动）
- **refactor**: 重构（既不是新增功能，也不是修复bug）
- **perf**: 性能优化
- **test**: 增加测试
- **chore**: 构建过程或辅助工具的变动
- **ci**: CI/CD 相关修改
- **build**: 构建系统或外部依赖的变更

### Scope 范围（可选）
- **docs**: 文档相关
- **tools**: 工具相关
- **ui**: UI组件
- **i18n**: 国际化
- **deps**: 依赖相关
- **config**: 配置相关

### 示例
```
feat(tools): 添加字数统计工具
docs: 创建项目需求文档
fix(ui): 修复深色模式下的按钮颜色问题
chore(deps): 更新 Next.js 到 14.0.4
```

## 现有提交的规范化

我们现有的提交应该是这样的：

```
docs: 初始化项目，添加需求文档、架构设计和README
docs: 更新需求和架构文档，添加部署方案和主题系统
docs: 添加开发指南文档
docs: 修正文档时间戳并添加任务跟踪列表
docs: 添加详细的视觉设计和用户体验标准
docs: 创建设计系统文档
docs: 更新任务列表，标记文档阶段完成
docs: 添加补充指南和最终检查清单
docs: 更新任务列表，记录项目文档体系完全建立
```

## 配置文件

### .gitmessage 模板
```
# <type>(<scope>): <subject>
# 
# <body>
# 
# <footer>
# 
# Type: feat|fix|docs|style|refactor|perf|test|chore|ci|build
# Scope: 可选，表示影响范围
# Subject: 简短描述，不超过50个字符
# Body: 详细描述，说明为什么做这个改动
# Footer: 关闭的issue或breaking changes
```

### commitlint 配置
```javascript
// commitlint.config.js
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat',
        'fix',
        'docs',
        'style',
        'refactor',
        'perf',
        'test',
        'chore',
        'ci',
        'build'
      ]
    ],
    'subject-case': [2, 'never', ['upper-case']],
    'subject-max-length': [2, 'always', 72]
  }
};
```

### package.json 配置
```json
{
  "scripts": {
    "commit": "cz"
  },
  "devDependencies": {
    "@commitlint/cli": "^18.0.0",
    "@commitlint/config-conventional": "^18.0.0",
    "commitizen": "^4.3.0",
    "cz-conventional-changelog": "^3.3.0",
    "husky": "^8.0.0"
  },
  "config": {
    "commitizen": {
      "path": "./node_modules/cz-conventional-changelog"
    }
  }
}
```

## 使用方法

### 1. 设置 Git 模板
```bash
git config --local commit.template .gitmessage
```

### 2. 使用 commitizen
```bash
npm run commit
```

### 3. 安装 husky hooks
```bash
npx husky-init && npm install
npx husky add .husky/commit-msg 'npx --no -- commitlint --edit "$1"'
```

## 修改历史提交（可选）

如果需要修改已有的提交信息：

```bash
# 交互式 rebase 最近 10 个提交
git rebase -i HEAD~10

# 将需要修改的提交从 pick 改为 reword
# 保存后会逐个让你修改提交信息
```

⚠️ **注意**：只在未推送到远程仓库的情况下修改历史提交！
