# 通用组件库使用指南

本文档介绍了项目中的通用组件库，这些组件为工具开发提供了统一的UI和交互模式。

## 组件概览

### 1. ToolContainer - 工具容器组件
提供统一的工具页面布局容器。

```tsx
import { ToolContainer } from '@/components/common'

<ToolContainer maxWidth="lg" className="py-8">
  {/* 工具内容 */}
</ToolContainer>
```

### 2. ToolHeader - 工具头部组件
显示工具标题、描述、面包屑导航和操作按钮。

```tsx
import { ToolHeader } from '@/components/common'

<ToolHeader
  title="Base64 编码/解码"
  description="在线Base64编码解码工具"
  icon={<FileText className="w-5 h-5" />}
  category="文本处理"
  locale="zh"
  breadcrumbs={[
    { label: '首页', href: '/' },
    { label: '文本工具', href: '/tools/text' },
    { label: 'Base64' }
  ]}
/>
```

### 3. ToolInput/ToolOutput - 输入输出组件
提供统一的输入输出界面。

```tsx
import { ToolInput, ToolOutput } from '@/components/common'

// 输入组件
<ToolInput
  label="输入文本"
  value={input}
  onChange={(e) => setInput(e.target.value)}
  placeholder="请输入要编码的文本"
  showClear
  onClear={() => setInput('')}
/>

// 输出组件
<ToolOutput
  label="编码结果"
  value={output}
  showCopy
  height="md"
/>

// 文件上传
<ToolFileInput
  label="选择文件"
  accept=".txt,.json"
  onChange={(files) => handleFiles(files)}
/>
```

### 4. ToolActions - 操作按钮组件
提供预设的常用操作按钮。

```tsx
import { 
  ToolActions,
  ExecuteButton,
  ResetButton,
  CopyButton,
  QuickActions 
} from '@/components/common'

// 单独使用按钮
<ToolActions position="center">
  <ExecuteButton onClick={handleExecute} loading={isProcessing} />
  <ResetButton onClick={handleReset} />
</ToolActions>

// 使用快捷操作栏
<QuickActions
  onExecute={handleExecute}
  onReset={handleReset}
  onCopy={handleCopy}
  executing={isProcessing}
  canCopy={!!output}
/>
```

### 5. ToolTabs - 标签页组件
用于在不同功能或视图之间切换。

```tsx
import { ToolTabs, TabPanel, TabsContainer } from '@/components/common'

// 手动控制
const [activeTab, setActiveTab] = useState('encode')

<ToolTabs
  tabs={[
    { id: 'encode', label: '编码' },
    { id: 'decode', label: '解码' }
  ]}
  activeTab={activeTab}
  onTabChange={setActiveTab}
  variant="pills"
/>

<TabPanel tabId="encode" activeTab={activeTab}>
  {/* 编码内容 */}
</TabPanel>

// 或使用容器组件
<TabsContainer
  tabs={[
    { id: 'basic', label: '基础设置' },
    { id: 'advanced', label: '高级选项' }
  ]}
  defaultTab="basic"
>
  <TabPanel tabId="basic" activeTab={activeTab}>
    {/* 基础设置内容 */}
  </TabPanel>
  <TabPanel tabId="advanced" activeTab={activeTab}>
    {/* 高级选项内容 */}
  </TabPanel>
</TabsContainer>
```

### 6. ToolSkeleton - 骨架屏组件
提供加载状态的占位显示。

```tsx
import { 
  ToolSkeleton,
  ToolCardSkeleton,
  ToolListSkeleton 
} from '@/components/common'

// 工具页面骨架屏
if (isLoading) {
  return <ToolSkeleton />
}

// 工具列表骨架屏
<ToolListSkeleton count={9} />
```

## 使用示例

### 完整的工具组件示例

```tsx
'use client'

import React, { useState } from 'react'
import {
  ToolContainer,
  ToolHeader,
  ToolInput,
  ToolOutput,
  QuickActions
} from '@/components/common'
import { Hash } from 'lucide-react'

export function Base64Tool({ locale }: { locale: string }) {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  const handleEncode = async () => {
    setIsProcessing(true)
    try {
      const encoded = btoa(input)
      setOutput(encoded)
    } catch (error) {
      console.error(error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleReset = () => {
    setInput('')
    setOutput('')
  }

  const handleCopy = async () => {
    await navigator.clipboard.writeText(output)
  }

  return (
    <ToolContainer>
      <ToolHeader
        title="Base64 编码器"
        description="快速进行Base64编码和解码"
        icon={<Hash className="w-5 h-5" />}
        locale={locale}
      />

      <div className="space-y-6">
        <ToolInput
          label="输入文本"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="请输入要编码的文本"
          showClear
          onClear={() => setInput('')}
        />

        <QuickActions
          onExecute={handleEncode}
          onReset={handleReset}
          onCopy={handleCopy}
          executing={isProcessing}
          canCopy={!!output}
        />

        <ToolOutput
          label="编码结果"
          value={output}
          showCopy
          height="md"
        />
      </div>
    </ToolContainer>
  )
}
```

## 设计原则

1. **一致性**: 所有工具使用相同的布局和交互模式
2. **可访问性**: 支持键盘导航和屏幕阅读器
3. **响应式**: 适配各种屏幕尺寸
4. **国际化**: 内置多语言支持
5. **性能**: 使用React.memo和懒加载优化性能

## 样式定制

所有组件都支持通过 `className` 属性进行样式定制：

```tsx
<ToolContainer className="custom-container">
  <ToolHeader className="custom-header" />
  <ToolInput className="custom-input" />
</ToolContainer>
```

## 最佳实践

1. **使用语义化的标签和描述**
   - 为每个输入输出区域提供清晰的标签
   - 使用 placeholder 提供输入提示

2. **提供即时反馈**
   - 使用 loading 状态显示处理进度
   - 显示错误信息和成功提示

3. **优化用户体验**
   - 提供一键清空和复制功能
   - 支持文件拖拽上传
   - 保存用户的偏好设置

4. **遵循无障碍标准**
   - 使用正确的ARIA标签
   - 确保键盘可访问性
   - 提供足够的颜色对比度

## 组件依赖

- React 18+
- Tailwind CSS
- lucide-react (图标库)
- @/lib/utils (工具函数)
- @/hooks/useTranslation (国际化)

## 更新日志

### v1.0.0 (2024-01-20)
- 初始版本发布
- 包含基础组件集
- 支持中英文界面
- 响应式设计
- 深色模式支持
