# 🏗️ PoorLow Tools - 架构设计文档

## 1. 系统架构概览

### 1.1 架构原则
- **混合架构**：初期纯前端，预留后端扩展能力
- **静态站点生成**：利用 Next.js SSG 提升性能和 SEO
- **渐进式加载**：按需加载工具，优化首屏体验
- **组件化设计**：统一的设计语言和高度复用的组件体系
- **响应式优先**：移动端优先的设计理念
- **AI 友好**：标准化的代码结构，便于 AI 辅助开发
- **极致 SEO**：每个工具独立页面，完整的结构化数据

### 1.2 技术架构图
```
┌─────────────────────────────────────────────────────────┐
│                      用户界面层                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │   布局组件   │  │   UI 组件   │  │  工具组件   │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
├─────────────────────────────────────────────────────────┤
│                      应用逻辑层                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │  状态管理   │  │   路由管理   │  │  国际化     │    │
│  │  (Zustand)  │  │  (Next.js)  │  │ (next-intl) │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
├─────────────────────────────────────────────────────────┤
│                      工具核心层                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │  文本处理   │  │  数据转换   │  │  加密算法   │    │
│  │    引擎     │  │    引擎     │  │    引擎     │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
├─────────────────────────────────────────────────────────┤
│                      基础服务层                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │  本地存储   │  │  Web APIs  │  │  工具函数   │    │
│  │(localStorage)│  │  (Canvas等) │  │   库       │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────────────────────────┘
```

## 2. 目录结构设计

```
poorlow-tools/
├── src/
│   ├── app/                      # Next.js App Router
│   │   ├── [locale]/            # 国际化路由
│   │   │   ├── layout.tsx       # 根布局
│   │   │   ├── page.tsx         # 首页
│   │   │   ├── tools/           # 工具路由
│   │   │   │   ├── layout.tsx
│   │   │   │   └── [category]/
│   │   │   │       └── [tool]/
│   │   │   │           └── page.tsx
│   │   │   └── (marketing)/     # 营销页面
│   │   │       ├── about/
│   │   │       ├── privacy/
│   │   │       └── terms/
│   │   ├── api/                 # API 路由（如需要）
│   │   └── globals.css          # 全局样式
│   │
│   ├── components/              # 组件库
│   │   ├── ui/                  # 基础 UI 组件（统一设计语言）
│   │   │   ├── button/          # 各种按钮组件
│   │   │   ├── input/           # 输入框组件
│   │   │   ├── card/            # 卡片组件
│   │   │   ├── dialog/          # 对话框组件
│   │   │   ├── toast/           # 提示组件
│   │   │   ├── dropdown/        # 下拉菜单
│   │   │   ├── tabs/            # 标签页
│   │   │   └── ...              # 更多基础组件
│   │   ├── layout/              # 布局组件
│   │   │   ├── header.tsx       # 全站统一头部
│   │   │   ├── footer.tsx       # 全站统一尾部
│   │   │   ├── sidebar.tsx      # 侧边栏
│   │   │   ├── mobile-nav.tsx   # 移动端导航
│   │   │   └── tool-layout.tsx  # 工具页面布局
│   │   ├── tools/               # 工具相关组件
│   │   │   ├── tool-card.tsx
│   │   │   ├── tool-grid.tsx
│   │   │   ├── tool-search.tsx
│   │   │   └── tool-wrapper.tsx # 工具通用包装器
│   │   └── common/              # 通用组件
│   │       ├── theme-provider.tsx # 主题提供者
│   │       ├── theme-toggle.tsx
│   │       ├── language-switch.tsx
│   │       ├── seo-meta.tsx
│   │       └── search-box.tsx    # 全站搜索
│   │
│   ├── lib/                     # 核心库
│   │   ├── tools/               # 工具实现
│   │   │   ├── text/           # 文本工具
│   │   │   ├── dev/            # 开发工具
│   │   │   ├── crypto/         # 加密工具
│   │   │   └── ...
│   │   ├── utils/               # 工具函数
│   │   │   ├── cn.ts
│   │   │   ├── format.ts
│   │   │   └── storage.ts
│   │   └── constants/           # 常量定义
│   │       ├── tools.ts
│   │       └── routes.ts
│   │
│   ├── hooks/                   # 自定义 Hooks
│   │   ├── use-local-storage.ts
│   │   ├── use-theme.ts
│   │   └── use-tool-state.ts
│   │
│   ├── types/                   # TypeScript 类型
│   │   ├── tools.ts
│   │   ├── i18n.ts
│   │   └── global.d.ts
│   │
│   └── i18n/                    # 国际化配置
│       ├── config.ts
│       └── request.ts
│
├── public/                      # 静态资源
│   ├── locales/                # 翻译文件
│   │   ├── zh/
│   │   ├── en/
│   │   └── ja/
│   ├── images/
│   └── fonts/
│
├── tests/                       # 测试文件
│   ├── unit/
│   ├── integration/
│   └── e2e/
│
└── 配置文件
    ├── next.config.js
    ├── tailwind.config.ts
    ├── tsconfig.json
    ├── package.json
    └── .env.local
```

## 3. 核心模块设计

### 3.1 工具框架设计

```typescript
// 工具基础接口
interface Tool {
  id: string;
  category: ToolCategory;
  name: I18nString;
  description: I18nString;
  keywords: string[];
  icon: LucideIcon;
  component: LazyExoticComponent<ToolComponent>;
  seo: ToolSEO;
}

// 工具组件接口
interface ToolComponent {
  onStateChange?: (state: any) => void;
  initialState?: any;
}

// 工具注册系统
class ToolRegistry {
  private tools: Map<string, Tool>;
  
  register(tool: Tool): void;
  getByCategory(category: ToolCategory): Tool[];
  search(query: string, locale: Locale): Tool[];
}
```

### 3.2 国际化架构

```typescript
// 语言配置
export const locales = ['zh', 'en', 'ja'] as const;
export type Locale = typeof locales[number];

// 路由结构
// /[locale]/tools/[category]/[tool]
// 例如：/zh/tools/text/word-counter

// 翻译文件结构
// public/locales/zh/common.json
// public/locales/zh/tools.json
// public/locales/zh/[tool-id].json
```

### 3.3 状态管理设计

```typescript
// Zustand Store 设计
interface AppStore {
  // 用户偏好
  theme: 'light' | 'dark' | 'system' | 'custom';
  customTheme?: CustomTheme;
  locale: Locale;
  
  // 工具状态
  favorites: string[];
  recentTools: string[];
  toolStates: Record<string, any>;
  searchHistory: string[];
  
  // Actions
  setTheme: (theme: Theme) => void;
  setCustomTheme: (customTheme: CustomTheme) => void;
  setLocale: (locale: Locale) => void;
  addFavorite: (toolId: string) => void;
  saveToolState: (toolId: string, state: any) => void;
  addSearchHistory: (query: string) => void;
}

// 自定义主题接口
interface CustomTheme {
  primary: string;
  secondary: string;
  background: string;
  foreground: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;
  border: string;
  input: string;
  ring: string;
  radius: string;
  fontFamily?: string;
}
```

### 3.4 性能优化策略

#### 3.4.1 代码分割
```typescript
// 工具组件懒加载
const WordCounter = lazy(() => 
  import('@/lib/tools/text/word-counter')
);

// 路由级别代码分割
// Next.js 自动处理
```

#### 3.4.2 资源优化
- 图片：使用 Next.js Image 组件
- 字体：字体子集化，只加载使用的字符
- CSS：PurgeCSS 移除未使用样式
- JS：Tree Shaking 和压缩

#### 3.4.3 缓存策略
```typescript
// Service Worker 配置
// 缓存静态资源和工具代码
const CACHE_NAME = 'poorlow-tools-v1';
const urlsToCache = [
  '/',
  '/styles/main.css',
  '/scripts/main.js'
];
```

## 4. 工具开发规范

### 4.1 工具组件模板

```typescript
// lib/tools/[category]/[tool-name].tsx
import { ToolComponent } from '@/types/tools';

export const ToolNameComponent: ToolComponent = ({ 
  onStateChange, 
  initialState 
}) => {
  // 工具逻辑实现
  return (
    <div className="tool-container">
      {/* 工具 UI */}
    </div>
  );
};

// 工具配置
export const toolConfig: Tool = {
  id: 'tool-name',
  category: 'text',
  name: {
    zh: '工具名称',
    en: 'Tool Name',
    ja: 'ツール名'
  },
  // ...
};
```

### 4.2 工具测试规范

```typescript
// tests/unit/tools/[tool-name].test.ts
describe('ToolName', () => {
  it('应该正确处理输入', () => {
    // 测试逻辑
  });
  
  it('应该保存和恢复状态', () => {
    // 测试状态管理
  });
});
```

## 5. SEO 技术方案

### 5.1 页面元数据
```typescript
// 每个工具页面的 metadata
export const metadata: Metadata = {
  title: '字数统计器 - PoorLow Tools',
  description: '免费在线字数统计工具...',
  keywords: ['字数统计', '字符计数'],
  openGraph: {
    // Open Graph 数据
  }
};
```

### 5.2 结构化数据
```json
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "字数统计器",
  "applicationCategory": "UtilityApplication",
  "operatingSystem": "Web Browser"
}
```

## 6. 安全设计

### 6.1 内容安全策略
```typescript
// CSP 配置
const cspHeader = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' blob: data:;
  font-src 'self';
`;
```

### 6.2 输入验证
- 所有用户输入进行验证
- 防止 XSS 攻击
- 限制文件大小和类型

## 7. 部署架构

### 7.1 自建服务器部署方案

```yaml
# Docker Compose 配置
version: '3.8'
services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
      
  # 后端服务预留
  api:
    build: ./api
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
```

### 7.2 CI/CD 流程
```yaml
# GitHub Actions / GitLab CI 配置
stages:
  - lint
  - test
  - build
  - deploy

deploy:
  script:
    - docker build -t poorlow-tools .
    - docker push registry.example.com/poorlow-tools
    - ssh deploy@server 'docker pull && docker-compose up -d'
```

### 7.3 后端架构预留

```
┌─────────────────────────────────────────────────────────┐
│                     负载均衡器 (Nginx)                    │
└─────────────────────────────────────────────────────────┘
                              │
    ┌─────────────────────────┴─────────────────────────┐
    │                                                   │
┌───▼────────────┐                          ┌──────────▼────────┐
│  Next.js 应用   │                          │   API 服务器      │
│  (SSR/SSG)     │                          │  (Node.js/Python) │
└────────────────┘                          └──────────┬────────┘
                                                       │
                                            ┌──────────▼────────┐
                                            │   数据库/缓存     │
                                            │ PostgreSQL/Redis  │
                                            └───────────────────┘
```

## 8. 监控和分析

### 8.1 性能监控
- Web Vitals 指标
- 用户行为分析
- 错误追踪

### 8.2 使用分析
- 工具使用频率
- 用户偏好统计
- 转化率分析

## 9. 公共组件设计规范

### 9.1 设计系统

```typescript
// 设计令牌 (Design Tokens)
export const tokens = {
  colors: {
    primary: { /* 主色阶 */ },
    secondary: { /* 辅助色阶 */ },
    neutral: { /* 中性色阶 */ },
    semantic: { /* 语义色 */ }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem'
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['Fira Code', 'monospace']
    },
    fontSize: { /* 字号系统 */ },
    lineHeight: { /* 行高系统 */ }
  },
  shadows: { /* 阴影系统 */ },
  animations: { /* 动画系统 */ }
};
```

### 9.2 组件开发规范

```typescript
// 组件模板
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  // ... 更多属性
}

export const Button: FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  ...props
}) => {
  // 统一的样式系统
  const classes = cn(
    'base-button-styles',
    variantStyles[variant],
    sizeStyles[size]
  );
  
  return <button className={classes} {...props} />;
};
```

## 10. AI 辅助开发策略

### 10.1 代码组织原则
- **单一职责**：每个文件/函数只做一件事
- **命名规范**：使用描述性的英文命名
- **类型完整**：所有函数都有完整的 TypeScript 类型
- **注释充分**：关键逻辑都有清晰的注释

### 10.2 AI 友好的项目结构
```
- 使用标准的文件命名：tool-name.tsx, use-tool-name.ts
- 统一的导入/导出模式
- 标准化的错误处理
- 可预测的状态管理
```

### 10.3 文档和示例
- 每个组件都有使用示例
- 工具开发模板和指南
- API 文档自动生成

## 11. SEO 技术架构

### 11.1 页面结构优化
```typescript
// 工具页面 SEO 组件
export function ToolSEO({ tool }: { tool: Tool }) {
  return (
    <>
      <Head>
        <title>{tool.title} - {siteName}</title>
        <meta name="description" content={tool.description} />
        <link rel="canonical" href={tool.url} />
        
        {/* Open Graph */}
        <meta property="og:title" content={tool.title} />
        <meta property="og:description" content={tool.description} />
        
        {/* 结构化数据 */}
        <script type="application/ld+json">
          {JSON.stringify(generateSchema(tool))}
        </script>
        
        {/* 多语言 */}
        <link rel="alternate" hreflang="zh" href={`/zh${tool.path}`} />
        <link rel="alternate" hreflang="en" href={`/en${tool.path}`} />
        <link rel="alternate" hreflang="ja" href={`/ja${tool.path}`} />
      </Head>
    </>
  );
}
```

### 11.2 性能优化
- 预渲染所有工具页面
- 图片懒加载和优化
- 关键 CSS 内联
- 资源预加载

## 12. 扩展性设计

### 12.1 工具扩展机制
- 插件化的工具注册系统
- 动态路由生成
- 自动化的工具文档生成
- 工具版本管理

### 12.2 未来 1000 个工具的架构考虑
- 工具分类的层级化
- 高效的搜索索引
- 渐进式的加载策略
- 模块化的打包方案

## 13. 更新记录
- 2025-07-16：初始架构设计
- 2025-07-16：添加自建服务器部署、主题系统、公共组件规范、AI友好特性和SEO架构
