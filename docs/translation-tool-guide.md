# 批量翻译工具使用指南

> 创建时间：2025-01-20 15:27
> 作者：AI Assistant

## 概述

批量翻译工具用于自动化翻译项目中的多语言文件，支持从中文翻译到英文和日文。该工具包含技术术语词典、质量检查和完整性验证功能。

## 安装依赖

```bash
npm install --save-dev tsx commander chalk glob
```

## 使用方法

### 1. 翻译单个文件

```bash
# 翻译 common.json 到英文和日文
npm run translate:file -- -f common.json -t en,ja

# 只翻译到英文
npm run translate:file -- -f navigation.json -t en

# 指定源语言（默认是 zh）
npm run translate:file -- -f home.json -t en -s zh
```

### 2. 批量翻译文件

```bash
# 翻译所有 JSON 文件
npm run translate:batch

# 翻译 tools 目录下的所有文件
npm run translate:batch -- -p "tools/*.json"

# 翻译所有文件到指定语言
npm run translate:batch -- -p "**/*.json" -t en,ja
```

### 3. 检查翻译完整性

```bash
# 检查所有翻译文件的完整性
npm run translate:check
```

该命令会检查：
- 缺失的翻译键
- 多余的翻译键
- 缺失的翻译文件

## 功能特性

### 技术术语词典

工具内置了常用技术术语的翻译对照表，确保技术术语翻译的一致性：

- 编码 → Encode / エンコード
- 解码 → Decode / デコード
- 格式化 → Format / フォーマット
- 等等...

### 保留不翻译的内容

以下内容会自动保留原文：
- 全大写常量（如 `API_KEY`）
- 技术键名（如 `id`, `key`, `code`）
- 文件扩展名（如 `.png`, `.json`）
- URL 和 Email
- 纯数字
- 颜色值（如 `#FF0000`）
- 技术缩写（如 `Base64`, `MD5`, `JWT`）

### 质量检查

翻译后会自动进行以下质量检查：

1. **长度比例检查**
   - 英文翻译：0.8-2.0 倍原文长度
   - 日文翻译：0.5-1.5 倍原文长度

2. **未翻译中文检查**
   - 确保翻译结果中没有遗留的中文字符

3. **占位符保留检查**
   - 确保 `{name}` 等占位符在翻译后保持不变

4. **HTML标签保留检查**
   - 确保 HTML 标签在翻译后保持完整

## 注意事项

### 当前限制

1. **模拟翻译模式**
   - 当前脚本使用模拟翻译，返回 `[EN] 原文` 格式
   - 需要集成实际的翻译 API（如 Google Translate API）

2. **API 密钥配置**
   - 使用实际翻译 API 时，需要在 `.env` 文件中配置：
   ```
   GOOGLE_TRANSLATE_API_KEY=your_api_key_here
   ```

3. **请求限流**
   - 脚本包含延迟机制防止 API 请求过快
   - 批量翻译大量文件时需要耐心等待

### 最佳实践

1. **定期检查完整性**
   ```bash
   npm run translate:check
   ```

2. **增量翻译**
   - 只翻译新增或修改的文件
   - 避免重复翻译已完成的内容

3. **人工审核**
   - 自动翻译后需要人工审核
   - 特别注意专业术语和上下文相关的翻译

4. **版本控制**
   - 翻译前先提交当前更改
   - 翻译后单独提交翻译文件

## 扩展开发

### 添加新语言

1. 在 `TECHNICAL_TERMS` 中添加新语言的术语对照
2. 在 `checkTranslations` 函数中添加新语言代码
3. 更新相关文档

### 集成其他翻译 API

1. 修改 `translateText` 函数
2. 添加相应的 API 配置
3. 实现错误处理和重试机制

### 自定义质量检查规则

在 `QUALITY_CHECKS` 对象中添加新的检查函数：

```typescript
newCheck: (original: string, translated: string) => {
  // 自定义检查逻辑
  return true; // 或 false
}
```

## 故障排除

### 常见问题

1. **命令未找到**
   - 确保已安装 `tsx` 依赖
   - 检查 `package.json` 中的脚本配置

2. **翻译失败**
   - 检查源文件 JSON 格式是否正确
   - 确保文件路径正确
   - 查看错误信息定位问题

3. **完整性检查失败**
   - 根据提示补充缺失的翻译
   - 删除多余的翻译键
   - 确保文件结构一致

## 更新日志

### v1.0.0 (2025-01-20)
- 初始版本发布
- 支持中英日三语翻译
- 包含技术术语词典
- 实现质量检查功能
- 提供完整性验证

---

如有问题或建议，请联系开发团队。
