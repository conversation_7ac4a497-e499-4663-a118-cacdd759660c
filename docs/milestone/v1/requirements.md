# 🌐 PoorLow Tools - 全球化在线工具站需求文档

## 1. 项目概述

### 1.1 项目背景
为个人开发者和日常用户打造一个功能全面、体验极致的在线工具集合站。所有工具均在浏览器端运行，无需服务器处理，确保用户数据隐私和安全。

### 1.2 项目目标
- 初期提供 100 个实用的在线工具，未来扩展至 1000 个
- 支持中文、英文、日文三语言
- 极致的性能和 SEO 体验
- 完全的移动端兼容
- 符合 W3C 标准和无障碍规范
- AI 友好的技术架构，便于大规模 AI 辅助开发
- 支持后端扩展能力，为复杂工具预留接口

### 1.3 目标用户
- 开发者：需要各种开发辅助工具
- 设计师：需要图像处理和颜色工具
- 学生：需要学习和计算工具
- 普通用户：需要日常生活工具

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 工具集合（100个工具）

**文本处理工具（15个）**
1. 字数统计器 - 统计字符、单词、段落
2. 文本比较器 - 对比两段文本差异
3. 大小写转换 - 各种大小写格式转换
4. 文本去重 - 去除重复行
5. 查找替换 - 批量查找替换
6. 文本排序 - 按字母、数字排序
7. 文本反转 - 字符串反转
8. 空格处理 - 去除多余空格
9. 文本加密 - 简单加密解密
10. 文本转语音 - TTS 功能
11. 繁简转换 - 中文繁简体转换
12. 拼音转换 - 汉字转拼音
13. 文本提取 - 提取邮箱、电话等
14. Lorem 生成器 - 占位文本生成
15. 文本统计分析 - 词频分析等

**开发工具（20个）**
1. JSON 格式化 - 美化和压缩
2. JSON 转换器 - JSON 转 CSV/XML
3. Base64 编解码
4. URL 编解码
5. HTML 实体编解码
6. 正则表达式测试
7. 代码美化 - JS/CSS/HTML
8. 代码压缩
9. 颜色选择器
10. 颜色格式转换
11. CSS 生成器
12. Markdown 编辑器
13. SQL 格式化
14. Unix 时间戳转换
15. Cron 表达式生成
16. JWT 解析器
17. 差异对比工具
18. ASCII 艺术生成
19. 代码片段管理
20. API 测试工具

**加密安全（10个）**
1. MD5 生成器
2. SHA 系列生成器
3. HMAC 生成器
4. AES 加密解密
5. RSA 密钥生成
6. 密码强度检测
7. 密码生成器
8. UUID 生成器
9. 哈希对比工具
10. 文件哈希计算

**图像处理（10个）**
1. 图片压缩
2. 图片格式转换
3. 图片裁剪
4. 图片旋转翻转
5. 二维码生成器
6. 二维码解析器
7. 图片水印
8. 图片滤镜
9. ICO 图标生成
10. 图片 Base64 转换

**文件转换（10个）**
1. PDF 转图片
2. 图片转 PDF
3. Word 转 PDF
4. Excel 转 CSV
5. Markdown 转 HTML
6. HTML 转 Markdown
7. XML 转 JSON
8. CSV 转 JSON
9. 电子书转换
10. 字体格式转换

**计算工具（10个）**
1. 科学计算器
2. 单位换算器
3. 汇率转换器
4. 贷款计算器
5. 个税计算器
6. BMI 计算器
7. 日期计算器
8. 时区转换器
9. 进制转换器
10. 百分比计算器

**生活助手（10个）**
1. 番茄钟计时器
2. 倒计时器
3. 秒表计时器
4. 待办事项
5. 便签记事本
6. 密码管理器
7. 随机数生成
8. 抽签决策器
9. 二维码名片
10. 个人简历生成

**数据分析（10个）**
1. 数据图表生成
2. 数据透视表
3. 统计分析工具
4. 数据清洗工具
5. 数据可视化
6. 趋势分析
7. 相关性分析
8. 数据导入导出
9. 数据模拟生成
10. 报表生成器

**网络工具（5个）**
1. IP 地址查询
2. 域名查询
3. 端口扫描
4. 网速测试
5. HTTP 状态码

### 2.2 系统功能

#### 2.2.1 多语言支持
- 支持中文（简体）、英文、日文
- 自动检测用户语言偏好
- 一键切换语言
- URL 路径包含语言标识

#### 2.2.2 搜索功能
- 全局工具搜索
- 支持工具名称、描述、标签搜索
- 搜索建议和自动完成
- 搜索历史记录

#### 2.2.3 用户偏好
- 收藏常用工具
- 最近使用记录
- 主题系统
  - 自动跟随系统主题
  - 手动切换深色/浅色模式
  - 自定义主题支持（颜色、字体等）
- 布局偏好设置

#### 2.2.4 分享功能
- 工具状态 URL 分享
- 社交媒体分享
- 二维码分享
- 复制链接

## 3. 非功能需求

### 3.1 性能要求
- 首屏加载时间 < 1 秒
- 工具切换响应 < 200ms
- 动画流畅度 60fps
- 内存占用 < 100MB

### 3.2 兼容性要求
- 浏览器：Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- 移动端：iOS 13+, Android 8+
- 屏幕尺寸：320px - 4K
- 网络环境：支持离线使用

### 3.3 安全要求
- 所有数据本地处理
- HTTPS 加密传输
- CSP 安全策略
- XSS 防护

### 3.4 SEO 要求
- 每个工具独立 URL
- 结构化数据标记
- 站点地图自动生成
- 页面加载性能优化
- 移动友好性

### 3.5 无障碍要求
- WCAG 2.1 AA 级别
- 键盘导航支持
- 屏幕阅读器兼容
- 高对比度模式
- 字体大小可调

## 4. 技术规范

### 4.1 技术栈
- 框架：Next.js 14 (App Router)
- 语言：TypeScript 5.x
- 样式：TailwindCSS 3.x + shadcn/ui
- 状态管理：Zustand
- 国际化：next-intl
- 测试：Vitest + Testing Library

### 4.2 开发规范
- 代码规范：ESLint + Prettier
- Git 提交规范：Conventional Commits
- 分支策略：Git Flow
- 代码审查：PR Review

### 4.3 部署要求
- 自建服务器部署
- Nginx 反向代理
- Docker 容器化部署
- CI/CD 自动化流程
- 监控告警系统
- 后端 API 预留（Node.js/Python）

## 5. 项目计划

### 5.1 里程碑
1. **M1 - 基础架构**（第1-2周）
   - 项目初始化
   - 基础组件开发
   - 国际化框架

2. **M2 - 核心功能**（第3-4周）
   - 工具框架开发
   - 前10个工具
   - 搜索功能

3. **M3 - 工具开发**（第5-10周）
   - 完成100个工具
   - 移动端适配
   - 性能优化

4. **M4 - 上线准备**（第11-12周）
   - SEO 优化
   - 测试完善
   - 部署上线

### 5.2 风险管理
- 工具复杂度评估
- 性能瓶颈预防
- 浏览器兼容性测试
- 用户体验迭代

## 6. 成功标准
- 100个工具全部可用且具备实用性
- 页面加载速度达标
- SEO 评分 > 98（极致 SEO）
- 移动端体验流畅
- 无障碍测试通过
- 统一的设计语言和组件体系
- AI 辅助开发效率提升 80%

## 7. 设计规范

### 7.1 统一设计语言
- **布局结构**：统一的头部导航、尾部信息、中间内容区
- **组件库**：完整的基础组件（按钮、输入框、卡片、对话框等）
- **颜色系统**：主色、辅助色、语义色、中性色
- **字体规范**：标题、正文、代码等字体层级
- **间距系统**：统一的 padding、margin 规范
- **动画规范**：过渡效果、加载动画、交互反馈

### 7.2 视觉设计要求

#### 7.2.1 设计原则
- **简洁清晰**：界面简洁，功能明确，避免视觉噪音
- **层次分明**：通过大小、颜色、间距建立清晰的视觉层次
- **一致性**：所有页面和组件保持视觉一致性
- **现代感**：采用现代设计语言，符合当前主流审美
- **专业性**：体现工具的专业性和可靠性

#### 7.2.2 颜色系统
```scss
// 主色调
$primary: #3B82F6;      // 专业蓝
$primary-dark: #2563EB;
$primary-light: #60A5FA;

// 中性色
$gray-50: #F9FAFB;
$gray-100: #F3F4F6;
$gray-200: #E5E7EB;
$gray-300: #D1D5DB;
$gray-400: #9CA3AF;
$gray-500: #6B7280;
$gray-600: #4B5563;
$gray-700: #374151;
$gray-800: #1F2937;
$gray-900: #111827;

// 语义色
$success: #10B981;
$warning: #F59E0B;
$error: #EF4444;
$info: #3B82F6;

// 深色模式
$dark-bg: #0F172A;
$dark-surface: #1E293B;
$dark-border: #334155;
```

#### 7.2.3 排版系统
- **字体选择**
  - 中文：-apple-system, "PingFang SC", "Microsoft YaHei"
  - 英文：Inter, system-ui, -apple-system
  - 代码：'Fira Code', 'SF Mono', Consolas
- **字号规范**
  - 标题1: 36px/48px
  - 标题2: 30px/40px
  - 标题3: 24px/32px
  - 正文: 16px/24px
  - 辅助: 14px/20px
  - 说明: 12px/16px
- **行高**: 1.5-1.75
- **段落间距**: 16px-24px

#### 7.2.4 间距系统
- 采用 8px 基础网格系统
- 间距规范: 4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px
- 组件内边距: 16px-24px
- 卡片间距: 24px-32px
- 页面边距: 移动端 16px, 桌面端 24px-48px

#### 7.2.5 阴影系统
```css
/* 轻微阴影 - 用于卡片悬浮 */
box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

/* 中等阴影 - 用于下拉菜单 */
box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

/* 强调阴影 - 用于弹窗 */
box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
```

#### 7.2.6 圆角规范
- 小圆角: 4px (按钮、输入框)
- 中圆角: 8px (卡片、容器)
- 大圆角: 12px (弹窗、大卡片)
- 圆形: 50% (头像、徽章)

### 7.3 交互设计要求

#### 7.3.1 交互原则
- **即时反馈**：所有操作都有明确的视觉或听觉反馈
- **可预测性**：交互行为符合用户预期
- **容错性**：提供撤销、重做等容错机制
- **效率优先**：减少操作步骤，提高使用效率
- **无障碍**：支持键盘操作和屏幕阅读器

#### 7.3.2 状态反馈
- **悬浮状态**: 颜色加深 10%, 鼠标指针变化
- **点击状态**: 缩放 95%, 颜色加深 20%
- **禁用状态**: 透明度 50%, 鼠标指针禁用
- **加载状态**: 骨架屏或加载动画
- **错误状态**: 红色边框和错误提示

#### 7.3.3 动画规范
- **时长**: 快速 150ms, 常规 250ms, 缓慢 350ms
- **缓动函数**: ease-out (退出), ease-in-out (进出)
- **使用场景**
  - 页面切换: 淡入淡出
  - 组件展开: 高度/宽度过渡
  - 状态切换: 颜色/透明度过渡
  - 微交互: 缩放/旋转效果

#### 7.3.4 响应式设计
- **断点设置**
  - 移动端: < 640px
  - 平板: 640px - 1024px
  - 桌面: > 1024px
  - 大屏: > 1440px
- **适配策略**
  - 弹性布局优先
  - 重要内容优先显示
  - 触摸友好的交互元素
  - 适配不同屏幕密度

### 7.4 用户体验标准

#### 7.4.1 性能体验
- **加载速度**: 首屏 < 1s, 交互响应 < 100ms
- **流畅度**: 动画 60fps, 滚动无卡顿
- **资源优化**: 图片压缩, 懒加载, CDN 加速

#### 7.4.2 操作体验
- **单手操作**: 移动端重要操作可单手完成
- **快捷键**: 支持常用快捷键操作
- **批量操作**: 支持批量选择和处理
- **历史记录**: 保存用户操作历史
- **个性化**: 记住用户偏好设置

#### 7.4.3 信息架构
- **导航清晰**: 不超过 3 层级
- **搜索优先**: 醒目的搜索入口
- **分类合理**: 工具按使用场景分类
- **标签系统**: 多维度标签便于查找
- **相关推荐**: 智能推荐相关工具

#### 7.4.4 错误处理
- **友好提示**: 人性化的错误信息
- **解决方案**: 提供明确的解决步骤
- **降级方案**: 功能异常时的备选方案
- **错误恢复**: 自动保存和恢复机制

### 7.5 界面组件规范

#### 7.5.1 导航栏
- 固定高度: 64px (桌面) / 56px (移动)
- Logo 位置: 左侧
- 主导航: 中部
- 功能区: 右侧（搜索、语言、主题、用户）
- 移动端: 汉堡菜单

#### 7.5.2 工具卡片
- 尺寸: 最小 240px x 180px
- 内容: 图标、标题、描述、标签
- 交互: 悬浮效果、点击进入
- 状态: 新工具标记、热门标记

#### 7.5.3 工具页面
- 布局: 左侧输入区、右侧结果区
- 工具栏: 顶部操作按钮
- 说明区: 使用说明和示例
- 反馈区: 评分和反馈入口

#### 7.5.4 表单设计
- 标签位置: 顶部对齐
- 输入框高度: 40px-48px
- 错误提示: 输入框下方
- 必填标记: 红色星号
- 帮助文本: 灰色小字

### 7.6 工具实用性要求
- 每个工具必须解决实际问题
- 功能完整，不能过于简单
- 提供丰富的选项和配置
- 支持批量处理
- 结果可导出/复制
- 提供使用说明和示例

### 7.7 SEO 极致优化
- 每个工具独立的 URL 和页面
- 丰富的结构化数据 (Schema.org)
- 优化的页面标题和描述
- 内部链接优化
- 图片 ALT 标签
- 语义化 HTML
- 快速的页面加载
- 移动友好性
- 国际化 SEO（hreflang）

## 8. 技术架构补充

### 8.1 后端预留设计
- RESTful API 设计规范
- GraphQL 接口支持
- WebSocket 实时通信
- 微服务架构预留
- 数据库设计（PostgreSQL/MongoDB）
- 缓存策略（Redis）
- 消息队列（RabbitMQ/Kafka）

### 8.2 AI 友好特性
- 使用主流稳定的技术栈
- 清晰的代码结构和命名
- 完整的类型定义
- 丰富的代码注释
- 标准化的组件模式
- 可预测的文件组织

## 9. 更新记录
- 2025-07-16：初始版本创建
- 2025-07-16：更新部署方案、主题系统、设计规范和 AI 友好特性
- 2025-07-16：添加详细的视觉设计和用户体验标准
