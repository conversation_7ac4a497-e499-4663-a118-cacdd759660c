# CLAUDE.md

This file contains guidance for Claude Code instances to work effectively in this repository.

## Project Overview

PoorLow Tools is a comprehensive online tools collection with 100+ utilities running entirely in the browser. It's built with Next.js 14 and supports Chinese, English, and Japanese languages.

## Common Commands

### Development
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run linting
npm run lint

# Commit with conventional commits
npm run commit
```

### Testing
```bash
# Run unit tests
npm run test

# Run test coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

## Architecture Overview

### Core Architecture Pattern
This project uses a **tool registry pattern** with dynamic imports for scalability and performance:

- **Central Registry**: `src/lib/tools/registry.ts` contains all 100+ tools with dynamic imports
- **Tool Definition**: Each tool has multilingual metadata (name, description, keywords) and React component
- **Category System**: Tools are organized into 13 categories defined in `src/lib/constants/categories.ts`
- **Type Safety**: Strong TypeScript typing via `src/types/tool.ts`

### Key Files and Their Purposes

#### Tool System Core
- `src/lib/tools/registry.ts` - Central tool registry with dynamic imports and search functionality
- `src/types/tool.ts` - TypeScript interfaces for Tool and ToolCategory types
- `src/lib/constants/categories.ts` - Category definitions with multilingual support

#### Tool Components
- `src/components/tools/` - Individual tool implementations (100+ components)
- All tools follow naming convention: `[ToolName]Tool.tsx`

### Multilingual Architecture
- **next-intl**: Handles i18n with locale-specific routing
- **Structured Translation**: Each tool has name, description, and keywords in 3 languages
- **Locale Files**: `public/locales/` contains translation files
- **Search Support**: Search functionality works across all languages

### Technology Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript with strict mode
- **Styling**: TailwindCSS + shadcn/ui components
- **State Management**: Zustand for global state
- **Icons**: Lucide React
- **Testing**: Vitest + Testing Library

### Tool Registration Example
```typescript
{
  id: 'base64',
  name: {
    zh: 'Base64 编解码',
    en: 'Base64 Encode/Decode',
    ja: 'Base64エンコード/デコード',
  },
  description: {
    zh: '在线 Base64 编码和解码工具',
    en: 'Online Base64 encoding and decoding tool',
    ja: 'オンラインBase64エンコード・デコードツール',
  },
  category: 'crypto',
  icon: Code,
  path: '/tools/base64',
  keywords: {
    zh: ['base64', '编码', '解码'],
    en: ['base64', 'encode', 'decode'],
    ja: ['base64', 'エンコード', 'デコード'],
  },
  component: Base64Tool,
}
```

### Categories
13 tool categories: text, image, crypto, converter, developer, calculator, generator, formatter, validator, productivity, lifestyle, network, other

### Privacy & Security
- All tools run client-side only
- No server-side data processing
- Privacy-first architecture for user data protection

## Development Guidelines

### Adding New Tools
1. Create component in `src/components/tools/`
2. Add to registry in `src/lib/tools/registry.ts`
3. Include multilingual metadata
4. Use dynamic imports for performance
5. Follow TypeScript strict mode

### Code Style
- Use ESLint + Prettier for formatting
- Follow shadcn/ui component patterns
- Implement responsive design with TailwindCSS
- Use Zustand for state management when needed

### Testing
- Write unit tests for tool logic
- Use Vitest as test runner
- Follow Testing Library best practices