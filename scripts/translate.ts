#!/usr/bin/env node

/**
 * 批量翻译工具
 * 用于自动翻译 JSON 语言文件
 * 
 * 使用方法：
 * - 翻译单个文件：npm run translate -- --file=common.json --from=zh --to=en,ja
 * - 翻译所有工具：npm run translate -- --pattern="tools/*.json" --from=zh --to=en,ja
 * - 检查翻译完整性：npm run translate:check
 */

import fs from 'fs/promises';
import path from 'path';
import { glob } from 'glob';
import { Command } from 'commander';
import chalk from 'chalk';

// 技术术语词典
const TECHNICAL_TERMS: Record<string, Record<string, string>> = {
  en: {
    '编码': 'Encode',
    '解码': 'Decode',
    '格式化': 'Format',
    '压缩': 'Compress',
    '解压': 'Decompress',
    '加密': 'Encrypt',
    '解密': 'Decrypt',
    '转换': 'Convert',
    '生成': 'Generate',
    '验证': 'Validate',
    '分析': 'Analyze',
    '优化': 'Optimize',
    '导入': 'Import',
    '导出': 'Export',
    '复制': 'Copy',
    '粘贴': 'Paste',
    '清空': 'Clear',
    '重置': 'Reset',
    '提交': 'Submit',
    '取消': 'Cancel',
    '保存': 'Save',
    '删除': 'Delete',
    '上传': 'Upload',
    '下载': 'Download',
    '预览': 'Preview',
    '设置': 'Settings',
    '帮助': 'Help',
    '关于': 'About',
    '工具': 'Tool',
    '输入': 'Input',
    '输出': 'Output',
    '结果': 'Result',
    '选项': 'Options',
    '高级': 'Advanced',
    '基础': 'Basic',
    '自定义': 'Custom',
    '默认': 'Default',
    '示例': 'Example',
    '教程': 'Tutorial',
    '文档': 'Documentation',
    '反馈': 'Feedback',
    '分享': 'Share',
    '收藏': 'Favorite',
    '历史': 'History',
    '最近': 'Recent',
    '推荐': 'Recommended',
    '热门': 'Popular',
    '新增': 'New',
    '更新': 'Update',
    '版本': 'Version',
    '语言': 'Language',
    '主题': 'Theme',
    '深色': 'Dark',
    '浅色': 'Light',
    '自动': 'Auto',
    '系统': 'System',
    '用户': 'User',
    '管理': 'Manage',
    '配置': 'Configure',
    '状态': 'Status',
    '成功': 'Success',
    '失败': 'Failed',
    '错误': 'Error',
    '警告': 'Warning',
    '信息': 'Info',
    '提示': 'Tip',
    '注意': 'Note',
    '重要': 'Important',
    '必填': 'Required',
    '可选': 'Optional',
    '搜索': 'Search',
    '筛选': 'Filter',
    '排序': 'Sort',
    '分页': 'Pagination',
    '全部': 'All',
    '无': 'None',
    '是': 'Yes',
    '否': 'No',
    '确定': 'OK',
    '返回': 'Back',
    '下一步': 'Next',
    '上一步': 'Previous',
    '完成': 'Done',
    '开始': 'Start',
    '停止': 'Stop',
    '暂停': 'Pause',
    '继续': 'Continue',
    '重试': 'Retry',
    '刷新': 'Refresh',
    '加载中': 'Loading',
    '处理中': 'Processing',
    '请稍候': 'Please wait',
    '即将推出': 'Coming soon',
    '敬请期待': 'Stay tuned',
    '了解更多': 'Learn more',
    '查看详情': 'View details',
    '立即使用': 'Use now',
    '免费试用': 'Try for free',
    '联系我们': 'Contact us',
    '服务条款': 'Terms of Service',
    '隐私政策': 'Privacy Policy',
    '版权所有': 'All rights reserved'
  },
  ja: {
    '编码': 'エンコード',
    '解码': 'デコード',
    '格式化': 'フォーマット',
    '压缩': '圧縮',
    '解压': '解凍',
    '加密': '暗号化',
    '解密': '復号化',
    '转换': '変換',
    '生成': '生成',
    '验证': '検証',
    '分析': '分析',
    '优化': '最適化',
    '导入': 'インポート',
    '导出': 'エクスポート',
    '复制': 'コピー',
    '粘贴': '貼り付け',
    '清空': 'クリア',
    '重置': 'リセット',
    '提交': '送信',
    '取消': 'キャンセル',
    '保存': '保存',
    '删除': '削除',
    '上传': 'アップロード',
    '下载': 'ダウンロード',
    '预览': 'プレビュー',
    '设置': '設定',
    '帮助': 'ヘルプ',
    '关于': 'について',
    '工具': 'ツール',
    '输入': '入力',
    '输出': '出力',
    '结果': '結果',
    '选项': 'オプション',
    '高级': '詳細',
    '基础': '基本',
    '自定义': 'カスタム',
    '默认': 'デフォルト',
    '示例': '例',
    '教程': 'チュートリアル',
    '文档': 'ドキュメント',
    '反馈': 'フィードバック',
    '分享': 'シェア',
    '收藏': 'お気に入り',
    '历史': '履歴',
    '最近': '最近',
    '推荐': 'おすすめ',
    '热门': '人気',
    '新增': '新規',
    '更新': '更新',
    '版本': 'バージョン',
    '语言': '言語',
    '主题': 'テーマ',
    '深色': 'ダーク',
    '浅色': 'ライト',
    '自动': '自動',
    '系统': 'システム',
    '用户': 'ユーザー',
    '管理': '管理',
    '配置': '設定',
    '状态': 'ステータス',
    '成功': '成功',
    '失败': '失敗',
    '错误': 'エラー',
    '警告': '警告',
    '信息': '情報',
    '提示': 'ヒント',
    '注意': '注意',
    '重要': '重要',
    '必填': '必須',
    '可选': '任意',
    '搜索': '検索',
    '筛选': 'フィルター',
    '排序': 'ソート',
    '分页': 'ページネーション',
    '全部': 'すべて',
    '无': 'なし',
    '是': 'はい',
    '否': 'いいえ',
    '确定': 'OK',
    '返回': '戻る',
    '下一步': '次へ',
    '上一步': '前へ',
    '完成': '完了',
    '开始': '開始',
    '停止': '停止',
    '暂停': '一時停止',
    '继续': '続行',
    '重试': '再試行',
    '刷新': '更新',
    '加载中': '読み込み中',
    '处理中': '処理中',
    '请稍候': 'お待ちください',
    '即将推出': '近日公開',
    '敬请期待': 'お楽しみに',
    '了解更多': '詳細を見る',
    '查看详情': '詳細を表示',
    '立即使用': '今すぐ使う',
    '免费试用': '無料で試す',
    '联系我们': 'お問い合わせ',
    '服务条款': '利用規約',
    '隐私政策': 'プライバシーポリシー',
    '版权所有': '著作権所有'
  }
};

// 保留不翻译的键名模式
const PRESERVE_PATTERNS = [
  /^[A-Z0-9_]+$/, // 全大写常量
  /^(id|key|code|type|name|value|data|meta|config|options)$/, // 技术键名
  /\.(png|jpg|jpeg|gif|svg|ico|webp)$/i, // 图片文件
  /\.(js|ts|jsx|tsx|css|scss|json|md)$/i, // 代码文件
  /^https?:\/\//i, // URL
  /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // Email
  /^\d+$/, // 纯数字
  /^#[0-9A-Fa-f]{3,8}$/, // 颜色值
  /^(Base64|MD5|SHA|AES|RSA|JWT|JSON|XML|HTML|CSS|JS|API|URL|UUID|QR)$/i // 技术缩写
];

// 翻译质量检查规则
const QUALITY_CHECKS = {
  // 长度异常检查（翻译后长度不应该相差太大）
  lengthRatio: (original: string, translated: string, targetLang: string) => {
    const ratio = translated.length / original.length;
    // 日文通常更短，英文可能更长
    const expectedRatio = targetLang === 'ja' ? [0.5, 1.5] : [0.8, 2.0];
    return ratio >= expectedRatio[0] && ratio <= expectedRatio[1];
  },
  
  // 检查是否包含未翻译的中文
  hasUntranslatedChinese: (translated: string) => {
    return !/[\u4e00-\u9fa5]/.test(translated);
  },
  
  // 检查占位符是否保留
  placeholdersPreserved: (original: string, translated: string) => {
    const originalPlaceholders: string[] = original.match(/\{[^}]+\}/g) || [];
    const translatedPlaceholders: string[] = translated.match(/\{[^}]+\}/g) || [];
    if (originalPlaceholders.length !== translatedPlaceholders.length) {
      return false;
    }
    for (const placeholder of originalPlaceholders) {
      if (!translatedPlaceholders.includes(placeholder)) {
        return false;
      }
    }
    return true;
  },
  
  // 检查HTML标签是否保留
  htmlTagsPreserved: (original: string, translated: string) => {
    const originalTags = original.match(/<[^>]+>/g) || [];
    const translatedTags = translated.match(/<[^>]+>/g) || [];
    return originalTags.length === translatedTags.length;
  }
};

// 延迟函数（用于限流）
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟翻译函数（实际项目中应该调用 Google Translate API）
async function translateText(text: string, from: string, to: string): Promise<string> {
  // 检查是否需要保留原文
  if (PRESERVE_PATTERNS.some(pattern => pattern.test(text))) {
    return text;
  }
  
  // 检查技术术语词典
  const termDict = TECHNICAL_TERMS[to];
  if (termDict && termDict[text]) {
    return termDict[text];
  }
  
  // 模拟 API 调用延迟
  await delay(100);
  
  // 这里应该调用实际的翻译 API
  // 现在只是返回一个模拟的翻译结果
  console.log(chalk.yellow(`[Mock] Translating "${text}" from ${from} to ${to}`));
  
  // 返回带标记的文本，表示需要实际翻译
  return `[${to.toUpperCase()}] ${text}`;
}

// 递归翻译 JSON 对象
async function translateObject(
  obj: any,
  from: string,
  to: string,
  path: string[] = [],
  issues: any[] = []
): Promise<any> {
  if (typeof obj === 'string') {
    const translated = await translateText(obj, from, to);
    
    // 质量检查
    if (!QUALITY_CHECKS.lengthRatio(obj, translated, to)) {
      issues.push({
        path: path.join('.'),
        type: 'length_ratio',
        original: obj,
        translated,
        message: '翻译长度异常'
      });
    }
    
    if (!QUALITY_CHECKS.hasUntranslatedChinese(translated)) {
      issues.push({
        path: path.join('.'),
        type: 'untranslated_chinese',
        original: obj,
        translated,
        message: '包含未翻译的中文'
      });
    }
    
    if (!QUALITY_CHECKS.placeholdersPreserved(obj, translated)) {
      issues.push({
        path: path.join('.'),
        type: 'placeholders',
        original: obj,
        translated,
        message: '占位符不匹配'
      });
    }
    
    if (!QUALITY_CHECKS.htmlTagsPreserved(obj, translated)) {
      issues.push({
        path: path.join('.'),
        type: 'html_tags',
        original: obj,
        translated,
        message: 'HTML标签不匹配'
      });
    }
    
    return translated;
  } else if (Array.isArray(obj)) {
    const result = [];
    for (let i = 0; i < obj.length; i++) {
      const item = await translateObject(obj[i], from, to, [...path, String(i)], issues);
      result.push(item);
    }
    return result;
  } else if (obj && typeof obj === 'object') {
    const result: any = {};
    for (const key of Object.keys(obj)) {
      result[key] = await translateObject(obj[key], from, to, [...path, key], issues);
    }
    return result;
  }
  
  return obj;
}

// 翻译单个文件
async function translateFile(
  sourceFile: string,
  targetLangs: string[],
  sourceLang: string = 'zh'
): Promise<void> {
  console.log(chalk.blue(`\n翻译文件: ${sourceFile}`));
  
  try {
    // 读取源文件
    const sourceContent = await fs.readFile(sourceFile, 'utf-8');
    const sourceData = JSON.parse(sourceContent);
    
    // 翻译到每种目标语言
    for (const targetLang of targetLangs) {
      console.log(chalk.cyan(`  翻译到 ${targetLang}...`));
      
      const issues: any[] = [];
      const translatedData = await translateObject(sourceData, sourceLang, targetLang, [], issues);
      
      // 构建目标文件路径
      const targetFile = sourceFile.replace(`/${sourceLang}/`, `/${targetLang}/`);
      const targetDir = path.dirname(targetFile);
      
      // 确保目标目录存在
      await fs.mkdir(targetDir, { recursive: true });
      
      // 写入翻译后的文件
      await fs.writeFile(
        targetFile,
        JSON.stringify(translatedData, null, 2) + '\n',
        'utf-8'
      );
      
      console.log(chalk.green(`  ✓ 已保存到: ${targetFile}`));
      
      // 报告质量问题
      if (issues.length > 0) {
        console.log(chalk.yellow(`  ⚠ 发现 ${issues.length} 个质量问题:`));
        issues.forEach(issue => {
          console.log(chalk.yellow(`    - ${issue.path}: ${issue.message}`));
        });
      }
    }
  } catch (error) {
    console.error(chalk.red(`  ✗ 翻译失败: ${error instanceof Error ? error.message : String(error)}`));
  }
}

// 检查翻译完整性
async function checkTranslations(): Promise<void> {
  console.log(chalk.blue('\n检查翻译完整性...'));
  
  const localesDir = path.join(process.cwd(), 'public/locales');
  const languages = ['zh', 'en', 'ja'];
  const issues: any[] = [];
  
  // 获取所有中文文件
  const zhFiles = await glob('**/*.json', {
    cwd: path.join(localesDir, 'zh')
  });
  
  for (const file of zhFiles) {
    const zhFile = path.join(localesDir, 'zh', file);
    const zhContent = await fs.readFile(zhFile, 'utf-8');
    const zhData = JSON.parse(zhContent);
    const zhKeys = getAllKeys(zhData);
    
    // 检查其他语言
    for (const lang of ['en', 'ja']) {
      const langFile = path.join(localesDir, lang, file);
      
      try {
        const langContent = await fs.readFile(langFile, 'utf-8');
        const langData = JSON.parse(langContent);
        const langKeys = getAllKeys(langData);
        
        // 找出缺失的键
        const missingKeys = zhKeys.filter(key => !langKeys.includes(key));
        if (missingKeys.length > 0) {
          issues.push({
            file: `${lang}/${file}`,
            type: 'missing_keys',
            keys: missingKeys
          });
        }
        
        // 找出多余的键
        const extraKeys = langKeys.filter(key => !zhKeys.includes(key));
        if (extraKeys.length > 0) {
          issues.push({
            file: `${lang}/${file}`,
            type: 'extra_keys',
            keys: extraKeys
          });
        }
      } catch (error) {
        issues.push({
          file: `${lang}/${file}`,
          type: 'missing_file',
          message: '文件不存在'
        });
      }
    }
  }
  
  // 报告结果
  if (issues.length === 0) {
    console.log(chalk.green('✓ 所有翻译文件完整性检查通过！'));
  } else {
    console.log(chalk.red(`✗ 发现 ${issues.length} 个问题:`));
    issues.forEach(issue => {
      if (issue.type === 'missing_file') {
        console.log(chalk.red(`  - ${issue.file}: ${issue.message}`));
      } else if (issue.type === 'missing_keys') {
        console.log(chalk.yellow(`  - ${issue.file}: 缺失 ${issue.keys.length} 个键`));
        issue.keys.forEach((key: string) => console.log(chalk.gray(`    - ${key}`)));
      } else if (issue.type === 'extra_keys') {
        console.log(chalk.yellow(`  - ${issue.file}: 多余 ${issue.keys.length} 个键`));
        issue.keys.forEach((key: string) => console.log(chalk.gray(`    - ${key}`)));
      }
    });
  }
}

// 获取对象的所有键路径
function getAllKeys(obj: any, prefix: string = ''): string[] {
  const keys: string[] = [];
  
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      keys.push(...getAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

// 主程序
const program = new Command();

program
  .name('translate')
  .description('批量翻译工具')
  .version('1.0.0');

program
  .command('file')
  .description('翻译单个文件')
  .option('-f, --file <file>', '要翻译的文件（相对于 public/locales/zh/）')
  .option('-t, --to <langs>', '目标语言，逗号分隔', 'en,ja')
  .option('-s, --from <lang>', '源语言', 'zh')
  .action(async (options) => {
    const sourceFile = path.join(process.cwd(), 'public/locales', options.from, options.file);
    const targetLangs = options.to.split(',');
    await translateFile(sourceFile, targetLangs, options.from);
  });

program
  .command('batch')
  .description('批量翻译文件')
  .option('-p, --pattern <pattern>', '文件匹配模式', '**/*.json')
  .option('-t, --to <langs>', '目标语言，逗号分隔', 'en,ja')
  .option('-s, --from <lang>', '源语言', 'zh')
  .action(async (options) => {
    const sourceDir = path.join(process.cwd(), 'public/locales', options.from);
    const files = await glob(options.pattern, { cwd: sourceDir });
    const targetLangs = options.to.split(',');
    
    console.log(chalk.blue(`找到 ${files.length} 个文件待翻译`));
    
    for (const file of files) {
      const sourceFile = path.join(sourceDir, file);
      await translateFile(sourceFile, targetLangs, options.from);
    }
  });

program
  .command('check')
  .description('检查翻译完整性')
  .action(async () => {
    await checkTranslations();
  });

program.parse(process.argv);
