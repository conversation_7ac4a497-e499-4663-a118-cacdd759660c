#!/bin/bash

# PoorLow Tools 自动部署脚本
# 用于服务器端自动拉取代码并部署

# 配置变量
PROJECT_DIR="/www/wwwroot/poorlow.com"
LOG_FILE="/www/wwwlogs/poorlow-deploy.log"
BACKUP_DIR="/www/backup/poorlow-$(date +%Y%m%d_%H%M%S)"
NODE_VERSION="18"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

log_success() {
    echo -e "${GREEN}$(date '+%Y-%m-%d %H:%M:%S') - ✅ $1${NC}" | tee -a $LOG_FILE
}

log_error() {
    echo -e "${RED}$(date '+%Y-%m-%d %H:%M:%S') - ❌ $1${NC}" | tee -a $LOG_FILE
}

log_warning() {
    echo -e "${YELLOW}$(date '+%Y-%m-%d %H:%M:%S') - ⚠️  $1${NC}" | tee -a $LOG_FILE
}

# 错误处理函数
handle_error() {
    log_error "部署失败: $1"
    if [ -d "$BACKUP_DIR" ]; then
        log_warning "正在恢复备份..."
        rm -rf $PROJECT_DIR
        mv $BACKUP_DIR $PROJECT_DIR
        log_success "已恢复到之前版本"
    fi
    exit 1
}

# 检查必要工具
check_requirements() {
    log "检查部署环境..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        handle_error "Node.js 未安装"
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        handle_error "npm 未安装"
    fi
    
    # 检查 PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 未安装，正在安装..."
        npm install -g pm2 || handle_error "PM2 安装失败"
    fi
    
    # 检查 Git
    if ! command -v git &> /dev/null; then
        handle_error "Git 未安装"
    fi
    
    log_success "环境检查通过"
}

# 创建备份
create_backup() {
    log "创建项目备份..."
    
    if [ -d "$PROJECT_DIR" ]; then
        mkdir -p /www/backup
        cp -r $PROJECT_DIR $BACKUP_DIR
        log_success "备份创建完成: $BACKUP_DIR"
    else
        log_warning "项目目录不存在，跳过备份"
    fi
}

# 拉取代码
pull_code() {
    log "拉取最新代码..."
    
    cd $PROJECT_DIR || handle_error "无法进入项目目录"
    
    # 检查 Git 状态
    git status >> $LOG_FILE 2>&1
    
    # 拉取最新代码
    git pull origin main >> $LOG_FILE 2>&1 || handle_error "代码拉取失败"
    
    log_success "代码拉取完成"
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    
    cd $PROJECT_DIR || handle_error "无法进入项目目录"
    
    # 清理 node_modules（可选）
    # rm -rf node_modules package-lock.json
    
    # 安装依赖
    npm ci >> $LOG_FILE 2>&1 || handle_error "依赖安装失败"
    
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log "构建生产版本..."
    
    cd $PROJECT_DIR || handle_error "无法进入项目目录"
    
    # 构建项目
    npm run build >> $LOG_FILE 2>&1 || handle_error "项目构建失败"
    
    log_success "项目构建完成"
}

# 重启应用
restart_app() {
    log "重启应用服务..."
    
    cd $PROJECT_DIR || handle_error "无法进入项目目录"
    
    # 检查 PM2 进程是否存在
    if pm2 list | grep -q "poorlow-tools"; then
        # 重启现有进程
        pm2 restart poorlow-tools >> $LOG_FILE 2>&1 || handle_error "应用重启失败"
        log_success "应用重启完成"
    else
        # 启动新进程
        pm2 start npm --name "poorlow-tools" -- start >> $LOG_FILE 2>&1 || handle_error "应用启动失败"
        pm2 save >> $LOG_FILE 2>&1
        log_success "应用启动完成"
    fi
    
    # 显示应用状态
    pm2 status poorlow-tools >> $LOG_FILE 2>&1
}

# 清理旧备份
cleanup_backups() {
    log "清理旧备份文件..."
    
    # 保留最近5个备份
    find /www/backup -name "poorlow-*" -type d | sort -r | tail -n +6 | xargs rm -rf
    
    log_success "备份清理完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    # 等待应用启动
    sleep 10
    
    # 检查端口是否监听
    if netstat -tlnp | grep -q ":3000"; then
        log_success "应用端口检查通过"
    else
        handle_error "应用端口检查失败"
    fi
    
    # 检查 HTTP 响应（可选）
    # if curl -f http://localhost:3000 > /dev/null 2>&1; then
    #     log_success "HTTP 健康检查通过"
    # else
    #     handle_error "HTTP 健康检查失败"
    # fi
}

# 发送通知（可选）
send_notification() {
    log "发送部署通知..."
    
    # 这里可以添加钉钉、企业微信等通知
    # curl -X POST "https://your-webhook-url" \
    #      -H "Content-Type: application/json" \
    #      -d '{"text":"PoorLow Tools 部署成功！"}'
    
    log_success "部署通知已发送"
}

# 主函数
main() {
    log "========================================="
    log "开始 PoorLow Tools 自动部署"
    log "========================================="
    
    # 执行部署步骤
    check_requirements
    create_backup
    pull_code
    install_dependencies
    build_project
    restart_app
    cleanup_backups
    health_check
    # send_notification
    
    log_success "========================================="
    log_success "🎉 部署完成！网站已更新到最新版本"
    log_success "========================================="
}

# 执行主函数
main "$@"
