#!/bin/bash

# PoorLow Tools 自动化部署 Webhook 设置脚本
# 用于设置 Git Webhook 自动部署

# 配置变量
WEBHOOK_PORT="9000"
WEBHOOK_SECRET="poorlow-deploy-$(openssl rand -hex 16)"
PROJECT_DIR="/Users/<USER>/poorlow-tools"
WEBHOOK_DIR="/opt/webhook"
SERVICE_NAME="poorlow-webhook"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 错误处理
handle_error() {
    log_error "$1"
    exit 1
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        handle_error "请使用 root 用户运行此脚本"
    fi
}

# 安装 webhook
install_webhook() {
    log_info "安装 webhook 工具..."
    
    # 检查是否已安装
    if command -v webhook &> /dev/null; then
        log_warning "webhook 已安装，跳过安装步骤"
        return
    fi
    
    # 根据系统类型安装
    if command -v apt &> /dev/null; then
        # Ubuntu/Debian
        apt update
        apt install -y webhook
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        yum install -y epel-release
        yum install -y webhook
    else
        # 手动安装
        log_info "手动安装 webhook..."
        wget -O /usr/local/bin/webhook https://github.com/adnanh/webhook/releases/download/2.8.1/webhook-linux-amd64
        chmod +x /usr/local/bin/webhook
    fi
    
    log_success "webhook 安装完成"
}

# 创建 webhook 配置
create_webhook_config() {
    log_info "创建 webhook 配置..."
    
    mkdir -p $WEBHOOK_DIR
    
    cat > $WEBHOOK_DIR/hooks.json << EOF
[
  {
    "id": "poorlow-deploy",
    "execute-command": "$PROJECT_DIR/scripts/deploy.sh",
    "command-working-directory": "$PROJECT_DIR",
    "response-message": "Deployment started",
    "trigger-rule": {
      "and": [
        {
          "match": {
            "type": "payload-hmac-sha256",
            "secret": "$WEBHOOK_SECRET",
            "parameter": {
              "source": "header",
              "name": "X-Hub-Signature-256"
            }
          }
        },
        {
          "match": {
            "type": "value",
            "value": "refs/heads/main",
            "parameter": {
              "source": "payload",
              "name": "ref"
            }
          }
        }
      ]
    }
  }
]
EOF
    
    log_success "webhook 配置创建完成"
}

# 创建 systemd 服务
create_systemd_service() {
    log_info "创建 systemd 服务..."
    
    cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=PoorLow Tools Webhook Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$WEBHOOK_DIR
ExecStart=/usr/local/bin/webhook -hooks hooks.json -verbose -port $WEBHOOK_PORT
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF
    
    # 重载 systemd 并启动服务
    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    systemctl start $SERVICE_NAME
    
    log_success "systemd 服务创建并启动完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 检查防火墙类型并开放端口
    if command -v ufw &> /dev/null; then
        # Ubuntu UFW
        ufw allow $WEBHOOK_PORT/tcp
        log_success "UFW 防火墙规则添加完成"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL firewalld
        firewall-cmd --permanent --add-port=$WEBHOOK_PORT/tcp
        firewall-cmd --reload
        log_success "firewalld 防火墙规则添加完成"
    else
        log_warning "未检测到防火墙，请手动开放端口 $WEBHOOK_PORT"
    fi
}

# 设置部署脚本权限
setup_deploy_script() {
    log_info "设置部署脚本权限..."
    
    chmod +x $PROJECT_DIR/scripts/deploy-static.sh
    
    # 确保日志目录存在
    mkdir -p /www/wwwlogs
    mkdir -p /www/backup
    
    log_success "部署脚本权限设置完成"
}

# 测试 webhook
test_webhook() {
    log_info "测试 webhook 服务..."
    
    # 检查服务状态
    if systemctl is-active --quiet $SERVICE_NAME; then
        log_success "webhook 服务运行正常"
    else
        handle_error "webhook 服务启动失败"
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep -q ":$WEBHOOK_PORT"; then
        log_success "webhook 端口监听正常"
    else
        handle_error "webhook 端口监听失败"
    fi
}

# 显示配置信息
show_config_info() {
    log_success "========================================="
    log_success "🎉 Webhook 自动部署配置完成！"
    log_success "========================================="
    echo
    log_info "配置信息："
    echo "  Webhook URL: http://*************:$WEBHOOK_PORT/hooks/poorlow-deploy"
    echo "  Secret: $WEBHOOK_SECRET"
    echo "  监听端口: $WEBHOOK_PORT"
    echo "  项目目录: $PROJECT_DIR"
    echo
    log_info "Git 仓库 Webhook 设置："
    echo "  1. 进入 GitHub/GitLab 仓库设置"
    echo "  2. 添加 Webhook："
    echo "     - URL: http://*************:$WEBHOOK_PORT/hooks/poorlow-deploy"
    echo "     - Secret: $WEBHOOK_SECRET"
    echo "     - Content-Type: application/json"
    echo "     - Events: Push events (main 分支)"
    echo
    log_info "测试部署："
    echo "  推送代码到 main 分支即可触发自动部署"
    echo
    log_warning "安全提醒："
    echo "  - 请保存好 Secret，用于 Git 仓库配置"
    echo "  - 建议配置 HTTPS 和域名访问"
    echo "  - 定期检查部署日志：/www/wwwlogs/poorlow-deploy.log"
    log_success "========================================="
}

# 主函数
main() {
    echo "🚀 开始配置 PoorLow Tools 自动化部署..."
    echo
    
    check_root
    install_webhook
    create_webhook_config
    create_systemd_service
    configure_firewall
    setup_deploy_script
    test_webhook
    show_config_info
}

# 执行主函数
main "$@"
