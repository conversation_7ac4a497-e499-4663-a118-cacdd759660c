#!/bin/bash

# PoorLow Tools 静态部署脚本 - 高性能版本
# 用于自动构建和部署静态文件

# 配置变量
PROJECT_DIR="/www/wwwroot/poorlow.com"
BUILD_DIR="$PROJECT_DIR/out"
NGINX_DIR="/etc/nginx/sites-available"
LOG_FILE="/www/wwwlogs/poorlow-deploy.log"
BACKUP_DIR="/www/backup/poorlow-$(date +%Y%m%d_%H%M%S)"
NODE_VERSION="18"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

log_success() {
    echo -e "${GREEN}$(date '+%Y-%m-%d %H:%M:%S') - ✅ $1${NC}" | tee -a $LOG_FILE
}

log_error() {
    echo -e "${RED}$(date '+%Y-%m-%d %H:%M:%S') - ❌ $1${NC}" | tee -a $LOG_FILE
}

log_warning() {
    echo -e "${YELLOW}$(date '+%Y-%m-%d %H:%M:%S') - ⚠️  $1${NC}" | tee -a $LOG_FILE
}

log_info() {
    echo -e "${BLUE}$(date '+%Y-%m-%d %H:%M:%S') - ℹ️  $1${NC}" | tee -a $LOG_FILE
}

# 错误处理函数
handle_error() {
    log_error "部署失败: $1"
    if [ -d "$BACKUP_DIR" ]; then
        log_warning "正在恢复备份..."
        rm -rf $BUILD_DIR
        if [ -d "$BACKUP_DIR/out" ]; then
            cp -r $BACKUP_DIR/out $BUILD_DIR
            log_success "已恢复到之前版本"
        fi
    fi
    exit 1
}

# 检查必要工具
check_requirements() {
    log "检查部署环境..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        handle_error "Node.js 未安装"
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        handle_error "Node.js 版本过低，需要 18.x 或更高版本"
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        handle_error "npm 未安装"
    fi
    
    # 检查 Nginx
    if ! command -v nginx &> /dev/null; then
        handle_error "Nginx 未安装"
    fi
    
    # 检查 Git
    if ! command -v git &> /dev/null; then
        handle_error "Git 未安装"
    fi
    
    log_success "环境检查通过 - Node.js $(node -v), npm $(npm -v), Nginx $(nginx -v 2>&1 | head -1 | cut -d' ' -f3)"
}

# 创建备份
create_backup() {
    log "创建项目备份..."
    
    if [ -d "$BUILD_DIR" ]; then
        mkdir -p /www/backup
        cp -r $PROJECT_DIR $BACKUP_DIR
        log_success "备份创建完成: $BACKUP_DIR"
    else
        log_warning "构建目录不存在，跳过备份"
    fi
}

# 拉取代码
pull_code() {
    log "拉取最新代码..."
    
    cd $PROJECT_DIR || handle_error "无法进入项目目录"
    
    # 检查 Git 状态
    git status >> $LOG_FILE 2>&1
    
    # 拉取最新代码
    git pull origin main >> $LOG_FILE 2>&1 || handle_error "代码拉取失败"
    
    log_success "代码拉取完成"
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    
    cd $PROJECT_DIR || handle_error "无法进入项目目录"
    
    # 使用 npm ci 进行快速安装
    npm ci --production=false >> $LOG_FILE 2>&1 || handle_error "依赖安装失败"
    
    log_success "依赖安装完成"
}

# 构建静态文件
build_static() {
    log "构建静态文件..."
    
    cd $PROJECT_DIR || handle_error "无法进入项目目录"
    
    # 清理之前的构建
    rm -rf out .next
    
    # 构建项目
    npm run build >> $LOG_FILE 2>&1 || handle_error "静态构建失败"
    
    # 检查构建结果
    if [ ! -d "out" ]; then
        handle_error "构建目录 'out' 不存在"
    fi
    
    # 统计构建文件
    local file_count=$(find out -type f | wc -l)
    local total_size=$(du -sh out | cut -f1)
    
    log_success "静态构建完成 - 文件数: $file_count, 总大小: $total_size"
}

# 配置 Nginx
configure_nginx() {
    log "配置 Nginx..."
    
    # 创建 Nginx 配置文件
    cat > $NGINX_DIR/poorlow.com << 'EOF'
server {
    listen 80;
    server_name poorlow.com www.poorlow.com;
    root /www/wwwroot/poorlow.com/out;
    index index.html;

    # 启用 Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # HTML 文件缓存
    location ~* \.html$ {
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
    }

    # 处理 Next.js 路由
    location / {
        try_files $uri $uri.html $uri/ =404;
    }

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
}
EOF

    # 启用站点
    ln -sf $NGINX_DIR/poorlow.com /etc/nginx/sites-enabled/
    
    # 测试 Nginx 配置
    nginx -t >> $LOG_FILE 2>&1 || handle_error "Nginx 配置测试失败"
    
    # 重载 Nginx
    systemctl reload nginx >> $LOG_FILE 2>&1 || handle_error "Nginx 重载失败"
    
    log_success "Nginx 配置完成"
}

# 清理旧备份
cleanup_backups() {
    log "清理旧备份文件..."
    
    # 保留最近5个备份
    find /www/backup -name "poorlow-*" -type d | sort -r | tail -n +6 | xargs rm -rf
    
    log_success "备份清理完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    # 检查 Nginx 状态
    if ! systemctl is-active --quiet nginx; then
        handle_error "Nginx 服务未运行"
    fi
    
    # 检查端口监听
    if ! netstat -tlnp | grep -q ":80"; then
        handle_error "端口 80 未监听"
    fi
    
    # 检查文件权限
    if [ ! -r "$BUILD_DIR/index.html" ]; then
        handle_error "静态文件权限错误"
    fi
    
    log_success "健康检查通过"
}

# 性能优化
optimize_performance() {
    log "执行性能优化..."
    
    cd $BUILD_DIR || handle_error "无法进入构建目录"
    
    # 设置正确的文件权限
    find . -type f -exec chmod 644 {} \;
    find . -type d -exec chmod 755 {} \;
    
    # 预压缩静态文件（如果有 gzip 命令）
    if command -v gzip &> /dev/null; then
        find . -name "*.js" -o -name "*.css" -o -name "*.html" | while read file; do
            gzip -c "$file" > "$file.gz"
        done
        log_info "静态文件预压缩完成"
    fi
    
    log_success "性能优化完成"
}

# 主函数
main() {
    log "========================================="
    log "开始 PoorLow Tools 静态部署（高性能版）"
    log "========================================="
    
    # 执行部署步骤
    check_requirements
    create_backup
    pull_code
    install_dependencies
    build_static
    configure_nginx
    optimize_performance
    cleanup_backups
    health_check
    
    log_success "========================================="
    log_success "🎉 静态部署完成！网站已更新到最新版本"
    log_success "🚀 性能优化：纯静态文件 + Nginx 直接服务"
    log_success "📊 访问地址：http://poorlow.com"
    log_success "========================================="
}

# 执行主函数
main "$@"
