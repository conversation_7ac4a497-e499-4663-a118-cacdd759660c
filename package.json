{"name": "poorlow-tools", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "commit": "cz", "translate": "tsx scripts/translate.ts", "translate:file": "tsx scripts/translate.ts file", "translate:batch": "tsx scripts/translate.ts batch", "translate:check": "tsx scripts/translate.ts check"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@types/crypto-js": "^4.2.2", "@types/exif-js": "^2.3.1", "@types/lodash": "^4.17.20", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "exif-js": "^2.3.0", "framer-motion": "^12.23.6", "fuse.js": "^7.1.0", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "next": "14.2.3", "next-intl": "^4.3.4", "pinyin-pro": "^3.26.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20.19.8", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.19", "chalk": "^5.4.1", "commander": "^14.0.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^10.1.5", "glob": "^11.0.3", "postcss": "^8.4.38", "prettier": "^3.6.2", "tailwindcss": "^3.4.3", "tsx": "^4.20.3", "typescript": "^5.4.5", "vitest": "^3.2.4"}}