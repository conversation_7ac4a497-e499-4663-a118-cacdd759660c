# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# Testing
/coverage
/.nyc_output

# Next.js
/.next/
/out/

# Production
/build
/dist

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env*.local
.env

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Test
/test-results/
/playwright-report/
/playwright/.cache/

# Temporary files
*.log
*.tmp
*.temp
.cache

# Build artifacts
/storybook-static
/analyze

# Private keys and certificates
*.key
*.pem
*.p12
*.pfx

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.backup
*.bak

# Lock files (根据团队约定决定是否忽略)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
.specstory
.history
