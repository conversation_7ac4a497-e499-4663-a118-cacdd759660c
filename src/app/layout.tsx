import type { Viewport } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { locales } from '@/i18n/config'

const inter = Inter({ subsets: ['latin'] })

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0F172A' }
  ],
}

interface RootLayoutProps {
  children: React.ReactNode
  params: {
    locale?: string
  }
}

export default function RootLayout({
  children,
  params,
}: RootLayoutProps) {
  // 获取当前语言，如果没有则使用默认语言
  const locale = params.locale || 'zh'
  const lang = locale === 'zh' ? 'zh-CN' : locale === 'ja' ? 'ja' : 'en'

  return (
    <html lang={lang} suppressHydrationWarning>
      <body className={inter.className}>
        <div className="min-h-screen flex flex-col">
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  )
}
