import { notFound } from 'next/navigation'
import { getToolById } from '@/lib/tools/registry'
import ClientPage from './client-page'
import { type Locale, locales } from '@/i18n/config'

interface ToolPageProps {
  params: {
    id: string
    locale: Locale
  }
}

export default function ToolPage({ params }: ToolPageProps) {
  const tool = getToolById(params.id)
  
  if (!tool) {
    notFound()
  }
  
  // 传递工具ID和语言
  return <ClientPage toolId={tool.id} locale={params.locale} />
}

// 生成静态路径
export async function generateStaticParams() {
  const { tools } = await import('@/lib/tools/registry')
  
  // 为每种语言生成路径
  return locales.flatMap(locale =>
    tools.map((tool) => ({
      locale,
      id: tool.id,
    }))
  )
}

// 生成元数据
export async function generateMetadata({ params }: ToolPageProps) {
  const tool = getToolById(params.id)
  const { locale } = params
  
  if (!tool) {
    const notFoundTitles: Record<Locale, string> = {
      zh: '工具未找到',
      en: 'Tool Not Found',
      ja: 'ツールが見つかりません',
    }
    return {
      title: notFoundTitles[locale],
    }
  }
  
  const siteName: Record<Locale, string> = {
    zh: 'PoorLow Tools',
    en: 'PoorLow Tools',
    ja: 'PoorLow Tools',
  }
  
  return {
    title: `${tool.name[locale]} - ${siteName[locale]}`,
    description: tool.description[locale],
    keywords: tool.keywords[locale],
    alternates: {
      languages: {
        'zh-CN': `/zh/tools/${params.id}`,
        'en': `/en/tools/${params.id}`,
        'ja': `/ja/tools/${params.id}`,
      },
    },
  }
}
