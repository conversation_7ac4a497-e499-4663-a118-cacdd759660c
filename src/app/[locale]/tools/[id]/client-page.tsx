'use client'

import { ToolWrapper } from '@/components/tools/ToolWrapper'
import { getToolById } from '@/lib/tools/registry'
import { type Locale } from '@/i18n/config'

interface ClientPageProps {
  toolId: string
  locale: Locale
}

export default function ClientPage({ toolId, locale }: ClientPageProps) {
  const tool = getToolById(toolId)
  
  if (!tool) {
    const notFoundMessages: Record<Locale, string> = {
      zh: '工具未找到',
      en: 'Tool not found',
      ja: 'ツールが見つかりません',
    }
    
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-xl text-gray-600">{notFoundMessages[locale]}</p>
        </div>
      </div>
    )
  }
  
  const ToolComponent = tool.component
  
  return (
    <ToolWrapper toolId={toolId} locale={locale}>
      <div className="min-h-screen bg-gray-50">
        <ToolComponent />
      </div>
    </ToolWrapper>
  )
}
