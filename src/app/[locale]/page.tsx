'use client'

import { useMemo, useEffect } from 'react'
import { tools } from '@/lib/tools/registry'
import { HeroSection } from '@/components/home/<USER>'
import { QuickAccessSection } from '@/components/home/<USER>'
import { CategorySection } from '@/components/home/<USER>'
import { CategoryShowcaseSection } from '@/components/home/<USER>'
import { ToolGridSection } from '@/components/home/<USER>'
import { RecommendationSection } from '@/components/home/<USER>'
import { useHomeStore } from '@/stores/homeStore'
import { type Locale } from '@/i18n/config'

interface HomePageProps {
  params: {
    locale: Locale
  }
}

export default function Home({ params }: HomePageProps) {
  const locale = params.locale || 'zh'
  const {
    searchQuery,
    selectedCategories,
    recentTools,
    favoriteTools,
    setSearchQuery,
    toggleCategory,
    addSearchHistory,
    resetFilters,
  } = useHomeStore()

  // 首页加载时重置过滤条件，确保显示所有工具
  useEffect(() => {
    resetFilters()
  }, [resetFilters])

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    if (query.trim()) {
      addSearchHistory(query.trim())
    }
  }

  // 获取最近使用的工具对象
  const recentToolObjects = useMemo(() => {
    return recentTools
      .map(id => tools.find(t => t.id === id))
      .filter(Boolean) as typeof tools
  }, [recentTools])

  // 获取收藏的工具对象
  const favoriteToolObjects = useMemo(() => {
    return favoriteTools
      .map(id => tools.find(t => t.id === id))
      .filter(Boolean) as typeof tools
  }, [favoriteTools])

  // 获取热门工具（暂时使用前10个工具）
  const popularTools = useMemo(() => {
    return tools.slice(0, 10)
  }, [])

  // 处理分类点击
  const handleCategoryClick = (category: string) => {
    toggleCategory(category)
    // 滚动到工具列表区
    setTimeout(() => {
      document.getElementById('tools-grid')?.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start' 
      })
    }, 100)
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Hero 区域 - 搜索和快速访问 */}
      <HeroSection
        recentTools={recentToolObjects}
        favoriteTools={favoriteToolObjects}
        popularTools={popularTools}
      />

      {/* 个性化推荐区 */}
      <RecommendationSection />

      {/* 分类展示区 */}
      <CategoryShowcaseSection
        tools={tools}
        onCategoryClick={handleCategoryClick}
      />

      {/* 工具列表区 */}
      <div id="tools-grid">
        <ToolGridSection
          tools={tools}
          searchQuery={searchQuery}
          selectedCategories={selectedCategories}
        />
      </div>
    </div>
  )
}
