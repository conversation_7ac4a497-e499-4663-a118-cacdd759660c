'use client';

import { useSearchParams, useParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';
import { searchTools } from '@/lib/utils/search';
import { Tool } from '@/types/tool';
import { ToolCard } from '@/components/home/<USER>';
import { SearchSection } from '@/components/home/<USER>';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { type Locale } from '@/i18n/config';

interface SearchPageProps {
  params: {
    locale: Locale;
  };
}

// 多语言文本
const texts = {
  zh: {
    backToHome: '返回首页',
    searchTools: '搜索工具',
    continuousSearch: '继续搜索...',
    searching: '搜索中...',
    searchResults: (query: string) => `搜索 "${query}" 的结果`,
    foundTools: (count: number) => `（找到 ${count} 个工具）`,
    noResults: (query: string) => `没有找到与 "${query}" 相关的工具`,
    tryOtherKeywords: '试试其他关键词，或者浏览我们的工具分类',
    enterKeywords: '输入关键词开始搜索',
    searchSupport: '支持工具名称、功能描述、分类等多种搜索方式',
  },
  en: {
    backToHome: 'Back to Home',
    searchTools: 'Search Tools',
    continuousSearch: 'Continue searching...',
    searching: 'Searching...',
    searchResults: (query: string) => `Results for "${query}"`,
    foundTools: (count: number) => `(${count} tools found)`,
    noResults: (query: string) => `No tools found for "${query}"`,
    tryOtherKeywords: 'Try other keywords or browse our tool categories',
    enterKeywords: 'Enter keywords to start searching',
    searchSupport: 'Supports tool names, feature descriptions, categories, and more',
  },
  ja: {
    backToHome: 'ホームに戻る',
    searchTools: 'ツールを検索',
    continuousSearch: '検索を続ける...',
    searching: '検索中...',
    searchResults: (query: string) => `"${query}" の検索結果`,
    foundTools: (count: number) => `（${count}個のツールが見つかりました）`,
    noResults: (query: string) => `"${query}" に関連するツールが見つかりません`,
    tryOtherKeywords: '他のキーワードを試すか、ツールカテゴリを閲覧してください',
    enterKeywords: 'キーワードを入力して検索を開始',
    searchSupport: 'ツール名、機能説明、カテゴリなど多様な検索方法をサポート',
  },
};

function SearchContent({ params }: SearchPageProps) {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const [results, setResults] = useState<Tool[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const locale = params.locale || 'zh';
  const t = texts[locale];

  useEffect(() => {
    if (query) {
      setIsLoading(true);
      // 模拟异步搜索
      setTimeout(() => {
        const searchResults = searchTools(query);
        setResults(searchResults);
        setIsLoading(false);
      }, 100);
    } else {
      setResults([]);
      setIsLoading(false);
    }
  }, [query, locale]);

  return (
    <div className="min-h-screen bg-background">
      {/* 顶部导航 */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link 
            href={`/${locale}`} 
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            {t.backToHome}
          </Link>
        </div>
      </div>

      {/* 搜索区域 */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto mb-8">
          <h1 className="text-2xl font-bold mb-4 text-center">{t.searchTools}</h1>
          <SearchSection 
            placeholder={t.continuousSearch}
            autoFocus
          />
        </div>

        {/* 搜索结果 */}
        <div className="max-w-6xl mx-auto">
          {query && (
            <div className="mb-6">
              <h2 className="text-lg text-muted-foreground">
                {isLoading ? (
                  t.searching
                ) : (
                  <>
                    {t.searchResults(query)}
                    {results.length > 0 && (
                      <span className="ml-2">{t.foundTools(results.length)}</span>
                    )}
                  </>
                )}
              </h2>
            </div>
          )}

          {!isLoading && query && results.length === 0 && (
            <div className="text-center py-12">
              <p className="text-lg text-muted-foreground mb-4">
                {t.noResults(query)}
              </p>
              <p className="text-sm text-muted-foreground">
                {t.tryOtherKeywords}
              </p>
            </div>
          )}

          {!isLoading && results.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {results.map((tool) => (
                <ToolCard key={tool.id} tool={tool} />
              ))}
            </div>
          )}

          {!query && !isLoading && (
            <div className="text-center py-12">
              <p className="text-lg text-muted-foreground mb-4">
                {t.enterKeywords}
              </p>
              <p className="text-sm text-muted-foreground">
                {t.searchSupport}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function SearchPage({ params }: SearchPageProps) {
  return (
    <Suspense fallback={<div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-muted-foreground">加载中...</p>
      </div>
    </div>}>
      <SearchContent params={params} />
    </Suspense>
  );
}
