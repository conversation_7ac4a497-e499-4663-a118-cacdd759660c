import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { locales, type Locale } from '@/i18n/config'
import { LanguageProvider } from '@/contexts/LanguageContext'

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

interface LocaleLayoutProps {
  children: React.ReactNode
  params: {
    locale: Locale
  }
}

export async function generateMetadata({ params }: LocaleLayoutProps): Promise<Metadata> {
  const { locale } = params

  // 根据语言生成不同的元数据
  const metadata: Record<Locale, Metadata> = {
    zh: {
      title: 'PoorLow Tools - 在线工具集合',
      description: '100+实用在线工具，无需安装，即用即走',
      keywords: ['在线工具', '开发工具', '文本处理', '图像处理', '加密解密'],
    },
    en: {
      title: 'PoorLow Tools - Online Tools Collection',
      description: '100+ practical online tools, no installation required, use anytime',
      keywords: ['online tools', 'developer tools', 'text processing', 'image processing', 'encryption decryption'],
    },
    ja: {
      title: 'PoorLow Tools - オンラインツールコレクション',
      description: '100以上の実用的なオンラインツール、インストール不要、いつでも利用可能',
      keywords: ['オンラインツール', '開発ツール', 'テキスト処理', '画像処理', '暗号化復号化'],
    },
  }

  return {
    ...metadata[locale],
    authors: [{ name: 'PoorLow Tools' }],
    manifest: '/manifest.json',
    alternates: {
      languages: {
        'zh-CN': '/zh',
        'en': '/en',
        'ja': '/ja',
      },
    },
  }
}

export default function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  // 验证 locale 是否有效
  if (!locales.includes(params.locale)) {
    notFound()
  }

  return (
    <LanguageProvider locale={params.locale}>
      {children}
    </LanguageProvider>
  )
}
