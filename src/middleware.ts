import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { locales, defaultLocale, type Locale } from '@/i18n/config'

// 获取请求的首选语言
function getLocale(request: NextRequest): Locale {
  // 1. 检查 cookie 中的语言偏好
  const cookieLocale = request.cookies.get('preferred-language')?.value as Locale
  if (cookieLocale && locales.includes(cookieLocale)) {
    return cookieLocale
  }

  // 2. 检查 Accept-Language 头
  const acceptLanguage = request.headers.get('Accept-Language')
  if (acceptLanguage) {
    // 解析 Accept-Language 头
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [code, priority = '1'] = lang.trim().split(';q=')
        return { code: code.toLowerCase(), priority: parseFloat(priority) }
      })
      .sort((a, b) => b.priority - a.priority)

    // 查找匹配的语言
    for (const { code } of languages) {
      // 检查完全匹配
      if (locales.includes(code as Locale)) {
        return code as Locale
      }
      
      // 检查语言代码匹配（忽略地区）
      const langCode = code.split('-')[0]
      if (langCode === 'zh') return 'zh'
      if (langCode === 'en') return 'en'
      if (langCode === 'ja') return 'ja'
    }
  }

  // 3. 返回默认语言
  return defaultLocale
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // 检查路径是否已包含语言代码
  const pathnameHasLocale = locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )

  if (pathnameHasLocale) {
    // 如果路径已包含语言代码，更新 cookie
    const pathLocale = pathname.split('/')[1] as Locale
    const response = NextResponse.next()
    response.cookies.set('preferred-language', pathLocale, {
      maxAge: 60 * 60 * 24 * 365, // 1年
      sameSite: 'lax',
      path: '/',
    })
    return response
  }

  // 获取合适的语言
  const locale = getLocale(request)

  // 重定向到带语言的路径
  const response = NextResponse.redirect(
    new URL(`/${locale}${pathname}`, request.url)
  )

  // 设置语言 cookie
  response.cookies.set('preferred-language', locale, {
    maxAge: 60 * 60 * 24 * 365, // 1年
    sameSite: 'lax',
    path: '/',
  })

  return response
}

export const config = {
  matcher: [
    // 排除以下路径
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|manifest.json|icons|images|sounds).*)',
  ],
}
