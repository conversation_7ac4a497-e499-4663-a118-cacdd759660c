import { LucideIcon } from 'lucide-react'

export interface Tool {
  id: string
  name: {
    zh: string
    en: string
    ja: string
  }
  description: {
    zh: string
    en: string
    ja: string
  }
  category: ToolCategory
  icon: LucideIcon
  path: string
  keywords: {
    zh: string[]
    en: string[]
    ja: string[]
  }
  component: React.ComponentType
  isNew?: boolean
  isBeta?: boolean
}

export type ToolCategory = 
  | 'text' // 文本处理
  | 'image' // 图像处理
  | 'crypto' // 加密解密
  | 'converter' // 转换工具
  | 'developer' // 开发工具
  | 'calculator' // 计算器
  | 'generator' // 生成器
  | 'formatter' // 格式化
  | 'validator' // 验证器
  | 'productivity' // 生产力工具
  | 'lifestyle' // 生活助手
  | 'network' // 网络工具
  | 'other' // 其他

export interface ToolCategoryInfo {
  id: ToolCategory
  name: {
    zh: string
    en: string
    ja: string
  }
  description: {
    zh: string
    en: string
    ja: string
  }
  icon: LucideIcon
  color?: string
  count?: number
}
