import { useState, useEffect, useCallback, useMemo } from 'react'
import { Tool } from '@/types/tool'
import { getSearchService } from '@/lib/services/searchService'
import { useDebounce } from './useDebounce'

interface UseSearchOptions {
  tools: Tool[]
  debounceDelay?: number
  maxSuggestions?: number
}

export function useSearch({ tools, debounceDelay = 300, maxSuggestions = 5 }: UseSearchOptions) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<Tool[]>(tools)
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [isSearching, setIsSearching] = useState(false)
  
  const debouncedQuery = useDebounce(query, debounceDelay)
  
  // 获取搜索服务实例
  const searchService = useMemo(() => getSearchService(tools), [tools])
  
  // 执行搜索
  useEffect(() => {
    if (!debouncedQuery) {
      setResults(tools)
      setSuggestions([])
      setIsSearching(false)
      return
    }
    
    setIsSearching(true)
    
    // 执行搜索
    const searchResults = searchService.search(debouncedQuery)
    const searchSuggestions = searchService.getSuggestions(debouncedQuery, maxSuggestions)
    
    setResults(searchResults)
    setSuggestions(searchSuggestions)
    setIsSearching(false)
  }, [debouncedQuery, searchService, tools, maxSuggestions])
  
  // 搜索方法
  const search = useCallback((searchQuery: string) => {
    setQuery(searchQuery)
  }, [])
  
  // 清除搜索
  const clearSearch = useCallback(() => {
    setQuery('')
    setResults(tools)
    setSuggestions([])
  }, [tools])
  
  // 获取高亮结果
  const getHighlightedResults = useCallback(() => {
    if (!query) return []
    
    // 调用 searchService 的 search 方法来获取 Fuse 结果
    // 然后手动构建高亮结果
    return results.map(tool => ({
      tool,
      highlights: {} // 简化处理，暂不实现高亮
    }))
  }, [query, results])
  
  return {
    query,
    results,
    suggestions,
    isSearching,
    search,
    clearSearch,
    getHighlightedResults,
    totalResults: results.length
  }
}
