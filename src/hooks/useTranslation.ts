'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { useLocale } from '@/contexts/LanguageContext'
import { type Locale } from '@/i18n/config'
import { 
  loadTranslations, 
  getNestedTranslation, 
  replacePlaceholders 
} from '@/lib/i18n/loader'

interface UseTranslationOptions {
  namespace?: string
  preload?: string[]
}

interface UseTranslationReturn {
  t: (key: string, params?: Record<string, string | number>) => string
  ready: boolean
  locale: string
}

/**
 * Hook for loading and using translations
 * @param options - Options for translation loading
 * @returns Translation function and state
 */
export function useTranslation(options?: UseTranslationOptions): UseTranslationReturn {
  const locale = useLocale()
  const [translations, setTranslations] = useState<Record<string, any>>({})
  const [ready, setReady] = useState(false)

  const namespace = options?.namespace || 'common'
  const preloadNamespaces = options?.preload || []

  // 预加载命名空间函数，用useMemo包装避免每次渲染重新创建
  const preloadNamespacesCallback = useMemo(() => {
    return async (locale: Locale, namespaces: string[]) => {
      const additionalTranslations: Record<string, any> = {}
      if (namespaces.length > 0) {
        const results = await Promise.all(
          namespaces.map(ns => 
            loadTranslations(locale, ns).then(trans => ({ ns, trans }))
          )
        )
        
        results.forEach(({ ns, trans }) => {
          additionalTranslations[ns] = trans
        })
      }
      return additionalTranslations
    }
  }, [])

  // Load translations
  useEffect(() => {
    let cancelled = false

    const loadAllTranslations = async () => {
      setReady(false)

      try {
        // Load main namespace
        const mainTranslations = await loadTranslations(locale, namespace)
        
        // Load additional namespaces if specified
        const preloadNamespaces = options?.preload || []
        const additionalTranslations = await preloadNamespacesCallback(locale, preloadNamespaces)

        if (!cancelled) {
          setTranslations({
            [namespace]: mainTranslations,
            ...additionalTranslations
          })
          setReady(true)
        }
      } catch (error) {
        console.error('Failed to load translations:', error)
        if (!cancelled) {
          setTranslations({})
          setReady(true)
        }
      }
    }

    loadAllTranslations()

    return () => {
      cancelled = true
    }
  }, [locale, namespace, options, preloadNamespacesCallback])

  // Translation function
  const t = useCallback((key: string, params?: Record<string, string | number>): string => {
    // Handle namespace prefix in key (e.g., "common:loading")
    let targetNamespace = namespace
    let translationKey = key
    
    if (key.includes(':')) {
      const [ns, k] = key.split(':', 2)
      targetNamespace = ns
      translationKey = k
    }

    // Get translation from loaded translations
    const namespaceTranslations = translations[targetNamespace] || {}
    const translation = getNestedTranslation(namespaceTranslations, translationKey)

    if (translation) {
      return replacePlaceholders(translation, params)
    }

    // Development warning for missing translations
    if (process.env.NODE_ENV === 'development') {
      console.warn(`Missing translation: ${targetNamespace}:${translationKey}`)
    }

    // Return key as fallback
    return translationKey
  }, [translations, namespace])

  return useMemo(() => ({
    t,
    ready,
    locale
  }), [t, ready, locale])
}

/**
 * Hook for loading translations for a specific tool
 * @param toolId - The tool identifier
 * @returns Translation function and state
 */
export function useToolTranslation(toolId: string): UseTranslationReturn {
  return useTranslation({
    namespace: `tools/${toolId}`,
    preload: ['tools/_common', 'common']
  })
}

/**
 * Hook for loading multiple translation namespaces
 * @param namespaces - Array of namespace names
 * @returns Translation function and state
 */
export function useMultiTranslation(namespaces: string[]): UseTranslationReturn {
  const [mainNamespace, ...otherNamespaces] = namespaces
  
  return useTranslation({
    namespace: mainNamespace,
    preload: otherNamespaces
  })
}
