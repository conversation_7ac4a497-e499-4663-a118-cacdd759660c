import { useParams } from 'next/navigation'

type TranslationKey = string

const translations: Record<string, Record<string, string>> = {
  zh: {
    // 通用
    'common.delete': '删除',
    'common.save': '保存',
    'common.cancel': '取消',
    'common.confirm': '确认',
    'common.copy': '复制',
    'common.copied': '已复制',
    'common.clear': '清空',
    'common.reset': '重置',
    'common.download': '下载',
    'common.upload': '上传',
    'common.generate': '生成',
    'common.calculate': '计算',
    'common.convert': '转换',
    'common.search': '搜索',
    'common.add': '添加',
    'common.remove': '删除',
    'common.edit': '编辑',
    'common.view': '查看',
    'common.close': '关闭',
    'common.back': '返回',
    'common.next': '下一步',
    'common.previous': '上一步',
    'common.finish': '完成',
    'common.loading': '加载中...',
    'common.error': '错误',
    'common.success': '成功',
    'common.warning': '警告',
    'common.info': '信息',
    
    // 工具相关
    'tools.weather-forecast.title': '天气预报',
    'tools.weather-forecast.description': '查看全球各地的实时天气和未来几天的天气预报',
    
    // 天气工具
    'weather.search': '搜索',
    'weather.searchPlaceholder': '输入城市名称...',
    'weather.unit': '温度单位',
    'weather.celsius': '摄氏度',
    'weather.fahrenheit': '华氏度',
    'weather.current': '当前天气',
    'weather.forecast': '天气预报',
    'weather.saved': '保存的位置',
    'weather.saveLocation': '保存位置',
    'weather.currentLocation': '当前位置',
    'weather.feelsLike': '体感温度',
    'weather.wind': '风速',
    'weather.humidity': '湿度',
    'weather.pressure': '气压',
    'weather.visibility': '能见度',
    'weather.uvIndex': 'UV指数',
    'weather.uvLow': '低',
    'weather.uvModerate': '中等',
    'weather.uvHigh': '高',
    'weather.uvVeryHigh': '很高',
    'weather.uvExtreme': '极高',
    'weather.avgTemp': '平均温度',
    'weather.noSavedLocations': '暂无保存的位置',
    'weather.errors.enterLocation': '请输入城市名称',
    'weather.errors.fetchFailed': '获取天气数据失败',
    'weather.errors.locationFailed': '获取当前位置失败',
    'weather.errors.locationNotSupported': '您的浏览器不支持地理定位',
    
    // 电子书阅读器
    'tools.ebook-reader.title': '电子书阅读器',
    'tools.ebook-reader.description': '在线阅读电子书，支持多种格式、书签、笔记和阅读进度管理',
    'ebook.uploadPrompt': '拖拽文件到此处或点击选择',
    'ebook.supportedFormats': '支持 EPUB、PDF、TXT、MOBI 格式',
    'ebook.selectFile': '选择文件',
    'ebook.parsing': '正在解析电子书...',
    'ebook.reader': '阅读',
    'ebook.contents': '目录',
    'ebook.bookmarks': '书签',
    'ebook.search': '搜索',
    'ebook.fontSize': '字体大小',
    'ebook.lineHeight': '行高',
    'ebook.fontFamily': '字体',
    'ebook.serif': '衬线字体',
    'ebook.sansSerif': '无衬线字体',
    'ebook.monospace': '等宽字体',
    'ebook.theme': '主题',
    'ebook.lightTheme': '亮色',
    'ebook.darkTheme': '暗色',
    'ebook.sepiaTheme': '护眼',
    'ebook.addBookmark': '添加书签',
    'ebook.page': '页',
    'ebook.previous': '上一页',
    'ebook.next': '下一页',
    'ebook.noBookmarks': '暂无书签',
    'ebook.searchPlaceholder': '搜索内容...',
    'ebook.searchResults': '搜索结果',
    'ebook.readingStats': '阅读统计',
    'ebook.minutes': '分钟',
    'ebook.progress': '进度',
    'ebook.pagesRead': '已读页数',
    'ebook.errors.parseFailed': '解析电子书失败',
  },
  en: {
    // Common
    'common.delete': 'Delete',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.confirm': 'Confirm',
    'common.copy': 'Copy',
    'common.copied': 'Copied',
    'common.clear': 'Clear',
    'common.reset': 'Reset',
    'common.download': 'Download',
    'common.upload': 'Upload',
    'common.generate': 'Generate',
    'common.calculate': 'Calculate',
    'common.convert': 'Convert',
    'common.search': 'Search',
    'common.add': 'Add',
    'common.remove': 'Remove',
    'common.edit': 'Edit',
    'common.view': 'View',
    'common.close': 'Close',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.finish': 'Finish',
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.info': 'Info',
    
    // Tools
    'tools.weather-forecast.title': 'Weather Forecast',
    'tools.weather-forecast.description': 'Check real-time weather and forecasts for locations worldwide',
    
    // Weather tool
    'weather.search': 'Search',
    'weather.searchPlaceholder': 'Enter city name...',
    'weather.unit': 'Temperature Unit',
    'weather.celsius': 'Celsius',
    'weather.fahrenheit': 'Fahrenheit',
    'weather.current': 'Current Weather',
    'weather.forecast': 'Forecast',
    'weather.saved': 'Saved Locations',
    'weather.saveLocation': 'Save Location',
    'weather.currentLocation': 'Current Location',
    'weather.feelsLike': 'Feels Like',
    'weather.wind': 'Wind',
    'weather.humidity': 'Humidity',
    'weather.pressure': 'Pressure',
    'weather.visibility': 'Visibility',
    'weather.uvIndex': 'UV Index',
    'weather.uvLow': 'Low',
    'weather.uvModerate': 'Moderate',
    'weather.uvHigh': 'High',
    'weather.uvVeryHigh': 'Very High',
    'weather.uvExtreme': 'Extreme',
    'weather.avgTemp': 'Average',
    'weather.noSavedLocations': 'No saved locations',
    'weather.errors.enterLocation': 'Please enter a city name',
    'weather.errors.fetchFailed': 'Failed to fetch weather data',
    'weather.errors.locationFailed': 'Failed to get current location',
    'weather.errors.locationNotSupported': 'Your browser does not support geolocation',
    
    // E-book Reader
    'tools.ebook-reader.title': 'E-book Reader',
    'tools.ebook-reader.description': 'Read e-books online with support for multiple formats, bookmarks, notes and reading progress',
    'ebook.uploadPrompt': 'Drag and drop files here or click to select',
    'ebook.supportedFormats': 'Supports EPUB, PDF, TXT, MOBI formats',
    'ebook.selectFile': 'Select File',
    'ebook.parsing': 'Parsing e-book...',
    'ebook.reader': 'Reader',
    'ebook.contents': 'Contents',
    'ebook.bookmarks': 'Bookmarks',
    'ebook.search': 'Search',
    'ebook.fontSize': 'Font Size',
    'ebook.lineHeight': 'Line Height',
    'ebook.fontFamily': 'Font Family',
    'ebook.serif': 'Serif',
    'ebook.sansSerif': 'Sans Serif',
    'ebook.monospace': 'Monospace',
    'ebook.theme': 'Theme',
    'ebook.lightTheme': 'Light',
    'ebook.darkTheme': 'Dark',
    'ebook.sepiaTheme': 'Sepia',
    'ebook.addBookmark': 'Add Bookmark',
    'ebook.page': 'Page',
    'ebook.previous': 'Previous',
    'ebook.next': 'Next',
    'ebook.noBookmarks': 'No bookmarks',
    'ebook.searchPlaceholder': 'Search content...',
    'ebook.searchResults': 'Search Results',
    'ebook.readingStats': 'Reading Statistics',
    'ebook.minutes': 'minutes',
    'ebook.progress': 'Progress',
    'ebook.pagesRead': 'Pages Read',
    'ebook.errors.parseFailed': 'Failed to parse e-book',
  },
  ja: {
    // 共通
    'common.delete': '削除',
    'common.save': '保存',
    'common.cancel': 'キャンセル',
    'common.confirm': '確認',
    'common.copy': 'コピー',
    'common.copied': 'コピーしました',
    'common.clear': 'クリア',
    'common.reset': 'リセット',
    'common.download': 'ダウンロード',
    'common.upload': 'アップロード',
    'common.generate': '生成',
    'common.calculate': '計算',
    'common.convert': '変換',
    'common.search': '検索',
    'common.add': '追加',
    'common.remove': '削除',
    'common.edit': '編集',
    'common.view': '表示',
    'common.close': '閉じる',
    'common.back': '戻る',
    'common.next': '次へ',
    'common.previous': '前へ',
    'common.finish': '完了',
    'common.loading': '読み込み中...',
    'common.error': 'エラー',
    'common.success': '成功',
    'common.warning': '警告',
    'common.info': '情報',
    
    // ツール
    'tools.weather-forecast.title': '天気予報',
    'tools.weather-forecast.description': '世界中の場所のリアルタイム天気と予報を確認',
    
    // 天気ツール
    'weather.search': '検索',
    'weather.searchPlaceholder': '都市名を入力...',
    'weather.unit': '温度単位',
    'weather.celsius': '摂氏',
    'weather.fahrenheit': '華氏',
    'weather.current': '現在の天気',
    'weather.forecast': '予報',
    'weather.saved': '保存した場所',
    'weather.saveLocation': '場所を保存',
    'weather.currentLocation': '現在地',
    'weather.feelsLike': '体感温度',
    'weather.wind': '風速',
    'weather.humidity': '湿度',
    'weather.pressure': '気圧',
    'weather.visibility': '視程',
    'weather.uvIndex': 'UV指数',
    'weather.uvLow': '低い',
    'weather.uvModerate': '中程度',
    'weather.uvHigh': '高い',
    'weather.uvVeryHigh': '非常に高い',
    'weather.uvExtreme': '極端に高い',
    'weather.avgTemp': '平均',
    'weather.noSavedLocations': '保存した場所はありません',
    'weather.errors.enterLocation': '都市名を入力してください',
    'weather.errors.fetchFailed': '天気データの取得に失敗しました',
    'weather.errors.locationFailed': '現在地の取得に失敗しました',
    'weather.errors.locationNotSupported': 'お使いのブラウザは位置情報をサポートしていません',
    
    // 電子書籍リーダー
    'tools.ebook-reader.title': '電子書籍リーダー',
    'tools.ebook-reader.description': 'オンラインで電子書籍を読む、複数フォーマット、ブックマーク、メモ、読書進捗管理対応',
    'ebook.uploadPrompt': 'ここにファイルをドラッグ＆ドロップまたはクリックして選択',
    'ebook.supportedFormats': 'EPUB、PDF、TXT、MOBI形式に対応',
    'ebook.selectFile': 'ファイルを選択',
    'ebook.parsing': '電子書籍を解析中...',
    'ebook.reader': 'リーダー',
    'ebook.contents': '目次',
    'ebook.bookmarks': 'ブックマーク',
    'ebook.search': '検索',
    'ebook.fontSize': 'フォントサイズ',
    'ebook.lineHeight': '行の高さ',
    'ebook.fontFamily': 'フォント',
    'ebook.serif': 'セリフ',
    'ebook.sansSerif': 'サンセリフ',
    'ebook.monospace': '等幅',
    'ebook.theme': 'テーマ',
    'ebook.lightTheme': 'ライト',
    'ebook.darkTheme': 'ダーク',
    'ebook.sepiaTheme': 'セピア',
    'ebook.addBookmark': 'ブックマークを追加',
    'ebook.page': 'ページ',
    'ebook.previous': '前へ',
    'ebook.next': '次へ',
    'ebook.noBookmarks': 'ブックマークはありません',
    'ebook.searchPlaceholder': 'コンテンツを検索...',
    'ebook.searchResults': '検索結果',
    'ebook.readingStats': '読書統計',
    'ebook.minutes': '分',
    'ebook.progress': '進捗',
    'ebook.pagesRead': '読んだページ',
    'ebook.errors.parseFailed': '電子書籍の解析に失敗しました',
  }
}

export function useTranslations() {
  const params = useParams()
  const locale = (params?.locale as string) || 'zh'
  
  const t = (key: TranslationKey): string => {
    return translations[locale]?.[key] || translations['en']?.[key] || key
  }
  
  return t
}
