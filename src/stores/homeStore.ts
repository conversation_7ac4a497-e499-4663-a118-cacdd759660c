import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface HomeStore {
  // 搜索相关
  searchQuery: string
  searchHistory: string[]
  setSearchQuery: (query: string) => void
  addSearchHistory: (query: string) => void
  clearSearchHistory: () => void

  // 分类筛选
  selectedCategories: string[]
  toggleCategory: (category: string) => void
  clearCategories: () => void
  
  // 重置所有过滤条件
  resetFilters: () => void

  // 工具收藏和历史
  favoriteTools: string[]
  recentTools: string[]
  addFavorite: (toolId: string) => void
  removeFavorite: (toolId: string) => void
  addRecentTool: (toolId: string) => void
}

export const useHomeStore = create<HomeStore>()(
  persist(
    (set) => ({
      // 搜索相关
      searchQuery: '',
      searchHistory: [],
      setSearchQuery: (query) => set({ searchQuery: query }),
      addSearchHistory: (query) =>
        set((state) => ({
          searchHistory: [
            query,
            ...state.searchHistory.filter((q) => q !== query),
          ].slice(0, 10), // 只保留最近10条
        })),
      clearSearchHistory: () => set({ searchHistory: [] }),

      // 分类筛选
      selectedCategories: [],
      toggleCategory: (category) =>
        set((state) => ({
          selectedCategories: state.selectedCategories.includes(category)
            ? state.selectedCategories.filter((c) => c !== category)
            : [...state.selectedCategories, category],
        })),
      clearCategories: () => set({ selectedCategories: [] }),
      
      // 重置所有过滤条件
      resetFilters: () => set({ searchQuery: '', selectedCategories: [] }),

      // 工具收藏和历史
      favoriteTools: [],
      recentTools: [],
      addFavorite: (toolId) =>
        set((state) => ({
          favoriteTools: state.favoriteTools.includes(toolId)
            ? state.favoriteTools
            : [...state.favoriteTools, toolId],
        })),
      removeFavorite: (toolId) =>
        set((state) => ({
          favoriteTools: state.favoriteTools.filter((id) => id !== toolId),
        })),
      addRecentTool: (toolId) =>
        set((state) => ({
          recentTools: [
            toolId,
            ...state.recentTools.filter((id) => id !== toolId),
          ].slice(0, 20), // 只保留最近20个
        })),
    }),
    {
      name: 'poorlow-tools-home',
    }
  )
)
