'use client'

import React, { createContext, useContext, useCallback } from 'react'
import { useRouter, usePathname, useParams } from 'next/navigation'
import { type Locale, locales, defaultLocale } from '@/i18n/config'

interface LanguageContextType {
  locale: Locale
  locales: readonly Locale[]
  changeLocale: (newLocale: Locale) => void
  getLocalizedPath: (path: string, newLocale?: Locale) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ 
  children,
  locale: initialLocale 
}: { 
  children: React.ReactNode
  locale?: Locale 
}) {
  const router = useRouter()
  const pathname = usePathname()
  const params = useParams()
  
  // 获取当前语言，优先使用传入的 locale，否则从 params 获取
  const locale = initialLocale || (params.locale as Locale) || defaultLocale

  // 获取本地化路径
  const getLocalizedPath = useCallback((path: string, newLocale?: Locale) => {
    const targetLocale = newLocale || locale
    
    // 如果路径已经包含语言前缀，替换它
    const pathWithoutLocale = path.replace(/^\/[a-z]{2}(-[A-Z]{2})?/, '')
    
    // 返回带有正确语言前缀的路径
    return `/${targetLocale}${pathWithoutLocale}`
  }, [locale])

  // 切换语言
  const changeLocale = useCallback((newLocale: Locale) => {
    if (newLocale === locale) return
    
    // 设置 cookie
    document.cookie = `preferred-language=${newLocale};path=/;max-age=31536000`
    
    // 获取新的路径并导航
    const newPath = getLocalizedPath(pathname, newLocale)
    router.push(newPath)
  }, [locale, pathname, router, getLocalizedPath])

  const value: LanguageContextType = {
    locale,
    locales,
    changeLocale,
    getLocalizedPath
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}

// 自定义 Hook 使用语言上下文
export function useLanguage() {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

// 便捷 Hook 只获取当前语言
export function useLocale() {
  const { locale } = useLanguage()
  return locale
}

// 便捷 Hook 获取本地化路径函数
export function useLocalizedPath() {
  const { getLocalizedPath } = useLanguage()
  return getLocalizedPath
}
