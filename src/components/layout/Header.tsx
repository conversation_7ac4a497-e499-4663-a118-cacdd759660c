'use client'

import Link from 'next/link'
import { useState } from 'react'
import { Menu, X, <PERSON>, Moon, Sun } from 'lucide-react'
import { cn } from '@/lib/utils'
import { LanguageSwitcher } from '@/components/common/LanguageSwitcher'
import { useParams } from 'next/navigation'
import { type Locale } from '@/i18n/config'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(false)
  const params = useParams()
  const locale = (params.locale as Locale) || 'zh'

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)
  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
    // 这里将来需要集成主题系统
    document.documentElement.classList.toggle('dark')
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-primary">PoorLow</span>
            <span className="text-2xl font-light">Tools</span>
          </Link>

          {/* 桌面端导航 */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link href={`/${locale}`} className="text-sm font-medium hover:text-primary transition-colors">
              首页
            </Link>
            <Link href={`/${locale}/tools`} className="text-sm font-medium hover:text-primary transition-colors">
              所有工具
            </Link>
            <Link href={`/${locale}/categories`} className="text-sm font-medium hover:text-primary transition-colors">
              分类
            </Link>
            <Link href={`/${locale}/about`} className="text-sm font-medium hover:text-primary transition-colors">
              关于
            </Link>
          </nav>

          {/* 功能按钮组 */}
          <div className="flex items-center space-x-4">
            {/* 搜索按钮 */}
            <button className="p-2 hover:bg-accent rounded-md transition-colors">
              <Search className="h-5 w-5" />
              <span className="sr-only">搜索</span>
            </button>

            {/* 语言切换 */}
            <LanguageSwitcher />

            {/* 主题切换 */}
            <button
              onClick={toggleDarkMode}
              className="p-2 hover:bg-accent rounded-md transition-colors"
            >
              {isDarkMode ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
              <span className="sr-only">切换主题</span>
            </button>

            {/* 移动端菜单按钮 */}
            <button
              onClick={toggleMenu}
              className="md:hidden p-2 hover:bg-accent rounded-md transition-colors"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
              <span className="sr-only">菜单</span>
            </button>
          </div>
        </div>

        {/* 移动端菜单 */}
        <div
          className={cn(
            "md:hidden overflow-hidden transition-all duration-300",
            isMenuOpen ? "max-h-60" : "max-h-0"
          )}
        >
          <nav className="py-4 space-y-2">
            <Link
              href={`/${locale}`}
              className="block px-4 py-2 text-sm font-medium hover:bg-accent rounded-md transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              首页
            </Link>
            <Link
              href={`/${locale}/tools`}
              className="block px-4 py-2 text-sm font-medium hover:bg-accent rounded-md transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              所有工具
            </Link>
            <Link
              href={`/${locale}/categories`}
              className="block px-4 py-2 text-sm font-medium hover:bg-accent rounded-md transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              分类
            </Link>
            <Link
              href={`/${locale}/about`}
              className="block px-4 py-2 text-sm font-medium hover:bg-accent rounded-md transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              关于
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}
