'use client';

import React, { useState, useEffect } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { ToolTutorial } from '@/components/seo/ToolTutorial';
import { ToolFAQ } from '@/components/seo/ToolFAQ';
import { ToolScenarios } from '@/components/seo/ToolScenarios';
import { RelatedTools } from '@/components/seo/RelatedTools';
import { ToolRating } from '@/components/seo/ToolRating';
import { ChevronDown, ChevronUp, BookOpen, HelpCircle, Lightbulb, Link2 } from 'lucide-react';

interface ToolPageLayoutProps {
  children: React.ReactNode;
  toolId: string;
  locale: string;
}

/**
 * 工具页面SEO布局组件
 * 提供响应式两栏布局，左侧为工具主体，右侧为SEO内容
 * 移动端为垂直布局，SEO内容可折叠
 */
export function ToolPageLayout({ children, toolId, locale }: ToolPageLayoutProps) {
  const { t } = useTranslation({ namespace: 'tools/_common' });
  const [activeTab, setActiveTab] = useState<'tutorial' | 'faq' | 'scenarios' | 'related'>('tutorial');
  const [isMobileExpanded, setIsMobileExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测是否为移动设备
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 从localStorage读取用户偏好
  useEffect(() => {
    const savedExpanded = localStorage.getItem('seo-content-expanded');
    if (savedExpanded !== null) {
      setIsMobileExpanded(savedExpanded === 'true');
    }
  }, []);

  // 保存用户偏好
  const toggleMobileExpanded = () => {
    const newValue = !isMobileExpanded;
    setIsMobileExpanded(newValue);
    localStorage.setItem('seo-content-expanded', String(newValue));
  };

  const tabs = [
    { id: 'tutorial' as const, label: t('tutorial'), icon: BookOpen },
    { id: 'faq' as const, label: t('faq'), icon: HelpCircle },
    { id: 'scenarios' as const, label: t('scenarios'), icon: Lightbulb },
    { id: 'related' as const, label: t('related_tools'), icon: Link2 },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-6 lg:py-8">
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
          {/* 工具主体区域 */}
          <div className="w-full lg:w-[70%] xl:w-[72%]">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              {children}
            </div>
          </div>

          {/* SEO内容区域 */}
          <div className="w-full lg:w-[30%] xl:w-[28%]">
            {/* 移动端折叠按钮 */}
            {isMobile && (
              <button
                onClick={toggleMobileExpanded}
                className="lg:hidden w-full mb-4 px-4 py-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                aria-expanded={isMobileExpanded}
                aria-controls="seo-content"
              >
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  {t('learn_more')}
                </span>
                {isMobileExpanded ? (
                  <ChevronUp className="w-5 h-5 text-gray-500" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                )}
              </button>
            )}

            {/* SEO内容容器 */}
            <div
              id="seo-content"
              className={`
                bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden
                transition-all duration-300 ease-in-out
                ${isMobile && !isMobileExpanded ? 'max-h-0 opacity-0' : 'max-h-[5000px] opacity-100'}
              `}
            >
              {/* 标签页导航 */}
              <div className="border-b border-gray-200 dark:border-gray-700">
                <nav className="flex -mb-px" role="tablist">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`
                          flex-1 px-3 py-3 text-sm font-medium text-center border-b-2 transition-colors
                          ${activeTab === tab.id
                            ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                            : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                          }
                        `}
                        role="tab"
                        aria-selected={activeTab === tab.id}
                        aria-controls={`tabpanel-${tab.id}`}
                      >
                        <Icon className="w-4 h-4 mx-auto mb-1" />
                        <span className="hidden sm:inline">{tab.label}</span>
                      </button>
                    );
                  })}
                </nav>
              </div>

              {/* 标签页内容 */}
              <div className="p-4 lg:p-5">
                {/* 教程标签页 */}
                <div
                  id="tabpanel-tutorial"
                  role="tabpanel"
                  className={activeTab === 'tutorial' ? 'block' : 'hidden'}
                >
                  <ToolTutorial toolId={toolId} locale={locale} />
                </div>

                {/* FAQ标签页 */}
                <div
                  id="tabpanel-faq"
                  role="tabpanel"
                  className={activeTab === 'faq' ? 'block' : 'hidden'}
                >
                  <ToolFAQ toolId={toolId} locale={locale} />
                </div>

                {/* 使用场景标签页 */}
                <div
                  id="tabpanel-scenarios"
                  role="tabpanel"
                  className={activeTab === 'scenarios' ? 'block' : 'hidden'}
                >
                  <ToolScenarios toolId={toolId} locale={locale} />
                </div>

                {/* 相关工具标签页 */}
                <div
                  id="tabpanel-related"
                  role="tabpanel"
                  className={activeTab === 'related' ? 'block' : 'hidden'}
                >
                  <RelatedTools toolId={toolId} locale={locale} />
                </div>
              </div>

              {/* 评分组件 - 始终显示在底部 */}
              <div className="border-t border-gray-200 dark:border-gray-700 p-4 lg:p-5">
                <ToolRating toolId={toolId} locale={locale} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
