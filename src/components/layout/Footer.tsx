import Link from 'next/link'
import { Github, Twitter, Mail, Heart } from 'lucide-react'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    产品: [
      { name: '所有工具', href: '/tools' },
      { name: '分类浏览', href: '/categories' },
      { name: '最新工具', href: '/new' },
      { name: '热门工具', href: '/popular' },
    ],
    资源: [
      { name: '使用指南', href: '/guide' },
      { name: 'API 文档', href: '/api' },
      { name: '更新日志', href: '/changelog' },
      { name: '常见问题', href: '/faq' },
    ],
    关于: [
      { name: '关于我们', href: '/about' },
      { name: '联系我们', href: '/contact' },
      { name: '隐私政策', href: '/privacy' },
      { name: '使用条款', href: '/terms' },
    ],
  }

  return (
    <footer className="bg-muted/50 border-t">
      <div className="container mx-auto px-4 py-12">
        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* 品牌信息 */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-bold mb-2">PoorLow Tools</h3>
              <p className="text-sm text-muted-foreground">
                100+实用在线工具，无需安装，即用即走。为开发者和创作者打造的高效工具集。
              </p>
            </div>
            <div className="flex space-x-4">
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-colors"
                aria-label="GitHub"
              >
                <Github className="h-5 w-5" />
              </a>
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-colors"
                aria-label="Twitter"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-muted-foreground hover:text-primary transition-colors"
                aria-label="Email"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* 链接组 */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h4 className="font-semibold mb-4">{category}</h4>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* 分隔线 */}
        <div className="border-t pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* 版权信息 */}
            <div className="text-sm text-muted-foreground text-center md:text-left">
              <p className="flex items-center justify-center md:justify-start">
                © {currentYear} PoorLow Tools. Made with 
                <Heart className="h-4 w-4 mx-1 text-red-500 fill-current" />
                in China.
              </p>
            </div>

            {/* 语言选择（简化版） */}
            <div className="flex items-center space-x-4 text-sm">
              <button className="text-muted-foreground hover:text-primary transition-colors">
                简体中文
              </button>
              <span className="text-muted-foreground">|</span>
              <button className="text-muted-foreground hover:text-primary transition-colors">
                English
              </button>
              <span className="text-muted-foreground">|</span>
              <button className="text-muted-foreground hover:text-primary transition-colors">
                日本語
              </button>
            </div>
          </div>
        </div>

        {/* 性能和无障碍标记 */}
        <div className="mt-8 flex flex-wrap justify-center gap-4 text-xs text-muted-foreground">
          <span>🚀 极速加载</span>
          <span>♿ 无障碍优化</span>
          <span>📱 移动端友好</span>
          <span>🔒 安全可靠</span>
          <span>🌍 全球 CDN</span>
        </div>
      </div>
    </footer>
  )
}
