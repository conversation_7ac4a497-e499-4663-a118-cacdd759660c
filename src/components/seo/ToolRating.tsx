'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { Star, ThumbsUp, Zap, Heart, Award } from 'lucide-react';

export interface ToolRatingProps {
  toolId: string;
  locale: string;
}

interface RatingData {
  average: number;
  count: number;
  distribution: number[];
  userRating?: number;
  userTags?: string[];
}

interface RatingTag {
  id: string;
  label: string;
  icon: React.ReactNode;
  count: number;
}

/**
 * 工具评分组件
 * 展示工具评分和用户反馈，使用localStorage存储数据
 */
export function ToolRating({ toolId, locale }: ToolRatingProps) {
  const { t } = useTranslation({ namespace: `tools/${toolId}` });
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set());
  const [hasRated, setHasRated] = useState(false);
  
  // 默认评分数据
  const defaultRatingData = useMemo<RatingData>(() => ({
    average: 4.5,
    count: 128,
    distribution: [2, 3, 8, 35, 80], // 1-5星的分布
  }), []);
  
  const [ratingData, setRatingData] = useState<RatingData>(defaultRatingData);

  const tags: RatingTag[] = [
    { id: 'useful', label: t('rating.tags.useful') || '实用', icon: <ThumbsUp className="w-3 h-3" />, count: 89 },
    { id: 'fast', label: t('rating.tags.fast') || '快速', icon: <Zap className="w-3 h-3" />, count: 67 },
    { id: 'easy', label: t('rating.tags.easy') || '简单', icon: <Heart className="w-3 h-3" />, count: 54 },
    { id: 'professional', label: t('rating.tags.professional') || '专业', icon: <Award className="w-3 h-3" />, count: 42 },
  ];

  // 从localStorage加载评分数据
  useEffect(() => {
    const storageKey = `tool-rating-${toolId}`;
    const storedData = localStorage.getItem(storageKey);
    
    if (storedData) {
      try {
        const data = JSON.parse(storedData);
        setRatingData(data.global || defaultRatingData);
        
        // 检查用户是否已评分
        const userRatingKey = `user-rating-${toolId}`;
        const userRating = localStorage.getItem(userRatingKey);
        if (userRating) {
          const userData = JSON.parse(userRating);
          setRating(userData.rating);
          setSelectedTags(new Set(userData.tags || []));
          setHasRated(true);
        }
      } catch (error) {
        console.error('Failed to load rating data:', error);
      }
    }
  }, [toolId, defaultRatingData]);

  // 提交评分
  const submitRating = () => {
    if (rating === 0) return;

    // 更新全局评分数据
    const newCount = ratingData.count + 1;
    const newAverage = ((ratingData.average * ratingData.count) + rating) / newCount;
    const newDistribution = [...ratingData.distribution];
    newDistribution[rating - 1]++;

    const newRatingData: RatingData = {
      average: newAverage,
      count: newCount,
      distribution: newDistribution,
    };

    // 保存到localStorage
    const storageKey = `tool-rating-${toolId}`;
    const globalData = {
      global: newRatingData,
      lastUpdated: new Date().toISOString(),
    };
    localStorage.setItem(storageKey, JSON.stringify(globalData));

    // 保存用户评分
    const userRatingKey = `user-rating-${toolId}`;
    const userData = {
      rating,
      tags: Array.from(selectedTags),
      timestamp: new Date().toISOString(),
    };
    localStorage.setItem(userRatingKey, JSON.stringify(userData));

    // 更新标签计数
    selectedTags.forEach(tagId => {
      const tag = tags.find(t => t.id === tagId);
      if (tag) {
        tag.count++;
      }
    });

    setRatingData(newRatingData);
    setHasRated(true);
  };

  // 切换标签选择
  const toggleTag = (tagId: string) => {
    if (hasRated) return;
    
    const newTags = new Set(selectedTags);
    if (newTags.has(tagId)) {
      newTags.delete(tagId);
    } else {
      newTags.add(tagId);
    }
    setSelectedTags(newTags);
  };

  // 计算星级显示
  const renderStars = (value: number, interactive: boolean = false) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => {
          const filled = star <= (interactive ? (hoveredRating || rating) : value);
          return (
            <button
              key={star}
              onClick={() => !hasRated && interactive && setRating(star)}
              onMouseEnter={() => !hasRated && interactive && setHoveredRating(star)}
              onMouseLeave={() => !hasRated && interactive && setHoveredRating(0)}
              disabled={hasRated || !interactive}
              className={`${
                interactive && !hasRated ? 'cursor-pointer' : 'cursor-default'
              } transition-colors`}
            >
              <Star
                className={`w-5 h-5 ${
                  filled
                    ? 'fill-yellow-400 text-yellow-400'
                    : 'fill-gray-200 text-gray-200 dark:fill-gray-700 dark:text-gray-700'
                }`}
              />
            </button>
          );
        })}
      </div>
    );
  };

  return (
    <div className="rating-container">
      {/* 平均评分展示 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {ratingData.average.toFixed(1)}
          </span>
          {renderStars(Math.round(ratingData.average))}
        </div>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {ratingData.count} {t('rating.reviews') || '评价'}
        </span>
      </div>

      {/* 评分分布 */}
      <div className="space-y-1 mb-6">
        {[5, 4, 3, 2, 1].map((star) => {
          const count = ratingData.distribution[star - 1];
          const percentage = ratingData.count > 0 ? (count / ratingData.count) * 100 : 0;
          
          return (
            <div key={star} className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 w-3">{star}</span>
              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
              <div className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-yellow-400 transition-all duration-300"
                  style={{ width: `${percentage}%` }}
                />
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400 w-8 text-right">
                {count}
              </span>
            </div>
          );
        })}
      </div>

      {/* 用户评分区域 */}
      {!hasRated ? (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
            {t('rating.rate_this_tool') || '为这个工具评分'}
          </p>
          
          {/* 星级选择 */}
          <div className="flex items-center justify-center mb-4">
            {renderStars(rating, true)}
          </div>

          {/* 标签选择 */}
          <div className="flex flex-wrap gap-2 mb-4">
            {tags.map((tag) => (
              <button
                key={tag.id}
                onClick={() => toggleTag(tag.id)}
                className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs transition-colors ${
                  selectedTags.has(tag.id)
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                    : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                {tag.icon}
                <span>{tag.label}</span>
              </button>
            ))}
          </div>

          {/* 提交按钮 */}
          <button
            onClick={submitRating}
            disabled={rating === 0}
            className={`w-full py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
              rating === 0
                ? 'bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-600 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700'
            }`}
          >
            {t('rating.submit') || '提交评价'}
          </button>
        </div>
      ) : (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {t('rating.thanks') || '感谢您的评价！'}
            </p>
            <div className="flex items-center justify-center space-x-2">
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {t('rating.your_rating') || '您的评分：'}
              </span>
              {renderStars(rating)}
            </div>
            {selectedTags.size > 0 && (
              <div className="mt-2 flex flex-wrap gap-1 justify-center">
                {Array.from(selectedTags).map(tagId => {
                  const tag = tags.find(t => t.id === tagId);
                  return tag ? (
                    <span
                      key={tagId}
                      className="inline-flex items-center space-x-1 px-2 py-0.5 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded-full text-xs"
                    >
                      {tag.icon}
                      <span>{tag.label}</span>
                    </span>
                  ) : null;
                })}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 热门标签展示 */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
          {t('rating.popular_tags') || '用户认为这个工具：'}
        </p>
        <div className="flex flex-wrap gap-2">
          {tags
            .sort((a, b) => b.count - a.count)
            .slice(0, 3)
            .map((tag) => (
              <div
                key={tag.id}
                className="inline-flex items-center space-x-1 text-xs text-gray-600 dark:text-gray-400"
              >
                {tag.icon}
                <span>{tag.label}</span>
                <span className="text-gray-400 dark:text-gray-600">({tag.count})</span>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
}
