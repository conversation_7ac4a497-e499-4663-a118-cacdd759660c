'use client';

import React, { useState, useEffect } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { ChevronRight, Copy, Check, BookOpen } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

export interface ToolTutorialProps {
  toolId: string;
  locale: string;
}

interface TutorialSection {
  id: string;
  title: string;
  content: string;
  level: number;
}

/**
 * 工具教程组件
 * 展示工具的详细使用教程，支持Markdown渲染和代码高亮
 */
export function ToolTutorial({ toolId, locale }: ToolTutorialProps) {
  const { t } = useTranslation({ namespace: `tools/${toolId}` });
  const [sections, setSections] = useState<TutorialSection[]>([]);
  const [activeSection, setActiveSection] = useState<string>('');
  const [copiedCode, setCopiedCode] = useState<string>('');

  // 模拟教程内容 - 实际应从翻译文件或API加载
  useEffect(() => {
    // TODO: 从翻译文件加载实际教程内容
    const mockTutorial = `
# 如何使用此工具

这是一个功能强大的在线工具，可以帮助您快速完成任务。

## 快速开始

1. 在输入框中输入您的内容
2. 选择所需的选项
3. 点击执行按钮
4. 查看并复制结果

## 主要功能

- **实时处理**：输入即可看到结果
- **多种格式**：支持多种输入输出格式
- **批量处理**：可以同时处理多个项目
- **历史记录**：自动保存您的操作历史

## 使用示例

### 基础示例

\`\`\`javascript
// 示例代码
const input = "Hello World";
const result = processData(input);
console.log(result);
\`\`\`

### 高级示例

\`\`\`python
# Python示例
import tool

data = tool.load_data("input.txt")
processed = tool.process(data, options={
    "format": "json",
    "encoding": "utf-8"
})
tool.save_result(processed, "output.json")
\`\`\`

## 使用技巧

1. **快捷键**：使用 Ctrl+Enter 快速执行
2. **拖拽文件**：直接拖拽文件到输入区域
3. **批量操作**：按住 Shift 选择多个项目
4. **自定义设置**：在设置中调整默认参数

## 常见问题

如果遇到问题，请检查：
- 输入格式是否正确
- 网络连接是否正常
- 浏览器是否支持所需功能
    `;

    // 解析Markdown内容，提取章节
    const lines = mockTutorial.split('\n');
    const extractedSections: TutorialSection[] = [];
    let currentContent = '';
    let currentSection: TutorialSection | null = null;

    lines.forEach((line) => {
      const h2Match = line.match(/^## (.+)$/);
      const h3Match = line.match(/^### (.+)$/);

      if (h2Match) {
        // 保存当前章节
        if (currentSection) {
          currentSection.content = currentContent.trim();
          extractedSections.push(currentSection);
        }
        
        // 创建新的 h2 章节
        currentSection = {
          id: h2Match[1].toLowerCase().replace(/\s+/g, '-'),
          title: h2Match[1],
          content: '',
          level: 2,
        };
        currentContent = '';
      } else if (h3Match) {
        // 保存当前章节
        if (currentSection) {
          currentSection.content = currentContent.trim();
          extractedSections.push(currentSection);
        }
        
        // 创建新的 h3 章节
        currentSection = {
          id: h3Match[1].toLowerCase().replace(/\s+/g, '-'),
          title: h3Match[1],
          content: '',
          level: 3,
        };
        currentContent = '';
      } else if (currentSection) {
        // 累积内容
        currentContent += line + '\n';
      }
    });

    // 保存最后一个章节
    if (currentSection) {
      (currentSection as TutorialSection).content = currentContent.trim();
      extractedSections.push(currentSection as TutorialSection);
    }

    setSections(extractedSections);
    if (extractedSections.length > 0) {
      setActiveSection(extractedSections[0].id);
    }
  }, [toolId, locale, t]);

  // 复制代码功能
  const copyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(''), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  // 自定义Markdown组件
  const components = {
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || '');
      const code = String(children).replace(/\n$/, '');

      if (!inline && match) {
        return (
          <div className="relative group">
            <button
              onClick={() => copyCode(code)}
              className="absolute right-2 top-2 p-2 bg-gray-700 hover:bg-gray-600 rounded-md opacity-0 group-hover:opacity-100 transition-opacity z-10"
              aria-label="复制代码"
            >
              {copiedCode === code ? (
                <Check className="w-4 h-4 text-green-400" />
              ) : (
                <Copy className="w-4 h-4 text-gray-300" />
              )}
            </button>
            <pre className="bg-gray-800 text-gray-100 p-4 rounded-md overflow-x-auto">
              <code className={className} {...props}>
                {code}
              </code>
            </pre>
          </div>
        );
      }

      return (
        <code className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-sm" {...props}>
          {children}
        </code>
      );
    },
    h1: ({ children }: any) => (
      <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">{children}</h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-xl font-semibold mt-6 mb-3 text-gray-800 dark:text-gray-200">{children}</h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-lg font-medium mt-4 mb-2 text-gray-700 dark:text-gray-300">{children}</h3>
    ),
    p: ({ children }: any) => (
      <p className="mb-4 text-gray-600 dark:text-gray-400 leading-relaxed">{children}</p>
    ),
    ul: ({ children }: any) => (
      <ul className="mb-4 ml-6 list-disc text-gray-600 dark:text-gray-400">{children}</ul>
    ),
    ol: ({ children }: any) => (
      <ol className="mb-4 ml-6 list-decimal text-gray-600 dark:text-gray-400">{children}</ol>
    ),
    li: ({ children }: any) => (
      <li className="mb-1">{children}</li>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-blue-500 pl-4 py-2 mb-4 italic text-gray-600 dark:text-gray-400">
        {children}
      </blockquote>
    ),
    strong: ({ children }: any) => (
      <strong className="font-semibold text-gray-700 dark:text-gray-300">{children}</strong>
    ),
  };

  return (
    <div className="tutorial-container">
      {/* 桌面端侧边栏目录 */}
      <div className="hidden xl:block xl:float-right xl:w-48 xl:ml-6 xl:mb-6">
        <div className="sticky top-4 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
            <BookOpen className="w-4 h-4 mr-2" />
            目录
          </h3>
          <nav className="space-y-1">
            {sections.map((section) => (
              <a
                key={section.id}
                href={`#${section.id}`}
                onClick={(e) => {
                  e.preventDefault();
                  setActiveSection(section.id);
                  document.getElementById(section.id)?.scrollIntoView({ behavior: 'smooth' });
                }}
                className={`
                  block py-1 text-sm transition-colors
                  ${section.level === 3 ? 'pl-4' : ''}
                  ${activeSection === section.id
                    ? 'text-blue-600 dark:text-blue-400 font-medium'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                  }
                `}
              >
                {section.level === 3 && (
                  <ChevronRight className="inline-block w-3 h-3 mr-1" />
                )}
                {section.title}
              </a>
            ))}
          </nav>
        </div>
      </div>

      {/* 教程内容 */}
      <div className="prose prose-gray dark:prose-invert max-w-none">
        <ReactMarkdown components={components}>
          {sections.map(s => `${'#'.repeat(s.level)} ${s.title}\n\n${s.content}`).join('\n\n')}
        </ReactMarkdown>
      </div>

      {/* 移动端浮动目录按钮 */}
      <div className="xl:hidden fixed bottom-20 right-4 z-10">
        <button
          className="p-3 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg"
          aria-label="显示目录"
        >
          <BookOpen className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
}
