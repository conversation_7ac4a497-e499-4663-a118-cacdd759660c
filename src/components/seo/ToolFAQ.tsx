'use client';

import React, { useState, useMemo } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { ChevronDown, Search, HelpCircle } from 'lucide-react';

export interface ToolFAQProps {
  toolId: string;
  locale: string;
}

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

/**
 * 工具FAQ组件
 * 展示常见问题和答案，支持搜索和分类筛选
 */
export function ToolFAQ({ toolId, locale }: ToolFAQProps) {
  const { t } = useTranslation({ namespace: `tools/${toolId}` });
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // FAQ数据，使用useMemo缓存避免每次渲染重新创建
  const faqItems: FAQItem[] = useMemo(() => [
    {
      id: '1',
      question: t('faq.q1') || '这个工具是免费的吗？',
      answer: t('faq.a1') || '是的，这个工具完全免费，无需注册即可使用所有功能。',
      category: 'general'
    },
    {
      id: '2',
      question: t('faq.q2') || '我的数据安全吗？',
      answer: t('faq.a2') || '所有数据处理都在您的浏览器本地完成，不会上传到服务器，确保您的数据安全。',
      category: 'security'
    },
    {
      id: '3',
      question: t('faq.q3') || '支持哪些文件格式？',
      answer: t('faq.a3') || '支持多种常见格式，包括文本、JSON、XML等。具体支持的格式请查看工具说明。',
      category: 'features'
    },
    {
      id: '4',
      question: t('faq.q4') || '如何批量处理？',
      answer: t('faq.a4') || '您可以一次性输入多个项目，使用换行符分隔，工具会自动批量处理。',
      category: 'usage'
    },
    {
      id: '5',
      question: t('faq.q5') || '处理结果可以导出吗？',
      answer: t('faq.a5') || '是的，您可以复制结果或下载为文件。支持多种导出格式。',
      category: 'features'
    }
  ], [t]);

  // 获取所有分类
  const categories = useMemo(() => {
    const cats = new Set<string>();
    faqItems.forEach(item => {
      if (item.category) cats.add(item.category);
    });
    return Array.from(cats);
  }, [faqItems]);

  // 过滤FAQ项目
  const filteredItems = useMemo(() => {
    return faqItems.filter(item => {
      // 分类过滤
      if (selectedCategory !== 'all' && item.category !== selectedCategory) {
        return false;
      }
      
      // 搜索过滤
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          item.question.toLowerCase().includes(query) ||
          item.answer.toLowerCase().includes(query)
        );
      }
      
      return true;
    });
  }, [faqItems, selectedCategory, searchQuery]);

  // 切换展开/收起
  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  // 展开/收起所有
  const toggleAll = () => {
    if (expandedItems.size === filteredItems.length) {
      setExpandedItems(new Set());
    } else {
      setExpandedItems(new Set(filteredItems.map(item => item.id)));
    }
  };

  // 高亮搜索关键词
  const highlightText = (text: string) => {
    if (!searchQuery) return text;
    
    const regex = new RegExp(`(${searchQuery})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  return (
    <div className="faq-container">
      {/* 搜索框 */}
      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={t('faq.search_placeholder') || '搜索问题...'}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* 分类筛选 */}
      {categories.length > 0 && (
        <div className="mb-4 flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-3 py-1 text-sm rounded-full transition-colors ${
              selectedCategory === 'all'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            {t('faq.all_categories') || '全部'}
          </button>
          {categories.map(cat => (
            <button
              key={cat}
              onClick={() => setSelectedCategory(cat)}
              className={`px-3 py-1 text-sm rounded-full transition-colors ${
                selectedCategory === cat
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {t(`faq.category.${cat}`) || cat}
            </button>
          ))}
        </div>
      )}

      {/* 展开/收起所有按钮 */}
      {filteredItems.length > 0 && (
        <div className="mb-4 flex justify-end">
          <button
            onClick={toggleAll}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
          >
            {expandedItems.size === filteredItems.length
              ? (t('faq.collapse_all') || '收起所有')
              : (t('faq.expand_all') || '展开所有')}
          </button>
        </div>
      )}

      {/* FAQ列表 */}
      <div className="space-y-3">
        {filteredItems.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <HelpCircle className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>{t('faq.no_results') || '没有找到相关问题'}</p>
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="mt-2 text-blue-600 dark:text-blue-400 hover:underline"
              >
                {t('faq.clear_search') || '清除搜索'}
              </button>
            )}
          </div>
        ) : (
          filteredItems.map((item) => (
            <div
              key={item.id}
              className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
            >
              <button
                onClick={() => toggleExpanded(item.id)}
                className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                aria-expanded={expandedItems.has(item.id)}
                aria-controls={`faq-answer-${item.id}`}
              >
                <span className="font-medium text-gray-900 dark:text-gray-100 pr-2">
                  {highlightText(item.question)}
                </span>
                <ChevronDown
                  className={`w-5 h-5 text-gray-500 flex-shrink-0 transition-transform ${
                    expandedItems.has(item.id) ? 'transform rotate-180' : ''
                  }`}
                />
              </button>
              
              <div
                id={`faq-answer-${item.id}`}
                className={`px-4 overflow-hidden transition-all duration-200 ${
                  expandedItems.has(item.id) ? 'py-3' : 'max-h-0'
                }`}
              >
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  {highlightText(item.answer)}
                </p>
              </div>
            </div>
          ))
        )}
      </div>

      {/* 提示信息 */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <p className="text-sm text-blue-800 dark:text-blue-200">
          {t('faq.help_text') || '没有找到您的问题？请通过页面底部的联系方式与我们联系。'}
        </p>
      </div>
    </div>
  );
}
