'use client';

import React, { useMemo } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { Link2, <PERSON>R<PERSON>, Sparkles } from 'lucide-react';
import Link from 'next/link';

export interface RelatedToolsProps {
  toolId: string;
  locale: string;
}

interface RelatedTool {
  id: string;
  name: string;
  description: string;
  category: string;
  relevanceScore: number;
}

/**
 * 相关工具组件
 * 展示与当前工具相关的其他工具，提高用户留存和页面浏览量
 */
export function RelatedTools({ toolId, locale }: RelatedToolsProps) {
  const { t } = useTranslation({ namespace: `tools/${toolId}` });
  const { t: tCommon } = useTranslation({ namespace: 'tools/_common' });

  // 相关工具数据，使用useMemo缓存避免每次渲染重新创建
  const relatedTools: RelatedTool[] = useMemo(() => [
    {
      id: 'url-encoder',
      name: t('related.url_encoder.name') || 'URL编码解码',
      description: t('related.url_encoder.description') || '对URL进行编码和解码，处理特殊字符',
      category: 'encoding',
      relevanceScore: 0.9
    },
    {
      id: 'json-formatter',
      name: t('related.json_formatter.name') || 'JSON格式化',
      description: t('related.json_formatter.description') || '格式化和验证JSON数据',
      category: 'development',
      relevanceScore: 0.8
    },
    {
      id: 'md5-generator',
      name: t('related.md5_generator.name') || 'MD5生成器',
      description: t('related.md5_generator.description') || '生成文本或文件的MD5哈希值',
      category: 'security',
      relevanceScore: 0.7
    },
    {
      id: 'text-diff',
      name: t('related.text_diff.name') || '文本对比',
      description: t('related.text_diff.description') || '比较两段文本的差异',
      category: 'text',
      relevanceScore: 0.6
    },
    {
      id: 'image-to-base64',
      name: t('related.image_to_base64.name') || '图片转Base64',
      description: t('related.image_to_base64.description') || '将图片转换为Base64编码',
      category: 'image',
      relevanceScore: 0.95
    }
  ], [t]);

  // 按相关度排序
  const sortedTools = useMemo(() => {
    return [...relatedTools].sort((a, b) => b.relevanceScore - a.relevanceScore);
  }, [relatedTools]);

  // 获取前4个最相关的工具
  const topRelatedTools = sortedTools.slice(0, 4);

  return (
    <div className="related-tools-container">
      {/* 相关工具网格 */}
      <div className="grid gap-3">
        {topRelatedTools.map((tool) => (
          <Link
            key={tool.id}
            href={`/${locale}/tools/${tool.id}`}
            className="block p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all group"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {tool.name}
                </h4>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                  {tool.description}
                </p>
                {tool.relevanceScore >= 0.9 && (
                  <div className="mt-2 inline-flex items-center text-xs text-orange-600 dark:text-orange-400">
                    <Sparkles className="w-3 h-3 mr-1" />
                    {t('related.highly_relevant') || '高度相关'}
                  </div>
                )}
              </div>
              <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors ml-3 flex-shrink-0" />
            </div>
          </Link>
        ))}
      </div>

      {/* 查看更多工具 */}
      <div className="mt-6 text-center">
        <Link
          href={`/${locale}/tools`}
          className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
        >
          <Link2 className="w-4 h-4 mr-1" />
          {t('related.view_all_tools') || '查看所有工具'}
        </Link>
      </div>

      {/* 工具推荐说明 */}
      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
          {t('related.why_recommended') || '为什么推荐这些工具？'}
        </h4>
        <ul className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
          <li>• {t('related.reason1') || '基于工具类别和功能相似性'}</li>
          <li>• {t('related.reason2') || '用户经常一起使用的工具'}</li>
          <li>• {t('related.reason3') || '可以配合完成更复杂的任务'}</li>
        </ul>
      </div>

      {/* 工具组合提示 */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="flex items-start space-x-2">
          <Sparkles className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <span className="font-medium">{t('related.pro_tip') || '专业提示：'}</span>
              {t('related.pro_tip_content') || '将这些工具组合使用，可以大大提高您的工作效率。例如，先用Base64编码数据，再用MD5生成校验值。'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
