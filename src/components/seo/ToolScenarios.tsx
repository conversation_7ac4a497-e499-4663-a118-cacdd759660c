'use client';

import React from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { Lightbulb, Code, FileText, Users, Zap, Globe } from 'lucide-react';
import type { LucideIcon } from 'lucide-react';

export interface ToolScenariosProps {
  toolId: string;
  locale: string;
}

interface Scenario {
  id: string;
  icon: LucideIcon;
  title: string;
  description: string;
  example?: string;
}

/**
 * 工具使用场景组件
 * 展示工具的实际应用场景和案例
 */
export function ToolScenarios({ toolId, locale }: ToolScenariosProps) {
  const { t } = useTranslation({ namespace: `tools/${toolId}` });

  // 模拟场景数据 - 实际应从翻译文件或API加载
  const scenarios: Scenario[] = [
    {
      id: 'development',
      icon: Code,
      title: t('scenarios.development.title') || '开发调试',
      description: t('scenarios.development.description') || '在开发过程中快速编码和解码数据，方便调试API接口。',
      example: t('scenarios.development.example') || '将JSON数据编码为Base64格式，用于API传输。'
    },
    {
      id: 'data-transfer',
      icon: Globe,
      title: t('scenarios.data_transfer.title') || '数据传输',
      description: t('scenarios.data_transfer.description') || '在不同系统之间安全传输二进制数据。',
      example: t('scenarios.data_transfer.example') || '将图片转换为Base64字符串，嵌入到JSON中传输。'
    },
    {
      id: 'documentation',
      icon: FileText,
      title: t('scenarios.documentation.title') || '文档编写',
      description: t('scenarios.documentation.description') || '在技术文档中嵌入编码后的示例数据。',
      example: t('scenarios.documentation.example') || '在Markdown文档中嵌入Base64编码的图片。'
    },
    {
      id: 'team-collaboration',
      icon: Users,
      title: t('scenarios.team_collaboration.title') || '团队协作',
      description: t('scenarios.team_collaboration.description') || '与团队成员分享编码后的配置或敏感数据。',
      example: t('scenarios.team_collaboration.example') || '将配置文件编码后通过聊天工具分享。'
    },
    {
      id: 'automation',
      icon: Zap,
      title: t('scenarios.automation.title') || '自动化处理',
      description: t('scenarios.automation.description') || '在自动化脚本中批量处理数据编码。',
      example: t('scenarios.automation.example') || '批量将文件转换为Base64格式。'
    }
  ];

  return (
    <div className="scenarios-container">
      {/* 场景网格 */}
      <div className="grid gap-4 md:grid-cols-2">
        {scenarios.map((scenario) => {
          const Icon = scenario.icon;
          return (
            <div
              key={scenario.id}
              className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <Icon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
                    {scenario.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    {scenario.description}
                  </p>
                  {scenario.example && (
                    <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs text-gray-700 dark:text-gray-300">
                      <span className="font-medium">{t('scenarios.example_label') || '示例：'}</span>
                      {scenario.example}
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 更多场景提示 */}
      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <Lightbulb className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h4 className="font-medium text-gray-900 dark:text-gray-100">
            {t('scenarios.more_ideas') || '更多使用场景'}
          </h4>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {t('scenarios.more_ideas_description') || '这个工具的应用场景远不止这些。发挥您的创造力，探索更多可能性！如果您有独特的使用方式，欢迎与我们分享。'}
        </p>
      </div>

      {/* 最佳实践 */}
      <div className="mt-6">
        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
          {t('scenarios.best_practices') || '最佳实践'}
        </h4>
        <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
          <li className="flex items-start">
            <span className="text-green-500 mr-2">✓</span>
            <span>{t('scenarios.practice1') || '始终验证输入数据的格式是否正确'}</span>
          </li>
          <li className="flex items-start">
            <span className="text-green-500 mr-2">✓</span>
            <span>{t('scenarios.practice2') || '对于大文件，考虑分块处理以提高性能'}</span>
          </li>
          <li className="flex items-start">
            <span className="text-green-500 mr-2">✓</span>
            <span>{t('scenarios.practice3') || '保存处理结果前进行备份'}</span>
          </li>
          <li className="flex items-start">
            <span className="text-green-500 mr-2">✓</span>
            <span>{t('scenarios.practice4') || '使用批量处理功能提高效率'}</span>
          </li>
        </ul>
      </div>
    </div>
  );
}
