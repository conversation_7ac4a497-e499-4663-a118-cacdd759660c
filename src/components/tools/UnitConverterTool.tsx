'use client'

import { useState, useEffect } from 'react'
import { Calculator, ArrowLeftRight, Thermometer, Ruler, Scale, Zap } from 'lucide-react'

// 单位类型定义
type UnitCategory = 'length' | 'weight' | 'temperature' | 'speed' | 'area' | 'volume'

interface Unit {
  name: string
  symbol: string
  toBase: (value: number) => number
  fromBase: (value: number) => number
}

// 单位定义
const unitDefinitions: Record<UnitCategory, Record<string, Unit>> = {
  length: {
    meter: { name: '米', symbol: 'm', toBase: (v) => v, fromBase: (v) => v },
    kilometer: { name: '千米', symbol: 'km', toBase: (v) => v * 1000, fromBase: (v) => v / 1000 },
    centimeter: { name: '厘米', symbol: 'cm', toBase: (v) => v / 100, fromBase: (v) => v * 100 },
    millimeter: { name: '毫米', symbol: 'mm', toBase: (v) => v / 1000, fromBase: (v) => v * 1000 },
    mile: { name: '英里', symbol: 'mi', toBase: (v) => v * 1609.344, fromBase: (v) => v / 1609.344 },
    yard: { name: '码', symbol: 'yd', toBase: (v) => v * 0.9144, fromBase: (v) => v / 0.9144 },
    foot: { name: '英尺', symbol: 'ft', toBase: (v) => v * 0.3048, fromBase: (v) => v / 0.3048 },
    inch: { name: '英寸', symbol: 'in', toBase: (v) => v * 0.0254, fromBase: (v) => v / 0.0254 },
  },
  weight: {
    kilogram: { name: '千克', symbol: 'kg', toBase: (v) => v, fromBase: (v) => v },
    gram: { name: '克', symbol: 'g', toBase: (v) => v / 1000, fromBase: (v) => v * 1000 },
    milligram: { name: '毫克', symbol: 'mg', toBase: (v) => v / 1000000, fromBase: (v) => v * 1000000 },
    ton: { name: '吨', symbol: 't', toBase: (v) => v * 1000, fromBase: (v) => v / 1000 },
    pound: { name: '磅', symbol: 'lb', toBase: (v) => v * 0.453592, fromBase: (v) => v / 0.453592 },
    ounce: { name: '盎司', symbol: 'oz', toBase: (v) => v * 0.0283495, fromBase: (v) => v / 0.0283495 },
    jin: { name: '斤', symbol: '斤', toBase: (v) => v * 0.5, fromBase: (v) => v / 0.5 },
  },
  temperature: {
    celsius: { name: '摄氏度', symbol: '°C', toBase: (v) => v, fromBase: (v) => v },
    fahrenheit: { name: '华氏度', symbol: '°F', toBase: (v) => (v - 32) * 5/9, fromBase: (v) => v * 9/5 + 32 },
    kelvin: { name: '开尔文', symbol: 'K', toBase: (v) => v - 273.15, fromBase: (v) => v + 273.15 },
  },
  speed: {
    mps: { name: '米/秒', symbol: 'm/s', toBase: (v) => v, fromBase: (v) => v },
    kmh: { name: '千米/时', symbol: 'km/h', toBase: (v) => v / 3.6, fromBase: (v) => v * 3.6 },
    mph: { name: '英里/时', symbol: 'mph', toBase: (v) => v * 0.44704, fromBase: (v) => v / 0.44704 },
    knot: { name: '节', symbol: 'kn', toBase: (v) => v * 0.514444, fromBase: (v) => v / 0.514444 },
  },
  area: {
    sqmeter: { name: '平方米', symbol: 'm²', toBase: (v) => v, fromBase: (v) => v },
    sqkilometer: { name: '平方千米', symbol: 'km²', toBase: (v) => v * 1000000, fromBase: (v) => v / 1000000 },
    hectare: { name: '公顷', symbol: 'ha', toBase: (v) => v * 10000, fromBase: (v) => v / 10000 },
    acre: { name: '英亩', symbol: 'ac', toBase: (v) => v * 4046.86, fromBase: (v) => v / 4046.86 },
    sqfoot: { name: '平方英尺', symbol: 'ft²', toBase: (v) => v * 0.092903, fromBase: (v) => v / 0.092903 },
    mu: { name: '亩', symbol: '亩', toBase: (v) => v * 666.667, fromBase: (v) => v / 666.667 },
  },
  volume: {
    liter: { name: '升', symbol: 'L', toBase: (v) => v, fromBase: (v) => v },
    milliliter: { name: '毫升', symbol: 'mL', toBase: (v) => v / 1000, fromBase: (v) => v * 1000 },
    cubicmeter: { name: '立方米', symbol: 'm³', toBase: (v) => v * 1000, fromBase: (v) => v / 1000 },
    gallon: { name: '加仑', symbol: 'gal', toBase: (v) => v * 3.78541, fromBase: (v) => v / 3.78541 },
    quart: { name: '夸脱', symbol: 'qt', toBase: (v) => v * 0.946353, fromBase: (v) => v / 0.946353 },
    pint: { name: '品脱', symbol: 'pt', toBase: (v) => v * 0.473176, fromBase: (v) => v / 0.473176 },
    cup: { name: '杯', symbol: 'cup', toBase: (v) => v * 0.236588, fromBase: (v) => v / 0.236588 },
  },
}

const categoryInfo: Record<UnitCategory, { name: string; icon: React.ReactNode }> = {
  length: { name: '长度', icon: <Ruler className="w-5 h-5" /> },
  weight: { name: '重量', icon: <Scale className="w-5 h-5" /> },
  temperature: { name: '温度', icon: <Thermometer className="w-5 h-5" /> },
  speed: { name: '速度', icon: <Zap className="w-5 h-5" /> },
  area: { name: '面积', icon: <Calculator className="w-5 h-5" /> },
  volume: { name: '体积', icon: <Calculator className="w-5 h-5" /> },
}

export default function UnitConverterTool() {
  const [category, setCategory] = useState<UnitCategory>('length')
  const [fromUnit, setFromUnit] = useState('meter')
  const [toUnit, setToUnit] = useState('kilometer')
  const [fromValue, setFromValue] = useState('')
  const [toValue, setToValue] = useState('')
  const [activeInput, setActiveInput] = useState<'from' | 'to'>('from')

  // 获取当前分类的单位
  const currentUnits = unitDefinitions[category]
  const unitKeys = Object.keys(currentUnits)

  // 转换逻辑
  useEffect(() => {
    if (activeInput === 'from' && fromValue !== '') {
      const numValue = parseFloat(fromValue)
      if (!isNaN(numValue)) {
        const baseValue = currentUnits[fromUnit].toBase(numValue)
        const result = currentUnits[toUnit].fromBase(baseValue)
        setToValue(result.toFixed(6).replace(/\.?0+$/, ''))
      } else {
        setToValue('')
      }
    } else if (activeInput === 'to' && toValue !== '') {
      const numValue = parseFloat(toValue)
      if (!isNaN(numValue)) {
        const baseValue = currentUnits[toUnit].toBase(numValue)
        const result = currentUnits[fromUnit].fromBase(baseValue)
        setFromValue(result.toFixed(6).replace(/\.?0+$/, ''))
      } else {
        setFromValue('')
      }
    }
  }, [fromValue, toValue, fromUnit, toUnit, category, activeInput, currentUnits])

  // 切换分类时重置单位
  useEffect(() => {
    const units = Object.keys(unitDefinitions[category])
    setFromUnit(units[0])
    setToUnit(units[1])
    setFromValue('')
    setToValue('')
  }, [category])

  // 交换单位
  const swapUnits = () => {
    setFromUnit(toUnit)
    setToUnit(fromUnit)
    setFromValue(toValue)
    setToValue(fromValue)
  }

  // 常用转换
  const commonConversions = [
    { from: 'meter', to: 'foot', category: 'length' as UnitCategory },
    { from: 'kilogram', to: 'pound', category: 'weight' as UnitCategory },
    { from: 'celsius', to: 'fahrenheit', category: 'temperature' as UnitCategory },
    { from: 'kilometer', to: 'mile', category: 'length' as UnitCategory },
  ]

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">单位换算器</h2>
        <p className="text-gray-600">
          支持长度、重量、温度等多种单位的快速换算
        </p>
      </div>

      {/* 分类选择 */}
      <div className="mb-6">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
          {(Object.entries(categoryInfo) as [UnitCategory, typeof categoryInfo[UnitCategory]][]).map(([key, info]) => (
            <button
              key={key}
              onClick={() => setCategory(key)}
              className={`flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                category === key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
            >
              {info.icon}
              <span>{info.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 转换器主体 */}
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4">
          {/* 输入单位 */}
          <div className="flex-1 w-full">
            <label className="block text-sm font-medium text-gray-700 mb-2">从</label>
            <select
              value={fromUnit}
              onChange={(e) => setFromUnit(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 mb-2"
            >
              {unitKeys.map((unit) => (
                <option key={unit} value={unit}>
                  {currentUnits[unit].name} ({currentUnits[unit].symbol})
                </option>
              ))}
            </select>
            <input
              type="number"
              value={fromValue}
              onChange={(e) => {
                setFromValue(e.target.value)
                setActiveInput('from')
              }}
              onFocus={() => setActiveInput('from')}
              placeholder="输入数值"
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* 交换按钮 */}
          <button
            onClick={swapUnits}
            className="flex items-center justify-center w-12 h-12 bg-white border border-gray-300 rounded-full hover:bg-gray-50 transition-colors"
            aria-label="交换单位"
          >
            <ArrowLeftRight className="w-5 h-5 text-gray-600" />
          </button>

          {/* 输出单位 */}
          <div className="flex-1 w-full">
            <label className="block text-sm font-medium text-gray-700 mb-2">到</label>
            <select
              value={toUnit}
              onChange={(e) => setToUnit(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 mb-2"
            >
              {unitKeys.map((unit) => (
                <option key={unit} value={unit}>
                  {currentUnits[unit].name} ({currentUnits[unit].symbol})
                </option>
              ))}
            </select>
            <input
              type="number"
              value={toValue}
              onChange={(e) => {
                setToValue(e.target.value)
                setActiveInput('to')
              }}
              onFocus={() => setActiveInput('to')}
              placeholder="转换结果"
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* 转换公式显示 */}
        {fromValue && toValue && (
          <div className="mt-4 text-center text-sm text-gray-600">
            {fromValue} {currentUnits[fromUnit].symbol} = {toValue} {currentUnits[toUnit].symbol}
          </div>
        )}
      </div>

      {/* 常用转换 */}
      <div className="mt-8">
        <h3 className="text-lg font-medium mb-4">常用转换</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {commonConversions.map((conv, index) => {
            const fromUnit = unitDefinitions[conv.category][conv.from]
            const toUnit = unitDefinitions[conv.category][conv.to]
            return (
              <button
                key={index}
                onClick={() => {
                  setCategory(conv.category)
                  setFromUnit(conv.from)
                  setToUnit(conv.to)
                }}
                className="p-3 bg-white border border-gray-200 rounded-md hover:bg-gray-50 transition-colors text-sm"
              >
                {fromUnit.name} → {toUnit.name}
              </button>
            )
          })}
        </div>
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Calculator className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持双向转换，可以在任意输入框输入数值</li>
              <li>点击交换按钮可以快速交换转换方向</li>
              <li>支持小数输入，结果自动保留合适的精度</li>
              <li>涵盖长度、重量、温度、速度、面积、体积等常用单位</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
