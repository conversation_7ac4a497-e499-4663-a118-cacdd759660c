'use client'

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { 
  Copy, 
  Download, 
  Layers, 
  Plus, 
  Minus, 
  RotateCcw, 
  Play,
  Box,
  ArrowUpDown,
  ArrowLeftRight,
  Zap,
  Settings
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface FlexItem {
  id: string
  label: string
  flexGrow: number
  flexShrink: number
  flexBasis: string
  alignSelf: string
  order: number
  backgroundColor: string
  textColor: string
  width?: string
  height?: string
}

interface FlexConfig {
  direction: string
  wrap: string
  justifyContent: string
  alignItems: string
  alignContent: string
  gap: number
  width: string
  height: string
  padding: number
}

interface FlexTemplate {
  name: string
  description: string
  config: FlexConfig
  items: FlexItem[]
}

const FlexboxGeneratorTool: React.FC = () => {
  const { toast } = useToast()

  const [flexConfig, setFlexConfig] = useState<FlexConfig>({
    direction: 'row',
    wrap: 'nowrap',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    alignContent: 'stretch',
    gap: 10,
    width: '100%',
    height: '300px',
    padding: 20
  })

  const [flexItems, setFlexItems] = useState<FlexItem[]>([
    {
      id: '1',
      label: 'Item 1',
      flexGrow: 0,
      flexShrink: 1,
      flexBasis: 'auto',
      alignSelf: 'auto',
      order: 0,
      backgroundColor: '#3b82f6',
      textColor: '#ffffff'
    },
    {
      id: '2',
      label: 'Item 2',
      flexGrow: 1,
      flexShrink: 1,
      flexBasis: 'auto',
      alignSelf: 'auto',
      order: 0,
      backgroundColor: '#10b981',
      textColor: '#ffffff'
    },
    {
      id: '3',
      label: 'Item 3',
      flexGrow: 0,
      flexShrink: 1,
      flexBasis: 'auto',
      alignSelf: 'auto',
      order: 0,
      backgroundColor: '#f59e0b',
      textColor: '#ffffff'
    }
  ])

  const [selectedItem, setSelectedItem] = useState<string | null>(null)
  const [previewMode, setPreviewMode] = useState<'visual' | 'code'>('visual')

  const flexTemplates: FlexTemplate[] = [
    {
      name: '导航栏布局',
      description: '水平导航菜单，项目均匀分布',
      config: {
        direction: 'row',
        wrap: 'nowrap',
        justifyContent: 'space-around',
        alignItems: 'center',
        alignContent: 'stretch',
        gap: 0,
        width: '100%',
        height: '60px',
        padding: 10
      },
      items: [
        { id: '1', label: '首页', flexGrow: 0, flexShrink: 0, flexBasis: 'auto', alignSelf: 'auto', order: 0, backgroundColor: '#1f2937', textColor: '#ffffff' },
        { id: '2', label: '产品', flexGrow: 0, flexShrink: 0, flexBasis: 'auto', alignSelf: 'auto', order: 0, backgroundColor: '#1f2937', textColor: '#ffffff' },
        { id: '3', label: '服务', flexGrow: 0, flexShrink: 0, flexBasis: 'auto', alignSelf: 'auto', order: 0, backgroundColor: '#1f2937', textColor: '#ffffff' },
        { id: '4', label: '关于', flexGrow: 0, flexShrink: 0, flexBasis: 'auto', alignSelf: 'auto', order: 0, backgroundColor: '#1f2937', textColor: '#ffffff' },
        { id: '5', label: '联系', flexGrow: 0, flexShrink: 0, flexBasis: 'auto', alignSelf: 'auto', order: 0, backgroundColor: '#1f2937', textColor: '#ffffff' }
      ]
    },
    {
      name: '卡片网格',
      description: '响应式卡片布局，自动换行',
      config: {
        direction: 'row',
        wrap: 'wrap',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        alignContent: 'flex-start',
        gap: 20,
        width: '100%',
        height: 'auto',
        padding: 20
      },
      items: Array.from({ length: 6 }, (_, i) => ({
        id: String(i + 1),
        label: `Card ${i + 1}`,
        flexGrow: 0,
        flexShrink: 0,
        flexBasis: '300px',
        alignSelf: 'auto',
        order: 0,
        backgroundColor: '#3b82f6',
        textColor: '#ffffff',
        height: '200px'
      }))
    },
    {
      name: '居中布局',
      description: '内容水平垂直居中',
      config: {
        direction: 'column',
        wrap: 'nowrap',
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        gap: 20,
        width: '100%',
        height: '400px',
        padding: 20
      },
      items: [
        {
          id: '1',
          label: '标题',
          flexGrow: 0,
          flexShrink: 0,
          flexBasis: 'auto',
          alignSelf: 'auto',
          order: 0,
          backgroundColor: '#1f2937',
          textColor: '#ffffff'
        },
        {
          id: '2',
          label: '内容描述',
          flexGrow: 0,
          flexShrink: 0,
          flexBasis: 'auto',
          alignSelf: 'auto',
          order: 0,
          backgroundColor: '#6b7280',
          textColor: '#ffffff'
        },
        {
          id: '3',
          label: '操作按钮',
          flexGrow: 0,
          flexShrink: 0,
          flexBasis: 'auto',
          alignSelf: 'auto',
          order: 0,
          backgroundColor: '#3b82f6',
          textColor: '#ffffff'
        }
      ]
    },
    {
      name: '侧边栏布局',
      description: '固定侧边栏 + 弹性主内容',
      config: {
        direction: 'row',
        wrap: 'nowrap',
        justifyContent: 'flex-start',
        alignItems: 'stretch',
        alignContent: 'stretch',
        gap: 0,
        width: '100%',
        height: '500px',
        padding: 0
      },
      items: [
        {
          id: '1',
          label: 'Sidebar',
          flexGrow: 0,
          flexShrink: 0,
          flexBasis: '250px',
          alignSelf: 'auto',
          order: 0,
          backgroundColor: '#374151',
          textColor: '#ffffff'
        },
        {
          id: '2',
          label: 'Main Content',
          flexGrow: 1,
          flexShrink: 1,
          flexBasis: 'auto',
          alignSelf: 'auto',
          order: 0,
          backgroundColor: '#f3f4f6',
          textColor: '#111827'
        }
      ]
    }
  ]

  const directionOptions = [
    { value: 'row', label: '水平 (row)' },
    { value: 'row-reverse', label: '水平反向 (row-reverse)' },
    { value: 'column', label: '垂直 (column)' },
    { value: 'column-reverse', label: '垂直反向 (column-reverse)' }
  ]

  const wrapOptions = [
    { value: 'nowrap', label: '不换行 (nowrap)' },
    { value: 'wrap', label: '换行 (wrap)' },
    { value: 'wrap-reverse', label: '反向换行 (wrap-reverse)' }
  ]

  const justifyContentOptions = [
    { value: 'flex-start', label: '起始对齐 (flex-start)' },
    { value: 'flex-end', label: '结束对齐 (flex-end)' },
    { value: 'center', label: '居中 (center)' },
    { value: 'space-between', label: '两端分布 (space-between)' },
    { value: 'space-around', label: '环绕分布 (space-around)' },
    { value: 'space-evenly', label: '均匀分布 (space-evenly)' }
  ]

  const alignItemsOptions = [
    { value: 'stretch', label: '拉伸 (stretch)' },
    { value: 'flex-start', label: '起始对齐 (flex-start)' },
    { value: 'flex-end', label: '结束对齐 (flex-end)' },
    { value: 'center', label: '居中 (center)' },
    { value: 'baseline', label: '基线对齐 (baseline)' }
  ]

  const alignSelfOptions = [
    { value: 'auto', label: '自动 (auto)' },
    { value: 'stretch', label: '拉伸 (stretch)' },
    { value: 'flex-start', label: '起始对齐 (flex-start)' },
    { value: 'flex-end', label: '结束对齐 (flex-end)' },
    { value: 'center', label: '居中 (center)' },
    { value: 'baseline', label: '基线对齐 (baseline)' }
  ]

  const addFlexItem = useCallback(() => {
    const newItem: FlexItem = {
      id: String(Date.now()),
      label: `Item ${flexItems.length + 1}`,
      flexGrow: 0,
      flexShrink: 1,
      flexBasis: 'auto',
      alignSelf: 'auto',
      order: 0,
      backgroundColor: '#3b82f6',
      textColor: '#ffffff'
    }
    setFlexItems(prev => [...prev, newItem])
  }, [flexItems.length])

  const updateFlexItem = useCallback((id: string, updates: Partial<FlexItem>) => {
    setFlexItems(prev => prev.map(item => 
      item.id === id ? { ...item, ...updates } : item
    ))
  }, [])

  const removeFlexItem = useCallback((id: string) => {
    setFlexItems(prev => prev.filter(item => item.id !== id))
    if (selectedItem === id) {
      setSelectedItem(null)
    }
  }, [selectedItem])

  const applyTemplate = useCallback((template: FlexTemplate) => {
    setFlexConfig(template.config)
    setFlexItems(template.items)
    setSelectedItem(null)
    toast({
      title: '模板已应用',
      description: `已成功应用"${template.name}"模板`
    })
  }, [toast])

  const resetFlex = useCallback(() => {
    setFlexConfig({
      direction: 'row',
      wrap: 'nowrap',
      justifyContent: 'flex-start',
      alignItems: 'stretch',
      alignContent: 'stretch',
      gap: 10,
      width: '100%',
      height: '300px',
      padding: 20
    })
    setFlexItems([
      {
        id: '1',
        label: 'Item 1',
        flexGrow: 0,
        flexShrink: 1,
        flexBasis: 'auto',
        alignSelf: 'auto',
        order: 0,
        backgroundColor: '#3b82f6',
        textColor: '#ffffff'
      },
      {
        id: '2',
        label: 'Item 2',
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: 'auto',
        alignSelf: 'auto',
        order: 0,
        backgroundColor: '#10b981',
        textColor: '#ffffff'
      },
      {
        id: '3',
        label: 'Item 3',
        flexGrow: 0,
        flexShrink: 1,
        flexBasis: 'auto',
        alignSelf: 'auto',
        order: 0,
        backgroundColor: '#f59e0b',
        textColor: '#ffffff'
      }
    ])
    setSelectedItem(null)
  }, [])

  const generateCSS = useCallback(() => {
    let css = `.flex-container {
  display: flex;
  flex-direction: ${flexConfig.direction};
  flex-wrap: ${flexConfig.wrap};
  justify-content: ${flexConfig.justifyContent};
  align-items: ${flexConfig.alignItems};
  align-content: ${flexConfig.alignContent};
  gap: ${flexConfig.gap}px;
  width: ${flexConfig.width};
  height: ${flexConfig.height};
  padding: ${flexConfig.padding}px;
}

`

    flexItems.forEach(item => {
      const flexShorthand = `${item.flexGrow} ${item.flexShrink} ${item.flexBasis}`
      css += `.flex-item-${item.id} {
  flex: ${flexShorthand};
  align-self: ${item.alignSelf};
  order: ${item.order};
  background-color: ${item.backgroundColor};
  color: ${item.textColor};
  padding: 1rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;${item.width ? `\n  width: ${item.width};` : ''}${item.height ? `\n  height: ${item.height};` : ''}
}

`
    })

    return css
  }, [flexConfig, flexItems])

  const generateHTML = useCallback(() => {
    let html = `<div class="flex-container">
`
    const sortedItems = [...flexItems].sort((a, b) => a.order - b.order)
    sortedItems.forEach(item => {
      html += `  <div class="flex-item-${item.id}">${item.label}</div>
`
    })
    html += `</div>`
    return html
  }, [flexItems])

  const copyCSS = useCallback(() => {
    navigator.clipboard.writeText(generateCSS())
    toast({
      title: '已复制',
      description: 'CSS代码已复制到剪贴板'
    })
  }, [generateCSS, toast])

  const copyHTML = useCallback(() => {
    navigator.clipboard.writeText(generateHTML())
    toast({
      title: '已复制',
      description: 'HTML代码已复制到剪贴板'
    })
  }, [generateHTML, toast])

  const downloadCode = useCallback(() => {
    const css = generateCSS()
    const html = generateHTML()
    
    const fullCode = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flexbox Layout</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        ${css}
    </style>
</head>
<body>
    ${html}
</body>
</html>`

    const blob = new Blob([fullCode], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'flexbox-layout.html'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: '下载完成',
      description: '完整的HTML文件已下载'
    })
  }, [generateCSS, generateHTML, toast])

  const containerStyle = {
    display: 'flex',
    flexDirection: flexConfig.direction as any,
    flexWrap: flexConfig.wrap as any,
    justifyContent: flexConfig.justifyContent as any,
    alignItems: flexConfig.alignItems as any,
    alignContent: flexConfig.alignContent as any,
    gap: `${flexConfig.gap}px`,
    width: '100%',
    minHeight: flexConfig.height === 'auto' ? '200px' : flexConfig.height,
    maxHeight: '600px',
    padding: `${flexConfig.padding}px`,
    backgroundColor: '#f8fafc',
    border: '2px dashed #cbd5e1',
    borderRadius: '8px',
    position: 'relative' as const,
    overflow: 'auto'
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Layers className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">Flexbox 生成器</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          可视化创建和编辑Flexbox弹性布局，实时预览效果，自动生成代码
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 配置面板 */}
        <div className="lg:col-span-1 space-y-6">
          <Tabs defaultValue="container" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="container">容器</TabsTrigger>
              <TabsTrigger value="items">项目</TabsTrigger>
              <TabsTrigger value="templates">模板</TabsTrigger>
            </TabsList>

            <TabsContent value="container" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    容器配置
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 主轴方向 */}
                  <div>
                    <Label>主轴方向 (flex-direction)</Label>
                    <Select 
                      value={flexConfig.direction}
                      onValueChange={(value) => setFlexConfig(prev => ({ ...prev, direction: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {directionOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 换行方式 */}
                  <div>
                    <Label>换行方式 (flex-wrap)</Label>
                    <Select 
                      value={flexConfig.wrap}
                      onValueChange={(value) => setFlexConfig(prev => ({ ...prev, wrap: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {wrapOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 主轴对齐 */}
                  <div>
                    <Label>主轴对齐 (justify-content)</Label>
                    <Select 
                      value={flexConfig.justifyContent}
                      onValueChange={(value) => setFlexConfig(prev => ({ ...prev, justifyContent: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {justifyContentOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 交叉轴对齐 */}
                  <div>
                    <Label>交叉轴对齐 (align-items)</Label>
                    <Select 
                      value={flexConfig.alignItems}
                      onValueChange={(value) => setFlexConfig(prev => ({ ...prev, alignItems: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {alignItemsOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 多行对齐 */}
                  <div>
                    <Label>多行对齐 (align-content)</Label>
                    <Select 
                      value={flexConfig.alignContent}
                      onValueChange={(value) => setFlexConfig(prev => ({ ...prev, alignContent: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {alignItemsOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 间距 */}
                  <div>
                    <Label className="text-sm">间距 (gap): {flexConfig.gap}px</Label>
                    <Slider
                      value={[flexConfig.gap]}
                      onValueChange={([value]) => setFlexConfig(prev => ({ ...prev, gap: value }))}
                      max={50}
                      step={1}
                      className="mt-2"
                    />
                  </div>

                  {/* 内边距 */}
                  <div>
                    <Label className="text-sm">内边距 (padding): {flexConfig.padding}px</Label>
                    <Slider
                      value={[flexConfig.padding]}
                      onValueChange={([value]) => setFlexConfig(prev => ({ ...prev, padding: value }))}
                      max={50}
                      step={1}
                      className="mt-2"
                    />
                  </div>

                  {/* 容器高度 */}
                  <div>
                    <Label>容器高度</Label>
                    <Input
                      value={flexConfig.height}
                      onChange={(e) => setFlexConfig(prev => ({ ...prev, height: e.target.value }))}
                      placeholder="300px"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="items" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Box className="h-5 w-5" />
                      弹性项目
                    </div>
                    <Button size="sm" onClick={addFlexItem}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {flexItems.map(item => (
                    <div key={item.id} className="p-3 border rounded-lg space-y-3">
                      <div className="flex items-center justify-between">
                        <Input
                          value={item.label}
                          onChange={(e) => updateFlexItem(item.id, { label: e.target.value })}
                          className="flex-1 mr-2"
                        />
                        <Button 
                          size="sm" 
                          variant={selectedItem === item.id ? "default" : "outline"}
                          onClick={() => setSelectedItem(selectedItem === item.id ? null : item.id)}
                          className="mr-1"
                        >
                          选择
                        </Button>
                        <Button 
                          size="sm" 
                          variant="destructive"
                          onClick={() => removeFlexItem(item.id)}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-2">
                        <div>
                          <Label className="text-xs">flex-grow</Label>
                          <Input
                            type="number"
                            min="0"
                            value={item.flexGrow}
                            onChange={(e) => updateFlexItem(item.id, { flexGrow: parseInt(e.target.value) || 0 })}
                          />
                        </div>
                        <div>
                          <Label className="text-xs">flex-shrink</Label>
                          <Input
                            type="number"
                            min="0"
                            value={item.flexShrink}
                            onChange={(e) => updateFlexItem(item.id, { flexShrink: parseInt(e.target.value) || 0 })}
                          />
                        </div>
                        <div>
                          <Label className="text-xs">flex-basis</Label>
                          <Input
                            value={item.flexBasis}
                            onChange={(e) => updateFlexItem(item.id, { flexBasis: e.target.value })}
                            placeholder="auto"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label className="text-xs">align-self</Label>
                          <Select 
                            value={item.alignSelf}
                            onValueChange={(value) => updateFlexItem(item.id, { alignSelf: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {alignSelfOptions.map(option => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label.split(' ')[0]}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label className="text-xs">order</Label>
                          <Input
                            type="number"
                            value={item.order}
                            onChange={(e) => updateFlexItem(item.id, { order: parseInt(e.target.value) || 0 })}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label className="text-xs">背景色</Label>
                          <Input
                            type="color"
                            value={item.backgroundColor}
                            onChange={(e) => updateFlexItem(item.id, { backgroundColor: e.target.value })}
                          />
                        </div>
                        <div>
                          <Label className="text-xs">文字色</Label>
                          <Input
                            type="color"
                            value={item.textColor}
                            onChange={(e) => updateFlexItem(item.id, { textColor: e.target.value })}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label className="text-xs">宽度 (可选)</Label>
                          <Input
                            value={item.width || ''}
                            onChange={(e) => updateFlexItem(item.id, { width: e.target.value || undefined })}
                            placeholder="auto"
                          />
                        </div>
                        <div>
                          <Label className="text-xs">高度 (可选)</Label>
                          <Input
                            value={item.height || ''}
                            onChange={(e) => updateFlexItem(item.id, { height: e.target.value || undefined })}
                            placeholder="auto"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="templates" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    预设模板
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {flexTemplates.map((template, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="font-medium">{template.name}</h4>
                          <p className="text-sm text-gray-500">{template.description}</p>
                        </div>
                        <Button 
                          size="sm" 
                          onClick={() => applyTemplate(template)}
                        >
                          应用
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        <Badge variant="secondary">
                          {template.config.direction}
                        </Badge>
                        <Badge variant="secondary">
                          {template.config.wrap}
                        </Badge>
                        <Badge variant="secondary">
                          {template.items.length}项目
                        </Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex gap-2">
            <Button variant="outline" onClick={resetFlex} className="flex-1">
              <RotateCcw className="h-4 w-4 mr-2" />
              重置
            </Button>
            <Button onClick={downloadCode} className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              下载
            </Button>
          </div>
        </div>

        {/* 预览和代码区域 */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Play className="h-5 w-5" />
                  实时预览
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant={previewMode === 'visual' ? 'default' : 'outline'}
                    onClick={() => setPreviewMode('visual')}
                  >
                    可视化
                  </Button>
                  <Button
                    size="sm"
                    variant={previewMode === 'code' ? 'default' : 'outline'}
                    onClick={() => setPreviewMode('code')}
                  >
                    代码
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {previewMode === 'visual' ? (
                <div style={containerStyle}>
                  {[...flexItems]
                    .sort((a, b) => a.order - b.order)
                    .map(item => (
                    <div
                      key={item.id}
                      style={{
                        flex: `${item.flexGrow} ${item.flexShrink} ${item.flexBasis}`,
                        alignSelf: item.alignSelf as any,
                        order: item.order,
                        backgroundColor: item.backgroundColor,
                        color: item.textColor,
                        padding: '1rem',
                        borderRadius: '4px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontWeight: '500',
                        cursor: 'pointer',
                        border: selectedItem === item.id ? '2px solid #3b82f6' : '1px solid rgba(0,0,0,0.1)',
                        transition: 'all 0.2s',
                        minWidth: '60px',
                        minHeight: '40px',
                        width: item.width,
                        height: item.height
                      }}
                      onClick={() => setSelectedItem(selectedItem === item.id ? null : item.id)}
                    >
                      {item.label}
                    </div>
                  ))}
                </div>
              ) : (
                <Tabs defaultValue="css" className="w-full">
                  <TabsList>
                    <TabsTrigger value="css">CSS</TabsTrigger>
                    <TabsTrigger value="html">HTML</TabsTrigger>
                  </TabsList>
                  <TabsContent value="css">
                    <div className="relative">
                      <Textarea
                        value={generateCSS()}
                        readOnly
                        className="min-h-[400px] font-mono text-sm"
                      />
                      <Button
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={copyCSS}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </TabsContent>
                  <TabsContent value="html">
                    <div className="relative">
                      <Textarea
                        value={generateHTML()}
                        readOnly
                        className="min-h-[200px] font-mono text-sm"
                      />
                      <Button
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={copyHTML}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>

          {/* Flexbox 信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowLeftRight className="h-5 w-5" />
                Flexbox 信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="font-semibold text-blue-600">{flexItems.length}</div>
                  <div className="text-gray-600">项目数</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="font-semibold text-green-600">{flexConfig.direction}</div>
                  <div className="text-gray-600">主轴方向</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="font-semibold text-purple-600">{flexConfig.wrap}</div>
                  <div className="text-gray-600">换行方式</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <div className="font-semibold text-orange-600">{flexConfig.gap}px</div>
                  <div className="text-gray-600">间距</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default FlexboxGeneratorTool