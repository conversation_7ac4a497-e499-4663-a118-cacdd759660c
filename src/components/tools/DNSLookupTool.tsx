'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Copy, Search, AlertCircle, CheckCircle, Globe, Server, Clock } from 'lucide-react'

interface DNSRecord {
  type: string
  name: string
  value: string
  ttl?: number
  priority?: number
}

interface DNSResult {
  domain: string
  records: DNSRecord[]
  timestamp: string
  queryTime: number
  server?: string
}

const DNSLookupTool: React.FC = () => {
  const [domain, setDomain] = useState('')
  const [recordType, setRecordType] = useState('A')
  const [results, setResults] = useState<DNSResult[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // 常见的DNS记录类型
  const recordTypes = [
    { value: 'A', label: 'A - IPv4地址', description: '将域名解析为IPv4地址' },
    { value: 'AAAA', label: 'AAAA - IPv6地址', description: '将域名解析为IPv6地址' },
    { value: 'CNAME', label: 'CNAME - 别名', description: '将域名指向另一个域名' },
    { value: 'MX', label: 'MX - 邮件服务器', description: '指定邮件服务器地址' },
    { value: 'TXT', label: 'TXT - 文本记录', description: '存储文本信息' },
    { value: 'NS', label: 'NS - 域名服务器', description: '指定域名服务器' },
    { value: 'SOA', label: 'SOA - 授权起始', description: '域名的权威信息' },
    { value: 'PTR', label: 'PTR - 反向解析', description: '将IP地址解析为域名' },
    { value: 'SRV', label: 'SRV - 服务记录', description: '指定服务的位置' },
    { value: 'CAA', label: 'CAA - 证书授权', description: '指定可颁发证书的CA' }
  ]

  // 模拟DNS查询（实际应用中需要使用真实的DNS API）
  const simulateDNSQuery = async (domain: string, type: string): Promise<DNSRecord[]> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))

    const records: DNSRecord[] = []

    switch (type) {
      case 'A':
        if (domain.includes('google')) {
          records.push({ type: 'A', name: domain, value: '**************', ttl: 300 })
          records.push({ type: 'A', name: domain, value: '**************', ttl: 300 })
        } else if (domain.includes('github')) {
          records.push({ type: 'A', name: domain, value: '************', ttl: 60 })
        } else {
          records.push({ type: 'A', name: domain, value: '*************', ttl: 86400 })
        }
        break
      case 'AAAA':
        if (domain.includes('google')) {
          records.push({ type: 'AAAA', name: domain, value: '2404:6800:4005:80f::200e', ttl: 300 })
        } else if (domain.includes('github')) {
          records.push({ type: 'AAAA', name: domain, value: '2606:50c0:8000::154', ttl: 60 })
        }
        break
      case 'CNAME':
        if (domain.startsWith('www.')) {
          records.push({ type: 'CNAME', name: domain, value: domain.substring(4), ttl: 3600 })
        } else if (domain.includes('cdn')) {
          records.push({ type: 'CNAME', name: domain, value: 'cdn.example.com', ttl: 1800 })
        }
        break
      case 'MX':
        records.push({ type: 'MX', name: domain, value: 'mail.example.com', ttl: 3600, priority: 10 })
        records.push({ type: 'MX', name: domain, value: 'mail2.example.com', ttl: 3600, priority: 20 })
        break
      case 'TXT':
        records.push({ type: 'TXT', name: domain, value: 'v=spf1 include:_spf.google.com ~all', ttl: 3600 })
        records.push({ type: 'TXT', name: domain, value: 'google-site-verification=abc123def456', ttl: 3600 })
        break
      case 'NS':
        records.push({ type: 'NS', name: domain, value: 'ns1.example.com', ttl: 86400 })
        records.push({ type: 'NS', name: domain, value: 'ns2.example.com', ttl: 86400 })
        break
      case 'SOA':
        records.push({ 
          type: 'SOA', 
          name: domain, 
          value: 'ns1.example.com hostmaster.example.com 2023071801 7200 3600 1209600 86400', 
          ttl: 86400 
        })
        break
      case 'PTR':
        if (domain.match(/^\d+\.\d+\.\d+\.\d+$/)) {
          records.push({ type: 'PTR', name: domain, value: 'example.com', ttl: 86400 })
        }
        break
      case 'SRV':
        records.push({ type: 'SRV', name: domain, value: '10 60 5060 sipserver.example.com', ttl: 3600 })
        break
      case 'CAA':
        records.push({ type: 'CAA', name: domain, value: '0 issue "letsencrypt.org"', ttl: 86400 })
        break
    }

    return records
  }

  const handleLookup = async () => {
    if (!domain.trim()) {
      setError('请输入域名或IP地址')
      return
    }

    setLoading(true)
    setError('')

    try {
      const startTime = Date.now()
      const records = await simulateDNSQuery(domain.trim(), recordType)
      const endTime = Date.now()

      const result: DNSResult = {
        domain: domain.trim(),
        records,
        timestamp: new Date().toISOString(),
        queryTime: endTime - startTime,
        server: '*******'
      }

      setResults(prev => [result, ...prev.slice(0, 4)])
    } catch (err) {
      setError(err instanceof Error ? err.message : '查询失败')
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const formatTTL = (ttl: number) => {
    if (ttl >= 86400) {
      return `${Math.floor(ttl / 86400)}天`
    } else if (ttl >= 3600) {
      return `${Math.floor(ttl / 3600)}小时`
    } else if (ttl >= 60) {
      return `${Math.floor(ttl / 60)}分钟`
    } else {
      return `${ttl}秒`
    }
  }

  const getRecordTypeColor = (type: string) => {
    const colors: { [key: string]: string } = {
      'A': 'bg-blue-100 text-blue-800',
      'AAAA': 'bg-purple-100 text-purple-800',
      'CNAME': 'bg-green-100 text-green-800',
      'MX': 'bg-orange-100 text-orange-800',
      'TXT': 'bg-yellow-100 text-yellow-800',
      'NS': 'bg-indigo-100 text-indigo-800',
      'SOA': 'bg-red-100 text-red-800',
      'PTR': 'bg-pink-100 text-pink-800',
      'SRV': 'bg-teal-100 text-teal-800',
      'CAA': 'bg-gray-100 text-gray-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            DNS查询工具
          </CardTitle>
          <CardDescription>
            查询域名的DNS记录，包括A、AAAA、CNAME、MX、TXT等记录类型
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="domain">域名或IP地址</Label>
              <Input
                id="domain"
                type="text"
                value={domain}
                onChange={(e) => setDomain(e.target.value)}
                placeholder="example.com 或 ***********"
                className="mt-1"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleLookup()
                  }
                }}
              />
            </div>
            <div>
              <Label htmlFor="recordType">记录类型</Label>
              <Select value={recordType} onValueChange={setRecordType}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {recordTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}

          <Button 
            onClick={handleLookup} 
            disabled={loading || !domain.trim()}
            className="w-full"
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                查询中...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Search className="w-4 h-4" />
                查询DNS记录
              </div>
            )}
          </Button>
        </CardContent>
      </Card>

      <Tabs defaultValue="results" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="results">查询结果</TabsTrigger>
          <TabsTrigger value="help">帮助说明</TabsTrigger>
        </TabsList>

        <TabsContent value="results" className="space-y-4">
          {results.length > 0 ? (
            <div className="space-y-4">
              {results.map((result, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">
                        {result.domain}
                      </CardTitle>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Clock className="w-4 h-4" />
                        {result.queryTime}ms
                      </div>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Server className="w-4 h-4" />
                        DNS服务器: {result.server}
                      </div>
                      <div>
                        查询时间: {new Date(result.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {result.records.length > 0 ? (
                      <div className="space-y-3">
                        {result.records.map((record, recordIndex) => (
                          <div key={recordIndex} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge className={getRecordTypeColor(record.type)}>
                                  {record.type}
                                </Badge>
                                {record.ttl && (
                                  <span className="text-sm text-gray-500">
                                    TTL: {formatTTL(record.ttl)}
                                  </span>
                                )}
                                {record.priority && (
                                  <span className="text-sm text-gray-500">
                                    优先级: {record.priority}
                                  </span>
                                )}
                              </div>
                              <div className="text-sm text-gray-600 break-all">
                                {record.name} → {record.value}
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(record.value)}
                              className="ml-2"
                            >
                              <Copy className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                        <p>未找到 {recordType} 记录</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Globe className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-500">暂无查询结果</p>
                <p className="text-sm text-gray-400 mt-1">
                  输入域名或IP地址，然后点击查询按钮
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="help">
          <Card>
            <CardHeader>
              <CardTitle>DNS记录类型说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recordTypes.map((type) => (
                  <div key={type.value} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                    <Badge className={getRecordTypeColor(type.value)}>
                      {type.value}
                    </Badge>
                    <div>
                      <h4 className="font-medium">{type.label}</h4>
                      <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">使用说明</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 输入完整的域名（如 example.com）或IP地址</li>
                  <li>• 选择要查询的DNS记录类型</li>
                  <li>• 点击查询按钮获取结果</li>
                  <li>• 可以点击复制按钮复制记录值</li>
                  <li>• 查询历史会保存最近5次结果</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default DNSLookupTool
