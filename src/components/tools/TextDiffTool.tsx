'use client'

import { useState, useMemo } from 'react'
import { Copy, Check, FileText, ArrowLeftRight, Download, Upload } from 'lucide-react'

interface DiffLine {
  type: 'equal' | 'add' | 'remove' | 'modify'
  leftLine?: string
  rightLine?: string
  leftLineNumber?: number
  rightLineNumber?: number
}

export default function TextDiffTool() {
  const [leftText, setLeftText] = useState('')
  const [rightText, setRightText] = useState('')
  const [showLineNumbers, setShowLineNumbers] = useState(true)
  const [ignoreCase, setIgnoreCase] = useState(false)
  const [ignoreWhitespace, setIgnoreWhitespace] = useState(false)
  const [diffMode, setDiffMode] = useState<'line' | 'word' | 'char'>('line')
  const [copied, setCopied] = useState<'left' | 'right' | null>(null)

  // 计算文本差异
  const diffResult = useMemo(() => {
    const leftLines = leftText.split('\n')
    const rightLines = rightText.split('\n')
    const result: DiffLine[] = []

    // 简单的行级别差异算法
    const maxLength = Math.max(leftLines.length, rightLines.length)
    let leftLineNum = 1
    let rightLineNum = 1

    for (let i = 0; i < maxLength; i++) {
      let leftLine = leftLines[i] !== undefined ? leftLines[i] : undefined
      let rightLine = rightLines[i] !== undefined ? rightLines[i] : undefined

      // 应用忽略选项
      if (ignoreCase) {
        leftLine = leftLine?.toLowerCase()
        rightLine = rightLine?.toLowerCase()
      }
      if (ignoreWhitespace) {
        leftLine = leftLine?.replace(/\s+/g, ' ').trim()
        rightLine = rightLine?.replace(/\s+/g, ' ').trim()
      }

      if (leftLine === rightLine && leftLine !== undefined) {
        result.push({
          type: 'equal',
          leftLine: leftLines[i],
          rightLine: rightLines[i],
          leftLineNumber: leftLineNum++,
          rightLineNumber: rightLineNum++,
        })
      } else if (leftLine !== undefined && rightLine === undefined) {
        result.push({
          type: 'remove',
          leftLine: leftLines[i],
          leftLineNumber: leftLineNum++,
        })
      } else if (leftLine === undefined && rightLine !== undefined) {
        result.push({
          type: 'add',
          rightLine: rightLines[i],
          rightLineNumber: rightLineNum++,
        })
      } else {
        result.push({
          type: 'modify',
          leftLine: leftLines[i],
          rightLine: rightLines[i],
          leftLineNumber: leftLineNum++,
          rightLineNumber: rightLineNum++,
        })
      }
    }

    return result
  }, [leftText, rightText, ignoreCase, ignoreWhitespace])

  // 统计信息
  const stats = useMemo(() => {
    const leftLines = leftText.split('\n').length
    const rightLines = rightText.split('\n').length
    const additions = diffResult.filter(d => d.type === 'add').length
    const deletions = diffResult.filter(d => d.type === 'remove').length
    const modifications = diffResult.filter(d => d.type === 'modify').length

    return {
      leftLines,
      rightLines,
      additions,
      deletions,
      modifications,
      total: additions + deletions + modifications,
    }
  }, [leftText, rightText, diffResult])

  // 复制文本
  const copyText = (text: string, side: 'left' | 'right') => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(side)
      setTimeout(() => setCopied(null), 2000)
    })
  }

  // 交换文本
  const swapTexts = () => {
    const temp = leftText
    setLeftText(rightText)
    setRightText(temp)
  }

  // 加载文件
  const loadFile = (file: File, side: 'left' | 'right') => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target?.result as string
      if (side === 'left') {
        setLeftText(text)
      } else {
        setRightText(text)
      }
    }
    reader.readAsText(file)
  }

  // 导出差异报告
  const exportDiff = () => {
    let report = '文本对比报告\n'
    report += '=' .repeat(50) + '\n\n'
    report += `统计信息：\n`
    report += `- 左侧行数：${stats.leftLines}\n`
    report += `- 右侧行数：${stats.rightLines}\n`
    report += `- 新增行数：${stats.additions}\n`
    report += `- 删除行数：${stats.deletions}\n`
    report += `- 修改行数：${stats.modifications}\n\n`
    report += '差异详情：\n'
    report += '-' .repeat(50) + '\n\n'

    diffResult.forEach((diff) => {
      if (diff.type === 'add') {
        report += `+ [行 ${diff.rightLineNumber}] ${diff.rightLine}\n`
      } else if (diff.type === 'remove') {
        report += `- [行 ${diff.leftLineNumber}] ${diff.leftLine}\n`
      } else if (diff.type === 'modify') {
        report += `~ [行 ${diff.leftLineNumber} -> ${diff.rightLineNumber}]\n`
        report += `  - ${diff.leftLine}\n`
        report += `  + ${diff.rightLine}\n`
      }
    })

    const blob = new Blob([report], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'text-diff-report.txt'
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本对比工具</h2>
        <p className="text-gray-600">
          对比两段文本的差异，支持行级别对比和多种选项
        </p>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* 统计信息 */}
          <div className="flex items-center gap-4 text-sm">
            <span className="text-green-600">+{stats.additions} 新增</span>
            <span className="text-red-600">-{stats.deletions} 删除</span>
            <span className="text-yellow-600">~{stats.modifications} 修改</span>
            <span className="text-gray-600">共 {stats.total} 处差异</span>
          </div>

          <div className="flex-1" />

          {/* 选项 */}
          <div className="flex items-center gap-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showLineNumbers}
                onChange={(e) => setShowLineNumbers(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm">显示行号</span>
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={ignoreCase}
                onChange={(e) => setIgnoreCase(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm">忽略大小写</span>
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={ignoreWhitespace}
                onChange={(e) => setIgnoreWhitespace(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm">忽略空白</span>
            </label>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-2">
            <button
              onClick={swapTexts}
              className="px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center"
            >
              <ArrowLeftRight className="w-4 h-4 mr-1" />
              交换
            </button>
            
            <button
              onClick={exportDiff}
              disabled={stats.total === 0}
              className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center disabled:bg-gray-400"
            >
              <Download className="w-4 h-4 mr-1" />
              导出报告
            </button>
          </div>
        </div>
      </div>

      {/* 文本输入区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
        {/* 左侧文本 */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="border-b border-gray-200 p-3 flex items-center justify-between">
            <h3 className="font-medium flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              原始文本
            </h3>
            <div className="flex items-center gap-2">
              <label className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded cursor-pointer">
                <input
                  type="file"
                  accept=".txt"
                  onChange={(e) => e.target.files?.[0] && loadFile(e.target.files[0], 'left')}
                  className="hidden"
                />
                <Upload className="w-4 h-4" />
              </label>
              <button
                onClick={() => copyText(leftText, 'left')}
                className="p-1 hover:bg-gray-100 rounded"
              >
                {copied === 'left' ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>
          <textarea
            value={leftText}
            onChange={(e) => setLeftText(e.target.value)}
            placeholder="粘贴或输入原始文本..."
            className="w-full h-96 p-4 font-mono text-sm resize-none focus:outline-none"
          />
        </div>

        {/* 右侧文本 */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="border-b border-gray-200 p-3 flex items-center justify-between">
            <h3 className="font-medium flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              对比文本
            </h3>
            <div className="flex items-center gap-2">
              <label className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded cursor-pointer">
                <input
                  type="file"
                  accept=".txt"
                  onChange={(e) => e.target.files?.[0] && loadFile(e.target.files[0], 'right')}
                  className="hidden"
                />
                <Upload className="w-4 h-4" />
              </label>
              <button
                onClick={() => copyText(rightText, 'right')}
                className="p-1 hover:bg-gray-100 rounded"
              >
                {copied === 'right' ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>
          <textarea
            value={rightText}
            onChange={(e) => setRightText(e.target.value)}
            placeholder="粘贴或输入对比文本..."
            className="w-full h-96 p-4 font-mono text-sm resize-none focus:outline-none"
          />
        </div>
      </div>

      {/* 差异显示 */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="border-b border-gray-200 p-3">
          <h3 className="font-medium">差异对比结果</h3>
        </div>
        
        <div className="max-h-96 overflow-y-auto">
          {diffResult.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              请输入文本开始对比
            </div>
          ) : (
            <table className="w-full">
              <tbody>
                {diffResult.map((diff, index) => (
                  <tr key={index} className={`
                    ${diff.type === 'equal' ? 'bg-white' : ''}
                    ${diff.type === 'add' ? 'bg-green-50' : ''}
                    ${diff.type === 'remove' ? 'bg-red-50' : ''}
                    ${diff.type === 'modify' ? 'bg-yellow-50' : ''}
                  `}>
                    {/* 左侧 */}
                    <td className="w-1/2 border-r border-gray-200">
                      <div className="flex">
                        {showLineNumbers && (
                          <div className="px-2 py-1 text-xs text-gray-500 bg-gray-50 border-r border-gray-200 select-none">
                            {diff.leftLineNumber || ' '}
                          </div>
                        )}
                        <div className={`flex-1 px-3 py-1 font-mono text-sm whitespace-pre-wrap ${
                          diff.type === 'remove' ? 'text-red-700 line-through' : ''
                        } ${
                          diff.type === 'modify' ? 'text-yellow-700' : ''
                        }`}>
                          {diff.leftLine !== undefined ? diff.leftLine : ' '}
                        </div>
                      </div>
                    </td>
                    
                    {/* 右侧 */}
                    <td className="w-1/2">
                      <div className="flex">
                        {showLineNumbers && (
                          <div className="px-2 py-1 text-xs text-gray-500 bg-gray-50 border-r border-gray-200 select-none">
                            {diff.rightLineNumber || ' '}
                          </div>
                        )}
                        <div className={`flex-1 px-3 py-1 font-mono text-sm whitespace-pre-wrap ${
                          diff.type === 'add' ? 'text-green-700' : ''
                        } ${
                          diff.type === 'modify' ? 'text-yellow-700' : ''
                        }`}>
                          {diff.rightLine !== undefined ? diff.rightLine : ' '}
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <FileText className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持直接粘贴文本或上传文本文件</li>
              <li>绿色背景表示新增内容，红色表示删除内容，黄色表示修改内容</li>
              <li>可以选择忽略大小写和空白字符进行对比</li>
              <li>支持导出详细的差异报告</li>
              <li>点击交换按钮可以快速交换左右两侧的文本</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
