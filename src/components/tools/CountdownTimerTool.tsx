'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Play, Pause, RotateCcw, Plus, Trash2, Bell, BellOff, Calendar, Clock } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Timer {
  id: string
  name: string
  targetDate: Date
  isActive: boolean
  showSeconds: boolean
  playSound: boolean
  message: string
}

interface TimeLeft {
  days: number
  hours: number
  minutes: number
  seconds: number
  total: number
}

export default function CountdownTimerTool() {
  const [timers, setTimers] = useState<Timer[]>([])
  const [quickMinutes, setQuickMinutes] = useState('')
  const [quickHours, setQuickHours] = useState('')
  const [eventName, setEventName] = useState('')
  const [eventDate, setEventDate] = useState('')
  const [eventTime, setEventTime] = useState('')
  const [showSeconds, setShowSeconds] = useState(true)
  const [playSound, setPlaySound] = useState(true)
  const [message, setMessage] = useState('')
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    // 初始化音频
    audioRef.current = new Audio('/sounds/notification.mp3')
    
    // 从 localStorage 加载计时器
    const savedTimers = localStorage.getItem('countdownTimers')
    if (savedTimers) {
      const parsed = JSON.parse(savedTimers)
      setTimers(parsed.map((timer: any) => ({
        ...timer,
        targetDate: new Date(timer.targetDate)
      })))
    }
  }, [])

  useEffect(() => {
    // 保存计时器到 localStorage
    if (timers.length > 0) {
      localStorage.setItem('countdownTimers', JSON.stringify(timers))
    }
  }, [timers])

  useEffect(() => {
    const interval = setInterval(() => {
      setTimers(prevTimers => {
        const updatedTimers = prevTimers.map(timer => {
          if (!timer.isActive) return timer
          
          const timeLeft = calculateTimeLeft(timer.targetDate)
          
          if (timeLeft.total <= 0 && timer.isActive) {
            // 计时结束
            if (timer.playSound && audioRef.current) {
              audioRef.current.play().catch(e => console.error('播放音频失败:', e))
            }
            
            // 显示通知
            if ('Notification' in window && Notification.permission === 'granted') {
              new Notification(`倒计时结束: ${timer.name}`, {
                body: timer.message || '时间到！',
                icon: '/icons/icon-192x192.png'
              })
            }
            
            toast({
              title: `倒计时结束: ${timer.name}`,
              description: timer.message || '时间到！',
            })
            
            return { ...timer, isActive: false }
          }
          
          return timer
        })
        
        return updatedTimers
      })
    }, 100) // 每100ms更新一次，确保秒数显示准确

    return () => clearInterval(interval)
  }, [toast])

  const calculateTimeLeft = (targetDate: Date): TimeLeft => {
    const difference = targetDate.getTime() - new Date().getTime()
    
    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 }
    }
    
    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60),
      total: difference
    }
  }

  const createQuickTimer = () => {
    const minutes = parseInt(quickMinutes) || 0
    const hours = parseInt(quickHours) || 0
    
    if (minutes === 0 && hours === 0) {
      toast({
        title: '请输入时间',
        description: '请至少输入分钟或小时',
        variant: 'destructive',
      })
      return
    }
    
    const targetDate = new Date()
    targetDate.setHours(targetDate.getHours() + hours)
    targetDate.setMinutes(targetDate.getMinutes() + minutes)
    
    const timer: Timer = {
      id: Date.now().toString(),
      name: `${hours > 0 ? hours + '小时' : ''}${minutes > 0 ? minutes + '分钟' : ''}倒计时`,
      targetDate,
      isActive: true,
      showSeconds,
      playSound,
      message: message || '时间到！'
    }
    
    setTimers([...timers, timer])
    setQuickMinutes('')
    setQuickHours('')
    setMessage('')
    
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }

  const createEventTimer = () => {
    if (!eventName || !eventDate) {
      toast({
        title: '请填写完整信息',
        description: '请输入事件名称和日期',
        variant: 'destructive',
      })
      return
    }
    
    const targetDate = new Date(eventDate)
    if (eventTime) {
      const [hours, minutes] = eventTime.split(':')
      targetDate.setHours(parseInt(hours), parseInt(minutes))
    }
    
    if (targetDate <= new Date()) {
      toast({
        title: '日期无效',
        description: '请选择未来的日期和时间',
        variant: 'destructive',
      })
      return
    }
    
    const timer: Timer = {
      id: Date.now().toString(),
      name: eventName,
      targetDate,
      isActive: true,
      showSeconds,
      playSound,
      message: message || `${eventName} 开始了！`
    }
    
    setTimers([...timers, timer])
    setEventName('')
    setEventDate('')
    setEventTime('')
    setMessage('')
    
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }

  const toggleTimer = (id: string) => {
    setTimers(timers.map(timer => 
      timer.id === id ? { ...timer, isActive: !timer.isActive } : timer
    ))
  }

  const deleteTimer = (id: string) => {
    setTimers(timers.filter(timer => timer.id !== id))
  }

  const formatTimeLeft = (timer: Timer) => {
    const timeLeft = calculateTimeLeft(timer.targetDate)
    
    if (timeLeft.total <= 0) {
      return '已结束'
    }
    
    const parts = []
    
    if (timeLeft.days > 0) {
      parts.push(`${timeLeft.days}天`)
    }
    if (timeLeft.hours > 0 || timeLeft.days > 0) {
      parts.push(`${timeLeft.hours}小时`)
    }
    if (timeLeft.minutes > 0 || timeLeft.hours > 0 || timeLeft.days > 0) {
      parts.push(`${timeLeft.minutes}分`)
    }
    if (timer.showSeconds) {
      parts.push(`${timeLeft.seconds}秒`)
    }
    
    return parts.join(' ')
  }

  const presetTimers = [
    { label: '1分钟', minutes: 1 },
    { label: '5分钟', minutes: 5 },
    { label: '10分钟', minutes: 10 },
    { label: '15分钟', minutes: 15 },
    { label: '30分钟', minutes: 30 },
    { label: '1小时', hours: 1 },
    { label: '2小时', hours: 2 },
    { label: '3小时', hours: 3 },
  ]

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-6">倒计时器</h2>
        
        <Tabs defaultValue="quick" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="quick">快速倒计时</TabsTrigger>
            <TabsTrigger value="event">事件倒计时</TabsTrigger>
          </TabsList>
          
          <TabsContent value="quick" className="space-y-4">
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
              {presetTimers.map((preset) => (
                <Button
                  key={preset.label}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setQuickHours((preset.hours || 0).toString())
                    setQuickMinutes((preset.minutes || 0).toString())
                  }}
                >
                  {preset.label}
                </Button>
              ))}
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="hours">小时</Label>
                <Input
                  id="hours"
                  type="number"
                  min="0"
                  max="23"
                  value={quickHours}
                  onChange={(e) => setQuickHours(e.target.value)}
                  placeholder="0"
                />
              </div>
              <div>
                <Label htmlFor="minutes">分钟</Label>
                <Input
                  id="minutes"
                  type="number"
                  min="0"
                  max="59"
                  value={quickMinutes}
                  onChange={(e) => setQuickMinutes(e.target.value)}
                  placeholder="0"
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="quick-message">提醒消息（可选）</Label>
                <Input
                  id="quick-message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="时间到！"
                />
              </div>
              
              <div className="flex items-center gap-4">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showSeconds}
                    onChange={(e) => setShowSeconds(e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">显示秒数</span>
                </label>
                
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={playSound}
                    onChange={(e) => setPlaySound(e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">播放提示音</span>
                </label>
              </div>
              
              <Button onClick={createQuickTimer} className="w-full">
                <Plus className="w-4 h-4 mr-2" />
                创建倒计时
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="event" className="space-y-4">
            <div>
              <Label htmlFor="event-name">事件名称</Label>
              <Input
                id="event-name"
                value={eventName}
                onChange={(e) => setEventName(e.target.value)}
                placeholder="例如：生日、会议、考试"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="event-date">日期</Label>
                <Input
                  id="event-date"
                  type="date"
                  value={eventDate}
                  onChange={(e) => setEventDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              <div>
                <Label htmlFor="event-time">时间（可选）</Label>
                <Input
                  id="event-time"
                  type="time"
                  value={eventTime}
                  onChange={(e) => setEventTime(e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="event-message">提醒消息（可选）</Label>
              <Input
                id="event-message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="事件开始了！"
              />
            </div>
            
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={showSeconds}
                  onChange={(e) => setShowSeconds(e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">显示秒数</span>
              </label>
              
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={playSound}
                  onChange={(e) => setPlaySound(e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">播放提示音</span>
              </label>
            </div>
            
            <Button onClick={createEventTimer} className="w-full">
              <Calendar className="w-4 h-4 mr-2" />
              创建事件倒计时
            </Button>
          </TabsContent>
        </Tabs>
      </Card>
      
      {timers.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">活动倒计时</h3>
          <div className="space-y-4">
            {timers.map((timer) => {
              const timeLeft = calculateTimeLeft(timer.targetDate)
              const isExpired = timeLeft.total <= 0
              
              return (
                <div
                  key={timer.id}
                  className={`p-4 rounded-lg border ${
                    isExpired
                      ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                      : timer.isActive
                      ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                      : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-semibold text-lg">{timer.name}</h4>
                      <p className="text-2xl font-mono mt-2">
                        {formatTimeLeft(timer)}
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">
                        目标时间: {timer.targetDate.toLocaleString('zh-CN')}
                      </p>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {!isExpired && (
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => toggleTimer(timer.id)}
                        >
                          {timer.isActive ? (
                            <Pause className="w-4 h-4" />
                          ) : (
                            <Play className="w-4 h-4" />
                          )}
                        </Button>
                      )}
                      
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => deleteTimer(timer.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {timer.message && (
                    <p className="text-sm text-muted-foreground mt-2">
                      提醒: {timer.message}
                    </p>
                  )}
                  
                  <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {timer.showSeconds ? '显示秒数' : '不显示秒数'}
                    </span>
                    <span className="flex items-center gap-1">
                      {timer.playSound ? (
                        <Bell className="w-3 h-3" />
                      ) : (
                        <BellOff className="w-3 h-3" />
                      )}
                      {timer.playSound ? '播放提示音' : '静音'}
                    </span>
                  </div>
                </div>
              )
            })}
          </div>
        </Card>
      )}
      
      {timers.length === 0 && (
        <Card className="p-12 text-center">
          <Clock className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">
            还没有倒计时器，创建一个吧！
          </p>
        </Card>
      )}
    </div>
  )
}
