'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Clock, Calendar, Plus, Minus, Copy, RotateCcw } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

interface TimeResult {
  years: number
  months: number
  days: number
  hours: number
  minutes: number
  seconds: number
  totalDays: number
  totalHours: number
  totalMinutes: number
  totalSeconds: number
}

interface WorkdayResult {
  workdays: number
  weekends: number
  totalDays: number
}

export default function TimeCalculatorTool() {
  const [activeTab, setActiveTab] = useState('interval')
  
  // 时间间隔计算
  const [startDate, setStartDate] = useState('')
  const [startTime, setStartTime] = useState('')
  const [endDate, setEndDate] = useState('')
  const [endTime, setEndTime] = useState('')
  const [intervalResult, setIntervalResult] = useState<TimeResult | null>(null)
  
  // 时间加减计算
  const [baseDate, setBaseDate] = useState('')
  const [baseTime, setBaseTime] = useState('')
  const [operation, setOperation] = useState<'add' | 'subtract'>('add')
  const [addYears, setAddYears] = useState('')
  const [addMonths, setAddMonths] = useState('')
  const [addDays, setAddDays] = useState('')
  const [addHours, setAddHours] = useState('')
  const [addMinutes, setAddMinutes] = useState('')
  const [addSeconds, setAddSeconds] = useState('')
  const [arithmeticResult, setArithmeticResult] = useState<Date | null>(null)
  
  // 工作日计算
  const [workStartDate, setWorkStartDate] = useState('')
  const [workEndDate, setWorkEndDate] = useState('')
  const [workdayResult, setWorkdayResult] = useState<WorkdayResult | null>(null)
  
  // 时区转换
  const [sourceTime, setSourceTime] = useState('')
  const [sourceTimezone, setSourceTimezone] = useState('UTC')
  const [targetTimezone, setTargetTimezone] = useState('Asia/Shanghai')
  const [timezoneResult, setTimezoneResult] = useState<string>('')

  const timezones = [
    { value: 'UTC', label: 'UTC (协调世界时)' },
    { value: 'Asia/Shanghai', label: 'Asia/Shanghai (北京时间)' },
    { value: 'America/New_York', label: 'America/New_York (纽约时间)' },
    { value: 'America/Los_Angeles', label: 'America/Los_Angeles (洛杉矶时间)' },
    { value: 'Europe/London', label: 'Europe/London (伦敦时间)' },
    { value: 'Europe/Paris', label: 'Europe/Paris (巴黎时间)' },
    { value: 'Asia/Tokyo', label: 'Asia/Tokyo (东京时间)' },
    { value: 'Australia/Sydney', label: 'Australia/Sydney (悉尼时间)' },
  ]

  // 设置当前时间
  const setCurrentTime = (type: 'start' | 'end' | 'base' | 'workStart' | 'workEnd' | 'source') => {
    const now = new Date()
    const dateStr = now.toISOString().split('T')[0]
    const timeStr = now.toTimeString().split(' ')[0].substring(0, 5)
    
    switch (type) {
      case 'start':
        setStartDate(dateStr)
        setStartTime(timeStr)
        break
      case 'end':
        setEndDate(dateStr)
        setEndTime(timeStr)
        break
      case 'base':
        setBaseDate(dateStr)
        setBaseTime(timeStr)
        break
      case 'workStart':
        setWorkStartDate(dateStr)
        break
      case 'workEnd':
        setWorkEndDate(dateStr)
        break
      case 'source':
        setSourceTime(now.toISOString().substring(0, 16))
        break
    }
  }

  // 计算时间间隔
  const calculateInterval = () => {
    if (!startDate || !endDate) {
      toast({
        title: "输入错误",
        description: "请选择开始和结束日期",
        variant: "destructive"
      })
      return
    }

    const start = new Date(`${startDate}T${startTime || '00:00'}:00`)
    const end = new Date(`${endDate}T${endTime || '00:00'}:00`)

    if (start >= end) {
      toast({
        title: "日期错误",
        description: "结束时间必须晚于开始时间",
        variant: "destructive"
      })
      return
    }

    const diffMs = end.getTime() - start.getTime()
    const totalSeconds = Math.floor(diffMs / 1000)
    const totalMinutes = Math.floor(totalSeconds / 60)
    const totalHours = Math.floor(totalMinutes / 60)
    const totalDays = Math.floor(totalHours / 24)

    const years = Math.floor(totalDays / 365)
    const months = Math.floor((totalDays % 365) / 30)
    const days = totalDays % 30
    const hours = totalHours % 24
    const minutes = totalMinutes % 60
    const seconds = totalSeconds % 60

    setIntervalResult({
      years,
      months,
      days,
      hours,
      minutes,
      seconds,
      totalDays,
      totalHours,
      totalMinutes,
      totalSeconds
    })
  }

  // 时间加减计算
  const calculateArithmetic = () => {
    if (!baseDate) {
      toast({
        title: "输入错误",
        description: "请选择基准日期",
        variant: "destructive"
      })
      return
    }

    const base = new Date(`${baseDate}T${baseTime || '00:00'}:00`)
    const result = new Date(base)

    const years = parseInt(addYears) || 0
    const months = parseInt(addMonths) || 0
    const days = parseInt(addDays) || 0
    const hours = parseInt(addHours) || 0
    const minutes = parseInt(addMinutes) || 0
    const seconds = parseInt(addSeconds) || 0

    if (operation === 'add') {
      result.setFullYear(result.getFullYear() + years)
      result.setMonth(result.getMonth() + months)
      result.setDate(result.getDate() + days)
      result.setHours(result.getHours() + hours)
      result.setMinutes(result.getMinutes() + minutes)
      result.setSeconds(result.getSeconds() + seconds)
    } else {
      result.setFullYear(result.getFullYear() - years)
      result.setMonth(result.getMonth() - months)
      result.setDate(result.getDate() - days)
      result.setHours(result.getHours() - hours)
      result.setMinutes(result.getMinutes() - minutes)
      result.setSeconds(result.getSeconds() - seconds)
    }

    setArithmeticResult(result)
  }

  // 计算工作日
  const calculateWorkdays = () => {
    if (!workStartDate || !workEndDate) {
      toast({
        title: "输入错误",
        description: "请选择开始和结束日期",
        variant: "destructive"
      })
      return
    }

    const start = new Date(workStartDate)
    const end = new Date(workEndDate)

    if (start >= end) {
      toast({
        title: "日期错误",
        description: "结束日期必须晚于开始日期",
        variant: "destructive"
      })
      return
    }

    let workdays = 0
    let weekends = 0
    const current = new Date(start)

    while (current <= end) {
      const dayOfWeek = current.getDay()
      if (dayOfWeek === 0 || dayOfWeek === 6) { // 周日或周六
        weekends++
      } else {
        workdays++
      }
      current.setDate(current.getDate() + 1)
    }

    const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1

    setWorkdayResult({
      workdays,
      weekends,
      totalDays
    })
  }

  // 时区转换
  const convertTimezone = () => {
    if (!sourceTime) {
      toast({
        title: "输入错误",
        description: "请选择源时间",
        variant: "destructive"
      })
      return
    }

    try {
      const source = new Date(sourceTime)
      
      // 使用Intl.DateTimeFormat进行时区转换
      const formatter = new Intl.DateTimeFormat('zh-CN', {
        timeZone: targetTimezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })

      const converted = formatter.format(source)
      setTimezoneResult(converted)
    } catch (error) {
      toast({
        title: "转换错误",
        description: "时区转换失败，请检查输入",
        variant: "destructive"
      })
    }
  }

  // 复制结果
  const copyResult = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "已复制",
      description: "结果已复制到剪贴板",
    })
  }

  // 清空所有输入
  const clearAll = () => {
    setStartDate('')
    setStartTime('')
    setEndDate('')
    setEndTime('')
    setBaseDate('')
    setBaseTime('')
    setAddYears('')
    setAddMonths('')
    setAddDays('')
    setAddHours('')
    setAddMinutes('')
    setAddSeconds('')
    setWorkStartDate('')
    setWorkEndDate('')
    setSourceTime('')
    setIntervalResult(null)
    setArithmeticResult(null)
    setWorkdayResult(null)
    setTimezoneResult('')
  }

  // 格式化时间间隔结果
  const formatIntervalResult = (result: TimeResult) => {
    const parts = []
    if (result.years > 0) parts.push(`${result.years}年`)
    if (result.months > 0) parts.push(`${result.months}个月`)
    if (result.days > 0) parts.push(`${result.days}天`)
    if (result.hours > 0) parts.push(`${result.hours}小时`)
    if (result.minutes > 0) parts.push(`${result.minutes}分钟`)
    if (result.seconds > 0) parts.push(`${result.seconds}秒`)
    
    return parts.length > 0 ? parts.join(' ') : '0秒'
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className="h-6 w-6" />
              <CardTitle>时间计算器</CardTitle>
            </div>
            <Button variant="outline" size="sm" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-1" />
              清空
            </Button>
          </div>
          <CardDescription>
            多功能时间计算工具，支持时间间隔、时间加减、工作日和时区转换
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="interval">时间间隔</TabsTrigger>
              <TabsTrigger value="arithmetic">时间加减</TabsTrigger>
              <TabsTrigger value="workday">工作日计算</TabsTrigger>
              <TabsTrigger value="timezone">时区转换</TabsTrigger>
            </TabsList>

            <TabsContent value="interval" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="startDate">开始日期</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="startDate"
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentTime('start')}
                      >
                        现在
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="startTime">开始时间（可选）</Label>
                    <Input
                      id="startTime"
                      type="time"
                      value={startTime}
                      onChange={(e) => setStartTime(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="endDate">结束日期</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="endDate"
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentTime('end')}
                      >
                        现在
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="endTime">结束时间（可选）</Label>
                    <Input
                      id="endTime"
                      type="time"
                      value={endTime}
                      onChange={(e) => setEndTime(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <Button onClick={calculateInterval} className="w-full">
                <Clock className="h-4 w-4 mr-2" />
                计算时间间隔
              </Button>

              {intervalResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">时间间隔结果</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{intervalResult.totalDays}</div>
                        <div className="text-sm text-gray-600">总天数</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{intervalResult.totalHours}</div>
                        <div className="text-sm text-gray-600">总小时数</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">{intervalResult.totalMinutes}</div>
                        <div className="text-sm text-gray-600">总分钟数</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{intervalResult.totalSeconds}</div>
                        <div className="text-sm text-gray-600">总秒数</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <span className="font-medium">详细间隔: </span>
                        <span>{formatIntervalResult(intervalResult)}</span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResult(formatIntervalResult(intervalResult))}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="arithmetic" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="baseDate">基准日期</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="baseDate"
                        type="date"
                        value={baseDate}
                        onChange={(e) => setBaseDate(e.target.value)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentTime('base')}
                      >
                        现在
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="baseTime">基准时间（可选）</Label>
                    <Input
                      id="baseTime"
                      type="time"
                      value={baseTime}
                      onChange={(e) => setBaseTime(e.target.value)}
                    />
                  </div>

                  <div>
                    <Label>操作类型</Label>
                    <Select value={operation} onValueChange={(value: 'add' | 'subtract') => setOperation(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="add">
                          <div className="flex items-center">
                            <Plus className="h-4 w-4 mr-2" />
                            加上
                          </div>
                        </SelectItem>
                        <SelectItem value="subtract">
                          <div className="flex items-center">
                            <Minus className="h-4 w-4 mr-2" />
                            减去
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="addYears">年</Label>
                      <Input
                        id="addYears"
                        type="number"
                        value={addYears}
                        onChange={(e) => setAddYears(e.target.value)}
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <Label htmlFor="addMonths">月</Label>
                      <Input
                        id="addMonths"
                        type="number"
                        value={addMonths}
                        onChange={(e) => setAddMonths(e.target.value)}
                        placeholder="0"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="addDays">天</Label>
                      <Input
                        id="addDays"
                        type="number"
                        value={addDays}
                        onChange={(e) => setAddDays(e.target.value)}
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <Label htmlFor="addHours">小时</Label>
                      <Input
                        id="addHours"
                        type="number"
                        value={addHours}
                        onChange={(e) => setAddHours(e.target.value)}
                        placeholder="0"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="addMinutes">分钟</Label>
                      <Input
                        id="addMinutes"
                        type="number"
                        value={addMinutes}
                        onChange={(e) => setAddMinutes(e.target.value)}
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <Label htmlFor="addSeconds">秒</Label>
                      <Input
                        id="addSeconds"
                        type="number"
                        value={addSeconds}
                        onChange={(e) => setAddSeconds(e.target.value)}
                        placeholder="0"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <Button onClick={calculateArithmetic} className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                计算结果时间
              </Button>

              {arithmeticResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">计算结果</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <div className="text-lg font-semibold">
                          {arithmeticResult.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: false
                          })}
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          星期{['日', '一', '二', '三', '四', '五', '六'][arithmeticResult.getDay()]}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResult(arithmeticResult.toISOString())}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="workday" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="workStartDate">开始日期</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="workStartDate"
                        type="date"
                        value={workStartDate}
                        onChange={(e) => setWorkStartDate(e.target.value)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentTime('workStart')}
                      >
                        今天
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="workEndDate">结束日期</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="workEndDate"
                        type="date"
                        value={workEndDate}
                        onChange={(e) => setWorkEndDate(e.target.value)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentTime('workEnd')}
                      >
                        今天
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <Button onClick={calculateWorkdays} className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                计算工作日
              </Button>

              {workdayResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">工作日统计</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{workdayResult.workdays}</div>
                        <div className="text-sm text-gray-600">工作日</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">{workdayResult.weekends}</div>
                        <div className="text-sm text-gray-600">周末</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">{workdayResult.totalDays}</div>
                        <div className="text-sm text-gray-600">总天数</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="timezone" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="sourceTime">源时间</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="sourceTime"
                        type="datetime-local"
                        value={sourceTime}
                        onChange={(e) => setSourceTime(e.target.value)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentTime('source')}
                      >
                        现在
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label>源时区</Label>
                    <Select value={sourceTimezone} onValueChange={setSourceTimezone}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {timezones.map((timezone) => (
                          <SelectItem key={timezone.value} value={timezone.value}>
                            {timezone.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label>目标时区</Label>
                    <Select value={targetTimezone} onValueChange={setTargetTimezone}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {timezones.map((timezone) => (
                          <SelectItem key={timezone.value} value={timezone.value}>
                            {timezone.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Button onClick={convertTimezone} className="w-full">
                <Clock className="h-4 w-4 mr-2" />
                转换时区
              </Button>

              {timezoneResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">时区转换结果</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <div className="text-lg font-semibold">{timezoneResult}</div>
                        <div className="text-sm text-gray-600 mt-1">
                          {timezones.find(tz => tz.value === targetTimezone)?.label || targetTimezone}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResult(timezoneResult)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
