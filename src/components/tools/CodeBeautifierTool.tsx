'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Copy, Download, FileCode, Trash2, Sparkles } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

// 简单的代码美化函数
const beautifyJS = (code: string, indent: string = '  '): string => {
  try {
    // 移除多余的空格和换行
    let formatted = code.trim()
    
    // 处理分号
    formatted = formatted.replace(/;\s*}/g, ';\n}')
    formatted = formatted.replace(/;\s*([^;\s])/g, ';\n$1')
    
    // 处理大括号
    formatted = formatted.replace(/\{/g, ' {\n')
    formatted = formatted.replace(/\}/g, '\n}')
    
    // 处理逗号
    formatted = formatted.replace(/,\s*/g, ', ')
    
    // 处理操作符
    formatted = formatted.replace(/\s*=\s*/g, ' = ')
    formatted = formatted.replace(/\s*\+\s*/g, ' + ')
    formatted = formatted.replace(/\s*-\s*/g, ' - ')
    formatted = formatted.replace(/\s*\*\s*/g, ' * ')
    formatted = formatted.replace(/\s*\/\s*/g, ' / ')
    
    // 处理缩进
    let indentLevel = 0
    const lines = formatted.split('\n')
    const formattedLines = lines.map(line => {
      const trimmedLine = line.trim()
      
      if (trimmedLine.endsWith('{')) {
        const result = indent.repeat(indentLevel) + trimmedLine
        indentLevel++
        return result
      } else if (trimmedLine.startsWith('}')) {
        indentLevel = Math.max(0, indentLevel - 1)
        return indent.repeat(indentLevel) + trimmedLine
      } else {
        return indent.repeat(indentLevel) + trimmedLine
      }
    })
    
    return formattedLines.join('\n')
  } catch (error) {
    throw new Error('JavaScript 格式化失败')
  }
}

const beautifyCSS = (code: string, indent: string = '  '): string => {
  try {
    let formatted = code.trim()
    
    // 处理大括号
    formatted = formatted.replace(/\s*{\s*/g, ' {\n')
    formatted = formatted.replace(/\s*}\s*/g, '\n}\n')
    
    // 处理分号
    formatted = formatted.replace(/;\s*/g, ';\n')
    
    // 处理冒号
    formatted = formatted.replace(/:\s*/g, ': ')
    
    // 处理逗号（选择器）
    formatted = formatted.replace(/,\s*/g, ',\n')
    
    // 处理缩进
    let indentLevel = 0
    const lines = formatted.split('\n')
    const formattedLines = lines.map(line => {
      const trimmedLine = line.trim()
      
      if (trimmedLine.endsWith('{')) {
        const result = indent.repeat(indentLevel) + trimmedLine
        indentLevel++
        return result
      } else if (trimmedLine.startsWith('}')) {
        indentLevel = Math.max(0, indentLevel - 1)
        return indent.repeat(indentLevel) + trimmedLine
      } else if (trimmedLine) {
        return indent.repeat(indentLevel) + trimmedLine
      }
      return ''
    })
    
    return formattedLines.filter(line => line).join('\n')
  } catch (error) {
    throw new Error('CSS 格式化失败')
  }
}

const beautifyHTML = (code: string, indent: string = '  '): string => {
  try {
    let formatted = code.trim()
    
    // 处理标签
    formatted = formatted.replace(/>\s*</g, '>\n<')
    formatted = formatted.replace(/(<[^>]+>)/g, (match) => {
      // 自闭合标签
      if (match.endsWith('/>') || match.includes('<br') || match.includes('<img') || 
          match.includes('<input') || match.includes('<meta') || match.includes('<link')) {
        return match
      }
      return match
    })
    
    // 处理缩进
    let indentLevel = 0
    const lines = formatted.split('\n')
    const formattedLines = lines.map(line => {
      const trimmedLine = line.trim()
      
      if (trimmedLine.startsWith('</')) {
        indentLevel = Math.max(0, indentLevel - 1)
        return indent.repeat(indentLevel) + trimmedLine
      } else if (trimmedLine.startsWith('<') && !trimmedLine.startsWith('<!')) {
        const result = indent.repeat(indentLevel) + trimmedLine
        if (!trimmedLine.endsWith('/>') && !trimmedLine.includes('</')) {
          indentLevel++
        }
        return result
      } else {
        return indent.repeat(indentLevel) + trimmedLine
      }
    })
    
    return formattedLines.join('\n')
  } catch (error) {
    throw new Error('HTML 格式化失败')
  }
}

const minifyJS = (code: string): string => {
  return code
    .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
    .replace(/\/\/.*$/gm, '') // 移除单行注释
    .replace(/\s+/g, ' ') // 压缩空白
    .replace(/\s*([{}();,:])\s*/g, '$1') // 移除符号周围的空格
    .trim()
}

const minifyCSS = (code: string): string => {
  return code
    .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
    .replace(/\s+/g, ' ') // 压缩空白
    .replace(/\s*([{}:;,])\s*/g, '$1') // 移除符号周围的空格
    .replace(/;\}/g, '}') // 移除最后一个分号
    .trim()
}

const minifyHTML = (code: string): string => {
  return code
    .replace(/<!--[\s\S]*?-->/g, '') // 移除注释
    .replace(/\s+/g, ' ') // 压缩空白
    .replace(/>\s+</g, '><') // 移除标签间的空白
    .trim()
}

export default function CodeBeautifierTool() {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [codeType, setCodeType] = useState<'javascript' | 'css' | 'html'>('javascript')
  const [indentType, setIndentType] = useState<'2spaces' | '4spaces' | 'tab'>('2spaces')
  const [minify, setMinify] = useState(false)
  const { toast } = useToast()

  const getIndentString = useCallback(() => {
    switch (indentType) {
      case '2spaces':
        return '  '
      case '4spaces':
        return '    '
      case 'tab':
        return '\t'
      default:
        return '  '
    }
  }, [indentType])

  const handleBeautify = useCallback((showToast: boolean = true) => {
    if (!input.trim()) {
      if (showToast) {
        toast({
          title: '请输入代码',
          description: '请在输入框中粘贴或输入要格式化的代码',
          variant: 'destructive',
        })
      }
      setOutput('')
      return
    }

    try {
      let result = ''
      const indent = getIndentString()

      if (minify) {
        // 压缩代码
        switch (codeType) {
          case 'javascript':
            result = minifyJS(input)
            break
          case 'css':
            result = minifyCSS(input)
            break
          case 'html':
            result = minifyHTML(input)
            break
        }
      } else {
        // 美化代码
        switch (codeType) {
          case 'javascript':
            result = beautifyJS(input, indent)
            break
          case 'css':
            result = beautifyCSS(input, indent)
            break
          case 'html':
            result = beautifyHTML(input, indent)
            break
        }
      }

      setOutput(result)
      if (showToast) {
        toast({
          title: '格式化成功',
          description: `${codeType.toUpperCase()} 代码已${minify ? '压缩' : '美化'}`,
        })
      }
    } catch (error) {
      setOutput('')
      if (showToast) {
        toast({
          title: '格式化失败',
          description: error instanceof Error ? error.message : '请检查代码格式是否正确',
          variant: 'destructive',
        })
      }
    }
  }, [input, codeType, minify, toast, getIndentString])

  // 实时格式化
  useEffect(() => {
    handleBeautify(false)
  }, [input, codeType, indentType, minify, handleBeautify])

  const handleCopy = useCallback(() => {
    if (!output) {
      toast({
        title: '没有可复制的内容',
        description: '请先格式化代码',
        variant: 'destructive',
      })
      return
    }

    navigator.clipboard.writeText(output).then(() => {
      toast({
        title: '复制成功',
        description: '格式化后的代码已复制到剪贴板',
      })
    }).catch(() => {
      toast({
        title: '复制失败',
        description: '请手动选择并复制代码',
        variant: 'destructive',
      })
    })
  }, [output, toast])

  const handleDownload = useCallback(() => {
    if (!output) {
      toast({
        title: '没有可下载的内容',
        description: '请先格式化代码',
        variant: 'destructive',
      })
      return
    }

    const extension = codeType === 'javascript' ? 'js' : codeType
    const filename = `formatted-code.${extension}`
    const blob = new Blob([output], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: '下载成功',
      description: `文件已保存为 ${filename}`,
    })
  }, [output, codeType, toast])

  const handleClear = useCallback(() => {
    setInput('')
    setOutput('')
    toast({
      title: '已清空',
      description: '输入和输出已清空',
    })
  }, [toast])

  const handlePaste = useCallback(async () => {
    try {
      const text = await navigator.clipboard.readText()
      setInput(text)
      toast({
        title: '粘贴成功',
        description: '代码已粘贴到输入框',
      })
    } catch (error) {
      toast({
        title: '粘贴失败',
        description: '无法访问剪贴板',
        variant: 'destructive',
      })
    }
  }, [toast])

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileCode className="h-5 w-5" />
            代码美化工具
          </CardTitle>
          <CardDescription>
            格式化和美化 JavaScript、CSS、HTML 代码，支持自定义缩进和压缩
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 设置区域 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>代码类型</Label>
              <Select value={codeType} onValueChange={(value: any) => setCodeType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="javascript">JavaScript</SelectItem>
                  <SelectItem value="css">CSS</SelectItem>
                  <SelectItem value="html">HTML</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>缩进类型</Label>
              <Select 
                value={indentType} 
                onValueChange={(value: any) => setIndentType(value)}
                disabled={minify}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2spaces">2 个空格</SelectItem>
                  <SelectItem value="4spaces">4 个空格</SelectItem>
                  <SelectItem value="tab">Tab 制表符</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>压缩模式</Label>
              <div className="flex items-center space-x-2 pt-2">
                <Switch
                  id="minify"
                  checked={minify}
                  onCheckedChange={setMinify}
                />
                <Label htmlFor="minify" className="cursor-pointer">
                  {minify ? '压缩代码' : '美化代码'}
                </Label>
              </div>
            </div>
          </div>

          {/* 编辑区域 */}
          <Tabs defaultValue="input" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="input">输入代码</TabsTrigger>
              <TabsTrigger value="output">格式化结果</TabsTrigger>
            </TabsList>

            <TabsContent value="input" className="space-y-4">
              <div className="flex justify-between items-center">
                <Label>输入代码</Label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePaste}
                  >
                    粘贴
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClear}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <Textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder={`在此粘贴或输入 ${codeType.toUpperCase()} 代码...`}
                className="min-h-[400px] font-mono text-sm"
              />
            </TabsContent>

            <TabsContent value="output" className="space-y-4">
              <div className="flex justify-between items-center">
                <Label>格式化结果</Label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopy}
                    disabled={!output}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    复制
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                    disabled={!output}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    下载
                  </Button>
                </div>
              </div>
              <Textarea
                value={output}
                readOnly
                placeholder="格式化后的代码将显示在这里..."
                className="min-h-[400px] font-mono text-sm"
              />
            </TabsContent>
          </Tabs>

          {/* 操作按钮 */}
          <div className="flex justify-center">
            <Button
              size="lg"
              onClick={() => handleBeautify(true)}
              disabled={!input.trim()}
              className="min-w-[200px]"
            >
              <Sparkles className="h-5 w-5 mr-2" />
              {minify ? '压缩代码' : '美化代码'}
            </Button>
          </div>

          {/* 提示信息 */}
          <Card className="bg-muted/50">
            <CardContent className="pt-6">
              <h4 className="font-semibold mb-2">使用提示：</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>支持 JavaScript、CSS 和 HTML 代码的格式化</li>
                <li>可以选择使用空格或 Tab 作为缩进</li>
                <li>压缩模式会移除所有不必要的空白和注释</li>
                <li>格式化结果可以直接复制或下载为文件</li>
                <li>建议在格式化前备份重要代码</li>
              </ul>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}
