'use client'

import React, { useState, useCallback } from 'react'
import { FileText, Copy, Check, Download, Upload, Play, Save, Trash2, Plus, Code, List } from 'lucide-react'

interface Variable {
  name: string
  value: string
  description?: string
}

interface Template {
  id: string
  name: string
  content: string
  variables: Variable[]
  createdAt: Date
}

const defaultTemplates: Template[] = [
  {
    id: 'email-1',
    name: '商务邮件模板',
    content: `尊敬的 {{客户名称}}：

您好！

感谢您对我们 {{产品名称}} 的关注。根据您的需求，我们为您准备了以下方案：

{{方案内容}}

如有任何问题，请随时联系我们。

此致
敬礼

{{发件人姓名}}
{{公司名称}}
{{日期}}`,
    variables: [
      { name: '客户名称', value: '张先生', description: '收件人称呼' },
      { name: '产品名称', value: '企业管理系统', description: '产品或服务名称' },
      { name: '方案内容', value: '详细方案内容...', description: '具体方案描述' },
      { name: '发件人姓名', value: '李经理', description: '发件人姓名' },
      { name: '公司名称', value: 'XX科技有限公司', description: '公司名称' },
      { name: '日期', value: new Date().toLocaleDateString('zh-CN'), description: '发送日期' }
    ],
    createdAt: new Date()
  },
  {
    id: 'code-1',
    name: '代码注释模板',
    content: `/**
 * @description {{功能描述}}
 * <AUTHOR>
 * @date {{日期}}
 * @param {{{参数类型}}} {{参数名}} - {{参数描述}}
 * @returns {{{返回类型}}} {{返回值描述}}
 * @example
 * {{示例代码}}
 */`,
    variables: [
      { name: '功能描述', value: '计算两个数的和', description: '函数功能说明' },
      { name: '作者', value: 'Developer', description: '代码作者' },
      { name: '日期', value: new Date().toISOString().split('T')[0], description: '创建日期' },
      { name: '参数类型', value: 'number', description: '参数数据类型' },
      { name: '参数名', value: 'a, b', description: '参数名称' },
      { name: '参数描述', value: '要相加的数字', description: '参数说明' },
      { name: '返回类型', value: 'number', description: '返回值类型' },
      { name: '返回值描述', value: '两数之和', description: '返回值说明' },
      { name: '示例代码', value: 'add(1, 2) // 3', description: '使用示例' }
    ],
    createdAt: new Date()
  },
  {
    id: 'sql-1',
    name: 'SQL 查询模板',
    content: `-- {{查询描述}}
-- 作者: {{作者}}
-- 日期: {{日期}}

SELECT 
    {{字段列表}}
FROM 
    {{表名}}
WHERE 
    {{条件}}
ORDER BY 
    {{排序字段}} {{排序方式}}
LIMIT {{限制数量}};`,
    variables: [
      { name: '查询描述', value: '查询用户订单信息', description: '查询目的说明' },
      { name: '作者', value: 'DBA', description: '查询作者' },
      { name: '日期', value: new Date().toISOString().split('T')[0], description: '创建日期' },
      { name: '字段列表', value: 'id, name, email, created_at', description: '要查询的字段' },
      { name: '表名', value: 'users', description: '数据表名称' },
      { name: '条件', value: 'status = "active"', description: 'WHERE 条件' },
      { name: '排序字段', value: 'created_at', description: '排序依据字段' },
      { name: '排序方式', value: 'DESC', description: 'ASC 或 DESC' },
      { name: '限制数量', value: '10', description: '返回记录数' }
    ],
    createdAt: new Date()
  }
]

export default function TextTemplateTool() {
  const [templates, setTemplates] = useState<Template[]>(() => {
    // 从 localStorage 加载模板
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('text-templates')
      if (saved) {
        try {
          const parsed = JSON.parse(saved)
          return parsed.map((t: any) => ({
            ...t,
            createdAt: new Date(t.createdAt)
          }))
        } catch {
          return defaultTemplates
        }
      }
    }
    return defaultTemplates
  })
  
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(templates[0] || null)
  const [editingTemplate, setEditingTemplate] = useState(false)
  const [previewText, setPreviewText] = useState('')
  const [copied, setCopied] = useState(false)
  
  // 保存模板到 localStorage
  const saveTemplates = useCallback((newTemplates: Template[]) => {
    setTemplates(newTemplates)
    if (typeof window !== 'undefined') {
      localStorage.setItem('text-templates', JSON.stringify(newTemplates))
    }
  }, [])
  
  // 生成预览
  const generatePreview = useCallback(() => {
    if (!selectedTemplate) return ''
    
    let result = selectedTemplate.content
    selectedTemplate.variables.forEach(variable => {
      const regex = new RegExp(`{{\\s*${variable.name}\\s*}}`, 'g')
      result = result.replace(regex, variable.value)
    })
    
    return result
  }, [selectedTemplate])
  
  // 更新预览
  React.useEffect(() => {
    setPreviewText(generatePreview())
  }, [generatePreview])
  
  // 创建新模板
  const createNewTemplate = () => {
    const newTemplate: Template = {
      id: `template-${Date.now()}`,
      name: '新模板',
      content: '在这里输入模板内容，使用 {{变量名}} 来定义变量',
      variables: [],
      createdAt: new Date()
    }
    
    const newTemplates = [...templates, newTemplate]
    saveTemplates(newTemplates)
    setSelectedTemplate(newTemplate)
    setEditingTemplate(true)
  }
  
  // 更新模板
  const updateTemplate = (updates: Partial<Template>) => {
    if (!selectedTemplate) return
    
    const updatedTemplate = { ...selectedTemplate, ...updates }
    const newTemplates = templates.map(t => 
      t.id === selectedTemplate.id ? updatedTemplate : t
    )
    
    saveTemplates(newTemplates)
    setSelectedTemplate(updatedTemplate)
  }
  
  // 删除模板
  const deleteTemplate = (id: string) => {
    const newTemplates = templates.filter(t => t.id !== id)
    saveTemplates(newTemplates)
    
    if (selectedTemplate?.id === id) {
      setSelectedTemplate(newTemplates[0] || null)
    }
  }
  
  // 提取变量
  const extractVariables = (content: string): Variable[] => {
    const regex = /\{\{\s*([^}]+)\s*\}\}/g
    const matches = content.match(regex) || []
    const uniqueNames = new Set<string>()
    
    matches.forEach(match => {
      const name = match.replace(/\{\{\s*|\s*\}\}/g, '').trim()
      uniqueNames.add(name)
    })
    
    return Array.from(uniqueNames).map(name => {
      const existing = selectedTemplate?.variables.find(v => v.name === name)
      return existing || { name, value: '', description: '' }
    })
  }
  
  // 更新模板内容
  const handleContentChange = (content: string) => {
    const variables = extractVariables(content)
    updateTemplate({ content, variables })
  }
  
  // 更新变量值
  const updateVariable = (index: number, updates: Partial<Variable>) => {
    if (!selectedTemplate) return
    
    const newVariables = [...selectedTemplate.variables]
    newVariables[index] = { ...newVariables[index], ...updates }
    updateTemplate({ variables: newVariables })
  }
  
  // 复制结果
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(previewText)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }
  
  // 下载结果
  const downloadResult = () => {
    const blob = new Blob([previewText], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${selectedTemplate?.name || 'template'}_${Date.now()}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  
  // 导入模板
  const importTemplate = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        const imported = JSON.parse(content)
        
        if (Array.isArray(imported)) {
          const newTemplates = imported.map(t => ({
            ...t,
            id: `template-${Date.now()}-${Math.random()}`,
            createdAt: new Date(t.createdAt || Date.now())
          }))
          saveTemplates([...templates, ...newTemplates])
        } else if (imported.content) {
          const newTemplate: Template = {
            id: `template-${Date.now()}`,
            name: imported.name || '导入的模板',
            content: imported.content,
            variables: imported.variables || extractVariables(imported.content),
            createdAt: new Date()
          }
          saveTemplates([...templates, newTemplate])
          setSelectedTemplate(newTemplate)
        }
      } catch (err) {
        console.error('导入失败:', err)
        alert('导入失败，请检查文件格式')
      }
    }
    reader.readAsText(file)
  }
  
  // 导出模板
  const exportTemplates = () => {
    const data = JSON.stringify(templates, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `templates_${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  
  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本模板工具</h2>
        <p className="text-gray-600">
          创建和管理文本模板，使用变量快速生成个性化内容
        </p>
      </div>
      
      <div className="grid md:grid-cols-3 gap-6">
        {/* 模板列表 */}
        <div className="md:col-span-1">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium">模板列表</h3>
            <div className="flex gap-2">
              <button
                onClick={createNewTemplate}
                className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                title="新建模板"
              >
                <Plus className="w-4 h-4" />
              </button>
              <label className="p-1 text-gray-600 hover:bg-gray-100 rounded cursor-pointer" title="导入模板">
                <input
                  type="file"
                  onChange={importTemplate}
                  className="hidden"
                  accept=".json,.txt"
                />
                <Upload className="w-4 h-4" />
              </label>
              <button
                onClick={exportTemplates}
                className="p-1 text-gray-600 hover:bg-gray-100 rounded"
                title="导出所有模板"
              >
                <Download className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {templates.map(template => (
              <div
                key={template.id}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedTemplate?.id === template.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onClick={() => {
                  setSelectedTemplate(template)
                  setEditingTemplate(false)
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="font-medium">{template.name}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      {template.variables.length} 个变量
                    </div>
                  </div>
                  {!template.id.startsWith('email-') && 
                   !template.id.startsWith('code-') && 
                   !template.id.startsWith('sql-') && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        if (confirm('确定要删除这个模板吗？')) {
                          deleteTemplate(template.id)
                        }
                      }}
                      className="p-1 text-red-500 hover:bg-red-50 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* 编辑区 */}
        <div className="md:col-span-2">
          {selectedTemplate ? (
            <div className="space-y-4">
              {/* 模板信息 */}
              <div className="bg-white rounded-lg border border-gray-300 p-4">
                <div className="flex items-center justify-between mb-3">
                  <input
                    type="text"
                    value={selectedTemplate.name}
                    onChange={(e) => updateTemplate({ name: e.target.value })}
                    className="text-lg font-medium bg-transparent border-b border-transparent hover:border-gray-300 focus:border-blue-500 focus:outline-none px-1"
                  />
                  <button
                    onClick={() => setEditingTemplate(!editingTemplate)}
                    className={`px-3 py-1 text-sm rounded-md transition-colors ${
                      editingTemplate
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    <Code className="w-4 h-4 inline mr-1" />
                    {editingTemplate ? '预览' : '编辑'}
                  </button>
                </div>
                
                {editingTemplate ? (
                  <textarea
                    value={selectedTemplate.content}
                    onChange={(e) => handleContentChange(e.target.value)}
                    className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                    placeholder="输入模板内容，使用 {{变量名}} 定义变量"
                  />
                ) : (
                  <div className="space-y-3">
                    {selectedTemplate.variables.length > 0 ? (
                      selectedTemplate.variables.map((variable, index) => (
                        <div key={variable.name} className="space-y-1">
                          <label className="block text-sm font-medium">
                            {variable.name}
                            {variable.description && (
                              <span className="text-gray-500 font-normal ml-2">
                                ({variable.description})
                              </span>
                            )}
                          </label>
                          <input
                            type="text"
                            value={variable.value}
                            onChange={(e) => updateVariable(index, { value: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder={`输入 ${variable.name} 的值`}
                          />
                        </div>
                      ))
                    ) : (
                      <div className="text-gray-500 text-center py-8">
                        <List className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                        <p>暂无变量</p>
                        <p className="text-sm mt-1">点击编辑按钮添加变量</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
              
              {/* 预览区 */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">生成结果</h3>
                  <div className="flex gap-2">
                    <button
                      onClick={copyToClipboard}
                      className="text-sm text-gray-600 hover:text-gray-800"
                    >
                      {copied ? (
                        <><Check className="w-4 h-4 inline mr-1 text-green-500" />已复制</>
                      ) : (
                        <><Copy className="w-4 h-4 inline mr-1" />复制</>
                      )}
                    </button>
                    <button
                      onClick={downloadResult}
                      className="text-sm text-gray-600 hover:text-gray-800"
                    >
                      <Download className="w-4 h-4 inline mr-1" />
                      下载
                    </button>
                  </div>
                </div>
                <textarea
                  value={previewText}
                  readOnly
                  className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm"
                  placeholder="填写变量后，这里将显示生成的文本"
                />
              </div>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <FileText className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <p className="text-lg mb-2">请选择或创建一个模板</p>
              <button
                onClick={createNewTemplate}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              >
                <Plus className="w-4 h-4 inline mr-2" />
                创建新模板
              </button>
            </div>
          )}
        </div>
      </div>
      
      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <FileText className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>使用 {'{{变量名}}'} 在模板中定义变量</li>
              <li>填写变量值后，自动生成最终文本</li>
              <li>支持创建、编辑、删除自定义模板</li>
              <li>模板会自动保存在浏览器本地</li>
              <li>可以导入导出模板，方便分享和备份</li>
              <li>内置常用模板，可直接使用或作为参考</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
