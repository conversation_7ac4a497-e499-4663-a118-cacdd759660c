'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { Upload, Camera, MapPin, Calendar, Settings, Info, Download, Copy, Check } from 'lucide-react'
// 声明 EXIF 库的类型
declare const EXIF: {
  getData(img: any, callback: () => void): void
  getAllTags(img: any): any
}

// 动态导入 EXIF 库
const exifJs = require('exif-js')

interface ExifData {
  // 基本信息
  Make?: string
  Model?: string
  Software?: string
  DateTime?: string
  DateTimeOriginal?: string
  
  // 拍摄参数
  ExposureTime?: string
  FNumber?: number
  ISO?: number
  FocalLength?: number
  FocalLengthIn35mmFilm?: number
  Flash?: string
  WhiteBalance?: string
  ExposureMode?: string
  ExposureProgram?: string
  MeteringMode?: string
  
  // 图像信息
  PixelXDimension?: number
  PixelYDimension?: number
  XResolution?: number
  YResolution?: number
  ResolutionUnit?: string
  ColorSpace?: string
  Orientation?: number
  
  // GPS信息
  GPSLatitude?: number[]
  GPSLatitudeRef?: string
  GPSLongitude?: number[]
  GPSLongitudeRef?: string
  GPSAltitude?: number
  GPSAltitudeRef?: number
  
  // 其他
  LensModel?: string
  Artist?: string
  Copyright?: string
  UserComment?: string
  
  // 原始数据
  [key: string]: any
}

export default function ImageExifViewerTool() {
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imageUrl, setImageUrl] = useState<string>('')
  const [exifData, setExifData] = useState<ExifData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [activeTab, setActiveTab] = useState<'basic' | 'camera' | 'gps' | 'all'>('basic')
  const [copied, setCopied] = useState(false)

  // 处理图片上传
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      setError('请选择图片文件')
      return
    }

    setImageFile(file)
    setError('')
    
    // 创建预览URL
    const url = URL.createObjectURL(file)
    setImageUrl(url)
    
    // 读取EXIF信息
    readExifData(file)
  }

  // 读取EXIF数据
  const readExifData = (file: File) => {
    setLoading(true)
    setExifData(null)

    // 使用 exifJs 库读取 EXIF 数据
    exifJs.getData(file, function(this: any) {
      const allTags = exifJs.getAllTags(this)
      setExifData(allTags)
      setLoading(false)
      
      if (Object.keys(allTags).length === 0) {
        setError('该图片不包含EXIF信息')
      }
    })
  }

  // 格式化曝光时间
  const formatExposureTime = (value: any): string => {
    if (!value) return '-'
    if (typeof value === 'number') {
      if (value < 1) {
        return `1/${Math.round(1/value)}s`
      }
      return `${value}s`
    }
    return String(value)
  }

  // 格式化光圈值
  const formatFNumber = (value: any): string => {
    if (!value) return '-'
    return `f/${value}`
  }

  // 格式化焦距
  const formatFocalLength = (value: any): string => {
    if (!value) return '-'
    return `${value}mm`
  }

  // 转换GPS坐标
  const convertGPSCoordinate = (coord: number[], ref: string): number => {
    if (!coord || coord.length !== 3) return 0
    
    const degrees = coord[0]
    const minutes = coord[1]
    const seconds = coord[2]
    
    let decimal = degrees + minutes / 60 + seconds / 3600
    
    if (ref === 'S' || ref === 'W') {
      decimal = -decimal
    }
    
    return decimal
  }

  // 获取GPS坐标字符串
  const getGPSString = (): string => {
    if (!exifData?.GPSLatitude || !exifData?.GPSLongitude) return ''
    
    const lat = convertGPSCoordinate(exifData.GPSLatitude, exifData.GPSLatitudeRef || 'N')
    const lng = convertGPSCoordinate(exifData.GPSLongitude, exifData.GPSLongitudeRef || 'E')
    
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
  }

  // 获取闪光灯状态
  const getFlashStatus = (flash: any): string => {
    if (!flash) return '-'
    const flashMap: { [key: number]: string } = {
      0x0: '未闪光',
      0x1: '闪光',
      0x5: '闪光但未检测到反射',
      0x7: '闪光且检测到反射',
      0x9: '强制闪光',
      0xd: '强制闪光但未检测到反射',
      0xf: '强制闪光且检测到反射',
      0x10: '禁用闪光',
      0x18: '自动模式未闪光',
      0x19: '自动模式闪光',
      0x1d: '自动模式闪光但未检测到反射',
      0x1f: '自动模式闪光且检测到反射',
      0x20: '无闪光功能',
      0x41: '闪光',
      0x45: '闪光但未检测到反射',
      0x47: '闪光且检测到反射',
      0x49: '强制闪光',
      0x4d: '强制闪光但未检测到反射',
      0x4f: '强制闪光且检测到反射',
      0x59: '自动模式闪光',
      0x5d: '自动模式闪光但未检测到反射',
      0x5f: '自动模式闪光且检测到反射'
    }
    return flashMap[flash] || `闪光 (${flash})`
  }

  // 复制EXIF数据
  const copyExifData = () => {
    if (!exifData) return
    
    const text = JSON.stringify(exifData, null, 2)
    navigator.clipboard.writeText(text).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  // 导出EXIF数据
  const exportExifData = () => {
    if (!exifData || !imageFile) return
    
    const data = {
      filename: imageFile.name,
      timestamp: new Date().toISOString(),
      exif: exifData
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${imageFile.name.split('.')[0]}_exif.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 清除数据
  const clearData = () => {
    setImageFile(null)
    setImageUrl('')
    setExifData(null)
    setError('')
    setActiveTab('basic')
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片 EXIF 信息查看器</h2>
        <p className="text-gray-600">
          查看照片的 EXIF 元数据，包括相机型号、拍摄参数、GPS 位置等信息
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* 图片上传区域 */}
        <div>
          <div className="bg-white rounded-lg border border-gray-300 p-6">
            <h3 className="font-medium mb-4">选择图片</h3>
            
            {!imageUrl ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
                <input
                  type="file"
                  onChange={handleImageUpload}
                  accept="image/*"
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="cursor-pointer">
                  <div className="text-center">
                    <Camera className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-600 mb-4">拖拽图片到此处或点击选择</p>
                    <span className="inline-block px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                      <Upload className="w-4 h-4 inline mr-2" />
                      选择图片
                    </span>
                  </div>
                </label>
              </div>
            ) : (
              <div>
                <div className="relative bg-gray-100 rounded-lg p-4 mb-4">
                  <Image
                    src={imageUrl}
                    alt="Preview"
                    width={500}
                    height={300}
                    className="max-w-full max-h-64 mx-auto object-contain"
                    unoptimized
                  />
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">文件名：</span>
                    <span className="font-medium">{imageFile?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">文件大小：</span>
                    <span className="font-medium">
                      {imageFile ? (imageFile.size / 1024 / 1024).toFixed(2) + ' MB' : '-'}
                    </span>
                  </div>
                </div>
                <button
                  onClick={clearData}
                  className="w-full mt-4 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                >
                  重新选择
                </button>
              </div>
            )}
            
            {error && (
              <div className="mt-4 p-3 bg-red-50 text-red-600 rounded-md text-sm">
                {error}
              </div>
            )}
          </div>
        </div>

        {/* EXIF信息显示区域 */}
        <div>
          <div className="bg-white rounded-lg border border-gray-300 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">EXIF 信息</h3>
              {exifData && (
                <div className="flex gap-2">
                  <button
                    onClick={copyExifData}
                    className={`px-3 py-1 text-sm rounded-md ${
                      copied
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    {copied ? (
                      <>
                        <Check className="w-3 h-3 inline mr-1" />
                        已复制
                      </>
                    ) : (
                      <>
                        <Copy className="w-3 h-3 inline mr-1" />
                        复制
                      </>
                    )}
                  </button>
                  <button
                    onClick={exportExifData}
                    className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
                  >
                    <Download className="w-3 h-3 inline mr-1" />
                    导出
                  </button>
                </div>
              )}
            </div>
            
            {loading ? (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p className="mt-2 text-gray-600">正在读取EXIF信息...</p>
              </div>
            ) : exifData ? (
              <div>
                {/* 标签页 */}
                <div className="flex gap-2 mb-4 border-b">
                  <button
                    onClick={() => setActiveTab('basic')}
                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === 'basic'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    基本信息
                  </button>
                  <button
                    onClick={() => setActiveTab('camera')}
                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === 'camera'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    拍摄参数
                  </button>
                  <button
                    onClick={() => setActiveTab('gps')}
                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === 'gps'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    GPS信息
                  </button>
                  <button
                    onClick={() => setActiveTab('all')}
                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === 'all'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    全部数据
                  </button>
                </div>

                {/* 内容区域 */}
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {activeTab === 'basic' && (
                    <>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <span className="text-gray-600">相机制造商：</span>
                        <span className="font-medium">{exifData.Make || '-'}</span>
                        
                        <span className="text-gray-600">相机型号：</span>
                        <span className="font-medium">{exifData.Model || '-'}</span>
                        
                        <span className="text-gray-600">拍摄时间：</span>
                        <span className="font-medium">{exifData.DateTimeOriginal || exifData.DateTime || '-'}</span>
                        
                        <span className="text-gray-600">软件：</span>
                        <span className="font-medium">{exifData.Software || '-'}</span>
                        
                        <span className="text-gray-600">图像尺寸：</span>
                        <span className="font-medium">
                          {exifData.PixelXDimension && exifData.PixelYDimension
                            ? `${exifData.PixelXDimension} × ${exifData.PixelYDimension}`
                            : '-'}
                        </span>
                        
                        <span className="text-gray-600">方向：</span>
                        <span className="font-medium">
                          {exifData.Orientation ? `方向 ${exifData.Orientation}` : '-'}
                        </span>
                      </div>
                    </>
                  )}

                  {activeTab === 'camera' && (
                    <>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <span className="text-gray-600">快门速度：</span>
                        <span className="font-medium">{formatExposureTime(exifData.ExposureTime)}</span>
                        
                        <span className="text-gray-600">光圈：</span>
                        <span className="font-medium">{formatFNumber(exifData.FNumber)}</span>
                        
                        <span className="text-gray-600">ISO：</span>
                        <span className="font-medium">{exifData.ISOSpeedRatings || '-'}</span>
                        
                        <span className="text-gray-600">焦距：</span>
                        <span className="font-medium">{formatFocalLength(exifData.FocalLength)}</span>
                        
                        <span className="text-gray-600">35mm等效焦距：</span>
                        <span className="font-medium">{formatFocalLength(exifData.FocalLengthIn35mmFilm)}</span>
                        
                        <span className="text-gray-600">闪光灯：</span>
                        <span className="font-medium">{getFlashStatus(exifData.Flash)}</span>
                        
                        <span className="text-gray-600">白平衡：</span>
                        <span className="font-medium">{exifData.WhiteBalance || '-'}</span>
                        
                        <span className="text-gray-600">曝光模式：</span>
                        <span className="font-medium">{exifData.ExposureMode || '-'}</span>
                        
                        <span className="text-gray-600">测光模式：</span>
                        <span className="font-medium">{exifData.MeteringMode || '-'}</span>
                        
                        <span className="text-gray-600">镜头型号：</span>
                        <span className="font-medium">{exifData.LensModel || '-'}</span>
                      </div>
                    </>
                  )}

                  {activeTab === 'gps' && (
                    <>
                      {exifData.GPSLatitude && exifData.GPSLongitude ? (
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <span className="text-gray-600">纬度：</span>
                            <span className="font-medium">
                              {convertGPSCoordinate(exifData.GPSLatitude, exifData.GPSLatitudeRef || 'N').toFixed(6)}°
                            </span>
                            
                            <span className="text-gray-600">经度：</span>
                            <span className="font-medium">
                              {convertGPSCoordinate(exifData.GPSLongitude, exifData.GPSLongitudeRef || 'E').toFixed(6)}°
                            </span>
                            
                            <span className="text-gray-600">海拔：</span>
                            <span className="font-medium">
                              {exifData.GPSAltitude 
                                ? `${exifData.GPSAltitude}m ${exifData.GPSAltitudeRef === 1 ? '(海平面以下)' : ''}`
                                : '-'}
                            </span>
                          </div>
                          
                          <div className="mt-4">
                            <a
                              href={`https://www.google.com/maps/search/?api=1&query=${getGPSString()}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                            >
                              <MapPin className="w-4 h-4 mr-2" />
                              在地图上查看
                            </a>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          该图片不包含GPS信息
                        </div>
                      )}
                    </>
                  )}

                  {activeTab === 'all' && (
                    <div className="bg-gray-50 rounded p-3">
                      <pre className="text-xs overflow-x-auto">
                        {JSON.stringify(exifData, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                请先选择图片
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持 JPG、PNG、TIFF 等包含 EXIF 信息的图片格式</li>
              <li>可查看相机型号、拍摄参数、GPS 位置等详细信息</li>
              <li>GPS 坐标可直接在 Google 地图中查看位置</li>
              <li>支持导出完整的 EXIF 数据为 JSON 格式</li>
              <li>所有处理都在浏览器本地完成，保护您的隐私</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
