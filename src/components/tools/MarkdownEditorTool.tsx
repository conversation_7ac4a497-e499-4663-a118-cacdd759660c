'use client'

import { useState, useEffect, useRef } from 'react'
import { Copy, Check, Download, Upload, Eye, EyeOff, FileText, Bold, Italic, Link, List, ListOrdered, Quote, Code, Image as ImageIcon, Table, Heading1, Heading2, Heading3 } from 'lucide-react'

interface ToolbarButton {
  icon: React.ReactNode
  title: string
  action: () => void
}

export default function MarkdownEditorTool() {
  const [markdown, setMarkdown] = useState('')
  const [preview, setPreview] = useState('')
  const [showPreview, setShowPreview] = useState(true)
  const [copied, setCopied] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Markdown 示例文本
  const exampleMarkdown = `# Markdown 编辑器示例

这是一个 **Markdown** 编辑器，支持实时预览。

## 功能特性

- 实时预览
- 语法高亮
- 工具栏快捷操作
- 导入/导出功能

### 代码示例

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

### 表格示例

| 功能 | 支持 |
|------|------|
| 标题 | ✅ |
| 列表 | ✅ |
| 链接 | ✅ |
| 图片 | ✅ |

> 这是一个引用示例

[访问 GitHub](https://github.com)
`

  // 转换 Markdown 为 HTML
  const convertMarkdownToHtml = (text: string): string => {
    let html = text

    // 转义 HTML
    html = html.replace(/&/g, '&amp;')
    html = html.replace(/</g, '&lt;')
    html = html.replace(/>/g, '&gt;')

    // 代码块
    html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
      return `<pre class="bg-gray-100 p-4 rounded overflow-x-auto"><code class="language-${lang || 'plaintext'}">${code.trim()}</code></pre>`
    })

    // 行内代码
    html = html.replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')

    // 标题
    html = html.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>')
    html = html.replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
    html = html.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')

    // 粗体
    html = html.replace(/\*\*([^*]+)\*\*/g, '<strong class="font-bold">$1</strong>')
    html = html.replace(/__([^_]+)__/g, '<strong class="font-bold">$1</strong>')

    // 斜体
    html = html.replace(/\*([^*]+)\*/g, '<em class="italic">$1</em>')
    html = html.replace(/_([^_]+)_/g, '<em class="italic">$1</em>')

    // 链接
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">$1</a>')

    // 图片
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="max-w-full h-auto rounded" />')

    // 无序列表
    html = html.replace(/^\* (.+)$/gim, '<li class="ml-4 list-disc">$1</li>')
    html = html.replace(/^- (.+)$/gim, '<li class="ml-4 list-disc">$1</li>')
    html = html.replace(/(<li class="ml-4 list-disc">.*<\/li>\n?)+/g, (match) => {
      return `<ul class="my-2">${match}</ul>`
    })

    // 有序列表
    html = html.replace(/^\d+\. (.+)$/gim, '<li class="ml-4 list-decimal">$1</li>')
    html = html.replace(/(<li class="ml-4 list-decimal">.*<\/li>\n?)+/g, (match) => {
      return `<ol class="my-2">${match}</ol>`
    })

    // 引用
    html = html.replace(/^&gt; (.+)$/gim, '<blockquote class="border-l-4 border-gray-300 pl-4 italic my-2">$1</blockquote>')

    // 表格
    html = html.replace(/\|(.+)\|/g, (match) => {
      const cells = match.split('|').filter(cell => cell.trim())
      const isHeader = cells.every(cell => cell.trim().match(/^-+$/))
      
      if (isHeader) return ''
      
      const cellTags = cells.map(cell => `<td class="border px-4 py-2">${cell.trim()}</td>`).join('')
      return `<tr>${cellTags}</tr>`
    })
    
    html = html.replace(/(<tr>.*<\/tr>\n?)+/g, (match) => {
      const rows = match.trim().split('\n')
      if (rows.length > 0) {
        const headerRow = rows[0]
        const bodyRows = rows.slice(1).join('\n')
        return `<table class="border-collapse border border-gray-300 my-4">
          <thead><tr class="bg-gray-100">${headerRow.replace(/<td/g, '<th').replace(/<\/td>/g, '</th>')}</tr></thead>
          <tbody>${bodyRows}</tbody>
        </table>`
      }
      return match
    })

    // 段落
    html = html.replace(/\n\n/g, '</p><p class="mb-4">')
    html = `<p class="mb-4">${html}</p>`

    // 清理空段落
    html = html.replace(/<p class="mb-4"><\/p>/g, '')

    return html
  }

  // 更新预览
  useEffect(() => {
    setPreview(convertMarkdownToHtml(markdown))
  }, [markdown])

  // 插入文本到光标位置
  const insertText = (before: string, after: string = '') => {
    const textarea = textareaRef.current
    if (!textarea) return

    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = markdown.substring(start, end)
    const newText = before + selectedText + after

    const newMarkdown = markdown.substring(0, start) + newText + markdown.substring(end)
    setMarkdown(newMarkdown)

    // 重新设置光标位置
    setTimeout(() => {
      textarea.focus()
      if (selectedText) {
        textarea.setSelectionRange(start, start + newText.length)
      } else {
        textarea.setSelectionRange(start + before.length, start + before.length)
      }
    }, 0)
  }

  // 工具栏按钮
  const toolbarButtons: ToolbarButton[] = [
    {
      icon: <Heading1 className="w-4 h-4" />,
      title: '一级标题',
      action: () => insertText('# ', ''),
    },
    {
      icon: <Heading2 className="w-4 h-4" />,
      title: '二级标题',
      action: () => insertText('## ', ''),
    },
    {
      icon: <Heading3 className="w-4 h-4" />,
      title: '三级标题',
      action: () => insertText('### ', ''),
    },
    {
      icon: <Bold className="w-4 h-4" />,
      title: '粗体',
      action: () => insertText('**', '**'),
    },
    {
      icon: <Italic className="w-4 h-4" />,
      title: '斜体',
      action: () => insertText('*', '*'),
    },
    {
      icon: <Link className="w-4 h-4" />,
      title: '链接',
      action: () => insertText('[', '](url)'),
    },
    {
      icon: <ImageIcon className="w-4 h-4" />,
      title: '图片',
      action: () => insertText('![', '](url)'),
    },
    {
      icon: <Code className="w-4 h-4" />,
      title: '代码',
      action: () => insertText('`', '`'),
    },
    {
      icon: <List className="w-4 h-4" />,
      title: '无序列表',
      action: () => insertText('- ', ''),
    },
    {
      icon: <ListOrdered className="w-4 h-4" />,
      title: '有序列表',
      action: () => insertText('1. ', ''),
    },
    {
      icon: <Quote className="w-4 h-4" />,
      title: '引用',
      action: () => insertText('> ', ''),
    },
    {
      icon: <Table className="w-4 h-4" />,
      title: '表格',
      action: () => insertText('| 列1 | 列2 |\n|------|------|\n| ', ' |  |'),
    },
  ]

  // 复制到剪贴板
  const copyToClipboard = () => {
    navigator.clipboard.writeText(markdown).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  // 下载文件
  const downloadFile = () => {
    const blob = new Blob([markdown], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'document.md'
    a.click()
    URL.revokeObjectURL(url)
  }

  // 加载文件
  const loadFile = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target?.result as string
      setMarkdown(text)
    }
    reader.readAsText(file)
  }

  // 加载示例
  const loadExample = () => {
    setMarkdown(exampleMarkdown)
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">Markdown 编辑器</h2>
        <p className="text-gray-600">
          实时预览的 Markdown 编辑器，支持常用语法和快捷操作
        </p>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg border border-gray-200 p-3 mb-4">
        <div className="flex items-center justify-between flex-wrap gap-2">
          <div className="flex items-center gap-1">
            {toolbarButtons.map((button, index) => (
              <button
                key={index}
                onClick={button.action}
                title={button.title}
                className="p-2 hover:bg-gray-100 rounded transition-colors"
              >
                {button.icon}
              </button>
            ))}
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={loadExample}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
            >
              加载示例
            </button>
            
            <label className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded cursor-pointer">
              <input
                type="file"
                accept=".md,.markdown,.txt"
                onChange={(e) => e.target.files?.[0] && loadFile(e.target.files[0])}
                className="hidden"
              />
              <Upload className="w-4 h-4 inline mr-1" />
              导入
            </label>
            
            <button
              onClick={downloadFile}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
            >
              <Download className="w-4 h-4 inline mr-1" />
              导出
            </button>
            
            <button
              onClick={copyToClipboard}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
            >
              {copied ? (
                <Check className="w-4 h-4 inline mr-1 text-green-600" />
              ) : (
                <Copy className="w-4 h-4 inline mr-1" />
              )}
              复制
            </button>
            
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="px-3 py-1 text-sm bg-blue-600 text-white hover:bg-blue-700 rounded"
            >
              {showPreview ? (
                <>
                  <EyeOff className="w-4 h-4 inline mr-1" />
                  隐藏预览
                </>
              ) : (
                <>
                  <Eye className="w-4 h-4 inline mr-1" />
                  显示预览
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 编辑器和预览 */}
      <div className={`grid ${showPreview ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1'} gap-4`}>
        {/* 编辑器 */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="border-b border-gray-200 p-3">
            <h3 className="font-medium flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Markdown 源码
            </h3>
          </div>
          <textarea
            ref={textareaRef}
            value={markdown}
            onChange={(e) => setMarkdown(e.target.value)}
            placeholder="在这里输入 Markdown..."
            className="w-full h-[600px] p-4 font-mono text-sm resize-none focus:outline-none"
            spellCheck={false}
          />
        </div>

        {/* 预览 */}
        {showPreview && (
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="border-b border-gray-200 p-3">
              <h3 className="font-medium flex items-center">
                <Eye className="w-5 h-5 mr-2" />
                预览
              </h3>
            </div>
            <div 
              className="h-[600px] p-4 overflow-y-auto prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: preview || '<p class="text-gray-400">预览内容将显示在这里...</p>' }}
            />
          </div>
        )}
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <FileText className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">Markdown 语法提示</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div>
                <p className="font-mono"># 一级标题</p>
                <p className="font-mono">## 二级标题</p>
                <p className="font-mono">**粗体文本**</p>
                <p className="font-mono">*斜体文本*</p>
              </div>
              <div>
                <p className="font-mono">[链接文本](URL)</p>
                <p className="font-mono">![图片描述](URL)</p>
                <p className="font-mono">`行内代码`</p>
                <p className="font-mono">```代码块```</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
