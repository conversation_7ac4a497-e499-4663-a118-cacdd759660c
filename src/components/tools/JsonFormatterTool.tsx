'use client'

import { useState } from 'react'
import { Code2, Copy, Download, AlertCircle, CheckCircle2 } from 'lucide-react'

export default function JsonFormatterTool() {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [error, setError] = useState('')
  const [indentSize, setIndentSize] = useState(2)
  const [copied, setCopied] = useState(false)

  const formatJson = () => {
    setError('')
    setCopied(false)
    
    if (!input.trim()) {
      setError('请输入 JSON 数据')
      setOutput('')
      return
    }

    try {
      const parsed = JSON.parse(input)
      const formatted = JSON.stringify(parsed, null, indentSize)
      setOutput(formatted)
    } catch (err) {
      if (err instanceof Error) {
        setError(`JSON 格式错误: ${err.message}`)
      } else {
        setError('JSON 格式错误')
      }
      setOutput('')
    }
  }

  const minifyJson = () => {
    setError('')
    setCopied(false)
    
    if (!input.trim()) {
      setError('请输入 JSON 数据')
      setOutput('')
      return
    }

    try {
      const parsed = JSON.parse(input)
      const minified = JSON.stringify(parsed)
      setOutput(minified)
    } catch (err) {
      if (err instanceof Error) {
        setError(`JSON 格式错误: ${err.message}`)
      } else {
        setError('JSON 格式错误')
      }
      setOutput('')
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(output)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  const downloadJson = () => {
    const blob = new Blob([output], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'formatted.json'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const loadSampleJson = () => {
    const sample = {
      "name": "PoorLow Tools",
      "version": "1.0.0",
      "description": "在线工具集合",
      "tools": [
        {
          "id": 1,
          "name": "JSON 格式化",
          "category": "开发工具"
        },
        {
          "id": 2,
          "name": "Base64 编解码",
          "category": "加密解密"
        }
      ],
      "features": ["快速", "免费", "无需安装"],
      "active": true
    }
    setInput(JSON.stringify(sample))
    setOutput('')
    setError('')
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">JSON 格式化工具</h2>
        <p className="text-gray-600">
          在线格式化、压缩、验证 JSON 数据
        </p>
      </div>

      {/* 工具栏 */}
      <div className="mb-4 flex flex-wrap gap-2">
        <button
          onClick={formatJson}
          className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          格式化
        </button>
        <button
          onClick={minifyJson}
          className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          压缩
        </button>
        <div className="flex items-center space-x-2">
          <label htmlFor="indent" className="text-sm text-gray-600">缩进:</label>
          <select
            id="indent"
            value={indentSize}
            onChange={(e) => setIndentSize(Number(e.target.value))}
            className="px-2 py-1 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value={2}>2 空格</option>
            <option value={4}>4 空格</option>
            <option value={8}>Tab</option>
          </select>
        </div>
        <button
          onClick={loadSampleJson}
          className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          加载示例
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* 主要内容区 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label htmlFor="json-input" className="block text-sm font-medium text-gray-700">
              输入 JSON
            </label>
            <button
              onClick={() => setInput('')}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              清空
            </button>
          </div>
          <textarea
            id="json-input"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder='{"key": "value"}'
            className="w-full h-96 px-3 py-2 font-mono text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label htmlFor="json-output" className="block text-sm font-medium text-gray-700">
              格式化结果
            </label>
            <div className="flex items-center space-x-2">
              {copied && (
                <span className="flex items-center text-sm text-green-600">
                  <CheckCircle2 className="h-4 w-4 mr-1" />
                  已复制
                </span>
              )}
              <button
                onClick={copyToClipboard}
                disabled={!output}
                className="p-1.5 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title="复制"
              >
                <Copy className="h-4 w-4" />
              </button>
              <button
                onClick={downloadJson}
                disabled={!output}
                className="p-1.5 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title="下载"
              >
                <Download className="h-4 w-4" />
              </button>
            </div>
          </div>
          <textarea
            id="json-output"
            value={output}
            readOnly
            placeholder="格式化后的 JSON 将显示在这里"
            className="w-full h-96 px-3 py-2 font-mono text-sm bg-gray-50 border border-gray-300 rounded-md shadow-sm"
          />
        </div>
      </div>

      {/* 功能说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Code2 className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">功能特点</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持格式化和压缩 JSON 数据</li>
              <li>实时验证 JSON 语法</li>
              <li>自定义缩进大小</li>
              <li>一键复制和下载结果</li>
              <li>详细的错误提示</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
