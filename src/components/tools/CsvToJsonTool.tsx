'use client'

import { useState } from 'react'
import { Copy, Check, Download, Upload, FileSpreadsheet, Code2, <PERSON>ertCircle, Settings } from 'lucide-react'

interface ConversionOptions {
  hasHeader: boolean
  delimiter: string
  trimValues: boolean
  skipEmptyRows: boolean
  parseNumbers: boolean
}

export default function CsvToJsonTool() {
  const [csvInput, setCsvInput] = useState('')
  const [jsonOutput, setJsonOutput] = useState('')
  const [error, setError] = useState('')
  const [copied, setCopied] = useState(false)
  const [showOptions, setShowOptions] = useState(false)
  const [options, setOptions] = useState<ConversionOptions>({
    hasHeader: true,
    delimiter: ',',
    trimValues: true,
    skipEmptyRows: true,
    parseNumbers: true,
  })

  // CSV 示例数据
  const exampleCsv = `姓名,年龄,城市,邮箱
张三,28,北京,z<PERSON><PERSON>@example.com
李四,32,上海,<EMAIL>
王五,25,广州,<EMAIL>
赵六,30,深圳,zhao<PERSON><PERSON>@example.com`

  // 解析 CSV 行
  const parseCSVLine = (line: string, delimiter: string): string[] => {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      const nextChar = line[i + 1]
      
      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          current += '"'
          i++ // 跳过下一个引号
        } else {
          inQuotes = !inQuotes
        }
      } else if (char === delimiter && !inQuotes) {
        result.push(current)
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current)
    return result
  }

  // CSV 转 JSON
  const convertCsvToJson = () => {
    try {
      if (!csvInput.trim()) {
        setJsonOutput('')
        setError('')
        return
      }

      const lines = csvInput.split('\n')
      const result: any[] = []
      let headers: string[] = []
      
      // 处理每一行
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]
        
        // 跳过空行
        if (options.skipEmptyRows && !line.trim()) {
          continue
        }
        
        const values = parseCSVLine(line, options.delimiter)
        
        // 处理值
        const processedValues = values.map(value => {
          let processed = value
          
          // 去除引号
          if (processed.startsWith('"') && processed.endsWith('"')) {
            processed = processed.slice(1, -1)
          }
          
          // 去除空白
          if (options.trimValues) {
            processed = processed.trim()
          }
          
          // 解析数字
          if (options.parseNumbers && !isNaN(Number(processed)) && processed !== '') {
            return Number(processed)
          }
          
          return processed
        })
        
        // 第一行作为表头
        if (i === 0 && options.hasHeader) {
          headers = processedValues.map(h => String(h))
        } else {
          // 创建对象
          if (options.hasHeader && headers.length > 0) {
            const obj: any = {}
            headers.forEach((header, index) => {
              obj[header] = processedValues[index] !== undefined ? processedValues[index] : null
            })
            result.push(obj)
          } else {
            // 没有表头，使用数组
            result.push(processedValues)
          }
        }
      }
      
      setJsonOutput(JSON.stringify(result, null, 2))
      setError('')
    } catch (err) {
      setError('转换失败：' + (err instanceof Error ? err.message : '未知错误'))
      setJsonOutput('')
    }
  }

  // JSON 转 CSV
  const convertJsonToCsv = () => {
    try {
      if (!csvInput.trim()) {
        setJsonOutput('')
        setError('')
        return
      }

      const data = JSON.parse(csvInput)
      
      if (!Array.isArray(data)) {
        throw new Error('输入必须是一个数组')
      }
      
      if (data.length === 0) {
        setJsonOutput('')
        return
      }
      
      let csv = ''
      
      // 如果是对象数组
      if (typeof data[0] === 'object' && !Array.isArray(data[0])) {
        // 获取所有键作为表头
        const headers = Array.from(new Set(data.flatMap(obj => Object.keys(obj))))
        
        // 添加表头
        csv += headers.map(h => escapeCSVValue(h)).join(options.delimiter) + '\n'
        
        // 添加数据行
        data.forEach(obj => {
          const row = headers.map(header => {
            const value = obj[header]
            return escapeCSVValue(value !== undefined ? String(value) : '')
          })
          csv += row.join(options.delimiter) + '\n'
        })
      } else {
        // 数组的数组
        data.forEach(row => {
          if (Array.isArray(row)) {
            csv += row.map(value => escapeCSVValue(String(value))).join(options.delimiter) + '\n'
          } else {
            csv += escapeCSVValue(String(row)) + '\n'
          }
        })
      }
      
      setJsonOutput(csv.trim())
      setError('')
    } catch (err) {
      setError('转换失败：' + (err instanceof Error ? err.message : '无效的JSON格式'))
      setJsonOutput('')
    }
  }

  // 转义 CSV 值
  const escapeCSVValue = (value: string): string => {
    if (value.includes(options.delimiter) || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`
    }
    return value
  }

  // 复制到剪贴板
  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonOutput).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  // 下载文件
  const downloadFile = (isJson: boolean) => {
    const content = jsonOutput
    const type = isJson ? 'application/json' : 'text/csv'
    const extension = isJson ? 'json' : 'csv'
    
    const blob = new Blob([content], { type })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `converted.${extension}`
    a.click()
    URL.revokeObjectURL(url)
  }

  // 加载文件
  const loadFile = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target?.result as string
      setCsvInput(text)
    }
    reader.readAsText(file)
  }

  // 加载示例
  const loadExample = () => {
    setCsvInput(exampleCsv)
  }

  // 清空
  const clearAll = () => {
    setCsvInput('')
    setJsonOutput('')
    setError('')
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">CSV/JSON 转换器</h2>
        <p className="text-gray-600">
          在 CSV 和 JSON 格式之间相互转换，支持自定义选项
        </p>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
        <div className="flex items-center justify-between flex-wrap gap-2">
          <div className="flex items-center gap-2">
            <button
              onClick={convertCsvToJson}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              CSV → JSON
            </button>
            
            <button
              onClick={convertJsonToCsv}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              JSON → CSV
            </button>
            
            <button
              onClick={() => setShowOptions(!showOptions)}
              className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md flex items-center"
            >
              <Settings className="w-4 h-4 mr-1" />
              选项
            </button>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={loadExample}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
            >
              加载示例
            </button>
            
            <label className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded cursor-pointer">
              <input
                type="file"
                accept=".csv,.json,.txt"
                onChange={(e) => e.target.files?.[0] && loadFile(e.target.files[0])}
                className="hidden"
              />
              <Upload className="w-4 h-4 inline mr-1" />
              导入文件
            </label>
            
            <button
              onClick={clearAll}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
            >
              清空
            </button>
          </div>
        </div>

        {/* 选项面板 */}
        {showOptions && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.hasHeader}
                  onChange={(e) => setOptions({ ...options, hasHeader: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm">第一行作为表头</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.trimValues}
                  onChange={(e) => setOptions({ ...options, trimValues: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm">去除空白字符</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.skipEmptyRows}
                  onChange={(e) => setOptions({ ...options, skipEmptyRows: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm">跳过空行</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.parseNumbers}
                  onChange={(e) => setOptions({ ...options, parseNumbers: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm">自动解析数字</span>
              </label>
              
              <div className="flex items-center">
                <span className="text-sm mr-2">分隔符：</span>
                <select
                  value={options.delimiter}
                  onChange={(e) => setOptions({ ...options, delimiter: e.target.value })}
                  className="px-2 py-1 border border-gray-300 rounded text-sm"
                >
                  <option value=",">逗号 (,)</option>
                  <option value=";">分号 (;)</option>
                  <option value="\t">制表符 (Tab)</option>
                  <option value="|">竖线 (|)</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
          <AlertCircle className="w-5 h-5 text-red-600 mr-2 flex-shrink-0 mt-0.5" />
          <span className="text-red-800">{error}</span>
        </div>
      )}

      {/* 输入输出区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* 输入区域 */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="border-b border-gray-200 p-3 flex items-center">
            <FileSpreadsheet className="w-5 h-5 mr-2" />
            <h3 className="font-medium">输入 (CSV/JSON)</h3>
          </div>
          <textarea
            value={csvInput}
            onChange={(e) => setCsvInput(e.target.value)}
            placeholder="粘贴 CSV 或 JSON 数据..."
            className="w-full h-96 p-4 font-mono text-sm resize-none focus:outline-none"
            spellCheck={false}
          />
        </div>

        {/* 输出区域 */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="border-b border-gray-200 p-3 flex items-center justify-between">
            <div className="flex items-center">
              <Code2 className="w-5 h-5 mr-2" />
              <h3 className="font-medium">输出 (JSON/CSV)</h3>
            </div>
            <div className="flex items-center gap-2">
              {jsonOutput && (
                <>
                  <button
                    onClick={() => downloadFile(jsonOutput.trim().startsWith('[') || jsonOutput.trim().startsWith('{'))}
                    className="p-1 hover:bg-gray-100 rounded"
                    title="下载文件"
                  >
                    <Download className="w-4 h-4 text-gray-400" />
                  </button>
                  <button
                    onClick={copyToClipboard}
                    className="p-1 hover:bg-gray-100 rounded"
                    title="复制到剪贴板"
                  >
                    {copied ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </>
              )}
            </div>
          </div>
          <textarea
            value={jsonOutput}
            readOnly
            placeholder="转换结果将显示在这里..."
            className="w-full h-96 p-4 font-mono text-sm resize-none focus:outline-none bg-gray-50"
          />
        </div>
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持标准 CSV 格式，包括带引号的字段</li>
              <li>JSON 必须是数组格式才能转换为 CSV</li>
              <li>可以自定义分隔符和其他转换选项</li>
              <li>支持导入本地文件和导出转换结果</li>
              <li>自动处理特殊字符和换行符</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
