'use client'

import { useEffect } from 'react'
import { useHomeStore } from '@/stores/homeStore'
import { type Locale } from '@/i18n/config'

interface ToolWrapperProps {
  toolId: string
  locale: Locale
  children: React.ReactNode
}

export function ToolWrapper({ toolId, locale, children }: ToolWrapperProps) {
  const addRecentTool = useHomeStore((state) => state.addRecentTool)

  useEffect(() => {
    // 记录工具使用
    addRecentTool(toolId)
  }, [toolId, addRecentTool])

  return <>{children}</>
}
