'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Download, Plus, Trash2, Settings } from 'lucide-react'

type ChartType = 'bar' | 'line' | 'pie'

interface DataPoint {
  label: string
  value: number
  color?: string
}

interface ChartSettings {
  title: string
  width: number
  height: number
  showLegend: boolean
  showGrid: boolean
  showValues: boolean
}

export default function ChartGeneratorTool() {
  const [chartType, setChartType] = useState<ChartType>('bar')
  const [dataPoints, setDataPoints] = useState<DataPoint[]>([
    { label: '项目A', value: 30, color: '#3B82F6' },
    { label: '项目B', value: 45, color: '#10B981' },
    { label: '项目C', value: 25, color: '#F59E0B' },
  ])
  const [newLabel, setNewLabel] = useState('')
  const [newValue, setNewValue] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const [settings, setSettings] = useState<ChartSettings>({
    title: '数据图表',
    width: 600,
    height: 400,
    showLegend: true,
    showGrid: true,
    showValues: true,
  })

  // 颜色预设
  const colorPresets = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
    '#EC4899', '#14B8A6', '#F97316', '#6366F1', '#84CC16'
  ]

  // 添加数据点
  const addDataPoint = () => {
    if (newLabel && newValue) {
      const value = parseFloat(newValue)
      if (!isNaN(value)) {
        const color = colorPresets[dataPoints.length % colorPresets.length]
        setDataPoints([...dataPoints, { label: newLabel, value, color }])
        setNewLabel('')
        setNewValue('')
      }
    }
  }

  // 删除数据点
  const removeDataPoint = (index: number) => {
    setDataPoints(dataPoints.filter((_, i) => i !== index))
  }

  // 更新数据点
  const updateDataPoint = (index: number, field: 'label' | 'value' | 'color', value: string) => {
    const updated = [...dataPoints]
    if (field === 'value') {
      const numValue = parseFloat(value)
      if (!isNaN(numValue)) {
        updated[index].value = numValue
      }
    } else if (field === 'label' || field === 'color') {
      updated[index][field] = value
    }
    setDataPoints(updated)
  }

  // 计算总和（用于饼图）
  const total = dataPoints.reduce((sum, point) => sum + point.value, 0)

  // 计算最大值（用于缩放）
  const maxValue = Math.max(...dataPoints.map(p => p.value), 1)

  // SVG 图表渲染
  const renderChart = () => {
    const { width, height, showGrid, showValues } = settings
    const padding = 60
    const chartWidth = width - padding * 2
    const chartHeight = height - padding * 2

    switch (chartType) {
      case 'bar':
        return (
          <svg width={width} height={height} className="bg-white rounded-lg">
            {/* 网格线 */}
            {showGrid && (
              <g>
                {[0, 0.25, 0.5, 0.75, 1].map((tick) => (
                  <line
                    key={tick}
                    x1={padding}
                    y1={padding + chartHeight * (1 - tick)}
                    x2={padding + chartWidth}
                    y2={padding + chartHeight * (1 - tick)}
                    stroke="#E5E7EB"
                    strokeDasharray="2,2"
                  />
                ))}
              </g>
            )}
            
            {/* 坐标轴 */}
            <line x1={padding} y1={padding} x2={padding} y2={padding + chartHeight} stroke="#374151" strokeWidth="2" />
            <line x1={padding} y1={padding + chartHeight} x2={padding + chartWidth} y2={padding + chartHeight} stroke="#374151" strokeWidth="2" />
            
            {/* Y轴标签 */}
            {[0, 0.25, 0.5, 0.75, 1].map((tick) => (
              <text
                key={tick}
                x={padding - 10}
                y={padding + chartHeight * (1 - tick) + 5}
                textAnchor="end"
                fontSize="12"
                fill="#6B7280"
              >
                {Math.round(maxValue * tick)}
              </text>
            ))}
            
            {/* 柱状图 */}
            {dataPoints.map((point, index) => {
              const barWidth = chartWidth / dataPoints.length * 0.6
              const barHeight = (point.value / maxValue) * chartHeight
              const x = padding + (chartWidth / dataPoints.length) * (index + 0.2)
              const y = padding + chartHeight - barHeight
              
              return (
                <g key={index}>
                  <rect
                    x={x}
                    y={y}
                    width={barWidth}
                    height={barHeight}
                    fill={point.color}
                    rx="4"
                  />
                  {showValues && (
                    <text
                      x={x + barWidth / 2}
                      y={y - 5}
                      textAnchor="middle"
                      fontSize="12"
                      fill="#374151"
                    >
                      {point.value}
                    </text>
                  )}
                  <text
                    x={x + barWidth / 2}
                    y={padding + chartHeight + 20}
                    textAnchor="middle"
                    fontSize="12"
                    fill="#6B7280"
                  >
                    {point.label}
                  </text>
                </g>
              )
            })}
          </svg>
        )
      
      case 'line':
        const points = dataPoints.map((point, index) => {
          const x = padding + (chartWidth / (dataPoints.length - 1 || 1)) * index
          const y = padding + chartHeight - (point.value / maxValue) * chartHeight
          return { x, y, ...point }
        })
        
        const pathData = points.map((point, index) => 
          `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
        ).join(' ')
        
        return (
          <svg width={width} height={height} className="bg-white rounded-lg">
            {/* 网格线 */}
            {showGrid && (
              <g>
                {[0, 0.25, 0.5, 0.75, 1].map((tick) => (
                  <line
                    key={tick}
                    x1={padding}
                    y1={padding + chartHeight * (1 - tick)}
                    x2={padding + chartWidth}
                    y2={padding + chartHeight * (1 - tick)}
                    stroke="#E5E7EB"
                    strokeDasharray="2,2"
                  />
                ))}
              </g>
            )}
            
            {/* 坐标轴 */}
            <line x1={padding} y1={padding} x2={padding} y2={padding + chartHeight} stroke="#374151" strokeWidth="2" />
            <line x1={padding} y1={padding + chartHeight} x2={padding + chartWidth} y2={padding + chartHeight} stroke="#374151" strokeWidth="2" />
            
            {/* Y轴标签 */}
            {[0, 0.25, 0.5, 0.75, 1].map((tick) => (
              <text
                key={tick}
                x={padding - 10}
                y={padding + chartHeight * (1 - tick) + 5}
                textAnchor="end"
                fontSize="12"
                fill="#6B7280"
              >
                {Math.round(maxValue * tick)}
              </text>
            ))}
            
            {/* 折线 */}
            <path
              d={pathData}
              fill="none"
              stroke="#3B82F6"
              strokeWidth="3"
            />
            
            {/* 数据点和标签 */}
            {points.map((point, index) => (
              <g key={index}>
                <circle
                  cx={point.x}
                  cy={point.y}
                  r="5"
                  fill={point.color}
                />
                {showValues && (
                  <text
                    x={point.x}
                    y={point.y - 10}
                    textAnchor="middle"
                    fontSize="12"
                    fill="#374151"
                  >
                    {point.value}
                  </text>
                )}
                <text
                  x={point.x}
                  y={padding + chartHeight + 20}
                  textAnchor="middle"
                  fontSize="12"
                  fill="#6B7280"
                >
                  {point.label}
                </text>
              </g>
            ))}
          </svg>
        )
      
      case 'pie':
        const centerX = width / 2
        const centerY = height / 2
        const radius = Math.min(width, height) / 3
        let currentAngle = -Math.PI / 2
        
        return (
          <svg width={width} height={height} className="bg-white rounded-lg">
            {dataPoints.map((point, index) => {
              const angle = (point.value / total) * Math.PI * 2
              const startAngle = currentAngle
              const endAngle = currentAngle + angle
              currentAngle = endAngle
              
              const x1 = centerX + radius * Math.cos(startAngle)
              const y1 = centerY + radius * Math.sin(startAngle)
              const x2 = centerX + radius * Math.cos(endAngle)
              const y2 = centerY + radius * Math.sin(endAngle)
              
              const largeArcFlag = angle > Math.PI ? 1 : 0
              
              const pathData = [
                `M ${centerX} ${centerY}`,
                `L ${x1} ${y1}`,
                `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                'Z'
              ].join(' ')
              
              // 标签位置
              const labelAngle = (startAngle + endAngle) / 2
              const labelX = centerX + radius * 0.7 * Math.cos(labelAngle)
              const labelY = centerY + radius * 0.7 * Math.sin(labelAngle)
              
              return (
                <g key={index}>
                  <path
                    d={pathData}
                    fill={point.color}
                    stroke="white"
                    strokeWidth="2"
                  />
                  {showValues && (
                    <text
                      x={labelX}
                      y={labelY}
                      textAnchor="middle"
                      fontSize="14"
                      fill="white"
                      fontWeight="bold"
                    >
                      {Math.round((point.value / total) * 100)}%
                    </text>
                  )}
                </g>
              )
            })}
            
            {/* 图例 */}
            {settings.showLegend && (
              <g>
                {dataPoints.map((point, index) => (
                  <g key={index} transform={`translate(${width - 150}, ${30 + index * 25})`}>
                    <rect x="0" y="0" width="15" height="15" fill={point.color} />
                    <text x="20" y="12" fontSize="12" fill="#374151">
                      {point.label} ({point.value})
                    </text>
                  </g>
                ))}
              </g>
            )}
          </svg>
        )
    }
  }

  // 下载 SVG
  const downloadSVG = () => {
    const svgElement = document.querySelector('.chart-container svg')
    if (svgElement) {
      const svgData = new XMLSerializer().serializeToString(svgElement)
      const blob = new Blob([svgData], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${settings.title}.svg`
      link.click()
      URL.revokeObjectURL(url)
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">数据图表生成器</h2>
        <p className="text-gray-600">
          快速创建柱状图、折线图、饼图等数据可视化图表
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧控制面板 */}
        <div className="space-y-4">
          {/* 图表类型选择 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3">图表类型</h3>
            <div className="grid grid-cols-3 gap-2">
              <button
                onClick={() => setChartType('bar')}
                className={`p-3 rounded-md border transition-colors ${
                  chartType === 'bar'
                    ? 'border-blue-500 bg-blue-50 text-blue-600'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <BarChart className="w-6 h-6 mx-auto mb-1" />
                <span className="text-xs">柱状图</span>
              </button>
              <button
                onClick={() => setChartType('line')}
                className={`p-3 rounded-md border transition-colors ${
                  chartType === 'line'
                    ? 'border-blue-500 bg-blue-50 text-blue-600'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <LineChart className="w-6 h-6 mx-auto mb-1" />
                <span className="text-xs">折线图</span>
              </button>
              <button
                onClick={() => setChartType('pie')}
                className={`p-3 rounded-md border transition-colors ${
                  chartType === 'pie'
                    ? 'border-blue-500 bg-blue-50 text-blue-600'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <PieChart className="w-6 h-6 mx-auto mb-1" />
                <span className="text-xs">饼图</span>
              </button>
            </div>
          </div>

          {/* 数据输入 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3">数据管理</h3>
            
            {/* 现有数据 */}
            <div className="space-y-2 mb-4 max-h-64 overflow-y-auto">
              {dataPoints.map((point, index) => (
                <div key={index} className="flex items-center gap-2">
                  <input
                    type="color"
                    value={point.color}
                    onChange={(e) => updateDataPoint(index, 'color', e.target.value)}
                    className="w-8 h-8 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    value={point.label}
                    onChange={(e) => updateDataPoint(index, 'label', e.target.value)}
                    className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                  <input
                    type="number"
                    value={point.value}
                    onChange={(e) => updateDataPoint(index, 'value', e.target.value)}
                    className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                  <button
                    onClick={() => removeDataPoint(index)}
                    className="p-1 text-red-500 hover:bg-red-50 rounded"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>

            {/* 添加新数据 */}
            <div className="flex gap-2">
              <input
                type="text"
                value={newLabel}
                onChange={(e) => setNewLabel(e.target.value)}
                placeholder="标签"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
              />
              <input
                type="number"
                value={newValue}
                onChange={(e) => setNewValue(e.target.value)}
                placeholder="数值"
                className="w-24 px-3 py-2 border border-gray-300 rounded-md"
              />
              <button
                onClick={addDataPoint}
                className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Plus className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* 图表设置 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center justify-between w-full mb-3"
            >
              <h3 className="font-medium flex items-center">
                <Settings className="w-4 h-4 mr-2" />
                图表设置
              </h3>
              <span className="text-gray-400 text-sm">{showSettings ? '收起' : '展开'}</span>
            </button>
            
            {showSettings && (
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    图表标题
                  </label>
                  <input
                    type="text"
                    value={settings.title}
                    onChange={(e) => setSettings({...settings, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      宽度
                    </label>
                    <input
                      type="number"
                      value={settings.width}
                      onChange={(e) => setSettings({...settings, width: parseInt(e.target.value) || 600})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      高度
                    </label>
                    <input
                      type="number"
                      value={settings.height}
                      onChange={(e) => setSettings({...settings, height: parseInt(e.target.value) || 400})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.showLegend}
                      onChange={(e) => setSettings({...settings, showLegend: e.target.checked})}
                      className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">显示图例</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.showGrid}
                      onChange={(e) => setSettings({...settings, showGrid: e.target.checked})}
                      className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">显示网格</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.showValues}
                      onChange={(e) => setSettings({...settings, showValues: e.target.checked})}
                      className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">显示数值</span>
                  </label>
                </div>
              </div>
            )}
          </div>

          {/* 下载按钮 */}
          <button
            onClick={downloadSVG}
            className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center justify-center"
          >
            <Download className="w-5 h-5 mr-2" />
            下载 SVG
          </button>
        </div>

        {/* 右侧图表预览 */}
        <div className="lg:col-span-2">
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium mb-3">{settings.title}</h3>
            <div className="chart-container flex justify-center items-center overflow-auto">
              {dataPoints.length > 0 ? (
                renderChart()
              ) : (
                <div className="text-center py-20 text-gray-500">
                  <BarChart className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>请添加数据以生成图表</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <BarChart className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持柱状图、折线图和饼图三种常用图表类型</li>
              <li>可自定义每个数据点的颜色、标签和数值</li>
              <li>支持调整图表尺寸、显示网格和数值标签</li>
              <li>生成的图表可以下载为 SVG 格式，便于后续编辑</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
