'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Shield, AlertTriangle, CheckCircle, Key, Clock, User } from 'lucide-react'

interface JWTPayload {
  [key: string]: any
}

interface DecodedJWT {
  header: any
  payload: JWTPayload
  signature: string
  isValid: boolean
  isExpired: boolean
  expiresAt?: Date
  issuedAt?: Date
  algorithm?: string
  issuer?: string
  audience?: string | string[]
  subject?: string
}

const JwtDecoderTool: React.FC = () => {
  const [jwtToken, setJwtToken] = useState('')
  const [decodedJWT, setDecodedJWT] = useState<DecodedJWT | null>(null)
  const [error, setError] = useState('')
  const [secretKey, setSecretKey] = useState('')
  const [generateToken, setGenerateToken] = useState(false)
  const [newTokenPayload, setNewTokenPayload] = useState('{\n  "sub": "1234567890",\n  "name": "John Doe",\n  "iat": 1516239022,\n  "exp": 1516242622\n}')
  
  // 示例 JWT Token
  const exampleJWT = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyNDI2MjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c'

  // Base64 URL 解码
  const base64UrlDecode = (str: string): string => {
    // 替换 URL 安全字符
    let base64 = str.replace(/-/g, '+').replace(/_/g, '/')
    
    // 添加填充
    while (base64.length % 4) {
      base64 += '='
    }
    
    try {
      return atob(base64)
    } catch (e) {
      throw new Error('Invalid base64 string')
    }
  }

  // Base64 URL 编码
  const base64UrlEncode = (str: string): string => {
    return btoa(str)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  // 解析 JWT Token
  const parseJWT = useCallback((token: string): DecodedJWT => {
    const parts = token.split('.')
    
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format. JWT must have 3 parts separated by dots.')
    }

    try {
      // 解码 Header
      const headerDecoded = base64UrlDecode(parts[0])
      const header = JSON.parse(headerDecoded)

      // 解码 Payload
      const payloadDecoded = base64UrlDecode(parts[1])
      const payload = JSON.parse(payloadDecoded)

      // 获取签名
      const signature = parts[2]

      // 检查过期时间
      const now = Math.floor(Date.now() / 1000)
      const isExpired = payload.exp ? now > payload.exp : false
      const expiresAt = payload.exp ? new Date(payload.exp * 1000) : undefined
      const issuedAt = payload.iat ? new Date(payload.iat * 1000) : undefined

      return {
        header,
        payload,
        signature,
        isValid: true,
        isExpired,
        expiresAt,
        issuedAt,
        algorithm: header.alg,
        issuer: payload.iss,
        audience: payload.aud,
        subject: payload.sub
      }
    } catch (e) {
      throw new Error(`Failed to parse JWT: ${(e as Error).message}`)
    }
  }, [])

  // 生成简单的 JWT Token（仅用于演示，实际应用中需要使用专门的库）
  const generateJWT = (payload: any, secret: string = 'your-secret-key'): string => {
    const header = {
      alg: 'HS256',
      typ: 'JWT'
    }

    const encodedHeader = base64UrlEncode(JSON.stringify(header))
    const encodedPayload = base64UrlEncode(JSON.stringify(payload))
    
    // 这里是简化的签名生成，实际应用中应该使用 HMAC SHA256
    const signature = base64UrlEncode(`${encodedHeader}.${encodedPayload}.${secret}`)
    
    return `${encodedHeader}.${encodedPayload}.${signature}`
  }

  // 处理 JWT 解析
  useEffect(() => {
    if (!jwtToken.trim()) {
      setDecodedJWT(null)
      setError('')
      return
    }

    try {
      const decoded = parseJWT(jwtToken.trim())
      setDecodedJWT(decoded)
      setError('')
    } catch (e) {
      setError((e as Error).message)
      setDecodedJWT(null)
    }
  }, [jwtToken, parseJWT])

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 使用示例 JWT
  const useExampleJWT = () => {
    setJwtToken(exampleJWT)
  }

  // 生成新的 JWT Token
  const handleGenerateToken = () => {
    try {
      const payload = JSON.parse(newTokenPayload)
      const token = generateJWT(payload, secretKey || 'demo-secret-key')
      setJwtToken(token)
      setGenerateToken(false)
    } catch (e) {
      setError(`Invalid JSON payload: ${(e as Error).message}`)
    }
  }

  // 格式化时间
  const formatDate = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Shield className="w-8 h-8" />
          JWT 解析器
        </h1>
        <p className="text-muted-foreground">
          解析和验证 JSON Web Tokens，查看 Header、Payload 和签名信息
        </p>
      </div>

      <Tabs defaultValue="decode" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="decode">解析 JWT</TabsTrigger>
          <TabsTrigger value="generate">生成 JWT</TabsTrigger>
        </TabsList>

        <TabsContent value="decode" className="space-y-6">
          {/* JWT 输入区域 */}
          <Card className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-lg font-semibold">JWT Token</Label>
                <Button variant="outline" size="sm" onClick={useExampleJWT}>
                  使用示例
                </Button>
              </div>
              <Textarea
                value={jwtToken}
                onChange={(e) => setJwtToken(e.target.value)}
                placeholder="请输入 JWT Token..."
                className="min-h-[120px] font-mono text-sm"
              />
              {error && (
                <div className="flex items-center gap-2 text-red-500 text-sm">
                  <AlertTriangle className="w-4 h-4" />
                  {error}
                </div>
              )}
            </div>
          </Card>

          {/* 解析结果 */}
          {decodedJWT && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Header 信息 */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Key className="w-5 h-5" />
                  Header
                </h3>
                <div className="space-y-3">
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm overflow-auto">
                      {JSON.stringify(decodedJWT.header, null, 2)}
                    </pre>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(JSON.stringify(decodedJWT.header, null, 2))}
                    className="flex items-center gap-2"
                  >
                    <Copy className="w-4 h-4" />
                    复制 Header
                  </Button>
                </div>
              </Card>

              {/* Payload 信息 */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Payload
                </h3>
                <div className="space-y-3">
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm overflow-auto">
                      {JSON.stringify(decodedJWT.payload, null, 2)}
                    </pre>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(JSON.stringify(decodedJWT.payload, null, 2))}
                    className="flex items-center gap-2"
                  >
                    <Copy className="w-4 h-4" />
                    复制 Payload
                  </Button>
                </div>
              </Card>
            </div>
          )}

          {/* Token 信息摘要 */}
          {decodedJWT && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Token 信息
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-1">
                  <Label className="text-sm text-muted-foreground">算法</Label>
                  <div className="font-mono text-sm">{decodedJWT.algorithm || 'N/A'}</div>
                </div>
                
                <div className="space-y-1">
                  <Label className="text-sm text-muted-foreground">状态</Label>
                  <div className="flex items-center gap-2">
                    {decodedJWT.isExpired ? (
                      <>
                        <AlertTriangle className="w-4 h-4 text-red-500" />
                        <span className="text-red-500">已过期</span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-green-500">有效</span>
                      </>
                    )}
                  </div>
                </div>

                {decodedJWT.issuedAt && (
                  <div className="space-y-1">
                    <Label className="text-sm text-muted-foreground">签发时间</Label>
                    <div className="text-sm">{formatDate(decodedJWT.issuedAt)}</div>
                  </div>
                )}

                {decodedJWT.expiresAt && (
                  <div className="space-y-1">
                    <Label className="text-sm text-muted-foreground">过期时间</Label>
                    <div className="text-sm">{formatDate(decodedJWT.expiresAt)}</div>
                  </div>
                )}

                {decodedJWT.issuer && (
                  <div className="space-y-1">
                    <Label className="text-sm text-muted-foreground">签发者 (iss)</Label>
                    <div className="text-sm font-mono">{decodedJWT.issuer}</div>
                  </div>
                )}

                {decodedJWT.subject && (
                  <div className="space-y-1">
                    <Label className="text-sm text-muted-foreground">主体 (sub)</Label>
                    <div className="text-sm font-mono">{decodedJWT.subject}</div>
                  </div>
                )}

                {decodedJWT.audience && (
                  <div className="space-y-1">
                    <Label className="text-sm text-muted-foreground">受众 (aud)</Label>
                    <div className="text-sm font-mono">
                      {Array.isArray(decodedJWT.audience) 
                        ? decodedJWT.audience.join(', ')
                        : decodedJWT.audience
                      }
                    </div>
                  </div>
                )}
              </div>

              {/* 签名信息 */}
              <div className="mt-6 space-y-2">
                <Label className="text-sm text-muted-foreground">签名</Label>
                <div className="bg-muted p-3 rounded-lg">
                  <div className="font-mono text-xs break-all">
                    {decodedJWT.signature}
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(decodedJWT.signature)}
                  className="flex items-center gap-2"
                >
                  <Copy className="w-4 h-4" />
                  复制签名
                </Button>
              </div>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="generate" className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">生成 JWT Token</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Payload (JSON)</Label>
                <Textarea
                  value={newTokenPayload}
                  onChange={(e) => setNewTokenPayload(e.target.value)}
                  className="min-h-[200px] font-mono text-sm"
                />
              </div>
              
              <div className="space-y-2">
                <Label>密钥 (可选)</Label>
                <Input
                  type="password"
                  value={secretKey}
                  onChange={(e) => setSecretKey(e.target.value)}
                  placeholder="输入签名密钥，留空使用默认密钥"
                />
              </div>

              <Button onClick={handleGenerateToken} className="w-full">
                生成 JWT Token
              </Button>

              <div className="text-xs text-muted-foreground">
                <p>⚠️ 注意：这里生成的 JWT 仅用于演示目的，实际应用中请使用专业的 JWT 库。</p>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* JWT 说明 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">JWT 格式说明</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-2">
            <h4 className="font-medium text-red-600">Header</h4>
            <p className="text-sm text-muted-foreground">
              包含令牌类型和签名算法信息
            </p>
            <div className="bg-red-50 dark:bg-red-950 p-3 rounded text-xs font-mono">
              eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-blue-600">Payload</h4>
            <p className="text-sm text-muted-foreground">
              包含声明信息（用户数据和元数据）
            </p>
            <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded text-xs font-mono">
              eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIn0
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-green-600">Signature</h4>
            <p className="text-sm text-muted-foreground">
              用于验证令牌的完整性和真实性
            </p>
            <div className="bg-green-50 dark:bg-green-950 p-3 rounded text-xs font-mono">
              SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
            </div>
          </div>
        </div>

        <div className="mt-6">
          <h4 className="font-medium mb-2">常见声明 (Claims)</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>iss (Issuer)</strong> - 签发者
            </div>
            <div>
              <strong>sub (Subject)</strong> - 主体
            </div>
            <div>
              <strong>aud (Audience)</strong> - 受众
            </div>
            <div>
              <strong>exp (Expiration Time)</strong> - 过期时间
            </div>
            <div>
              <strong>nbf (Not Before)</strong> - 生效时间
            </div>
            <div>
              <strong>iat (Issued At)</strong> - 签发时间
            </div>
            <div>
              <strong>jti (JWT ID)</strong> - JWT 唯一标识
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default JwtDecoderTool
