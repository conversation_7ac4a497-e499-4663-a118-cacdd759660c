'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Languages, ArrowRightLeft, Copy, Volume2, History, BookOpen, Globe, RotateCcw } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface TranslationHistory {
  id: string
  sourceText: string
  translatedText: string
  sourceLang: string
  targetLang: string
  timestamp: Date
}

interface Language {
  code: string
  name: string
  nativeName: string
}

/**
 * 文本翻译器工具组件
 * 提供多语言翻译功能，支持历史记录和语音朗读
 */
export default function TextTranslatorTool() {
  const [sourceText, setSourceText] = useState('')
  const [translatedText, setTranslatedText] = useState('')
  const [sourceLang, setSourceLang] = useState('auto')
  const [targetLang, setTargetLang] = useState('zh')
  const [isTranslating, setIsTranslating] = useState(false)
  const [history, setHistory] = useState<TranslationHistory[]>([])
  const [detectedLang, setDetectedLang] = useState<string>('')
  const { toast } = useToast()

  // 支持的语言列表
  const languages: Language[] = useMemo(() => [
    { code: 'auto', name: '自动检测', nativeName: 'Auto Detect' },
    { code: 'zh', name: '中文', nativeName: '中文' },
    { code: 'en', name: '英语', nativeName: 'English' },
    { code: 'ja', name: '日语', nativeName: '日本語' },
    { code: 'ko', name: '韩语', nativeName: '한국어' },
    { code: 'fr', name: '法语', nativeName: 'Français' },
    { code: 'de', name: '德语', nativeName: 'Deutsch' },
    { code: 'es', name: '西班牙语', nativeName: 'Español' },
    { code: 'it', name: '意大利语', nativeName: 'Italiano' },
    { code: 'pt', name: '葡萄牙语', nativeName: 'Português' },
    { code: 'ru', name: '俄语', nativeName: 'Русский' },
    { code: 'ar', name: '阿拉伯语', nativeName: 'العربية' },
    { code: 'hi', name: '印地语', nativeName: 'हिन्दी' },
    { code: 'th', name: '泰语', nativeName: 'ไทย' },
    { code: 'vi', name: '越南语', nativeName: 'Tiếng Việt' },
    { code: 'tr', name: '土耳其语', nativeName: 'Türkçe' },
    { code: 'pl', name: '波兰语', nativeName: 'Polski' },
    { code: 'nl', name: '荷兰语', nativeName: 'Nederlands' },
    { code: 'sv', name: '瑞典语', nativeName: 'Svenska' },
    { code: 'da', name: '丹麦语', nativeName: 'Dansk' },
    { code: 'no', name: '挪威语', nativeName: 'Norsk' },
    { code: 'fi', name: '芬兰语', nativeName: 'Suomi' },
    { code: 'cs', name: '捷克语', nativeName: 'Čeština' },
    { code: 'hu', name: '匈牙利语', nativeName: 'Magyar' },
    { code: 'ro', name: '罗马尼亚语', nativeName: 'Română' },
    { code: 'bg', name: '保加利亚语', nativeName: 'Български' },
    { code: 'hr', name: '克罗地亚语', nativeName: 'Hrvatski' },
    { code: 'sk', name: '斯洛伐克语', nativeName: 'Slovenčina' },
    { code: 'sl', name: '斯洛文尼亚语', nativeName: 'Slovenščina' },
    { code: 'et', name: '爱沙尼亚语', nativeName: 'Eesti' },
    { code: 'lv', name: '拉脱维亚语', nativeName: 'Latviešu' },
    { code: 'lt', name: '立陶宛语', nativeName: 'Lietuvių' }
  ], [])

  // 获取语言名称
  const getLanguageName = useCallback((code: string) => {
    const lang = languages.find(l => l.code === code)
    return lang ? lang.name : code
  }, [languages])

  // 简单的语言检测（基于字符特征）
  const detectLanguage = useCallback((text: string): string => {
    if (!text.trim()) return 'unknown'
    
    // 中文检测
    if (/[\u4e00-\u9fff]/.test(text)) return 'zh'
    
    // 日文检测（平假名、片假名）
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return 'ja'
    
    // 韩文检测
    if (/[\uac00-\ud7af]/.test(text)) return 'ko'
    
    // 阿拉伯文检测
    if (/[\u0600-\u06ff]/.test(text)) return 'ar'
    
    // 俄文检测
    if (/[\u0400-\u04ff]/.test(text)) return 'ru'
    
    // 泰文检测
    if (/[\u0e00-\u0e7f]/.test(text)) return 'th'
    
    // 默认为英文
    return 'en'
  }, [])

  // 模拟翻译API（实际应用中应该调用真实的翻译服务）
  const translateText = useCallback(async (text: string, from: string, to: string): Promise<string> => {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000))
    
    // 简单的模拟翻译逻辑
    const translations: { [key: string]: { [key: string]: string } } = {
      'hello': {
        'zh': '你好',
        'ja': 'こんにちは',
        'ko': '안녕하세요',
        'fr': 'Bonjour',
        'de': 'Hallo',
        'es': 'Hola',
        'it': 'Ciao',
        'pt': 'Olá',
        'ru': 'Привет'
      },
      'world': {
        'zh': '世界',
        'ja': '世界',
        'ko': '세계',
        'fr': 'monde',
        'de': 'Welt',
        'es': 'mundo',
        'it': 'mondo',
        'pt': 'mundo',
        'ru': 'мир'
      },
      'thank you': {
        'zh': '谢谢',
        'ja': 'ありがとう',
        'ko': '감사합니다',
        'fr': 'Merci',
        'de': 'Danke',
        'es': 'Gracias',
        'it': 'Grazie',
        'pt': 'Obrigado',
        'ru': 'Спасибо'
      },
      '你好': {
        'en': 'Hello',
        'ja': 'こんにちは',
        'ko': '안녕하세요',
        'fr': 'Bonjour',
        'de': 'Hallo',
        'es': 'Hola'
      },
      '世界': {
        'en': 'World',
        'ja': '世界',
        'ko': '세계',
        'fr': 'Monde',
        'de': 'Welt',
        'es': 'Mundo'
      },
      '谢谢': {
        'en': 'Thank you',
        'ja': 'ありがとう',
        'ko': '감사합니다',
        'fr': 'Merci',
        'de': 'Danke',
        'es': 'Gracias'
      }
    }
    
    const lowerText = text.toLowerCase().trim()
    if (translations[lowerText] && translations[lowerText][to]) {
      return translations[lowerText][to]
    }
    
    // 如果没有找到对应翻译，返回模拟翻译结果
    return `[${to.toUpperCase()}] ${text}`
  }, [])

  // 执行翻译
  const handleTranslate = useCallback(async () => {
    if (!sourceText.trim()) {
      toast({
        title: '请输入文本',
        description: '请先输入要翻译的文本内容',
        variant: 'destructive'
      })
      return
    }

    if (sourceLang !== 'auto' && sourceLang === targetLang) {
      toast({
        title: '语言相同',
        description: '源语言和目标语言不能相同',
        variant: 'destructive'
      })
      return
    }

    setIsTranslating(true)
    
    try {
      // 检测源语言
      let detectedSourceLang = sourceLang
      if (sourceLang === 'auto') {
        detectedSourceLang = detectLanguage(sourceText)
        setDetectedLang(detectedSourceLang)
      }

      // 如果检测到的语言和目标语言相同，提示用户
      if (detectedSourceLang === targetLang) {
        toast({
          title: '语言相同',
          description: `检测到的源语言与目标语言相同（${getLanguageName(detectedSourceLang)}）`,
          variant: 'destructive'
        })
        setIsTranslating(false)
        return
      }

      // 执行翻译
      const result = await translateText(sourceText, detectedSourceLang, targetLang)
      setTranslatedText(result)

      // 添加到历史记录
      const historyItem: TranslationHistory = {
        id: Date.now().toString(),
        sourceText,
        translatedText: result,
        sourceLang: detectedSourceLang,
        targetLang,
        timestamp: new Date()
      }
      setHistory(prev => [historyItem, ...prev.slice(0, 19)]) // 保留最近20条记录

      toast({
        title: '翻译完成',
        description: `已将${getLanguageName(detectedSourceLang)}翻译为${getLanguageName(targetLang)}`
      })
    } catch (error) {
      console.error('翻译失败:', error)
      toast({
        title: '翻译失败',
        description: '翻译过程中出现错误，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsTranslating(false)
    }
  }, [sourceText, sourceLang, targetLang, translateText, detectLanguage, getLanguageName, toast])

  // 交换语言
  const swapLanguages = useCallback(() => {
    if (sourceLang === 'auto') {
      toast({
        title: '无法交换',
        description: '自动检测模式下无法交换语言',
        variant: 'destructive'
      })
      return
    }

    setSourceLang(targetLang)
    setTargetLang(sourceLang)
    setSourceText(translatedText)
    setTranslatedText(sourceText)
    setDetectedLang('')
  }, [sourceLang, targetLang, sourceText, translatedText, toast])

  // 复制文本
  const copyText = useCallback(async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: '复制成功',
        description: `已复制${type}到剪贴板`
      })
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法访问剪贴板',
        variant: 'destructive'
      })
    }
  }, [toast])

  // 语音朗读
  const speakText = useCallback((text: string, lang: string) => {
    if (!text.trim()) return

    if ('speechSynthesis' in window) {
      // 停止当前朗读
      speechSynthesis.cancel()
      
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.lang = lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : lang === 'ko' ? 'ko-KR' : lang
      utterance.rate = 0.8
      utterance.pitch = 1
      
      speechSynthesis.speak(utterance)
      
      toast({
        title: '开始朗读',
        description: `正在朗读${getLanguageName(lang)}文本`
      })
    } else {
      toast({
        title: '不支持语音',
        description: '您的浏览器不支持语音合成功能',
        variant: 'destructive'
      })
    }
  }, [getLanguageName, toast])

  // 从历史记录加载
  const loadFromHistory = useCallback((item: TranslationHistory) => {
    setSourceText(item.sourceText)
    setTranslatedText(item.translatedText)
    setSourceLang(item.sourceLang)
    setTargetLang(item.targetLang)
    setDetectedLang('')
  }, [])

  // 清空内容
  const clearAll = useCallback(() => {
    setSourceText('')
    setTranslatedText('')
    setDetectedLang('')
  }, [])

  // 清空历史记录
  const clearHistory = useCallback(() => {
    setHistory([])
    toast({
      title: '历史记录已清空',
      description: '所有翻译历史记录已被删除'
    })
  }, [toast])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Languages className="h-6 w-6" />
            文本翻译器
          </CardTitle>
          <CardDescription>
            多语言文本翻译工具，支持自动语言检测、历史记录和语音朗读
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="translator" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="translator">翻译器</TabsTrigger>
              <TabsTrigger value="history">历史记录</TabsTrigger>
            </TabsList>

            <TabsContent value="translator" className="space-y-6">
              {/* 语言选择 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                <div className="space-y-2">
                  <Label>源语言</Label>
                  <Select value={sourceLang} onValueChange={setSourceLang}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.map(lang => (
                        <SelectItem key={lang.code} value={lang.code}>
                          {lang.name} {lang.code !== 'auto' && `(${lang.nativeName})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {detectedLang && (
                    <Badge variant="secondary" className="text-xs">
                      检测到: {getLanguageName(detectedLang)}
                    </Badge>
                  )}
                </div>

                <div className="flex justify-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={swapLanguages}
                    disabled={sourceLang === 'auto'}
                    className="rounded-full"
                  >
                    <ArrowRightLeft className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label>目标语言</Label>
                  <Select value={targetLang} onValueChange={setTargetLang}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.filter(lang => lang.code !== 'auto').map(lang => (
                        <SelectItem key={lang.code} value={lang.code}>
                          {lang.name} ({lang.nativeName})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 翻译区域 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 源文本 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>原文</Label>
                    <div className="flex gap-2">
                      {sourceText && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => speakText(sourceText, sourceLang === 'auto' ? detectedLang : sourceLang)}
                          >
                            <Volume2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyText(sourceText, '原文')}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                  <Textarea
                    value={sourceText}
                    onChange={(e) => setSourceText(e.target.value)}
                    placeholder="请输入要翻译的文本..."
                    className="min-h-[200px] resize-none"
                  />
                  <div className="text-sm text-muted-foreground">
                    {sourceText.length} 字符
                  </div>
                </div>

                {/* 译文 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>译文</Label>
                    <div className="flex gap-2">
                      {translatedText && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => speakText(translatedText, targetLang)}
                          >
                            <Volume2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyText(translatedText, '译文')}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                  <Textarea
                    value={translatedText}
                    readOnly
                    placeholder="翻译结果将显示在这里..."
                    className="min-h-[200px] resize-none bg-muted/50"
                  />
                  <div className="text-sm text-muted-foreground">
                    {translatedText.length} 字符
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-2">
                <Button 
                  onClick={handleTranslate} 
                  disabled={isTranslating || !sourceText.trim()}
                  className="flex-1"
                >
                  <Languages className="h-4 w-4 mr-2" />
                  {isTranslating ? '翻译中...' : '翻译'}
                </Button>
                <Button variant="outline" onClick={clearAll}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  清空
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  <span className="font-medium">翻译历史</span>
                  <Badge variant="secondary">{history.length}</Badge>
                </div>
                {history.length > 0 && (
                  <Button variant="outline" size="sm" onClick={clearHistory}>
                    清空历史
                  </Button>
                )}
              </div>

              {history.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">暂无翻译历史</p>
                  <p className="text-sm text-muted-foreground">开始翻译后，历史记录将显示在这里</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {history.map((item) => (
                    <Card key={item.id} className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <CardContent className="p-4" onClick={() => loadFromHistory(item)}>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Globe className="h-4 w-4" />
                            <span>{getLanguageName(item.sourceLang)}</span>
                            <ArrowRightLeft className="h-3 w-3" />
                            <span>{getLanguageName(item.targetLang)}</span>
                            <span className="ml-auto">
                              {item.timestamp.toLocaleString()}
                            </span>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">原文</p>
                              <p className="text-sm line-clamp-2">{item.sourceText}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-muted-foreground">译文</p>
                              <p className="text-sm line-clamp-2">{item.translatedText}</p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
