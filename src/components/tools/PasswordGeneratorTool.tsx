'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { Copy, Check, RefreshCw, <PERSON>ting<PERSON>, Shield, AlertTriangle, CheckCircle, X } from 'lucide-react'

interface PasswordSettings {
  length: number
  uppercase: boolean
  lowercase: boolean
  numbers: boolean
  symbols: boolean
  excludeSimilar: boolean
  excludeAmbiguous: boolean
  customSymbols: string
}

interface PasswordStrength {
  score: number
  label: string
  color: string
  tips: string[]
}

interface GeneratedPassword {
  value: string
  timestamp: number
  strength: number
}

export default function PasswordGeneratorTool() {
  const [password, setPassword] = useState('')
  const [copied, setCopied] = useState(false)
  const [strength, setStrength] = useState<PasswordStrength>({
    score: 0,
    label: '非常弱',
    color: 'red',
    tips: [],
  })
  const [history, setHistory] = useState<GeneratedPassword[]>([])
  const [showSettings, setShowSettings] = useState(true)
  const [settings, setSettings] = useState<PasswordSettings>({
    length: 16,
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: true,
    excludeSimilar: false,
    excludeAmbiguous: false,
    customSymbols: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  })

  // 字符集
  const charSets = useMemo(() => ({
    uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    lowercase: 'abcdefghijklmnopqrstuvwxyz',
    numbers: '0123456789',
    similar: 'il1Lo0O',
    ambiguous: '{}[]()/\\\'"`~,;.<>',
  }), [])

  // 生成密码
  const generatePassword = useCallback(() => {
    let chars = ''
    let requiredChars = []

    // 构建字符集
    if (settings.lowercase) {
      let lowercaseChars = charSets.lowercase
      if (settings.excludeSimilar) {
        lowercaseChars = lowercaseChars.replace(/[ilo]/g, '')
      }
      chars += lowercaseChars
      requiredChars.push(lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length)))
    }

    if (settings.uppercase) {
      let uppercaseChars = charSets.uppercase
      if (settings.excludeSimilar) {
        uppercaseChars = uppercaseChars.replace(/[ILO]/g, '')
      }
      chars += uppercaseChars
      requiredChars.push(uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length)))
    }

    if (settings.numbers) {
      let numberChars = charSets.numbers
      if (settings.excludeSimilar) {
        numberChars = numberChars.replace(/[01]/g, '')
      }
      chars += numberChars
      requiredChars.push(numberChars.charAt(Math.floor(Math.random() * numberChars.length)))
    }

    if (settings.symbols) {
      let symbolChars = settings.customSymbols
      if (settings.excludeAmbiguous) {
        // 移除歧义字符
        charSets.ambiguous.split('').forEach(char => {
          symbolChars = symbolChars.replace(new RegExp(`\\${char}`, 'g'), '')
        })
      }
      if (symbolChars.length > 0) {
        chars += symbolChars
        requiredChars.push(symbolChars.charAt(Math.floor(Math.random() * symbolChars.length)))
      }
    }

    if (chars.length === 0) {
      setPassword('请至少选择一种字符类型')
      return
    }

    // 生成剩余字符
    let result = []
    const remainingLength = settings.length - requiredChars.length

    for (let i = 0; i < remainingLength; i++) {
      result.push(chars.charAt(Math.floor(Math.random() * chars.length)))
    }

    // 合并必需字符和随机字符
    result = [...result, ...requiredChars]

    // 打乱顺序
    for (let i = result.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[result[i], result[j]] = [result[j], result[i]]
    }

    const newPassword = result.join('')
    setPassword(newPassword)

    // 计算密码强度
    const newStrength = calculateStrength(newPassword)
    setStrength(newStrength)

    // 添加到历史记录
    const newHistory: GeneratedPassword = {
      value: newPassword,
      timestamp: Date.now(),
      strength: newStrength.score,
    }
    setHistory(prev => [newHistory, ...prev.slice(0, 19)])
  }, [settings, charSets])

  // 计算密码强度
  const calculateStrength = (pwd: string): PasswordStrength => {
    let score = 0
    const tips: string[] = []

    // 长度评分
    if (pwd.length >= 20) score += 30
    else if (pwd.length >= 16) score += 25
    else if (pwd.length >= 12) score += 20
    else if (pwd.length >= 8) score += 10
    else tips.push('密码长度至少应为8个字符')

    // 字符类型评分
    const hasLower = /[a-z]/.test(pwd)
    const hasUpper = /[A-Z]/.test(pwd)
    const hasNumber = /[0-9]/.test(pwd)
    const hasSymbol = /[^a-zA-Z0-9]/.test(pwd)

    const typeCount = [hasLower, hasUpper, hasNumber, hasSymbol].filter(Boolean).length
    score += typeCount * 15

    if (!hasLower) tips.push('添加小写字母')
    if (!hasUpper) tips.push('添加大写字母')
    if (!hasNumber) tips.push('添加数字')
    if (!hasSymbol) tips.push('添加特殊符号')

    // 连续字符检测
    const hasSequential = /(.)\1{2,}/.test(pwd) || /012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz/i.test(pwd)
    if (hasSequential) {
      score -= 10
      tips.push('避免使用连续或重复的字符')
    }

    // 常见密码模式检测
    const commonPatterns = /^[a-zA-Z]+[0-9]+$|^[0-9]+[a-zA-Z]+$|password|123456|qwerty/i
    if (commonPatterns.test(pwd)) {
      score -= 20
      tips.push('避免使用常见的密码模式')
    }

    // 确定强度等级
    let label = '非常弱'
    let color = 'red'

    if (score >= 80) {
      label = '非常强'
      color = 'green'
    } else if (score >= 60) {
      label = '强'
      color = 'green'
    } else if (score >= 40) {
      label = '中等'
      color = 'yellow'
    } else if (score >= 20) {
      label = '弱'
      color = 'orange'
    }

    return { score: Math.min(100, Math.max(0, score)), label, color, tips }
  }

  // 复制到剪贴板
  const copyToClipboard = () => {
    navigator.clipboard.writeText(password).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  // 初始生成
  useEffect(() => {
    generatePassword()
  }, [generatePassword])

  // 设置变化时重新生成
  useEffect(() => {
    generatePassword()
  }, [generatePassword])

  // 获取强度条颜色
  const getStrengthColor = () => {
    switch (strength.color) {
      case 'red': return 'bg-red-500'
      case 'orange': return 'bg-orange-500'
      case 'yellow': return 'bg-yellow-500'
      case 'green': return 'bg-green-500'
      default: return 'bg-gray-300'
    }
  }

  // 格式化时间戳
  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">密码生成器</h2>
        <p className="text-gray-600">
          生成安全、随机的强密码，保护您的账户安全
        </p>
      </div>

      {/* 生成的密码 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <input
            type="text"
            value={password}
            readOnly
            className="flex-1 px-4 py-3 bg-gray-50 border border-gray-200 rounded-md font-mono text-lg"
          />
          <button
            onClick={copyToClipboard}
            className="p-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            {copied ? <Check className="w-5 h-5" /> : <Copy className="w-5 h-5" />}
          </button>
          <button
            onClick={generatePassword}
            className="p-3 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>

        {/* 密码强度 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">密码强度</span>
            <span className={`text-sm font-medium ${
              strength.color === 'red' ? 'text-red-600' :
              strength.color === 'orange' ? 'text-orange-600' :
              strength.color === 'yellow' ? 'text-yellow-600' :
              'text-green-600'
            }`}>
              {strength.label}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor()}`}
              style={{ width: `${strength.score}%` }}
            />
          </div>
          
          {/* 强度提示 */}
          {strength.tips.length > 0 && (
            <div className="mt-3 p-3 bg-yellow-50 rounded-md">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                <div className="text-sm text-yellow-900">
                  <p className="font-medium mb-1">改进建议：</p>
                  <ul className="list-disc list-inside space-y-0.5">
                    {strength.tips.map((tip, index) => (
                      <li key={index}>{tip}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 设置面板 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="flex items-center justify-between w-full mb-4"
        >
          <h3 className="font-medium flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            密码设置
          </h3>
          <span className="text-gray-400 text-sm">{showSettings ? '收起' : '展开'}</span>
        </button>

        {showSettings && (
          <div className="space-y-4">
            {/* 密码长度 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                密码长度：{settings.length} 个字符
              </label>
              <input
                type="range"
                min="6"
                max="128"
                value={settings.length}
                onChange={(e) => setSettings({ ...settings, length: parseInt(e.target.value) })}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>6</span>
                <span>32</span>
                <span>64</span>
                <span>96</span>
                <span>128</span>
              </div>
            </div>

            {/* 字符类型 */}
            <div className="grid grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.uppercase}
                  onChange={(e) => setSettings({ ...settings, uppercase: e.target.checked })}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium">大写字母</div>
                  <div className="text-xs text-gray-500">A-Z</div>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.lowercase}
                  onChange={(e) => setSettings({ ...settings, lowercase: e.target.checked })}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium">小写字母</div>
                  <div className="text-xs text-gray-500">a-z</div>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.numbers}
                  onChange={(e) => setSettings({ ...settings, numbers: e.target.checked })}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium">数字</div>
                  <div className="text-xs text-gray-500">0-9</div>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.symbols}
                  onChange={(e) => setSettings({ ...settings, symbols: e.target.checked })}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium">特殊符号</div>
                  <div className="text-xs text-gray-500">!@#$%...</div>
                </div>
              </label>
            </div>

            {/* 高级选项 */}
            <div className="border-t pt-4 space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.excludeSimilar}
                  onChange={(e) => setSettings({ ...settings, excludeSimilar: e.target.checked })}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium">排除相似字符</div>
                  <div className="text-xs text-gray-500">不包含 i, l, 1, L, o, 0, O</div>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.excludeAmbiguous}
                  onChange={(e) => setSettings({ ...settings, excludeAmbiguous: e.target.checked })}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium">排除歧义符号</div>
                  <div className="text-xs text-gray-500">不包含 { } [ ] ( ) / \ &apos; &quot; ` ~ , ; . &lt; &gt;</div>
                </div>
              </label>
            </div>

            {/* 自定义符号 */}
            {settings.symbols && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  自定义符号集
                </label>
                <input
                  type="text"
                  value={settings.customSymbols}
                  onChange={(e) => setSettings({ ...settings, customSymbols: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="输入要包含的特殊符号"
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* 历史记录 */}
      {history.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="font-medium mb-4 flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            生成历史
          </h3>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {history.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                <div className="flex-1 flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
                    item.strength >= 80 ? 'bg-green-500' :
                    item.strength >= 60 ? 'bg-green-400' :
                    item.strength >= 40 ? 'bg-yellow-500' :
                    item.strength >= 20 ? 'bg-orange-500' :
                    'bg-red-500'
                  }`} />
                  <div className="font-mono text-sm break-all">{item.value}</div>
                </div>
                <div className="flex items-center gap-2 ml-2">
                  <span className="text-xs text-gray-500">{formatTimestamp(item.timestamp)}</span>
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(item.value)
                      setCopied(true)
                      setTimeout(() => setCopied(false), 2000)
                    }}
                    className="p-1 hover:bg-gray-200 rounded"
                  >
                    <Copy className="w-4 h-4 text-gray-400" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 安全提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Shield className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">密码安全提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>使用至少12个字符的密码，16个或更多字符更安全</li>
              <li>包含大小写字母、数字和特殊符号的组合</li>
              <li>避免使用个人信息、常见单词或键盘模式</li>
              <li>为每个账户使用不同的密码</li>
              <li>定期更换重要账户的密码</li>
              <li>考虑使用密码管理器来安全存储密码</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
