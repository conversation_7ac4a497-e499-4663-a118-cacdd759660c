'use client'

import React, { useState, use<PERSON><PERSON>back, useRef, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { toast } from '@/hooks/use-toast'
import { 
  Upload, 
  Download, 
  Trash2, 
  Settings, 
  Image as ImageIcon,
  Monitor,
  Smartphone,
  Tablet,
  Square,
  Maximize,
  Minimize,
  RotateCw,
  Crop,
  Info,
  CheckCircle,
  AlertCircle,
  Lock,
  Unlock,
  RefreshCw,
  FileImage,
  <PERSON><PERSON>,
  Zap
} from 'lucide-react'

interface ImageFile {
  id: string
  file: File
  originalUrl: string
  processedUrl?: string
  originalWidth: number
  originalHeight: number
  newWidth: number
  newHeight: number
  aspectRatio: number
  fileSize: number
  newFileSize?: number
  status: 'pending' | 'processing' | 'completed' | 'error'
  error?: string
}

interface ResizeSettings {
  width: number
  height: number
  maintainAspectRatio: boolean
  resizeMode: 'exact' | 'fit' | 'fill' | 'stretch'
  quality: number
  format: 'original' | 'jpeg' | 'png' | 'webp'
  addSuffix: boolean
  suffix: string
  backgroundColor: string
}

const PRESET_SIZES = {
  'avatar-small': { name: '头像 (小)', width: 64, height: 64 },
  'avatar-medium': { name: '头像 (中)', width: 128, height: 128 },
  'avatar-large': { name: '头像 (大)', width: 256, height: 256 },
  'thumbnail': { name: '缩略图', width: 200, height: 200 },
  'social-facebook': { name: 'Facebook 封面', width: 1200, height: 630 },
  'social-twitter': { name: 'Twitter 标题', width: 1500, height: 500 },
  'social-instagram': { name: 'Instagram 正方形', width: 1080, height: 1080 },
  'mobile-1x': { name: '移动端 1x', width: 375, height: 667 },
  'mobile-2x': { name: '移动端 2x', width: 750, height: 1334 },
  'desktop-hd': { name: '桌面 HD', width: 1920, height: 1080 },
  'desktop-4k': { name: '桌面 4K', width: 3840, height: 2160 },
  'print-a4': { name: '打印 A4 (300DPI)', width: 2480, height: 3508 },
  'web-banner': { name: '网页横幅', width: 1200, height: 400 },
  'blog-featured': { name: '博客特色图', width: 1200, height: 675 }
}

const RESIZE_MODES = {
  'exact': {
    name: '精确尺寸',
    description: '强制调整到指定尺寸，可能改变宽高比'
  },
  'fit': {
    name: '适应尺寸',
    description: '按比例缩放，确保图片完全包含在指定尺寸内'
  },
  'fill': {
    name: '填充尺寸',
    description: '按比例缩放并裁剪，填满指定尺寸'
  },
  'stretch': {
    name: '拉伸填满',
    description: '拉伸图片填满指定尺寸，忽略宽高比'
  }
}

const FORMAT_OPTIONS = [
  { value: 'original', label: '原始格式', description: '保持原始文件格式' },
  { value: 'jpeg', label: 'JPEG', description: '适合照片，较小文件大小' },
  { value: 'png', label: 'PNG', description: '支持透明度，无损压缩' },
  { value: 'webp', label: 'WebP', description: '现代格式，最佳压缩比' }
]

export default function ImageResizerTool() {
  const [images, setImages] = useState<ImageFile[]>([])
  const [settings, setSettings] = useState<ResizeSettings>({
    width: 800,
    height: 600,
    maintainAspectRatio: true,
    resizeMode: 'fit',
    quality: 90,
    format: 'original',
    addSuffix: true,
    suffix: '_resized',
    backgroundColor: '#ffffff'
  })
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return

    const validFiles = Array.from(files).filter(file => {
      if (!file.type.startsWith('image/')) {
        toast({
          title: "文件类型错误",
          description: `"${file.name}" 不是有效的图片文件`,
          variant: "destructive",
        })
        return false
      }
      if (file.size > 50 * 1024 * 1024) { // 50MB limit
        toast({
          title: "文件过大",
          description: `"${file.name}" 超过50MB限制`,
          variant: "destructive",
        })
        return false
      }
      return true
    })

    validFiles.forEach(file => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const img = new Image()
        img.onload = () => {
          const imageFile: ImageFile = {
            id: `${Date.now()}-${Math.random()}`,
            file,
            originalUrl: e.target?.result as string,
            originalWidth: img.width,
            originalHeight: img.height,
            newWidth: img.width,
            newHeight: img.height,
            aspectRatio: img.width / img.height,
            fileSize: file.size,
            status: 'pending'
          }
          setImages(prev => [...prev, imageFile])
        }
        img.src = e.target?.result as string
      }
      reader.readAsDataURL(file)
    })
  }, [])

  const removeImage = useCallback((id: string) => {
    setImages(prev => prev.filter(img => img.id !== id))
    if (selectedImageId === id) {
      setSelectedImageId(null)
    }
  }, [selectedImageId])

  const clearAllImages = useCallback(() => {
    setImages([])
    setSelectedImageId(null)
  }, [])

  const updateSettings = useCallback((key: keyof ResizeSettings, value: any) => {
    setSettings(prev => {
      const newSettings = { ...prev, [key]: value }
      
      // 如果开启宽高比锁定，自动计算另一个维度
      if (key === 'width' && newSettings.maintainAspectRatio && selectedImageId) {
        const selectedImage = images.find(img => img.id === selectedImageId)
        if (selectedImage) {
          newSettings.height = Math.round(value / selectedImage.aspectRatio)
        }
      } else if (key === 'height' && newSettings.maintainAspectRatio && selectedImageId) {
        const selectedImage = images.find(img => img.id === selectedImageId)
        if (selectedImage) {
          newSettings.width = Math.round(value * selectedImage.aspectRatio)
        }
      }
      
      return newSettings
    })
  }, [selectedImageId, images])

  const applyPresetSize = useCallback((presetKey: keyof typeof PRESET_SIZES) => {
    const preset = PRESET_SIZES[presetKey]
    updateSettings('width', preset.width)
    updateSettings('height', preset.height)
  }, [updateSettings])

  const resizeImage = useCallback((imageFile: ImageFile, canvas: HTMLCanvasElement): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('无法获取canvas上下文'))
          return
        }

        let { width: targetWidth, height: targetHeight } = settings
        let sourceX = 0, sourceY = 0, sourceWidth = img.width, sourceHeight = img.height

        // 根据调整模式计算最终尺寸和裁剪区域
        switch (settings.resizeMode) {
          case 'exact':
            // 精确尺寸：直接使用目标尺寸
            break
            
          case 'fit':
            // 适应尺寸：按比例缩放，保持完整图片
            const fitRatio = Math.min(targetWidth / img.width, targetHeight / img.height)
            targetWidth = Math.round(img.width * fitRatio)
            targetHeight = Math.round(img.height * fitRatio)
            break
            
          case 'fill':
            // 填充尺寸：按比例缩放并裁剪
            const fillRatio = Math.max(targetWidth / img.width, targetHeight / img.height)
            const scaledWidth = img.width * fillRatio
            const scaledHeight = img.height * fillRatio
            
            sourceX = (img.width - targetWidth / fillRatio) / 2
            sourceY = (img.height - targetHeight / fillRatio) / 2
            sourceWidth = targetWidth / fillRatio
            sourceHeight = targetHeight / fillRatio
            break
            
          case 'stretch':
            // 拉伸填满：直接拉伸到目标尺寸
            break
        }

        // 设置画布尺寸
        canvas.width = targetWidth
        canvas.height = targetHeight

        // 如果不是PNG格式或原始图片不支持透明度，设置背景色
        if (settings.format !== 'png' && settings.format !== 'original') {
          ctx.fillStyle = settings.backgroundColor
          ctx.fillRect(0, 0, targetWidth, targetHeight)
        }

        // 启用图像平滑
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'

        // 绘制调整后的图片
        ctx.drawImage(
          img,
          sourceX, sourceY, sourceWidth, sourceHeight,
          0, 0, targetWidth, targetHeight
        )

        // 根据设置的格式和质量导出
        const outputFormat = settings.format === 'original' 
          ? imageFile.file.type 
          : `image/${settings.format}`
        
        const quality = settings.quality / 100

        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('图片处理失败'))
          }
        }, outputFormat, quality)
      }
      
      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = imageFile.originalUrl
    })
  }, [settings])

  const processImage = useCallback(async (imageFile: ImageFile): Promise<ImageFile> => {
    if (!canvasRef.current) {
      throw new Error('Canvas未就绪')
    }

    try {
      const blob = await resizeImage(imageFile, canvasRef.current)
      const processedUrl = URL.createObjectURL(blob)
      
      return {
        ...imageFile,
        processedUrl,
        newWidth: settings.width,
        newHeight: settings.height,
        newFileSize: blob.size,
        status: 'completed'
      }
    } catch (error) {
      return {
        ...imageFile,
        status: 'error',
        error: error instanceof Error ? error.message : '处理失败'
      }
    }
  }, [resizeImage, settings])

  const processAllImages = useCallback(async () => {
    if (images.length === 0) {
      toast({
        title: "没有图片",
        description: "请先上传要处理的图片",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setProcessingProgress(0)

    try {
      const totalImages = images.length
      const processedImages: ImageFile[] = []

      for (let i = 0; i < totalImages; i++) {
        const image = images[i]
        setImages(prev => prev.map(img => 
          img.id === image.id ? { ...img, status: 'processing' } : img
        ))

        const processedImage = await processImage(image)
        processedImages.push(processedImage)
        
        setImages(prev => prev.map(img => 
          img.id === image.id ? processedImage : img
        ))

        setProcessingProgress(((i + 1) / totalImages) * 100)
      }

      const successCount = processedImages.filter(img => img.status === 'completed').length
      const errorCount = processedImages.filter(img => img.status === 'error').length

      toast({
        title: "处理完成",
        description: `成功处理 ${successCount} 张图片${errorCount > 0 ? `，${errorCount} 张失败` : ''}`,
      })
    } catch (error) {
      toast({
        title: "处理失败",
        description: error instanceof Error ? error.message : '未知错误',
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
      setProcessingProgress(0)
    }
  }, [images, processImage])

  const downloadImage = useCallback((imageFile: ImageFile) => {
    if (!imageFile.processedUrl) return

    const link = document.createElement('a')
    link.href = imageFile.processedUrl
    
    const originalName = imageFile.file.name
    const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'))
    const extension = settings.format === 'original' 
      ? originalName.substring(originalName.lastIndexOf('.'))
      : `.${settings.format}`
    
    const suffix = settings.addSuffix ? settings.suffix : ''
    link.download = `${nameWithoutExt}${suffix}${extension}`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, [settings])

  const downloadAllImages = useCallback(async () => {
    const completedImages = images.filter(img => img.status === 'completed' && img.processedUrl)
    
    if (completedImages.length === 0) {
      toast({
        title: "没有可下载的图片",
        description: "请先处理图片",
        variant: "destructive",
      })
      return
    }

    // 如果只有一张图片，直接下载
    if (completedImages.length === 1) {
      downloadImage(completedImages[0])
      return
    }

    // 多张图片打包下载（简化版：依次下载）
    for (const image of completedImages) {
      downloadImage(image)
      // 添加小延迟避免浏览器阻止多个下载
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    toast({
      title: "下载完成",
      description: `已下载 ${completedImages.length} 张图片`,
    })
  }, [images, downloadImage])

  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }, [])

  const calculateCompressionRatio = useCallback((original: number, compressed: number): string => {
    if (!compressed || compressed >= original) return '0%'
    const ratio = ((original - compressed) / original) * 100
    return `${ratio.toFixed(1)}%`
  }, [])

  // 选择图片时自动更新设置中的尺寸
  useEffect(() => {
    if (selectedImageId) {
      const selectedImage = images.find(img => img.id === selectedImageId)
      if (selectedImage && settings.maintainAspectRatio) {
        const newHeight = Math.round(settings.width / selectedImage.aspectRatio)
        if (newHeight !== settings.height) {
          setSettings(prev => ({ ...prev, height: newHeight }))
        }
      }
    }
  }, [selectedImageId, images, settings.width, settings.maintainAspectRatio])

  return (
    <div className="w-full max-w-7xl mx-auto p-4 space-y-6">
      <canvas ref={canvasRef} style={{ display: 'none' }} />
      
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          图片尺寸调整工具
        </h1>
        <p className="text-gray-600">
          批量调整图片大小，支持多种调整模式、格式转换和质量控制
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 设置面板 */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Settings className="w-4 h-4" />
                调整设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 预设尺寸 */}
              <div className="space-y-2">
                <Label className="text-xs">快速预设</Label>
                <Select onValueChange={applyPresetSize}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="选择预设尺寸" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(PRESET_SIZES).map(([key, preset]) => (
                      <SelectItem key={key} value={key} className="text-xs">
                        {preset.name} ({preset.width}×{preset.height})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              {/* 尺寸设置 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label className="text-xs">保持宽高比</Label>
                  <Switch
                    checked={settings.maintainAspectRatio}
                    onCheckedChange={(checked) => updateSettings('maintainAspectRatio', checked)}
                  />
                  {settings.maintainAspectRatio ? (
                    <Lock className="w-3 h-3 text-blue-500" />
                  ) : (
                    <Unlock className="w-3 h-3 text-gray-400" />
                  )}
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-1">
                    <Label className="text-xs">宽度 (px)</Label>
                    <Input
                      type="number"
                      value={settings.width}
                      onChange={(e) => updateSettings('width', parseInt(e.target.value) || 0)}
                      className="h-8 text-xs"
                      min="1"
                      max="8192"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs">高度 (px)</Label>
                    <Input
                      type="number"
                      value={settings.height}
                      onChange={(e) => updateSettings('height', parseInt(e.target.value) || 0)}
                      className="h-8 text-xs"
                      min="1"
                      max="8192"
                    />
                  </div>
                </div>
              </div>

              {/* 调整模式 */}
              <div className="space-y-2">
                <Label className="text-xs">调整模式</Label>
                <Select value={settings.resizeMode} onValueChange={(value) => updateSettings('resizeMode', value)}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(RESIZE_MODES).map(([key, mode]) => (
                      <SelectItem key={key} value={key} className="text-xs">
                        <div>
                          <div className="font-medium">{mode.name}</div>
                          <div className="text-gray-500 text-xs">{mode.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 输出格式 */}
              <div className="space-y-2">
                <Label className="text-xs">输出格式</Label>
                <Select value={settings.format} onValueChange={(value) => updateSettings('format', value)}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FORMAT_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value} className="text-xs">
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-gray-500 text-xs">{option.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 质量设置 */}
              {(settings.format === 'jpeg' || settings.format === 'webp') && (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="text-xs">图片质量</Label>
                    <span className="text-xs text-gray-500">{settings.quality}%</span>
                  </div>
                  <Slider
                    value={[settings.quality]}
                    onValueChange={(value) => updateSettings('quality', value[0])}
                    min={10}
                    max={100}
                    step={5}
                    className="w-full"
                  />
                </div>
              )}

              {/* 背景颜色 */}
              {(settings.resizeMode === 'fit' || settings.format === 'jpeg') && (
                <div className="space-y-2">
                  <Label className="text-xs">背景颜色</Label>
                  <div className="flex gap-2">
                    <Input
                      value={settings.backgroundColor}
                      onChange={(e) => updateSettings('backgroundColor', e.target.value)}
                      className="h-8 text-xs flex-1"
                    />
                    <input
                      type="color"
                      value={settings.backgroundColor}
                      onChange={(e) => updateSettings('backgroundColor', e.target.value)}
                      className="w-8 h-8 rounded border border-gray-300"
                    />
                  </div>
                </div>
              )}

              {/* 文件名设置 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label className="text-xs">添加后缀</Label>
                  <Switch
                    checked={settings.addSuffix}
                    onCheckedChange={(checked) => updateSettings('addSuffix', checked)}
                  />
                </div>
                {settings.addSuffix && (
                  <Input
                    value={settings.suffix}
                    onChange={(e) => updateSettings('suffix', e.target.value)}
                    placeholder="_resized"
                    className="h-8 text-xs"
                  />
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 图片管理和预览区域 */}
        <div className="lg:col-span-2 space-y-4">
          {/* 上传区域 */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Upload className="w-4 h-4" />
                  图片上传
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    size="sm"
                    className="text-xs"
                  >
                    <Upload className="w-3 h-3 mr-1" />
                    选择图片
                  </Button>
                  {images.length > 0 && (
                    <Button
                      onClick={clearAllImages}
                      variant="outline"
                      size="sm"
                      className="text-xs"
                    >
                      <Trash2 className="w-3 h-3 mr-1" />
                      清空
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleFileSelect(e.target.files)}
                className="hidden"
              />
              
              <div 
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
                onDrop={(e) => {
                  e.preventDefault()
                  handleFileSelect(e.dataTransfer.files)
                }}
                onDragOver={(e) => e.preventDefault()}
              >
                <ImageIcon className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-600 mb-2">
                  拖拽图片到此处或 <button 
                    onClick={() => fileInputRef.current?.click()}
                    className="text-blue-500 hover:underline"
                  >
                    点击选择
                  </button>
                </p>
                <p className="text-xs text-gray-500">
                  支持 JPG、PNG、WebP、GIF 格式，单个文件最大 50MB
                </p>
              </div>

              {images.length > 0 && (
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      已选择 {images.length} 张图片
                    </span>
                    <div className="flex gap-2">
                      <Button
                        onClick={processAllImages}
                        disabled={isProcessing}
                        size="sm"
                        className="text-xs"
                      >
                        {isProcessing ? (
                          <>
                            <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                            处理中...
                          </>
                        ) : (
                          <>
                            <Zap className="w-3 h-3 mr-1" />
                            批量处理
                          </>
                        )}
                      </Button>
                      <Button
                        onClick={downloadAllImages}
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        disabled={!images.some(img => img.status === 'completed')}
                      >
                        <Download className="w-3 h-3 mr-1" />
                        全部下载
                      </Button>
                    </div>
                  </div>
                  
                  {isProcessing && (
                    <div className="space-y-2">
                      <Progress value={processingProgress} className="w-full" />
                      <p className="text-sm text-gray-600 text-center">
                        处理进度: {Math.round(processingProgress)}%
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 图片列表 */}
          {images.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <FileImage className="w-4 h-4" />
                  图片列表
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {images.map((image) => (
                    <div
                      key={image.id}
                      className={`p-3 border rounded-lg transition-all cursor-pointer ${
                        selectedImageId === image.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedImageId(image.id)}
                    >
                      <div className="flex items-center gap-3">
                        {/* 缩略图 */}
                        <div className="relative">
                          <img
                            src={image.originalUrl}
                            alt={image.file.name}
                            className="w-16 h-16 object-cover rounded border"
                          />
                          {image.status === 'completed' && (
                            <CheckCircle className="absolute -top-1 -right-1 w-4 h-4 text-green-500 bg-white rounded-full" />
                          )}
                          {image.status === 'error' && (
                            <AlertCircle className="absolute -top-1 -right-1 w-4 h-4 text-red-500 bg-white rounded-full" />
                          )}
                          {image.status === 'processing' && (
                            <RefreshCw className="absolute -top-1 -right-1 w-4 h-4 text-blue-500 bg-white rounded-full animate-spin" />
                          )}
                        </div>

                        {/* 信息 */}
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">
                            {image.file.name}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                            <span>{image.originalWidth}×{image.originalHeight}</span>
                            <span>{formatFileSize(image.fileSize)}</span>
                            <Badge variant={
                              image.status === 'completed' ? 'default' :
                              image.status === 'error' ? 'destructive' :
                              image.status === 'processing' ? 'secondary' : 'outline'
                            } className="text-xs">
                              {image.status === 'pending' && '待处理'}
                              {image.status === 'processing' && '处理中'}
                              {image.status === 'completed' && '已完成'}
                              {image.status === 'error' && '失败'}
                            </Badge>
                          </div>
                          
                          {image.status === 'completed' && image.newFileSize && (
                            <div className="text-xs text-green-600 mt-1">
                              {image.newWidth}×{image.newHeight} • {formatFileSize(image.newFileSize)} 
                              • 压缩 {calculateCompressionRatio(image.fileSize, image.newFileSize)}
                            </div>
                          )}
                          
                          {image.status === 'error' && image.error && (
                            <div className="text-xs text-red-600 mt-1">
                              错误: {image.error}
                            </div>
                          )}
                        </div>

                        {/* 操作按钮 */}
                        <div className="flex gap-1">
                          {image.status === 'completed' && (
                            <Button
                              onClick={(e) => {
                                e.stopPropagation()
                                downloadImage(image)
                              }}
                              size="sm"
                              variant="outline"
                              className="text-xs"
                            >
                              <Download className="w-3 h-3" />
                            </Button>
                          )}
                          <Button
                            onClick={(e) => {
                              e.stopPropagation()
                              removeImage(image.id)
                            }}
                            size="sm"
                            variant="outline"
                            className="text-xs text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 预览对比 */}
          {selectedImageId && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Monitor className="w-4 h-4" />
                  预览对比
                </CardTitle>
              </CardHeader>
              <CardContent>
                {(() => {
                  const selectedImage = images.find(img => img.id === selectedImageId)
                  if (!selectedImage) return null

                  return (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* 原图 */}
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">原图</h4>
                        <div className="border rounded-lg p-2 bg-gray-50">
                          <img
                            src={selectedImage.originalUrl}
                            alt="原图"
                            className="w-full h-48 object-contain rounded"
                          />
                          <div className="text-xs text-gray-600 mt-2 text-center">
                            {selectedImage.originalWidth}×{selectedImage.originalHeight} • {formatFileSize(selectedImage.fileSize)}
                          </div>
                        </div>
                      </div>

                      {/* 处理后 */}
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">处理后预览</h4>
                        <div className="border rounded-lg p-2 bg-gray-50">
                          {selectedImage.processedUrl ? (
                            <>
                              <img
                                src={selectedImage.processedUrl}
                                alt="处理后"
                                className="w-full h-48 object-contain rounded"
                              />
                              <div className="text-xs text-gray-600 mt-2 text-center">
                                {selectedImage.newWidth}×{selectedImage.newHeight} • {formatFileSize(selectedImage.newFileSize || 0)}
                              </div>
                            </>
                          ) : (
                            <div className="w-full h-48 flex items-center justify-center text-gray-400 text-sm rounded bg-gray-100">
                              {selectedImage.status === 'processing' ? '处理中...' : '等待处理'}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })()}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}