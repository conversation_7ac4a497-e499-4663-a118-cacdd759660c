'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { Calculator, User, Ruler, Weight, Info, TrendingUp, TrendingDown } from 'lucide-react'

interface BmiCategory {
  min: number
  max: number
  name: string
  color: string
  description: string
}

interface BmiRecord {
  date: string
  height: number
  weight: number
  bmi: number
  category: string
}

export default function BmiCalculatorTool() {
  const [height, setHeight] = useState('')
  const [weight, setWeight] = useState('')
  const [unit, setUnit] = useState<'metric' | 'imperial'>('metric')
  const [bmi, setBmi] = useState<number | null>(null)
  const [category, setCategory] = useState<BmiCategory | null>(null)
  const [idealWeight, setIdealWeight] = useState<{ min: number; max: number } | null>(null)
  const [history, setHistory] = useState<BmiRecord[]>([])

  // BMI 分类标准
  const bmiCategories: BmiCategory[] = useMemo(() => [
    {
      min: 0,
      max: 18.5,
      name: '体重过轻',
      color: 'text-blue-600',
      description: '您的体重低于正常范围，建议适当增加营养摄入。',
    },
    {
      min: 18.5,
      max: 24,
      name: '正常体重',
      color: 'text-green-600',
      description: '恭喜！您的体重在健康范围内，请继续保持。',
    },
    {
      min: 24,
      max: 28,
      name: '超重',
      color: 'text-yellow-600',
      description: '您的体重略高，建议适当控制饮食并增加运动。',
    },
    {
      min: 28,
      max: 32,
      name: '轻度肥胖',
      color: 'text-orange-600',
      description: '您需要注意控制体重，建议咨询医生制定减重计划。',
    },
    {
      min: 32,
      max: Infinity,
      name: '重度肥胖',
      color: 'text-red-600',
      description: '您的健康风险较高，强烈建议尽快就医咨询。',
    },
  ], [])

  // 计算 BMI
  const calculateBmi = useCallback(() => {
    const h = parseFloat(height)
    const w = parseFloat(weight)

    if (!h || !w || h <= 0 || w <= 0) {
      setBmi(null)
      setCategory(null)
      setIdealWeight(null)
      return
    }

    let heightInMeters: number
    let weightInKg: number

    if (unit === 'metric') {
      heightInMeters = h / 100 // cm to m
      weightInKg = w
    } else {
      heightInMeters = h * 0.0254 // inches to m
      weightInKg = w * 0.453592 // lbs to kg
    }

    const bmiValue = weightInKg / (heightInMeters * heightInMeters)
    setBmi(Math.round(bmiValue * 10) / 10)

    // 确定分类
    const cat = bmiCategories.find(c => bmiValue >= c.min && bmiValue < c.max)
    setCategory(cat || null)

    // 计算理想体重范围
    const minIdealBmi = 18.5
    const maxIdealBmi = 24
    const minWeight = minIdealBmi * heightInMeters * heightInMeters
    const maxWeight = maxIdealBmi * heightInMeters * heightInMeters

    if (unit === 'metric') {
      setIdealWeight({
        min: Math.round(minWeight * 10) / 10,
        max: Math.round(maxWeight * 10) / 10,
      })
    } else {
      setIdealWeight({
        min: Math.round(minWeight * 2.20462 * 10) / 10, // kg to lbs
        max: Math.round(maxWeight * 2.20462 * 10) / 10,
      })
    }
  }, [height, weight, unit, bmiCategories])

  // 监听输入变化
  useEffect(() => {
    calculateBmi()
  }, [calculateBmi])

  // 保存记录
  const saveRecord = () => {
    if (bmi && category) {
      const newRecord: BmiRecord = {
        date: new Date().toLocaleDateString('zh-CN'),
        height: parseFloat(height),
        weight: parseFloat(weight),
        bmi: bmi,
        category: category.name,
      }
      const updatedHistory = [newRecord, ...history.slice(0, 9)]
      setHistory(updatedHistory)
      localStorage.setItem('bmi-history', JSON.stringify(updatedHistory))
    }
  }

  // 加载历史记录
  useEffect(() => {
    const saved = localStorage.getItem('bmi-history')
    if (saved) {
      setHistory(JSON.parse(saved))
    }
  }, [])

  // 清空输入
  const clearAll = () => {
    setHeight('')
    setWeight('')
    setBmi(null)
    setCategory(null)
    setIdealWeight(null)
  }

  // 获取体重变化建议
  const getWeightAdvice = () => {
    if (!bmi || !category || !idealWeight || !weight) return null

    const currentWeight = parseFloat(weight)
    const targetWeight = (idealWeight.min + idealWeight.max) / 2

    if (category.name === '正常体重') {
      return null
    }

    const diff = Math.abs(currentWeight - targetWeight)
    const isOverweight = currentWeight > targetWeight

    return {
      diff: Math.round(diff * 10) / 10,
      isOverweight,
      unit: unit === 'metric' ? 'kg' : 'lbs',
    }
  }

  const advice = getWeightAdvice()

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">BMI 计算器</h2>
        <p className="text-gray-600">
          计算您的身体质量指数（BMI），了解您的健康体重范围
        </p>
      </div>

      {/* 单位选择 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <div className="flex items-center justify-center gap-4">
          <button
            onClick={() => setUnit('metric')}
            className={`px-4 py-2 rounded-md ${
              unit === 'metric'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 hover:bg-gray-200'
            }`}
          >
            公制单位（cm/kg）
          </button>
          <button
            onClick={() => setUnit('imperial')}
            className={`px-4 py-2 rounded-md ${
              unit === 'imperial'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 hover:bg-gray-200'
            }`}
          >
            英制单位（in/lbs）
          </button>
        </div>
      </div>

      {/* 输入区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <Ruler className="w-5 h-5 mr-2 text-blue-600" />
            <h3 className="font-medium">身高</h3>
          </div>
          <div className="flex items-center">
            <input
              type="number"
              value={height}
              onChange={(e) => setHeight(e.target.value)}
              placeholder={unit === 'metric' ? '170' : '67'}
              className="flex-1 px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <span className="ml-3 text-gray-600 font-medium">
              {unit === 'metric' ? 'cm' : 'in'}
            </span>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <Weight className="w-5 h-5 mr-2 text-blue-600" />
            <h3 className="font-medium">体重</h3>
          </div>
          <div className="flex items-center">
            <input
              type="number"
              value={weight}
              onChange={(e) => setWeight(e.target.value)}
              placeholder={unit === 'metric' ? '65' : '143'}
              className="flex-1 px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <span className="ml-3 text-gray-600 font-medium">
              {unit === 'metric' ? 'kg' : 'lbs'}
            </span>
          </div>
        </div>
      </div>

      {/* 结果显示 */}
      {bmi && category && (
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="text-center mb-6">
            <div className="text-5xl font-bold mb-2">{bmi}</div>
            <div className={`text-xl font-medium ${category.color}`}>
              {category.name}
            </div>
          </div>

          <div className="mb-6">
            <div className="bg-gray-200 rounded-full h-4 relative overflow-hidden">
              <div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-400 via-green-400 via-yellow-400 via-orange-400 to-red-400"
                style={{ width: '100%' }}
              />
              <div
                className="absolute top-0 w-1 h-6 bg-black -mt-1"
                style={{
                  left: `${Math.min((bmi / 40) * 100, 100)}%`,
                  transform: 'translateX(-50%)',
                }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-600 mt-1">
              <span>15</span>
              <span>18.5</span>
              <span>24</span>
              <span>28</span>
              <span>32</span>
              <span>40</span>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <p className="text-gray-700">{category.description}</p>
          </div>

          {idealWeight && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium mb-1">理想体重范围</h4>
                <p className="text-lg">
                  {idealWeight.min} - {idealWeight.max} {unit === 'metric' ? 'kg' : 'lbs'}
                </p>
              </div>

              {advice && (
                <div className={`rounded-lg p-4 ${advice.isOverweight ? 'bg-orange-50' : 'bg-blue-50'}`}>
                  <h4 className="font-medium mb-1">体重调整建议</h4>
                  <p className="text-lg flex items-center">
                    {advice.isOverweight ? (
                      <>
                        <TrendingDown className="w-4 h-4 mr-1" />
                        减重 {advice.diff} {advice.unit}
                      </>
                    ) : (
                      <>
                        <TrendingUp className="w-4 h-4 mr-1" />
                        增重 {advice.diff} {advice.unit}
                      </>
                    )}
                  </p>
                </div>
              )}
            </div>
          )}

          <div className="flex justify-center gap-4 mt-6">
            <button
              onClick={saveRecord}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              保存记录
            </button>
            <button
              onClick={clearAll}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              清空
            </button>
          </div>
        </div>
      )}

      {/* 历史记录 */}
      {history.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="font-medium mb-4">历史记录</h3>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">日期</th>
                  <th className="text-left py-2">身高</th>
                  <th className="text-left py-2">体重</th>
                  <th className="text-left py-2">BMI</th>
                  <th className="text-left py-2">分类</th>
                </tr>
              </thead>
              <tbody>
                {history.map((record, index) => (
                  <tr key={index} className="border-b">
                    <td className="py-2">{record.date}</td>
                    <td className="py-2">{record.height} {unit === 'metric' ? 'cm' : 'in'}</td>
                    <td className="py-2">{record.weight} {unit === 'metric' ? 'kg' : 'lbs'}</td>
                    <td className="py-2">{record.bmi}</td>
                    <td className="py-2">
                      <span className={bmiCategories.find(c => c.name === record.category)?.color}>
                        {record.category}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* BMI 说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">关于 BMI</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>BMI = 体重(kg) ÷ 身高²(m²)</li>
              <li>BMI 是评估体重是否健康的常用指标</li>
              <li>本工具采用中国标准，正常范围为 18.5-24</li>
              <li>BMI 不适用于儿童、孕妇、运动员等特殊人群</li>
              <li>建议结合体脂率、腰围等指标综合评估健康状况</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
