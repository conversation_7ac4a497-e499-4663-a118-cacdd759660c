'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { 
  Calendar, 
  Download, 
  Plus, 
  Trash2, 
  Edit3, 
  Eye,
  ChevronLeft,
  ChevronRight,
  Settings,
  Palette,
  Type,
  Layout
} from 'lucide-react'
import { <PERSON>l<PERSON>ontainer, ToolHeader, ToolActions } from '@/components/common'

/**
 * 日历生成器工具组件
 * 提供自定义日历生成功能，支持多种样式和事件管理
 */
export default function CalendarGeneratorTool() {
  // 当前日期状态
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())
  
  // 日历配置
  const [calendarConfig, setCalendarConfig] = useState({
    title: '我的日历',
    showWeekNumbers: false,
    showLunarCalendar: false,
    showHolidays: true,
    firstDayOfWeek: 0, // 0: 周日, 1: 周一
    theme: 'default',
    size: 'medium',
    language: 'zh-CN'
  })
  
  // 样式配置
  const [styleConfig, setStyleConfig] = useState({
    primaryColor: '#3b82f6',
    secondaryColor: '#e5e7eb',
    textColor: '#1f2937',
    backgroundColor: '#ffffff',
    borderColor: '#d1d5db',
    headerStyle: 'modern',
    cellStyle: 'rounded',
    fontFamily: 'system-ui'
  })
  
  // 事件管理
  const [events, setEvents] = useState<Array<{
    id: string
    date: string
    title: string
    description: string
    color: string
    type: 'event' | 'holiday' | 'birthday' | 'meeting' | 'reminder'
  }>>([])
  
  // 新事件表单
  const [newEvent, setNewEvent] = useState<{
    date: string
    title: string
    description: string
    color: string
    type: 'event' | 'holiday' | 'birthday' | 'meeting' | 'reminder'
  }>({
    date: '',
    title: '',
    description: '',
    color: '#3b82f6',
    type: 'event'
  })
  
  // 编辑状态
  const [editingEvent, setEditingEvent] = useState<string | null>(null)
  const [showEventForm, setShowEventForm] = useState(false)
  
  // 预设主题
  const themes = {
    default: {
      name: '默认',
      primaryColor: '#3b82f6',
      secondaryColor: '#e5e7eb',
      textColor: '#1f2937',
      backgroundColor: '#ffffff'
    },
    dark: {
      name: '深色',
      primaryColor: '#6366f1',
      secondaryColor: '#374151',
      textColor: '#f9fafb',
      backgroundColor: '#111827'
    },
    nature: {
      name: '自然',
      primaryColor: '#10b981',
      secondaryColor: '#d1fae5',
      textColor: '#065f46',
      backgroundColor: '#f0fdf4'
    },
    sunset: {
      name: '日落',
      primaryColor: '#f59e0b',
      secondaryColor: '#fef3c7',
      textColor: '#92400e',
      backgroundColor: '#fffbeb'
    },
    ocean: {
      name: '海洋',
      primaryColor: '#0ea5e9',
      secondaryColor: '#e0f2fe',
      textColor: '#0c4a6e',
      backgroundColor: '#f0f9ff'
    }
  }
  
  // 事件类型配置
  const eventTypes = {
    event: { name: '事件', color: '#3b82f6', icon: '📅' },
    holiday: { name: '节假日', color: '#ef4444', icon: '🎉' },
    birthday: { name: '生日', color: '#f59e0b', icon: '🎂' },
    meeting: { name: '会议', color: '#8b5cf6', icon: '💼' },
    reminder: { name: '提醒', color: '#10b981', icon: '⏰' }
  }
  
  // 获取月份信息
  const getMonthInfo = useCallback((year: number, month: number) => {
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = (firstDay.getDay() - calendarConfig.firstDayOfWeek + 7) % 7
    
    return {
      firstDay,
      lastDay,
      daysInMonth,
      startingDayOfWeek
    }
  }, [calendarConfig.firstDayOfWeek])
  
  // 获取日历网格数据
  const calendarGrid = useMemo(() => {
    const { daysInMonth, startingDayOfWeek } = getMonthInfo(selectedYear, selectedMonth)
    const grid: Array<{
      date: number | null
      isCurrentMonth: boolean
      isToday: boolean
      events: typeof events
      dateString: string
    }> = []
    
    // 上个月的日期
    const prevMonth = selectedMonth === 0 ? 11 : selectedMonth - 1
    const prevYear = selectedMonth === 0 ? selectedYear - 1 : selectedYear
    const prevMonthDays = new Date(prevYear, prevMonth + 1, 0).getDate()
    
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      const date = prevMonthDays - i
      const dateString = `${prevYear}-${String(prevMonth + 1).padStart(2, '0')}-${String(date).padStart(2, '0')}`
      grid.push({
        date,
        isCurrentMonth: false,
        isToday: false,
        events: events.filter(e => e.date === dateString),
        dateString
      })
    }
    
    // 当前月的日期
    const today = new Date()
    for (let date = 1; date <= daysInMonth; date++) {
      const dateString = `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${String(date).padStart(2, '0')}`
      const isToday = today.getFullYear() === selectedYear && 
                     today.getMonth() === selectedMonth && 
                     today.getDate() === date
      
      grid.push({
        date,
        isCurrentMonth: true,
        isToday,
        events: events.filter(e => e.date === dateString),
        dateString
      })
    }
    
    // 下个月的日期
    const nextMonth = selectedMonth === 11 ? 0 : selectedMonth + 1
    const nextYear = selectedMonth === 11 ? selectedYear + 1 : selectedYear
    const remainingCells = 42 - grid.length // 6行 x 7列
    
    for (let date = 1; date <= remainingCells; date++) {
      const dateString = `${nextYear}-${String(nextMonth + 1).padStart(2, '0')}-${String(date).padStart(2, '0')}`
      grid.push({
        date,
        isCurrentMonth: false,
        isToday: false,
        events: events.filter(e => e.date === dateString),
        dateString
      })
    }
    
    return grid
  }, [selectedYear, selectedMonth, events, getMonthInfo])
  
  // 获取星期标题
  const weekDays = useMemo(() => {
    const days = ['日', '一', '二', '三', '四', '五', '六']
    const startIndex = calendarConfig.firstDayOfWeek
    return [...days.slice(startIndex), ...days.slice(0, startIndex)]
  }, [calendarConfig.firstDayOfWeek])
  
  // 月份名称
  const monthNames = [
    '一月', '二月', '三月', '四月', '五月', '六月',
    '七月', '八月', '九月', '十月', '十一月', '十二月'
  ]
  
  // 导航到上个月
  const goToPreviousMonth = () => {
    if (selectedMonth === 0) {
      setSelectedMonth(11)
      setSelectedYear(selectedYear - 1)
    } else {
      setSelectedMonth(selectedMonth - 1)
    }
  }
  
  // 导航到下个月
  const goToNextMonth = () => {
    if (selectedMonth === 11) {
      setSelectedMonth(0)
      setSelectedYear(selectedYear + 1)
    } else {
      setSelectedMonth(selectedMonth + 1)
    }
  }
  
  // 添加事件
  const addEvent = () => {
    if (!newEvent.date || !newEvent.title) return
    
    const event = {
      id: Date.now().toString(),
      ...newEvent
    }
    
    setEvents([...events, event])
    setNewEvent({
      date: '',
      title: '',
      description: '',
      color: '#3b82f6',
      type: 'event'
    })
    setShowEventForm(false)
  }
  
  // 删除事件
  const deleteEvent = (id: string) => {
    setEvents(events.filter(e => e.id !== id))
  }
  
  // 编辑事件
  const editEvent = (id: string) => {
    const event = events.find(e => e.id === id)
    if (event) {
      setNewEvent({
        date: event.date,
        title: event.title,
        description: event.description,
        color: event.color,
        type: event.type
      })
      setEditingEvent(id)
      setShowEventForm(true)
    }
  }
  
  // 更新事件
  const updateEvent = () => {
    if (!editingEvent || !newEvent.date || !newEvent.title) return
    
    setEvents(events.map(e => 
      e.id === editingEvent 
        ? { ...e, ...newEvent }
        : e
    ))
    
    setNewEvent({
      date: '',
      title: '',
      description: '',
      color: '#3b82f6',
      type: 'event'
    })
    setEditingEvent(null)
    setShowEventForm(false)
  }
  
  // 应用主题
  const applyTheme = (themeName: string) => {
    const theme = themes[themeName as keyof typeof themes]
    if (theme) {
      setStyleConfig(prev => ({
        ...prev,
        ...theme
      }))
      setCalendarConfig(prev => ({
        ...prev,
        theme: themeName
      }))
    }
  }
  
  // 导出日历为HTML
  const exportCalendar = () => {
    const calendarHTML = generateCalendarHTML()
    const blob = new Blob([calendarHTML], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `calendar-${selectedYear}-${selectedMonth + 1}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  
  // 生成日历HTML
  const generateCalendarHTML = () => {
    const { daysInMonth, startingDayOfWeek } = getMonthInfo(selectedYear, selectedMonth)
    
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${calendarConfig.title} - ${selectedYear}年${selectedMonth + 1}月</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: ${styleConfig.fontFamily}, sans-serif;
            background-color: ${styleConfig.backgroundColor};
            color: ${styleConfig.textColor};
            padding: 20px;
        }
        
        .calendar-container {
            max-width: 800px;
            margin: 0 auto;
            background: ${styleConfig.backgroundColor};
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .calendar-header {
            background: ${styleConfig.primaryColor};
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .calendar-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .calendar-month {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
        }
        
        .weekday-header {
            background: ${styleConfig.secondaryColor};
            padding: 12px;
            text-align: center;
            font-weight: 600;
            border-bottom: 1px solid ${styleConfig.borderColor};
        }
        
        .calendar-cell {
            min-height: 80px;
            border: 1px solid ${styleConfig.borderColor};
            padding: 8px;
            position: relative;
        }
        
        .calendar-cell.other-month {
            opacity: 0.3;
        }
        
        .calendar-cell.today {
            background: ${styleConfig.primaryColor}20;
        }
        
        .date-number {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .event-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            display: inline-block;
            margin: 1px;
        }
        
        .event-list {
            margin-top: 20px;
            padding: 20px;
        }
        
        .event-item {
            display: flex;
            align-items: center;
            padding: 8px;
            margin: 4px 0;
            border-radius: 6px;
            background: ${styleConfig.secondaryColor};
        }
        
        .event-date {
            font-weight: 600;
            margin-right: 12px;
            min-width: 80px;
        }
        
        .event-title {
            font-weight: 500;
        }
        
        @media print {
            body { padding: 0; }
            .calendar-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="calendar-container">
        <div class="calendar-header">
            <div class="calendar-title">${calendarConfig.title}</div>
            <div class="calendar-month">${selectedYear}年${monthNames[selectedMonth]}</div>
        </div>
        
        <div class="calendar-grid">
            ${weekDays.map(day => `<div class="weekday-header">${day}</div>`).join('')}
            ${calendarGrid.map(cell => `
                <div class="calendar-cell ${!cell.isCurrentMonth ? 'other-month' : ''} ${cell.isToday ? 'today' : ''}">
                    <div class="date-number">${cell.date}</div>
                    ${cell.events.map(event => `<span class="event-dot" style="background-color: ${event.color}"></span>`).join('')}
                </div>
            `).join('')}
        </div>
        
        ${events.length > 0 ? `
        <div class="event-list">
            <h3 style="margin-bottom: 16px;">事件列表</h3>
            ${events.map(event => `
                <div class="event-item">
                    <div class="event-date">${event.date}</div>
                    <div>
                        <div class="event-title">${event.title}</div>
                        ${event.description ? `<div style="font-size: 14px; opacity: 0.7;">${event.description}</div>` : ''}
                    </div>
                </div>
            `).join('')}
        </div>
        ` : ''}
    </div>
</body>
</html>
    `
  }
  
  return (
    <ToolContainer>
      <ToolHeader
        title="日历生成器"
        description="创建自定义日历，支持事件管理、多种主题和样式配置"
        icon={<Calendar className="w-6 h-6" />}
        locale="zh"
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 日历预览 */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToPreviousMonth}
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  
                  <div className="flex items-center gap-2">
                    <Select
                      value={selectedYear.toString()}
                      onValueChange={(value) => setSelectedYear(parseInt(value))}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 21 }, (_, i) => {
                          const year = new Date().getFullYear() - 10 + i
                          return (
                            <SelectItem key={year} value={year.toString()}>
                              {year}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                    
                    <Select
                      value={selectedMonth.toString()}
                      onValueChange={(value) => setSelectedMonth(parseInt(value))}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {monthNames.map((month, index) => (
                          <SelectItem key={index} value={index.toString()}>
                            {month}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToNextMonth}
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
                
                <Button onClick={exportCalendar}>
                  <Download className="w-4 h-4 mr-2" />
                  导出HTML
                </Button>
              </div>
            </CardHeader>
            
            <CardContent>
              {/* 日历标题 */}
              <div 
                className="text-center py-4 mb-4 rounded-lg text-white font-bold text-xl"
                style={{ backgroundColor: styleConfig.primaryColor }}
              >
                {calendarConfig.title}
              </div>
              
              {/* 日历网格 */}
              <div className="grid grid-cols-7 gap-1 border rounded-lg overflow-hidden">
                {/* 星期标题 */}
                {weekDays.map((day, index) => (
                  <div
                    key={index}
                    className="p-3 text-center font-semibold border-b"
                    style={{ 
                      backgroundColor: styleConfig.secondaryColor,
                      color: styleConfig.textColor
                    }}
                  >
                    {day}
                  </div>
                ))}
                
                {/* 日期单元格 */}
                {calendarGrid.map((cell, index) => (
                  <div
                    key={index}
                    className={`
                      min-h-[80px] p-2 border-b border-r relative
                      ${!cell.isCurrentMonth ? 'opacity-30' : ''}
                      ${cell.isToday ? 'ring-2 ring-blue-500' : ''}
                    `}
                    style={{
                      backgroundColor: cell.isToday 
                        ? `${styleConfig.primaryColor}20` 
                        : styleConfig.backgroundColor
                    }}
                  >
                    <div className="font-semibold text-sm mb-1">
                      {cell.date}
                    </div>
                    
                    {/* 事件指示器 */}
                    <div className="flex flex-wrap gap-1">
                      {cell.events.slice(0, 3).map((event, eventIndex) => (
                        <div
                          key={eventIndex}
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: event.color }}
                          title={event.title}
                        />
                      ))}
                      {cell.events.length > 3 && (
                        <div className="text-xs text-gray-500">
                          +{cell.events.length - 3}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* 配置面板 */}
        <div className="space-y-6">
          <Tabs defaultValue="settings" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="settings">
                <Settings className="w-4 h-4 mr-1" />
                设置
              </TabsTrigger>
              <TabsTrigger value="style">
                <Palette className="w-4 h-4 mr-1" />
                样式
              </TabsTrigger>
              <TabsTrigger value="events">
                <Calendar className="w-4 h-4 mr-1" />
                事件
              </TabsTrigger>
            </TabsList>
            
            {/* 基本设置 */}
            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">基本设置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="title">日历标题</Label>
                    <Input
                      id="title"
                      value={calendarConfig.title}
                      onChange={(e) => setCalendarConfig(prev => ({
                        ...prev,
                        title: e.target.value
                      }))}
                      placeholder="输入日历标题"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="firstDay">一周开始</Label>
                    <Select
                      value={calendarConfig.firstDayOfWeek.toString()}
                      onValueChange={(value) => setCalendarConfig(prev => ({
                        ...prev,
                        firstDayOfWeek: parseInt(value)
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">周日</SelectItem>
                        <SelectItem value="1">周一</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="weekNumbers">显示周数</Label>
                    <Switch
                      id="weekNumbers"
                      checked={calendarConfig.showWeekNumbers}
                      onCheckedChange={(checked) => setCalendarConfig(prev => ({
                        ...prev,
                        showWeekNumbers: checked
                      }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="holidays">显示节假日</Label>
                    <Switch
                      id="holidays"
                      checked={calendarConfig.showHolidays}
                      onCheckedChange={(checked) => setCalendarConfig(prev => ({
                        ...prev,
                        showHolidays: checked
                      }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* 样式设置 */}
            <TabsContent value="style" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">主题样式</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>预设主题</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {Object.entries(themes).map(([key, theme]) => (
                        <Button
                          key={key}
                          variant={calendarConfig.theme === key ? "default" : "outline"}
                          size="sm"
                          onClick={() => applyTheme(key)}
                          className="justify-start"
                        >
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: theme.primaryColor }}
                          />
                          {theme.name}
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <Label htmlFor="primaryColor">主色调</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="primaryColor"
                        type="color"
                        value={styleConfig.primaryColor}
                        onChange={(e) => setStyleConfig(prev => ({
                          ...prev,
                          primaryColor: e.target.value
                        }))}
                        className="w-12 h-10 p-1"
                      />
                      <Input
                        value={styleConfig.primaryColor}
                        onChange={(e) => setStyleConfig(prev => ({
                          ...prev,
                          primaryColor: e.target.value
                        }))}
                        placeholder="#3b82f6"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="backgroundColor">背景色</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="backgroundColor"
                        type="color"
                        value={styleConfig.backgroundColor}
                        onChange={(e) => setStyleConfig(prev => ({
                          ...prev,
                          backgroundColor: e.target.value
                        }))}
                        className="w-12 h-10 p-1"
                      />
                      <Input
                        value={styleConfig.backgroundColor}
                        onChange={(e) => setStyleConfig(prev => ({
                          ...prev,
                          backgroundColor: e.target.value
                        }))}
                        placeholder="#ffffff"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="fontFamily">字体</Label>
                    <Select
                      value={styleConfig.fontFamily}
                      onValueChange={(value) => setStyleConfig(prev => ({
                        ...prev,
                        fontFamily: value
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="system-ui">系统默认</SelectItem>
                        <SelectItem value="serif">衬线字体</SelectItem>
                        <SelectItem value="monospace">等宽字体</SelectItem>
                        <SelectItem value="cursive">手写字体</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* 事件管理 */}
            <TabsContent value="events" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center justify-between">
                    事件管理
                    <Button
                      size="sm"
                      onClick={() => setShowEventForm(true)}
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      添加
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {/* 事件表单 */}
                  {showEventForm && (
                    <div className="space-y-4 p-4 border rounded-lg mb-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">
                          {editingEvent ? '编辑事件' : '添加事件'}
                        </h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setShowEventForm(false)
                            setEditingEvent(null)
                            setNewEvent({
                              date: '',
                              title: '',
                              description: '',
                              color: '#3b82f6',
                              type: 'event'
                            })
                          }}
                        >
                          ×
                        </Button>
                      </div>
                      
                      <div>
                        <Label htmlFor="eventDate">日期</Label>
                        <Input
                          id="eventDate"
                          type="date"
                          value={newEvent.date}
                          onChange={(e) => setNewEvent(prev => ({
                            ...prev,
                            date: e.target.value
                          }))}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="eventTitle">标题</Label>
                        <Input
                          id="eventTitle"
                          value={newEvent.title}
                          onChange={(e) => setNewEvent(prev => ({
                            ...prev,
                            title: e.target.value
                          }))}
                          placeholder="输入事件标题"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="eventDescription">描述</Label>
                        <Textarea
                          id="eventDescription"
                          value={newEvent.description}
                          onChange={(e) => setNewEvent(prev => ({
                            ...prev,
                            description: e.target.value
                          }))}
                          placeholder="输入事件描述（可选）"
                          rows={2}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="eventType">类型</Label>
                          <Select
                            value={newEvent.type}
                            onValueChange={(value: any) => setNewEvent(prev => ({
                              ...prev,
                              type: value,
                              color: eventTypes[value as keyof typeof eventTypes].color
                            }))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {Object.entries(eventTypes).map(([key, type]) => (
                                <SelectItem key={key} value={key}>
                                  {type.icon} {type.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label htmlFor="eventColor">颜色</Label>
                          <div className="flex gap-2">
                            <Input
                              id="eventColor"
                              type="color"
                              value={newEvent.color}
                              onChange={(e) => setNewEvent(prev => ({
                                ...prev,
                                color: e.target.value
                              }))}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={newEvent.color}
                              onChange={(e) => setNewEvent(prev => ({
                                ...prev,
                                color: e.target.value
                              }))}
                              placeholder="#3b82f6"
                              className="flex-1"
                            />
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          onClick={editingEvent ? updateEvent : addEvent}
                          disabled={!newEvent.date || !newEvent.title}
                          className="flex-1"
                        >
                          {editingEvent ? '更新' : '添加'}
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setShowEventForm(false)
                            setEditingEvent(null)
                            setNewEvent({
                              date: '',
                              title: '',
                              description: '',
                              color: '#3b82f6',
                              type: 'event'
                            })
                          }}
                        >
                          取消
                        </Button>
                      </div>
                    </div>
                  )}
                  
                  {/* 事件列表 */}
                  <div className="space-y-2">
                    {events.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Calendar className="w-12 h-12 mx-auto mb-2 opacity-50" />
                        <p>暂无事件</p>
                        <p className="text-sm">点击上方"添加"按钮创建事件</p>
                      </div>
                    ) : (
                      events.map((event) => (
                        <div
                          key={event.id}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: event.color }}
                            />
                            <div>
                              <div className="font-medium">
                                {eventTypes[event.type].icon} {event.title}
                              </div>
                              <div className="text-sm text-gray-500">
                                {event.date}
                                {event.description && (
                                  <span className="ml-2">• {event.description}</span>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => editEvent(event.id)}
                            >
                              <Edit3 className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteEvent(event.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </ToolContainer>
  )
}
