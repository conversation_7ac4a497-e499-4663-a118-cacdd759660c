'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { MapPin, Calendar, DollarSign, Plus, Trash2, Edit2, Save, X, Plane, Hotel, Camera, ShoppingBag, Utensils, Clock, CheckSquare, FileText, Download, Upload } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Activity {
  id: string
  time: string
  title: string
  location?: string
  cost?: number
  notes?: string
  completed: boolean
}

interface DayPlan {
  date: string
  activities: Activity[]
  accommodation?: string
  budget: number
}

interface TravelPlan {
  id: string
  title: string
  destination: string
  startDate: string
  endDate: string
  budget: number
  currency: string
  description?: string
  days: DayPlan[]
  checklist: ChecklistItem[]
  notes: string
  createdAt: string
  updatedAt: string
}

interface ChecklistItem {
  id: string
  text: string
  completed: boolean
  category: 'before' | 'packing' | 'documents'
}

const CURRENCIES = [
  { code: 'CNY', symbol: '¥', name: '人民币' },
  { code: 'USD', symbol: '$', name: '美元' },
  { code: 'EUR', symbol: '€', name: '欧元' },
  { code: 'JPY', symbol: '¥', name: '日元' },
  { code: 'GBP', symbol: '£', name: '英镑' },
  { code: 'KRW', symbol: '₩', name: '韩元' },
  { code: 'THB', symbol: '฿', name: '泰铢' },
  { code: 'SGD', symbol: 'S$', name: '新加坡元' },
]

const DEFAULT_CHECKLIST: ChecklistItem[] = [
  // 出发前
  { id: '1', text: '预订机票', completed: false, category: 'before' },
  { id: '2', text: '预订酒店', completed: false, category: 'before' },
  { id: '3', text: '办理签证', completed: false, category: 'before' },
  { id: '4', text: '购买旅行保险', completed: false, category: 'before' },
  { id: '5', text: '兑换外币', completed: false, category: 'before' },
  { id: '6', text: '预订租车/交通', completed: false, category: 'before' },
  
  // 行李打包
  { id: '7', text: '衣物', completed: false, category: 'packing' },
  { id: '8', text: '洗漱用品', completed: false, category: 'packing' },
  { id: '9', text: '充电器/转换插头', completed: false, category: 'packing' },
  { id: '10', text: '药品', completed: false, category: 'packing' },
  { id: '11', text: '防晒用品', completed: false, category: 'packing' },
  { id: '12', text: '相机/摄影设备', completed: false, category: 'packing' },
  
  // 重要文件
  { id: '13', text: '护照', completed: false, category: 'documents' },
  { id: '14', text: '身份证', completed: false, category: 'documents' },
  { id: '15', text: '机票行程单', completed: false, category: 'documents' },
  { id: '16', text: '酒店预订确认', completed: false, category: 'documents' },
  { id: '17', text: '旅行保险单', completed: false, category: 'documents' },
  { id: '18', text: '紧急联系人信息', completed: false, category: 'documents' },
]

export default function TravelPlannerTool() {
  const [plans, setPlans] = useState<TravelPlan[]>([])
  const [currentPlan, setCurrentPlan] = useState<TravelPlan | null>(null)
  const [editingPlanId, setEditingPlanId] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  
  // 表单状态
  const [title, setTitle] = useState('')
  const [destination, setDestination] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [budget, setBudget] = useState('')
  const [currency, setCurrency] = useState('CNY')
  const [description, setDescription] = useState('')
  
  const { toast } = useToast()

  // 从 localStorage 加载数据
  useEffect(() => {
    const savedPlans = localStorage.getItem('travelPlans')
    if (savedPlans) {
      setPlans(JSON.parse(savedPlans))
    }
  }, [])

  // 保存数据到 localStorage
  useEffect(() => {
    if (plans.length > 0) {
      localStorage.setItem('travelPlans', JSON.stringify(plans))
    }
  }, [plans])

  const generateDayPlans = (start: string, end: string): DayPlan[] => {
    const startDate = new Date(start)
    const endDate = new Date(end)
    const days: DayPlan[] = []
    
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      days.push({
        date: d.toISOString().split('T')[0],
        activities: [],
        budget: 0,
      })
    }
    
    return days
  }

  const createPlan = () => {
    if (!title || !destination || !startDate || !endDate) {
      toast({
        title: '请填写完整信息',
        description: '标题、目的地和日期为必填项',
        variant: 'destructive',
      })
      return
    }
    
    if (new Date(startDate) > new Date(endDate)) {
      toast({
        title: '日期错误',
        description: '结束日期不能早于开始日期',
        variant: 'destructive',
      })
      return
    }
    
    const newPlan: TravelPlan = {
      id: Date.now().toString(),
      title,
      destination,
      startDate,
      endDate,
      budget: parseFloat(budget) || 0,
      currency,
      description,
      days: generateDayPlans(startDate, endDate),
      checklist: DEFAULT_CHECKLIST.map(item => ({ ...item, id: Date.now().toString() + item.id })),
      notes: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    
    setPlans([...plans, newPlan])
    setCurrentPlan(newPlan)
    resetForm()
    setActiveTab('itinerary')
    
    toast({
      title: '旅行计划已创建',
      description: `${title} - ${destination}`,
    })
  }

  const updatePlan = (planId: string, updates: Partial<TravelPlan>) => {
    const updatedPlans = plans.map(plan => 
      plan.id === planId 
        ? { ...plan, ...updates, updatedAt: new Date().toISOString() }
        : plan
    )
    setPlans(updatedPlans)
    
    if (currentPlan?.id === planId) {
      setCurrentPlan({ ...currentPlan, ...updates })
    }
  }

  const deletePlan = (planId: string) => {
    setPlans(plans.filter(p => p.id !== planId))
    if (currentPlan?.id === planId) {
      setCurrentPlan(null)
    }
    toast({
      title: '旅行计划已删除',
    })
  }

  const resetForm = () => {
    setTitle('')
    setDestination('')
    setStartDate('')
    setEndDate('')
    setBudget('')
    setCurrency('CNY')
    setDescription('')
  }

  const addActivity = (planId: string, dayIndex: number, activity: Omit<Activity, 'id'>) => {
    const plan = plans.find(p => p.id === planId)
    if (!plan) return
    
    const newActivity: Activity = {
      ...activity,
      id: Date.now().toString(),
    }
    
    const updatedDays = [...plan.days]
    updatedDays[dayIndex].activities.push(newActivity)
    
    updatePlan(planId, { days: updatedDays })
  }

  const updateActivity = (planId: string, dayIndex: number, activityId: string, updates: Partial<Activity>) => {
    const plan = plans.find(p => p.id === planId)
    if (!plan) return
    
    const updatedDays = [...plan.days]
    updatedDays[dayIndex].activities = updatedDays[dayIndex].activities.map(activity =>
      activity.id === activityId ? { ...activity, ...updates } : activity
    )
    
    updatePlan(planId, { days: updatedDays })
  }

  const deleteActivity = (planId: string, dayIndex: number, activityId: string) => {
    const plan = plans.find(p => p.id === planId)
    if (!plan) return
    
    const updatedDays = [...plan.days]
    updatedDays[dayIndex].activities = updatedDays[dayIndex].activities.filter(
      activity => activity.id !== activityId
    )
    
    updatePlan(planId, { days: updatedDays })
  }

  const toggleChecklistItem = (planId: string, itemId: string) => {
    const plan = plans.find(p => p.id === planId)
    if (!plan) return
    
    const updatedChecklist = plan.checklist.map(item =>
      item.id === itemId ? { ...item, completed: !item.completed } : item
    )
    
    updatePlan(planId, { checklist: updatedChecklist })
  }

  const addChecklistItem = (planId: string, text: string, category: ChecklistItem['category']) => {
    const plan = plans.find(p => p.id === planId)
    if (!plan) return
    
    const newItem: ChecklistItem = {
      id: Date.now().toString(),
      text,
      completed: false,
      category,
    }
    
    updatePlan(planId, { checklist: [...plan.checklist, newItem] })
  }

  const deleteChecklistItem = (planId: string, itemId: string) => {
    const plan = plans.find(p => p.id === planId)
    if (!plan) return
    
    updatePlan(planId, { checklist: plan.checklist.filter(item => item.id !== itemId) })
  }

  const calculateTotalBudget = (plan: TravelPlan): number => {
    const activitiesCost = plan.days.reduce((total, day) => 
      total + day.activities.reduce((dayTotal, activity) => 
        dayTotal + (activity.cost || 0), 0
      ), 0
    )
    
    const accommodationCost = plan.days.reduce((total, day) => 
      total + day.budget, 0
    )
    
    return activitiesCost + accommodationCost
  }

  const exportPlan = (plan: TravelPlan) => {
    const content = generatePlanText(plan)
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${plan.title}-旅行计划.txt`
    a.click()
    URL.revokeObjectURL(url)
    
    toast({
      title: '导出成功',
      description: '旅行计划已导出为文本文件',
    })
  }

  const generatePlanText = (plan: TravelPlan): string => {
    const currency = CURRENCIES.find(c => c.code === plan.currency)
    let text = `${plan.title}\n`
    text += `目的地：${plan.destination}\n`
    text += `日期：${new Date(plan.startDate).toLocaleDateString('zh-CN')} - ${new Date(plan.endDate).toLocaleDateString('zh-CN')}\n`
    text += `预算：${currency?.symbol}${plan.budget}\n`
    if (plan.description) {
      text += `\n描述：${plan.description}\n`
    }
    
    text += '\n=== 行程安排 ===\n'
    plan.days.forEach((day, index) => {
      text += `\n第${index + 1}天 - ${new Date(day.date).toLocaleDateString('zh-CN', { month: 'long', day: 'numeric', weekday: 'long' })}\n`
      if (day.accommodation) {
        text += `住宿：${day.accommodation}\n`
      }
      
      if (day.activities.length > 0) {
        day.activities.forEach(activity => {
          text += `  ${activity.time} - ${activity.title}`
          if (activity.location) text += ` @ ${activity.location}`
          if (activity.cost) text += ` (${currency?.symbol}${activity.cost})`
          if (activity.completed) text += ' ✓'
          text += '\n'
          if (activity.notes) {
            text += `    备注：${activity.notes}\n`
          }
        })
      } else {
        text += '  暂无安排\n'
      }
    })
    
    text += '\n=== 准备清单 ===\n'
    const categories = {
      before: '出发前准备',
      packing: '行李打包',
      documents: '重要文件',
    }
    
    Object.entries(categories).forEach(([key, label]) => {
      const items = plan.checklist.filter(item => item.category === key)
      if (items.length > 0) {
        text += `\n${label}：\n`
        items.forEach(item => {
          text += `  ${item.completed ? '✓' : '□'} ${item.text}\n`
        })
      }
    })
    
    if (plan.notes) {
      text += `\n=== 备注 ===\n${plan.notes}\n`
    }
    
    const totalBudget = calculateTotalBudget(plan)
    text += `\n=== 费用统计 ===\n`
    text += `预算总额：${currency?.symbol}${plan.budget}\n`
    text += `实际花费：${currency?.symbol}${totalBudget}\n`
    text += `剩余预算：${currency?.symbol}${plan.budget - totalBudget}\n`
    
    return text
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-6">旅行规划器</h2>
        
        {!currentPlan ? (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">旅行标题 *</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="例如：日本东京7日游"
                />
              </div>
              
              <div>
                <Label htmlFor="destination">目的地 *</Label>
                <Input
                  id="destination"
                  value={destination}
                  onChange={(e) => setDestination(e.target.value)}
                  placeholder="例如：东京、大阪"
                />
              </div>
              
              <div>
                <Label htmlFor="startDate">开始日期 *</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="endDate">结束日期 *</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="budget">预算</Label>
                <Input
                  id="budget"
                  type="number"
                  value={budget}
                  onChange={(e) => setBudget(e.target.value)}
                  placeholder="0"
                />
              </div>
              
              <div>
                <Label htmlFor="currency">货币</Label>
                <Select value={currency} onValueChange={setCurrency}>
                  <SelectTrigger id="currency">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {CURRENCIES.map((curr) => (
                      <SelectItem key={curr.code} value={curr.code}>
                        {curr.symbol} {curr.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="description">描述（可选）</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="添加一些关于这次旅行的描述..."
                rows={3}
              />
            </div>
            
            <Button onClick={createPlan} className="w-full">
              <Plus className="w-4 h-4 mr-2" />
              创建旅行计划
            </Button>
            
            {plans.length > 0 && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">我的旅行计划</h3>
                <div className="space-y-2">
                  {plans.map((plan) => (
                    <div
                      key={plan.id}
                      className="p-4 rounded-lg border hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => setCurrentPlan(plan)}
                    >
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-semibold">{plan.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            <MapPin className="w-3 h-3 inline mr-1" />
                            {plan.destination}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            <Calendar className="w-3 h-3 inline mr-1" />
                            {new Date(plan.startDate).toLocaleDateString('zh-CN')} - 
                            {new Date(plan.endDate).toLocaleDateString('zh-CN')}
                          </p>
                        </div>
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            deletePlan(plan.id)
                          }}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold">{currentPlan.title}</h3>
                <p className="text-muted-foreground">
                  <MapPin className="w-4 h-4 inline mr-1" />
                  {currentPlan.destination} · 
                  <Calendar className="w-4 h-4 inline mx-1" />
                  {new Date(currentPlan.startDate).toLocaleDateString('zh-CN')} - 
                  {new Date(currentPlan.endDate).toLocaleDateString('zh-CN')}
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  size="icon"
                  variant="outline"
                  onClick={() => exportPlan(currentPlan)}
                >
                  <Download className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPlan(null)}
                >
                  返回列表
                </Button>
              </div>
            </div>
            
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">概览</TabsTrigger>
                <TabsTrigger value="itinerary">行程</TabsTrigger>
                <TabsTrigger value="checklist">清单</TabsTrigger>
                <TabsTrigger value="notes">备注</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-4">
                <Card className="p-4">
                  <h4 className="font-semibold mb-2">预算概览</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>预算总额</span>
                      <span className="font-semibold">
                        {CURRENCIES.find(c => c.code === currentPlan.currency)?.symbol}
                        {currentPlan.budget}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>实际花费</span>
                      <span className="font-semibold">
                        {CURRENCIES.find(c => c.code === currentPlan.currency)?.symbol}
                        {calculateTotalBudget(currentPlan)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>剩余预算</span>
                      <span className={`font-semibold ${currentPlan.budget - calculateTotalBudget(currentPlan) < 0 ? 'text-red-500' : 'text-green-500'}`}>
                        {CURRENCIES.find(c => c.code === currentPlan.currency)?.symbol}
                        {currentPlan.budget - calculateTotalBudget(currentPlan)}
                      </span>
                    </div>
                  </div>
                </Card>
                
                <Card className="p-4">
                  <h4 className="font-semibold mb-2">行程统计</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-2xl font-bold">{currentPlan.days.length}</p>
                      <p className="text-sm text-muted-foreground">天数</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold">
                        {currentPlan.days.reduce((total, day) => total + day.activities.length, 0)}
                      </p>
                      <p className="text-sm text-muted-foreground">活动数</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold">
                        {currentPlan.checklist.filter(item => item.completed).length}
                      </p>
                      <p className="text-sm text-muted-foreground">已完成准备</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold">
                        {currentPlan.checklist.filter(item => !item.completed).length}
                      </p>
                      <p className="text-sm text-muted-foreground">待完成准备</p>
                    </div>
                  </div>
                </Card>
                
                {currentPlan.description && (
                  <Card className="p-4">
                    <h4 className="font-semibold mb-2">旅行描述</h4>
                    <p className="text-sm">{currentPlan.description}</p>
                  </Card>
                )}
              </TabsContent>
              
              <TabsContent value="itinerary" className="space-y-4">
                {currentPlan.days.map((day, dayIndex) => (
                  <Card key={day.date} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold">
                        第{dayIndex + 1}天 - {new Date(day.date).toLocaleDateString('zh-CN', {
                          month: 'long',
                          day: 'numeric',
                          weekday: 'long',
                        })}
                      </h4>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const time = prompt('时间（例如：09:00）')
                          const title = prompt('活动名称')
                          if (time && title) {
                            addActivity(currentPlan.id, dayIndex, {
                              time,
                              title,
                              completed: false,
                            })
                          }
                        }}
                      >
                        <Plus className="w-4 h-4 mr-1" />
                        添加活动
                      </Button>
                    </div>
                    
                    {day.activities.length === 0 ? (
                      <p className="text-sm text-muted-foreground text-center py-8">
                        暂无安排，点击上方按钮添加活动
                      </p>
                    ) : (
                      <div className="space-y-2">
                        {day.activities.map((activity) => (
                          <div
                            key={activity.id}
                            className={`p-3 rounded-lg border ${
                              activity.completed ? 'bg-muted' : ''
                            }`}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <input
                                    type="checkbox"
                                    checked={activity.completed}
                                    onChange={() => updateActivity(
                                      currentPlan.id,
                                      dayIndex,
                                      activity.id,
                                      { completed: !activity.completed }
                                    )}
                                    className="rounded"
                                  />
                                  <span className="font-medium">
                                    {activity.time} - {activity.title}
                                  </span>
                                </div>
                                {activity.location && (
                                  <p className="text-sm text-muted-foreground ml-6">
                                    <MapPin className="w-3 h-3 inline mr-1" />
                                    {activity.location}
                                  </p>
                                )}
                                {activity.cost !== undefined && (
                                  <p className="text-sm text-muted-foreground ml-6">
                                    <DollarSign className="w-3 h-3 inline mr-1" />
                                    {CURRENCIES.find(c => c.code === currentPlan.currency)?.symbol}
                                    {activity.cost}
                                  </p>
                                )}
                                {activity.notes && (
                                  <p className="text-sm text-muted-foreground ml-6 mt-1">
                                    {activity.notes}
                                  </p>
                                )}
                              </div>
                              <Button
                                size="icon"
                                variant="ghost"
                                onClick={() => deleteActivity(currentPlan.id, dayIndex, activity.id)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </Card>
                ))}
              </TabsContent>
              
              <TabsContent value="checklist" className="space-y-4">
                <div className="space-y-6">
                  {Object.entries({
                    before: '出发前准备',
                    packing: '行李打包',
                    documents: '重要文件',
                  }).map(([category, label]) => (
                    <Card key={category} className="p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold">{label}</h4>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const text = prompt('添加新项目')
                            if (text) {
                              addChecklistItem(currentPlan.id, text, category as ChecklistItem['category'])
                            }
                          }}
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                      
                      <div className="space-y-2">
                        {currentPlan.checklist
                          .filter(item => item.category === category)
                          .map((item) => (
                            <div key={item.id} className="flex items-center justify-between">
                              <label className="flex items-center gap-2 cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={item.completed}
                                  onChange={() => toggleChecklistItem(currentPlan.id, item.id)}
                                  className="rounded"
                                />
                                <span className={item.completed ? 'line-through text-muted-foreground' : ''}>
                                  {item.text}
                                </span>
                              </label>
                              <Button
                                size="icon"
                                variant="ghost"
                                onClick={() => deleteChecklistItem(currentPlan.id, item.id)}
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                      </div>
                    </Card>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="notes" className="space-y-4">
                <Card className="p-4">
                  <Label htmlFor="notes">旅行备注</Label>
                  <Textarea
                    id="notes"
                    value={currentPlan.notes}
                    onChange={(e) => updatePlan(currentPlan.id, { notes: e.target.value })}
                    placeholder="添加旅行相关的备注信息..."
                    rows={10}
                    className="mt-2"
                  />
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </Card>
    </div>
  )
}
