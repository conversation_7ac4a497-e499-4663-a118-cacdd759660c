'use client'

import { useState, useCallback } from 'react'
import { Upload, Download, Image as ImageIcon, FileImage, AlertCircle, Info, X } from 'lucide-react'
import Image from 'next/image'

interface ConversionOptions {
  format: 'jpeg' | 'png' | 'webp' | 'bmp' | 'ico'
  quality: number
  width?: number
  height?: number
  maintainAspectRatio: boolean
}

interface ImageFile {
  id: string
  file: File
  preview: string
  converted?: {
    blob: Blob
    url: string
    size: number
  }
  error?: string
}

export default function ImageConverterTool() {
  const [images, setImages] = useState<ImageFile[]>([])
  const [options, setOptions] = useState<ConversionOptions>({
    format: 'png',
    quality: 90,
    maintainAspectRatio: true,
  })
  const [converting, setConverting] = useState(false)
  const [dragActive, setDragActive] = useState(false)

  // 处理文件拖放
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  // 处理文件放置
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const files = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    )
    handleFiles(files)
  }, [])

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files ? Array.from(e.target.files) : []
    handleFiles(files)
  }

  // 处理文件
  const handleFiles = (files: File[]) => {
    const newImages: ImageFile[] = files.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      preview: URL.createObjectURL(file),
    }))
    setImages(prev => [...prev, ...newImages])
  }

  // 移除图片
  const removeImage = (id: string) => {
    setImages(prev => {
      const image = prev.find(img => img.id === id)
      if (image) {
        URL.revokeObjectURL(image.preview)
        if (image.converted) {
          URL.revokeObjectURL(image.converted.url)
        }
      }
      return prev.filter(img => img.id !== id)
    })
  }

  // 转换图片
  const convertImages = async () => {
    setConverting(true)

    const updatedImages = await Promise.all(
      images.map(async (image) => {
        try {
          const converted = await convertImage(image.file, options)
          return {
            ...image,
            converted,
            error: undefined,
          }
        } catch (error) {
          return {
            ...image,
            error: error instanceof Error ? error.message : '转换失败',
            converted: undefined,
          }
        }
      })
    )

    setImages(updatedImages)
    setConverting(false)
  }

  // 转换单个图片
  const convertImage = (file: File, options: ConversionOptions): Promise<{ blob: Blob; url: string; size: number }> => {
    return new Promise((resolve, reject) => {
      const img = new window.Image()
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      img.onload = () => {
        try {
          // 计算目标尺寸
          let targetWidth = options.width || img.width
          let targetHeight = options.height || img.height

          if (options.maintainAspectRatio) {
            if (options.width && !options.height) {
              targetHeight = (img.height / img.width) * options.width
            } else if (options.height && !options.width) {
              targetWidth = (img.width / img.height) * options.height
            } else if (options.width && options.height) {
              const ratio = Math.min(options.width / img.width, options.height / img.height)
              targetWidth = img.width * ratio
              targetHeight = img.height * ratio
            }
          }

          // 设置画布尺寸
          canvas.width = targetWidth
          canvas.height = targetHeight

          // 绘制图片
          ctx?.drawImage(img, 0, 0, targetWidth, targetHeight)

          // 转换为指定格式
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const url = URL.createObjectURL(blob)
                resolve({ blob, url, size: blob.size })
              } else {
                reject(new Error('转换失败'))
              }
            },
            `image/${options.format}`,
            options.quality / 100
          )
        } catch (error) {
          reject(error)
        }
      }

      img.onerror = () => {
        reject(new Error('图片加载失败'))
      }

      img.src = URL.createObjectURL(file)
    })
  }

  // 下载单个图片
  const downloadImage = (image: ImageFile) => {
    if (!image.converted) return

    const link = document.createElement('a')
    const originalName = image.file.name.split('.').slice(0, -1).join('.')
    link.download = `${originalName}.${options.format}`
    link.href = image.converted.url
    link.click()
  }

  // 下载所有图片
  const downloadAll = () => {
    images.forEach(image => {
      if (image.converted) {
        downloadImage(image)
      }
    })
  }

  // 清空所有图片
  const clearAll = () => {
    images.forEach(image => {
      URL.revokeObjectURL(image.preview)
      if (image.converted) {
        URL.revokeObjectURL(image.converted.url)
      }
    })
    setImages([])
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 计算压缩率
  const getCompressionRate = (original: number, converted: number): string => {
    const rate = ((original - converted) / original) * 100
    return rate > 0 ? `-${rate.toFixed(1)}%` : `+${Math.abs(rate).toFixed(1)}%`
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片格式转换</h2>
        <p className="text-gray-600">
          批量转换图片格式，支持 JPEG、PNG、WebP 等多种格式
        </p>
      </div>

      {/* 选项设置 */}
      <div className="mb-6 bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="font-medium mb-4">转换选项</h3>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">目标格式</label>
            <select
              value={options.format}
              onChange={(e) => setOptions({ ...options, format: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="jpeg">JPEG</option>
              <option value="png">PNG</option>
              <option value="webp">WebP</option>
              <option value="bmp">BMP</option>
              <option value="ico">ICO</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              质量 ({options.quality}%)
            </label>
            <input
              type="range"
              min="10"
              max="100"
              value={options.quality}
              onChange={(e) => setOptions({ ...options, quality: parseInt(e.target.value) })}
              className="w-full"
              disabled={options.format === 'png' || options.format === 'bmp'}
            />
            {(options.format === 'png' || options.format === 'bmp') && (
              <p className="text-xs text-gray-500 mt-1">
                {options.format.toUpperCase()} 格式不支持质量调整
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">宽度（可选）</label>
            <input
              type="number"
              placeholder="自动"
              value={options.width || ''}
              onChange={(e) => setOptions({ ...options, width: e.target.value ? parseInt(e.target.value) : undefined })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">高度（可选）</label>
            <input
              type="number"
              placeholder="自动"
              value={options.height || ''}
              onChange={(e) => setOptions({ ...options, height: e.target.value ? parseInt(e.target.value) : undefined })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>

        <div className="mt-4">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={options.maintainAspectRatio}
              onChange={(e) => setOptions({ ...options, maintainAspectRatio: e.target.checked })}
              className="rounded"
            />
            <span className="text-sm">保持宽高比</span>
          </label>
        </div>
      </div>

      {/* 上传区域 */}
      <div
        className={`mb-6 border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id="file-upload"
          className="hidden"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
        />
        <label
          htmlFor="file-upload"
          className="cursor-pointer flex flex-col items-center"
        >
          <Upload className="w-12 h-12 text-gray-400 mb-3" />
          <p className="text-gray-600 mb-1">
            点击选择图片或拖放到此处
          </p>
          <p className="text-sm text-gray-500">
            支持 JPG、PNG、GIF、BMP、WebP 等格式
          </p>
        </label>
      </div>

      {/* 图片列表 */}
      {images.length > 0 && (
        <div className="mb-6 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">图片列表 ({images.length})</h3>
            <div className="flex gap-2">
              <button
                onClick={convertImages}
                disabled={converting}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300"
              >
                {converting ? '转换中...' : '开始转换'}
              </button>
              {images.some(img => img.converted) && (
                <button
                  onClick={downloadAll}
                  className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                >
                  下载全部
                </button>
              )}
              <button
                onClick={clearAll}
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
              >
                清空
              </button>
            </div>
          </div>

          <div className="space-y-3">
            {images.map((image) => (
              <div key={image.id} className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-start gap-4">
                  <Image
                    src={image.preview}
                    alt="预览"
                    width={80}
                    height={80}
                    className="w-20 h-20 object-cover rounded"
                    unoptimized
                  />
                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="font-medium">{image.file.name}</p>
                        <p className="text-sm text-gray-500">
                          原始大小：{formatFileSize(image.file.size)}
                        </p>
                      </div>
                      <button
                        onClick={() => removeImage(image.id)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>

                    {image.converted && (
                      <div className="mt-2 flex items-center gap-4">
                        <p className="text-sm text-green-600">
                          转换完成：{formatFileSize(image.converted.size)}
                          <span className="ml-1 font-medium">
                            ({getCompressionRate(image.file.size, image.converted.size)})
                          </span>
                        </p>
                        <button
                          onClick={() => downloadImage(image)}
                          className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                        >
                          <Download className="w-4 h-4" />
                          下载
                        </button>
                      </div>
                    )}

                    {image.error && (
                      <p className="mt-2 text-sm text-red-600">{image.error}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持批量转换多张图片</li>
              <li>可以调整图片尺寸和质量</li>
              <li>WebP 格式通常能获得最佳的压缩效果</li>
              <li>PNG 格式适合需要透明背景的图片</li>
              <li>JPEG 格式适合照片类图片</li>
              <li>所有处理都在浏览器本地完成，不会上传到服务器</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 格式说明 */}
      <div className="mt-6 grid md:grid-cols-2 gap-4">
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <FileImage className="w-4 h-4" />
            支持的输入格式
          </h4>
          <p className="text-sm text-gray-600">
            JPEG, PNG, GIF, BMP, WebP, TIFF, ICO
          </p>
        </div>
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <ImageIcon className="w-4 h-4" />
            输出格式特点
          </h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• JPEG：有损压缩，适合照片</li>
            <li>• PNG：无损压缩，支持透明</li>
            <li>• WebP：现代格式，压缩率高</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
