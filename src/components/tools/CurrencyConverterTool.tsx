'use client'

import { useState, useEffect, useCallback } from 'react'
import { ArrowLeftRight, TrendingUp, TrendingDown, DollarSign, RefreshCw, History } from 'lucide-react'

interface Currency {
  code: string
  name: string
  symbol: string
  flag: string
}

interface ConversionRecord {
  date: string
  from: string
  to: string
  amount: number
  result: number
  rate: number
}

// 主要货币列表
const currencies: Currency[] = [
  { code: 'CNY', name: '人民币', symbol: '¥', flag: '🇨🇳' },
  { code: 'USD', name: '美元', symbol: '$', flag: '🇺🇸' },
  { code: 'EUR', name: '欧元', symbol: '€', flag: '🇪🇺' },
  { code: 'JPY', name: '日元', symbol: '¥', flag: '🇯🇵' },
  { code: 'GBP', name: '英镑', symbol: '£', flag: '🇬🇧' },
  { code: 'AUD', name: '澳元', symbol: 'A$', flag: '🇦🇺' },
  { code: 'CAD', name: '加元', symbol: 'C$', flag: '🇨🇦' },
  { code: 'CHF', name: '瑞士法郎', symbol: 'Fr', flag: '🇨🇭' },
  { code: 'HKD', name: '港元', symbol: 'HK$', flag: '🇭🇰' },
  { code: 'SGD', name: '新加坡元', symbol: 'S$', flag: '🇸🇬' },
  { code: 'SEK', name: '瑞典克朗', symbol: 'kr', flag: '🇸🇪' },
  { code: 'NOK', name: '挪威克朗', symbol: 'kr', flag: '🇳🇴' },
  { code: 'NZD', name: '新西兰元', symbol: 'NZ$', flag: '🇳🇿' },
  { code: 'MXN', name: '墨西哥比索', symbol: '$', flag: '🇲🇽' },
  { code: 'ZAR', name: '南非兰特', symbol: 'R', flag: '🇿🇦' },
  { code: 'RUB', name: '俄罗斯卢布', symbol: '₽', flag: '🇷🇺' },
  { code: 'INR', name: '印度卢比', symbol: '₹', flag: '🇮🇳' },
  { code: 'KRW', name: '韩元', symbol: '₩', flag: '🇰🇷' },
  { code: 'THB', name: '泰铢', symbol: '฿', flag: '🇹🇭' },
  { code: 'MYR', name: '马来西亚林吉特', symbol: 'RM', flag: '🇲🇾' },
]

// 模拟汇率数据（以 USD 为基准）
const mockRates: { [key: string]: number } = {
  USD: 1,
  CNY: 7.2456,
  EUR: 0.9234,
  JPY: 150.123,
  GBP: 0.7856,
  AUD: 1.5234,
  CAD: 1.3456,
  CHF: 0.8901,
  HKD: 7.8234,
  SGD: 1.3567,
  SEK: 10.5678,
  NOK: 10.6789,
  NZD: 1.6234,
  MXN: 17.2345,
  ZAR: 18.9012,
  RUB: 92.3456,
  INR: 83.1234,
  KRW: 1325.67,
  THB: 35.6789,
  MYR: 4.6789,
}

export default function CurrencyConverterTool() {
  const [amount, setAmount] = useState('100')
  const [fromCurrency, setFromCurrency] = useState('USD')
  const [toCurrency, setToCurrency] = useState('CNY')
  const [result, setResult] = useState<number | null>(null)
  const [rate, setRate] = useState<number | null>(null)
  const [history, setHistory] = useState<ConversionRecord[]>([])
  const [lastUpdate, setLastUpdate] = useState(new Date())
  const [favorites, setFavorites] = useState<string[]>(['USD', 'CNY', 'EUR', 'JPY'])

  // 计算汇率
  const calculateRate = (from: string, to: string): number => {
    const fromRate = mockRates[from] || 1
    const toRate = mockRates[to] || 1
    return toRate / fromRate
  }

  // 执行转换
  const convert = useCallback(() => {
    const amountNum = parseFloat(amount)
    if (isNaN(amountNum) || amountNum <= 0) {
      setResult(null)
      setRate(null)
      return
    }

    const conversionRate = calculateRate(fromCurrency, toCurrency)
    const convertedAmount = amountNum * conversionRate
    
    setRate(conversionRate)
    setResult(convertedAmount)

    // 保存到历史记录
    const newRecord: ConversionRecord = {
      date: new Date().toLocaleString('zh-CN'),
      from: fromCurrency,
      to: toCurrency,
      amount: amountNum,
      result: convertedAmount,
      rate: conversionRate,
    }
    
    setHistory(prevHistory => {
      const updatedHistory = [newRecord, ...prevHistory.slice(0, 9)]
      localStorage.setItem('currency-history', JSON.stringify(updatedHistory))
      return updatedHistory
    })
  }, [amount, fromCurrency, toCurrency])

  // 监听输入变化
  useEffect(() => {
    convert()
  }, [convert])

  // 加载历史记录
  useEffect(() => {
    const saved = localStorage.getItem('currency-history')
    if (saved) {
      setHistory(JSON.parse(saved))
    }

    const savedFavorites = localStorage.getItem('currency-favorites')
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites))
    }
  }, [])

  // 交换货币
  const swapCurrencies = () => {
    setFromCurrency(toCurrency)
    setToCurrency(fromCurrency)
  }

  // 切换收藏
  const toggleFavorite = (code: string) => {
    const newFavorites = favorites.includes(code)
      ? favorites.filter(f => f !== code)
      : [...favorites, code]
    setFavorites(newFavorites)
    localStorage.setItem('currency-favorites', JSON.stringify(newFavorites))
  }

  // 获取货币信息
  const getCurrency = (code: string) => currencies.find(c => c.code === code)

  // 格式化数字
  const formatNumber = (num: number, code: string) => {
    const currency = getCurrency(code)
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(num)
  }

  // 应用历史记录
  const applyHistory = (record: ConversionRecord) => {
    setAmount(record.amount.toString())
    setFromCurrency(record.from)
    setToCurrency(record.to)
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">汇率换算器</h2>
        <p className="text-gray-600">
          实时汇率转换，支持全球主要货币
        </p>
      </div>

      {/* 最后更新时间 */}
      <div className="text-sm text-gray-500 mb-4 flex items-center justify-end">
        <RefreshCw className="w-4 h-4 mr-1" />
        汇率更新时间：{lastUpdate.toLocaleString('zh-CN')}
      </div>

      {/* 主要转换区域 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-center">
          {/* 源货币 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              从
            </label>
            <select
              value={fromCurrency}
              onChange={(e) => setFromCurrency(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {currencies.map(currency => (
                <option key={currency.code} value={currency.code}>
                  {currency.flag} {currency.code} - {currency.name}
                </option>
              ))}
            </select>
            <div className="mt-3">
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="输入金额"
                className="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 交换按钮 */}
          <div className="flex justify-center">
            <button
              onClick={swapCurrencies}
              className="p-3 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
              title="交换货币"
            >
              <ArrowLeftRight className="w-6 h-6" />
            </button>
          </div>

          {/* 目标货币 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              到
            </label>
            <select
              value={toCurrency}
              onChange={(e) => setToCurrency(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {currencies.map(currency => (
                <option key={currency.code} value={currency.code}>
                  {currency.flag} {currency.code} - {currency.name}
                </option>
              ))}
            </select>
            <div className="mt-3">
              {result !== null && (
                <div className="w-full px-4 py-3 text-lg bg-gray-50 border border-gray-300 rounded-md">
                  {getCurrency(toCurrency)?.symbol} {formatNumber(result, toCurrency)}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 汇率信息 */}
        {rate !== null && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <div className="text-center">
              <p className="text-sm text-blue-600 mb-1">当前汇率</p>
              <p className="text-xl font-semibold">
                1 {fromCurrency} = {rate.toFixed(4)} {toCurrency}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* 快捷货币选择 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <h3 className="font-medium mb-3">常用货币</h3>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
          {favorites.map(code => {
            const currency = getCurrency(code)
            if (!currency) return null
            
            return (
              <button
                key={code}
                onClick={() => setFromCurrency(code)}
                className={`p-3 rounded-lg border transition-colors ${
                  fromCurrency === code
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-center">
                  <span className="text-2xl mr-2">{currency.flag}</span>
                  <div className="text-left">
                    <div className="font-medium">{code}</div>
                    <div className="text-xs text-gray-500">{currency.name}</div>
                  </div>
                </div>
              </button>
            )
          })}
        </div>
      </div>

      {/* 汇率趋势（模拟） */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <h3 className="font-medium mb-3">主要货币对 CNY 汇率</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {['USD', 'EUR', 'JPY', 'GBP', 'HKD', 'SGD'].map(code => {
            const currency = getCurrency(code)
            const rate = calculateRate(code, 'CNY')
            const trend = Math.random() > 0.5
            
            return (
              <div key={code} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <span className="text-xl mr-2">{currency?.flag}</span>
                  <span className="font-medium">{code}/CNY</span>
                </div>
                <div className="flex items-center">
                  <span className="font-semibold mr-2">{rate.toFixed(4)}</span>
                  {trend ? (
                    <TrendingUp className="w-4 h-4 text-green-600" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-600" />
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* 历史记录 */}
      {history.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center mb-3">
            <History className="w-5 h-5 mr-2" />
            <h3 className="font-medium">转换历史</h3>
          </div>
          <div className="space-y-2">
            {history.map((record, index) => (
              <button
                key={index}
                onClick={() => applyHistory(record)}
                className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-medium">
                      {formatNumber(record.amount, record.from)} {record.from}
                    </span>
                    <span className="mx-2">→</span>
                    <span className="font-medium">
                      {formatNumber(record.result, record.to)} {record.to}
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {record.date}
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  汇率: 1 {record.from} = {record.rate.toFixed(4)} {record.to}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 说明 */}
      <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
        <div className="flex items-start">
          <DollarSign className="h-5 w-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-yellow-900">
            <h3 className="font-semibold mb-1">温馨提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>本工具提供的汇率仅供参考，实际汇率请以银行或金融机构为准</li>
              <li>汇率会实时波动，建议在进行实际交易前再次确认</li>
              <li>大额换汇请咨询专业金融机构</li>
              <li>不同银行的汇率可能存在差异</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
