'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Shield, Eye, EyeOff, AlertTriangle, CheckCircle, XCircle, Info } from 'lucide-react'

const PasswordStrengthTool: React.FC = () => {
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [strength, setStrength] = useState({
    score: 0,
    level: 'Very Weak',
    color: 'text-red-500',
    bgColor: 'bg-red-500',
    percentage: 0
  })
  const [checks, setChecks] = useState({
    length: false,
    lowercase: false,
    uppercase: false,
    numbers: false,
    symbols: false,
    noRepeating: false,
    noSequential: false,
    noCommon: false
  })
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [entropy, setEntropy] = useState(0)

  // 常见密码列表（简化版）
  const commonPasswords = useMemo(() => [
    'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
    'admin', 'letmein', 'welcome', 'monkey', 'dragon', 'master', 'hello',
    'login', 'pass', 'admin123', 'root', 'user', 'test', 'guest'
  ], [])

  // 检查密码强度
  useEffect(() => {
    if (!password) {
      setStrength({ score: 0, level: 'Very Weak', color: 'text-red-500', bgColor: 'bg-red-500', percentage: 0 })
      setChecks({
        length: false,
        lowercase: false,
        uppercase: false,
        numbers: false,
        symbols: false,
        noRepeating: false,
        noSequential: false,
        noCommon: false
      })
      setSuggestions([])
      setEntropy(0)
      return
    }

    // 各项检查
    const newChecks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /[0-9]/.test(password),
      symbols: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
      noRepeating: !hasRepeatingCharacters(password),
      noSequential: !hasSequentialCharacters(password),
      noCommon: !commonPasswords.includes(password.toLowerCase())
    }

    // 计算强度分数
    let score = 0
    Object.values(newChecks).forEach(check => {
      if (check) score += 1
    })

    // 额外分数：长度奖励
    if (password.length >= 12) score += 1
    if (password.length >= 16) score += 1

    // 强度等级
    let level = 'Very Weak'
    let color = 'text-red-500'
    let bgColor = 'bg-red-500'
    let percentage = 0

    if (score >= 8) {
      level = 'Very Strong'
      color = 'text-green-500'
      bgColor = 'bg-green-500'
      percentage = 100
    } else if (score >= 6) {
      level = 'Strong'
      color = 'text-green-400'
      bgColor = 'bg-green-400'
      percentage = 80
    } else if (score >= 4) {
      level = 'Medium'
      color = 'text-yellow-500'
      bgColor = 'bg-yellow-500'
      percentage = 60
    } else if (score >= 2) {
      level = 'Weak'
      color = 'text-orange-500'
      bgColor = 'bg-orange-500'
      percentage = 40
    } else {
      percentage = 20
    }

    setStrength({ score, level, color, bgColor, percentage })
    setChecks(newChecks)

    // 生成建议
    const newSuggestions = []
    if (!newChecks.length) newSuggestions.push('使用至少8个字符')
    if (!newChecks.lowercase) newSuggestions.push('包含小写字母')
    if (!newChecks.uppercase) newSuggestions.push('包含大写字母')
    if (!newChecks.numbers) newSuggestions.push('包含数字')
    if (!newChecks.symbols) newSuggestions.push('包含特殊字符')
    if (!newChecks.noRepeating) newSuggestions.push('避免重复字符')
    if (!newChecks.noSequential) newSuggestions.push('避免连续字符')
    if (!newChecks.noCommon) newSuggestions.push('避免常见密码')
    if (password.length < 12) newSuggestions.push('建议使用12个字符以上')

    setSuggestions(newSuggestions)

    // 计算熵值
    const charsetSize = calculateCharsetSize(password)
    const entropyValue = password.length * Math.log2(charsetSize)
    setEntropy(entropyValue)
  }, [password, commonPasswords])

  // 检查重复字符
  const hasRepeatingCharacters = (pass: string): boolean => {
    for (let i = 0; i < pass.length - 2; i++) {
      if (pass[i] === pass[i + 1] && pass[i] === pass[i + 2]) {
        return true
      }
    }
    return false
  }

  // 检查连续字符
  const hasSequentialCharacters = (pass: string): boolean => {
    const sequences = [
      'abcdefghijklmnopqrstuvwxyz',
      'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      '01234567890',
      'qwertyuiop',
      'asdfghjkl',
      'zxcvbnm'
    ]

    for (const seq of sequences) {
      for (let i = 0; i < seq.length - 2; i++) {
        const subSeq = seq.substring(i, i + 3)
        if (pass.includes(subSeq)) return true
      }
    }
    return false
  }

  // 计算字符集大小
  const calculateCharsetSize = (pass: string): number => {
    let size = 0
    if (/[a-z]/.test(pass)) size += 26
    if (/[A-Z]/.test(pass)) size += 26
    if (/[0-9]/.test(pass)) size += 10
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pass)) size += 32
    return size || 1
  }

  // 生成强密码
  const generateStrongPassword = () => {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?'
    
    const allChars = lowercase + uppercase + numbers + symbols
    let result = ''
    
    // 确保包含各种字符类型
    result += lowercase[Math.floor(Math.random() * lowercase.length)]
    result += uppercase[Math.floor(Math.random() * uppercase.length)]
    result += numbers[Math.floor(Math.random() * numbers.length)]
    result += symbols[Math.floor(Math.random() * symbols.length)]
    
    // 填充剩余长度
    for (let i = 4; i < 16; i++) {
      result += allChars[Math.floor(Math.random() * allChars.length)]
    }
    
    // 打乱顺序
    result = result.split('').sort(() => Math.random() - 0.5).join('')
    
    setPassword(result)
  }

  // 复制密码
  const copyPassword = () => {
    navigator.clipboard.writeText(password)
  }

  // 清空密码
  const clearPassword = () => {
    setPassword('')
  }

  // 估算破解时间
  const estimateCrackTime = (entropy: number): string => {
    const attemptsPerSecond = 1000000000 // 10亿次/秒
    const combinations = Math.pow(2, entropy)
    const seconds = combinations / (2 * attemptsPerSecond)
    
    if (seconds < 1) return '瞬间'
    if (seconds < 60) return `${Math.round(seconds)}秒`
    if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`
    if (seconds < 86400) return `${Math.round(seconds / 3600)}小时`
    if (seconds < 31536000) return `${Math.round(seconds / 86400)}天`
    if (seconds < 31536000000) return `${Math.round(seconds / 31536000)}年`
    return '数千年'
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Shield className="w-8 h-8" />
          密码强度检测器
        </h1>
        <p className="text-muted-foreground">
          检测密码强度，提供安全性评估和改进建议
        </p>
      </div>

      {/* 密码输入区域 */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="password">输入密码</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="请输入要检测的密码..."
                className="pr-10"
              />
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 -translate-y-1/2"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={generateStrongPassword} className="flex-1">
              生成强密码
            </Button>
            <Button variant="outline" onClick={copyPassword} disabled={!password}>
              复制
            </Button>
            <Button variant="outline" onClick={clearPassword} disabled={!password}>
              清空
            </Button>
          </div>
        </div>
      </Card>

      {password && (
        <>
          {/* 强度显示 */}
          <Card className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">密码强度</h3>
                <span className={`font-bold ${strength.color}`}>{strength.level}</span>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>强度等级</span>
                  <span>{strength.percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-500 ${strength.bgColor}`}
                    style={{ width: `${strength.percentage}%` }}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">强度分数：</span>
                  <span className="ml-2">{strength.score}/10</span>
                </div>
                <div>
                  <span className="font-medium">密码长度：</span>
                  <span className="ml-2">{password.length} 字符</span>
                </div>
                <div>
                  <span className="font-medium">熵值：</span>
                  <span className="ml-2">{entropy.toFixed(1)} bits</span>
                </div>
                <div>
                  <span className="font-medium">破解时间：</span>
                  <span className="ml-2">{estimateCrackTime(entropy)}</span>
                </div>
              </div>
            </div>
          </Card>

          {/* 详细检查 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">详细检查</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <CheckItem 
                checked={checks.length} 
                text="至少8个字符" 
                detail={`当前：${password.length}个字符`}
              />
              <CheckItem 
                checked={checks.lowercase} 
                text="包含小写字母" 
                detail="a-z"
              />
              <CheckItem 
                checked={checks.uppercase} 
                text="包含大写字母" 
                detail="A-Z"
              />
              <CheckItem 
                checked={checks.numbers} 
                text="包含数字" 
                detail="0-9"
              />
              <CheckItem 
                checked={checks.symbols} 
                text="包含特殊字符" 
                detail="!@#$%^&*等"
              />
              <CheckItem 
                checked={checks.noRepeating} 
                text="无重复字符" 
                detail="避免aaa、111等"
              />
              <CheckItem 
                checked={checks.noSequential} 
                text="无连续字符" 
                detail="避免abc、123等"
              />
              <CheckItem 
                checked={checks.noCommon} 
                text="非常见密码" 
                detail="避免password等"
              />
            </div>
          </Card>

          {/* 改进建议 */}
          {suggestions.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-yellow-500" />
                改进建议
              </h3>
              <ul className="space-y-2">
                {suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start gap-2 text-sm">
                    <Info className="w-4 h-4 text-blue-500 mt-0.5" />
                    <span>{suggestion}</span>
                  </li>
                ))}
              </ul>
            </Card>
          )}
        </>
      )}

      {/* 安全提示 */}
      <Card className="p-6 border-blue-200 bg-blue-50 dark:bg-blue-900/20">
        <div className="flex items-start gap-3">
          <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-blue-800 dark:text-blue-200">密码安全建议：</p>
            <ul className="mt-1 text-blue-700 dark:text-blue-300 space-y-1">
              <li>• 使用12个字符以上的密码</li>
              <li>• 包含大小写字母、数字和特殊字符</li>
              <li>• 避免使用个人信息（生日、姓名等）</li>
              <li>• 不要在多个网站使用相同密码</li>
              <li>• 定期更换重要账户密码</li>
              <li>• 考虑使用密码管理器</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}

// 检查项组件
const CheckItem: React.FC<{ checked: boolean; text: string; detail: string }> = ({ 
  checked, 
  text, 
  detail 
}) => (
  <div className="flex items-start gap-2 p-3 rounded-lg border">
    {checked ? (
      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500 mt-0.5" />
    )}
    <div className="flex-1">
      <div className={`font-medium ${checked ? 'text-green-700' : 'text-red-700'}`}>
        {text}
      </div>
      <div className="text-sm text-muted-foreground">{detail}</div>
    </div>
  </div>
)

export default PasswordStrengthTool
