'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  ToolInput,
  ToolOutput,
  ToolTabs,
  TabPanel,
  QuickActions
} from '@/components/common'
import { Hash } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'

interface Base64ToolProps {
  locale: string
}

export default function Base64Tool({ locale }: Base64ToolProps) {
  const { t } = useTranslation({ namespace: 'tools/base64' })
  const [activeTab, setActiveTab] = useState('encode')
  const [encodeInput, setEncodeInput] = useState('')
  const [encodeOutput, setEncodeOutput] = useState('')
  const [decodeInput, setDecodeInput] = useState('')
  const [decodeOutput, setDecodeOutput] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState('')

  // 编码处理
  const handleEncode = async () => {
    setIsProcessing(true)
    setError('')
    try {
      const encoded = btoa(unescape(encodeURIComponent(encodeInput)))
      setEncodeOutput(encoded)
    } catch (err) {
      setError(t('encode_error') || '编码失败：输入包含无效字符')
      setEncodeOutput('')
    } finally {
      setIsProcessing(false)
    }
  }

  // 解码处理
  const handleDecode = async () => {
    setIsProcessing(true)
    setError('')
    try {
      const decoded = decodeURIComponent(escape(atob(decodeInput)))
      setDecodeOutput(decoded)
    } catch (err) {
      setError(t('decode_error') || '解码失败：输入不是有效的 Base64')
      setDecodeOutput('')
    } finally {
      setIsProcessing(false)
    }
  }

  // 重置编码
  const handleEncodeReset = () => {
    setEncodeInput('')
    setEncodeOutput('')
    setError('')
  }

  // 重置解码
  const handleDecodeReset = () => {
    setDecodeInput('')
    setDecodeOutput('')
    setError('')
  }

  // 复制编码结果
  const handleEncodeCopy = async () => {
    if (encodeOutput) {
      await navigator.clipboard.writeText(encodeOutput)
    }
  }

  // 复制解码结果
  const handleDecodeCopy = async () => {
    if (decodeOutput) {
      await navigator.clipboard.writeText(decodeOutput)
    }
  }

  const tabs = [
    { id: 'encode', label: t('encode_tab') || '编码' },
    { id: 'decode', label: t('decode_tab') || '解码' }
  ]

  return (
    <ToolContainer>
      <ToolHeader
        title={t('title') || 'Base64 编码/解码'}
        description={t('description') || '在线Base64编码解码工具，支持文本和文件的编码解码'}
        icon={<Hash className="w-5 h-5" />}
        locale={locale}
      />

      <div className="space-y-6">
        <ToolTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          variant="pills"
        />

        {/* 编码标签页 */}
        <TabPanel tabId="encode" activeTab={activeTab}>
          <div className="space-y-6">
            <ToolInput
              label={t('encode_input_label') || '输入文本'}
              value={encodeInput}
              onChange={(e) => setEncodeInput(e.target.value)}
              placeholder={t('encode_input_placeholder') || '请输入要编码的文本'}
              error={activeTab === 'encode' ? error : undefined}
              showClear
              onClear={() => setEncodeInput('')}
            />

            <QuickActions
              onExecute={handleEncode}
              onReset={handleEncodeReset}
              onCopy={handleEncodeCopy}
              executing={isProcessing}
              canCopy={!!encodeOutput}
            />

            <ToolOutput
              label={t('encode_output_label') || 'Base64 编码结果'}
              value={encodeOutput}
              showCopy
              height="md"
            />
          </div>
        </TabPanel>

        {/* 解码标签页 */}
        <TabPanel tabId="decode" activeTab={activeTab}>
          <div className="space-y-6">
            <ToolInput
              label={t('decode_input_label') || '输入 Base64'}
              value={decodeInput}
              onChange={(e) => setDecodeInput(e.target.value)}
              placeholder={t('decode_input_placeholder') || '请输入要解码的 Base64 字符串'}
              error={activeTab === 'decode' ? error : undefined}
              showClear
              onClear={() => setDecodeInput('')}
            />

            <QuickActions
              onExecute={handleDecode}
              onReset={handleDecodeReset}
              onCopy={handleDecodeCopy}
              executing={isProcessing}
              canCopy={!!decodeOutput}
            />

            <ToolOutput
              label={t('decode_output_label') || '解码结果'}
              value={decodeOutput}
              showCopy
              height="md"
            />
          </div>
        </TabPanel>
      </div>
    </ToolContainer>
  )
}
