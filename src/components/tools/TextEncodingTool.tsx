'use client'

import React, { useState, useCallback } from 'react'
import { ArrowRightLeft, Co<PERSON>, Check, FileText, AlertCircle, Download, Upload } from 'lucide-react'

interface EncodingOption {
  value: string
  label: string
  encode: (text: string) => string
  decode: (text: string) => string
}

const encodingOptions: EncodingOption[] = [
  {
    value: 'base64',
    label: 'Base64',
    encode: (text: string) => btoa(unescape(encodeURIComponent(text))),
    decode: (text: string) => {
      try {
        return decodeURIComponent(escape(atob(text)))
      } catch {
        throw new Error('无效的 Base64 编码')
      }
    }
  },
  {
    value: 'url',
    label: 'URL 编码',
    encode: (text: string) => encodeURIComponent(text),
    decode: (text: string) => {
      try {
        return decodeURIComponent(text)
      } catch {
        throw new Error('无效的 URL 编码')
      }
    }
  },
  {
    value: 'html',
    label: 'HTML 实体',
    encode: (text: string) => {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    },
    decode: (text: string) => {
      const div = document.createElement('div')
      div.innerHTML = text
      return div.textContent || ''
    }
  },
  {
    value: 'unicode',
    label: 'Unicode 转义',
    encode: (text: string) => {
      return text.split('').map(char => {
        const code = char.charCodeAt(0)
        if (code > 127) {
          return '\\u' + code.toString(16).padStart(4, '0')
        }
        return char
      }).join('')
    },
    decode: (text: string) => {
      return text.replace(/\\u([0-9a-fA-F]{4})/g, (match, code) => {
        return String.fromCharCode(parseInt(code, 16))
      })
    }
  },
  {
    value: 'hex',
    label: '十六进制',
    encode: (text: string) => {
      return text.split('').map(char => 
        char.charCodeAt(0).toString(16).padStart(2, '0')
      ).join(' ')
    },
    decode: (text: string) => {
      const hex = text.replace(/\s+/g, '')
      let result = ''
      for (let i = 0; i < hex.length; i += 2) {
        result += String.fromCharCode(parseInt(hex.substr(i, 2), 16))
      }
      return result
    }
  },
  {
    value: 'binary',
    label: '二进制',
    encode: (text: string) => {
      return text.split('').map(char => 
        char.charCodeAt(0).toString(2).padStart(8, '0')
      ).join(' ')
    },
    decode: (text: string) => {
      return text.split(/\s+/).filter(bin => bin).map(bin => 
        String.fromCharCode(parseInt(bin, 2))
      ).join('')
    }
  },
  {
    value: 'morse',
    label: '摩斯密码',
    encode: (text: string) => {
      const morseCode: { [key: string]: string } = {
        'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
        'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
        'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
        'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
        'Y': '-.--', 'Z': '--..', '0': '-----', '1': '.----', '2': '..---',
        '3': '...--', '4': '....-', '5': '.....', '6': '-....', '7': '--...',
        '8': '---..', '9': '----.', ' ': '/'
      }
      return text.toUpperCase().split('').map(char => morseCode[char] || char).join(' ')
    },
    decode: (text: string) => {
      const morseToChar: { [key: string]: string } = {
        '.-': 'A', '-...': 'B', '-.-.': 'C', '-..': 'D', '.': 'E', '..-.': 'F',
        '--.': 'G', '....': 'H', '..': 'I', '.---': 'J', '-.-': 'K', '.-..': 'L',
        '--': 'M', '-.': 'N', '---': 'O', '.--.': 'P', '--.-': 'Q', '.-.': 'R',
        '...': 'S', '-': 'T', '..-': 'U', '...-': 'V', '.--': 'W', '-..-': 'X',
        '-.--': 'Y', '--..': 'Z', '-----': '0', '.----': '1', '..---': '2',
        '...--': '3', '....-': '4', '.....': '5', '-....': '6', '--...': '7',
        '---..': '8', '----.': '9', '/': ' '
      }
      return text.split(' ').map(code => morseToChar[code] || code).join('')
    }
  },
  {
    value: 'rot13',
    label: 'ROT13',
    encode: (text: string) => {
      return text.replace(/[A-Za-z]/g, char => {
        const code = char.charCodeAt(0)
        const base = code < 97 ? 65 : 97
        return String.fromCharCode((code - base + 13) % 26 + base)
      })
    },
    decode: (text: string) => {
      // ROT13 是自反的，编码和解码相同
      return text.replace(/[A-Za-z]/g, char => {
        const code = char.charCodeAt(0)
        const base = code < 97 ? 65 : 97
        return String.fromCharCode((code - base + 13) % 26 + base)
      })
    }
  }
]

export default function TextEncodingTool() {
  const [inputText, setInputText] = useState('')
  const [outputText, setOutputText] = useState('')
  const [selectedEncoding, setSelectedEncoding] = useState('base64')
  const [mode, setMode] = useState<'encode' | 'decode'>('encode')
  const [error, setError] = useState('')
  const [copied, setCopied] = useState(false)
  
  // 处理编码/解码
  const processText = useCallback(() => {
    if (!inputText.trim()) {
      setOutputText('')
      setError('')
      return
    }
    
    const option = encodingOptions.find(opt => opt.value === selectedEncoding)
    if (!option) return
    
    try {
      const result = mode === 'encode' 
        ? option.encode(inputText)
        : option.decode(inputText)
      setOutputText(result)
      setError('')
    } catch (err) {
      setError(err instanceof Error ? err.message : '处理失败')
      setOutputText('')
    }
  }, [inputText, selectedEncoding, mode])
  
  // 监听输入变化
  React.useEffect(() => {
    processText()
  }, [processText])
  
  // 交换输入输出
  const swapTexts = () => {
    setInputText(outputText)
    setOutputText(inputText)
    setMode(mode === 'encode' ? 'decode' : 'encode')
  }
  
  // 复制结果
  const copyToClipboard = async () => {
    if (!outputText) return
    
    try {
      await navigator.clipboard.writeText(outputText)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }
  
  // 清空所有
  const clearAll = () => {
    setInputText('')
    setOutputText('')
    setError('')
  }
  
  // 下载结果
  const downloadResult = () => {
    if (!outputText) return
    
    const blob = new Blob([outputText], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${mode}_${selectedEncoding}_${Date.now()}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  
  // 上传文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputText(content)
    }
    reader.readAsText(file)
  }
  
  // 示例文本
  const examples = [
    { label: '中文示例', text: '你好，世界！Hello World!' },
    { label: '英文示例', text: 'The quick brown fox jumps over the lazy dog.' },
    { label: '特殊字符', text: '!@#$%^&*()_+-=[]{}|;:\'",.<>?/~`' },
    { label: '混合内容', text: 'Test123测试!@#$%^&*()' },
    { label: 'Emoji', text: '😀🎉🌟 Hello 你好 👋' }
  ]
  
  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本编码转换工具</h2>
        <p className="text-gray-600">
          支持多种编码格式的相互转换，包括 Base64、URL、Unicode、十六进制等
        </p>
      </div>
      
      {/* 控制栏 */}
      <div className="mb-6 flex flex-wrap gap-4 items-center">
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium mb-1">编码格式</label>
          <select
            value={selectedEncoding}
            onChange={(e) => setSelectedEncoding(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {encodingOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={() => setMode('encode')}
            className={`px-4 py-2 rounded-md transition-colors ${
              mode === 'encode'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            编码
          </button>
          <button
            onClick={() => setMode('decode')}
            className={`px-4 py-2 rounded-md transition-colors ${
              mode === 'decode'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            解码
          </button>
        </div>
        
        <button
          onClick={swapTexts}
          className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md"
          title="交换输入输出"
        >
          <ArrowRightLeft className="w-5 h-5" />
        </button>
      </div>
      
      {/* 主要内容区 */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* 输入区 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="font-medium">输入文本</label>
            <div className="flex gap-2">
              <label className="text-sm text-gray-600 hover:text-gray-800 cursor-pointer">
                <input
                  type="file"
                  onChange={handleFileUpload}
                  className="hidden"
                  accept=".txt"
                />
                <Upload className="w-4 h-4 inline mr-1" />
                上传文件
              </label>
            </div>
          </div>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder={`请输入要${mode === 'encode' ? '编码' : '解码'}的文本...`}
            className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
          />
          <div className="mt-2 text-sm text-gray-500">
            字符数: {inputText.length}
          </div>
        </div>
        
        {/* 输出区 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="font-medium">
              {mode === 'encode' ? '编码' : '解码'}结果
            </label>
            <div className="flex gap-2">
              {outputText && (
                <>
                  <button
                    onClick={copyToClipboard}
                    className="text-sm text-gray-600 hover:text-gray-800"
                  >
                    {copied ? (
                      <><Check className="w-4 h-4 inline mr-1 text-green-500" />已复制</>
                    ) : (
                      <><Copy className="w-4 h-4 inline mr-1" />复制</>
                    )}
                  </button>
                  <button
                    onClick={downloadResult}
                    className="text-sm text-gray-600 hover:text-gray-800"
                  >
                    <Download className="w-4 h-4 inline mr-1" />
                    下载
                  </button>
                </>
              )}
            </div>
          </div>
          <textarea
            value={outputText}
            readOnly
            placeholder={error || '结果将显示在这里...'}
            className={`w-full h-64 px-3 py-2 border rounded-md font-mono text-sm ${
              error 
                ? 'border-red-300 bg-red-50 text-red-600' 
                : 'border-gray-300 bg-gray-50'
            }`}
          />
          <div className="mt-2 text-sm text-gray-500">
            字符数: {outputText.length}
          </div>
        </div>
      </div>
      
      {/* 错误提示 */}
      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}
      
      {/* 示例 */}
      <div className="mt-6">
        <h3 className="font-medium mb-3">快速示例</h3>
        <div className="flex flex-wrap gap-2">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={() => setInputText(example.text)}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              {example.label}
            </button>
          ))}
          <button
            onClick={clearAll}
            className="px-3 py-1 text-sm text-red-600 hover:bg-red-50 rounded-md"
          >
            清空
          </button>
        </div>
      </div>
      
      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <FileText className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>选择编码格式，然后选择编码或解码模式</li>
              <li>输入文本会自动进行转换，结果实时显示</li>
              <li>支持文件上传和结果下载</li>
              <li>点击交换按钮可以快速切换输入输出并反转操作模式</li>
              <li>Base64 编码支持中文和 Unicode 字符</li>
              <li>摩斯密码仅支持英文字母和数字</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
