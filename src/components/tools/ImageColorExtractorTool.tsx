'use client'

import React, { useState, useRef, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Upload, Download, Copy, Palette, Eye, Grid, BarChart3, Pipette, RefreshCw } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface ColorInfo {
  hex: string
  rgb: { r: number; g: number; b: number }
  hsl: { h: number; s: number; l: number }
  percentage: number
  count: number
}

interface ColorPalette {
  dominant: ColorInfo[]
  vibrant: ColorInfo[]
  muted: ColorInfo[]
  all: ColorInfo[]
}

/**
 * 图片颜色提取器工具组件
 * 提供图片主色调提取、调色板生成和颜色分析功能
 */
export default function ImageColorExtractorTool() {
  const [image, setImage] = useState<string | null>(null)
  const [originalFile, setOriginalFile] = useState<File | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [colorPalette, setColorPalette] = useState<ColorPalette | null>(null)
  const [colorCount, setColorCount] = useState([10])
  const [algorithm, setAlgorithm] = useState('kmeans')
  const [quality, setQuality] = useState([5])
  const [activeTab, setActiveTab] = useState('dominant')
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const { toast } = useToast()

  /**
   * RGB转HSL颜色空间转换
   */
  const rgbToHsl = useCallback((r: number, g: number, b: number) => {
    r /= 255
    g /= 255
    b /= 255

    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h = 0
    let s = 0
    const l = (max + min) / 2

    if (max !== min) {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break
        case g: h = (b - r) / d + 2; break
        case b: h = (r - g) / d + 4; break
      }
      h /= 6
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    }
  }, [])

  /**
   * RGB转HEX颜色格式转换
   */
  const rgbToHex = useCallback((r: number, g: number, b: number) => {
    return '#' + [r, g, b].map(x => {
      const hex = x.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }).join('')
  }, [])

  /**
   * 计算颜色亮度
   */
  const getColorBrightness = useCallback((r: number, g: number, b: number) => {
    return (r * 299 + g * 587 + b * 114) / 1000
  }, [])

  /**
   * 计算颜色饱和度
   */
  const getColorSaturation = useCallback((r: number, g: number, b: number) => {
    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    return max === 0 ? 0 : (max - min) / max
  }, [])

  /**
   * K-means聚类算法提取主色调
   */
  const extractColorsKMeans = useCallback((imageData: ImageData, k: number) => {
    const pixels: number[][] = []
    const data = imageData.data

    // 采样像素点（根据质量设置）
    const step = quality[0]
    for (let i = 0; i < data.length; i += 4 * step) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]
      const a = data[i + 3]
      
      // 跳过透明像素
      if (a < 128) continue
      
      pixels.push([r, g, b])
    }

    if (pixels.length === 0) return []

    // 初始化聚类中心
    const centroids: number[][] = []
    for (let i = 0; i < k; i++) {
      const randomIndex = Math.floor(Math.random() * pixels.length)
      centroids.push([...pixels[randomIndex]])
    }

    // K-means迭代
    for (let iter = 0; iter < 20; iter++) {
      const clusters: number[][][] = Array(k).fill(null).map(() => [])
      
      // 分配像素到最近的聚类中心
      pixels.forEach(pixel => {
        let minDistance = Infinity
        let closestCentroid = 0
        
        centroids.forEach((centroid, index) => {
          const distance = Math.sqrt(
            Math.pow(pixel[0] - centroid[0], 2) +
            Math.pow(pixel[1] - centroid[1], 2) +
            Math.pow(pixel[2] - centroid[2], 2)
          )
          
          if (distance < minDistance) {
            minDistance = distance
            closestCentroid = index
          }
        })
        
        clusters[closestCentroid].push(pixel)
      })

      // 更新聚类中心
      let changed = false
      clusters.forEach((cluster, index) => {
        if (cluster.length > 0) {
          const newCentroid = [
            Math.round(cluster.reduce((sum, pixel) => sum + pixel[0], 0) / cluster.length),
            Math.round(cluster.reduce((sum, pixel) => sum + pixel[1], 0) / cluster.length),
            Math.round(cluster.reduce((sum, pixel) => sum + pixel[2], 0) / cluster.length)
          ]
          
          if (newCentroid.some((val, i) => Math.abs(val - centroids[index][i]) > 1)) {
            centroids[index] = newCentroid
            changed = true
          }
        }
      })

      if (!changed) break
    }

    // 计算每个聚类的像素数量
    const results: ColorInfo[] = []
    centroids.forEach((centroid, index) => {
      const clusterSize = pixels.filter(pixel => {
        let minDistance = Infinity
        let closestCentroid = 0
        
        centroids.forEach((c, i) => {
          const distance = Math.sqrt(
            Math.pow(pixel[0] - c[0], 2) +
            Math.pow(pixel[1] - c[1], 2) +
            Math.pow(pixel[2] - c[2], 2)
          )
          
          if (distance < minDistance) {
            minDistance = distance
            closestCentroid = i
          }
        })
        
        return closestCentroid === index
      }).length

      if (clusterSize > 0) {
        const [r, g, b] = centroid
        results.push({
          hex: rgbToHex(r, g, b),
          rgb: { r, g, b },
          hsl: rgbToHsl(r, g, b),
          percentage: (clusterSize / pixels.length) * 100,
          count: clusterSize
        })
      }
    })

    return results.sort((a, b) => b.percentage - a.percentage)
  }, [quality, rgbToHex, rgbToHsl])

  /**
   * 颜色直方图算法提取颜色
   */
  const extractColorsHistogram = useCallback((imageData: ImageData, k: number) => {
    const colorMap = new Map<string, number>()
    const data = imageData.data

    // 统计颜色频率（降低精度以减少颜色数量）
    const step = quality[0]
    for (let i = 0; i < data.length; i += 4 * step) {
      const r = Math.floor(data[i] / 8) * 8
      const g = Math.floor(data[i + 1] / 8) * 8
      const b = Math.floor(data[i + 2] / 8) * 8
      const a = data[i + 3]
      
      if (a < 128) continue
      
      const key = `${r},${g},${b}`
      colorMap.set(key, (colorMap.get(key) || 0) + 1)
    }

    // 排序并取前k个颜色
    const sortedColors = Array.from(colorMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, k)

    const totalPixels = Array.from(colorMap.values()).reduce((sum, count) => sum + count, 0)

    return sortedColors.map(([colorKey, count]) => {
      const [r, g, b] = colorKey.split(',').map(Number)
      return {
        hex: rgbToHex(r, g, b),
        rgb: { r, g, b },
        hsl: rgbToHsl(r, g, b),
        percentage: (count / totalPixels) * 100,
        count
      }
    })
  }, [quality, rgbToHex, rgbToHsl])

  /**
   * 分类颜色为不同类型
   */
  const categorizeColors = useCallback((colors: ColorInfo[]) => {
    const vibrant: ColorInfo[] = []
    const muted: ColorInfo[] = []

    colors.forEach(color => {
      const { r, g, b } = color.rgb
      const brightness = getColorBrightness(r, g, b)
      const saturation = getColorSaturation(r, g, b)

      if (saturation > 0.4 && brightness > 50 && brightness < 200) {
        vibrant.push(color)
      } else {
        muted.push(color)
      }
    })

    return { vibrant, muted }
  }, [getColorBrightness, getColorSaturation])

  /**
   * 处理图片并提取颜色
   */
  const processImage = useCallback(async () => {
    if (!image || !canvasRef.current) return

    setIsProcessing(true)

    try {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        img.src = image
      })

      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')!
      
      // 调整画布大小以优化性能
      const maxSize = 300
      const scale = Math.min(maxSize / img.width, maxSize / img.height)
      canvas.width = img.width * scale
      canvas.height = img.height * scale

      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

      // 根据算法提取颜色
      let dominantColors: ColorInfo[]
      if (algorithm === 'kmeans') {
        dominantColors = extractColorsKMeans(imageData, colorCount[0])
      } else {
        dominantColors = extractColorsHistogram(imageData, colorCount[0])
      }

      // 分类颜色
      const { vibrant, muted } = categorizeColors(dominantColors)

      setColorPalette({
        dominant: dominantColors,
        vibrant,
        muted,
        all: dominantColors
      })

      toast({
        title: '颜色提取完成',
        description: `成功提取了 ${dominantColors.length} 种主要颜色`,
      })
    } catch (error) {
      console.error('颜色提取失败:', error)
      toast({
        title: '提取失败',
        description: '图片颜色提取过程中出现错误',
        variant: 'destructive',
      })
    } finally {
      setIsProcessing(false)
    }
  }, [image, colorCount, algorithm, quality, extractColorsKMeans, extractColorsHistogram, categorizeColors, toast])

  /**
   * 处理文件上传
   */
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      toast({
        title: '文件格式错误',
        description: '请选择有效的图片文件',
        variant: 'destructive',
      })
      return
    }

    setOriginalFile(file)
    const reader = new FileReader()
    reader.onload = (e) => {
      setImage(e.target?.result as string)
      setColorPalette(null)
    }
    reader.readAsDataURL(file)
  }, [toast])

  /**
   * 复制颜色值到剪贴板
   */
  const copyColorValue = useCallback(async (color: ColorInfo, format: 'hex' | 'rgb' | 'hsl') => {
    let value = ''
    switch (format) {
      case 'hex':
        value = color.hex
        break
      case 'rgb':
        value = `rgb(${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b})`
        break
      case 'hsl':
        value = `hsl(${color.hsl.h}, ${color.hsl.s}%, ${color.hsl.l}%)`
        break
    }

    try {
      await navigator.clipboard.writeText(value)
      toast({
        title: '已复制',
        description: `颜色值 ${value} 已复制到剪贴板`,
      })
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法复制到剪贴板',
        variant: 'destructive',
      })
    }
  }, [toast])

  /**
   * 导出调色板
   */
  const exportPalette = useCallback((format: 'css' | 'json' | 'ase') => {
    if (!colorPalette) return

    const colors = colorPalette[activeTab as keyof ColorPalette] as ColorInfo[]
    let content = ''
    let filename = ''
    let mimeType = ''

    switch (format) {
      case 'css':
        content = `:root {\n${colors.map((color, index) => 
          `  --color-${index + 1}: ${color.hex};`
        ).join('\n')}\n}`
        filename = 'color-palette.css'
        mimeType = 'text/css'
        break

      case 'json':
        content = JSON.stringify({
          palette: activeTab,
          colors: colors.map((color, index) => ({
            name: `color-${index + 1}`,
            hex: color.hex,
            rgb: color.rgb,
            hsl: color.hsl,
            percentage: color.percentage
          }))
        }, null, 2)
        filename = 'color-palette.json'
        mimeType = 'application/json'
        break

      case 'ase':
        // 简化的ASE格式（实际ASE格式更复杂）
        content = colors.map(color => 
          `${color.hex}\t${color.rgb.r}\t${color.rgb.g}\t${color.rgb.b}`
        ).join('\n')
        filename = 'color-palette.ase'
        mimeType = 'text/plain'
        break
    }

    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: '导出成功',
      description: `调色板已导出为 ${filename}`,
    })
  }, [colorPalette, activeTab, toast])

  /**
   * 渲染颜色卡片
   */
  const renderColorCard = useCallback((color: ColorInfo, index: number) => (
    <Card key={index} className="overflow-hidden">
      <div 
        className="h-20 w-full cursor-pointer transition-transform hover:scale-105"
        style={{ backgroundColor: color.hex }}
        onClick={() => copyColorValue(color, 'hex')}
      />
      <CardContent className="p-3 space-y-2">
        <div className="flex items-center justify-between">
          <Badge variant="secondary" className="text-xs">
            {color.percentage.toFixed(1)}%
          </Badge>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
              onClick={() => copyColorValue(color, 'hex')}
            >
              <Copy className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span className="text-muted-foreground">HEX:</span>
            <span className="font-mono">{color.hex}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">RGB:</span>
            <span className="font-mono">
              {color.rgb.r}, {color.rgb.g}, {color.rgb.b}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">HSL:</span>
            <span className="font-mono">
              {color.hsl.h}°, {color.hsl.s}%, {color.hsl.l}%
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  ), [copyColorValue])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">图片颜色提取器</h1>
        <p className="text-muted-foreground">
          从图片中提取主色调，生成调色板，支持多种提取算法和导出格式
        </p>
      </div>

      {/* 上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            图片上传
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={() => fileInputRef.current?.click()}
              disabled={isProcessing}
            >
              <Upload className="h-4 w-4 mr-2" />
              选择图片
            </Button>
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
            {originalFile && (
              <span className="text-sm text-muted-foreground">
                {originalFile.name}
              </span>
            )}
          </div>

          {image && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>预览图片</Label>
                <div className="mt-2 border rounded-lg overflow-hidden">
                  <img
                    src={image}
                    alt="预览"
                    className="w-full h-48 object-contain bg-gray-50"
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <Label>提取算法</Label>
                  <Select value={algorithm} onValueChange={setAlgorithm}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="kmeans">K-means聚类</SelectItem>
                      <SelectItem value="histogram">颜色直方图</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>颜色数量: {colorCount[0]}</Label>
                  <Slider
                    value={colorCount}
                    onValueChange={setColorCount}
                    min={3}
                    max={20}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>采样质量: {quality[0]}</Label>
                  <Slider
                    value={quality}
                    onValueChange={setQuality}
                    min={1}
                    max={10}
                    step={1}
                    className="mt-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    值越小质量越高，处理时间越长
                  </p>
                </div>

                <Button
                  onClick={processImage}
                  disabled={isProcessing}
                  className="w-full"
                >
                  {isProcessing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      提取中...
                    </>
                  ) : (
                    <>
                      <Palette className="h-4 w-4 mr-2" />
                      提取颜色
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 隐藏的画布用于图片处理 */}
      <canvas ref={canvasRef} className="hidden" />

      {/* 颜色结果 */}
      {colorPalette && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                颜色调色板
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => exportPalette('css')}
                >
                  <Download className="h-4 w-4 mr-2" />
                  CSS
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => exportPalette('json')}
                >
                  <Download className="h-4 w-4 mr-2" />
                  JSON
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => exportPalette('ase')}
                >
                  <Download className="h-4 w-4 mr-2" />
                  ASE
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="dominant" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  主色调
                </TabsTrigger>
                <TabsTrigger value="vibrant" className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  鲜艳色
                </TabsTrigger>
                <TabsTrigger value="muted" className="flex items-center gap-2">
                  <Pipette className="h-4 w-4" />
                  柔和色
                </TabsTrigger>
                <TabsTrigger value="all" className="flex items-center gap-2">
                  <Grid className="h-4 w-4" />
                  全部
                </TabsTrigger>
              </TabsList>

              <TabsContent value="dominant" className="mt-4">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                  {colorPalette.dominant.map((color, index) => renderColorCard(color, index))}
                </div>
              </TabsContent>

              <TabsContent value="vibrant" className="mt-4">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                  {colorPalette.vibrant.length > 0 ? (
                    colorPalette.vibrant.map((color, index) => renderColorCard(color, index))
                  ) : (
                    <p className="text-muted-foreground col-span-full text-center py-8">
                      未找到鲜艳色彩
                    </p>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="muted" className="mt-4">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                  {colorPalette.muted.length > 0 ? (
                    colorPalette.muted.map((color, index) => renderColorCard(color, index))
                  ) : (
                    <p className="text-muted-foreground col-span-full text-center py-8">
                      未找到柔和色彩
                    </p>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="all" className="mt-4">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                  {colorPalette.all.map((color, index) => renderColorCard(color, index))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-muted-foreground">
          <div>
            <strong>提取算法：</strong>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>K-means聚类：更准确的颜色分组，适合复杂图片</li>
              <li>颜色直方图：更快的处理速度，适合简单图片</li>
            </ul>
          </div>
          <div>
            <strong>颜色分类：</strong>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>主色调：按出现频率排序的主要颜色</li>
              <li>鲜艳色：高饱和度的明亮颜色</li>
              <li>柔和色：低饱和度的温和颜色</li>
            </ul>
          </div>
          <div>
            <strong>导出格式：</strong>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>CSS：CSS变量格式，可直接用于样式表</li>
              <li>JSON：结构化数据，包含完整颜色信息</li>
              <li>ASE：Adobe色板格式，可导入设计软件</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
