'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Copy, ArrowRightLeft, Upload, Download, Trash2, CheckCircle } from 'lucide-react';

export default function YamlJsonConverterTool() {
  // 状态定义
  const [yamlInput, setYamlInput] = useState('');
  const [jsonInput, setJsonInput] = useState('');
  const [yamlOutput, setYamlOutput] = useState('');
  const [jsonOutput, setJsonOutput] = useState('');
  const [jsonIndent, setJsonIndent] = useState('2');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // 示例数据
  const yamlExample = `# 用户配置示例
user:
  name: 张三
  age: 30
  email: <EMAIL>
  addresses:
    - type: home
      street: 北京市朝阳区
      city: 北京
      zipcode: "100000"
    - type: work
      street: 上海市浦东新区
      city: 上海
      zipcode: "200000"
  preferences:
    theme: dark
    language: zh-CN
    notifications: true
settings:
  timeout: 30
  retries: 3
  debug: false`;

  const jsonExample = `{
  "user": {
    "name": "张三",
    "age": 30,
    "email": "<EMAIL>",
    "addresses": [
      {
        "type": "home",
        "street": "北京市朝阳区",
        "city": "北京",
        "zipcode": "100000"
      },
      {
        "type": "work",
        "street": "上海市浦东新区",
        "city": "上海",
        "zipcode": "200000"
      }
    ],
    "preferences": {
      "theme": "dark",
      "language": "zh-CN",
      "notifications": true
    }
  },
  "settings": {
    "timeout": 30,
    "retries": 3,
    "debug": false
  }
}`;

  // 简单的YAML解析器（仅支持基本功能）
  const parseYaml = (yamlStr: string): any => {
    try {
      const lines = yamlStr.split('\n');
      const result: any = {};
      const stack: any[] = [{ obj: result, indent: -1, isArray: false }];
      
      for (let line of lines) {
        // 跳过注释和空行
        if (line.trim().startsWith('#') || line.trim() === '') continue;
        
        const indent = line.length - line.trimStart().length;
        const trimmedLine = line.trim();
        
        // 清理栈，保持正确的缩进层级
        while (stack.length > 1 && stack[stack.length - 1].indent >= indent) {
          stack.pop();
        }
        
        const current = stack[stack.length - 1];
        
        // 处理数组项
        if (trimmedLine.startsWith('- ')) {
          const value = trimmedLine.substring(2);
          if (!Array.isArray(current.obj)) {
            // 如果当前不是数组，需要创建数组
            const keys = Object.keys(current.obj);
            const lastKey = keys[keys.length - 1];
            current.obj[lastKey] = [];
            current.obj = current.obj[lastKey];
          }
          
          if (value.includes(':')) {
            // 数组中的对象
            const obj: any = {};
            const [key, val] = value.split(':').map(s => s.trim());
            obj[key.replace(/['"]/g, '')] = parseValue(val);
            current.obj.push(obj);
            stack.push({ obj: obj, indent: indent, isArray: false });
          } else {
            // 简单数组项
            current.obj.push(parseValue(value));
          }
        } else if (trimmedLine.includes(':')) {
          const [key, ...valueParts] = trimmedLine.split(':');
          const value = valueParts.join(':').trim();
          const cleanKey = key.trim().replace(/['"]/g, '');
          
          if (value === '' || value === '|' || value === '>') {
            // 嵌套对象或多行值
            current.obj[cleanKey] = {};
            stack.push({ obj: current.obj[cleanKey], indent: indent, isArray: false });
          } else {
            current.obj[cleanKey] = parseValue(value);
          }
        }
      }
      
      return result;
    } catch (err) {
      throw new Error('YAML 解析失败：格式不正确');
    }
  };

  // 解析值的类型
  const parseValue = (value: string): any => {
    if (!value || value === '') return null;
    
    const trimmed = value.trim();
    
    // 布尔值
    if (trimmed === 'true') return true;
    if (trimmed === 'false') return false;
    
    // null
    if (trimmed === 'null' || trimmed === '~') return null;
    
    // 数字
    if (/^-?\d+$/.test(trimmed)) return parseInt(trimmed);
    if (/^-?\d+\.\d+$/.test(trimmed)) return parseFloat(trimmed);
    
    // 字符串（去除引号）
    return trimmed.replace(/^["']|["']$/g, '');
  };

  // 简单的JSON转YAML
  const jsonToYaml = (obj: any, indent: number = 0): string => {
    const spaces = '  '.repeat(indent);
    
    if (obj === null || obj === undefined) {
      return 'null';
    }
    
    if (typeof obj === 'boolean' || typeof obj === 'number') {
      return obj.toString();
    }
    
    if (typeof obj === 'string') {
      // 如果字符串包含特殊字符，加引号
      if (obj.includes(':') || obj.includes('\n') || obj.includes('#') || /^\d+$/.test(obj)) {
        return `"${obj}"`;
      }
      return obj;
    }
    
    if (Array.isArray(obj)) {
      if (obj.length === 0) return '[]';
      return obj.map(item => {
        if (typeof item === 'object' && item !== null) {
          const yamlObj = jsonToYaml(item, indent + 1);
          const lines = yamlObj.split('\n');
          return `${spaces}- ${lines[0]}\n${lines.slice(1).map(line => `  ${spaces}${line}`).join('\n')}`;
        } else {
          return `${spaces}- ${jsonToYaml(item, 0)}`;
        }
      }).join('\n');
    }
    
    if (typeof obj === 'object') {
      const entries = Object.entries(obj);
      if (entries.length === 0) return '{}';
      
      return entries.map(([key, value]) => {
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          const yamlValue = jsonToYaml(value, indent + 1);
          return `${spaces}${key}:\n${yamlValue.split('\n').map(line => `  ${spaces}${line}`).join('\n')}`;
        } else if (Array.isArray(value)) {
          if (value.length === 0) {
            return `${spaces}${key}: []`;
          }
          const yamlValue = jsonToYaml(value, indent + 1);
          return `${spaces}${key}:\n${yamlValue}`;
        } else {
          return `${spaces}${key}: ${jsonToYaml(value, 0)}`;
        }
      }).join('\n');
    }
    
    return String(obj);
  };

  // YAML转JSON
  const convertYamlToJson = () => {
    try {
      setError('');
      setSuccess('');
      
      if (!yamlInput.trim()) {
        setError('请输入YAML内容');
        return;
      }
      
      const parsed = parseYaml(yamlInput);
      const json = JSON.stringify(parsed, null, parseInt(jsonIndent));
      setJsonOutput(json);
      setSuccess('YAML 转 JSON 成功！');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'YAML转换失败');
      setJsonOutput('');
    }
  };

  // JSON转YAML
  const convertJsonToYaml = () => {
    try {
      setError('');
      setSuccess('');
      
      if (!jsonInput.trim()) {
        setError('请输入JSON内容');
        return;
      }
      
      const parsed = JSON.parse(jsonInput);
      const yaml = jsonToYaml(parsed);
      setYamlOutput(yaml);
      setSuccess('JSON 转 YAML 成功！');
    } catch (err) {
      setError('JSON格式不正确或转换失败');
      setYamlOutput('');
    }
  };

  // 复制到剪贴板
  const handleCopy = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setSuccess(`${type} 已复制到剪贴板`);
    } catch (err) {
      setError('复制失败');
    }
  };

  // 清空输入输出
  const handleClear = () => {
    setYamlInput('');
    setJsonInput('');
    setYamlOutput('');
    setJsonOutput('');
    setError('');
    setSuccess('');
  };

  // 加载示例
  const loadExample = (type: 'yaml' | 'json') => {
    if (type === 'yaml') {
      setYamlInput(yamlExample);
    } else {
      setJsonInput(jsonExample);
    }
    setError('');
    setSuccess('');
  };

  // 文件上传处理
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, type: 'yaml' | 'json') => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 1024 * 1024) { // 1MB限制
      setError('文件大小不能超过1MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (type === 'yaml') {
        setYamlInput(content);
      } else {
        setJsonInput(content);
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      {/* 工具说明卡片 */}
      <Card>
        <CardHeader>
          <CardTitle>YAML/JSON 转换器</CardTitle>
          <CardDescription>
            在YAML和JSON格式之间相互转换。支持复杂的嵌套结构、数组和各种数据类型。
            适用于配置文件转换、API数据格式转换等场景。
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 状态提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-4">
            <div className="flex items-center text-red-600">
              <span className="text-sm">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {success && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-4">
            <div className="flex items-center text-green-600">
              <CheckCircle className="h-4 w-4 mr-2" />
              <span className="text-sm">{success}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 转换工具 */}
      <Tabs defaultValue="yaml-to-json" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="yaml-to-json">YAML → JSON</TabsTrigger>
          <TabsTrigger value="json-to-yaml">JSON → YAML</TabsTrigger>
        </TabsList>

        {/* YAML转JSON */}
        <TabsContent value="yaml-to-json" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* YAML输入 */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">YAML 输入</CardTitle>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadExample('yaml')}
                    >
                      加载示例
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <label>
                        <Upload className="h-4 w-4 mr-1" />
                        上传文件
                        <input
                          type="file"
                          accept=".yaml,.yml"
                          className="hidden"
                          onChange={(e) => handleFileUpload(e, 'yaml')}
                        />
                      </label>
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="yaml-input">请输入YAML内容</Label>
                  <Textarea
                    id="yaml-input"
                    placeholder="user:&#10;  name: 张三&#10;  age: 30"
                    value={yamlInput}
                    onChange={(e) => setYamlInput(e.target.value)}
                    className="min-h-[300px] font-mono text-sm"
                  />
                </div>
              </CardContent>
            </Card>

            {/* JSON输出 */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">JSON 输出</CardTitle>
                  <div className="flex gap-2">
                    <Select value={jsonIndent} onValueChange={setJsonIndent}>
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">压缩</SelectItem>
                        <SelectItem value="2">2空格</SelectItem>
                        <SelectItem value="4">4空格</SelectItem>
                        <SelectItem value="tab">Tab</SelectItem>
                      </SelectContent>
                    </Select>
                    {jsonOutput && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopy(jsonOutput, 'JSON')}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  value={jsonOutput}
                  readOnly
                  className="min-h-[300px] font-mono text-sm"
                  placeholder="转换结果将显示在这里..."
                />
                <Button onClick={convertYamlToJson} className="w-full">
                  <ArrowRightLeft className="h-4 w-4 mr-2" />
                  转换为 JSON
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* JSON转YAML */}
        <TabsContent value="json-to-yaml" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* JSON输入 */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">JSON 输入</CardTitle>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadExample('json')}
                    >
                      加载示例
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <label>
                        <Upload className="h-4 w-4 mr-1" />
                        上传文件
                        <input
                          type="file"
                          accept=".json"
                          className="hidden"
                          onChange={(e) => handleFileUpload(e, 'json')}
                        />
                      </label>
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="json-input">请输入JSON内容</Label>
                  <Textarea
                    id="json-input"
                    placeholder='{"name": "张三", "age": 30}'
                    value={jsonInput}
                    onChange={(e) => setJsonInput(e.target.value)}
                    className="min-h-[300px] font-mono text-sm"
                  />
                </div>
              </CardContent>
            </Card>

            {/* YAML输出 */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">YAML 输出</CardTitle>
                  {yamlOutput && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCopy(yamlOutput, 'YAML')}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      复制
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  value={yamlOutput}
                  readOnly
                  className="min-h-[300px] font-mono text-sm"
                  placeholder="转换结果将显示在这里..."
                />
                <Button onClick={convertJsonToYaml} className="w-full">
                  <ArrowRightLeft className="h-4 w-4 mr-2" />
                  转换为 YAML
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 操作按钮 */}
      <Card>
        <CardContent className="pt-4">
          <div className="flex gap-2 justify-center">
            <Button variant="outline" onClick={handleClear}>
              <Trash2 className="h-4 w-4 mr-2" />
              清空所有
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <div><strong>支持的YAML特性：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>键值对：<code>key: value</code></li>
            <li>嵌套对象：通过缩进表示层级关系</li>
            <li>数组：<code>- item1</code>、<code>- item2</code></li>
            <li>数据类型：字符串、数字、布尔值、null</li>
            <li>注释：以 <code>#</code> 开头的行</li>
          </ul>
          <div className="mt-4"><strong>注意事项：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>YAML对缩进敏感，请使用空格而不是Tab</li>
            <li>字符串如包含特殊字符建议使用引号</li>
            <li>支持上传.yaml、.yml和.json文件</li>
            <li>文件大小限制为1MB</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
