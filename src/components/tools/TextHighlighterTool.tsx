'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Copy, RefreshCw, Trash2, Download, Upload, Highlighter, Plus, X, Eye, EyeOff } from 'lucide-react';

interface HighlightRule {
  id: string;
  keyword: string;
  color: string;
  backgroundColor: string;
  caseSensitive: boolean;
  wholeWord: boolean;
  regex: boolean;
  enabled: boolean;
}

interface HighlightOptions {
  outputFormat: 'html' | 'markdown' | 'plain';
  showLineNumbers: boolean;
  preserveWhitespace: boolean;
  highlightOverlap: 'first' | 'last' | 'all';
  customCssClass: string;
}

const DEFAULT_COLORS = [
  { name: '黄色', color: '#000000', backgroundColor: '#ffff00' },
  { name: '绿色', color: '#000000', backgroundColor: '#90EE90' },
  { name: '蓝色', color: '#ffffff', backgroundColor: '#4169E1' },
  { name: '红色', color: '#ffffff', backgroundColor: '#FF6347' },
  { name: '紫色', color: '#ffffff', backgroundColor: '#9370DB' },
  { name: '橙色', color: '#000000', backgroundColor: '#FFA500' },
  { name: '粉色', color: '#000000', backgroundColor: '#FFB6C1' },
  { name: '青色', color: '#000000', backgroundColor: '#00FFFF' },
];

export default function TextHighlighterTool() {
  const [input, setInput] = useState('');
  const [rules, setRules] = useState<HighlightRule[]>([]);
  const [options, setOptions] = useState<HighlightOptions>({
    outputFormat: 'html',
    showLineNumbers: false,
    preserveWhitespace: true,
    highlightOverlap: 'first',
    customCssClass: 'highlight'
  });
  const [newKeyword, setNewKeyword] = useState('');
  const [selectedColorIndex, setSelectedColorIndex] = useState(0);
  const [error, setError] = useState('');

  const addRule = useCallback(() => {
    if (!newKeyword.trim()) {
      setError('请输入关键词');
      return;
    }

    const existingRule = rules.find(rule => 
      rule.keyword.toLowerCase() === newKeyword.toLowerCase()
    );
    
    if (existingRule) {
      setError('关键词已存在');
      return;
    }

    const selectedColor = DEFAULT_COLORS[selectedColorIndex];
    const newRule: HighlightRule = {
      id: Date.now().toString(),
      keyword: newKeyword.trim(),
      color: selectedColor.color,
      backgroundColor: selectedColor.backgroundColor,
      caseSensitive: false,
      wholeWord: false,
      regex: false,
      enabled: true
    };

    setRules(prev => [...prev, newRule]);
    setNewKeyword('');
    setSelectedColorIndex((selectedColorIndex + 1) % DEFAULT_COLORS.length);
    setError('');
  }, [newKeyword, selectedColorIndex, rules]);

  const removeRule = useCallback((id: string) => {
    setRules(prev => prev.filter(rule => rule.id !== id));
  }, []);

  const updateRule = useCallback((id: string, updates: Partial<HighlightRule>) => {
    setRules(prev => prev.map(rule => 
      rule.id === id ? { ...rule, ...updates } : rule
    ));
  }, []);

  const toggleRule = useCallback((id: string) => {
    setRules(prev => prev.map(rule => 
      rule.id === id ? { ...rule, enabled: !rule.enabled } : rule
    ));
  }, []);

  const clearRules = useCallback(() => {
    setRules([]);
  }, []);

  const escapeHtml = (text: string): string => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  };

  const escapeRegex = (text: string): string => {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  };

  const createRegexPattern = useCallback((rule: HighlightRule): RegExp | null => {
    try {
      let pattern = rule.regex ? rule.keyword : escapeRegex(rule.keyword);
      
      if (rule.wholeWord && !rule.regex) {
        pattern = `\\b${pattern}\\b`;
      }
      
      const flags = rule.caseSensitive ? 'g' : 'gi';
      return new RegExp(pattern, flags);
    } catch (err) {
      return null;
    }
  }, []);

  const highlightText = useMemo(() => {
    try {
      setError('');
      
      if (!input.trim()) return '';
      
      const enabledRules = rules.filter(rule => rule.enabled);
      if (enabledRules.length === 0) return input;
      
      let result = input;
      const matches: Array<{
        start: number;
        end: number;
        rule: HighlightRule;
        text: string;
      }> = [];
      
      // 收集所有匹配项
      enabledRules.forEach(rule => {
        const regex = createRegexPattern(rule);
        if (!regex) return;
        
        let match;
        while ((match = regex.exec(input)) !== null) {
          matches.push({
            start: match.index,
            end: match.index + match[0].length,
            rule,
            text: match[0]
          });
          
          // 防止无限循环
          if (match[0].length === 0) {
            regex.lastIndex++;
          }
        }
      });
      
      // 按位置排序
      matches.sort((a, b) => a.start - b.start);
      
      // 处理重叠
      const finalMatches: typeof matches = [];
      matches.forEach(match => {
        const hasOverlap = finalMatches.some(existing => 
          (match.start < existing.end && match.end > existing.start)
        );
        
        if (!hasOverlap) {
          finalMatches.push(match);
        } else if (options.highlightOverlap === 'last') {
          // 移除重叠的旧匹配
          const nonOverlapping = finalMatches.filter(existing => 
            !(match.start < existing.end && match.end > existing.start)
          );
          nonOverlapping.push(match);
          finalMatches.splice(0, finalMatches.length, ...nonOverlapping);
        }
        // 'first' 模式下忽略新匹配，'all' 模式下需要更复杂的处理
      });
      
      // 从后往前替换，避免位置偏移
      finalMatches.sort((a, b) => b.start - a.start);
      
      finalMatches.forEach(match => {
        const before = result.substring(0, match.start);
        const after = result.substring(match.end);
        
        let highlighted = '';
        switch (options.outputFormat) {
          case 'html':
            const escapedText = escapeHtml(match.text);
            const style = `color: ${match.rule.color}; background-color: ${match.rule.backgroundColor};`;
            const className = options.customCssClass ? ` class="${options.customCssClass}"` : '';
            highlighted = `<span style="${style}"${className}>${escapedText}</span>`;
            break;
          case 'markdown':
            highlighted = `==${match.text}==`;
            break;
          case 'plain':
            highlighted = `【${match.text}】`;
            break;
        }
        
        result = before + highlighted + after;
      });
      
      // 处理换行和空格
      if (options.outputFormat === 'html') {
        if (options.preserveWhitespace) {
          result = result.replace(/\n/g, '<br>').replace(/  /g, '&nbsp;&nbsp;');
        }
        
        if (options.showLineNumbers) {
          const lines = result.split('<br>');
          result = lines.map((line, index) => 
            `<span class="line-number">${index + 1}.</span> ${line}`
          ).join('<br>');
        }
      }
      
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : '高亮处理失败');
      return input;
    }
  }, [input, rules, options, createRegexPattern]);

  const statistics = useMemo(() => {
    const enabledRules = rules.filter(rule => rule.enabled);
    let totalMatches = 0;
    const ruleStats: Record<string, number> = {};
    
    enabledRules.forEach(rule => {
      const regex = createRegexPattern(rule);
      if (!regex) return;
      
      const matches = input.match(regex);
      const count = matches ? matches.length : 0;
      ruleStats[rule.keyword] = count;
      totalMatches += count;
    });
    
    return { totalMatches, ruleStats };
  }, [input, rules, createRegexPattern]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(highlightText);
    } catch (err) {
      setError('复制失败');
    }
  };

  const handleClear = () => {
    setInput('');
    setError('');
  };

  const handleReset = () => {
    setRules([]);
    setOptions({
      outputFormat: 'html',
      showLineNumbers: false,
      preserveWhitespace: true,
      highlightOverlap: 'first',
      customCssClass: 'highlight'
    });
    setNewKeyword('');
    setSelectedColorIndex(0);
  };

  const updateOption = <K extends keyof HighlightOptions>(key: K, value: HighlightOptions[K]) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 1024 * 1024) {
      setError('文件大小不能超过1MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setInput(content);
    };
    reader.readAsText(file);
  };

  const handleDownload = () => {
    if (!highlightText) return;
    
    const extension = options.outputFormat === 'html' ? 'html' : 
                     options.outputFormat === 'markdown' ? 'md' : 'txt';
    const mimeType = options.outputFormat === 'html' ? 'text/html' : 
                    options.outputFormat === 'markdown' ? 'text/markdown' : 'text/plain';
    
    let content = highlightText;
    if (options.outputFormat === 'html') {
      content = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>高亮文本</title>
  <style>
    body { font-family: monospace; white-space: pre-wrap; }
    .line-number { color: #666; margin-right: 10px; }
    .${options.customCssClass} { padding: 2px 4px; border-radius: 3px; }
  </style>
</head>
<body>
${content}
</body>
</html>`;
    }
    
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `highlighted-text.${extension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const examples = [
    {
      name: '编程代码',
      text: `function calculateSum(a, b) {
  const result = a + b;
  console.log('Result:', result);
  return result;
}

const numbers = [1, 2, 3, 4, 5];
const sum = numbers.reduce((acc, num) => acc + num, 0);`,
      keywords: ['function', 'const', 'console', 'return']
    },
    {
      name: '技术文档',
      text: `React是一个用于构建用户界面的JavaScript库。它采用组件化的开发模式，使用虚拟DOM来提高性能。React支持JSX语法，可以在JavaScript中编写类似HTML的代码。`,
      keywords: ['React', 'JavaScript', '组件', 'DOM', 'JSX']
    },
    {
      name: '日志分析',
      text: `[2024-01-01 10:00:00] INFO: Application started successfully
[2024-01-01 10:01:15] ERROR: Database connection failed
[2024-01-01 10:01:16] WARN: Retrying database connection
[2024-01-01 10:01:20] INFO: Database connection restored`,
      keywords: ['ERROR', 'WARN', 'INFO', 'Database']
    }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>文本高亮器</CardTitle>
          <CardDescription>
            为文本中的关键词添加高亮标记，支持正则表达式、大小写敏感、整词匹配等高级功能。
            可输出HTML、Markdown或纯文本格式。
          </CardDescription>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">高亮规则</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap items-end">
            <div className="flex-1 min-w-[200px]">
              <Label htmlFor="newKeyword">关键词</Label>
              <Input
                id="newKeyword"
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                placeholder="输入要高亮的关键词"
                onKeyPress={(e) => e.key === 'Enter' && addRule()}
              />
            </div>
            <div className="min-w-[120px]">
              <Label htmlFor="colorSelect">颜色</Label>
              <Select value={selectedColorIndex.toString()} onValueChange={(value) => setSelectedColorIndex(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {DEFAULT_COLORS.map((color, index) => (
                    <SelectItem key={index} value={index.toString()}>
                      <div className="flex items-center">
                        <div 
                          className="w-4 h-4 rounded mr-2 border"
                          style={{ backgroundColor: color.backgroundColor }}
                        />
                        {color.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button onClick={addRule}>
              <Plus className="h-4 w-4 mr-1" />
              添加
            </Button>
          </div>

          {error && (
            <div className="text-sm text-red-500">{error}</div>
          )}

          {rules.length > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label>高亮规则 ({rules.length})</Label>
                <Button variant="outline" size="sm" onClick={clearRules}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  清空规则
                </Button>
              </div>
              
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {rules.map((rule) => (
                  <div key={rule.id} className="flex items-center gap-2 p-2 border rounded">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleRule(rule.id)}
                    >
                      {rule.enabled ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    </Button>
                    
                    <Badge 
                      style={{ 
                        color: rule.color, 
                        backgroundColor: rule.backgroundColor 
                      }}
                      className="font-mono"
                    >
                      {rule.keyword}
                    </Badge>
                    
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateRule(rule.id, { caseSensitive: !rule.caseSensitive })}
                        className={rule.caseSensitive ? 'bg-blue-100' : ''}
                      >
                        Aa
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateRule(rule.id, { wholeWord: !rule.wholeWord })}
                        className={rule.wholeWord ? 'bg-blue-100' : ''}
                      >
                        W
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateRule(rule.id, { regex: !rule.regex })}
                        className={rule.regex ? 'bg-blue-100' : ''}
                      >
                        .*
                      </Button>
                    </div>
                    
                    <div className="text-sm text-muted-foreground">
                      {statistics.ruleStats[rule.keyword] || 0} 匹配
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeRule(rule.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">输出选项</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="outputFormat">输出格式</Label>
              <Select value={options.outputFormat} onValueChange={(value: 'html' | 'markdown' | 'plain') => updateOption('outputFormat', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="html">HTML</SelectItem>
                  <SelectItem value="markdown">Markdown</SelectItem>
                  <SelectItem value="plain">纯文本</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="highlightOverlap">重叠处理</Label>
              <Select value={options.highlightOverlap} onValueChange={(value) => updateOption('highlightOverlap', value as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="first">优先第一个</SelectItem>
                  <SelectItem value="last">优先最后一个</SelectItem>
                  <SelectItem value="all">显示所有</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {options.outputFormat === 'html' && (
              <div className="space-y-2">
                <Label htmlFor="customCssClass">CSS类名</Label>
                <Input
                  id="customCssClass"
                  value={options.customCssClass}
                  onChange={(e) => updateOption('customCssClass', e.target.value)}
                  placeholder="highlight"
                />
              </div>
            )}
          </div>

          <div className="flex flex-wrap gap-4">
            {options.outputFormat === 'html' && (
              <>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="showLineNumbers"
                    checked={options.showLineNumbers}
                    onCheckedChange={(checked) => updateOption('showLineNumbers', checked)}
                  />
                  <Label htmlFor="showLineNumbers">显示行号</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="preserveWhitespace"
                    checked={options.preserveWhitespace}
                    onCheckedChange={(checked) => updateOption('preserveWhitespace', checked)}
                  />
                  <Label htmlFor="preserveWhitespace">保留空格</Label>
                </div>
              </>
            )}
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" onClick={handleReset}>
              <RefreshCw className="h-4 w-4 mr-1" />
              重置选项
            </Button>
            <Button variant="outline" asChild>
              <label>
                <Upload className="h-4 w-4 mr-1" />
                上传文件
                <input
                  type="file"
                  accept=".txt,.md,.js,.ts,.jsx,.tsx,.css,.html,.json,.xml,.log"
                  className="hidden"
                  onChange={handleFileUpload}
                />
              </label>
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">输入文本</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="input">请输入要处理的文本</Label>
              <Textarea
                id="input"
                placeholder="在这里输入文本..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                className="min-h-[300px] font-mono text-sm"
              />
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClear}>
                <Trash2 className="h-4 w-4 mr-1" />
                清空
              </Button>
            </div>

            <div className="space-y-2">
              <Label>示例文本：</Label>
              <div className="space-y-2">
                {examples.map((example, index) => (
                  <div key={index} className="p-2 border rounded">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-sm">{example.name}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setInput(example.text);
                          // 自动添加示例关键词
                          const newRules = example.keywords.map((keyword, i) => ({
                            id: `example-${Date.now()}-${i}`,
                            keyword,
                            color: DEFAULT_COLORS[i % DEFAULT_COLORS.length].color,
                            backgroundColor: DEFAULT_COLORS[i % DEFAULT_COLORS.length].backgroundColor,
                            caseSensitive: false,
                            wholeWord: false,
                            regex: false,
                            enabled: true
                          }));
                          setRules(newRules);
                        }}
                      >
                        使用示例
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      关键词: {example.keywords.join(', ')}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">高亮结果</CardTitle>
              <div className="flex gap-2">
                {highlightText && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDownload}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      下载
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopy}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      复制
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {options.outputFormat === 'html' ? (
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label>HTML预览：</Label>
                    <div 
                      className="p-3 bg-white border rounded text-sm max-h-[300px] overflow-auto font-mono"
                      dangerouslySetInnerHTML={{ __html: highlightText }}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>HTML代码：</Label>
                    <Textarea
                      value={highlightText}
                      readOnly
                      className="min-h-[200px] font-mono text-xs"
                      placeholder="高亮结果将显示在这里..."
                    />
                  </div>
                </div>
              ) : (
                <Textarea
                  value={highlightText}
                  readOnly
                  className="min-h-[300px] font-mono text-xs"
                  placeholder="高亮结果将显示在这里..."
                />
              )}
              
              {highlightText && (
                <div className="text-sm text-muted-foreground">
                  <p>格式: {options.outputFormat.toUpperCase()}</p>
                  <p>总匹配数: {statistics.totalMatches}</p>
                  <p>启用规则: {rules.filter(r => r.enabled).length}/{rules.length}</p>
                  <p>字符数: {highlightText.length}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <div><strong>高亮规则选项：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li><strong>Aa</strong>：大小写敏感匹配</li>
            <li><strong>W</strong>：整词匹配（只匹配完整单词）</li>
            <li><strong>.*</strong>：正则表达式模式</li>
            <li><strong>眼睛图标</strong>：启用/禁用规则</li>
          </ul>
          <div className="mt-4"><strong>输出格式说明：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>HTML：带样式的HTML标签，支持自定义CSS类</li>
            <li>Markdown：使用 ==文本== 格式标记高亮</li>
            <li>纯文本：使用 【文本】 格式标记高亮</li>
          </ul>
          <div className="mt-4"><strong>正则表达式示例：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>\d+ ：匹配数字</li>
            <li>[A-Z]\w+ ：匹配大写字母开头的单词</li>
            <li>\b\w{4}\b ：匹配4个字符的单词</li>
            <li>(error|warn|info) ：匹配多个关键词之一</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
