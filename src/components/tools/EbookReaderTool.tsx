'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  FileText, 
  ChevronLeft, 
  ChevronRight, 
  Bookmark, 
  Search,
  Settings,
  Sun,
  Moon,
  Type,
  Maximize2,
  Download,
  AlertCircle,
  BookOpen,
  List,
  Clock,
  Hash
} from 'lucide-react'
import { useTranslations } from '@/hooks/useTranslations'

interface BookContent {
  title: string
  author: string
  chapters: Chapter[]
  totalPages: number
  currentPage: number
  lastRead: Date
}

interface Chapter {
  id: string
  title: string
  content: string
  pageStart: number
  pageEnd: number
}

interface Bookmark {
  id: string
  page: number
  chapter: string
  text: string
  date: Date
}

interface ReadingSettings {
  fontSize: number
  lineHeight: number
  fontFamily: string
  theme: 'light' | 'dark' | 'sepia'
  pageWidth: number
}

interface ReadingProgress {
  bookId: string
  currentPage: number
  totalPages: number
  percentage: number
  readingTime: number
  lastRead: Date
}

export default function EbookReaderTool() {
  const t = useTranslations()
  const [file, setFile] = useState<File | null>(null)
  const [bookContent, setBookContent] = useState<BookContent | null>(null)
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<{page: number, text: string}[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('reader')
  const [showSettings, setShowSettings] = useState(false)
  const [readingProgress, setReadingProgress] = useState<ReadingProgress | null>(null)
  const [readingSettings, setReadingSettings] = useState<ReadingSettings>({
    fontSize: 16,
    lineHeight: 1.6,
    fontFamily: 'serif',
    theme: 'light',
    pageWidth: 600
  })
  const fileInputRef = useRef<HTMLInputElement>(null)
  const readerRef = useRef<HTMLDivElement>(null)

  // 从本地存储加载设置和书签
  useEffect(() => {
    const savedSettings = localStorage.getItem('ebookReaderSettings')
    if (savedSettings) {
      setReadingSettings(JSON.parse(savedSettings))
    }

    const savedBookmarks = localStorage.getItem('ebookReaderBookmarks')
    if (savedBookmarks) {
      setBookmarks(JSON.parse(savedBookmarks))
    }
  }, [])

  // 保存设置到本地存储
  useEffect(() => {
    localStorage.setItem('ebookReaderSettings', JSON.stringify(readingSettings))
  }, [readingSettings])

  // 保存书签到本地存储
  useEffect(() => {
    localStorage.setItem('ebookReaderBookmarks', JSON.stringify(bookmarks))
  }, [bookmarks])

  // 模拟解析电子书内容
  const parseEbook = async (file: File) => {
    setLoading(true)
    setError('')

    try {
      // 模拟解析过程
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 模拟书籍内容
      const mockBook: BookContent = {
        title: file.name.replace(/\.[^/.]+$/, ''),
        author: '未知作者',
        chapters: [
          {
            id: '1',
            title: '第一章：开始',
            content: `这是一个示例电子书的第一章内容。

在这个数字化的时代，电子书已经成为了人们阅读的重要方式之一。它不仅方便携带，还能够提供丰富的阅读体验。

通过这个电子书阅读器，您可以：
- 自定义阅读界面
- 添加书签和笔记
- 搜索内容
- 调整字体和主题
- 记录阅读进度

让我们开始这段阅读之旅吧！`,
            pageStart: 1,
            pageEnd: 3
          },
          {
            id: '2',
            title: '第二章：功能介绍',
            content: `本章将详细介绍电子书阅读器的各项功能。

1. 文件支持
支持多种电子书格式，包括 EPUB、PDF、TXT 等常见格式。

2. 阅读体验
- 流畅的翻页效果
- 自适应排版
- 夜间模式保护眼睛
- 多种字体选择

3. 个性化设置
根据您的阅读习惯，自定义最舒适的阅读环境。`,
            pageStart: 4,
            pageEnd: 6
          },
          {
            id: '3',
            title: '第三章：高级功能',
            content: `除了基础的阅读功能，我们还提供了一些高级特性。

1. 智能书签
自动保存您的阅读位置，下次打开时继续阅读。

2. 全文搜索
快速定位您想要查找的内容。

3. 阅读统计
记录您的阅读时间和进度，帮助您养成良好的阅读习惯。

4. 导出功能
支持将笔记和书签导出，方便您整理和分享。`,
            pageStart: 7,
            pageEnd: 10
          }
        ],
        totalPages: 10,
        currentPage: 1,
        lastRead: new Date()
      }

      setBookContent(mockBook)
      setReadingProgress({
        bookId: file.name,
        currentPage: 1,
        totalPages: 10,
        percentage: 10,
        readingTime: 0,
        lastRead: new Date()
      })
    } catch (err) {
      setError(t('ebook.errors.parseFailed'))
    } finally {
      setLoading(false)
    }
  }

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setFile(file)
      parseEbook(file)
    }
  }

  // 翻页功能
  const goToPage = (page: number) => {
    if (!bookContent) return
    
    const newPage = Math.max(1, Math.min(page, bookContent.totalPages))
    setBookContent({
      ...bookContent,
      currentPage: newPage
    })

    if (readingProgress) {
      const newProgress = {
        ...readingProgress,
        currentPage: newPage,
        percentage: Math.round((newPage / bookContent.totalPages) * 100),
        lastRead: new Date()
      }
      setReadingProgress(newProgress)
    }
  }

  // 添加书签
  const addBookmark = () => {
    if (!bookContent) return

    const currentChapter = bookContent.chapters.find(
      ch => bookContent.currentPage >= ch.pageStart && bookContent.currentPage <= ch.pageEnd
    )

    if (currentChapter) {
      const newBookmark: Bookmark = {
        id: Date.now().toString(),
        page: bookContent.currentPage,
        chapter: currentChapter.title,
        text: currentChapter.content.substring(0, 100) + '...',
        date: new Date()
      }

      setBookmarks([...bookmarks, newBookmark])
    }
  }

  // 删除书签
  const deleteBookmark = (id: string) => {
    setBookmarks(bookmarks.filter(b => b.id !== id))
  }

  // 跳转到书签
  const goToBookmark = (bookmark: Bookmark) => {
    goToPage(bookmark.page)
    setActiveTab('reader')
  }

  // 搜索功能
  const searchInBook = () => {
    if (!bookContent || !searchQuery) return

    const results: {page: number, text: string}[] = []
    
    bookContent.chapters.forEach(chapter => {
      if (chapter.content.toLowerCase().includes(searchQuery.toLowerCase())) {
        // 简化的搜索结果
        results.push({
          page: chapter.pageStart,
          text: chapter.title
        })
      }
    })

    setSearchResults(results)
  }

  // 获取当前章节内容
  const getCurrentChapterContent = () => {
    if (!bookContent) return ''

    const currentChapter = bookContent.chapters.find(
      ch => bookContent.currentPage >= ch.pageStart && bookContent.currentPage <= ch.pageEnd
    )

    return currentChapter?.content || ''
  }

  // 获取主题样式
  const getThemeStyles = () => {
    const themes = {
      light: {
        backgroundColor: '#ffffff',
        color: '#000000',
        borderColor: '#e5e7eb'
      },
      dark: {
        backgroundColor: '#1a1a1a',
        color: '#ffffff',
        borderColor: '#333333'
      },
      sepia: {
        backgroundColor: '#f4ecd8',
        color: '#5c4b37',
        borderColor: '#d4c4a8'
      }
    }

    return themes[readingSettings.theme]
  }

  return (
    <div className="w-full max-w-6xl space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>{t('tools.ebook-reader.title')}</CardTitle>
          <CardDescription>
            {t('tools.ebook-reader.description')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!bookContent ? (
            // 文件上传界面
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p className="text-lg mb-2">{t('ebook.uploadPrompt')}</p>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('ebook.supportedFormats')}
                </p>
                <Input
                  ref={fileInputRef}
                  type="file"
                  accept=".epub,.pdf,.txt,.mobi"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Button onClick={() => fileInputRef.current?.click()}>
                  <Upload className="w-4 h-4 mr-2" />
                  {t('ebook.selectFile')}
                </Button>
              </div>

              {loading && (
                <div className="space-y-2">
                  <p className="text-sm text-center">{t('ebook.parsing')}</p>
                  <Progress value={66} />
                </div>
              )}

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          ) : (
            // 阅读器界面
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <div className="flex justify-between items-center mb-4">
                <TabsList>
                  <TabsTrigger value="reader">
                    <BookOpen className="w-4 h-4 mr-2" />
                    {t('ebook.reader')}
                  </TabsTrigger>
                  <TabsTrigger value="contents">
                    <List className="w-4 h-4 mr-2" />
                    {t('ebook.contents')}
                  </TabsTrigger>
                  <TabsTrigger value="bookmarks">
                    <Bookmark className="w-4 h-4 mr-2" />
                    {t('ebook.bookmarks')}
                  </TabsTrigger>
                  <TabsTrigger value="search">
                    <Search className="w-4 h-4 mr-2" />
                    {t('ebook.search')}
                  </TabsTrigger>
                </TabsList>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSettings(!showSettings)}
                >
                  <Settings className="w-4 h-4" />
                </Button>
              </div>

              {/* 设置面板 */}
              {showSettings && (
                <Card className="mb-4">
                  <CardContent className="pt-6 space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>{t('ebook.fontSize')}</Label>
                        <Slider
                          value={[readingSettings.fontSize]}
                          onValueChange={(value) => 
                            setReadingSettings({...readingSettings, fontSize: value[0]})
                          }
                          min={12}
                          max={24}
                          step={1}
                        />
                        <p className="text-sm text-muted-foreground">
                          {readingSettings.fontSize}px
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label>{t('ebook.lineHeight')}</Label>
                        <Slider
                          value={[readingSettings.lineHeight]}
                          onValueChange={(value) => 
                            setReadingSettings({...readingSettings, lineHeight: value[0]})
                          }
                          min={1.2}
                          max={2}
                          step={0.1}
                        />
                        <p className="text-sm text-muted-foreground">
                          {readingSettings.lineHeight}
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label>{t('ebook.fontFamily')}</Label>
                        <Select
                          value={readingSettings.fontFamily}
                          onValueChange={(value) => 
                            setReadingSettings({...readingSettings, fontFamily: value})
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="serif">{t('ebook.serif')}</SelectItem>
                            <SelectItem value="sans-serif">{t('ebook.sansSerif')}</SelectItem>
                            <SelectItem value="monospace">{t('ebook.monospace')}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>{t('ebook.theme')}</Label>
                        <Select
                          value={readingSettings.theme}
                          onValueChange={(value: 'light' | 'dark' | 'sepia') => 
                            setReadingSettings({...readingSettings, theme: value})
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="light">
                              <div className="flex items-center">
                                <Sun className="w-4 h-4 mr-2" />
                                {t('ebook.lightTheme')}
                              </div>
                            </SelectItem>
                            <SelectItem value="dark">
                              <div className="flex items-center">
                                <Moon className="w-4 h-4 mr-2" />
                                {t('ebook.darkTheme')}
                              </div>
                            </SelectItem>
                            <SelectItem value="sepia">
                              <div className="flex items-center">
                                <Type className="w-4 h-4 mr-2" />
                                {t('ebook.sepiaTheme')}
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 阅读器内容 */}
              <TabsContent value="reader" className="space-y-4">
                <Card>
                  <CardContent className="pt-6">
                    {/* 书籍信息 */}
                    <div className="flex justify-between items-center mb-4">
                      <div>
                        <h3 className="text-lg font-semibold">{bookContent.title}</h3>
                        <p className="text-sm text-muted-foreground">{bookContent.author}</p>
                      </div>
                      <Button variant="outline" size="sm" onClick={addBookmark}>
                        <Bookmark className="w-4 h-4 mr-2" />
                        {t('ebook.addBookmark')}
                      </Button>
                    </div>

                    {/* 阅读区域 */}
                    <div
                      ref={readerRef}
                      className="min-h-[500px] p-8 rounded-lg transition-all duration-200"
                      style={{
                        ...getThemeStyles(),
                        fontSize: `${readingSettings.fontSize}px`,
                        lineHeight: readingSettings.lineHeight,
                        fontFamily: readingSettings.fontFamily,
                        maxWidth: `${readingSettings.pageWidth}px`,
                        margin: '0 auto'
                      }}
                    >
                      <div className="whitespace-pre-wrap">
                        {getCurrentChapterContent()}
                      </div>
                    </div>

                    {/* 翻页控制 */}
                    <div className="flex items-center justify-between mt-6">
                      <Button
                        variant="outline"
                        onClick={() => goToPage(bookContent.currentPage - 1)}
                        disabled={bookContent.currentPage === 1}
                      >
                        <ChevronLeft className="w-4 h-4 mr-2" />
                        {t('ebook.previous')}
                      </Button>

                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">
                          {t('ebook.page')} {bookContent.currentPage} / {bookContent.totalPages}
                        </p>
                        {readingProgress && (
                          <Progress value={readingProgress.percentage} className="w-32 mt-2" />
                        )}
                      </div>

                      <Button
                        variant="outline"
                        onClick={() => goToPage(bookContent.currentPage + 1)}
                        disabled={bookContent.currentPage === bookContent.totalPages}
                      >
                        {t('ebook.next')}
                        <ChevronRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 目录 */}
              <TabsContent value="contents" className="space-y-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-2">
                      {bookContent.chapters.map((chapter) => (
                        <Button
                          key={chapter.id}
                          variant="ghost"
                          className="w-full justify-start"
                          onClick={() => {
                            goToPage(chapter.pageStart)
                            setActiveTab('reader')
                          }}
                        >
                          <Hash className="w-4 h-4 mr-2" />
                          <span className="flex-1 text-left">{chapter.title}</span>
                          <span className="text-sm text-muted-foreground">
                            {t('ebook.page')} {chapter.pageStart}
                          </span>
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 书签 */}
              <TabsContent value="bookmarks" className="space-y-4">
                <Card>
                  <CardContent className="pt-6">
                    {bookmarks.length === 0 ? (
                      <p className="text-center text-muted-foreground py-8">
                        {t('ebook.noBookmarks')}
                      </p>
                    ) : (
                      <div className="space-y-2">
                        {bookmarks.map((bookmark) => (
                          <div
                            key={bookmark.id}
                            className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50"
                          >
                            <div 
                              className="flex-1 cursor-pointer"
                              onClick={() => goToBookmark(bookmark)}
                            >
                              <p className="font-medium">{bookmark.chapter}</p>
                              <p className="text-sm text-muted-foreground">
                                {t('ebook.page')} {bookmark.page} • {new Date(bookmark.date).toLocaleDateString()}
                              </p>
                              <p className="text-sm mt-1">{bookmark.text}</p>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteBookmark(bookmark.id)}
                            >
                              {t('common.delete')}
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 搜索 */}
              <TabsContent value="search" className="space-y-4">
                <Card>
                  <CardContent className="pt-6 space-y-4">
                    <div className="flex gap-2">
                      <Input
                        placeholder={t('ebook.searchPlaceholder')}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && searchInBook()}
                      />
                      <Button onClick={searchInBook}>
                        <Search className="w-4 h-4" />
                      </Button>
                    </div>

                    {searchResults.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm text-muted-foreground">
                          {t('ebook.searchResults')} ({searchResults.length})
                        </p>
                        {searchResults.map((result, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            className="w-full justify-start"
                            onClick={() => {
                              goToPage(result.page)
                              setActiveTab('reader')
                            }}
                          >
                            <FileText className="w-4 h-4 mr-2" />
                            <span className="flex-1 text-left">{result.text}</span>
                            <span className="text-sm text-muted-foreground">
                              {t('ebook.page')} {result.page}
                            </span>
                          </Button>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}

          {/* 阅读统计 */}
          {readingProgress && (
            <Card>
              <CardContent className="pt-6">
                <h4 className="font-medium mb-4">{t('ebook.readingStats')}</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <Clock className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-2xl font-bold">{readingProgress.readingTime}</p>
                    <p className="text-sm text-muted-foreground">{t('ebook.minutes')}</p>
                  </div>
                  <div>
                    <BookOpen className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-2xl font-bold">{readingProgress.percentage}%</p>
                    <p className="text-sm text-muted-foreground">{t('ebook.progress')}</p>
                  </div>
                  <div>
                    <FileText className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-2xl font-bold">{readingProgress.currentPage}</p>
                    <p className="text-sm text-muted-foreground">{t('ebook.pagesRead')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
