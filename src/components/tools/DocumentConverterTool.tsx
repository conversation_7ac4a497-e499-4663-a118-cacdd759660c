'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { FileText, Upload, Download, AlertCircle, FileIcon, Loader2 } from 'lucide-react'
import { useTranslations } from '@/hooks/useTranslations'
import { useParams } from 'next/navigation'

interface ConversionFormat {
  from: string
  to: string
  name: {
    zh: string
    en: string
    ja: string
  }
}

const conversionFormats: ConversionFormat[] = [
  {
    from: 'txt',
    to: 'pdf',
    name: {
      zh: 'TXT 转 PDF',
      en: 'TXT to PDF',
      ja: 'TXT → PDF'
    }
  },
  {
    from: 'txt',
    to: 'html',
    name: {
      zh: 'TXT 转 HTML',
      en: 'TXT to HTML',
      ja: 'TXT → HTML'
    }
  },
  {
    from: 'markdown',
    to: 'html',
    name: {
      zh: 'Markdown 转 HTML',
      en: 'Markdown to HTML',
      ja: 'Markdown → HTML'
    }
  },
  {
    from: 'markdown',
    to: 'pdf',
    name: {
      zh: 'Markdown 转 PDF',
      en: 'Markdown to PDF',
      ja: 'Markdown → PDF'
    }
  },
  {
    from: 'html',
    to: 'txt',
    name: {
      zh: 'HTML 转 TXT',
      en: 'HTML to TXT',
      ja: 'HTML → TXT'
    }
  },
  {
    from: 'html',
    to: 'markdown',
    name: {
      zh: 'HTML 转 Markdown',
      en: 'HTML to Markdown',
      ja: 'HTML → Markdown'
    }
  },
  {
    from: 'csv',
    to: 'json',
    name: {
      zh: 'CSV 转 JSON',
      en: 'CSV to JSON',
      ja: 'CSV → JSON'
    }
  },
  {
    from: 'json',
    to: 'csv',
    name: {
      zh: 'JSON 转 CSV',
      en: 'JSON to CSV',
      ja: 'JSON → CSV'
    }
  },
  {
    from: 'xml',
    to: 'json',
    name: {
      zh: 'XML 转 JSON',
      en: 'XML to JSON',
      ja: 'XML → JSON'
    }
  },
  {
    from: 'json',
    to: 'xml',
    name: {
      zh: 'JSON 转 XML',
      en: 'JSON to XML',
      ja: 'JSON → XML'
    }
  }
]

export default function DocumentConverterTool() {
  const t = useTranslations()
  const params = useParams()
  const locale = (params?.locale as 'zh' | 'en' | 'ja') || 'zh'
  const { toast } = useToast()
  const [selectedFormat, setSelectedFormat] = useState<string>('')
  const [inputFile, setInputFile] = useState<File | null>(null)
  const [inputContent, setInputContent] = useState<string>('')
  const [outputContent, setOutputContent] = useState<string>('')
  const [isConverting, setIsConverting] = useState(false)
  const [dragActive, setDragActive] = useState(false)

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleFileSelect = useCallback(async (file: File) => {
    setInputFile(file)
    
    try {
      const content = await file.text()
      setInputContent(content)
    } catch (error) {
      toast({
        title: locale === 'zh' ? '读取文件失败' : locale === 'ja' ? 'ファイル読み取りエラー' : 'Failed to read file',
        description: locale === 'zh' ? '请确保文件格式正确' : locale === 'ja' ? 'ファイル形式を確认してください' : 'Please ensure the file format is correct',
        variant: 'destructive'
      })
    }
  }, [locale, toast])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0])
    }
  }, [handleFileSelect])

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0])
    }
  }

  const convertMarkdownToHtml = (markdown: string): string => {
    let html = markdown
    
    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')
    
    // Bold
    html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
    
    // Italic
    html = html.replace(/\*(.+?)\*/g, '<em>$1</em>')
    
    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    
    // Line breaks
    html = html.replace(/\n/g, '<br>\n')
    
    return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Converted Document</title>
</head>
<body>
${html}
</body>
</html>`
  }

  const convertHtmlToMarkdown = (html: string): string => {
    let markdown = html
    
    // Remove HTML tags
    markdown = markdown.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    markdown = markdown.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
    
    // Headers
    markdown = markdown.replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n')
    markdown = markdown.replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n')
    markdown = markdown.replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n')
    
    // Bold and Italic
    markdown = markdown.replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
    markdown = markdown.replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
    markdown = markdown.replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
    markdown = markdown.replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')
    
    // Links
    markdown = markdown.replace(/<a[^>]+href="([^"]+)"[^>]*>([^<]+)<\/a>/gi, '[$2]($1)')
    
    // Line breaks and paragraphs
    markdown = markdown.replace(/<br[^>]*>/gi, '\n')
    markdown = markdown.replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
    
    // Remove remaining tags
    markdown = markdown.replace(/<[^>]+>/g, '')
    
    // Clean up extra whitespace
    markdown = markdown.replace(/\n{3,}/g, '\n\n')
    
    return markdown.trim()
  }

  const convertCsvToJson = (csv: string): string => {
    const lines = csv.trim().split('\n')
    if (lines.length === 0) return '[]'
    
    const headers = lines[0].split(',').map(h => h.trim())
    const result = []
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim())
      const obj: any = {}
      
      headers.forEach((header, index) => {
        obj[header] = values[index] || ''
      })
      
      result.push(obj)
    }
    
    return JSON.stringify(result, null, 2)
  }

  const convertJsonToCsv = (json: string): string => {
    try {
      const data = JSON.parse(json)
      if (!Array.isArray(data) || data.length === 0) return ''
      
      const headers = Object.keys(data[0])
      const csvHeaders = headers.join(',')
      
      const csvRows = data.map(row => {
        return headers.map(header => {
          const value = row[header]
          return typeof value === 'string' && value.includes(',') 
            ? `"${value}"` 
            : value
        }).join(',')
      })
      
      return [csvHeaders, ...csvRows].join('\n')
    } catch (error) {
      throw new Error('Invalid JSON format')
    }
  }

  const convertXmlToJson = (xml: string): string => {
    // Simple XML to JSON conversion (basic implementation)
    const parseXmlNode = (node: string): any => {
      const tagMatch = node.match(/<(\w+)([^>]*)>([\s\S]*?)<\/\1>/)
      if (!tagMatch) return node.trim()
      
      const [, tagName, attributes, content] = tagMatch
      const result: any = {}
      
      // Parse attributes
      const attrRegex = /(\w+)="([^"]*)"/g
      let attrMatch
      while ((attrMatch = attrRegex.exec(attributes))) {
        result[`@${attrMatch[1]}`] = attrMatch[2]
      }
      
      // Parse child nodes
      const childRegex = /<(\w+)([^>]*)>[\s\S]*?<\/\1>/g
      const children = content.match(childRegex)
      
      if (children) {
        children.forEach(child => {
          const childResult = parseXmlNode(child)
          const childTagMatch = child.match(/<(\w+)/)
          if (childTagMatch) {
            const childTag = childTagMatch[1]
            if (result[childTag]) {
              if (!Array.isArray(result[childTag])) {
                result[childTag] = [result[childTag]]
              }
              result[childTag].push(childResult)
            } else {
              result[childTag] = childResult
            }
          }
        })
      } else {
        const textContent = content.trim()
        if (textContent) {
          result['#text'] = textContent
        }
      }
      
      return result
    }
    
    try {
      const result = parseXmlNode(xml.trim())
      return JSON.stringify(result, null, 2)
    } catch (error) {
      throw new Error('Invalid XML format')
    }
  }

  const convertJsonToXml = (json: string, rootName: string = 'root'): string => {
    const buildXml = (obj: any, tagName: string): string => {
      if (typeof obj !== 'object' || obj === null) {
        return `<${tagName}>${obj}</${tagName}>`
      }
      
      let xml = `<${tagName}>`
      
      for (const key in obj) {
        if (key.startsWith('@')) {
          // Skip attributes in this simple implementation
          continue
        } else if (key === '#text') {
          xml = `<${tagName}>${obj[key]}</${tagName}>`
          break
        } else if (Array.isArray(obj[key])) {
          obj[key].forEach((item: any) => {
            xml += '\n  ' + buildXml(item, key)
          })
        } else {
          xml += '\n  ' + buildXml(obj[key], key)
        }
      }
      
      if (!xml.endsWith(`</${tagName}>`)) {
        xml += `\n</${tagName}>`
      }
      
      return xml
    }
    
    try {
      const data = JSON.parse(json)
      return '<?xml version="1.0" encoding="UTF-8"?>\n' + buildXml(data, rootName)
    } catch (error) {
      throw new Error('Invalid JSON format')
    }
  }

  const convertTxtToPdf = (text: string): string => {
    // Note: This is a simplified version. In a real application,
    // you would use a library like jsPDF or similar
    return `%PDF-1.4
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /Resources << /Font << /F1 4 0 R >> >> /MediaBox [0 0 612 792] /Contents 5 0 R >>
endobj
4 0 obj
<< /Type /Font /Subtype /Type1 /BaseFont /Helvetica >>
endobj
5 0 obj
<< /Length ${text.length + 50} >>
stream
BT
/F1 12 Tf
50 750 Td
(${text.replace(/\n/g, ') Tj T* (')} ) Tj
ET
endstream
endobj
xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000262 00000 n
0000000341 00000 n
trailer
<< /Size 6 /Root 1 0 R >>
startxref
${text.length + 450}
%%EOF`
  }

  const convertHtmlToTxt = (html: string): string => {
    // Remove all HTML tags
    let text = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
    text = text.replace(/<[^>]+>/g, '')
    
    // Decode HTML entities
    text = text.replace(/&nbsp;/g, ' ')
    text = text.replace(/&amp;/g, '&')
    text = text.replace(/&lt;/g, '<')
    text = text.replace(/&gt;/g, '>')
    text = text.replace(/&quot;/g, '"')
    text = text.replace(/&#39;/g, "'")
    
    // Clean up whitespace
    text = text.replace(/\s+/g, ' ')
    text = text.trim()
    
    return text
  }

  const convertTxtToHtml = (text: string): string => {
    // Escape HTML special characters
    let html = text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
    
    // Convert line breaks to <br>
    html = html.replace(/\n/g, '<br>\n')
    
    return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Converted Document</title>
</head>
<body>
<p>${html}</p>
</body>
</html>`
  }

  const handleConvert = async () => {
    if (!selectedFormat || !inputContent) {
      toast({
        title: locale === 'zh' ? '请选择转换格式并上传文件' : locale === 'ja' ? '変換形式を選択してファイルをアップロードしてください' : 'Please select format and upload file',
        variant: 'destructive'
      })
      return
    }

    setIsConverting(true)
    
    try {
      const format = conversionFormats.find(f => `${f.from}-${f.to}` === selectedFormat)
      if (!format) throw new Error('Invalid format')

      let output = ''
      
      switch (selectedFormat) {
        case 'txt-pdf':
          output = convertTxtToPdf(inputContent)
          break
        case 'txt-html':
          output = convertTxtToHtml(inputContent)
          break
        case 'markdown-html':
          output = convertMarkdownToHtml(inputContent)
          break
        case 'markdown-pdf':
          // First convert to HTML, then to PDF (simplified)
          const htmlContent = convertMarkdownToHtml(inputContent)
          output = convertTxtToPdf(htmlContent.replace(/<[^>]+>/g, ''))
          break
        case 'html-txt':
          output = convertHtmlToTxt(inputContent)
          break
        case 'html-markdown':
          output = convertHtmlToMarkdown(inputContent)
          break
        case 'csv-json':
          output = convertCsvToJson(inputContent)
          break
        case 'json-csv':
          output = convertJsonToCsv(inputContent)
          break
        case 'xml-json':
          output = convertXmlToJson(inputContent)
          break
        case 'json-xml':
          output = convertJsonToXml(inputContent)
          break
        default:
          throw new Error('Unsupported conversion')
      }
      
      setOutputContent(output)
      
      toast({
        title: locale === 'zh' ? '转换成功！' : locale === 'ja' ? '変換成功！' : 'Conversion successful!',
        description: locale === 'zh' ? '文档已成功转换' : locale === 'ja' ? 'ドキュメントが正常に変換されました' : 'Document converted successfully'
      })
    } catch (error) {
      toast({
        title: locale === 'zh' ? '转换失败' : locale === 'ja' ? '変換エラー' : 'Conversion failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive'
      })
    } finally {
      setIsConverting(false)
    }
  }

  const handleDownload = () => {
    if (!outputContent || !selectedFormat) return
    
    const format = conversionFormats.find(f => `${f.from}-${f.to}` === selectedFormat)
    if (!format) return
    
    const blob = new Blob([outputContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `converted.${format.to}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleClear = () => {
    setSelectedFormat('')
    setInputFile(null)
    setInputContent('')
    setOutputContent('')
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {locale === 'zh' ? '文档转换器' : locale === 'ja' ? 'ドキュメントコンバーター' : 'Document Converter'}
        </CardTitle>
        <CardDescription>
          {locale === 'zh' 
            ? '在不同文档格式之间进行转换' 
            : locale === 'ja' 
            ? '異なるドキュメント形式間で変換' 
            : 'Convert between different document formats'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Format Selection */}
        <div className="space-y-2">
          <Label>{locale === 'zh' ? '选择转换格式' : locale === 'ja' ? '変換形式を選択' : 'Select Conversion Format'}</Label>
          <Select value={selectedFormat} onValueChange={setSelectedFormat}>
            <SelectTrigger>
              <SelectValue placeholder={locale === 'zh' ? '选择格式...' : locale === 'ja' ? '形式を選択...' : 'Select format...'} />
            </SelectTrigger>
            <SelectContent>
              {conversionFormats.map((format) => (
                <SelectItem key={`${format.from}-${format.to}`} value={`${format.from}-${format.to}`}>
                  {format.name[locale]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* File Upload */}
        <div className="space-y-2">
          <Label>{locale === 'zh' ? '上传文件' : locale === 'ja' ? 'ファイルをアップロード' : 'Upload File'}</Label>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive ? 'border-primary bg-primary/5' : 'border-gray-300 dark:border-gray-700'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              type="file"
              id="file-upload"
              className="hidden"
              onChange={handleFileInputChange}
              accept=".txt,.html,.md,.csv,.json,.xml"
            />
            <label htmlFor="file-upload" className="cursor-pointer">
              <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {locale === 'zh' 
                  ? '拖拽文件到此处或点击上传' 
                  : locale === 'ja' 
                  ? 'ファイルをドラッグ＆ドロップまたはクリックしてアップロード' 
                  : 'Drag and drop file here or click to upload'}
              </p>
              {inputFile && (
                <p className="mt-2 text-sm font-medium flex items-center justify-center gap-2">
                  <FileIcon className="h-4 w-4" />
                  {inputFile.name}
                </p>
              )}
            </label>
          </div>
        </div>

        {/* Convert Button */}
        <div className="flex gap-2">
          <Button 
            onClick={handleConvert} 
            disabled={!selectedFormat || !inputContent || isConverting}
            className="flex-1"
          >
            {isConverting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {locale === 'zh' ? '转换' : locale === 'ja' ? '変換' : 'Convert'}
          </Button>
          <Button onClick={handleClear} variant="outline">
            {locale === 'zh' ? '清除' : locale === 'ja' ? 'クリア' : 'Clear'}
          </Button>
        </div>

        {/* Output */}
        {outputContent && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>{locale === 'zh' ? '转换结果' : locale === 'ja' ? '変換結果' : 'Conversion Result'}</Label>
              <Button onClick={handleDownload} size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                {locale === 'zh' ? '下载' : locale === 'ja' ? 'ダウンロード' : 'Download'}
              </Button>
            </div>
            <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900 max-h-96 overflow-auto">
              <pre className="text-sm whitespace-pre-wrap break-words font-mono">
                {outputContent.substring(0, 1000)}
                {outputContent.length > 1000 && '...'}
              </pre>
            </div>
          </div>
        )}

        {/* Tips */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {locale === 'zh' 
              ? '支持 TXT、HTML、Markdown、CSV、JSON、XML 等格式之间的转换。某些复杂格式可能需要进一步处理。' 
              : locale === 'ja' 
              ? 'TXT、HTML、Markdown、CSV、JSON、XMLなどの形式間の変換をサポートしています。複雑な形式はさらに処理が必要な場合があります。' 
              : 'Supports conversion between TXT, HTML, Markdown, CSV, JSON, XML and more. Complex formats may require further processing.'}
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )
}
