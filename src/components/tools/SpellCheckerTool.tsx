'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { CheckCircle, AlertCircle, BookOpen, Languages, Search, Plus, X, RotateCcw } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface SpellError {
  word: string
  position: number
  suggestions: string[]
  context: string
}

interface SpellCheckResult {
  errors: SpellError[]
  totalWords: number
  errorCount: number
  accuracy: number
}

/**
 * 拼写检查器工具组件
 * 提供多语言拼写检查功能，支持自定义词典和拼写建议
 */
export default function SpellCheckerTool() {
  const [inputText, setInputText] = useState('')
  const [language, setLanguage] = useState<'en' | 'zh' | 'mixed'>('en')
  const [result, setResult] = useState<SpellCheckResult | null>(null)
  const [customWords, setCustomWords] = useState<string[]>([])
  const [newCustomWord, setNewCustomWord] = useState('')
  const [isChecking, setIsChecking] = useState(false)
  const [highlightedText, setHighlightedText] = useState('')
  const { toast } = useToast()

  // 基础英文词典（简化版）
  const englishDictionary = useMemo(() => new Set([
    'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with', 'he',
    'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she', 'or',
    'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what', 'so', 'up', 'out', 'if', 'about',
    'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know',
    'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other', 'than',
    'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think', 'also', 'back', 'after', 'use', 'two',
    'how', 'our', 'work', 'first', 'well', 'way', 'even', 'new', 'want', 'because', 'any', 'these', 'give',
    'day', 'most', 'us', 'is', 'water', 'long', 'find', 'here', 'thing', 'every', 'great', 'where', 'much',
    'before', 'move', 'right', 'boy', 'old', 'too', 'same', 'tell', 'does', 'set', 'three', 'must', 'air',
    'large', 'big', 'such', 'follow', 'act', 'why', 'ask', 'men', 'change', 'went', 'light', 'kind', 'off',
    'need', 'house', 'picture', 'try', 'again', 'animal', 'point', 'mother', 'world', 'near', 'build',
    'self', 'earth', 'father', 'head', 'stand', 'own', 'page', 'should', 'country', 'found', 'answer',
    'school', 'grow', 'study', 'still', 'learn', 'plant', 'cover', 'food', 'sun', 'four', 'between',
    'state', 'keep', 'eye', 'never', 'last', 'let', 'thought', 'city', 'tree', 'cross', 'farm', 'hard',
    'start', 'might', 'story', 'saw', 'far', 'sea', 'draw', 'left', 'late', 'run', 'while', 'press',
    'close', 'night', 'real', 'life', 'few', 'north', 'open', 'seem', 'together', 'next', 'white',
    'children', 'begin', 'got', 'walk', 'example', 'ease', 'paper', 'group', 'always', 'music', 'those',
    'both', 'mark', 'often', 'letter', 'until', 'mile', 'river', 'car', 'feet', 'care', 'second', 'book',
    'carry', 'took', 'science', 'eat', 'room', 'friend', 'began', 'idea', 'fish', 'mountain', 'stop',
    'once', 'base', 'hear', 'horse', 'cut', 'sure', 'watch', 'color', 'face', 'wood', 'main', 'enough',
    'plain', 'girl', 'usual', 'young', 'ready', 'above', 'ever', 'red', 'list', 'though', 'feel', 'talk',
    'bird', 'soon', 'body', 'dog', 'family', 'direct', 'pose', 'leave', 'song', 'measure', 'door',
    'product', 'black', 'short', 'numeral', 'class', 'wind', 'question', 'happen', 'complete', 'ship',
    'area', 'half', 'rock', 'order', 'fire', 'south', 'problem', 'piece', 'told', 'knew', 'pass', 'since',
    'top', 'whole', 'king', 'space', 'heard', 'best', 'hour', 'better', 'during', 'hundred', 'five',
    'remember', 'step', 'early', 'hold', 'west', 'ground', 'interest', 'reach', 'fast', 'verb', 'sing',
    'listen', 'six', 'table', 'travel', 'less', 'morning', 'ten', 'simple', 'several', 'vowel', 'toward',
    'war', 'lay', 'against', 'pattern', 'slow', 'center', 'love', 'person', 'money', 'serve', 'appear',
    'road', 'map', 'rain', 'rule', 'govern', 'pull', 'cold', 'notice', 'voice', 'unit', 'power', 'town',
    'fine', 'certain', 'fly', 'fall', 'lead', 'cry', 'dark', 'machine', 'note', 'wait', 'plan', 'figure',
    'star', 'box', 'noun', 'field', 'rest', 'correct', 'able', 'pound', 'done', 'beauty', 'drive', 'stood',
    'contain', 'front', 'teach', 'week', 'final', 'gave', 'green', 'oh', 'quick', 'develop', 'ocean',
    'warm', 'free', 'minute', 'strong', 'special', 'mind', 'behind', 'clear', 'tail', 'produce', 'fact',
    'street', 'inch', 'multiply', 'nothing', 'course', 'stay', 'wheel', 'full', 'force', 'blue', 'object',
    'decide', 'surface', 'deep', 'moon', 'island', 'foot', 'system', 'busy', 'test', 'record', 'boat',
    'common', 'gold', 'possible', 'plane', 'stead', 'dry', 'wonder', 'laugh', 'thousands', 'ago', 'ran',
    'check', 'game', 'shape', 'equate', 'hot', 'miss', 'brought', 'heat', 'snow', 'tire', 'bring', 'yes'
  ]), [])

  // 中文常用字符检查（简化版）
  const isChineseChar = useCallback((char: string) => {
    return /[\u4e00-\u9fff]/.test(char)
  }, [])

  // 编辑距离算法，用于生成拼写建议
  const levenshteinDistance = useCallback((str1: string, str2: string): number => {
    const matrix = []
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    return matrix[str2.length][str1.length]
  }, [])

  // 生成拼写建议
  const generateSuggestions = useCallback((word: string): string[] => {
    const suggestions: string[] = []
    const wordLower = word.toLowerCase()
    
    // 从词典中找到相似的单词
    const allWords = Array.from(englishDictionary).concat(customWords)
    
    for (const dictWord of allWords) {
      const distance = levenshteinDistance(wordLower, dictWord.toLowerCase())
      if (distance <= 2 && distance > 0) {
        suggestions.push(dictWord)
      }
    }
    
    // 按编辑距离排序
    suggestions.sort((a, b) => 
      levenshteinDistance(wordLower, a.toLowerCase()) - 
      levenshteinDistance(wordLower, b.toLowerCase())
    )
    
    return suggestions.slice(0, 5) // 返回前5个建议
  }, [englishDictionary, customWords, levenshteinDistance])

  // 检查单词是否正确
  const isWordCorrect = useCallback((word: string): boolean => {
    const wordLower = word.toLowerCase()
    
    // 检查是否为数字
    if (/^\d+$/.test(word)) return true
    
    // 检查是否包含中文字符
    if (language === 'zh' || language === 'mixed') {
      if (Array.from(word).some(char => isChineseChar(char))) {
        return true // 简化处理，认为中文都是正确的
      }
    }
    
    // 检查英文词典
    if (englishDictionary.has(wordLower)) return true
    
    // 检查自定义词典
    if (customWords.some(w => w.toLowerCase() === wordLower)) return true
    
    return false
  }, [language, englishDictionary, customWords, isChineseChar])

  // 执行拼写检查
  const performSpellCheck = useCallback(async () => {
    if (!inputText.trim()) {
      toast({
        title: '请输入文本',
        description: '请先输入要检查的文本内容',
        variant: 'destructive'
      })
      return
    }

    setIsChecking(true)
    
    try {
      // 分词处理
      const words = inputText.match(/\b\w+\b/g) || []
      const errors: SpellError[] = []
      
      for (let i = 0; i < words.length; i++) {
        const word = words[i]
        if (!isWordCorrect(word)) {
          const position = inputText.indexOf(word)
          const contextStart = Math.max(0, position - 20)
          const contextEnd = Math.min(inputText.length, position + word.length + 20)
          const context = inputText.slice(contextStart, contextEnd)
          
          errors.push({
            word,
            position,
            suggestions: generateSuggestions(word),
            context
          })
        }
      }
      
      const totalWords = words.length
      const errorCount = errors.length
      const accuracy = totalWords > 0 ? ((totalWords - errorCount) / totalWords) * 100 : 100
      
      setResult({
        errors,
        totalWords,
        errorCount,
        accuracy
      })
      
      // 生成高亮文本
      let highlighted = inputText
      errors.forEach(error => {
        const regex = new RegExp(`\\b${error.word}\\b`, 'gi')
        highlighted = highlighted.replace(regex, `<mark class="bg-red-200 text-red-800">${error.word}</mark>`)
      })
      setHighlightedText(highlighted)
      
      toast({
        title: '拼写检查完成',
        description: `发现 ${errorCount} 个拼写错误，准确率 ${accuracy.toFixed(1)}%`
      })
    } catch (error) {
      console.error('拼写检查失败:', error)
      toast({
        title: '检查失败',
        description: '拼写检查过程中出现错误，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsChecking(false)
    }
  }, [inputText, isWordCorrect, generateSuggestions, toast])

  // 添加自定义单词
  const addCustomWord = useCallback(() => {
    if (!newCustomWord.trim()) return
    
    const word = newCustomWord.trim().toLowerCase()
    if (!customWords.includes(word)) {
      setCustomWords(prev => [...prev, word])
      setNewCustomWord('')
      toast({
        title: '添加成功',
        description: `已将 &quot;${word}&quot; 添加到自定义词典`
      })
    } else {
      toast({
        title: '单词已存在',
        description: '该单词已在自定义词典中',
        variant: 'destructive'
      })
    }
  }, [newCustomWord, customWords, toast])

  // 删除自定义单词
  const removeCustomWord = useCallback((word: string) => {
    setCustomWords(prev => prev.filter(w => w !== word))
    toast({
      title: '删除成功',
      description: `已从自定义词典中删除 &quot;${word}&quot;`
    })
  }, [toast])

  // 清空结果
  const clearResults = useCallback(() => {
    setResult(null)
    setHighlightedText('')
  }, [])

  // 示例文本
  const exampleTexts = {
    en: 'This is a sampel text with some mispelled words. Plese check the speling carefully.',
    zh: '这是一个包含一些错误的中文文本示例。请仔细检查拼写。',
    mixed: 'This is a mixed text with both English and 中文内容. Some words might be mispelled.'
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-6 w-6" />
            拼写检查器
          </CardTitle>
          <CardDescription>
            智能检查文本拼写错误，支持多语言和自定义词典
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="checker" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="checker">拼写检查</TabsTrigger>
              <TabsTrigger value="dictionary">自定义词典</TabsTrigger>
              <TabsTrigger value="results">检查结果</TabsTrigger>
            </TabsList>

            <TabsContent value="checker" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="language">检查语言</Label>
                  <Select value={language} onValueChange={(value: 'en' | 'zh' | 'mixed') => setLanguage(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">英文</SelectItem>
                      <SelectItem value="zh">中文</SelectItem>
                      <SelectItem value="mixed">中英混合</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>示例文本</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setInputText(exampleTexts[language])}
                    className="w-full"
                  >
                    <Languages className="h-4 w-4 mr-2" />
                    加载示例文本
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="inputText">输入文本</Label>
                <Textarea
                  id="inputText"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  placeholder="请输入要检查拼写的文本..."
                  className="min-h-[200px]"
                />
              </div>

              <div className="flex gap-2">
                <Button onClick={performSpellCheck} disabled={isChecking} className="flex-1">
                  <Search className="h-4 w-4 mr-2" />
                  {isChecking ? '检查中...' : '开始检查'}
                </Button>
                <Button variant="outline" onClick={clearResults}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  清空结果
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="dictionary" className="space-y-4">
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    value={newCustomWord}
                    onChange={(e) => setNewCustomWord(e.target.value)}
                    placeholder="输入要添加的单词..."
                    onKeyPress={(e) => e.key === 'Enter' && addCustomWord()}
                  />
                  <Button onClick={addCustomWord}>
                    <Plus className="h-4 w-4 mr-2" />
                    添加
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label>自定义词典 ({customWords.length} 个单词)</Label>
                  <div className="flex flex-wrap gap-2 p-4 border rounded-lg min-h-[100px]">
                    {customWords.length === 0 ? (
                      <p className="text-muted-foreground">暂无自定义单词</p>
                    ) : (
                      customWords.map((word, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          {word}
                          <X
                            className="h-3 w-3 cursor-pointer hover:text-red-500"
                            onClick={() => removeCustomWord(word)}
                          />
                        </Badge>
                      ))
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              {result ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-5 w-5 text-green-500" />
                          <div>
                            <p className="text-sm text-muted-foreground">总词数</p>
                            <p className="text-2xl font-bold">{result.totalWords}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-5 w-5 text-red-500" />
                          <div>
                            <p className="text-sm text-muted-foreground">错误数</p>
                            <p className="text-2xl font-bold">{result.errorCount}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-5 w-5 text-blue-500" />
                          <div>
                            <p className="text-sm text-muted-foreground">准确率</p>
                            <p className="text-2xl font-bold">{result.accuracy.toFixed(1)}%</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {highlightedText && (
                    <div className="space-y-2">
                      <Label>高亮显示错误</Label>
                      <div 
                        className="p-4 border rounded-lg bg-muted/50"
                        dangerouslySetInnerHTML={{ __html: highlightedText }}
                      />
                    </div>
                  )}

                  {result.errors.length > 0 && (
                    <div className="space-y-2">
                      <Label>拼写错误详情</Label>
                      <div className="space-y-2">
                        {result.errors.map((error, index) => (
                          <Card key={index}>
                            <CardContent className="p-4">
                              <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                  <Badge variant="destructive">{error.word}</Badge>
                                  <span className="text-sm text-muted-foreground">
                                    位置: {error.position}
                                  </span>
                                </div>
                                <div className="text-sm">
                                  <span className="text-muted-foreground">上下文: </span>
                                  <code className="bg-muted px-1 rounded">{error.context}</code>
                                </div>
                                {error.suggestions.length > 0 && (
                                  <div className="flex flex-wrap gap-1">
                                    <span className="text-sm text-muted-foreground">建议: </span>
                                    {error.suggestions.map((suggestion, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs">
                                        {suggestion}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">暂无检查结果</p>
                  <p className="text-sm text-muted-foreground">请先在"拼写检查"标签页中输入文本并执行检查</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
