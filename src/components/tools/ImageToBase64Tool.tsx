'use client'

import React, { useState, useRef } from 'react'
import NextImage from 'next/image'
import { Upload, Download, Copy, Check, FileImage, Info, RefreshCw } from 'lucide-react'

interface ConversionResult {
  base64: string
  dataUrl: string
  size: number
  mimeType: string
  width?: number
  height?: number
}

export default function ImageToBase64Tool() {
  const [image, setImage] = useState<File | null>(null)
  const [imageUrl, setImageUrl] = useState<string>('')
  const [result, setResult] = useState<ConversionResult | null>(null)
  const [base64Input, setBase64Input] = useState<string>('')
  const [decodedImageUrl, setDecodedImageUrl] = useState<string>('')
  const [mode, setMode] = useState<'encode' | 'decode'>('encode')
  const [copied, setCopied] = useState(false)
  const [copyType, setCopyType] = useState<'base64' | 'dataUrl'>('base64')
  
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 图片转 Base64
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImage(file)
    const url = URL.createObjectURL(file)
    setImageUrl(url)

    const reader = new FileReader()
    reader.onload = (event) => {
      const dataUrl = event.target?.result as string
      const base64 = dataUrl.split(',')[1]
      
      // 获取图片尺寸
      const img = new Image()
      img.onload = () => {
        setResult({
          base64,
          dataUrl,
          size: file.size,
          mimeType: file.type,
          width: img.width,
          height: img.height
        })
      }
      img.src = dataUrl
    }
    reader.readAsDataURL(file)
  }

  // Base64 转图片
  const handleBase64Decode = () => {
    try {
      let base64 = base64Input.trim()
      
      // 如果输入的是完整的 data URL
      if (base64.startsWith('data:')) {
        setDecodedImageUrl(base64)
        return
      }
      
      // 尝试检测 MIME 类型
      let mimeType = 'image/png' // 默认
      
      // 根据 Base64 开头判断图片类型
      if (base64.startsWith('/9j/')) {
        mimeType = 'image/jpeg'
      } else if (base64.startsWith('iVBORw0KGgo')) {
        mimeType = 'image/png'
      } else if (base64.startsWith('R0lGOD')) {
        mimeType = 'image/gif'
      } else if (base64.startsWith('UklGR')) {
        mimeType = 'image/webp'
      }
      
      const dataUrl = `data:${mimeType};base64,${base64}`
      setDecodedImageUrl(dataUrl)
    } catch (error) {
      alert('Base64 解码失败，请检查输入')
    }
  }

  // 复制到剪贴板
  const copyToClipboard = (type: 'base64' | 'dataUrl') => {
    if (!result) return
    
    const text = type === 'base64' ? result.base64 : result.dataUrl
    navigator.clipboard.writeText(text).then(() => {
      setCopied(true)
      setCopyType(type)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  // 下载解码后的图片
  const downloadDecodedImage = () => {
    if (!decodedImageUrl) return
    
    const link = document.createElement('a')
    link.href = decodedImageUrl
    link.download = `decoded_image_${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 清除数据
  const clearData = () => {
    setImage(null)
    setImageUrl('')
    setResult(null)
    setBase64Input('')
    setDecodedImageUrl('')
    if (fileInputRef.current) fileInputRef.current.value = ''
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片 Base64 转换</h2>
        <p className="text-gray-600">
          图片与 Base64 编码相互转换，支持多种图片格式
        </p>
      </div>

      {/* 模式切换 */}
      <div className="flex gap-2 mb-6">
        <button
          onClick={() => setMode('encode')}
          className={`px-4 py-2 rounded-md ${
            mode === 'encode'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 hover:bg-gray-200'
          }`}
        >
          图片转 Base64
        </button>
        <button
          onClick={() => setMode('decode')}
          className={`px-4 py-2 rounded-md ${
            mode === 'decode'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 hover:bg-gray-200'
          }`}
        >
          Base64 转图片
        </button>
      </div>

      {mode === 'encode' ? (
        <div className="grid md:grid-cols-2 gap-6">
          {/* 图片上传区域 */}
          <div>
            <div className="bg-white rounded-lg border border-gray-300 p-6">
              <h3 className="font-medium mb-4">选择图片</h3>
              
              {!imageUrl ? (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
                  <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleImageUpload}
                    accept="image/*"
                    className="hidden"
                  />
                  <div className="text-center">
                    <FileImage className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-600 mb-4">拖拽图片到此处或点击选择</p>
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                      <Upload className="w-4 h-4 inline mr-2" />
                      选择图片
                    </button>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="relative bg-gray-100 rounded-lg p-4 mb-4">
                    <NextImage
                      src={imageUrl}
                      alt="Preview"
                      width={400}
                      height={300}
                      className="max-w-full max-h-64 mx-auto object-contain"
                      unoptimized
                    />
                  </div>
                  {result && (
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">文件名：</span>
                        <span>{image?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">文件大小：</span>
                        <span>{formatFileSize(result.size)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">图片尺寸：</span>
                        <span>{result.width} × {result.height} px</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">MIME 类型：</span>
                        <span>{result.mimeType}</span>
                      </div>
                    </div>
                  )}
                  <button
                    onClick={clearData}
                    className="w-full mt-4 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                  >
                    <RefreshCw className="w-4 h-4 inline mr-2" />
                    重新选择
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Base64 结果 */}
          <div>
            <div className="bg-white rounded-lg border border-gray-300 p-6">
              <h3 className="font-medium mb-4">Base64 编码结果</h3>
              
              {result ? (
                <div>
                  {/* 复制按钮 */}
                  <div className="flex gap-2 mb-4">
                    <button
                      onClick={() => copyToClipboard('base64')}
                      className={`flex-1 px-4 py-2 rounded-md ${
                        copied && copyType === 'base64'
                          ? 'bg-green-500 text-white'
                          : 'bg-blue-500 text-white hover:bg-blue-600'
                      }`}
                    >
                      {copied && copyType === 'base64' ? (
                        <>
                          <Check className="w-4 h-4 inline mr-2" />
                          已复制
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4 inline mr-2" />
                          复制 Base64
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => copyToClipboard('dataUrl')}
                      className={`flex-1 px-4 py-2 rounded-md ${
                        copied && copyType === 'dataUrl'
                          ? 'bg-green-500 text-white'
                          : 'bg-purple-500 text-white hover:bg-purple-600'
                      }`}
                    >
                      {copied && copyType === 'dataUrl' ? (
                        <>
                          <Check className="w-4 h-4 inline mr-2" />
                          已复制
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4 inline mr-2" />
                          复制 Data URL
                        </>
                      )}
                    </button>
                  </div>

                  {/* Base64 内容 */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        纯 Base64（不含前缀）
                      </label>
                      <textarea
                        value={result.base64}
                        readOnly
                        className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md font-mono text-xs"
                        onClick={(e) => e.currentTarget.select()}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        长度：{result.base64.length} 字符
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">
                        完整 Data URL（可直接用于 img src）
                      </label>
                      <textarea
                        value={result.dataUrl}
                        readOnly
                        className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md font-mono text-xs"
                        onClick={(e) => e.currentTarget.select()}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        长度：{result.dataUrl.length} 字符
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  请先选择图片
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 gap-6">
          {/* Base64 输入区域 */}
          <div>
            <div className="bg-white rounded-lg border border-gray-300 p-6">
              <h3 className="font-medium mb-4">输入 Base64 编码</h3>
              
              <textarea
                value={base64Input}
                onChange={(e) => setBase64Input(e.target.value)}
                placeholder="粘贴 Base64 编码或完整的 Data URL..."
                className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md font-mono text-sm"
              />
              
              <div className="mt-4 space-y-2">
                <button
                  onClick={handleBase64Decode}
                  disabled={!base64Input}
                  className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  解码为图片
                </button>
                <button
                  onClick={clearData}
                  className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                >
                  清空
                </button>
              </div>
            </div>
          </div>

          {/* 解码结果 */}
          <div>
            <div className="bg-white rounded-lg border border-gray-300 p-6">
              <h3 className="font-medium mb-4">解码结果</h3>
              
              {decodedImageUrl ? (
                <div>
                  <div className="bg-gray-100 rounded-lg p-4 mb-4">
                    <NextImage
                      src={decodedImageUrl}
                      alt="Decoded"
                      width={400}
                      height={300}
                      className="max-w-full max-h-64 mx-auto object-contain"
                      unoptimized
                      onError={() => {
                        alert('图片解码失败，请检查 Base64 编码是否正确')
                        setDecodedImageUrl('')
                      }}
                    />
                  </div>
                  <button
                    onClick={downloadDecodedImage}
                    className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                  >
                    <Download className="w-4 h-4 inline mr-2" />
                    下载图片
                  </button>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  请输入 Base64 编码
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持 JPG、PNG、GIF、WebP 等常见图片格式</li>
              <li>图片转 Base64：选择图片后自动生成编码</li>
              <li>Base64 转图片：支持纯 Base64 或完整 Data URL</li>
              <li>点击文本框可快速全选内容</li>
              <li>所有处理都在浏览器本地完成，保护您的隐私</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
