'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, FileText, Key, CheckCircle, XCircle, AlertTriangle, Copy, Upload, Download, Eye, EyeOff } from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface SignatureResult {
  isValid: boolean
  algorithm: string
  publicKey: string
  message: string
  signature: string
  timestamp: string
  errors?: string[]
}

interface KeyPair {
  publicKey: string
  privateKey: string
  algorithm: string
  keySize: number
}

const DigitalSignatureVerifierTool: React.FC = () => {
  const [mode, setMode] = useState<'verify' | 'generate' | 'sign'>('verify')
  const [message, setMessage] = useState('')
  const [signature, setSignature] = useState('')
  const [publicKey, setPublicKey] = useState('')
  const [privateKey, setPrivateKey] = useState('')
  const [algorithm, setAlgorithm] = useState<'RSA' | 'ECDSA' | 'Ed25519'>('RSA')
  const [keySize, setKeySize] = useState<1024 | 2048 | 4096>(2048)
  const [result, setResult] = useState<SignatureResult | null>(null)
  const [keyPair, setKeyPair] = useState<KeyPair | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showPrivateKey, setShowPrivateKey] = useState(false)

  // 模拟密钥对生成
  const generateKeyPair = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // 模拟异步密钥生成
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockKeyPair: KeyPair = {
        publicKey: generateMockPublicKey(algorithm, keySize),
        privateKey: generateMockPrivateKey(algorithm, keySize),
        algorithm,
        keySize
      }
      
      setKeyPair(mockKeyPair)
      setPublicKey(mockKeyPair.publicKey)
      setPrivateKey(mockKeyPair.privateKey)
    } catch (err) {
      setError('密钥生成失败')
    } finally {
      setLoading(false)
    }
  }

  // 模拟数字签名
  const signMessage = async () => {
    if (!message.trim()) {
      setError('请输入要签名的消息')
      return
    }
    
    if (!privateKey.trim()) {
      setError('请提供私钥')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      // 模拟签名过程
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const mockSignature = generateMockSignature(message, algorithm)
      setSignature(mockSignature)
      
      const signResult: SignatureResult = {
        isValid: true,
        algorithm,
        publicKey: extractPublicKeyFromPrivate(privateKey),
        message,
        signature: mockSignature,
        timestamp: new Date().toISOString()
      }
      
      setResult(signResult)
    } catch (err) {
      setError('签名失败')
    } finally {
      setLoading(false)
    }
  }

  // 模拟签名验证
  const verifySignature = async () => {
    if (!message.trim() || !signature.trim() || !publicKey.trim()) {
      setError('请填写所有必要字段')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      // 模拟验证过程
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const isValid = validateMockSignature(message, signature, publicKey)
      const errors: string[] = []
      
      if (!isValid) {
        errors.push('签名与消息不匹配')
      }
      
      if (!isValidPublicKey(publicKey)) {
        errors.push('公钥格式无效')
      }
      
      const verifyResult: SignatureResult = {
        isValid: isValid && errors.length === 0,
        algorithm: detectAlgorithm(publicKey),
        publicKey,
        message,
        signature,
        timestamp: new Date().toISOString(),
        errors: errors.length > 0 ? errors : undefined
      }
      
      setResult(verifyResult)
    } catch (err) {
      setError('验证失败')
    } finally {
      setLoading(false)
    }
  }

  // 生成模拟公钥
  const generateMockPublicKey = (alg: string, size: number): string => {
    const header = `-----BEGIN ${alg} PUBLIC KEY-----`
    const footer = `-----END ${alg} PUBLIC KEY-----`
    const keyData = Array.from({ length: Math.ceil(size / 64) }, () => 
      Array.from({ length: 64 }, () => 
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'[Math.floor(Math.random() * 64)]
      ).join('')
    ).join('\n')
    
    return `${header}\n${keyData}\n${footer}`
  }

  // 生成模拟私钥
  const generateMockPrivateKey = (alg: string, size: number): string => {
    const header = `-----BEGIN ${alg} PRIVATE KEY-----`
    const footer = `-----END ${alg} PRIVATE KEY-----`
    const keyData = Array.from({ length: Math.ceil(size / 32) }, () => 
      Array.from({ length: 64 }, () => 
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'[Math.floor(Math.random() * 64)]
      ).join('')
    ).join('\n')
    
    return `${header}\n${keyData}\n${footer}`
  }

  // 生成模拟签名
  const generateMockSignature = (msg: string, alg: string): string => {
    const hash = btoa(msg + alg + Date.now())
    return hash.replace(/[+/=]/g, char => {
      switch (char) {
        case '+': return '-'
        case '/': return '_'
        case '=': return ''
        default: return char
      }
    })
  }

  // 验证模拟签名
  const validateMockSignature = (msg: string, sig: string, pubKey: string): boolean => {
    // 简单的模拟验证逻辑
    return sig.length > 20 && pubKey.includes('PUBLIC KEY') && msg.length > 0
  }

  // 验证公钥格式
  const isValidPublicKey = (key: string): boolean => {
    return key.includes('BEGIN') && key.includes('PUBLIC KEY') && key.includes('END')
  }

  // 检测算法
  const detectAlgorithm = (key: string): string => {
    if (key.includes('RSA')) return 'RSA'
    if (key.includes('ECDSA')) return 'ECDSA'
    if (key.includes('Ed25519')) return 'Ed25519'
    return 'Unknown'
  }

  // 从私钥提取公钥
  const extractPublicKeyFromPrivate = (privKey: string): string => {
    return privKey.replace('PRIVATE', 'PUBLIC').replace(/[a-zA-Z0-9+/=]{100,}/g, match => 
      match.substring(0, Math.floor(match.length / 2))
    )
  }

  // 复制文本
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 导出密钥
  const exportKey = (key: string, filename: string) => {
    const blob = new Blob([key], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 清空表单
  const clearForm = () => {
    setMessage('')
    setSignature('')
    setPublicKey('')
    setPrivateKey('')
    setResult(null)
    setKeyPair(null)
    setError(null)
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Shield className="w-8 h-8" />
          数字签名验证器
        </h1>
        <p className="text-muted-foreground">
          生成、签名和验证数字签名，确保消息完整性和身份认证
        </p>
      </div>

      {/* 模式切换 */}
      <Card className="p-6">
        <Tabs value={mode} onValueChange={(value) => setMode(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="verify">验证签名</TabsTrigger>
            <TabsTrigger value="sign">创建签名</TabsTrigger>
            <TabsTrigger value="generate">生成密钥</TabsTrigger>
          </TabsList>

          {/* 验证签名 */}
          <TabsContent value="verify" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="verify-message">原始消息</Label>
                <Textarea
                  id="verify-message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="输入需要验证的原始消息..."
                  className="min-h-[100px]"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="verify-signature">数字签名</Label>
                <Textarea
                  id="verify-signature"
                  value={signature}
                  onChange={(e) => setSignature(e.target.value)}
                  placeholder="输入数字签名..."
                  className="min-h-[100px] font-mono text-sm"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="verify-public-key">公钥</Label>
                <Textarea
                  id="verify-public-key"
                  value={publicKey}
                  onChange={(e) => setPublicKey(e.target.value)}
                  placeholder="输入 PEM 格式的公钥..."
                  className="min-h-[150px] font-mono text-sm"
                />
              </div>

              <Button onClick={verifySignature} disabled={loading} className="w-full">
                {loading ? '验证中...' : '验证签名'}
              </Button>
            </div>
          </TabsContent>

          {/* 创建签名 */}
          <TabsContent value="sign" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sign-message">要签名的消息</Label>
                <Textarea
                  id="sign-message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="输入要签名的消息..."
                  className="min-h-[100px]"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sign-private-key">私钥</Label>
                <div className="relative">
                  <Textarea
                    id="sign-private-key"
                    value={privateKey}
                    onChange={(e) => setPrivateKey(e.target.value)}
                    placeholder="输入 PEM 格式的私钥..."
                    className="min-h-[150px] font-mono text-sm pr-10"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-2"
                    onClick={() => setShowPrivateKey(!showPrivateKey)}
                  >
                    {showPrivateKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
              </div>

              <Button onClick={signMessage} disabled={loading} className="w-full">
                {loading ? '签名中...' : '创建签名'}
              </Button>

              {signature && (
                <div className="space-y-2">
                  <Label>生成的签名</Label>
                  <div className="relative">
                    <Textarea
                      value={signature}
                      readOnly
                      className="min-h-[100px] font-mono text-sm pr-10"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-2"
                      onClick={() => copyToClipboard(signature)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          {/* 生成密钥 */}
          <TabsContent value="generate" className="space-y-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="algorithm">算法</Label>
                  <select
                    id="algorithm"
                    value={algorithm}
                    onChange={(e) => setAlgorithm(e.target.value as any)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="RSA">RSA</option>
                    <option value="ECDSA">ECDSA</option>
                    <option value="Ed25519">Ed25519</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="key-size">密钥长度</Label>
                  <select
                    id="key-size"
                    value={keySize}
                    onChange={(e) => setKeySize(Number(e.target.value) as any)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value={1024}>1024 位</option>
                    <option value={2048}>2048 位</option>
                    <option value={4096}>4096 位</option>
                  </select>
                </div>
              </div>

              <Button onClick={generateKeyPair} disabled={loading} className="w-full">
                {loading ? '生成中...' : '生成密钥对'}
              </Button>

              {keyPair && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label>公钥</Label>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(keyPair.publicKey)}
                        >
                          <Copy className="w-4 h-4 mr-1" />
                          复制
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => exportKey(keyPair.publicKey, 'public_key.pem')}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          下载
                        </Button>
                      </div>
                    </div>
                    <Textarea
                      value={keyPair.publicKey}
                      readOnly
                      className="min-h-[150px] font-mono text-sm"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label>私钥</Label>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(keyPair.privateKey)}
                        >
                          <Copy className="w-4 h-4 mr-1" />
                          复制
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => exportKey(keyPair.privateKey, 'private_key.pem')}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          下载
                        </Button>
                      </div>
                    </div>
                    <Textarea
                      value={keyPair.privateKey}
                      readOnly
                      className="min-h-[200px] font-mono text-sm"
                    />
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2 mt-4">
          <Button variant="outline" onClick={clearForm} className="flex-1">
            清空
          </Button>
        </div>
      </Card>

      {/* 验证结果 */}
      {result && (
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">验证结果</h3>
              <div className={`flex items-center gap-2 ${result.isValid ? 'text-green-500' : 'text-red-500'}`}>
                {result.isValid ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <XCircle className="w-5 h-5" />
                )}
                <span className="font-medium">
                  {result.isValid ? '签名有效' : '签名无效'}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>算法</Label>
                <Badge variant="outline">{result.algorithm}</Badge>
              </div>
              <div className="space-y-2">
                <Label>验证时间</Label>
                <span className="text-sm text-muted-foreground">
                  {new Date(result.timestamp).toLocaleString()}
                </span>
              </div>
            </div>

            {result.errors && result.errors.length > 0 && (
              <div className="space-y-2">
                <Label>错误信息</Label>
                <div className="space-y-1">
                  {result.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-600 flex items-center gap-2">
                      <XCircle className="w-4 h-4" />
                      {error}
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label>签名摘要</Label>
              <div className="p-3 bg-muted rounded-lg font-mono text-sm break-all">
                {result.signature.substring(0, 100)}...
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* 使用说明 */}
      <Card className="p-6 border-blue-200 bg-blue-50 dark:bg-blue-900/20">
        <div className="flex items-start gap-3">
          <FileText className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-blue-800 dark:text-blue-200">使用说明：</p>
            <ul className="mt-1 text-blue-700 dark:text-blue-300 space-y-1">
              <li>• 数字签名用于验证消息完整性和发送者身份</li>
              <li>• 支持 RSA、ECDSA、Ed25519 等主流签名算法</li>
              <li>• 私钥用于签名，公钥用于验证</li>
              <li>• 妥善保管私钥，可以分享公钥</li>
              <li>• 所有操作在浏览器本地完成，确保隐私安全</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default DigitalSignatureVerifierTool