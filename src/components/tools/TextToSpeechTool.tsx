'use client'

import { useState, useRef, useEffect } from 'react'
import { Volume2, Play, Pause, Download, Settings, AlertCircle, Info, Languages, Mic } from 'lucide-react'

interface Voice {
  voice: SpeechSynthesisVoice
  lang: string
  name: string
}

export default function TextToSpeechTool() {
  const [text, setText] = useState('')
  const [voices, setVoices] = useState<Voice[]>([])
  const [selectedVoice, setSelectedVoice] = useState<string>('')
  const [rate, setRate] = useState(1)
  const [pitch, setPitch] = useState(1)
  const [volume, setVolume] = useState(1)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [error, setError] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  
  const speechRef = useRef<SpeechSynthesisUtterance | null>(null)
  const synthRef = useRef<SpeechSynthesis | null>(null)

  // 初始化语音合成
  useEffect(() => {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      synthRef.current = window.speechSynthesis
      
      const loadVoices = () => {
        const availableVoices = synthRef.current!.getVoices()
        const voiceList: Voice[] = availableVoices.map(voice => ({
          voice,
          lang: voice.lang,
          name: voice.name
        }))
        
        // 按语言分组并排序
        voiceList.sort((a, b) => {
          if (a.lang.startsWith('zh') && !b.lang.startsWith('zh')) return -1
          if (!a.lang.startsWith('zh') && b.lang.startsWith('zh')) return 1
          return a.lang.localeCompare(b.lang)
        })
        
        setVoices(voiceList)
        
        // 默认选择中文语音
        const defaultVoice = voiceList.find(v => v.lang.startsWith('zh')) || voiceList[0]
        if (defaultVoice) {
          setSelectedVoice(defaultVoice.name)
        }
      }
      
      loadVoices()
      
      // 某些浏览器需要监听 voiceschanged 事件
      synthRef.current.addEventListener('voiceschanged', loadVoices)
      
      return () => {
        synthRef.current?.removeEventListener('voiceschanged', loadVoices)
      }
    } else {
      setError('您的浏览器不支持语音合成功能')
    }
  }, [])

  // 播放语音
  const handlePlay = () => {
    if (!text.trim()) {
      setError('请输入要朗读的文本')
      return
    }
    
    if (!synthRef.current) {
      setError('语音合成功能不可用')
      return
    }
    
    setError('')
    
    // 如果正在暂停，恢复播放
    if (isPaused && speechRef.current) {
      synthRef.current.resume()
      setIsPaused(false)
      setIsPlaying(true)
      return
    }
    
    // 停止当前播放
    synthRef.current.cancel()
    
    // 创建新的语音实例
    const utterance = new SpeechSynthesisUtterance(text)
    
    // 设置语音
    const voice = voices.find(v => v.name === selectedVoice)
    if (voice) {
      utterance.voice = voice.voice
      utterance.lang = voice.lang
    }
    
    // 设置参数
    utterance.rate = rate
    utterance.pitch = pitch
    utterance.volume = volume
    
    // 事件监听
    utterance.onstart = () => {
      setIsPlaying(true)
      setIsPaused(false)
    }
    
    utterance.onend = () => {
      setIsPlaying(false)
      setIsPaused(false)
    }
    
    utterance.onerror = (event) => {
      setError('语音播放出错：' + event.error)
      setIsPlaying(false)
      setIsPaused(false)
    }
    
    speechRef.current = utterance
    synthRef.current.speak(utterance)
  }

  // 暂停播放
  const handlePause = () => {
    if (synthRef.current && isPlaying) {
      synthRef.current.pause()
      setIsPaused(true)
      setIsPlaying(false)
    }
  }

  // 停止播放
  const handleStop = () => {
    if (synthRef.current) {
      synthRef.current.cancel()
      setIsPlaying(false)
      setIsPaused(false)
    }
  }

  // 下载音频（使用 MediaRecorder API）
  const handleDownload = async () => {
    if (!text.trim()) {
      setError('请输入要转换的文本')
      return
    }
    
    setError('音频下载功能需要浏览器支持 MediaRecorder API，部分浏览器可能不支持')
  }

  // 加载示例文本
  const loadExample = () => {
    setText(`欢迎使用文本转语音工具！

这是一个功能强大的在线语音合成工具，可以将您输入的文本转换为自然流畅的语音。

主要特点：
1. 支持多种语言和语音
2. 可调节语速、音调和音量
3. 实时预览效果
4. 简单易用的界面

现在就试试吧！`)
  }

  // 获取语言显示名称
  const getLanguageDisplay = (lang: string) => {
    const langMap: { [key: string]: string } = {
      'zh-CN': '中文（简体）',
      'zh-TW': '中文（繁体）',
      'zh-HK': '中文（香港）',
      'en-US': '英语（美国）',
      'en-GB': '英语（英国）',
      'ja-JP': '日语',
      'ko-KR': '韩语',
      'fr-FR': '法语',
      'de-DE': '德语',
      'es-ES': '西班牙语',
      'ru-RU': '俄语',
      'ar-SA': '阿拉伯语'
    }
    
    return langMap[lang] || lang
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本转语音</h2>
        <p className="text-gray-600">
          将文本转换为自然流畅的语音，支持多种语言和声音
        </p>
      </div>

      {/* 语音选择 */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2 flex items-center gap-2">
          <Languages className="w-4 h-4" />
          选择语音
        </label>
        <select
          value={selectedVoice}
          onChange={(e) => setSelectedVoice(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          disabled={voices.length === 0}
        >
          {voices.length === 0 ? (
            <option>加载语音中...</option>
          ) : (
            voices.map((voice) => (
              <option key={voice.name} value={voice.name}>
                {voice.name} ({getLanguageDisplay(voice.lang)})
              </option>
            ))
          )}
        </select>
      </div>

      {/* 文本输入 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium">输入文本</label>
          <div className="flex gap-2">
            <button
              onClick={loadExample}
              className="text-sm text-blue-500 hover:text-blue-600"
            >
              加载示例
            </button>
            <span className="text-sm text-gray-500">{text.length} 字符</span>
          </div>
        </div>
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="在此输入要转换为语音的文本..."
          className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* 高级设置 */}
      <div className="mb-6">
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
        >
          <Settings className="w-4 h-4" />
          高级设置
          <span className="text-gray-400">{showSettings ? '▲' : '▼'}</span>
        </button>
        
        {showSettings && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                语速：{rate.toFixed(1)}x
              </label>
              <input
                type="range"
                min="0.5"
                max="2"
                step="0.1"
                value={rate}
                onChange={(e) => setRate(parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0.5x（慢速）</span>
                <span>1.0x（正常）</span>
                <span>2.0x（快速）</span>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">
                音调：{pitch.toFixed(1)}
              </label>
              <input
                type="range"
                min="0.5"
                max="2"
                step="0.1"
                value={pitch}
                onChange={(e) => setPitch(parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0.5（低沉）</span>
                <span>1.0（正常）</span>
                <span>2.0（高亢）</span>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">
                音量：{Math.round(volume * 100)}%
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => setVolume(parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0%</span>
                <span>50%</span>
                <span>100%</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 控制按钮 */}
      <div className="flex items-center justify-center gap-4 mb-6">
        {!isPlaying && !isPaused ? (
          <button
            onClick={handlePlay}
            disabled={!text.trim() || voices.length === 0}
            className="flex items-center gap-2 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            <Play className="w-5 h-5" />
            播放
          </button>
        ) : isPlaying ? (
          <button
            onClick={handlePause}
            className="flex items-center gap-2 px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            <Pause className="w-5 h-5" />
            暂停
          </button>
        ) : (
          <button
            onClick={handlePlay}
            className="flex items-center gap-2 px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            <Play className="w-5 h-5" />
            继续
          </button>
        )}
        
        {(isPlaying || isPaused) && (
          <button
            onClick={handleStop}
            className="flex items-center gap-2 px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            <Volume2 className="w-5 h-5" />
            停止
          </button>
        )}
        
        <button
          onClick={handleDownload}
          disabled={!text.trim()}
          className="flex items-center gap-2 px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <Download className="w-5 h-5" />
          下载音频
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-6 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-800">
          <AlertCircle className="w-4 h-4 flex-shrink-0" />
          {error}
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>输入或粘贴要转换的文本</li>
              <li>选择合适的语音和语言</li>
              <li>调整语速、音调等参数以获得最佳效果</li>
              <li>点击播放按钮即可听到语音</li>
              <li>支持暂停和继续播放</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 兼容性提示 */}
      <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-yellow-900">
            <h3 className="font-semibold mb-1">兼容性说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>语音合成功能依赖浏览器支持</li>
              <li>不同浏览器和操作系统提供的语音可能不同</li>
              <li>建议使用最新版本的 Chrome、Edge 或 Safari 浏览器</li>
              <li>音频下载功能可能在某些浏览器中不可用</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 语音识别提示 */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-start">
          <Mic className="h-5 w-5 text-gray-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-gray-700">
            <p>
              <span className="font-semibold">提示：</span>
              如需语音转文字功能，请使用我们的&ldquo;语音识别&rdquo;工具。
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
