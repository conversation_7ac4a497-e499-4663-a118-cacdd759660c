'use client'

import { useState, useEffect } from 'react'
import { Send, Copy, Check, Clock, Globe, Server, AlertCircle, Info } from 'lucide-react'

interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS'
  url: string
  headers: { key: string; value: string }[]
  body: string
  bodyType: 'none' | 'json' | 'text' | 'form'
}

interface ResponseData {
  status: number
  statusText: string
  headers: Record<string, string>
  body: any
  time: number
  size: number
}

export default function HttpRequestTool() {
  const [config, setConfig] = useState<RequestConfig>({
    method: 'GET',
    url: '',
    headers: [{ key: '', value: '' }],
    body: '',
    bodyType: 'none',
  })
  
  const [response, setResponse] = useState<ResponseData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState<'headers' | 'body'>('headers')
  const [responseTab, setResponseTab] = useState<'body' | 'headers' | 'info'>('body')
  const [copied, setCopied] = useState(false)
  const [history, setHistory] = useState<{ url: string; method: string; time: string }[]>([])

  // 预设的常用请求头
  const commonHeaders = [
    { name: 'Content-Type', values: ['application/json', 'application/x-www-form-urlencoded', 'text/plain', 'text/html'] },
    { name: 'Accept', values: ['application/json', 'text/html', 'text/plain', '*/*'] },
    { name: 'Authorization', values: ['Bearer ', 'Basic ', 'Token '] },
    { name: 'User-Agent', values: ['Mozilla/5.0', 'PostmanRuntime/7.0.0', 'curl/7.0.0'] },
  ]

  // 发送请求
  const sendRequest = async () => {
    if (!config.url) {
      setError('请输入 URL')
      return
    }

    setLoading(true)
    setError('')
    setResponse(null)

    const startTime = Date.now()

    try {
      // 构建请求头
      const headers: Record<string, string> = {}
      config.headers.forEach(header => {
        if (header.key && header.value) {
          headers[header.key] = header.value
        }
      })

      // 构建请求体
      let body: string | FormData | undefined
      if (config.method !== 'GET' && config.method !== 'HEAD' && config.bodyType !== 'none') {
        if (config.bodyType === 'json') {
          headers['Content-Type'] = 'application/json'
          body = config.body
        } else if (config.bodyType === 'form') {
          headers['Content-Type'] = 'application/x-www-form-urlencoded'
          body = config.body
        } else {
          body = config.body
        }
      }

      // 发送请求（注意：由于浏览器的 CORS 限制，某些请求可能会失败）
      const res = await fetch(config.url, {
        method: config.method,
        headers,
        body: body || undefined,
        mode: 'cors',
      })

      const responseTime = Date.now() - startTime

      // 获取响应头
      const responseHeaders: Record<string, string> = {}
      res.headers.forEach((value, key) => {
        responseHeaders[key] = value
      })

      // 获取响应体
      const contentType = res.headers.get('content-type') || ''
      let responseBody: any
      let responseSize = 0

      if (contentType.includes('application/json')) {
        const text = await res.text()
        responseSize = new Blob([text]).size
        try {
          responseBody = JSON.parse(text)
        } catch {
          responseBody = text
        }
      } else if (contentType.includes('text/')) {
        responseBody = await res.text()
        responseSize = new Blob([responseBody]).size
      } else {
        responseBody = '(Binary content)'
        responseSize = parseInt(res.headers.get('content-length') || '0')
      }

      setResponse({
        status: res.status,
        statusText: res.statusText,
        headers: responseHeaders,
        body: responseBody,
        time: responseTime,
        size: responseSize,
      })

      // 添加到历史记录
      setHistory(prev => [
        { url: config.url, method: config.method, time: new Date().toLocaleTimeString() },
        ...prev.slice(0, 9)
      ])
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败')
    } finally {
      setLoading(false)
    }
  }

  // 添加请求头
  const addHeader = () => {
    setConfig(prev => ({
      ...prev,
      headers: [...prev.headers, { key: '', value: '' }]
    }))
  }

  // 更新请求头
  const updateHeader = (index: number, field: 'key' | 'value', value: string) => {
    setConfig(prev => ({
      ...prev,
      headers: prev.headers.map((header, i) =>
        i === index ? { ...header, [field]: value } : header
      )
    }))
  }

  // 删除请求头
  const removeHeader = (index: number) => {
    setConfig(prev => ({
      ...prev,
      headers: prev.headers.filter((_, i) => i !== index)
    }))
  }

  // 复制响应
  const copyResponse = async () => {
    if (!response) return
    
    try {
      const content = typeof response.body === 'string' 
        ? response.body 
        : JSON.stringify(response.body, null, 2)
      await navigator.clipboard.writeText(content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 格式化字节大小
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取状态码颜色
  const getStatusColor = (status: number): string => {
    if (status >= 200 && status < 300) return 'text-green-600'
    if (status >= 300 && status < 400) return 'text-blue-600'
    if (status >= 400 && status < 500) return 'text-yellow-600'
    if (status >= 500) return 'text-red-600'
    return 'text-gray-600'
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">HTTP 请求测试器</h2>
        <p className="text-gray-600">
          在线测试 HTTP 请求，支持多种请求方法和自定义请求头
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* 左侧：请求配置 */}
        <div className="space-y-4">
          {/* URL 和方法 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex gap-2">
              <select
                value={config.method}
                onChange={(e) => setConfig({ ...config, method: e.target.value as any })}
                className="px-3 py-2 border border-gray-300 rounded-md font-medium"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
                <option value="PATCH">PATCH</option>
                <option value="HEAD">HEAD</option>
                <option value="OPTIONS">OPTIONS</option>
              </select>
              <input
                type="text"
                value={config.url}
                onChange={(e) => setConfig({ ...config, url: e.target.value })}
                placeholder="https://api.example.com/endpoint"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={sendRequest}
                disabled={loading}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <Send className="w-4 h-4" />
                {loading ? '发送中...' : '发送'}
              </button>
            </div>
          </div>

          {/* 请求配置标签页 */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="flex border-b">
              <button
                onClick={() => setActiveTab('headers')}
                className={`px-4 py-2 font-medium ${
                  activeTab === 'headers'
                    ? 'text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                请求头
              </button>
              {config.method !== 'GET' && config.method !== 'HEAD' && (
                <button
                  onClick={() => setActiveTab('body')}
                  className={`px-4 py-2 font-medium ${
                    activeTab === 'body'
                      ? 'text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  请求体
                </button>
              )}
            </div>

            <div className="p-4">
              {activeTab === 'headers' && (
                <div className="space-y-2">
                  {config.headers.map((header, index) => (
                    <div key={index} className="flex gap-2">
                      <input
                        type="text"
                        value={header.key}
                        onChange={(e) => updateHeader(index, 'key', e.target.value)}
                        placeholder="Header name"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                      />
                      <input
                        type="text"
                        value={header.value}
                        onChange={(e) => updateHeader(index, 'value', e.target.value)}
                        placeholder="Header value"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                      />
                      <button
                        onClick={() => removeHeader(index)}
                        className="px-3 py-2 text-red-500 hover:bg-red-50 rounded-md"
                      >
                        删除
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={addHeader}
                    className="w-full px-3 py-2 text-blue-500 hover:bg-blue-50 rounded-md text-sm"
                  >
                    + 添加请求头
                  </button>
                </div>
              )}

              {activeTab === 'body' && (
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        value="none"
                        checked={config.bodyType === 'none'}
                        onChange={(e) => setConfig({ ...config, bodyType: 'none' })}
                      />
                      <span className="text-sm">无</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        value="json"
                        checked={config.bodyType === 'json'}
                        onChange={(e) => setConfig({ ...config, bodyType: 'json' })}
                      />
                      <span className="text-sm">JSON</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        value="text"
                        checked={config.bodyType === 'text'}
                        onChange={(e) => setConfig({ ...config, bodyType: 'text' })}
                      />
                      <span className="text-sm">文本</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        value="form"
                        checked={config.bodyType === 'form'}
                        onChange={(e) => setConfig({ ...config, bodyType: 'form' })}
                      />
                      <span className="text-sm">表单</span>
                    </label>
                  </div>
                  
                  {config.bodyType !== 'none' && (
                    <textarea
                      value={config.body}
                      onChange={(e) => setConfig({ ...config, body: e.target.value })}
                      placeholder={
                        config.bodyType === 'json' ? '{"key": "value"}' :
                        config.bodyType === 'form' ? 'key1=value1&key2=value2' :
                        '输入请求体内容...'
                      }
                      className="w-full h-32 p-3 font-mono text-sm border border-gray-300 rounded-md resize-none"
                    />
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 历史记录 */}
          {history.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h3 className="font-medium mb-3 flex items-center gap-2">
                <Clock className="w-4 h-4" />
                请求历史
              </h3>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {history.map((item, index) => (
                  <div key={index} className="text-sm text-gray-600 flex items-center gap-2">
                    <span className="font-medium text-gray-700">{item.method}</span>
                    <span className="truncate flex-1">{item.url}</span>
                    <span className="text-xs text-gray-500">{item.time}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 右侧：响应结果 */}
        <div className="bg-white rounded-lg border border-gray-200">
          {error && (
            <div className="p-4 bg-red-50 border-b border-red-200">
              <p className="text-red-800 flex items-center gap-2">
                <AlertCircle className="w-4 h-4" />
                {error}
              </p>
            </div>
          )}

          {response ? (
            <>
              {/* 响应状态 */}
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <span className={`text-2xl font-bold ${getStatusColor(response.status)}`}>
                      {response.status}
                    </span>
                    <span className="text-gray-600">{response.statusText}</span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {response.time}ms
                    </span>
                    <span className="flex items-center gap-1">
                      <Server className="w-3 h-3" />
                      {formatBytes(response.size)}
                    </span>
                  </div>
                </div>
              </div>

              {/* 响应标签页 */}
              <div className="flex border-b">
                <button
                  onClick={() => setResponseTab('body')}
                  className={`px-4 py-2 font-medium ${
                    responseTab === 'body'
                      ? 'text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  响应体
                </button>
                <button
                  onClick={() => setResponseTab('headers')}
                  className={`px-4 py-2 font-medium ${
                    responseTab === 'headers'
                      ? 'text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  响应头
                </button>
                <button
                  onClick={() => setResponseTab('info')}
                  className={`px-4 py-2 font-medium ${
                    responseTab === 'info'
                      ? 'text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  信息
                </button>
                <div className="ml-auto p-2">
                  <button
                    onClick={copyResponse}
                    className="p-2 hover:bg-gray-100 rounded-md"
                    title="复制响应"
                  >
                    {copied ? <Check className="w-4 h-4 text-green-500" /> : <Copy className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              <div className="p-4">
                {responseTab === 'body' && (
                  <pre className="text-sm font-mono whitespace-pre-wrap break-all max-h-96 overflow-auto">
                    {typeof response.body === 'string' 
                      ? response.body 
                      : JSON.stringify(response.body, null, 2)}
                  </pre>
                )}

                {responseTab === 'headers' && (
                  <div className="space-y-2">
                    {Object.entries(response.headers).map(([key, value]) => (
                      <div key={key} className="flex">
                        <span className="font-medium text-gray-700 w-1/3">{key}:</span>
                        <span className="text-gray-600 w-2/3 break-all">{value}</span>
                      </div>
                    ))}
                  </div>
                )}

                {responseTab === 'info' && (
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium mb-1">响应信息</h4>
                      <div className="space-y-1 text-sm">
                        <div>状态码: {response.status} {response.statusText}</div>
                        <div>响应时间: {response.time}ms</div>
                        <div>响应大小: {formatBytes(response.size)}</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="p-8 text-center text-gray-400">
              <Globe className="w-12 h-12 mx-auto mb-3" />
              <p>发送请求后将在此显示响应结果</p>
            </div>
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持 GET、POST、PUT、DELETE 等常用 HTTP 方法</li>
              <li>可自定义请求头和请求体</li>
              <li>自动检测响应类型并格式化显示</li>
              <li>显示响应时间、大小等详细信息</li>
              <li>由于浏览器 CORS 限制，某些跨域请求可能会失败</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
