'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Apple, Calculator, Copy, RotateCcw, Utensils, PieChart } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

interface NutritionNeedsResult {
  calories: number
  protein: number
  carbs: number
  fat: number
  fiber: number
  water: number
}

interface FoodNutritionResult {
  food: string
  weight: number
  nutrition: {
    calories: number
    protein: number
    carbs: number
    fat: number
    fiber: number
    sugar: number
    sodium: number
    calcium: number
    iron: number
    vitaminC: number
  }
}

interface MacroDistributionResult {
  totalCalories: number
  protein: { grams: number; calories: number; percentage: number }
  carbs: { grams: number; calories: number; percentage: number }
  fat: { grams: number; calories: number; percentage: number }
  other: { calories: number; percentage: number }
}

export default function NutritionCalculatorTool() {
  const [activeTab, setActiveTab] = useState('needs')
  
  // 营养需求计算
  const [age, setAge] = useState('')
  const [gender, setGender] = useState<'male' | 'female'>('male')
  const [weight, setWeight] = useState('')
  const [height, setHeight] = useState('')
  const [activityLevel, setActivityLevel] = useState('moderate')
  const [goal, setGoal] = useState('maintain')
  const [nutritionNeedsResult, setNutritionNeedsResult] = useState<NutritionNeedsResult | null>(null)
  
  // 食物营养分析
  const [foodType, setFoodType] = useState('')
  const [foodWeight, setFoodWeight] = useState('')
  const [foodNutritionResult, setFoodNutritionResult] = useState<FoodNutritionResult | null>(null)
  
  // 营养素比例计算
  const [proteinGrams, setProteinGrams] = useState('')
  const [carbsGrams, setCarbsGrams] = useState('')
  const [fatGrams, setFatGrams] = useState('')
  const [alcoholGrams, setAlcoholGrams] = useState('')
  const [macroResult, setMacroResult] = useState<MacroDistributionResult | null>(null)

  const activityLevels = [
    { value: 'sedentary', label: '久坐（很少运动）', multiplier: 1.2 },
    { value: 'light', label: '轻度活动（每周1-3天）', multiplier: 1.375 },
    { value: 'moderate', label: '中度活动（每周3-5天）', multiplier: 1.55 },
    { value: 'active', label: '高度活动（每周6-7天）', multiplier: 1.725 },
    { value: 'very_active', label: '极度活动（每天2次）', multiplier: 1.9 },
  ]

  const goals = [
    { value: 'lose', label: '减重', calorieAdjustment: -500 },
    { value: 'maintain', label: '维持体重', calorieAdjustment: 0 },
    { value: 'gain', label: '增重', calorieAdjustment: 500 },
  ]

  const commonFoods = [
    { name: '白米饭', calories: 130, protein: 2.7, carbs: 28, fat: 0.3, fiber: 0.4, sugar: 0.1, sodium: 1, calcium: 10, iron: 0.8, vitaminC: 0 },
    { name: '鸡胸肉', calories: 165, protein: 31, carbs: 0, fat: 3.6, fiber: 0, sugar: 0, sodium: 74, calcium: 15, iron: 1.04, vitaminC: 0 },
    { name: '鸡蛋', calories: 155, protein: 13, carbs: 1.1, fat: 11, fiber: 0, sugar: 1.1, sodium: 124, calcium: 50, iron: 1.75, vitaminC: 0 },
    { name: '牛奶', calories: 42, protein: 3.4, carbs: 5, fat: 1, fiber: 0, sugar: 5, sodium: 44, calcium: 113, iron: 0.03, vitaminC: 0 },
    { name: '苹果', calories: 52, protein: 0.3, carbs: 14, fat: 0.2, fiber: 2.4, sugar: 10, sodium: 1, calcium: 6, iron: 0.12, vitaminC: 4.6 },
    { name: '香蕉', calories: 89, protein: 1.1, carbs: 23, fat: 0.3, fiber: 2.6, sugar: 12, sodium: 1, calcium: 5, iron: 0.26, vitaminC: 8.7 },
    { name: '胡萝卜', calories: 41, protein: 0.9, carbs: 10, fat: 0.2, fiber: 2.8, sugar: 4.7, sodium: 69, calcium: 33, iron: 0.3, vitaminC: 5.9 },
    { name: '西兰花', calories: 34, protein: 2.8, carbs: 7, fat: 0.4, fiber: 2.6, sugar: 1.5, sodium: 33, calcium: 47, iron: 0.73, vitaminC: 89.2 },
    { name: '三文鱼', calories: 208, protein: 22, carbs: 0, fat: 12, fiber: 0, sugar: 0, sodium: 59, calcium: 12, iron: 0.8, vitaminC: 0 },
    { name: '燕麦', calories: 389, protein: 17, carbs: 66, fat: 7, fiber: 11, sugar: 0.6, sodium: 2, calcium: 54, iron: 4.7, vitaminC: 0 },
  ]

  // 计算营养需求
  const calculateNutritionNeeds = () => {
    if (!age || !weight || !height) {
      toast({
        title: "输入错误",
        description: "请填写所有必要信息",
        variant: "destructive"
      })
      return
    }

    const ageNum = parseInt(age)
    const weightNum = parseFloat(weight)
    const heightNum = parseFloat(height)

    // 计算BMR（基础代谢率）
    let bmr: number
    if (gender === 'male') {
      bmr = 88.362 + (13.397 * weightNum) + (4.799 * heightNum) - (5.677 * ageNum)
    } else {
      bmr = 447.593 + (9.247 * weightNum) + (3.098 * heightNum) - (4.330 * ageNum)
    }

    // 计算TDEE（总日消耗）
    const selectedActivity = activityLevels.find(level => level.value === activityLevel)
    const tdee = bmr * (selectedActivity?.multiplier || 1.55)

    // 根据目标调整卡路里
    const selectedGoal = goals.find(g => g.value === goal)
    const targetCalories = tdee + (selectedGoal?.calorieAdjustment || 0)

    // 计算营养素需求
    const protein = weightNum * (goal === 'gain' ? 2.0 : goal === 'lose' ? 1.8 : 1.6)
    const fat = targetCalories * 0.25 / 9
    const carbs = (targetCalories - protein * 4 - fat * 9) / 4
    const fiber = Math.max(25, weightNum * 0.4)
    const water = weightNum * 35 + (activityLevel === 'very_active' ? 500 : activityLevel === 'active' ? 300 : 0)

    const result: NutritionNeedsResult = {
      calories: Math.round(targetCalories),
      protein: Math.round(protein),
      carbs: Math.round(carbs),
      fat: Math.round(fat),
      fiber: Math.round(fiber),
      water: Math.round(water),
    }

    setNutritionNeedsResult(result)
  }

  // 计算食物营养
  const calculateFoodNutrition = () => {
    if (!foodType || !foodWeight) {
      toast({
        title: "输入错误",
        description: "请选择食物类型并输入重量",
        variant: "destructive"
      })
      return
    }

    const food = commonFoods.find(f => f.name === foodType)
    if (!food) {
      toast({
        title: "错误",
        description: "未找到选择的食物",
        variant: "destructive"
      })
      return
    }

    const weight = parseFloat(foodWeight)
    const multiplier = weight / 100

    const result: FoodNutritionResult = {
      food: food.name,
      weight: weight,
      nutrition: {
        calories: Math.round(food.calories * multiplier),
        protein: Math.round(food.protein * multiplier * 10) / 10,
        carbs: Math.round(food.carbs * multiplier * 10) / 10,
        fat: Math.round(food.fat * multiplier * 10) / 10,
        fiber: Math.round(food.fiber * multiplier * 10) / 10,
        sugar: Math.round(food.sugar * multiplier * 10) / 10,
        sodium: Math.round(food.sodium * multiplier),
        calcium: Math.round(food.calcium * multiplier),
        iron: Math.round(food.iron * multiplier * 100) / 100,
        vitaminC: Math.round(food.vitaminC * multiplier * 10) / 10,
      }
    }

    setFoodNutritionResult(result)
  }

  // 计算营养素比例
  const calculateMacroDistribution = () => {
    if (!proteinGrams && !carbsGrams && !fatGrams) {
      toast({
        title: "输入错误",
        description: "请至少输入一种营养素的量",
        variant: "destructive"
      })
      return
    }

    const protein = parseFloat(proteinGrams || '0')
    const carbs = parseFloat(carbsGrams || '0')
    const fat = parseFloat(fatGrams || '0')
    const alcohol = parseFloat(alcoholGrams || '0')

    const proteinCalories = protein * 4
    const carbsCalories = carbs * 4
    const fatCalories = fat * 9
    const alcoholCalories = alcohol * 7

    const totalCalories = proteinCalories + carbsCalories + fatCalories + alcoholCalories

    if (totalCalories === 0) {
      toast({
        title: "错误",
        description: "总热量不能为零",
        variant: "destructive"
      })
      return
    }

    const result: MacroDistributionResult = {
      totalCalories: Math.round(totalCalories),
      protein: {
        grams: protein,
        calories: Math.round(proteinCalories),
        percentage: Math.round((proteinCalories / totalCalories) * 100)
      },
      carbs: {
        grams: carbs,
        calories: Math.round(carbsCalories),
        percentage: Math.round((carbsCalories / totalCalories) * 100)
      },
      fat: {
        grams: fat,
        calories: Math.round(fatCalories),
        percentage: Math.round((fatCalories / totalCalories) * 100)
      },
      other: {
        calories: Math.round(alcoholCalories),
        percentage: Math.round((alcoholCalories / totalCalories) * 100)
      }
    }

    setMacroResult(result)
  }

  // 复制结果
  const copyResult = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "已复制",
      description: "结果已复制到剪贴板",
    })
  }

  // 清空所有输入
  const clearAll = () => {
    setAge('')
    setWeight('')
    setHeight('')
    setFoodWeight('')
    setProteinGrams('')
    setCarbsGrams('')
    setFatGrams('')
    setAlcoholGrams('')
    setNutritionNeedsResult(null)
    setFoodNutritionResult(null)
    setMacroResult(null)
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Apple className="h-6 w-6" />
              <CardTitle>营养计算器</CardTitle>
            </div>
            <Button variant="outline" size="sm" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-1" />
              清空
            </Button>
          </div>
          <CardDescription>
            计算营养需求、食物营养成分和营养素比例分布
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="needs">营养需求</TabsTrigger>
              <TabsTrigger value="food">食物营养</TabsTrigger>
              <TabsTrigger value="macro">营养素比例</TabsTrigger>
            </TabsList>

            <TabsContent value="needs" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">基本信息</h4>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="age">年龄</Label>
                      <Input
                        id="age"
                        type="number"
                        value={age}
                        onChange={(e) => setAge(e.target.value)}
                        placeholder="25"
                      />
                    </div>
                    <div>
                      <Label>性别</Label>
                      <Select value={gender} onValueChange={(value: 'male' | 'female') => setGender(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">男性</SelectItem>
                          <SelectItem value="female">女性</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="weight">体重 (kg)</Label>
                      <Input
                        id="weight"
                        type="number"
                        value={weight}
                        onChange={(e) => setWeight(e.target.value)}
                        placeholder="70"
                      />
                    </div>
                    <div>
                      <Label htmlFor="height">身高 (cm)</Label>
                      <Input
                        id="height"
                        type="number"
                        value={height}
                        onChange={(e) => setHeight(e.target.value)}
                        placeholder="175"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">目标设定</h4>
                  
                  <div>
                    <Label>活动水平</Label>
                    <Select value={activityLevel} onValueChange={setActivityLevel}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {activityLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>目标</Label>
                    <Select value={goal} onValueChange={setGoal}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {goals.map((g) => (
                          <SelectItem key={g.value} value={g.value}>
                            {g.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Button onClick={calculateNutritionNeeds} className="w-full">
                <Calculator className="h-4 w-4 mr-2" />
                计算营养需求
              </Button>

              {nutritionNeedsResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">每日营养需求</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      <div className="p-4 bg-blue-50 rounded-lg text-center">
                        <div className="text-2xl font-bold text-blue-600">{nutritionNeedsResult.calories}</div>
                        <div className="text-sm text-gray-600">卡路里</div>
                      </div>
                      <div className="p-4 bg-green-50 rounded-lg text-center">
                        <div className="text-2xl font-bold text-green-600">{nutritionNeedsResult.protein}g</div>
                        <div className="text-sm text-gray-600">蛋白质</div>
                      </div>
                      <div className="p-4 bg-yellow-50 rounded-lg text-center">
                        <div className="text-2xl font-bold text-yellow-600">{nutritionNeedsResult.carbs}g</div>
                        <div className="text-sm text-gray-600">碳水化合物</div>
                      </div>
                      <div className="p-4 bg-purple-50 rounded-lg text-center">
                        <div className="text-2xl font-bold text-purple-600">{nutritionNeedsResult.fat}g</div>
                        <div className="text-sm text-gray-600">脂肪</div>
                      </div>
                      <div className="p-4 bg-orange-50 rounded-lg text-center">
                        <div className="text-2xl font-bold text-orange-600">{nutritionNeedsResult.fiber}g</div>
                        <div className="text-sm text-gray-600">纤维</div>
                      </div>
                      <div className="p-4 bg-cyan-50 rounded-lg text-center">
                        <div className="text-2xl font-bold text-cyan-600">{nutritionNeedsResult.water}mL</div>
                        <div className="text-sm text-gray-600">水分</div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResult(`每日营养需求：热量 ${nutritionNeedsResult.calories} 卡，蛋白质 ${nutritionNeedsResult.protein}g，碳水化合物 ${nutritionNeedsResult.carbs}g，脂肪 ${nutritionNeedsResult.fat}g`)}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制结果
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="food" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label>食物类型</Label>
                    <Select value={foodType} onValueChange={setFoodType}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择食物类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {commonFoods.map((food) => (
                          <SelectItem key={food.name} value={food.name}>
                            {food.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="foodWeight">重量 (g)</Label>
                    <Input
                      id="foodWeight"
                      type="number"
                      value={foodWeight}
                      onChange={(e) => setFoodWeight(e.target.value)}
                      placeholder="100"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">说明</h4>
                  <div className="text-sm text-gray-600 space-y-2">
                    <p>• 营养成分基于每100g食物计算</p>
                    <p>• 实际营养含量可能因品种、产地、加工方式等因素有所差异</p>
                    <p>• 建议结合多种食物来源获得全面营养</p>
                  </div>
                </div>
              </div>

              <Button onClick={calculateFoodNutrition} className="w-full">
                <Utensils className="h-4 w-4 mr-2" />
                计算食物营养
              </Button>

              {foodNutritionResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {foodNutritionResult.food} ({foodNutritionResult.weight}g) 营养成分
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                      <div className="p-3 bg-blue-50 rounded-lg text-center">
                        <div className="text-xl font-bold text-blue-600">{foodNutritionResult.nutrition.calories}</div>
                        <div className="text-sm text-gray-600">卡路里</div>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg text-center">
                        <div className="text-xl font-bold text-green-600">{foodNutritionResult.nutrition.protein}g</div>
                        <div className="text-sm text-gray-600">蛋白质</div>
                      </div>
                      <div className="p-3 bg-yellow-50 rounded-lg text-center">
                        <div className="text-xl font-bold text-yellow-600">{foodNutritionResult.nutrition.carbs}g</div>
                        <div className="text-sm text-gray-600">碳水化合物</div>
                      </div>
                      <div className="p-3 bg-purple-50 rounded-lg text-center">
                        <div className="text-xl font-bold text-purple-600">{foodNutritionResult.nutrition.fat}g</div>
                        <div className="text-sm text-gray-600">脂肪</div>
                      </div>
                      <div className="p-3 bg-orange-50 rounded-lg text-center">
                        <div className="text-xl font-bold text-orange-600">{foodNutritionResult.nutrition.fiber}g</div>
                        <div className="text-sm text-gray-600">纤维</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                      <div className="flex justify-between">
                        <span>糖分</span>
                        <span className="font-medium">{foodNutritionResult.nutrition.sugar}g</span>
                      </div>
                      <div className="flex justify-between">
                        <span>钠</span>
                        <span className="font-medium">{foodNutritionResult.nutrition.sodium}mg</span>
                      </div>
                      <div className="flex justify-between">
                        <span>钙</span>
                        <span className="font-medium">{foodNutritionResult.nutrition.calcium}mg</span>
                      </div>
                      <div className="flex justify-between">
                        <span>铁</span>
                        <span className="font-medium">{foodNutritionResult.nutrition.iron}mg</span>
                      </div>
                      <div className="flex justify-between">
                        <span>维生素C</span>
                        <span className="font-medium">{foodNutritionResult.nutrition.vitaminC}mg</span>
                      </div>
                    </div>

                    <div className="flex justify-end mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResult(`${foodNutritionResult.food} (${foodNutritionResult.weight}g)：热量 ${foodNutritionResult.nutrition.calories} 卡，蛋白质 ${foodNutritionResult.nutrition.protein}g，碳水化合物 ${foodNutritionResult.nutrition.carbs}g，脂肪 ${foodNutritionResult.nutrition.fat}g`)}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制结果
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="macro" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">营养素摄入量 (g)</h4>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="protein">蛋白质</Label>
                      <Input
                        id="protein"
                        type="number"
                        value={proteinGrams}
                        onChange={(e) => setProteinGrams(e.target.value)}
                        placeholder="100"
                      />
                    </div>
                    <div>
                      <Label htmlFor="carbs">碳水化合物</Label>
                      <Input
                        id="carbs"
                        type="number"
                        value={carbsGrams}
                        onChange={(e) => setCarbsGrams(e.target.value)}
                        placeholder="200"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="fat">脂肪</Label>
                      <Input
                        id="fat"
                        type="number"
                        value={fatGrams}
                        onChange={(e) => setFatGrams(e.target.value)}
                        placeholder="50"
                      />
                    </div>
                    <div>
                      <Label htmlFor="alcohol">酒精 (可选)</Label>
                      <Input
                        id="alcohol"
                        type="number"
                        value={alcoholGrams}
                        onChange={(e) => setAlcoholGrams(e.target.value)}
                        placeholder="0"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">热量系数</h4>
                  <div className="text-sm text-gray-600 space-y-2">
                    <p>• 蛋白质：4 卡/克</p>
                    <p>• 碳水化合物：4 卡/克</p>
                    <p>• 脂肪：9 卡/克</p>
                    <p>• 酒精：7 卡/克</p>
                  </div>
                </div>
              </div>

              <Button onClick={calculateMacroDistribution} className="w-full">
                <PieChart className="h-4 w-4 mr-2" />
                计算营养素比例
              </Button>

              {macroResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">营养素分布</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">{macroResult.totalCalories}</div>
                      <div className="text-gray-600">总热量 (卡)</div>
                    </div>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">蛋白质</span>
                          <span className="font-bold text-green-600">
                            {macroResult.protein.grams}g ({macroResult.protein.calories}卡) - {macroResult.protein.percentage}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div 
                            className="bg-green-500 h-3 rounded-full" 
                            style={{ width: `${macroResult.protein.percentage}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">碳水化合物</span>
                          <span className="font-bold text-yellow-600">
                            {macroResult.carbs.grams}g ({macroResult.carbs.calories}卡) - {macroResult.carbs.percentage}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div 
                            className="bg-yellow-500 h-3 rounded-full" 
                            style={{ width: `${macroResult.carbs.percentage}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">脂肪</span>
                          <span className="font-bold text-purple-600">
                            {macroResult.fat.grams}g ({macroResult.fat.calories}卡) - {macroResult.fat.percentage}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div 
                            className="bg-purple-500 h-3 rounded-full" 
                            style={{ width: `${macroResult.fat.percentage}%` }}
                          ></div>
                        </div>
                      </div>

                      {macroResult.other.calories > 0 && (
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">酒精</span>
                            <span className="font-bold text-red-600">
                              {macroResult.other.calories}卡 - {macroResult.other.percentage}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div 
                              className="bg-red-500 h-3 rounded-full" 
                              style={{ width: `${macroResult.other.percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResult(`营养素分布：总热量 ${macroResult.totalCalories}卡，蛋白质 ${macroResult.protein.percentage}%，碳水化合物 ${macroResult.carbs.percentage}%，脂肪 ${macroResult.fat.percentage}%`)}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制结果
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
