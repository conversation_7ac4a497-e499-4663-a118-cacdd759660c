'use client'

import { useState, useEffect, useCallback } from 'react'
import { Globe, MapPin, Wifi, Server, Copy, Check, Loader2, Info } from 'lucide-react'

interface IPInfo {
  ip: string
  country: string
  countryCode: string
  region: string
  city: string
  latitude: number
  longitude: number
  timezone: string
  isp: string
  org: string
  as: string
}

interface LocalIPInfo {
  ipv4: string
  ipv6?: string
  userAgent: string
  language: string
  platform: string
  screenResolution: string
}

export default function IPLookupTool() {
  const [queryIP, setQueryIP] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [ipInfo, setIpInfo] = useState<IPInfo | null>(null)
  const [localInfo, setLocalInfo] = useState<LocalIPInfo | null>(null)
  const [myIP, setMyIP] = useState('')
  const [copied, setCopied] = useState<string>('')

  // 查询IP信息
  const lookupIP = useCallback(async (ip?: string) => {
    const targetIP = ip || queryIP
    
    if (!targetIP) {
      setError('请输入要查询的IP地址')
      return
    }

    // 简单的IP格式验证
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/
    if (!ipRegex.test(targetIP)) {
      setError('请输入有效的IPv4地址')
      return
    }

    setLoading(true)
    setError('')

    try {
      // 使用免费的IP查询API
      const response = await fetch(`https://ipapi.co/${targetIP}/json/`)
      
      if (!response.ok) {
        throw new Error('查询失败')
      }

      const data = await response.json()
      
      if (data.error) {
        throw new Error(data.reason || '查询失败')
      }

      const info: IPInfo = {
        ip: data.ip,
        country: data.country_name || '未知',
        countryCode: data.country_code || '',
        region: data.region || '未知',
        city: data.city || '未知',
        latitude: data.latitude || 0,
        longitude: data.longitude || 0,
        timezone: data.timezone || '未知',
        isp: data.org || '未知',
        org: data.org || '未知',
        as: data.asn || '未知',
      }

      setIpInfo(info)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'IP查询失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }, [queryIP])

  // 获取我的IP
  const fetchMyIP = useCallback(async () => {
    try {
      const response = await fetch('https://api.ipify.org?format=json')
      const data = await response.json()
      setMyIP(data.ip)
      setQueryIP(data.ip)
      if (localInfo) {
        setLocalInfo({ ...localInfo, ipv4: data.ip })
      }
      // 自动查询用户自己的IP信息
      lookupIP(data.ip)
    } catch (err) {
      console.error('获取IP失败:', err)
    }
  }, [localInfo, lookupIP])

  // 获取本地信息
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const info: LocalIPInfo = {
        ipv4: '获取中...',
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        screenResolution: `${screen.width} × ${screen.height}`,
      }
      setLocalInfo(info)
    }

    // 获取用户的公网IP
    fetchMyIP()
  }, [fetchMyIP])

  // 复制功能
  const copyToClipboard = (text: string, field: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(field)
      setTimeout(() => setCopied(''), 2000)
    })
  }

  // 格式化位置信息
  const formatLocation = (info: IPInfo) => {
    const parts = []
    if (info.city && info.city !== '未知') parts.push(info.city)
    if (info.region && info.region !== '未知') parts.push(info.region)
    if (info.country && info.country !== '未知') parts.push(info.country)
    return parts.join(', ') || '未知'
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">IP地址查询</h2>
        <p className="text-gray-600">
          查询IP地址的地理位置、运营商等详细信息
        </p>
      </div>

      {/* 查询输入框 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex gap-3">
          <input
            type="text"
            value={queryIP}
            onChange={(e) => setQueryIP(e.target.value)}
            placeholder="输入要查询的IP地址"
            className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                lookupIP()
              }
            }}
          />
          <button
            onClick={() => lookupIP()}
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                查询中
              </>
            ) : (
              <>
                <Globe className="w-4 h-4 mr-2" />
                查询
              </>
            )}
          </button>
          <button
            onClick={() => {
              setQueryIP(myIP)
              lookupIP(myIP)
            }}
            className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            我的IP
          </button>
        </div>
        {error && (
          <p className="mt-2 text-sm text-red-600">{error}</p>
        )}
      </div>

      {/* 查询结果 */}
      {ipInfo && (
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-medium mb-4 flex items-center">
            <Server className="w-5 h-5 mr-2" />
            IP信息详情
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">IP地址：</span>
                <div className="flex items-center gap-2">
                  <span className="font-mono">{ipInfo.ip}</span>
                  <button
                    onClick={() => copyToClipboard(ipInfo.ip, 'ip')}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    {copied === 'ip' ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">位置：</span>
                <span className="text-right">{formatLocation(ipInfo)}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">时区：</span>
                <span>{ipInfo.timezone}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">坐标：</span>
                <span className="font-mono text-sm">
                  {ipInfo.latitude.toFixed(4)}, {ipInfo.longitude.toFixed(4)}
                </span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">国家代码：</span>
                <span>{ipInfo.countryCode}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">运营商：</span>
                <span className="text-right text-sm">{ipInfo.isp}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">组织：</span>
                <span className="text-right text-sm">{ipInfo.org}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-600">AS号：</span>
                <span>{ipInfo.as}</span>
              </div>
            </div>
          </div>

          {/* 地图链接 */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <a
              href={`https://www.google.com/maps?q=${ipInfo.latitude},${ipInfo.longitude}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-blue-600 hover:text-blue-700"
            >
              <MapPin className="w-4 h-4 mr-1" />
              在地图上查看位置
            </a>
          </div>
        </div>
      )}

      {/* 本地信息 */}
      {localInfo && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium mb-4 flex items-center">
            <Wifi className="w-5 h-5 mr-2" />
            本地环境信息
          </h3>
          
          <div className="space-y-2">
            <div className="flex items-start">
              <span className="text-gray-600 w-32 flex-shrink-0">公网IP：</span>
              <span className="font-mono break-all">{localInfo.ipv4}</span>
            </div>
            
            <div className="flex items-start">
              <span className="text-gray-600 w-32 flex-shrink-0">浏览器语言：</span>
              <span>{localInfo.language}</span>
            </div>
            
            <div className="flex items-start">
              <span className="text-gray-600 w-32 flex-shrink-0">操作系统：</span>
              <span>{localInfo.platform}</span>
            </div>
            
            <div className="flex items-start">
              <span className="text-gray-600 w-32 flex-shrink-0">屏幕分辨率：</span>
              <span>{localInfo.screenResolution}</span>
            </div>
            
            <div className="flex items-start">
              <span className="text-gray-600 w-32 flex-shrink-0">用户代理：</span>
              <span className="text-sm break-all">{localInfo.userAgent}</span>
            </div>
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持查询任意公网IPv4地址的详细信息</li>
              <li>自动显示您当前的公网IP地址和本地环境信息</li>
              <li>查询结果包括地理位置、运营商、时区等信息</li>
              <li>点击&ldquo;在地图上查看位置&rdquo;可以在Google地图上查看IP所在位置</li>
              <li>查询数据来自公开的IP地理位置数据库，仅供参考</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
