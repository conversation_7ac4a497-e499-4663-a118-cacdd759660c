'use client'

import React, { useState, useC<PERSON>back, useMemo, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Copy, Play, Pause, RotateCcw, Settings, Eye, Code, Sparkles, Plus, Trash2, Move, RotateCw } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface Keyframe {
  id: string
  percentage: number
  transforms: {
    translateX: number
    translateY: number
    rotate: number
    scale: number
    opacity: number
  }
  backgroundColor: string
  borderRadius: number
}

interface AnimationConfig {
  name: string
  duration: number
  timingFunction: string
  delay: number
  iterationCount: string
  direction: string
  fillMode: string
  playState: 'running' | 'paused'
}

interface AnimationPreset {
  name: string
  description: string
  config: AnimationConfig
  keyframes: Omit<Keyframe, 'id'>[]
}

const timingFunctions = [
  { value: 'linear', label: '线性 (linear)' },
  { value: 'ease', label: '缓动 (ease)' },
  { value: 'ease-in', label: '缓入 (ease-in)' },
  { value: 'ease-out', label: '缓出 (ease-out)' },
  { value: 'ease-in-out', label: '缓入缓出 (ease-in-out)' },
  { value: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', label: '自定义贝塞尔曲线' }
]

const animationPresets: AnimationPreset[] = [
  {
    name: '淡入动画',
    description: '元素从透明到不透明',
    config: {
      name: 'fadeIn',
      duration: 1,
      timingFunction: 'ease-in',
      delay: 0,
      iterationCount: '1',
      direction: 'normal',
      fillMode: 'forwards',
      playState: 'running'
    },
    keyframes: [
      { percentage: 0, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 0 }, backgroundColor: '#3b82f6', borderRadius: 8 },
      { percentage: 100, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#3b82f6', borderRadius: 8 }
    ]
  },
  {
    name: '弹跳动画',
    description: '元素弹跳效果',
    config: {
      name: 'bounce',
      duration: 2,
      timingFunction: 'ease',
      delay: 0,
      iterationCount: 'infinite',
      direction: 'normal',
      fillMode: 'none',
      playState: 'running'
    },
    keyframes: [
      { percentage: 0, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#10b981', borderRadius: 8 },
      { percentage: 20, transforms: { translateX: 0, translateY: -30, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#10b981', borderRadius: 8 },
      { percentage: 40, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#10b981', borderRadius: 8 },
      { percentage: 60, transforms: { translateX: 0, translateY: -15, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#10b981', borderRadius: 8 },
      { percentage: 80, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#10b981', borderRadius: 8 },
      { percentage: 100, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#10b981', borderRadius: 8 }
    ]
  },
  {
    name: '旋转缩放',
    description: '旋转并放大效果',
    config: {
      name: 'rotateScale',
      duration: 1.5,
      timingFunction: 'ease-in-out',
      delay: 0,
      iterationCount: 'infinite',
      direction: 'alternate',
      fillMode: 'none',
      playState: 'running'
    },
    keyframes: [
      { percentage: 0, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#f59e0b', borderRadius: 8 },
      { percentage: 50, transforms: { translateX: 0, translateY: 0, rotate: 180, scale: 1.2, opacity: 0.8 }, backgroundColor: '#ef4444', borderRadius: 50 },
      { percentage: 100, transforms: { translateX: 0, translateY: 0, rotate: 360, scale: 1, opacity: 1 }, backgroundColor: '#8b5cf6', borderRadius: 8 }
    ]
  },
  {
    name: '滑入动画',
    description: '从左侧滑入',
    config: {
      name: 'slideInLeft',
      duration: 0.8,
      timingFunction: 'ease-out',
      delay: 0,
      iterationCount: '1',
      direction: 'normal',
      fillMode: 'forwards',
      playState: 'running'
    },
    keyframes: [
      { percentage: 0, transforms: { translateX: -100, translateY: 0, rotate: 0, scale: 1, opacity: 0 }, backgroundColor: '#ec4899', borderRadius: 8 },
      { percentage: 100, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#ec4899', borderRadius: 8 }
    ]
  },
  {
    name: '心跳动画',
    description: '心跳般的缩放效果',
    config: {
      name: 'heartbeat',
      duration: 1.2,
      timingFunction: 'ease-in-out',
      delay: 0,
      iterationCount: 'infinite',
      direction: 'normal',
      fillMode: 'none',
      playState: 'running'
    },
    keyframes: [
      { percentage: 0, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#ef4444', borderRadius: 50 },
      { percentage: 14, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1.3, opacity: 1 }, backgroundColor: '#ef4444', borderRadius: 50 },
      { percentage: 28, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#ef4444', borderRadius: 50 },
      { percentage: 42, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1.3, opacity: 1 }, backgroundColor: '#ef4444', borderRadius: 50 },
      { percentage: 70, transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 }, backgroundColor: '#ef4444', borderRadius: 50 }
    ]
  }
]

export default function CssAnimationGeneratorTool() {
  const [config, setConfig] = useState<AnimationConfig>({
    name: 'customAnimation',
    duration: 2,
    timingFunction: 'ease-in-out',
    delay: 0,
    iterationCount: 'infinite',
    direction: 'normal',
    fillMode: 'none',
    playState: 'running'
  })

  const [keyframes, setKeyframes] = useState<Keyframe[]>([
    {
      id: '1',
      percentage: 0,
      transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 },
      backgroundColor: '#3b82f6',
      borderRadius: 8
    },
    {
      id: '2',
      percentage: 100,
      transforms: { translateX: 100, translateY: 0, rotate: 360, scale: 1.2, opacity: 1 },
      backgroundColor: '#ef4444',
      borderRadius: 50
    }
  ])

  const [selectedKeyframe, setSelectedKeyframe] = useState('1')
  const [isPlaying, setIsPlaying] = useState(true)
  const [activeTab, setActiveTab] = useState('keyframes')
  const animationRef = useRef<HTMLDivElement>(null)

  const updateConfig = useCallback((updates: Partial<AnimationConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }))
  }, [])

  const updateKeyframe = useCallback((id: string, updates: Partial<Keyframe>) => {
    setKeyframes(prev => prev.map(kf => 
      kf.id === id ? { ...kf, ...updates } : kf
    ))
  }, [])

  const addKeyframe = useCallback(() => {
    const newId = Date.now().toString()
    const newPercentage = Math.round((keyframes.length * 100) / (keyframes.length + 1))
    
    const newKeyframe: Keyframe = {
      id: newId,
      percentage: newPercentage,
      transforms: { translateX: 0, translateY: 0, rotate: 0, scale: 1, opacity: 1 },
      backgroundColor: '#3b82f6',
      borderRadius: 8
    }
    
    setKeyframes(prev => [...prev, newKeyframe].sort((a, b) => a.percentage - b.percentage))
    setSelectedKeyframe(newId)
  }, [keyframes.length])

  const removeKeyframe = useCallback((id: string) => {
    if (keyframes.length <= 2) {
      toast.error('至少需要保留两个关键帧')
      return
    }
    
    setKeyframes(prev => prev.filter(kf => kf.id !== id))
    
    if (selectedKeyframe === id) {
      setSelectedKeyframe(keyframes.find(kf => kf.id !== id)?.id || '1')
    }
  }, [keyframes.length, selectedKeyframe, keyframes])

  const generateKeyframesCSS = useMemo(() => {
    return keyframes.map(kf => {
      const transform = `translate(${kf.transforms.translateX}px, ${kf.transforms.translateY}px) rotate(${kf.transforms.rotate}deg) scale(${kf.transforms.scale})`
      
      return `  ${kf.percentage}% {
    transform: ${transform};
    opacity: ${kf.transforms.opacity};
    background-color: ${kf.backgroundColor};
    border-radius: ${kf.borderRadius}px;
  }`
    }).join('\n')
  }, [keyframes])

  const generateAnimationCSS = useMemo(() => {
    const keyframesCSS = `@keyframes ${config.name} {\n${generateKeyframesCSS}\n}`
    
    const animationProperty = `animation: ${config.name} ${config.duration}s ${config.timingFunction} ${config.delay}s ${config.iterationCount} ${config.direction} ${config.fillMode} ${config.playState};`
    
    return { keyframesCSS, animationProperty }
  }, [config, generateKeyframesCSS])

  const togglePlayState = useCallback(() => {
    const newState = isPlaying ? 'paused' : 'running'
    setIsPlaying(!isPlaying)
    updateConfig({ playState: newState })
  }, [isPlaying, updateConfig])

  const restartAnimation = useCallback(() => {
    if (animationRef.current) {
      animationRef.current.style.animation = 'none'
      setTimeout(() => {
        if (animationRef.current) {
          animationRef.current.style.animation = `${config.name} ${config.duration}s ${config.timingFunction} ${config.delay}s ${config.iterationCount} ${config.direction} ${config.fillMode} ${config.playState}`
        }
      }, 10)
    }
  }, [config])

  const applyPreset = useCallback((preset: AnimationPreset) => {
    setConfig(preset.config)
    const newKeyframes = preset.keyframes.map((kf, index) => ({
      ...kf,
      id: (index + 1).toString()
    }))
    setKeyframes(newKeyframes)
    setSelectedKeyframe('1')
    setIsPlaying(preset.config.playState === 'running')
    toast.success(`已应用预设: ${preset.name}`)
  }, [])

  const copyCSS = useCallback(() => {
    const fullCSS = `${generateAnimationCSS.keyframesCSS}\n\n.animated-element {\n  ${generateAnimationCSS.animationProperty}\n}`
    navigator.clipboard.writeText(fullCSS)
    toast.success('CSS代码已复制到剪贴板')
  }, [generateAnimationCSS])

  const copyKeyframesOnly = useCallback(() => {
    navigator.clipboard.writeText(generateAnimationCSS.keyframesCSS)
    toast.success('关键帧CSS已复制到剪贴板')
  }, [generateAnimationCSS])

  const copyAnimationProperty = useCallback(() => {
    navigator.clipboard.writeText(generateAnimationCSS.animationProperty)
    toast.success('动画属性已复制到剪贴板')
  }, [generateAnimationCSS])

  const selectedKeyframeData = keyframes.find(kf => kf.id === selectedKeyframe)

  // 动态生成CSS样式
  useEffect(() => {
    const style = document.createElement('style')
    style.textContent = generateAnimationCSS.keyframesCSS
    document.head.appendChild(style)
    
    return () => {
      document.head.removeChild(style)
    }
  }, [generateAnimationCSS.keyframesCSS])

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          CSS动画生成器
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          可视化关键帧动画编辑器，支持多重变换、时间轴控制和预设库
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 预览区域 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                动画预览
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={togglePlayState}
                >
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={restartAnimation}
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 动画元素预览 */}
            <div className="relative h-64 bg-gray-50 dark:bg-gray-800 rounded-lg overflow-hidden">
              <div
                ref={animationRef}
                className="absolute w-16 h-16 transition-all"
                style={{
                  top: '50%',
                  left: '50px',
                  marginTop: '-32px',
                  animation: `${config.name} ${config.duration}s ${config.timingFunction} ${config.delay}s ${config.iterationCount} ${config.direction} ${config.fillMode} ${config.playState}`,
                  backgroundColor: keyframes[0]?.backgroundColor || '#3b82f6',
                  borderRadius: `${keyframes[0]?.borderRadius || 8}px`
                }}
              />
              
              {/* 路径轨迹 */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none opacity-30">
                <polyline
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="2"
                  strokeDasharray="5,5"
                  points={keyframes.map(kf => {
                    const x = 50 + 32 + kf.transforms.translateX
                    const y = 128 + kf.transforms.translateY
                    return `${x},${y}`
                  }).join(' ')}
                />
                {keyframes.map((kf, index) => (
                  <circle
                    key={kf.id}
                    cx={50 + 32 + kf.transforms.translateX}
                    cy={128 + kf.transforms.translateY}
                    r="4"
                    fill="#3b82f6"
                    className="cursor-pointer hover:r-6 transition-all"
                    onClick={() => setSelectedKeyframe(kf.id)}
                  />
                ))}
              </svg>
            </div>

            {/* 时间轴 */}
            <div className="space-y-2">
              <Label>时间轴</Label>
              <div className="relative h-12 bg-gray-100 dark:bg-gray-700 rounded-lg">
                {keyframes.map(kf => (
                  <div
                    key={kf.id}
                    className={`absolute top-1 w-2 h-10 rounded cursor-pointer transition-colors ${
                      selectedKeyframe === kf.id ? 'bg-blue-500' : 'bg-gray-400 hover:bg-gray-500'
                    }`}
                    style={{ left: `${kf.percentage}%`, marginLeft: '-4px' }}
                    onClick={() => setSelectedKeyframe(kf.id)}
                  >
                    <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 whitespace-nowrap">
                      {kf.percentage}%
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 播放控制 */}
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="text-gray-600">时长</div>
                <div className="font-mono">{config.duration}s</div>
              </div>
              <div className="text-center">
                <div className="text-gray-600">延迟</div>
                <div className="font-mono">{config.delay}s</div>
              </div>
              <div className="text-center">
                <div className="text-gray-600">状态</div>
                <Badge variant={isPlaying ? "default" : "secondary"}>
                  {isPlaying ? '播放中' : '已暂停'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 编辑面板 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              动画编辑
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="keyframes">关键帧</TabsTrigger>
                <TabsTrigger value="animation">动画属性</TabsTrigger>
                <TabsTrigger value="presets">预设</TabsTrigger>
              </TabsList>

              <TabsContent value="keyframes" className="space-y-4">
                {/* 关键帧列表 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-semibold">关键帧列表</Label>
                    <Button onClick={addKeyframe} size="sm">
                      <Plus className="h-4 w-4 mr-1" />
                      添加关键帧
                    </Button>
                  </div>
                  
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {keyframes.map(kf => (
                      <div
                        key={kf.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedKeyframe === kf.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedKeyframe(kf.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div
                              className="w-4 h-4 rounded"
                              style={{ backgroundColor: kf.backgroundColor }}
                            />
                            <span className="font-medium">{kf.percentage}%</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                removeKeyframe(kf.id)
                              }}
                              disabled={keyframes.length <= 2}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 选中关键帧的编辑 */}
                {selectedKeyframeData && (
                  <div className="space-y-4 pt-4 border-t">
                    <Label className="text-base font-semibold">
                      编辑关键帧 {selectedKeyframeData.percentage}%
                    </Label>
                    
                    {/* 时间位置 */}
                    <div className="space-y-2">
                      <Label>时间位置: {selectedKeyframeData.percentage}%</Label>
                      <Slider
                        value={[selectedKeyframeData.percentage]}
                        onValueChange={([value]) => updateKeyframe(selectedKeyframe, { percentage: value })}
                        min={0}
                        max={100}
                        step={1}
                      />
                    </div>

                    {/* 变换属性 */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>X位移: {selectedKeyframeData.transforms.translateX}px</Label>
                        <Slider
                          value={[selectedKeyframeData.transforms.translateX]}
                          onValueChange={([value]) => updateKeyframe(selectedKeyframe, { 
                            transforms: { ...selectedKeyframeData.transforms, translateX: value }
                          })}
                          min={-200}
                          max={200}
                          step={1}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Y位移: {selectedKeyframeData.transforms.translateY}px</Label>
                        <Slider
                          value={[selectedKeyframeData.transforms.translateY]}
                          onValueChange={([value]) => updateKeyframe(selectedKeyframe, { 
                            transforms: { ...selectedKeyframeData.transforms, translateY: value }
                          })}
                          min={-100}
                          max={100}
                          step={1}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>旋转: {selectedKeyframeData.transforms.rotate}°</Label>
                        <Slider
                          value={[selectedKeyframeData.transforms.rotate]}
                          onValueChange={([value]) => updateKeyframe(selectedKeyframe, { 
                            transforms: { ...selectedKeyframeData.transforms, rotate: value }
                          })}
                          min={0}
                          max={360}
                          step={1}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>缩放: {selectedKeyframeData.transforms.scale}</Label>
                        <Slider
                          value={[selectedKeyframeData.transforms.scale]}
                          onValueChange={([value]) => updateKeyframe(selectedKeyframe, { 
                            transforms: { ...selectedKeyframeData.transforms, scale: value }
                          })}
                          min={0.1}
                          max={3}
                          step={0.1}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>透明度: {selectedKeyframeData.transforms.opacity}</Label>
                      <Slider
                        value={[selectedKeyframeData.transforms.opacity]}
                        onValueChange={([value]) => updateKeyframe(selectedKeyframe, { 
                          transforms: { ...selectedKeyframeData.transforms, opacity: value }
                        })}
                        min={0}
                        max={1}
                        step={0.01}
                      />
                    </div>

                    {/* 样式属性 */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>背景色</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            type="color"
                            value={selectedKeyframeData.backgroundColor}
                            onChange={(e) => updateKeyframe(selectedKeyframe, { backgroundColor: e.target.value })}
                            className="w-12 h-8 p-0 border-0"
                          />
                          <Input
                            value={selectedKeyframeData.backgroundColor}
                            onChange={(e) => updateKeyframe(selectedKeyframe, { backgroundColor: e.target.value })}
                            className="flex-1 text-sm"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>圆角: {selectedKeyframeData.borderRadius}px</Label>
                        <Slider
                          value={[selectedKeyframeData.borderRadius]}
                          onValueChange={([value]) => updateKeyframe(selectedKeyframe, { borderRadius: value })}
                          min={0}
                          max={50}
                          step={1}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="animation" className="space-y-4">
                {/* 动画名称 */}
                <div className="space-y-2">
                  <Label>动画名称</Label>
                  <Input
                    value={config.name}
                    onChange={(e) => updateConfig({ name: e.target.value })}
                    placeholder="输入动画名称"
                  />
                </div>

                {/* 时长和延迟 */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>动画时长: {config.duration}s</Label>
                    <Slider
                      value={[config.duration]}
                      onValueChange={([value]) => updateConfig({ duration: value })}
                      min={0.1}
                      max={10}
                      step={0.1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>延迟时间: {config.delay}s</Label>
                    <Slider
                      value={[config.delay]}
                      onValueChange={([value]) => updateConfig({ delay: value })}
                      min={0}
                      max={5}
                      step={0.1}
                    />
                  </div>
                </div>

                {/* 时间函数 */}
                <div className="space-y-2">
                  <Label>时间函数</Label>
                  <Select value={config.timingFunction} onValueChange={(value) => updateConfig({ timingFunction: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {timingFunctions.map(func => (
                        <SelectItem key={func.value} value={func.value}>
                          {func.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 其他属性 */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>重复次数</Label>
                    <Select value={config.iterationCount} onValueChange={(value) => updateConfig({ iterationCount: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1次</SelectItem>
                        <SelectItem value="2">2次</SelectItem>
                        <SelectItem value="3">3次</SelectItem>
                        <SelectItem value="5">5次</SelectItem>
                        <SelectItem value="infinite">无限</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>播放方向</Label>
                    <Select value={config.direction} onValueChange={(value) => updateConfig({ direction: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="normal">正常</SelectItem>
                        <SelectItem value="reverse">反向</SelectItem>
                        <SelectItem value="alternate">交替</SelectItem>
                        <SelectItem value="alternate-reverse">反向交替</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>填充模式</Label>
                  <Select value={config.fillMode} onValueChange={(value) => updateConfig({ fillMode: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">无</SelectItem>
                      <SelectItem value="forwards">向前</SelectItem>
                      <SelectItem value="backwards">向后</SelectItem>
                      <SelectItem value="both">双向</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>

              <TabsContent value="presets" className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  {animationPresets.map((preset, index) => (
                    <div
                      key={index}
                      className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-gray-300 dark:hover:border-gray-600 cursor-pointer transition-colors"
                      onClick={() => applyPreset(preset)}
                    >
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-gray-900 dark:text-gray-100">
                            {preset.name}
                          </h3>
                          <Badge variant="outline">
                            {preset.keyframes.length} 帧
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500">
                          {preset.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-400">
                          <span>时长: {preset.config.duration}s</span>
                          <span>重复: {preset.config.iterationCount}</span>
                          <span>函数: {preset.config.timingFunction}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* 代码导出 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            生成的CSS代码
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Tabs defaultValue="full" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="full">完整代码</TabsTrigger>
              <TabsTrigger value="keyframes">关键帧</TabsTrigger>
              <TabsTrigger value="property">动画属性</TabsTrigger>
            </TabsList>
            
            <TabsContent value="full" className="space-y-3">
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <pre className="text-sm overflow-x-auto">
                  <code>
{`${generateAnimationCSS.keyframesCSS}

.animated-element {
  ${generateAnimationCSS.animationProperty}
}`}
                  </code>
                </pre>
              </div>
              <Button onClick={copyCSS} className="w-full">
                <Copy className="h-4 w-4 mr-2" />
                复制完整CSS代码
              </Button>
            </TabsContent>
            
            <TabsContent value="keyframes" className="space-y-3">
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <pre className="text-sm overflow-x-auto">
                  <code>{generateAnimationCSS.keyframesCSS}</code>
                </pre>
              </div>
              <Button onClick={copyKeyframesOnly} className="w-full">
                <Copy className="h-4 w-4 mr-2" />
                复制关键帧CSS
              </Button>
            </TabsContent>
            
            <TabsContent value="property" className="space-y-3">
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <pre className="text-sm overflow-x-auto">
                  <code>{generateAnimationCSS.animationProperty}</code>
                </pre>
              </div>
              <Button onClick={copyAnimationProperty} className="w-full">
                <Copy className="h-4 w-4 mr-2" />
                复制动画属性
              </Button>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}