'use client'

import { useState, useRef, useEffect } from 'react'
import { Table, Download, Upload, Plus, Trash2, Copy, FileSpreadsheet, Edit3, Check, X, ArrowUpDown, Filter, Calculator, Info } from 'lucide-react'

interface Cell {
  value: string
  formula?: string
}

interface SpreadsheetData {
  [key: string]: Cell
}

interface ColumnStats {
  count: number
  sum: number
  avg: number
  min: number
  max: number
}

export default function SpreadsheetTool() {
  const [data, setData] = useState<SpreadsheetData>({})
  const [selectedCell, setSelectedCell] = useState<string | null>(null)
  const [editingCell, setEditingCell] = useState<string | null>(null)
  const [editValue, setEditValue] = useState('')
  const [rows, setRows] = useState(10)
  const [cols, setCols] = useState(10)
  const [selectedRange, setSelectedRange] = useState<string[]>([])
  const [sortColumn, setSortColumn] = useState<number | null>(null)
  const [sortAsc, setSortAsc] = useState(true)
  const [filterColumn, setFilterColumn] = useState<number | null>(null)
  const [filterValue, setFilterValue] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)

  // 列标签（A, B, C, ..., Z, AA, AB, ...）
  const getColumnLabel = (index: number): string => {
    let label = ''
    while (index >= 0) {
      label = String.fromCharCode(65 + (index % 26)) + label
      index = Math.floor(index / 26) - 1
    }
    return label
  }

  // 获取单元格标识
  const getCellId = (row: number, col: number): string => {
    return `${getColumnLabel(col)}${row + 1}`
  }

  // 解析单元格引用
  const parseCellRef = (ref: string): { row: number; col: number } | null => {
    const match = ref.match(/^([A-Z]+)(\d+)$/)
    if (!match) return null
    
    const colStr = match[1]
    const rowStr = match[2]
    
    let col = 0
    for (let i = 0; i < colStr.length; i++) {
      col = col * 26 + (colStr.charCodeAt(i) - 64)
    }
    col--
    
    const row = parseInt(rowStr) - 1
    
    if (row < 0 || row >= rows || col < 0 || col >= cols) return null
    
    return { row, col }
  }

  // 计算公式
  const evaluateFormula = (formula: string): string => {
    if (!formula.startsWith('=')) return formula

    try {
      let expression = formula.substring(1)
      
      // 替换单元格引用
      const cellRefPattern = /[A-Z]+\d+/g
      expression = expression.replace(cellRefPattern, (match) => {
        const ref = parseCellRef(match)
        if (!ref) return '0'
        const cellId = getCellId(ref.row, ref.col)
        const cell = data[cellId]
        if (!cell) return '0'
        return cell.formula ? evaluateFormula(cell.formula) : cell.value || '0'
      })

      // 支持的函数
      expression = expression.replace(/SUM\(([^)]+)\)/gi, (match, args) => {
        const values = args.split(',').map((arg: string) => {
          const trimmed = arg.trim()
          if (trimmed.includes(':')) {
            // 范围求和 (A1:B2)
            const [start, end] = trimmed.split(':')
            const startRef = parseCellRef(start)
            const endRef = parseCellRef(end)
            if (!startRef || !endRef) return 0
            
            let sum = 0
            for (let r = startRef.row; r <= endRef.row; r++) {
              for (let c = startRef.col; c <= endRef.col; c++) {
                const cellId = getCellId(r, c)
                const cell = data[cellId]
                if (cell) {
                  const value = cell.formula ? evaluateFormula(cell.formula) : cell.value
                  sum += parseFloat(value) || 0
                }
              }
            }
            return sum
          } else {
            return parseFloat(trimmed) || 0
          }
        })
        return values.reduce((a: number, b: number) => a + b, 0).toString()
      })

      expression = expression.replace(/AVG\(([^)]+)\)/gi, (match, args) => {
        const values: number[] = []
        args.split(',').forEach((arg: string) => {
          const trimmed = arg.trim()
          if (trimmed.includes(':')) {
            const [start, end] = trimmed.split(':')
            const startRef = parseCellRef(start)
            const endRef = parseCellRef(end)
            if (!startRef || !endRef) return
            
            for (let r = startRef.row; r <= endRef.row; r++) {
              for (let c = startRef.col; c <= endRef.col; c++) {
                const cellId = getCellId(r, c)
                const cell = data[cellId]
                if (cell) {
                  const value = cell.formula ? evaluateFormula(cell.formula) : cell.value
                  const num = parseFloat(value)
                  if (!isNaN(num)) values.push(num)
                }
              }
            }
          } else {
            const num = parseFloat(trimmed)
            if (!isNaN(num)) values.push(num)
          }
        })
        return values.length > 0 ? (values.reduce((a, b) => a + b, 0) / values.length).toString() : '0'
      })

      // 安全评估数学表达式
      const result = Function('"use strict"; return (' + expression + ')')()
      return result.toString()
    } catch (error) {
      return '#ERROR'
    }
  }

  // 获取单元格显示值
  const getCellDisplay = (cellId: string): string => {
    const cell = data[cellId]
    if (!cell) return ''
    if (cell.formula) {
      return evaluateFormula(cell.formula)
    }
    return cell.value
  }

  // 编辑单元格
  const startEditing = (cellId: string) => {
    setEditingCell(cellId)
    const cell = data[cellId]
    setEditValue(cell?.formula || cell?.value || '')
    setTimeout(() => inputRef.current?.focus(), 0)
  }

  // 保存编辑
  const saveEdit = () => {
    if (!editingCell) return

    const newData = { ...data }
    if (editValue.trim()) {
      newData[editingCell] = {
        value: editValue.startsWith('=') ? '' : editValue,
        formula: editValue.startsWith('=') ? editValue : undefined
      }
    } else {
      delete newData[editingCell]
    }
    
    setData(newData)
    setEditingCell(null)
    setEditValue('')
  }

  // 取消编辑
  const cancelEdit = () => {
    setEditingCell(null)
    setEditValue('')
  }

  // 键盘导航
  const handleKeyDown = (e: React.KeyboardEvent, row: number, col: number) => {
    if (editingCell) {
      if (e.key === 'Enter') {
        saveEdit()
        if (row < rows - 1) {
          setSelectedCell(getCellId(row + 1, col))
        }
      } else if (e.key === 'Escape') {
        cancelEdit()
      }
      return
    }

    const cellId = getCellId(row, col)
    
    switch (e.key) {
      case 'Enter':
        startEditing(cellId)
        break
      case 'Delete':
        const newData = { ...data }
        delete newData[cellId]
        setData(newData)
        break
      case 'ArrowUp':
        if (row > 0) setSelectedCell(getCellId(row - 1, col))
        break
      case 'ArrowDown':
        if (row < rows - 1) setSelectedCell(getCellId(row + 1, col))
        break
      case 'ArrowLeft':
        if (col > 0) setSelectedCell(getCellId(row, col - 1))
        break
      case 'ArrowRight':
        if (col < cols - 1) setSelectedCell(getCellId(row, col + 1))
        break
    }
  }

  // 导出为CSV
  const exportCSV = () => {
    const csvRows = []
    
    for (let r = 0; r < rows; r++) {
      const csvRow = []
      for (let c = 0; c < cols; c++) {
        const cellId = getCellId(r, c)
        const value = getCellDisplay(cellId)
        csvRow.push(`"${value.replace(/"/g, '""')}"`)
      }
      csvRows.push(csvRow.join(','))
    }
    
    const csv = csvRows.join('\n')
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `spreadsheet_${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  // 导入CSV
  const importCSV = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target?.result as string
      const lines = text.split('\n')
      const newData: SpreadsheetData = {}
      
      lines.forEach((line, r) => {
        if (r >= rows) return
        
        const values = line.match(/("([^"]|"")*"|[^,]+)/g) || []
        values.forEach((value, c) => {
          if (c >= cols) return
          
          // 去除引号
          let cleanValue = value.trim()
          if (cleanValue.startsWith('"') && cleanValue.endsWith('"')) {
            cleanValue = cleanValue.slice(1, -1).replace(/""/g, '"')
          }
          
          if (cleanValue) {
            const cellId = getCellId(r, c)
            newData[cellId] = { value: cleanValue }
          }
        })
      })
      
      setData(newData)
    }
    reader.readAsText(file)
  }

  // 添加行
  const addRow = () => {
    setRows(rows + 1)
  }

  // 添加列
  const addColumn = () => {
    setCols(cols + 1)
  }

  // 获取列统计信息
  const getColumnStats = (col: number): ColumnStats => {
    const values: number[] = []
    
    for (let r = 0; r < rows; r++) {
      const cellId = getCellId(r, col)
      const value = getCellDisplay(cellId)
      const num = parseFloat(value)
      if (!isNaN(num)) {
        values.push(num)
      }
    }
    
    if (values.length === 0) {
      return { count: 0, sum: 0, avg: 0, min: 0, max: 0 }
    }
    
    const sum = values.reduce((a, b) => a + b, 0)
    const avg = sum / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)
    
    return { count: values.length, sum, avg, min, max }
  }

  // 复制到剪贴板
  const copyToClipboard = () => {
    if (selectedCell) {
      const value = getCellDisplay(selectedCell)
      navigator.clipboard.writeText(value)
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">数据表格编辑器</h2>
        <p className="text-gray-600">
          在线电子表格编辑器，支持公式计算、数据导入导出
        </p>
      </div>

      {/* 工具栏 */}
      <div className="mb-4 flex flex-wrap gap-2">
        <button
          onClick={addRow}
          className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          添加行
        </button>
        
        <button
          onClick={addColumn}
          className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          添加列
        </button>
        
        <button
          onClick={exportCSV}
          className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center gap-2"
        >
          <Download className="w-4 h-4" />
          导出 CSV
        </button>
        
        <label className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 cursor-pointer transition-colors flex items-center gap-2">
          <Upload className="w-4 h-4" />
          导入 CSV
          <input
            type="file"
            accept=".csv"
            onChange={importCSV}
            className="hidden"
          />
        </label>
        
        {selectedCell && (
          <button
            onClick={copyToClipboard}
            className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center gap-2"
          >
            <Copy className="w-4 h-4" />
            复制
          </button>
        )}
      </div>

      {/* 公式栏 */}
      {selectedCell && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <div className="flex items-center gap-2">
            <span className="font-medium text-gray-700">{selectedCell}:</span>
            <span className="text-gray-600">
              {data[selectedCell]?.formula || getCellDisplay(selectedCell) || '(空)'}
            </span>
          </div>
        </div>
      )}

      {/* 表格容器 */}
      <div className="overflow-auto max-h-[600px] border border-gray-300 rounded-lg">
        <table className="w-full border-collapse">
          <thead className="sticky top-0 z-10">
            <tr>
              <th className="bg-gray-100 border border-gray-300 p-2 min-w-[50px]"></th>
              {Array.from({ length: cols }, (_, i) => (
                <th 
                  key={i} 
                  className="bg-gray-100 border border-gray-300 p-2 min-w-[100px] font-medium relative group"
                >
                  <div className="flex items-center justify-between">
                    {getColumnLabel(i)}
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                      <button
                        onClick={() => {
                          setSortColumn(i)
                          setSortAsc(!sortAsc)
                        }}
                        className="p-1 hover:bg-gray-200 rounded"
                        title="排序"
                      >
                        <ArrowUpDown className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {Array.from({ length: rows }, (_, r) => (
              <tr key={r}>
                <td className="bg-gray-100 border border-gray-300 p-2 text-center font-medium">
                  {r + 1}
                </td>
                {Array.from({ length: cols }, (_, c) => {
                  const cellId = getCellId(r, c)
                  const isSelected = selectedCell === cellId
                  const isEditing = editingCell === cellId
                  
                  return (
                    <td
                      key={c}
                      className={`border border-gray-300 p-0 relative ${
                        isSelected ? 'ring-2 ring-blue-500 ring-inset' : ''
                      }`}
                      onClick={() => {
                        setSelectedCell(cellId)
                        if (editingCell && editingCell !== cellId) {
                          saveEdit()
                        }
                      }}
                      onDoubleClick={() => startEditing(cellId)}
                      onKeyDown={(e) => handleKeyDown(e, r, c)}
                      tabIndex={0}
                    >
                      {isEditing ? (
                        <input
                          ref={inputRef}
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          onBlur={saveEdit}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault()
                              saveEdit()
                            } else if (e.key === 'Escape') {
                              e.preventDefault()
                              cancelEdit()
                            }
                          }}
                          className="w-full h-full px-2 py-1 border-none outline-none"
                        />
                      ) : (
                        <div className="px-2 py-1 min-h-[32px] cursor-cell">
                          {getCellDisplay(cellId)}
                        </div>
                      )}
                      {data[cellId]?.formula && (
                        <div className="absolute top-0 right-0 text-xs text-blue-500 px-1">
                          ƒ
                        </div>
                      )}
                    </td>
                  )
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 统计信息 */}
      {selectedCell && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium mb-2">列统计信息</h3>
          {(() => {
            const ref = parseCellRef(selectedCell)
            if (!ref) return null
            const stats = getColumnStats(ref.col)
            
            return (
              <div className="grid grid-cols-2 md:grid-cols-5 gap-3 text-sm">
                <div>
                  <span className="text-gray-600">计数：</span>
                  <span className="font-medium">{stats.count}</span>
                </div>
                <div>
                  <span className="text-gray-600">求和：</span>
                  <span className="font-medium">{stats.sum.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-600">平均：</span>
                  <span className="font-medium">{stats.avg.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-600">最小：</span>
                  <span className="font-medium">{stats.min}</span>
                </div>
                <div>
                  <span className="text-gray-600">最大：</span>
                  <span className="font-medium">{stats.max}</span>
                </div>
              </div>
            )
          })()}
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>单击选择单元格，双击或按 Enter 键编辑</li>
              <li>使用方向键在单元格间导航</li>
              <li>支持公式计算，以 = 开头，例如：=A1+B1</li>
              <li>支持函数：SUM(A1:B2) 求和，AVG(A1:B2) 平均值</li>
              <li>可以导入导出 CSV 格式文件</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 快捷键说明 */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">快捷键</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
          <div><kbd className="px-2 py-1 bg-gray-200 rounded">Enter</kbd> 编辑单元格</div>
          <div><kbd className="px-2 py-1 bg-gray-200 rounded">Esc</kbd> 取消编辑</div>
          <div><kbd className="px-2 py-1 bg-gray-200 rounded">Delete</kbd> 清空单元格</div>
          <div><kbd className="px-2 py-1 bg-gray-200 rounded">↑ ↓ ← →</kbd> 移动选择</div>
          <div><kbd className="px-2 py-1 bg-gray-200 rounded">Ctrl+C</kbd> 复制</div>
          <div><kbd className="px-2 py-1 bg-gray-200 rounded">Ctrl+V</kbd> 粘贴</div>
        </div>
      </div>
    </div>
  )
}
