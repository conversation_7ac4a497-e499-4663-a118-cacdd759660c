'use client'

import { useState } from 'react'
import { Copy, Check, Link2, AlertCircle, ArrowUpDown } from 'lucide-react'

interface EncodingExample {
  name: string
  original: string
  encoded: string
  description: string
}

export default function UrlEncoderTool() {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [mode, setMode] = useState<'encode' | 'decode'>('encode')
  const [encodeComponent, setEncodeComponent] = useState(true)
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState('')

  // 常见编码示例
  const examples: EncodingExample[] = [
    {
      name: '中文字符',
      original: '你好世界',
      encoded: '%E4%BD%A0%E5%A5%BD%E4%B8%96%E7%95%8C',
      description: 'UTF-8 编码的中文字符',
    },
    {
      name: '空格和特殊字符',
      original: 'hello world & friends!',
      encoded: 'hello%20world%20%26%20friends!',
      description: '空格编码为 %20，& 编码为 %26',
    },
    {
      name: '查询参数',
      original: 'name=张三&age=25&city=北京',
      encoded: 'name%3D%E5%BC%A0%E4%B8%89%26age%3D25%26city%3D%E5%8C%97%E4%BA%AC',
      description: 'URL 查询字符串编码',
    },
    {
      name: '完整 URL',
      original: 'https://example.com/path?q=搜索内容&lang=中文',
      encoded: 'https://example.com/path?q=%E6%90%9C%E7%B4%A2%E5%86%85%E5%AE%B9&lang=%E4%B8%AD%E6%96%87',
      description: '只编码查询参数部分',
    },
    {
      name: '邮箱地址',
      original: '<EMAIL>',
      encoded: 'user%40example.com',
      description: '@ 符号编码为 %40',
    },
    {
      name: '路径分隔符',
      original: '/path/to/file',
      encoded: '%2Fpath%2Fto%2Ffile',
      description: '/ 编码为 %2F（使用 encodeURIComponent）',
    },
  ]

  // URL 编码
  const encodeUrl = () => {
    try {
      setError('')
      if (!input.trim()) {
        setOutput('')
        return
      }

      let encoded: string
      if (encodeComponent) {
        // 使用 encodeURIComponent（编码所有特殊字符）
        encoded = encodeURIComponent(input)
      } else {
        // 使用 encodeURI（保留 URL 结构字符）
        encoded = encodeURI(input)
      }
      
      setOutput(encoded)
    } catch (err) {
      setError('编码失败：' + (err instanceof Error ? err.message : '未知错误'))
      setOutput('')
    }
  }

  // URL 解码
  const decodeUrl = () => {
    try {
      setError('')
      if (!input.trim()) {
        setOutput('')
        return
      }

      let decoded: string
      if (encodeComponent) {
        // 使用 decodeURIComponent
        decoded = decodeURIComponent(input)
      } else {
        // 使用 decodeURI
        decoded = decodeURI(input)
      }
      
      setOutput(decoded)
    } catch (err) {
      setError('解码失败：输入的 URL 编码格式不正确')
      setOutput('')
    }
  }

  // 处理输入变化
  const handleInputChange = (value: string) => {
    setInput(value)
    if (value) {
      if (mode === 'encode') {
        encodeUrl()
      } else {
        decodeUrl()
      }
    } else {
      setOutput('')
      setError('')
    }
  }

  // 切换模式
  const toggleMode = () => {
    const newMode = mode === 'encode' ? 'decode' : 'encode'
    setMode(newMode)
    setInput(output)
    setOutput(input)
    setError('')
  }

  // 应用示例
  const applyExample = (example: EncodingExample) => {
    if (mode === 'encode') {
      setInput(example.original)
      setOutput(example.encoded)
    } else {
      setInput(example.encoded)
      setOutput(example.original)
    }
    setError('')
  }

  // 复制到剪贴板
  const copyToClipboard = () => {
    navigator.clipboard.writeText(output).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  // 清空所有
  const clearAll = () => {
    setInput('')
    setOutput('')
    setError('')
  }

  // 获取字符编码详情
  const getEncodingDetails = (text: string): string => {
    if (!text) return ''
    
    const details: string[] = []
    for (let i = 0; i < Math.min(text.length, 10); i++) {
      const char = text[i]
      const code = char.charCodeAt(0)
      const hex = code.toString(16).toUpperCase().padStart(4, '0')
      const encoded = encodeURIComponent(char)
      details.push(`${char} → U+${hex} → ${encoded}`)
    }
    
    if (text.length > 10) {
      details.push('...')
    }
    
    return details.join('\n')
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">URL 编码/解码</h2>
        <p className="text-gray-600">
          在线 URL 编码和解码工具，支持完整 URL 和组件编码
        </p>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <button
                onClick={() => {
                  setMode('encode')
                  handleInputChange(input)
                }}
                className={`px-4 py-2 rounded-md ${
                  mode === 'encode'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                编码
              </button>
              <button
                onClick={() => {
                  setMode('decode')
                  handleInputChange(input)
                }}
                className={`px-4 py-2 rounded-md ${
                  mode === 'decode'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                解码
              </button>
            </div>

            <button
              onClick={toggleMode}
              className="p-2 bg-gray-100 hover:bg-gray-200 rounded-md"
              title="交换输入输出"
            >
              <ArrowUpDown className="w-4 h-4" />
            </button>
          </div>

          <div className="flex items-center gap-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={encodeComponent}
                onChange={(e) => {
                  setEncodeComponent(e.target.checked)
                  handleInputChange(input)
                }}
                className="mr-2"
              />
              <span className="text-sm">
                使用 {mode === 'encode' ? 'encodeURIComponent' : 'decodeURIComponent'}
              </span>
            </label>

            <button
              onClick={clearAll}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
            >
              清空
            </button>
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
          <AlertCircle className="w-5 h-5 text-red-600 mr-2 flex-shrink-0 mt-0.5" />
          <span className="text-red-800">{error}</span>
        </div>
      )}

      {/* 输入输出区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
        {/* 输入区域 */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="border-b border-gray-200 p-3 flex items-center">
            <Link2 className="w-5 h-5 mr-2" />
            <h3 className="font-medium">
              {mode === 'encode' ? '原始文本' : '编码文本'}
            </h3>
          </div>
          <textarea
            value={input}
            onChange={(e) => handleInputChange(e.target.value)}
            placeholder={
              mode === 'encode' 
                ? '输入要编码的文本或 URL...' 
                : '输入要解码的 URL 编码文本...'
            }
            className="w-full h-64 p-4 font-mono text-sm resize-none focus:outline-none"
            spellCheck={false}
          />
        </div>

        {/* 输出区域 */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="border-b border-gray-200 p-3 flex items-center justify-between">
            <div className="flex items-center">
              <Link2 className="w-5 h-5 mr-2" />
              <h3 className="font-medium">
                {mode === 'encode' ? '编码结果' : '解码结果'}
              </h3>
            </div>
            {output && (
              <button
                onClick={copyToClipboard}
                className="p-1 hover:bg-gray-100 rounded"
              >
                {copied ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 text-gray-400" />
                )}
              </button>
            )}
          </div>
          <textarea
            value={output}
            readOnly
            placeholder="结果将显示在这里..."
            className="w-full h-64 p-4 font-mono text-sm resize-none focus:outline-none bg-gray-50"
          />
        </div>
      </div>

      {/* 编码详情 */}
      {input && mode === 'encode' && (
        <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
          <h3 className="font-medium mb-3">字符编码详情</h3>
          <pre className="text-xs font-mono text-gray-600 whitespace-pre-wrap">
            {getEncodingDetails(input)}
          </pre>
        </div>
      )}

      {/* 常见示例 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="font-medium mb-4">常见编码示例</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={() => applyExample(example)}
              className="text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <div className="font-medium text-sm mb-1">{example.name}</div>
              <div className="text-xs text-gray-600 mb-2">{example.description}</div>
              <div className="font-mono text-xs">
                <div className="text-gray-500">原始: {example.original}</div>
                <div className="text-gray-500 truncate">编码: {example.encoded}</div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li><strong>encodeURI</strong>: 保留 URL 结构字符（如 :/?#[]@），适合编码完整 URL</li>
              <li><strong>encodeURIComponent</strong>: 编码所有特殊字符，适合编码查询参数值</li>
              <li>中文和其他 Unicode 字符会被编码为 UTF-8 格式</li>
              <li>空格可能编码为 %20 或 + 号（取决于上下文）</li>
              <li>解码时请确保输入的编码格式正确，否则可能失败</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
