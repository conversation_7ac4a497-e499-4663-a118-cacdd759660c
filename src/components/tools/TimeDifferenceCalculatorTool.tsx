'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Clock, Calendar, RefreshCw, Copy, History, Calculator } from 'lucide-react';

interface TimeUnit {
  years: number;
  months: number;
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface TimeCalculation {
  totalSeconds: number;
  totalMinutes: number;
  totalHours: number;
  totalDays: number;
  totalWeeks: number;
  totalMonths: number;
  totalYears: number;
  breakdown: TimeUnit;
  workingDays: number;
  weekends: number;
}

export default function TimeDifferenceCalculatorTool() {
  const [startDate, setStartDate] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endDate, setEndDate] = useState('');
  const [endTime, setEndTime] = useState('');
  const [includeTime, setIncludeTime] = useState(true);
  const [calculation, setCalculation] = useState<TimeCalculation | null>(null);
  const [error, setError] = useState('');
  const [presetType, setPresetType] = useState('');
  const [history, setHistory] = useState<Array<{
    startDateTime: string;
    endDateTime: string;
    result: TimeCalculation;
    timestamp: string;
  }>>([]);

  // 预设快速选项
  const presets = [
    { value: 'now-1hour', label: '当前时间往前1小时', hours: -1 },
    { value: 'now-1day', label: '当前时间往前1天', days: -1 },
    { value: 'now-1week', label: '当前时间往前1周', days: -7 },
    { value: 'now-1month', label: '当前时间往前1个月', months: -1 },
    { value: 'now-1year', label: '当前时间往前1年', years: -1 },
    { value: 'now+1hour', label: '当前时间往后1小时', hours: 1 },
    { value: 'now+1day', label: '当前时间往后1天', days: 1 },
    { value: 'now+1week', label: '当前时间往后1周', days: 7 },
    { value: 'now+1month', label: '当前时间往后1个月', months: 1 },
    { value: 'now+1year', label: '当前时间往后1年', years: 1 },
  ];

  // 计算工作日（排除周末）
  const calculateWorkingDays = (start: Date, end: Date): { workingDays: number; weekends: number } => {
    let workingDays = 0;
    let weekends = 0;
    let current = new Date(start);
    
    while (current <= end) {
      const dayOfWeek = current.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        weekends++;
      } else {
        workingDays++;
      }
      current.setDate(current.getDate() + 1);
    }
    
    return { workingDays, weekends };
  };

  // 计算精确的年月日差异
  const calculateExactDifference = (start: Date, end: Date): TimeUnit => {
    let years = end.getFullYear() - start.getFullYear();
    let months = end.getMonth() - start.getMonth();
    let days = end.getDate() - start.getDate();
    let hours = end.getHours() - start.getHours();
    let minutes = end.getMinutes() - start.getMinutes();
    let seconds = end.getSeconds() - start.getSeconds();

    // 处理负数
    if (seconds < 0) {
      seconds += 60;
      minutes--;
    }
    if (minutes < 0) {
      minutes += 60;
      hours--;
    }
    if (hours < 0) {
      hours += 24;
      days--;
    }
    if (days < 0) {
      const prevMonth = new Date(end.getFullYear(), end.getMonth(), 0);
      days += prevMonth.getDate();
      months--;
    }
    if (months < 0) {
      months += 12;
      years--;
    }

    return { years, months, days, hours, minutes, seconds };
  };

  // 计算时间差
  const calculateTimeDifference = () => {
    try {
      setError('');
      
      if (!startDate || !endDate) {
        setError('请选择开始和结束日期');
        return;
      }

      let startDateTime: Date;
      let endDateTime: Date;

      if (includeTime) {
        if (!startTime || !endTime) {
          setError('请输入开始和结束时间');
          return;
        }
        startDateTime = new Date(`${startDate}T${startTime}`);
        endDateTime = new Date(`${endDate}T${endTime}`);
      } else {
        startDateTime = new Date(`${startDate}T00:00:00`);
        endDateTime = new Date(`${endDate}T23:59:59`);
      }

      if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
        setError('日期时间格式无效');
        return;
      }

      const timeDiff = Math.abs(endDateTime.getTime() - startDateTime.getTime());
      
      // 计算各种单位的总量
      const totalSeconds = Math.floor(timeDiff / 1000);
      const totalMinutes = Math.floor(totalSeconds / 60);
      const totalHours = Math.floor(totalMinutes / 60);
      const totalDays = Math.floor(totalHours / 24);
      const totalWeeks = Math.floor(totalDays / 7);
      const totalMonths = Math.floor(totalDays / 30.44); // 平均月天数
      const totalYears = Math.floor(totalDays / 365.25); // 考虑闰年

      // 计算精确分解
      const breakdown = calculateExactDifference(
        startDateTime < endDateTime ? startDateTime : endDateTime,
        startDateTime < endDateTime ? endDateTime : startDateTime
      );

      // 计算工作日
      const { workingDays, weekends } = calculateWorkingDays(
        startDateTime < endDateTime ? startDateTime : endDateTime,
        startDateTime < endDateTime ? endDateTime : startDateTime
      );

      const result: TimeCalculation = {
        totalSeconds,
        totalMinutes,
        totalHours,
        totalDays,
        totalWeeks,
        totalMonths,
        totalYears,
        breakdown,
        workingDays,
        weekends,
      };

      setCalculation(result);

      // 添加到历史记录
      const historyItem = {
        startDateTime: startDateTime.toLocaleString('zh-CN'),
        endDateTime: endDateTime.toLocaleString('zh-CN'),
        result,
        timestamp: new Date().toLocaleString('zh-CN'),
      };
      setHistory(prev => [historyItem, ...prev.slice(0, 9)]); // 保留最近10条记录

    } catch (err) {
      setError(err instanceof Error ? err.message : '计算失败');
    }
  };

  // 应用预设
  const applyPreset = (preset: string) => {
    const presetConfig = presets.find(p => p.value === preset);
    if (!presetConfig) return;

    const now = new Date();
    const targetDate = new Date(now);

    if (presetConfig.years) {
      targetDate.setFullYear(now.getFullYear() + presetConfig.years);
    }
    if (presetConfig.months) {
      targetDate.setMonth(now.getMonth() + presetConfig.months);
    }
    if (presetConfig.days) {
      targetDate.setDate(now.getDate() + presetConfig.days);
    }
    if (presetConfig.hours) {
      targetDate.setHours(now.getHours() + presetConfig.hours);
    }

    // 设置当前时间和目标时间
    const nowStr = now.toISOString().slice(0, 10);
    const nowTimeStr = now.toTimeString().slice(0, 5);
    const targetStr = targetDate.toISOString().slice(0, 10);
    const targetTimeStr = targetDate.toTimeString().slice(0, 5);

    if (preset.includes('+')) {
      setStartDate(nowStr);
      setStartTime(nowTimeStr);
      setEndDate(targetStr);
      setEndTime(targetTimeStr);
    } else {
      setStartDate(targetStr);
      setStartTime(targetTimeStr);
      setEndDate(nowStr);
      setEndTime(nowTimeStr);
    }

    setIncludeTime(true);
  };

  // 设置当前时间
  const setCurrentTime = (type: 'start' | 'end') => {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10);
    const timeStr = now.toTimeString().slice(0, 5);

    if (type === 'start') {
      setStartDate(dateStr);
      setStartTime(timeStr);
    } else {
      setEndDate(dateStr);
      setEndTime(timeStr);
    }
  };

  // 复制结果
  const copyResult = () => {
    if (!calculation) return;

    const result = `时间差计算结果：
精确差异：${calculation.breakdown.years}年 ${calculation.breakdown.months}个月 ${calculation.breakdown.days}天 ${calculation.breakdown.hours}小时 ${calculation.breakdown.minutes}分钟 ${calculation.breakdown.seconds}秒

总计：
- ${calculation.totalSeconds.toLocaleString()} 秒
- ${calculation.totalMinutes.toLocaleString()} 分钟
- ${calculation.totalHours.toLocaleString()} 小时
- ${calculation.totalDays.toLocaleString()} 天
- ${calculation.totalWeeks.toLocaleString()} 周
- ${calculation.totalMonths.toLocaleString()} 个月
- ${calculation.totalYears.toLocaleString()} 年

工作日统计：
- 工作日：${calculation.workingDays} 天
- 周末：${calculation.weekends} 天`;

    navigator.clipboard.writeText(result);
  };

  // 交换开始和结束时间
  const swapTimes = () => {
    const tempDate = startDate;
    const tempTime = startTime;
    setStartDate(endDate);
    setStartTime(endTime);
    setEndDate(tempDate);
    setEndTime(tempTime);
  };

  // 清空所有输入
  const clearAll = () => {
    setStartDate('');
    setStartTime('');
    setEndDate('');
    setEndTime('');
    setCalculation(null);
    setError('');
    setPresetType('');
  };

  // 格式化持续时间显示
  const formatDuration = (breakdown: TimeUnit): string => {
    const parts = [];
    if (breakdown.years > 0) parts.push(`${breakdown.years}年`);
    if (breakdown.months > 0) parts.push(`${breakdown.months}个月`);
    if (breakdown.days > 0) parts.push(`${breakdown.days}天`);
    if (breakdown.hours > 0) parts.push(`${breakdown.hours}小时`);
    if (breakdown.minutes > 0) parts.push(`${breakdown.minutes}分钟`);
    if (breakdown.seconds > 0) parts.push(`${breakdown.seconds}秒`);
    
    return parts.join(' ') || '0秒';
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            时间差计算器
          </CardTitle>
          <CardDescription>
            精确计算两个时间点之间的差异，支持多种显示格式和工作日统计
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="calculator" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="calculator">计算器</TabsTrigger>
          <TabsTrigger value="history">历史记录</TabsTrigger>
        </TabsList>

        <TabsContent value="calculator" className="space-y-4">
          {/* 快速预设 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                快速预设
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>预设时间范围</Label>
                  <Select value={presetType} onValueChange={(value) => {
                    setPresetType(value);
                    applyPreset(value);
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择预设时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      {presets.map(preset => (
                        <SelectItem key={preset.value} value={preset.value}>
                          {preset.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end gap-2">
                  <Button variant="outline" onClick={() => setIncludeTime(!includeTime)}>
                    {includeTime ? '忽略时间' : '包含时间'}
                  </Button>
                  <Button variant="outline" onClick={clearAll}>
                    清空所有
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 时间输入 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                时间设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 开始时间 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">开始时间</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentTime('start')}
                  >
                    设为当前时间
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start-date">日期</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                    />
                  </div>
                  {includeTime && (
                    <div className="space-y-2">
                      <Label htmlFor="start-time">时间</Label>
                      <Input
                        id="start-time"
                        type="time"
                        step="1"
                        value={startTime}
                        onChange={(e) => setStartTime(e.target.value)}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="border-t my-4" />

              {/* 结束时间 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">结束时间</Label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={swapTimes}
                    >
                      交换时间
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentTime('end')}
                    >
                      设为当前时间
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="end-date">日期</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                    />
                  </div>
                  {includeTime && (
                    <div className="space-y-2">
                      <Label htmlFor="end-time">时间</Label>
                      <Input
                        id="end-time"
                        type="time"
                        step="1"
                        value={endTime}
                        onChange={(e) => setEndTime(e.target.value)}
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={calculateTimeDifference} className="flex items-center gap-2">
                  <Calculator className="h-4 w-4" />
                  计算时间差
                </Button>
                {calculation && (
                  <Button variant="outline" onClick={copyResult} className="flex items-center gap-2">
                    <Copy className="h-4 w-4" />
                    复制结果
                  </Button>
                )}
              </div>

              {error && (
                <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                  {error}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 计算结果 */}
          {calculation && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* 精确差异 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">精确差异</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-primary mb-4">
                    {formatDuration(calculation.breakdown)}
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">年：</span>
                        <span className="font-medium">{calculation.breakdown.years}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">月：</span>
                        <span className="font-medium">{calculation.breakdown.months}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">天：</span>
                        <span className="font-medium">{calculation.breakdown.days}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">小时：</span>
                        <span className="font-medium">{calculation.breakdown.hours}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">分钟：</span>
                        <span className="font-medium">{calculation.breakdown.minutes}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">秒：</span>
                        <span className="font-medium">{calculation.breakdown.seconds}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 总计统计 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">总计统计</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">总秒数：</span>
                      <Badge variant="secondary">{calculation.totalSeconds.toLocaleString()}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">总分钟数：</span>
                      <Badge variant="secondary">{calculation.totalMinutes.toLocaleString()}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">总小时数：</span>
                      <Badge variant="secondary">{calculation.totalHours.toLocaleString()}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">总天数：</span>
                      <Badge variant="secondary">{calculation.totalDays.toLocaleString()}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">总周数：</span>
                      <Badge variant="secondary">{calculation.totalWeeks.toLocaleString()}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">总月数：</span>
                      <Badge variant="secondary">{calculation.totalMonths.toLocaleString()}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">总年数：</span>
                      <Badge variant="secondary">{calculation.totalYears.toLocaleString()}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 工作日统计 */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="text-lg">工作日统计</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {calculation.workingDays}
                      </div>
                      <div className="text-sm text-muted-foreground">工作日</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {calculation.weekends}
                      </div>
                      <div className="text-sm text-muted-foreground">周末天数</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {calculation.totalDays}
                      </div>
                      <div className="text-sm text-muted-foreground">总天数</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                计算历史
              </CardTitle>
              <CardDescription>
                最近的计算记录（最多保留10条）
              </CardDescription>
            </CardHeader>
            <CardContent>
              {history.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  暂无计算历史
                </div>
              ) : (
                <div className="space-y-4">
                  {history.map((item, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-2">
                      <div className="flex justify-between items-start">
                        <div className="text-sm text-muted-foreground">
                          计算时间：{item.timestamp}
                        </div>
                        <Badge variant="outline">#{index + 1}</Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">开始：</span>
                          {item.startDateTime}
                        </div>
                        <div>
                          <span className="text-muted-foreground">结束：</span>
                          {item.endDateTime}
                        </div>
                      </div>
                      <div className="font-medium">
                        差异：{formatDuration(item.result.breakdown)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        总计 {item.result.totalDays} 天，其中工作日 {item.result.workingDays} 天
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
