'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  Download, 
  RotateCcw, 
  Undo, 
  Redo, 
  Square, 
  Circle, 
  Eye,
  EyeOff,
  Trash2,
  Settings,
  Palette,
  Grid,
  Zap,
  Save,
  RefreshCw
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'

/**
 * 马赛克区域接口
 */
interface MosaicArea {
  id: string
  type: 'rectangle' | 'circle' | 'polygon'
  points: { x: number; y: number }[]
  mosaicType: 'pixelate' | 'blur' | 'color'
  intensity: number
  visible: boolean
}

/**
 * 历史记录接口
 */
interface HistoryState {
  areas: MosaicArea[]
  timestamp: number
}

/**
 * 图片马赛克处理工具组件
 * 支持多种马赛克效果和区域绘制
 */
export default function ImageMosaicTool() {
  // 基础状态
  const [image, setImage] = useState<HTMLImageElement | null>(null)
  const [imageUrl, setImageUrl] = useState<string>('')
  const [isProcessing, setIsProcessing] = useState(false)
  
  // Canvas 引用
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // 马赛克区域管理
  const [mosaicAreas, setMosaicAreas] = useState<MosaicArea[]>([])
  const [selectedAreaId, setSelectedAreaId] = useState<string | null>(null)
  
  // 绘制状态
  const [isDrawing, setIsDrawing] = useState(false)
  const [currentTool, setCurrentTool] = useState<'rectangle' | 'circle' | 'polygon'>('rectangle')
  const [currentPoints, setCurrentPoints] = useState<{ x: number; y: number }[]>([])
  
  // 马赛克设置
  const [mosaicType, setMosaicType] = useState<'pixelate' | 'blur' | 'color'>('pixelate')
  const [intensity, setIntensity] = useState(10)
  
  // 历史记录
  const [history, setHistory] = useState<HistoryState[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  
  // 预览设置
  const [showPreview, setShowPreview] = useState(true)
  const [previewOpacity, setPreviewOpacity] = useState(0.8)

  /**
   * 添加历史记录
   */
  const addToHistory = useCallback((areas: MosaicArea[]) => {
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push({
      areas: JSON.parse(JSON.stringify(areas)),
      timestamp: Date.now()
    })
    
    // 限制历史记录数量
    if (newHistory.length > 50) {
      newHistory.shift()
    } else {
      setHistoryIndex(prev => prev + 1)
    }
    
    setHistory(newHistory)
  }, [history, historyIndex])

  /**
   * 撤销操作
   */
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(prev => prev - 1)
      setMosaicAreas(history[historyIndex - 1].areas)
    }
  }, [history, historyIndex])

  /**
   * 重做操作
   */
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(prev => prev + 1)
      setMosaicAreas(history[historyIndex + 1].areas)
    }
  }, [history, historyIndex])

  /**
   * 处理文件上传
   */
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      toast({
        title: '文件格式错误',
        description: '请选择有效的图片文件',
        variant: 'destructive'
      })
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const img = new Image()
      img.onload = () => {
        setImage(img)
        setImageUrl(e.target?.result as string)
        setMosaicAreas([])
        setHistory([])
        setHistoryIndex(-1)
        addToHistory([])
        
        toast({
          title: '图片上传成功',
          description: '可以开始添加马赛克区域'
        })
      }
      img.src = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }, [addToHistory])

  /**
   * 获取Canvas坐标
   */
  const getCanvasCoordinates = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return { x: 0, y: 0 }

    const rect = canvas.getBoundingClientRect()
    const scaleX = canvas.width / rect.width
    const scaleY = canvas.height / rect.height

    return {
      x: (event.clientX - rect.left) * scaleX,
      y: (event.clientY - rect.top) * scaleY
    }
  }, [])

  /**
   * 开始绘制
   */
  const startDrawing = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const point = getCanvasCoordinates(event)
    setIsDrawing(true)
    setCurrentPoints([point])
  }, [getCanvasCoordinates])

  /**
   * 绘制过程中
   */
  const onDrawing = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return

    const point = getCanvasCoordinates(event)
    
    if (currentTool === 'polygon') {
      // 多边形模式下，移动时更新临时点
      setCurrentPoints(prev => [...prev.slice(0, -1), point])
    } else {
      // 矩形和圆形模式下，更新第二个点
      setCurrentPoints(prev => [prev[0], point])
    }
  }, [isDrawing, currentTool, getCanvasCoordinates])

  /**
   * 结束绘制
   */
  const endDrawing = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return

    const point = getCanvasCoordinates(event)

    if (currentTool === 'polygon') {
      // 多边形模式下，添加点
      const newPoints = [...currentPoints.slice(0, -1), point]
      setCurrentPoints(newPoints)
      
      // 双击结束多边形绘制
      if (event.detail === 2) {
        finishPolygon(newPoints)
      }
    } else {
      // 矩形和圆形模式下，完成绘制
      const finalPoints = [currentPoints[0], point]
      createMosaicArea(finalPoints)
    }
  }, [isDrawing, currentTool, currentPoints, getCanvasCoordinates])

  /**
   * 完成多边形绘制
   */
  const finishPolygon = useCallback((points: { x: number; y: number }[]) => {
    if (points.length >= 3) {
      createMosaicArea(points)
    }
    setIsDrawing(false)
    setCurrentPoints([])
  }, [])

  /**
   * 创建马赛克区域
   */
  const createMosaicArea = useCallback((points: { x: number; y: number }[]) => {
    const newArea: MosaicArea = {
      id: Date.now().toString(),
      type: currentTool,
      points,
      mosaicType,
      intensity,
      visible: true
    }

    const newAreas = [...mosaicAreas, newArea]
    setMosaicAreas(newAreas)
    addToHistory(newAreas)
    setIsDrawing(false)
    setCurrentPoints([])
    
    toast({
      title: '马赛克区域已添加',
      description: `${currentTool === 'rectangle' ? '矩形' : currentTool === 'circle' ? '圆形' : '多边形'}区域创建成功`
    })
  }, [currentTool, mosaicType, intensity, mosaicAreas, addToHistory])

  /**
   * 删除马赛克区域
   */
  const deleteMosaicArea = useCallback((areaId: string) => {
    const newAreas = mosaicAreas.filter(area => area.id !== areaId)
    setMosaicAreas(newAreas)
    addToHistory(newAreas)
    setSelectedAreaId(null)
  }, [mosaicAreas, addToHistory])

  /**
   * 切换区域可见性
   */
  const toggleAreaVisibility = useCallback((areaId: string) => {
    const newAreas = mosaicAreas.map(area =>
      area.id === areaId ? { ...area, visible: !area.visible } : area
    )
    setMosaicAreas(newAreas)
    addToHistory(newAreas)
  }, [mosaicAreas, addToHistory])

  /**
   * 更新区域设置
   */
  const updateAreaSettings = useCallback((areaId: string, updates: Partial<MosaicArea>) => {
    const newAreas = mosaicAreas.map(area =>
      area.id === areaId ? { ...area, ...updates } : area
    )
    setMosaicAreas(newAreas)
    addToHistory(newAreas)
  }, [mosaicAreas, addToHistory])

  /**
   * 应用像素化效果
   */
  const applyPixelateEffect = useCallback((
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    pixelSize: number
  ) => {
    const imageData = ctx.getImageData(x, y, width, height)
    const data = imageData.data

    for (let py = 0; py < height; py += pixelSize) {
      for (let px = 0; px < width; px += pixelSize) {
        // 获取像素块的平均颜色
        let r = 0, g = 0, b = 0, count = 0

        for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {
          for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {
            const index = ((py + dy) * width + (px + dx)) * 4
            r += data[index]
            g += data[index + 1]
            b += data[index + 2]
            count++
          }
        }

        r = Math.floor(r / count)
        g = Math.floor(g / count)
        b = Math.floor(b / count)

        // 应用平均颜色到整个像素块
        for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {
          for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {
            const index = ((py + dy) * width + (px + dx)) * 4
            data[index] = r
            data[index + 1] = g
            data[index + 2] = b
          }
        }
      }
    }

    ctx.putImageData(imageData, x, y)
  }, [])

  /**
   * 应用模糊效果
   */
  const applyBlurEffect = useCallback((
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    blurRadius: number
  ) => {
    const imageData = ctx.getImageData(x, y, width, height)
    const data = imageData.data
    const output = new Uint8ClampedArray(data)

    const kernel = []
    const kernelSize = blurRadius * 2 + 1
    let kernelSum = 0

    // 生成高斯核
    for (let i = 0; i < kernelSize; i++) {
      const value = Math.exp(-Math.pow(i - blurRadius, 2) / (2 * Math.pow(blurRadius / 3, 2)))
      kernel[i] = value
      kernelSum += value
    }

    // 归一化核
    for (let i = 0; i < kernelSize; i++) {
      kernel[i] /= kernelSum
    }

    // 水平模糊
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let r = 0, g = 0, b = 0

        for (let i = 0; i < kernelSize; i++) {
          const px = Math.min(Math.max(x + i - blurRadius, 0), width - 1)
          const index = (y * width + px) * 4
          const weight = kernel[i]

          r += data[index] * weight
          g += data[index + 1] * weight
          b += data[index + 2] * weight
        }

        const index = (y * width + x) * 4
        output[index] = r
        output[index + 1] = g
        output[index + 2] = b
      }
    }

    // 垂直模糊
    for (let x = 0; x < width; x++) {
      for (let y = 0; y < height; y++) {
        let r = 0, g = 0, b = 0

        for (let i = 0; i < kernelSize; i++) {
          const py = Math.min(Math.max(y + i - blurRadius, 0), height - 1)
          const index = (py * width + x) * 4
          const weight = kernel[i]

          r += output[index] * weight
          g += output[index + 1] * weight
          b += output[index + 2] * weight
        }

        const index = (y * width + x) * 4
        data[index] = r
        data[index + 1] = g
        data[index + 2] = b
      }
    }

    ctx.putImageData(imageData, x, y)
  }, [])

  /**
   * 应用颜色马赛克效果
   */
  const applyColorEffect = useCallback((
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    colorIntensity: number
  ) => {
    const imageData = ctx.getImageData(x, y, width, height)
    const data = imageData.data

    // 生成随机颜色
    const colors = []
    const colorCount = Math.max(2, Math.floor(colorIntensity / 2))
    
    for (let i = 0; i < colorCount; i++) {
      colors.push({
        r: Math.floor(Math.random() * 256),
        g: Math.floor(Math.random() * 256),
        b: Math.floor(Math.random() * 256)
      })
    }

    for (let i = 0; i < data.length; i += 4) {
      const brightness = (data[i] + data[i + 1] + data[i + 2]) / 3
      const colorIndex = Math.floor((brightness / 255) * (colors.length - 1))
      const color = colors[colorIndex]

      data[i] = color.r
      data[i + 1] = color.g
      data[i + 2] = color.b
    }

    ctx.putImageData(imageData, x, y)
  }, [])

  /**
   * 应用马赛克效果到区域
   */
  const applyMosaicToArea = useCallback((
    ctx: CanvasRenderingContext2D,
    area: MosaicArea
  ) => {
    if (!area.visible) return

    // 创建路径
    ctx.save()
    ctx.beginPath()

    if (area.type === 'rectangle') {
      const [start, end] = area.points
      const x = Math.min(start.x, end.x)
      const y = Math.min(start.y, end.y)
      const width = Math.abs(end.x - start.x)
      const height = Math.abs(end.y - start.y)
      
      ctx.rect(x, y, width, height)
      ctx.clip()

      // 应用效果
      if (area.mosaicType === 'pixelate') {
        applyPixelateEffect(ctx, x, y, width, height, area.intensity)
      } else if (area.mosaicType === 'blur') {
        applyBlurEffect(ctx, x, y, width, height, area.intensity)
      } else if (area.mosaicType === 'color') {
        applyColorEffect(ctx, x, y, width, height, area.intensity)
      }
    } else if (area.type === 'circle') {
      const [center, edge] = area.points
      const radius = Math.sqrt(
        Math.pow(edge.x - center.x, 2) + Math.pow(edge.y - center.y, 2)
      )
      
      ctx.arc(center.x, center.y, radius, 0, 2 * Math.PI)
      ctx.clip()

      const x = center.x - radius
      const y = center.y - radius
      const size = radius * 2

      if (area.mosaicType === 'pixelate') {
        applyPixelateEffect(ctx, x, y, size, size, area.intensity)
      } else if (area.mosaicType === 'blur') {
        applyBlurEffect(ctx, x, y, size, size, area.intensity)
      } else if (area.mosaicType === 'color') {
        applyColorEffect(ctx, x, y, size, size, area.intensity)
      }
    } else if (area.type === 'polygon') {
      area.points.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          ctx.lineTo(point.x, point.y)
        }
      })
      ctx.closePath()
      ctx.clip()

      // 计算多边形边界框
      const xs = area.points.map(p => p.x)
      const ys = area.points.map(p => p.y)
      const minX = Math.min(...xs)
      const minY = Math.min(...ys)
      const maxX = Math.max(...xs)
      const maxY = Math.max(...ys)

      if (area.mosaicType === 'pixelate') {
        applyPixelateEffect(ctx, minX, minY, maxX - minX, maxY - minY, area.intensity)
      } else if (area.mosaicType === 'blur') {
        applyBlurEffect(ctx, minX, minY, maxX - minX, maxY - minY, area.intensity)
      } else if (area.mosaicType === 'color') {
        applyColorEffect(ctx, minX, minY, maxX - minX, maxY - minY, area.intensity)
      }
    }

    ctx.restore()
  }, [applyPixelateEffect, applyBlurEffect, applyColorEffect])

  /**
   * 渲染图片和马赛克效果
   */
  const renderCanvas = useCallback(() => {
    const canvas = canvasRef.current
    const overlayCanvas = overlayCanvasRef.current
    if (!canvas || !overlayCanvas || !image) return

    const ctx = canvas.getContext('2d')
    const overlayCtx = overlayCanvas.getContext('2d')
    if (!ctx || !overlayCtx) return

    // 设置画布尺寸
    canvas.width = image.width
    canvas.height = image.height
    overlayCanvas.width = image.width
    overlayCanvas.height = image.height

    // 绘制原始图片
    ctx.drawImage(image, 0, 0)

    // 应用马赛克效果
    mosaicAreas.forEach(area => {
      applyMosaicToArea(ctx, area)
    })

    // 绘制预览覆盖层
    overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height)
    
    if (showPreview) {
      overlayCtx.globalAlpha = previewOpacity
      
      // 绘制现有区域边框
      mosaicAreas.forEach(area => {
        if (!area.visible) return

        overlayCtx.strokeStyle = area.id === selectedAreaId ? '#ff4444' : '#00ff00'
        overlayCtx.lineWidth = 2
        overlayCtx.setLineDash([5, 5])
        overlayCtx.beginPath()

        if (area.type === 'rectangle') {
          const [start, end] = area.points
          const x = Math.min(start.x, end.x)
          const y = Math.min(start.y, end.y)
          const width = Math.abs(end.x - start.x)
          const height = Math.abs(end.y - start.y)
          overlayCtx.rect(x, y, width, height)
        } else if (area.type === 'circle') {
          const [center, edge] = area.points
          const radius = Math.sqrt(
            Math.pow(edge.x - center.x, 2) + Math.pow(edge.y - center.y, 2)
          )
          overlayCtx.arc(center.x, center.y, radius, 0, 2 * Math.PI)
        } else if (area.type === 'polygon') {
          area.points.forEach((point, index) => {
            if (index === 0) {
              overlayCtx.moveTo(point.x, point.y)
            } else {
              overlayCtx.lineTo(point.x, point.y)
            }
          })
          overlayCtx.closePath()
        }

        overlayCtx.stroke()
      })

      // 绘制当前绘制的区域
      if (isDrawing && currentPoints.length > 0) {
        overlayCtx.strokeStyle = '#ffff00'
        overlayCtx.lineWidth = 2
        overlayCtx.setLineDash([])
        overlayCtx.beginPath()

        if (currentTool === 'rectangle' && currentPoints.length === 2) {
          const [start, end] = currentPoints
          const x = Math.min(start.x, end.x)
          const y = Math.min(start.y, end.y)
          const width = Math.abs(end.x - start.x)
          const height = Math.abs(end.y - start.y)
          overlayCtx.rect(x, y, width, height)
        } else if (currentTool === 'circle' && currentPoints.length === 2) {
          const [center, edge] = currentPoints
          const radius = Math.sqrt(
            Math.pow(edge.x - center.x, 2) + Math.pow(edge.y - center.y, 2)
          )
          overlayCtx.arc(center.x, center.y, radius, 0, 2 * Math.PI)
        } else if (currentTool === 'polygon' && currentPoints.length > 0) {
          currentPoints.forEach((point, index) => {
            if (index === 0) {
              overlayCtx.moveTo(point.x, point.y)
            } else {
              overlayCtx.lineTo(point.x, point.y)
            }
          })
        }

        overlayCtx.stroke()
      }

      overlayCtx.globalAlpha = 1
    }
  }, [image, mosaicAreas, selectedAreaId, showPreview, previewOpacity, isDrawing, currentPoints, currentTool, applyMosaicToArea])

  /**
   * 导出处理后的图片
   */
  const exportImage = useCallback(async () => {
    const canvas = canvasRef.current
    if (!canvas) return

    try {
      setIsProcessing(true)
      
      // 创建下载链接
      canvas.toBlob((blob) => {
        if (!blob) return

        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `mosaic-image-${Date.now()}.png`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        toast({
          title: '导出成功',
          description: '马赛克处理后的图片已保存'
        })
      }, 'image/png', 0.9)
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: '导出失败',
        description: '请重试或检查图片格式',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }, [])

  /**
   * 清除所有马赛克区域
   */
  const clearAllAreas = useCallback(() => {
    setMosaicAreas([])
    addToHistory([])
    setSelectedAreaId(null)
    toast({
      title: '已清除',
      description: '所有马赛克区域已清除'
    })
  }, [addToHistory])

  // 监听状态变化，重新渲染画布
  useEffect(() => {
    renderCanvas()
  }, [renderCanvas])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'z':
            event.preventDefault()
            if (event.shiftKey) {
              redo()
            } else {
              undo()
            }
            break
          case 's':
            event.preventDefault()
            exportImage()
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [undo, redo, exportImage])

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      {/* 标题和描述 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">图片马赛克工具</h1>
        <p className="text-muted-foreground">
          智能图片马赛克处理，支持像素化、模糊、颜色马赛克等多种效果
        </p>
      </div>

      {/* 文件上传 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            图片上传
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div
              className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-lg font-medium">点击上传图片</p>
              <p className="text-sm text-muted-foreground">支持 JPG、PNG、GIF 等格式</p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
        </CardContent>
      </Card>

      {image && (
        <>
          {/* 工具栏 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                马赛克设置
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="tools" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="tools">绘制工具</TabsTrigger>
                  <TabsTrigger value="effects">效果设置</TabsTrigger>
                  <TabsTrigger value="preview">预览设置</TabsTrigger>
                </TabsList>

                <TabsContent value="tools" className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label>绘制工具</Label>
                      <div className="flex gap-2 mt-2">
                        <Button
                          variant={currentTool === 'rectangle' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setCurrentTool('rectangle')}
                        >
                          <Square className="h-4 w-4 mr-2" />
                          矩形
                        </Button>
                        <Button
                          variant={currentTool === 'circle' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setCurrentTool('circle')}
                        >
                          <Circle className="h-4 w-4 mr-2" />
                          圆形
                        </Button>
                        <Button
                          variant={currentTool === 'polygon' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setCurrentTool('polygon')}
                        >
                          <Grid className="h-4 w-4 mr-2" />
                          多边形
                        </Button>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={undo}
                        disabled={historyIndex <= 0}
                      >
                        <Undo className="h-4 w-4 mr-2" />
                        撤销
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={redo}
                        disabled={historyIndex >= history.length - 1}
                      >
                        <Redo className="h-4 w-4 mr-2" />
                        重做
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearAllAreas}
                        disabled={mosaicAreas.length === 0}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        清除全部
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="effects" className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label>马赛克类型</Label>
                      <Select value={mosaicType} onValueChange={(value: 'pixelate' | 'blur' | 'color') => setMosaicType(value)}>
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pixelate">像素化</SelectItem>
                          <SelectItem value="blur">模糊</SelectItem>
                          <SelectItem value="color">颜色马赛克</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>强度: {intensity}</Label>
                      <Slider
                        value={[intensity]}
                        onValueChange={(value) => setIntensity(value[0])}
                        min={1}
                        max={50}
                        step={1}
                        className="mt-2"
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="preview" className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>显示预览边框</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowPreview(!showPreview)}
                      >
                        {showPreview ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                      </Button>
                    </div>

                    {showPreview && (
                      <div>
                        <Label>预览透明度: {Math.round(previewOpacity * 100)}%</Label>
                        <Slider
                          value={[previewOpacity]}
                          onValueChange={(value) => setPreviewOpacity(value[0])}
                          min={0.1}
                          max={1}
                          step={0.1}
                          className="mt-2"
                        />
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* 画布区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  马赛克编辑
                </span>
                <div className="flex gap-2">
                  <Button
                    onClick={exportImage}
                    disabled={isProcessing}
                    size="sm"
                  >
                    {isProcessing ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Download className="h-4 w-4 mr-2" />
                    )}
                    导出图片
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative border rounded-lg overflow-hidden bg-gray-50">
                <canvas
                  ref={canvasRef}
                  className="max-w-full h-auto"
                  style={{ display: 'block' }}
                />
                <canvas
                  ref={overlayCanvasRef}
                  className="absolute top-0 left-0 max-w-full h-auto cursor-crosshair"
                  onMouseDown={startDrawing}
                  onMouseMove={onDrawing}
                  onMouseUp={endDrawing}
                  onDoubleClick={() => {
                    if (currentTool === 'polygon' && currentPoints.length >= 3) {
                      finishPolygon(currentPoints)
                    }
                  }}
                />
              </div>

              {currentTool === 'polygon' && isDrawing && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-700">
                    多边形绘制模式：点击添加顶点，双击完成绘制
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 马赛克区域列表 */}
          {mosaicAreas.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Grid className="h-5 w-5" />
                  马赛克区域 ({mosaicAreas.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {mosaicAreas.map((area, index) => (
                    <div
                      key={area.id}
                      className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedAreaId === area.id ? 'border-blue-500 bg-blue-50' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setSelectedAreaId(area.id)}
                    >
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">
                          {area.type === 'rectangle' ? '矩形' : area.type === 'circle' ? '圆形' : '多边形'}
                        </Badge>
                        <span className="text-sm">
                          {area.mosaicType === 'pixelate' ? '像素化' : area.mosaicType === 'blur' ? '模糊' : '颜色马赛克'} 
                          (强度: {area.intensity})
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            toggleAreaVisibility(area.id)
                          }}
                        >
                          {area.visible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteMosaicArea(area.id)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle>使用说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>• 选择绘制工具（矩形、圆形、多边形）</p>
                <p>• 在图片上拖拽绘制马赛克区域</p>
                <p>• 多边形模式：点击添加顶点，双击完成绘制</p>
                <p>• 调整马赛克类型和强度</p>
                <p>• 支持撤销/重做操作 (Ctrl+Z / Ctrl+Shift+Z)</p>
                <p>• 快捷键：Ctrl+S 导出图片</p>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
