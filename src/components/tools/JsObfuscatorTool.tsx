'use client'

import { useState } from 'react'
import { Shield, Copy, Check, Code, Settings, AlertCircle, RotateCcw, Download, Info } from 'lucide-react'

interface ObfuscatorOptions {
  compact: boolean
  controlFlowFlattening: boolean
  deadCodeInjection: boolean
  debugProtection: boolean
  disableConsoleOutput: boolean
  identifierNamesGenerator: 'hexadecimal' | 'mangled' | 'mangled-shuffled'
  renameGlobals: boolean
  rotateStringArray: boolean
  selfDefending: boolean
  shuffleStringArray: boolean
  splitStrings: boolean
  stringArray: boolean
  stringArrayEncoding: string[]
  stringArrayThreshold: number
  transformObjectKeys: boolean
  unicodeEscapeSequence: boolean
}

export default function JsObfuscatorTool() {
  const [inputCode, setInputCode] = useState('')
  const [outputCode, setOutputCode] = useState('')
  const [isObfuscating, setIsObfuscating] = useState(false)
  const [copied, setCopied] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [options, setOptions] = useState<ObfuscatorOptions>({
    compact: true,
    controlFlowFlattening: false,
    deadCodeInjection: false,
    debugProtection: false,
    disableConsoleOutput: false,
    identifierNamesGenerator: 'hexadecimal',
    renameGlobals: false,
    rotateStringArray: true,
    selfDefending: false,
    shuffleStringArray: true,
    splitStrings: false,
    stringArray: true,
    stringArrayEncoding: [],
    stringArrayThreshold: 0.75,
    transformObjectKeys: false,
    unicodeEscapeSequence: false,
  })

  // 简单的混淆实现（实际项目中应使用专业的混淆库）
  const obfuscateCode = () => {
    if (!inputCode.trim()) return

    setIsObfuscating(true)
    
    setTimeout(() => {
      try {
        let result = inputCode

        // 1. 字符串数组混淆
        if (options.stringArray) {
          const strings: string[] = []
          const stringMap = new Map<string, number>()
          
          // 提取所有字符串
          result = result.replace(/(["'`])([^"'`]+)\1/g, (match, quote, str) => {
            if (!stringMap.has(str)) {
              stringMap.set(str, strings.length)
              strings.push(str)
            }
            return `_0x${stringMap.get(str)!.toString(16)}()`
          })

          // 创建字符串数组
          if (strings.length > 0) {
            const arrayName = '_0x' + Math.random().toString(16).substr(2, 4)
            const stringArray = options.shuffleStringArray 
              ? strings.sort(() => Math.random() - 0.5)
              : strings

            const arrayDeclaration = `const ${arrayName} = [${stringArray.map(s => `'${s}'`).join(', ')}];`
            
            // 添加解码函数
            const decodeFunctions = Array.from(stringMap.entries()).map(([str, idx]) => {
              return `const _0x${idx.toString(16)} = () => ${arrayName}[${idx}];`
            }).join('\n')

            result = `${arrayDeclaration}\n${decodeFunctions}\n${result}`
          }
        }

        // 2. 变量名混淆
        if (options.renameGlobals) {
          const varMap = new Map<string, string>()
          let varCounter = 0

          // 匹配变量声明
          result = result.replace(/\b(var|let|const|function)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g, (match, keyword, varName) => {
            if (!varMap.has(varName)) {
              const newName = options.identifierNamesGenerator === 'hexadecimal'
                ? `_0x${(varCounter++).toString(16)}`
                : options.identifierNamesGenerator === 'mangled'
                ? `_${String.fromCharCode(97 + varCounter++ % 26)}`
                : `_${Math.random().toString(36).substr(2, 5)}`
              
              varMap.set(varName, newName)
            }
            return `${keyword} ${varMap.get(varName)}`
          })

          // 替换变量引用
          varMap.forEach((newName, oldName) => {
            const regex = new RegExp(`\\b${oldName}\\b`, 'g')
            result = result.replace(regex, newName)
          })
        }

        // 3. 控制流扁平化
        if (options.controlFlowFlattening) {
          // 简单示例：将 if-else 转换为 switch
          result = result.replace(/if\s*\((.*?)\)\s*{([\s\S]*?)}\s*else\s*{([\s\S]*?)}/g, (match, condition, ifBlock, elseBlock) => {
            const switchVar = '_0x' + Math.random().toString(16).substr(2, 4)
            return `const ${switchVar} = (${condition}) ? 1 : 0; switch(${switchVar}) { case 1: {${ifBlock}} break; case 0: {${elseBlock}} break; }`
          })
        }

        // 4. 死代码注入
        if (options.deadCodeInjection) {
          const deadCode = [
            `if (false) { console.log('${Math.random().toString(36)}'); }`,
            `const _unused_${Math.random().toString(36).substr(2, 5)} = ${Math.random()};`,
            `function _dead_${Math.random().toString(36).substr(2, 5)}() { return ${Math.random()}; }`,
          ]
          
          // 随机插入死代码
          const lines = result.split('\n')
          for (let i = 0; i < 3; i++) {
            const randomIndex = Math.floor(Math.random() * lines.length)
            lines.splice(randomIndex, 0, deadCode[i % deadCode.length])
          }
          result = lines.join('\n')
        }

        // 5. 禁用控制台输出
        if (options.disableConsoleOutput) {
          result = `(function(){const _console=console;const _methods=['log','debug','info','warn','error'];_methods.forEach(m=>console[m]=()=>{});})();\n${result}`
        }

        // 6. 调试保护
        if (options.debugProtection) {
          const debugProtectionCode = `
(function() {
  function detectDebugger() {
    const start = Date.now();
    debugger;
    if (Date.now() - start > 100) {
      while (true) { debugger; }
    }
  }
  setInterval(detectDebugger, 1000);
})();`
          result = debugProtectionCode + '\n' + result
        }

        // 7. 自我防御
        if (options.selfDefending) {
          result = `(function(){const _0x${Math.random().toString(16).substr(2, 4)}=function(){const _0x${Math.random().toString(16).substr(2, 4)}=function(){};return _0x${Math.random().toString(16).substr(2, 4)}.toString().indexOf('[native code]')!==-1;};if(!_0x${Math.random().toString(16).substr(2, 4)}()){while(true){}}})();\n${result}`
        }

        // 8. Unicode 转义
        if (options.unicodeEscapeSequence) {
          result = result.replace(/[^\x00-\x7F]/g, (char) => {
            return '\\u' + ('0000' + char.charCodeAt(0).toString(16)).slice(-4)
          })
        }

        // 9. 压缩代码
        if (options.compact) {
          result = result
            .replace(/\/\*[\s\S]*?\*\//g, '') // 删除多行注释
            .replace(/\/\/.*$/gm, '') // 删除单行注释
            .replace(/\s+/g, ' ') // 压缩空白
            .replace(/\s*([{}();,:])\s*/g, '$1') // 删除符号周围的空格
            .trim()
        }

        setOutputCode(result)
      } catch (error) {
        setOutputCode(`// 混淆失败：${error instanceof Error ? error.message : '未知错误'}`)
      } finally {
        setIsObfuscating(false)
      }
    }, 500)
  }

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(outputCode)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 下载混淆后的代码
  const downloadCode = () => {
    const blob = new Blob([outputCode], { type: 'text/javascript' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'obfuscated.js'
    a.click()
    URL.revokeObjectURL(url)
  }

  // 重置选项
  const resetOptions = () => {
    setOptions({
      compact: true,
      controlFlowFlattening: false,
      deadCodeInjection: false,
      debugProtection: false,
      disableConsoleOutput: false,
      identifierNamesGenerator: 'hexadecimal',
      renameGlobals: false,
      rotateStringArray: true,
      selfDefending: false,
      shuffleStringArray: true,
      splitStrings: false,
      stringArray: true,
      stringArrayEncoding: [],
      stringArrayThreshold: 0.75,
      transformObjectKeys: false,
      unicodeEscapeSequence: false,
    })
  }

  // 加载示例代码
  const loadExample = () => {
    setInputCode(`// 示例代码
function calculatePrice(quantity, price) {
  const TAX_RATE = 0.08;
  const subtotal = quantity * price;
  const tax = subtotal * TAX_RATE;
  const total = subtotal + tax;
  
  console.log('Subtotal: $' + subtotal.toFixed(2));
  console.log('Tax: $' + tax.toFixed(2));
  console.log('Total: $' + total.toFixed(2));
  
  return total;
}

const result = calculatePrice(5, 19.99);
document.getElementById('result').textContent = 'Total: $' + result.toFixed(2);`)
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">JavaScript 混淆器</h2>
        <p className="text-gray-600">
          混淆 JavaScript 代码，保护源代码不被轻易理解和逆向工程
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">原始代码</label>
            <button
              onClick={loadExample}
              className="text-sm text-blue-500 hover:text-blue-600"
            >
              加载示例
            </button>
          </div>
          <textarea
            value={inputCode}
            onChange={(e) => setInputCode(e.target.value)}
            placeholder="请输入要混淆的 JavaScript 代码..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">混淆后的代码</label>
            <div className="flex gap-2">
              {outputCode && (
                <>
                  <button
                    onClick={downloadCode}
                    className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                  >
                    <Download className="w-4 h-4" />
                    下载
                  </button>
                  <button
                    onClick={copyToClipboard}
                    className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                  >
                    {copied ? (
                      <>
                        <Check className="w-4 h-4" />
                        已复制
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4" />
                        复制
                      </>
                    )}
                  </button>
                </>
              )}
            </div>
          </div>
          <textarea
            value={outputCode}
            readOnly
            placeholder="混淆后的代码将显示在这里..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none bg-gray-50"
          />
        </div>
      </div>

      {/* 混淆选项 */}
      <div className="mt-6 border-t pt-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">混淆选项</h3>
          <div className="flex gap-2">
            <button
              onClick={resetOptions}
              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 flex items-center gap-1"
            >
              <RotateCcw className="w-4 h-4" />
              重置
            </button>
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="px-3 py-1 text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
            >
              <Settings className="w-4 h-4" />
              {showAdvanced ? '隐藏高级选项' : '显示高级选项'}
            </button>
          </div>
        </div>

        {/* 基础选项 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.compact}
              onChange={(e) => setOptions({ ...options, compact: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">压缩代码</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.stringArray}
              onChange={(e) => setOptions({ ...options, stringArray: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">字符串数组混淆</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.renameGlobals}
              onChange={(e) => setOptions({ ...options, renameGlobals: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">重命名变量</span>
          </label>

          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.disableConsoleOutput}
              onChange={(e) => setOptions({ ...options, disableConsoleOutput: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">禁用控制台输出</span>
          </label>
        </div>

        {/* 高级选项 */}
        {showAdvanced && (
          <div className="mt-4 pt-4 border-t">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={options.controlFlowFlattening}
                  onChange={(e) => setOptions({ ...options, controlFlowFlattening: e.target.checked })}
                  className="rounded text-blue-500 focus:ring-blue-500"
                />
                <span className="text-sm">控制流扁平化</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={options.deadCodeInjection}
                  onChange={(e) => setOptions({ ...options, deadCodeInjection: e.target.checked })}
                  className="rounded text-blue-500 focus:ring-blue-500"
                />
                <span className="text-sm">死代码注入</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={options.debugProtection}
                  onChange={(e) => setOptions({ ...options, debugProtection: e.target.checked })}
                  className="rounded text-blue-500 focus:ring-blue-500"
                />
                <span className="text-sm">调试保护</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={options.selfDefending}
                  onChange={(e) => setOptions({ ...options, selfDefending: e.target.checked })}
                  className="rounded text-blue-500 focus:ring-blue-500"
                />
                <span className="text-sm">自我防御</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={options.shuffleStringArray}
                  onChange={(e) => setOptions({ ...options, shuffleStringArray: e.target.checked })}
                  className="rounded text-blue-500 focus:ring-blue-500"
                />
                <span className="text-sm">随机打乱字符串数组</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={options.unicodeEscapeSequence}
                  onChange={(e) => setOptions({ ...options, unicodeEscapeSequence: e.target.checked })}
                  className="rounded text-blue-500 focus:ring-blue-500"
                />
                <span className="text-sm">Unicode 转义</span>
              </label>

              <div className="col-span-2">
                <label className="block text-sm font-medium mb-2">
                  标识符命名方式
                </label>
                <select
                  value={options.identifierNamesGenerator}
                  onChange={(e) => setOptions({ ...options, identifierNamesGenerator: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="hexadecimal">十六进制 (_0x1234)</option>
                  <option value="mangled">简化 (_a, _b, _c)</option>
                  <option value="mangled-shuffled">随机简化</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 混淆按钮 */}
      <button
        onClick={obfuscateCode}
        disabled={!inputCode.trim() || isObfuscating}
        className="mt-6 w-full py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
      >
        <Shield className="w-5 h-5" />
        {isObfuscating ? '混淆中...' : '混淆代码'}
      </button>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>代码混淆可以增加逆向工程的难度，但不能完全防止</li>
              <li>混淆会增加代码体积和运行开销</li>
              <li>建议保留原始代码副本，混淆后的代码难以维护</li>
              <li>某些混淆选项可能影响代码功能，请充分测试</li>
              <li>生产环境建议使用专业的混淆工具</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 安全提示 */}
      <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-yellow-900">
            <h3 className="font-semibold mb-1">安全提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>混淆不等于加密，敏感信息不应出现在前端代码中</li>
              <li>API 密钥、密码等应该在服务端处理</li>
              <li>混淆可能会被某些杀毒软件误报</li>
              <li>过度混淆可能导致调试困难</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 选项说明 */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">混淆选项说明</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          <div>
            <strong>压缩代码：</strong>
            <span className="text-gray-600">删除空白和注释，减小文件体积</span>
          </div>
          <div>
            <strong>字符串数组混淆：</strong>
            <span className="text-gray-600">将字符串提取到数组中</span>
          </div>
          <div>
            <strong>重命名变量：</strong>
            <span className="text-gray-600">将变量名替换为无意义的名称</span>
          </div>
          <div>
            <strong>控制流扁平化：</strong>
            <span className="text-gray-600">使代码逻辑更难理解</span>
          </div>
          <div>
            <strong>死代码注入：</strong>
            <span className="text-gray-600">添加永不执行的代码迷惑分析</span>
          </div>
          <div>
            <strong>调试保护：</strong>
            <span className="text-gray-600">检测并阻止调试器</span>
          </div>
        </div>
      </div>
    </div>
  )
}
