'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select } from '@/components/ui/select'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Package, Terminal, Plus, Trash2, Play, Download, Search, Settings, ArrowRight, CheckCircle } from 'lucide-react'

interface PackageCommand {
  npm: string
  yarn: string
  pnpm: string
  bun: string
}

interface PackageInfo {
  name: string
  version?: string
  dev?: boolean
  global?: boolean
}

const PackageManagerTool: React.FC = () => {
  const [selectedManager, setSelectedManager] = useState<keyof PackageCommand>('npm')
  const [packageName, setPackageName] = useState('')
  const [packageVersion, setPackageVersion] = useState('')
  const [isDev, setIsDev] = useState(false)
  const [isGlobal, setIsGlobal] = useState(false)
  const [customCommand, setCustomCommand] = useState('')
  const [packages, setPackages] = useState<PackageInfo[]>([])

  // 包管理器配置
  const packageManagers = {
    npm: {
      name: 'npm',
      icon: '📦',
      description: 'Node.js 官方包管理器',
      installCommand: 'npm install',
      color: 'text-red-600'
    },
    yarn: {
      name: 'Yarn',
      icon: '🧶',
      description: 'Facebook 开发的快速包管理器',
      installCommand: 'yarn add',
      color: 'text-blue-600'
    },
    pnpm: {
      name: 'pnpm',
      icon: '⚡',
      description: '高效的磁盘空间利用包管理器',
      installCommand: 'pnpm add',
      color: 'text-yellow-600'
    },
    bun: {
      name: 'Bun',
      icon: '🍞',
      description: '极速的 JavaScript 运行时和包管理器',
      installCommand: 'bun add',
      color: 'text-orange-600'
    }
  }

  // 常用命令映射
  const commonCommands: Record<string, PackageCommand> = {
    install: {
      npm: 'npm install',
      yarn: 'yarn install',
      pnpm: 'pnpm install',
      bun: 'bun install'
    },
    init: {
      npm: 'npm init',
      yarn: 'yarn init',
      pnpm: 'pnpm init',
      bun: 'bun init'
    },
    run: {
      npm: 'npm run',
      yarn: 'yarn run',
      pnpm: 'pnpm run',
      bun: 'bun run'
    },
    test: {
      npm: 'npm test',
      yarn: 'yarn test',
      pnpm: 'pnpm test',
      bun: 'bun test'
    },
    build: {
      npm: 'npm run build',
      yarn: 'yarn build',
      pnpm: 'pnpm run build',
      bun: 'bun run build'
    },
    dev: {
      npm: 'npm run dev',
      yarn: 'yarn dev',
      pnpm: 'pnpm run dev',
      bun: 'bun run dev'
    },
    update: {
      npm: 'npm update',
      yarn: 'yarn upgrade',
      pnpm: 'pnpm update',
      bun: 'bun update'
    },
    outdated: {
      npm: 'npm outdated',
      yarn: 'yarn outdated',
      pnpm: 'pnpm outdated',
      bun: 'bun outdated'
    },
    audit: {
      npm: 'npm audit',
      yarn: 'yarn audit',
      pnpm: 'pnpm audit',
      bun: 'bun audit'
    },
    clean: {
      npm: 'npm cache clean --force',
      yarn: 'yarn cache clean',
      pnpm: 'pnpm store prune',
      bun: 'bun cache clean'
    }
  }

  // 生成安装命令
  const generateInstallCommand = (pkg: PackageInfo): PackageCommand => {
    const versionSuffix = pkg.version ? `@${pkg.version}` : ''
    const packageWithVersion = `${pkg.name}${versionSuffix}`
    
    const commands: PackageCommand = {
      npm: `npm install ${packageWithVersion}`,
      yarn: `yarn add ${packageWithVersion}`,
      pnpm: `pnpm add ${packageWithVersion}`,
      bun: `bun add ${packageWithVersion}`
    }

    if (pkg.dev) {
      commands.npm += ' --save-dev'
      commands.yarn += ' --dev'
      commands.pnpm += ' --save-dev'
      commands.bun += ' --dev'
    }

    if (pkg.global) {
      commands.npm = `npm install ${packageWithVersion} --global`
      commands.yarn = `yarn global add ${packageWithVersion}`
      commands.pnpm = `pnpm add ${packageWithVersion} --global`
      commands.bun = `bun add ${packageWithVersion} --global`
    }

    return commands
  }

  // 生成卸载命令
  const generateUninstallCommand = (pkg: PackageInfo): PackageCommand => {
    const commands: PackageCommand = {
      npm: `npm uninstall ${pkg.name}`,
      yarn: `yarn remove ${pkg.name}`,
      pnpm: `pnpm remove ${pkg.name}`,
      bun: `bun remove ${pkg.name}`
    }

    if (pkg.global) {
      commands.npm += ' --global'
      commands.yarn = `yarn global remove ${pkg.name}`
      commands.pnpm += ' --global'
      commands.bun += ' --global'
    }

    return commands
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 添加包到列表
  const addPackage = () => {
    if (packageName.trim()) {
      const newPackage: PackageInfo = {
        name: packageName.trim(),
        version: packageVersion.trim() || undefined,
        dev: isDev,
        global: isGlobal
      }
      setPackages([...packages, newPackage])
      setPackageName('')
      setPackageVersion('')
      setIsDev(false)
      setIsGlobal(false)
    }
  }

  // 删除包
  const removePackage = (index: number) => {
    setPackages(packages.filter((_, i) => i !== index))
  }

  // 生成批量安装命令
  const generateBatchInstallCommand = (): PackageCommand => {
    const devPackages = packages.filter(pkg => pkg.dev && !pkg.global)
    const prodPackages = packages.filter(pkg => !pkg.dev && !pkg.global)
    const globalPackages = packages.filter(pkg => pkg.global)

    const commands: PackageCommand = { npm: '', yarn: '', pnpm: '', bun: '' }

    // 生产依赖
    if (prodPackages.length > 0) {
      const packageNames = prodPackages.map(pkg => pkg.version ? `${pkg.name}@${pkg.version}` : pkg.name).join(' ')
      commands.npm = `npm install ${packageNames}`
      commands.yarn = `yarn add ${packageNames}`
      commands.pnpm = `pnpm add ${packageNames}`
      commands.bun = `bun add ${packageNames}`
    }

    // 开发依赖
    if (devPackages.length > 0) {
      const packageNames = devPackages.map(pkg => pkg.version ? `${pkg.name}@${pkg.version}` : pkg.name).join(' ')
      const devCommand = {
        npm: `npm install ${packageNames} --save-dev`,
        yarn: `yarn add ${packageNames} --dev`,
        pnpm: `pnpm add ${packageNames} --save-dev`,
        bun: `bun add ${packageNames} --dev`
      }
      
      if (commands.npm) {
        commands.npm += ` && ${devCommand.npm}`
        commands.yarn += ` && ${devCommand.yarn}`
        commands.pnpm += ` && ${devCommand.pnpm}`
        commands.bun += ` && ${devCommand.bun}`
      } else {
        commands.npm = devCommand.npm
        commands.yarn = devCommand.yarn
        commands.pnpm = devCommand.pnpm
        commands.bun = devCommand.bun
      }
    }

    // 全局依赖
    if (globalPackages.length > 0) {
      globalPackages.forEach(pkg => {
        const packageName = pkg.version ? `${pkg.name}@${pkg.version}` : pkg.name
        const globalCommand = {
          npm: `npm install ${packageName} --global`,
          yarn: `yarn global add ${packageName}`,
          pnpm: `pnpm add ${packageName} --global`,
          bun: `bun add ${packageName} --global`
        }
        
        if (commands.npm) {
          commands.npm += ` && ${globalCommand.npm}`
          commands.yarn += ` && ${globalCommand.yarn}`
          commands.pnpm += ` && ${globalCommand.pnpm}`
          commands.bun += ` && ${globalCommand.bun}`
        } else {
          commands.npm = globalCommand.npm
          commands.yarn = globalCommand.yarn
          commands.pnpm = globalCommand.pnpm
          commands.bun = globalCommand.bun
        }
      })
    }

    return commands
  }

  // 命令卡片组件
  const CommandCard: React.FC<{ title: string; command: string; icon: React.ReactNode; description?: string }> = ({ title, command, icon, description }) => (
    <Card className="p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          {icon}
          <div>
            <h3 className="font-medium">{title}</h3>
            {description && <p className="text-sm text-muted-foreground">{description}</p>}
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => copyToClipboard(command)}
          className="flex items-center gap-2"
        >
          <Copy className="w-4 h-4" />
          复制
        </Button>
      </div>
      <div className="mt-3 p-3 bg-muted rounded-lg">
        <code className="text-sm font-mono">{command}</code>
      </div>
    </Card>
  )

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Package className="w-8 h-8" />
          包管理器助手
        </h1>
        <p className="text-muted-foreground">
          快速生成不同包管理器的命令，支持 npm、yarn、pnpm、bun
        </p>
      </div>

      {/* 包管理器选择 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">选择包管理器</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(packageManagers).map(([key, manager]) => (
            <button
              key={key}
              onClick={() => setSelectedManager(key as keyof PackageCommand)}
              className={`p-4 rounded-lg border transition-all ${
                selectedManager === key
                  ? 'border-primary bg-primary/10'
                  : 'border-muted hover:border-muted-foreground/50'
              }`}
            >
              <div className="text-2xl mb-2">{manager.icon}</div>
              <div className={`font-medium ${manager.color}`}>{manager.name}</div>
              <div className="text-xs text-muted-foreground mt-1">{manager.description}</div>
            </button>
          ))}
        </div>
      </Card>

      <Tabs defaultValue="common" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="common">常用命令</TabsTrigger>
          <TabsTrigger value="packages">包管理</TabsTrigger>
          <TabsTrigger value="custom">自定义</TabsTrigger>
        </TabsList>

        <TabsContent value="common" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">常用命令</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <CommandCard
                title="初始化项目"
                command={commonCommands.init[selectedManager]}
                icon={<Play className="w-5 h-5 text-green-600" />}
                description="创建新的项目和 package.json"
              />
              <CommandCard
                title="安装依赖"
                command={commonCommands.install[selectedManager]}
                icon={<Download className="w-5 h-5 text-blue-600" />}
                description="安装 package.json 中的所有依赖"
              />
              <CommandCard
                title="运行脚本"
                command={`${commonCommands.run[selectedManager]} <script-name>`}
                icon={<Terminal className="w-5 h-5 text-purple-600" />}
                description="运行 package.json 中定义的脚本"
              />
              <CommandCard
                title="运行测试"
                command={commonCommands.test[selectedManager]}
                icon={<CheckCircle className="w-5 h-5 text-green-600" />}
                description="运行项目测试"
              />
              <CommandCard
                title="构建项目"
                command={commonCommands.build[selectedManager]}
                icon={<Settings className="w-5 h-5 text-orange-600" />}
                description="构建生产版本"
              />
              <CommandCard
                title="开发模式"
                command={commonCommands.dev[selectedManager]}
                icon={<Play className="w-5 h-5 text-blue-600" />}
                description="启动开发服务器"
              />
              <CommandCard
                title="更新依赖"
                command={commonCommands.update[selectedManager]}
                icon={<ArrowRight className="w-5 h-5 text-yellow-600" />}
                description="更新所有依赖到最新版本"
              />
              <CommandCard
                title="检查过时依赖"
                command={commonCommands.outdated[selectedManager]}
                icon={<Search className="w-5 h-5 text-red-600" />}
                description="检查哪些包有新版本"
              />
              <CommandCard
                title="安全审计"
                command={commonCommands.audit[selectedManager]}
                icon={<Settings className="w-5 h-5 text-red-600" />}
                description="检查已知的安全漏洞"
              />
              <CommandCard
                title="清理缓存"
                command={commonCommands.clean[selectedManager]}
                icon={<Trash2 className="w-5 h-5 text-gray-600" />}
                description="清理包管理器缓存"
              />
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="packages" className="space-y-6">
          {/* 添加包 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">添加包</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>包名</Label>
                <Input
                  value={packageName}
                  onChange={(e) => setPackageName(e.target.value)}
                  placeholder="例如：react、lodash、express"
                />
              </div>
              <div className="space-y-2">
                <Label>版本 (可选)</Label>
                <Input
                  value={packageVersion}
                  onChange={(e) => setPackageVersion(e.target.value)}
                  placeholder="例如：^18.0.0、latest"
                />
              </div>
            </div>
            
            <div className="flex gap-4 mt-4">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={isDev}
                  onChange={(e) => setIsDev(e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">开发依赖</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={isGlobal}
                  onChange={(e) => setIsGlobal(e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">全局安装</span>
              </label>
            </div>

            <Button onClick={addPackage} className="mt-4 flex items-center gap-2">
              <Plus className="w-4 h-4" />
              添加包
            </Button>
          </Card>

          {/* 包列表 */}
          {packages.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">包列表</h3>
              <div className="space-y-2">
                {packages.map((pkg, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="font-mono">{pkg.name}</span>
                      {pkg.version && <span className="text-sm text-muted-foreground">@{pkg.version}</span>}
                      {pkg.dev && <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">dev</span>}
                      {pkg.global && <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">global</span>}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removePackage(index)}
                      className="flex items-center gap-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      删除
                    </Button>
                  </div>
                ))}
              </div>

              {/* 批量安装命令 */}
              <div className="mt-6 space-y-4">
                <h4 className="font-medium">批量安装命令</h4>
                <div className="p-4 bg-muted rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-mono text-sm">{packageManagers[selectedManager].name}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(generateBatchInstallCommand()[selectedManager])}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                  <code className="text-sm block whitespace-pre-wrap">
                    {generateBatchInstallCommand()[selectedManager]}
                  </code>
                </div>
              </div>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="custom" className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">自定义命令</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>输入命令模板</Label>
                <Input
                  value={customCommand}
                  onChange={(e) => setCustomCommand(e.target.value)}
                  placeholder="例如：install react @types/react"
                />
                <p className="text-sm text-muted-foreground">
                  输入不含包管理器名称的命令，系统会自动转换为不同包管理器的格式
                </p>
              </div>

              {customCommand && (
                <div className="space-y-3">
                  <h4 className="font-medium">生成的命令</h4>
                  {Object.entries(packageManagers).map(([key, manager]) => (
                    <div key={key} className="p-3 bg-muted rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className={`font-medium ${manager.color}`}>{manager.name}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(`${manager.installCommand.split(' ')[0]} ${customCommand}`)}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                      <code className="text-sm">
                        {manager.installCommand.split(' ')[0]} {customCommand}
                      </code>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 包管理器对比 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">包管理器对比</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">特性</th>
                <th className="text-left p-2">npm</th>
                <th className="text-left p-2">Yarn</th>
                <th className="text-left p-2">pnpm</th>
                <th className="text-left p-2">Bun</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="p-2">安装速度</td>
                <td className="p-2">中等</td>
                <td className="p-2">快</td>
                <td className="p-2">最快</td>
                <td className="p-2">最快</td>
              </tr>
              <tr className="border-b">
                <td className="p-2">磁盘空间</td>
                <td className="p-2">多</td>
                <td className="p-2">多</td>
                <td className="p-2">少</td>
                <td className="p-2">少</td>
              </tr>
              <tr className="border-b">
                <td className="p-2">锁文件</td>
                <td className="p-2">package-lock.json</td>
                <td className="p-2">yarn.lock</td>
                <td className="p-2">pnpm-lock.yaml</td>
                <td className="p-2">bun.lockb</td>
              </tr>
              <tr className="border-b">
                <td className="p-2">兼容性</td>
                <td className="p-2">最好</td>
                <td className="p-2">好</td>
                <td className="p-2">好</td>
                <td className="p-2">新</td>
              </tr>
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  )
}

export default PackageManagerTool
