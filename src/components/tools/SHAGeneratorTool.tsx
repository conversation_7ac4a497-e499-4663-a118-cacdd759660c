'use client'

import { useState } from 'react'
import { Shield, Copy, Check, FileText, Hash, AlertCircle, Info } from 'lucide-react'

type HashType = 'SHA-1' | 'SHA-256' | 'SHA-384' | 'SHA-512'

interface HashResult {
  type: HashType
  value: string
  length: number
}

export default function SHAGeneratorTool() {
  const [input, setInput] = useState('')
  const [inputType, setInputType] = useState<'text' | 'file'>('text')
  const [hashResults, setHashResults] = useState<HashResult[]>([])
  const [isHashing, setIsHashing] = useState(false)
  const [copiedHash, setCopiedHash] = useState<string | null>(null)
  const [error, setError] = useState('')

  // 将字符串转换为 ArrayBuffer
  const stringToArrayBuffer = (str: string): ArrayBuffer => {
    const encoder = new TextEncoder()
    const uint8Array = encoder.encode(str)
    const buffer = new ArrayBuffer(uint8Array.length)
    const view = new Uint8Array(buffer)
    for (let i = 0; i < uint8Array.length; i++) {
      view[i] = uint8Array[i]
    }
    return buffer
  }

  // 将 ArrayBuffer 转换为十六进制字符串
  const arrayBufferToHex = (buffer: ArrayBuffer): string => {
    const byteArray = new Uint8Array(buffer)
    const hexCodes: string[] = []
    for (let i = 0; i < byteArray.length; i++) {
      const hexCode = byteArray[i].toString(16)
      const paddedHexCode = hexCode.padStart(2, '0')
      hexCodes.push(paddedHexCode)
    }
    return hexCodes.join('')
  }

  // 生成单个哈希值
  const generateHash = async (data: ArrayBuffer, algorithm: HashType): Promise<string> => {
    const hashBuffer = await crypto.subtle.digest(algorithm, data)
    return arrayBufferToHex(hashBuffer)
  }

  // 生成所有类型的哈希值
  const generateAllHashes = async () => {
    if (!input.trim() && inputType === 'text') {
      setError('请输入要加密的文本')
      return
    }

    setIsHashing(true)
    setError('')

    try {
      const data = inputType === 'text' ? stringToArrayBuffer(input) : stringToArrayBuffer(input)
      
      const hashTypes: HashType[] = ['SHA-1', 'SHA-256', 'SHA-384', 'SHA-512']
      const results: HashResult[] = []

      for (const type of hashTypes) {
        const hash = await generateHash(data, type)
        results.push({
          type,
          value: hash,
          length: hash.length / 2 // 每两个十六进制字符表示一个字节
        })
      }

      setHashResults(results)
    } catch (err) {
      setError('生成哈希值时出错：' + (err as Error).message)
    } finally {
      setIsHashing(false)
    }
  }

  // 处理文件上传
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsHashing(true)
    setError('')

    try {
      const buffer = await file.arrayBuffer()
      
      const hashTypes: HashType[] = ['SHA-1', 'SHA-256', 'SHA-384', 'SHA-512']
      const results: HashResult[] = []

      for (const type of hashTypes) {
        const hash = await generateHash(buffer, type)
        results.push({
          type,
          value: hash,
          length: hash.length / 2
        })
      }

      setHashResults(results)
      setInput(file.name) // 显示文件名
    } catch (err) {
      setError('处理文件时出错：' + (err as Error).message)
    } finally {
      setIsHashing(false)
    }
  }

  // 复制到剪贴板
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedHash(type)
      setTimeout(() => setCopiedHash(null), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 加载示例文本
  const loadExample = () => {
    setInput('Hello, World!')
    setInputType('text')
  }

  // 获取算法描述
  const getAlgorithmInfo = (type: HashType): string => {
    switch (type) {
      case 'SHA-1':
        return '160位哈希值，已不安全，仅用于兼容性'
      case 'SHA-256':
        return '256位哈希值，目前最常用的SHA算法'
      case 'SHA-384':
        return '384位哈希值，SHA-512的截断版本'
      case 'SHA-512':
        return '512位哈希值，提供最高级别的安全性'
      default:
        return ''
    }
  }

  // 获取算法推荐级别
  const getSecurityLevel = (type: HashType): { level: string; color: string } => {
    switch (type) {
      case 'SHA-1':
        return { level: '不推荐', color: 'text-red-600 bg-red-50' }
      case 'SHA-256':
        return { level: '推荐', color: 'text-green-600 bg-green-50' }
      case 'SHA-384':
        return { level: '安全', color: 'text-blue-600 bg-blue-50' }
      case 'SHA-512':
        return { level: '最安全', color: 'text-purple-600 bg-purple-50' }
      default:
        return { level: '', color: '' }
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">SHA 哈希生成器</h2>
        <p className="text-gray-600">
          生成文本或文件的 SHA 哈希值，支持 SHA-1、SHA-256、SHA-384 和 SHA-512
        </p>
      </div>

      {/* 输入类型选择 */}
      <div className="mb-6">
        <div className="flex gap-4 mb-4">
          <button
            onClick={() => setInputType('text')}
            className={`px-4 py-2 rounded-md transition-colors ${
              inputType === 'text'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <FileText className="w-4 h-4 inline mr-2" />
            文本输入
          </button>
          <button
            onClick={() => setInputType('file')}
            className={`px-4 py-2 rounded-md transition-colors ${
              inputType === 'file'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Hash className="w-4 h-4 inline mr-2" />
            文件校验
          </button>
        </div>

        {/* 输入区域 */}
        {inputType === 'text' ? (
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium">输入文本</label>
              <button
                onClick={loadExample}
                className="text-sm text-blue-500 hover:text-blue-600"
              >
                加载示例
              </button>
            </div>
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="请输入要生成哈希值的文本..."
              className="w-full h-32 p-4 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        ) : (
          <div>
            <label className="block text-sm font-medium mb-2">选择文件</label>
            <input
              type="file"
              onChange={handleFileUpload}
              className="w-full p-3 border border-gray-300 rounded-lg file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
            {input && inputType === 'file' && (
              <p className="mt-2 text-sm text-gray-600">已选择文件：{input}</p>
            )}
          </div>
        )}
      </div>

      {/* 生成按钮 */}
      <button
        onClick={generateAllHashes}
        disabled={isHashing || (inputType === 'text' && !input.trim())}
        className="w-full py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
      >
        <Shield className="w-5 h-5" />
        {isHashing ? '生成中...' : '生成哈希值'}
      </button>

      {/* 错误提示 */}
      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-800">
          <AlertCircle className="w-4 h-4 flex-shrink-0" />
          {error}
        </div>
      )}

      {/* 结果显示 */}
      {hashResults.length > 0 && (
        <div className="mt-6 space-y-4">
          <h3 className="text-lg font-semibold">哈希结果</h3>
          {hashResults.map((result) => {
            const security = getSecurityLevel(result.type)
            return (
              <div key={result.type} className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <h4 className="font-medium">{result.type}</h4>
                    <span className={`px-2 py-1 rounded-md text-xs ${security.color}`}>
                      {security.level}
                    </span>
                    <span className="text-xs text-gray-500">
                      {result.length} 字节
                    </span>
                  </div>
                  <button
                    onClick={() => copyToClipboard(result.value, result.type)}
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                    title="复制哈希值"
                  >
                    {copiedHash === result.type ? (
                      <Check className="w-5 h-5 text-green-500" />
                    ) : (
                      <Copy className="w-5 h-5" />
                    )}
                  </button>
                </div>
                <p className="text-xs text-gray-600 mb-2">
                  {getAlgorithmInfo(result.type)}
                </p>
                <div className="bg-gray-50 p-3 rounded-md break-all font-mono text-sm">
                  {result.value}
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>SHA（安全哈希算法）用于生成数据的数字指纹</li>
              <li>相同的输入总是产生相同的哈希值</li>
              <li>哈希值是单向的，无法从哈希值反推原始数据</li>
              <li>SHA-256 是目前最常用的算法，适合大多数场景</li>
              <li>文件校验功能可用于验证文件完整性</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 安全提示 */}
      <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-yellow-900">
            <h3 className="font-semibold mb-1">安全提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>SHA-1 已被证明不够安全，避免在新项目中使用</li>
              <li>对于密码存储，应使用专门的密码哈希函数（如 bcrypt、Argon2）</li>
              <li>哈希值可以公开分享，不会泄露原始数据</li>
              <li>大文件的哈希计算可能需要较长时间</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 常见用途 */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">常见用途</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          <div className="flex items-start">
            <span className="text-gray-400 mr-2">•</span>
            <div>
              <strong>文件完整性验证</strong>
              <p className="text-gray-600">确保下载的文件未被篡改</p>
            </div>
          </div>
          <div className="flex items-start">
            <span className="text-gray-400 mr-2">•</span>
            <div>
              <strong>数字签名</strong>
              <p className="text-gray-600">用于创建和验证数字签名</p>
            </div>
          </div>
          <div className="flex items-start">
            <span className="text-gray-400 mr-2">•</span>
            <div>
              <strong>数据去重</strong>
              <p className="text-gray-600">通过比较哈希值识别重复数据</p>
            </div>
          </div>
          <div className="flex items-start">
            <span className="text-gray-400 mr-2">•</span>
            <div>
              <strong>区块链技术</strong>
              <p className="text-gray-600">作为区块链的核心加密技术</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
