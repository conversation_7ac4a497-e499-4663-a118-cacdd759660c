'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Download, Upload, Edit, Trash2, Plus, Search, Tag, Star, StarOff, Code, FileText, Zap, CheckCircle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CodeSnippet {
  id: string
  title: string
  description: string
  code: string
  language: string
  tags: string[]
  category: string
  isStarred: boolean
  createdAt: Date
  updatedAt: Date
}

interface SnippetCollection {
  id: string
  name: string
  description: string
  snippets: CodeSnippet[]
}

const LANGUAGES = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'cpp', label: 'C++' },
  { value: 'csharp', label: 'C#' },
  { value: 'go', label: 'Go' },
  { value: 'rust', label: 'Rust' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'html', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'scss', label: 'SCSS' },
  { value: 'sql', label: 'SQL' },
  { value: 'bash', label: 'Bash' },
  { value: 'json', label: 'JSON' },
  { value: 'xml', label: 'XML' },
  { value: 'yaml', label: 'YAML' },
  { value: 'markdown', label: 'Markdown' },
  { value: 'text', label: 'Plain Text' },
]

const CATEGORIES = [
  { value: 'functions', label: '函数/方法' },
  { value: 'components', label: '组件' },
  { value: 'utils', label: '工具函数' },
  { value: 'hooks', label: 'Hooks' },
  { value: 'configs', label: '配置' },
  { value: 'templates', label: '模板' },
  { value: 'algorithms', label: '算法' },
  { value: 'patterns', label: '设计模式' },
  { value: 'snippets', label: '代码片段' },
  { value: 'examples', label: '示例代码' },
]

const DEFAULT_SNIPPETS: CodeSnippet[] = [
  {
    id: '1',
    title: 'React 函数组件模板',
    description: '标准的 React 函数组件模板',
    code: `import React from 'react'

interface Props {
  // 定义属性类型
}

const ComponentName: React.FC<Props> = ({ }) => {
  return (
    <div>
      {/* 组件内容 */}
    </div>
  )
}

export default ComponentName`,
    language: 'typescript',
    tags: ['react', 'typescript', 'component', 'template'],
    category: 'components',
    isStarred: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '2',
    title: 'useState Hook',
    description: 'React useState Hook 使用示例',
    code: `const [state, setState] = useState(initialValue)

// 更新状态
setState(newValue)

// 函数式更新
setState(prevState => prevState + 1)`,
    language: 'javascript',
    tags: ['react', 'hooks', 'state'],
    category: 'hooks',
    isStarred: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '3',
    title: 'Promise 包装器',
    description: '将回调函数转换为 Promise 的工具函数',
    code: `function promisify(fn) {
  return function(...args) {
    return new Promise((resolve, reject) => {
      fn(...args, (err, result) => {
        if (err) {
          reject(err)
        } else {
          resolve(result)
        }
      })
    })
  }
}

// 使用示例
const readFileAsync = promisify(fs.readFile)
const data = await readFileAsync('file.txt', 'utf8')`,
    language: 'javascript',
    tags: ['javascript', 'promise', 'async', 'utils'],
    category: 'utils',
    isStarred: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

export default function CodeSnippetManagerTool() {
  const [snippets, setSnippets] = useState<CodeSnippet[]>(DEFAULT_SNIPPETS)
  const [filteredSnippets, setFilteredSnippets] = useState<CodeSnippet[]>(DEFAULT_SNIPPETS)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedLanguage, setSelectedLanguage] = useState<string>('all')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [showStarredOnly, setShowStarredOnly] = useState(false)
  const [activeTab, setActiveTab] = useState('browse')
  const [editingSnippet, setEditingSnippet] = useState<CodeSnippet | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const { toast } = useToast()

  // 新建片段的表单状态
  const [newSnippet, setNewSnippet] = useState({
    title: '',
    description: '',
    code: '',
    language: 'javascript',
    tags: '',
    category: 'snippets'
  })

  // 过滤代码片段
  const filterSnippets = useCallback(() => {
    let filtered = [...snippets]

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(snippet => 
        snippet.title.toLowerCase().includes(query) ||
        snippet.description.toLowerCase().includes(query) ||
        snippet.code.toLowerCase().includes(query) ||
        snippet.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }

    // 语言过滤
    if (selectedLanguage !== 'all') {
      filtered = filtered.filter(snippet => snippet.language === selectedLanguage)
    }

    // 分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(snippet => snippet.category === selectedCategory)
    }

    // 收藏过滤
    if (showStarredOnly) {
      filtered = filtered.filter(snippet => snippet.isStarred)
    }

    // 按更新时间排序
    filtered.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())

    setFilteredSnippets(filtered)
  }, [snippets, searchQuery, selectedLanguage, selectedCategory, showStarredOnly])

  useEffect(() => {
    filterSnippets()
  }, [filterSnippets])

  // 保存到本地存储
  useEffect(() => {
    localStorage.setItem('code-snippets', JSON.stringify(snippets))
  }, [snippets])

  // 从本地存储加载
  useEffect(() => {
    const saved = localStorage.getItem('code-snippets')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setSnippets(parsed.map((s: any) => ({
          ...s,
          createdAt: new Date(s.createdAt),
          updatedAt: new Date(s.updatedAt)
        })))
      } catch (error) {
        console.error('加载代码片段失败:', error)
      }
    }
  }, [])

  // 创建新片段
  const handleCreateSnippet = useCallback(() => {
    if (!newSnippet.title.trim() || !newSnippet.code.trim()) {
      toast({
        title: '请填写必要信息',
        description: '标题和代码内容不能为空',
        variant: 'destructive',
      })
      return
    }

    const snippet: CodeSnippet = {
      id: Date.now().toString(),
      title: newSnippet.title.trim(),
      description: newSnippet.description.trim(),
      code: newSnippet.code.trim(),
      language: newSnippet.language,
      tags: newSnippet.tags.split(',').map(t => t.trim()).filter(t => t),
      category: newSnippet.category,
      isStarred: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setSnippets(prev => [snippet, ...prev])
    setNewSnippet({
      title: '',
      description: '',
      code: '',
      language: 'javascript',
      tags: '',
      category: 'snippets'
    })
    setIsCreating(false)
    setActiveTab('browse')

    toast({
      title: '创建成功',
      description: '代码片段已创建',
    })
  }, [newSnippet, toast])

  // 编辑片段
  const handleEditSnippet = useCallback((snippet: CodeSnippet) => {
    setEditingSnippet(snippet)
    setActiveTab('edit')
  }, [])

  // 保存编辑
  const handleSaveEdit = useCallback(() => {
    if (!editingSnippet) return

    setSnippets(prev => 
      prev.map(s => 
        s.id === editingSnippet.id 
          ? { ...editingSnippet, updatedAt: new Date() }
          : s
      )
    )
    setEditingSnippet(null)
    setActiveTab('browse')

    toast({
      title: '保存成功',
      description: '代码片段已更新',
    })
  }, [editingSnippet, toast])

  // 删除片段
  const handleDeleteSnippet = useCallback((id: string) => {
    setSnippets(prev => prev.filter(s => s.id !== id))
    toast({
      title: '删除成功',
      description: '代码片段已删除',
    })
  }, [toast])

  // 切换收藏状态
  const handleToggleStar = useCallback((id: string) => {
    setSnippets(prev => 
      prev.map(s => 
        s.id === id 
          ? { ...s, isStarred: !s.isStarred, updatedAt: new Date() }
          : s
      )
    )
  }, [])

  // 复制代码
  const handleCopyCode = useCallback((code: string, title: string) => {
    navigator.clipboard.writeText(code).then(() => {
      toast({
        title: '复制成功',
        description: `"${title}" 的代码已复制到剪贴板`,
      })
    }).catch(() => {
      toast({
        title: '复制失败',
        description: '请手动选择并复制',
        variant: 'destructive',
      })
    })
  }, [toast])

  // 导出片段
  const handleExport = useCallback(() => {
    const data = {
      version: '1.0.0',
      exportTime: new Date().toISOString(),
      snippets: snippets
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `code-snippets-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: '导出成功',
      description: '代码片段已导出',
    })
  }, [snippets, toast])

  // 导入片段
  const handleImport = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        if (data.snippets && Array.isArray(data.snippets)) {
          const importedSnippets = data.snippets.map((s: any) => ({
            ...s,
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            createdAt: new Date(s.createdAt),
            updatedAt: new Date(s.updatedAt)
          }))
          setSnippets(prev => [...importedSnippets, ...prev])
          toast({
            title: '导入成功',
            description: `已导入 ${importedSnippets.length} 个代码片段`,
          })
        } else {
          throw new Error('文件格式不正确')
        }
      } catch (error) {
        toast({
          title: '导入失败',
          description: '请检查文件格式是否正确',
          variant: 'destructive',
        })
      }
    }
    reader.readAsText(file)
    event.target.value = ''
  }, [toast])

  return (
    <div className="w-full max-w-7xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            代码片段管理器
          </CardTitle>
          <CardDescription>
            管理和组织您的代码片段，支持多种编程语言和标签分类
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="browse">浏览片段</TabsTrigger>
              <TabsTrigger value="create">创建片段</TabsTrigger>
              <TabsTrigger value="edit" disabled={!editingSnippet}>编辑片段</TabsTrigger>
            </TabsList>

            <TabsContent value="browse" className="space-y-4">
              {/* 搜索和过滤 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索片段..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择语言" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有语言</SelectItem>
                    {LANGUAGES.map(lang => (
                      <SelectItem key={lang.value} value={lang.value}>
                        {lang.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有分类</SelectItem>
                    {CATEGORIES.map(cat => (
                      <SelectItem key={cat.value} value={cat.value}>
                        {cat.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <div className="flex items-center gap-2">
                  <Button
                    variant={showStarredOnly ? "default" : "outline"}
                    size="sm"
                    onClick={() => setShowStarredOnly(!showStarredOnly)}
                  >
                    <Star className="h-4 w-4 mr-2" />
                    收藏
                  </Button>
                  <Button onClick={handleExport} variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    导出
                  </Button>
                  <div className="relative">
                    <Button variant="outline" size="sm" onClick={() => document.getElementById('import-input')?.click()}>
                      <Upload className="h-4 w-4 mr-2" />
                      导入
                    </Button>
                    <input
                      id="import-input"
                      type="file"
                      accept=".json"
                      onChange={handleImport}
                      className="hidden"
                    />
                  </div>
                </div>
              </div>

              {/* 片段列表 */}
              <div className="space-y-4">
                {filteredSnippets.length === 0 ? (
                  <Card className="p-8 text-center">
                    <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">
                      {searchQuery || selectedLanguage !== 'all' || selectedCategory !== 'all' || showStarredOnly 
                        ? '没有找到匹配的代码片段' 
                        : '还没有代码片段，点击"创建片段"开始添加'
                      }
                    </p>
                  </Card>
                ) : (
                  filteredSnippets.map(snippet => (
                    <Card key={snippet.id} className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold">{snippet.title}</h3>
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              {LANGUAGES.find(l => l.value === snippet.language)?.label}
                            </span>
                            <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">
                              {CATEGORIES.find(c => c.value === snippet.category)?.label}
                            </span>
                          </div>
                          {snippet.description && (
                            <p className="text-sm text-muted-foreground mb-2">{snippet.description}</p>
                          )}
                          {snippet.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-2">
                              {snippet.tags.map(tag => (
                                <span key={tag} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded flex items-center gap-1">
                                  <Tag className="h-3 w-3" />
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleStar(snippet.id)}
                          >
                            {snippet.isStarred ? (
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            ) : (
                              <StarOff className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditSnippet(snippet)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteSnippet(snippet.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-3">
                        <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                          <code>{snippet.code}</code>
                        </pre>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">
                          更新于 {snippet.updatedAt.toLocaleDateString()}
                        </span>
                        <Button
                          size="sm"
                          onClick={() => handleCopyCode(snippet.code, snippet.title)}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          复制代码
                        </Button>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="create" className="space-y-4">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">创建新的代码片段</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="title">片段标题 *</Label>
                      <Input
                        id="title"
                        value={newSnippet.title}
                        onChange={(e) => setNewSnippet(prev => ({ ...prev, title: e.target.value }))}
                        placeholder="输入片段标题"
                      />
                    </div>
                    <div>
                      <Label htmlFor="language">编程语言</Label>
                      <Select value={newSnippet.language} onValueChange={(value) => setNewSnippet(prev => ({ ...prev, language: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {LANGUAGES.map(lang => (
                            <SelectItem key={lang.value} value={lang.value}>
                              {lang.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">描述</Label>
                    <Input
                      id="description"
                      value={newSnippet.description}
                      onChange={(e) => setNewSnippet(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="简要描述片段用途"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="category">分类</Label>
                      <Select value={newSnippet.category} onValueChange={(value) => setNewSnippet(prev => ({ ...prev, category: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {CATEGORIES.map(cat => (
                            <SelectItem key={cat.value} value={cat.value}>
                              {cat.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="tags">标签</Label>
                      <Input
                        id="tags"
                        value={newSnippet.tags}
                        onChange={(e) => setNewSnippet(prev => ({ ...prev, tags: e.target.value }))}
                        placeholder="使用逗号分隔多个标签"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="code">代码内容 *</Label>
                    <Textarea
                      id="code"
                      value={newSnippet.code}
                      onChange={(e) => setNewSnippet(prev => ({ ...prev, code: e.target.value }))}
                      placeholder="粘贴或输入代码"
                      className="min-h-[300px] font-mono"
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setNewSnippet({
                        title: '',
                        description: '',
                        code: '',
                        language: 'javascript',
                        tags: '',
                        category: 'snippets'
                      })}
                    >
                      清空
                    </Button>
                    <Button onClick={handleCreateSnippet}>
                      <Plus className="h-4 w-4 mr-2" />
                      创建片段
                    </Button>
                  </div>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="edit" className="space-y-4">
              {editingSnippet && (
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4">编辑代码片段</h3>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="edit-title">片段标题</Label>
                        <Input
                          id="edit-title"
                          value={editingSnippet.title}
                          onChange={(e) => setEditingSnippet(prev => prev ? ({ ...prev, title: e.target.value }) : null)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="edit-language">编程语言</Label>
                        <Select value={editingSnippet.language} onValueChange={(value) => setEditingSnippet(prev => prev ? ({ ...prev, language: value }) : null)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {LANGUAGES.map(lang => (
                              <SelectItem key={lang.value} value={lang.value}>
                                {lang.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="edit-description">描述</Label>
                      <Input
                        id="edit-description"
                        value={editingSnippet.description}
                        onChange={(e) => setEditingSnippet(prev => prev ? ({ ...prev, description: e.target.value }) : null)}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="edit-category">分类</Label>
                        <Select value={editingSnippet.category} onValueChange={(value) => setEditingSnippet(prev => prev ? ({ ...prev, category: value }) : null)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {CATEGORIES.map(cat => (
                              <SelectItem key={cat.value} value={cat.value}>
                                {cat.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="edit-tags">标签</Label>
                        <Input
                          id="edit-tags"
                          value={editingSnippet.tags.join(', ')}
                          onChange={(e) => setEditingSnippet(prev => prev ? ({ ...prev, tags: e.target.value.split(',').map(t => t.trim()).filter(t => t) }) : null)}
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="edit-code">代码内容</Label>
                      <Textarea
                        id="edit-code"
                        value={editingSnippet.code}
                        onChange={(e) => setEditingSnippet(prev => prev ? ({ ...prev, code: e.target.value }) : null)}
                        className="min-h-[300px] font-mono"
                      />
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setEditingSnippet(null)
                          setActiveTab('browse')
                        }}
                      >
                        取消
                      </Button>
                      <Button onClick={handleSaveEdit}>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        保存修改
                      </Button>
                    </div>
                  </div>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card className="bg-muted/50">
        <CardHeader>
          <CardTitle className="text-lg">使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm text-muted-foreground">
            <div>
              <h4 className="font-medium text-foreground mb-2">主要功能</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li><strong>代码片段管理</strong>：创建、编辑、删除代码片段</li>
                <li><strong>智能搜索</strong>：支持标题、描述、代码、标签全文搜索</li>
                <li><strong>多维度过滤</strong>：按语言、分类、收藏状态过滤</li>
                <li><strong>标签系统</strong>：灵活的标签分类管理</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-foreground mb-2">支持特性</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>支持20多种编程语言高亮显示</li>
                <li>收藏功能，快速访问常用片段</li>
                <li>数据导入导出，支持备份和迁移</li>
                <li>本地存储，保护隐私数据</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-foreground mb-2">使用建议</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>为每个片段添加描述性标题和说明</li>
                <li>使用合适的标签便于后续查找</li>
                <li>定期整理和更新代码片段</li>
                <li>利用搜索功能快速定位所需片段</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
