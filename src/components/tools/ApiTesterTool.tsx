'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Send, Copy, Trash2, Plus, X, Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Header {
  key: string
  value: string
}

interface QueryParam {
  key: string
  value: string
}

interface Response {
  status: number
  statusText: string
  headers: Record<string, string>
  data: any
  time: number
}

export default function ApiTesterTool() {
  const [method, setMethod] = useState<'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'>('GET')
  const [url, setUrl] = useState('')
  const [headers, setHeaders] = useState<Header[]>([
    { key: 'Content-Type', value: 'application/json' }
  ])
  const [queryParams, setQueryParams] = useState<QueryParam[]>([])
  const [body, setBody] = useState('')
  const [response, setResponse] = useState<Response | null>(null)
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('params')
  const { toast } = useToast()

  // 添加请求头
  const addHeader = useCallback(() => {
    setHeaders([...headers, { key: '', value: '' }])
  }, [headers])

  // 更新请求头
  const updateHeader = useCallback((index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...headers]
    newHeaders[index][field] = value
    setHeaders(newHeaders)
  }, [headers])

  // 删除请求头
  const removeHeader = useCallback((index: number) => {
    setHeaders(headers.filter((_, i) => i !== index))
  }, [headers])

  // 添加查询参数
  const addQueryParam = useCallback(() => {
    setQueryParams([...queryParams, { key: '', value: '' }])
  }, [queryParams])

  // 更新查询参数
  const updateQueryParam = useCallback((index: number, field: 'key' | 'value', value: string) => {
    const newParams = [...queryParams]
    newParams[index][field] = value
    setQueryParams(newParams)
  }, [queryParams])

  // 删除查询参数
  const removeQueryParam = useCallback((index: number) => {
    setQueryParams(queryParams.filter((_, i) => i !== index))
  }, [queryParams])

  // 构建完整的URL（包含查询参数）
  const buildUrl = useCallback(() => {
    if (!url) return ''
    
    const validParams = queryParams.filter(p => p.key)
    if (validParams.length === 0) return url
    
    const urlObj = new URL(url)
    validParams.forEach(param => {
      urlObj.searchParams.append(param.key, param.value)
    })
    
    return urlObj.toString()
  }, [url, queryParams])

  // 发送请求
  const sendRequest = useCallback(async () => {
    if (!url) {
      toast({
        title: '请输入URL',
        description: '请输入要测试的API地址',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)
    setResponse(null)

    try {
      const startTime = Date.now()
      const fullUrl = buildUrl()
      
      // 构建请求头
      const requestHeaders: Record<string, string> = {}
      headers.forEach(header => {
        if (header.key) {
          requestHeaders[header.key] = header.value
        }
      })

      // 构建请求选项
      const options: RequestInit = {
        method,
        headers: requestHeaders,
      }

      // 添加请求体（仅非GET请求）
      if (method !== 'GET' && body) {
        options.body = body
      }

      // 发送请求
      const res = await fetch(fullUrl, options)
      const endTime = Date.now()

      // 获取响应头
      const responseHeaders: Record<string, string> = {}
      res.headers.forEach((value, key) => {
        responseHeaders[key] = value
      })

      // 解析响应体
      let data
      const contentType = res.headers.get('content-type')
      if (contentType?.includes('application/json')) {
        data = await res.json()
      } else {
        data = await res.text()
      }

      setResponse({
        status: res.status,
        statusText: res.statusText,
        headers: responseHeaders,
        data,
        time: endTime - startTime,
      })

      toast({
        title: '请求成功',
        description: `状态码: ${res.status} ${res.statusText}`,
      })
    } catch (error) {
      toast({
        title: '请求失败',
        description: error instanceof Error ? error.message : '网络错误',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }, [url, method, headers, body, buildUrl, toast])

  // 复制响应
  const copyResponse = useCallback(() => {
    if (!response) return

    const text = JSON.stringify(response.data, null, 2)
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: '复制成功',
        description: '响应内容已复制到剪贴板',
      })
    }).catch(() => {
      toast({
        title: '复制失败',
        description: '请手动选择并复制',
        variant: 'destructive',
      })
    })
  }, [response, toast])

  // 清空所有
  const clearAll = useCallback(() => {
    setUrl('')
    setMethod('GET')
    setHeaders([{ key: 'Content-Type', value: 'application/json' }])
    setQueryParams([])
    setBody('')
    setResponse(null)
    toast({
      title: '已清空',
      description: '所有输入已清空',
    })
  }, [toast])

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            API 测试工具
          </CardTitle>
          <CardDescription>
            测试 REST API 接口，支持各种 HTTP 方法和自定义请求头
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 请求配置 */}
          <div className="space-y-4">
            {/* URL 和方法 */}
            <div className="flex gap-2">
              <Select value={method} onValueChange={(value: any) => setMethod(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GET">GET</SelectItem>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                  <SelectItem value="PATCH">PATCH</SelectItem>
                </SelectContent>
              </Select>
              <Input
                value={url}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setUrl(e.target.value)}
                placeholder="https://api.example.com/endpoint"
                className="flex-1"
              />
              <Button onClick={sendRequest} disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    发送中
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    发送
                  </>
                )}
              </Button>
            </div>

            {/* 请求参数标签页 */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="params">查询参数</TabsTrigger>
                <TabsTrigger value="headers">请求头</TabsTrigger>
                <TabsTrigger value="body">请求体</TabsTrigger>
              </TabsList>

              {/* 查询参数 */}
              <TabsContent value="params" className="space-y-2">
                <div className="flex justify-between items-center mb-2">
                  <Label>查询参数</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={addQueryParam}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    添加参数
                  </Button>
                </div>
                {queryParams.map((param, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={param.key}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateQueryParam(index, 'key', e.target.value)}
                      placeholder="参数名"
                      className="flex-1"
                    />
                    <Input
                      value={param.value}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateQueryParam(index, 'value', e.target.value)}
                      placeholder="参数值"
                      className="flex-1"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeQueryParam(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </TabsContent>

              {/* 请求头 */}
              <TabsContent value="headers" className="space-y-2">
                <div className="flex justify-between items-center mb-2">
                  <Label>请求头</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={addHeader}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    添加请求头
                  </Button>
                </div>
                {headers.map((header, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={header.key}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateHeader(index, 'key', e.target.value)}
                      placeholder="Header名称"
                      className="flex-1"
                    />
                    <Input
                      value={header.value}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateHeader(index, 'value', e.target.value)}
                      placeholder="Header值"
                      className="flex-1"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeHeader(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </TabsContent>

              {/* 请求体 */}
              <TabsContent value="body" className="space-y-2">
                <Label>请求体 (JSON)</Label>
                <Textarea
                  value={body}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setBody(e.target.value)}
                  placeholder='{"key": "value"}'
                  className="min-h-[200px] font-mono text-sm"
                  disabled={method === 'GET'}
                />
                {method === 'GET' && (
                  <p className="text-sm text-muted-foreground">
                    GET 请求不支持请求体
                  </p>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* 响应结果 */}
          {response && (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">响应结果</CardTitle>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyResponse}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      复制
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 响应状态 */}
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label className="text-muted-foreground">状态码</Label>
                    <p className={`text-lg font-semibold ${
                      response.status >= 200 && response.status < 300 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {response.status} {response.statusText}
                    </p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">响应时间</Label>
                    <p className="text-lg font-semibold">{response.time}ms</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">大小</Label>
                    <p className="text-lg font-semibold">
                      {new Blob([JSON.stringify(response.data)]).size} bytes
                    </p>
                  </div>
                </div>

                {/* 响应内容标签页 */}
                <Tabs defaultValue="body">
                  <TabsList>
                    <TabsTrigger value="body">响应体</TabsTrigger>
                    <TabsTrigger value="headers">响应头</TabsTrigger>
                  </TabsList>

                  <TabsContent value="body">
                    <pre className="bg-muted p-4 rounded-lg overflow-auto max-h-96 text-sm">
                      {typeof response.data === 'string' 
                        ? response.data 
                        : JSON.stringify(response.data, null, 2)}
                    </pre>
                  </TabsContent>

                  <TabsContent value="headers">
                    <div className="space-y-2">
                      {Object.entries(response.headers).map(([key, value]) => (
                        <div key={key} className="flex justify-between p-2 bg-muted rounded">
                          <span className="font-medium">{key}</span>
                          <span className="text-muted-foreground">{value}</span>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={clearAll}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              清空所有
            </Button>
          </div>

          {/* 提示信息 */}
          <Card className="bg-muted/50">
            <CardContent className="pt-6">
              <h4 className="font-semibold mb-2">使用提示：</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>支持 GET、POST、PUT、DELETE、PATCH 等 HTTP 方法</li>
                <li>可以自定义请求头和查询参数</li>
                <li>请求体支持 JSON 格式</li>
                <li>响应结果包含状态码、响应时间和响应内容</li>
                <li>由于浏览器安全限制，可能无法访问某些跨域 API</li>
              </ul>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}
