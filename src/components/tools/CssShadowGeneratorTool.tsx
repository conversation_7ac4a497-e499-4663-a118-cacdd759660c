'use client';

import React, { useState, useCallback, useMemo, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { 
  Zap, 
  Copy, 
  Download,
  Plus,
  Minus,
  RotateCw,
  Settings,
  Eye,
  Layers,
  Code,
  Sparkles,
  Palette,
  Grid,
  Target
} from 'lucide-react';

interface ShadowLayer {
  id: string;
  x: number;
  y: number;
  blur: number;
  spread: number;
  color: string;
  opacity: number;
  inset: boolean;
  enabled: boolean;
}

interface ShadowPreset {
  name: string;
  description: string;
  layers: Omit<ShadowLayer, 'id' | 'enabled'>[];
}

const CssShadowGeneratorTool: React.FC = () => {
  const [shadowLayers, setShadowLayers] = useState<ShadowLayer[]>([
    {
      id: '1',
      x: 0,
      y: 4,
      blur: 6,
      spread: 0,
      color: '#000000',
      opacity: 0.1,
      inset: false,
      enabled: true
    }
  ]);
  
  const [selectedLayer, setSelectedLayer] = useState('1');
  const [previewMode, setPreviewMode] = useState<'element' | 'text' | 'card'>('element');
  const [exportFormat, setExportFormat] = useState<'css' | 'scss' | 'tailwind' | 'js'>('css');
  const [elementSize, setElementSize] = useState({ width: 200, height: 120 });
  const [elementColor, setElementColor] = useState('#ffffff');
  const [backgroundColor, setBackgroundColor] = useState('#f0f0f0');
  const [showGrid, setShowGrid] = useState(false);
  const [activeTab, setActiveTab] = useState('generator');
  
  const previewRef = useRef<HTMLDivElement>(null);

  // Shadow presets
  const presets: ShadowPreset[] = [
    {
      name: 'Subtle',
      description: 'Light shadow for cards',
      layers: [
        { x: 0, y: 1, blur: 3, spread: 0, color: '#000000', opacity: 0.1, inset: false }
      ]
    },
    {
      name: 'Medium',
      description: 'Medium elevation shadow',
      layers: [
        { x: 0, y: 4, blur: 6, spread: -1, color: '#000000', opacity: 0.1, inset: false },
        { x: 0, y: 2, blur: 4, spread: -1, color: '#000000', opacity: 0.06, inset: false }
      ]
    },
    {
      name: 'Large',
      description: 'High elevation shadow',
      layers: [
        { x: 0, y: 10, blur: 15, spread: -3, color: '#000000', opacity: 0.1, inset: false },
        { x: 0, y: 4, blur: 6, spread: -2, color: '#000000', opacity: 0.05, inset: false }
      ]
    },
    {
      name: 'Inset',
      description: 'Inner shadow effect',
      layers: [
        { x: 0, y: 2, blur: 4, spread: 0, color: '#000000', opacity: 0.06, inset: true }
      ]
    },
    {
      name: 'Colored',
      description: 'Colorful shadow',
      layers: [
        { x: 0, y: 8, blur: 16, spread: 0, color: '#3b82f6', opacity: 0.3, inset: false }
      ]
    },
    {
      name: 'Neumorphism',
      description: 'Neumorphic design shadow',
      layers: [
        { x: -8, y: -8, blur: 16, spread: 0, color: '#ffffff', opacity: 0.7, inset: false },
        { x: 8, y: 8, blur: 16, spread: 0, color: '#d1d9e6', opacity: 0.7, inset: false }
      ]
    },
    {
      name: 'Floating',
      description: 'Floating button shadow',
      layers: [
        { x: 0, y: 6, blur: 20, spread: 0, color: '#000000', opacity: 0.15, inset: false },
        { x: 0, y: 2, blur: 6, spread: 0, color: '#000000', opacity: 0.1, inset: false }
      ]
    },
    {
      name: 'Intense',
      description: 'Strong dramatic shadow',
      layers: [
        { x: 0, y: 25, blur: 50, spread: -12, color: '#000000', opacity: 0.25, inset: false }
      ]
    }
  ];

  // Generate box-shadow CSS
  const generateBoxShadow = useCallback((layers: ShadowLayer[]): string => {
    const enabledLayers = layers.filter(layer => layer.enabled);
    if (enabledLayers.length === 0) return 'none';

    return enabledLayers
      .map(layer => {
        const { x, y, blur, spread, color, opacity, inset } = layer;
        const shadowColor = `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`;
        const values = `${x}px ${y}px ${blur}px ${spread}px ${shadowColor}`;
        return inset ? `inset ${values}` : values;
      })
      .join(', ');
  }, []);

  const currentBoxShadow = useMemo(() => generateBoxShadow(shadowLayers), [shadowLayers, generateBoxShadow]);

  // Layer management functions
  const addLayer = useCallback(() => {
    const newId = Date.now().toString();
    const newLayer: ShadowLayer = {
      id: newId,
      x: 0,
      y: 4,
      blur: 6,
      spread: 0,
      color: '#000000',
      opacity: 0.1,
      inset: false,
      enabled: true
    };
    
    setShadowLayers(prev => [...prev, newLayer]);
    setSelectedLayer(newId);
  }, []);

  const removeLayer = useCallback((id: string) => {
    if (shadowLayers.length <= 1) return;
    
    setShadowLayers(prev => prev.filter(layer => layer.id !== id));
    
    if (selectedLayer === id) {
      const remainingLayers = shadowLayers.filter(layer => layer.id !== id);
      setSelectedLayer(remainingLayers[0]?.id || '');
    }
  }, [shadowLayers, selectedLayer]);

  const updateLayer = useCallback((id: string, updates: Partial<ShadowLayer>) => {
    setShadowLayers(prev => 
      prev.map(layer => 
        layer.id === id ? { ...layer, ...updates } : layer
      )
    );
  }, []);

  const duplicateLayer = useCallback((id: string) => {
    const layerToDuplicate = shadowLayers.find(layer => layer.id === id);
    if (!layerToDuplicate) return;

    const newId = Date.now().toString();
    const newLayer = { ...layerToDuplicate, id: newId };
    
    setShadowLayers(prev => [...prev, newLayer]);
    setSelectedLayer(newId);
  }, [shadowLayers]);

  // Preset functions
  const applyPreset = useCallback((preset: ShadowPreset) => {
    const newLayers = preset.layers.map((layer, index) => ({
      ...layer,
      id: (Date.now() + index).toString(),
      enabled: true
    }));
    
    setShadowLayers(newLayers);
    setSelectedLayer(newLayers[0]?.id || '');
  }, []);

  // Random shadow generator
  const generateRandomShadow = useCallback(() => {
    const numLayers = 1 + Math.floor(Math.random() * 3);
    const colors = ['#000000', '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'];
    
    const newLayers: ShadowLayer[] = [];
    
    for (let i = 0; i < numLayers; i++) {
      newLayers.push({
        id: (Date.now() + i).toString(),
        x: Math.floor(Math.random() * 20) - 10,
        y: Math.floor(Math.random() * 20) - 10,
        blur: Math.floor(Math.random() * 30) + 2,
        spread: Math.floor(Math.random() * 10) - 5,
        color: colors[Math.floor(Math.random() * colors.length)],
        opacity: 0.1 + Math.random() * 0.4,
        inset: Math.random() > 0.7,
        enabled: true
      });
    }
    
    setShadowLayers(newLayers);
    setSelectedLayer(newLayers[0]?.id || '');
  }, []);

  // Export functions
  const copyCSS = useCallback(() => {
    let cssCode = '';
    
    switch (exportFormat) {
      case 'css':
        cssCode = `.shadow {\n  box-shadow: ${currentBoxShadow};\n}`;
        break;
      case 'scss':
        cssCode = `$shadow: ${currentBoxShadow};\n\n.shadow {\n  box-shadow: $shadow;\n}`;
        break;
      case 'tailwind':
        cssCode = `/* Add to tailwind.config.js */\n{\n  boxShadow: {\n    'custom': '${currentBoxShadow}'\n  }\n}\n\n/* Usage */\n<div class="shadow-custom">\n  Content here\n</div>`;
        break;
      case 'js':
        cssCode = `const shadowStyle = {\n  boxShadow: '${currentBoxShadow}'\n};\n\n// React usage\n<div style={shadowStyle}>\n  Content here\n</div>`;
        break;
    }
    
    navigator.clipboard.writeText(cssCode);
  }, [currentBoxShadow, exportFormat]);

  const downloadCSS = useCallback(() => {
    const cssContent = `.shadow {\n  box-shadow: ${currentBoxShadow};\n}`;
    const blob = new Blob([cssContent], { type: 'text/css' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'shadow.css';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [currentBoxShadow]);

  const selectedLayerData = shadowLayers.find(layer => layer.id === selectedLayer);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-100 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-600 rounded-xl shadow-lg">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              CSS Shadow Generator
            </h1>
          </div>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Create stunning box-shadow effects with our visual editor. Layer multiple shadows,
            adjust properties in real-time, and export to various formats.
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="generator" className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              Generator
            </TabsTrigger>
            <TabsTrigger value="presets" className="flex items-center gap-2">
              <Sparkles className="w-4 h-4" />
              Presets
            </TabsTrigger>
            <TabsTrigger value="export" className="flex items-center gap-2">
              <Code className="w-4 h-4" />
              Export
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generator" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Controls Panel */}
              <Card className="lg:col-span-1">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Shadow Controls
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Layer Management */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-semibold">Shadow Layers</Label>
                      <Button onClick={addLayer} size="sm">
                        <Plus className="w-4 h-4 mr-2" />
                        Add
                      </Button>
                    </div>

                    <div className="space-y-2">
                      {shadowLayers.map((layer, index) => (
                        <div 
                          key={layer.id}
                          className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                            selectedLayer === layer.id ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setSelectedLayer(layer.id)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Switch
                                checked={layer.enabled}
                                onCheckedChange={(enabled) => updateLayer(layer.id, { enabled })}
                                size="sm"
                              />
                              <span className="text-sm font-medium">Layer {index + 1}</span>
                              {layer.inset && <Badge variant="outline" className="text-xs">Inset</Badge>}
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  duplicateLayer(layer.id);
                                }}
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                              {shadowLayers.length > 1 && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeLayer(layer.id);
                                  }}
                                >
                                  <Minus className="w-3 h-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Selected Layer Controls */}
                  {selectedLayerData && (
                    <div className="space-y-4 pt-4 border-t">
                      <Label className="text-base font-semibold">Edit Layer {shadowLayers.findIndex(l => l.id === selectedLayer) + 1}</Label>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>X Offset: {selectedLayerData.x}px</Label>
                          <Slider
                            value={[selectedLayerData.x]}
                            onValueChange={([value]) => updateLayer(selectedLayer, { x: value })}
                            min={-50}
                            max={50}
                            step={1}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Y Offset: {selectedLayerData.y}px</Label>
                          <Slider
                            value={[selectedLayerData.y]}
                            onValueChange={([value]) => updateLayer(selectedLayer, { y: value })}
                            min={-50}
                            max={50}
                            step={1}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Blur: {selectedLayerData.blur}px</Label>
                          <Slider
                            value={[selectedLayerData.blur]}
                            onValueChange={([value]) => updateLayer(selectedLayer, { blur: value })}
                            min={0}
                            max={100}
                            step={1}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Spread: {selectedLayerData.spread}px</Label>
                          <Slider
                            value={[selectedLayerData.spread]}
                            onValueChange={([value]) => updateLayer(selectedLayer, { spread: value })}
                            min={-50}
                            max={50}
                            step={1}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Color</Label>
                        <div className="flex gap-2">
                          <Input
                            type="color"
                            value={selectedLayerData.color}
                            onChange={(e) => updateLayer(selectedLayer, { color: e.target.value })}
                            className="w-16 h-10 p-1 border-2"
                          />
                          <Input
                            value={selectedLayerData.color}
                            onChange={(e) => updateLayer(selectedLayer, { color: e.target.value })}
                            className="flex-1"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Opacity: {Math.round(selectedLayerData.opacity * 100)}%</Label>
                        <Slider
                          value={[selectedLayerData.opacity]}
                          onValueChange={([value]) => updateLayer(selectedLayer, { opacity: value })}
                          min={0}
                          max={1}
                          step={0.01}
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="inset"
                          checked={selectedLayerData.inset}
                          onCheckedChange={(inset) => updateLayer(selectedLayer, { inset })}
                        />
                        <Label htmlFor="inset">Inset Shadow</Label>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="space-y-2 pt-4 border-t">
                    <Button onClick={generateRandomShadow} className="w-full" variant="outline">
                      <RotateCw className="w-4 h-4 mr-2" />
                      Random Shadow
                    </Button>
                    <div className="grid grid-cols-2 gap-2">
                      <Button onClick={copyCSS} size="sm">
                        <Copy className="w-4 h-4 mr-2" />
                        Copy CSS
                      </Button>
                      <Button onClick={downloadCSS} size="sm" variant="outline">
                        <Download className="w-4 h-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Preview Panel */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="w-5 h-5" />
                      Preview
                    </CardTitle>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Label htmlFor="preview-mode" className="text-sm">Mode:</Label>
                        <Select value={previewMode} onValueChange={(value: 'element' | 'text' | 'card') => setPreviewMode(value)}>
                          <SelectTrigger className="w-24">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="element">Element</SelectItem>
                            <SelectItem value="text">Text</SelectItem>
                            <SelectItem value="card">Card</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Background Controls */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Background Color</Label>
                      <div className="flex gap-2">
                        <Input
                          type="color"
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="w-16 h-8 p-1 border-2"
                        />
                        <Input
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="flex-1 text-sm"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Element Color</Label>
                      <div className="flex gap-2">
                        <Input
                          type="color"
                          value={elementColor}
                          onChange={(e) => setElementColor(e.target.value)}
                          className="w-16 h-8 p-1 border-2"
                        />
                        <Input
                          value={elementColor}
                          onChange={(e) => setElementColor(e.target.value)}
                          className="flex-1 text-sm"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Size Controls */}
                  {previewMode === 'element' && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Width: {elementSize.width}px</Label>
                        <Slider
                          value={[elementSize.width]}
                          onValueChange={([value]) => setElementSize(prev => ({ ...prev, width: value }))}
                          min={100}
                          max={400}
                          step={10}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Height: {elementSize.height}px</Label>
                        <Slider
                          value={[elementSize.height]}
                          onValueChange={([value]) => setElementSize(prev => ({ ...prev, height: value }))}
                          min={60}
                          max={300}
                          step={10}
                        />
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="show-grid"
                      checked={showGrid}
                      onCheckedChange={setShowGrid}
                    />
                    <Label htmlFor="show-grid" className="text-sm">Show grid background</Label>
                  </div>

                  {/* Preview Area */}
                  <div className="relative">
                    <div 
                      ref={previewRef}
                      className={`min-h-[300px] p-8 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center ${
                        showGrid ? 'bg-grid-pattern' : ''
                      }`}
                      style={{ 
                        backgroundColor: showGrid ? 'transparent' : backgroundColor,
                        backgroundImage: showGrid ? 'radial-gradient(circle, #ccc 1px, transparent 1px)' : undefined,
                        backgroundSize: showGrid ? '20px 20px' : undefined
                      }}
                    >
                      {previewMode === 'element' && (
                        <div
                          className="rounded-lg"
                          style={{
                            width: `${elementSize.width}px`,
                            height: `${elementSize.height}px`,
                            backgroundColor: elementColor,
                            boxShadow: currentBoxShadow
                          }}
                        />
                      )}

                      {previewMode === 'text' && (
                        <div
                          className="text-4xl font-bold px-8 py-4 rounded-lg"
                          style={{
                            backgroundColor: elementColor,
                            color: '#333',
                            boxShadow: currentBoxShadow
                          }}
                        >
                          Sample Text
                        </div>
                      )}

                      {previewMode === 'card' && (
                        <div
                          className="p-6 rounded-xl max-w-sm"
                          style={{
                            backgroundColor: elementColor,
                            boxShadow: currentBoxShadow
                          }}
                        >
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">Card Title</h3>
                          <p className="text-gray-600 text-sm">
                            This is a sample card with the generated shadow effect applied.
                          </p>
                          <Button size="sm" className="mt-4">
                            Action
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Shadow Information */}
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <Label className="text-sm font-medium text-gray-600">Current CSS:</Label>
                    <pre className="mt-2 text-xs font-mono bg-white p-3 rounded border overflow-x-auto">
                      box-shadow: {currentBoxShadow || 'none'};
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="presets" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  Shadow Presets
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {presets.map((preset, index) => (
                    <div
                      key={index}
                      className="relative cursor-pointer group border-2 border-gray-200 rounded-lg overflow-hidden hover:border-purple-400 transition-colors"
                      onClick={() => applyPreset(preset)}
                    >
                      <div className="h-32 bg-gray-50 flex items-center justify-center">
                        <div
                          className="w-16 h-10 bg-white rounded"
                          style={{ boxShadow: generateBoxShadow(preset.layers.map((layer, i) => ({ ...layer, id: i.toString(), enabled: true }))) }}
                        />
                      </div>
                      <div className="p-3 bg-white">
                        <div className="font-medium text-sm">{preset.name}</div>
                        <div className="text-xs text-gray-500">{preset.description}</div>
                      </div>
                      <div className="absolute inset-0 bg-purple-500 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 bg-white px-2 py-1 rounded text-xs font-medium">
                          Apply
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Code className="w-5 h-5" />
                    Export Code
                  </CardTitle>
                  <Select value={exportFormat} onValueChange={(value: 'css' | 'scss' | 'tailwind' | 'js') => setExportFormat(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="css">CSS</SelectItem>
                      <SelectItem value="scss">SCSS</SelectItem>
                      <SelectItem value="tailwind">Tailwind</SelectItem>
                      <SelectItem value="js">JavaScript</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Generated Code:</Label>
                  <pre className="mt-2 p-4 bg-gray-900 text-gray-100 rounded-lg overflow-x-auto text-sm">
                    {exportFormat === 'css' && `.shadow {\n  box-shadow: ${currentBoxShadow};\n}`}
                    {exportFormat === 'scss' && `$shadow: ${currentBoxShadow};\n\n.shadow {\n  box-shadow: $shadow;\n}`}
                    {exportFormat === 'tailwind' && `/* Add to tailwind.config.js */\n{\n  boxShadow: {\n    'custom': '${currentBoxShadow}'\n  }\n}\n\n/* Usage */\n<div class="shadow-custom">\n  Content here\n</div>`}
                    {exportFormat === 'js' && `const shadowStyle = {\n  boxShadow: '${currentBoxShadow}'\n};\n\n// React usage\n<div style={shadowStyle}>\n  Content here\n</div>`}
                  </pre>
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={copyCSS} className="flex-1">
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Code
                  </Button>
                  <Button onClick={downloadCSS} variant="outline" className="flex-1">
                    <Download className="w-4 h-4 mr-2" />
                    Download CSS
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Shadow Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Total Layers:</span>
                    <Badge>{shadowLayers.filter(l => l.enabled).length}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Has Inset:</span>
                    <Badge variant={shadowLayers.some(l => l.inset && l.enabled) ? "default" : "outline"}>
                      {shadowLayers.some(l => l.inset && l.enabled) ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Max Blur:</span>
                    <Badge variant="outline">
                      {Math.max(...shadowLayers.filter(l => l.enabled).map(l => l.blur))}px
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CssShadowGeneratorTool;