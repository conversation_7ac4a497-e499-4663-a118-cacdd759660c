'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Search, Replace, Copy, Check, Download, Upload, Settings, Info, AlertCircle } from 'lucide-react'

interface ReplaceOptions {
  caseSensitive: boolean
  wholeWord: boolean
  useRegex: boolean
  multiline: boolean
  global: boolean
}

interface ReplaceHistory {
  find: string
  replace: string
  timestamp: Date
}

export default function TextReplaceTool() {
  const [inputText, setInputText] = useState('')
  const [outputText, setOutputText] = useState('')
  const [findText, setFindText] = useState('')
  const [replaceText, setReplaceText] = useState('')
  const [copied, setCopied] = useState(false)
  const [matchCount, setMatchCount] = useState(0)
  const [highlightedText, setHighlightedText] = useState('')
  const [history, setHistory] = useState<ReplaceHistory[]>([])
  const [showHistory, setShowHistory] = useState(false)
  const [options, setOptions] = useState<ReplaceOptions>({
    caseSensitive: false,
    wholeWord: false,
    useRegex: false,
    multiline: false,
    global: true,
  })

  // 构建正则表达式
  const buildRegex = useCallback((pattern: string): RegExp | null => {
    try {
      let flags = ''
      if (!options.caseSensitive) flags += 'i'
      if (options.global) flags += 'g'
      if (options.multiline) flags += 'm'

      if (options.useRegex) {
        return new RegExp(pattern, flags)
      } else {
        // 转义特殊字符
        const escaped = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
        let regexPattern = escaped

        if (options.wholeWord) {
          regexPattern = `\\b${escaped}\\b`
        }

        return new RegExp(regexPattern, flags)
      }
    } catch (error) {
      console.error('正则表达式错误:', error)
      return null
    }
  }, [options])

  // 查找并高亮匹配项
  const findMatches = useCallback(() => {
    if (!inputText || !findText) {
      setHighlightedText(inputText)
      setMatchCount(0)
      return
    }

    const regex = buildRegex(findText)
    if (!regex) {
      setHighlightedText(inputText)
      setMatchCount(0)
      return
    }

    const matches = inputText.match(regex)
    const count = matches ? matches.length : 0
    setMatchCount(count)

    // 高亮显示匹配项
    if (count > 0) {
      const highlighted = inputText.replace(regex, (match) => {
        return `<mark class="bg-yellow-300">${match}</mark>`
      })
      setHighlightedText(highlighted)
    } else {
      setHighlightedText(inputText)
    }
  }, [inputText, findText, buildRegex])

  // 执行替换
  const performReplace = () => {
    if (!inputText || !findText) return

    const regex = buildRegex(findText)
    if (!regex) return

    const result = inputText.replace(regex, replaceText)
    setOutputText(result)

    // 添加到历史记录
    const newHistory: ReplaceHistory = {
      find: findText,
      replace: replaceText,
      timestamp: new Date(),
    }
    setHistory([newHistory, ...history.slice(0, 9)]) // 保留最近10条记录
  }

  // 替换全部
  const replaceAll = () => {
    performReplace()
  }

  // 使用历史记录
  const applyHistoryItem = (item: ReplaceHistory) => {
    setFindText(item.find)
    setReplaceText(item.replace)
    setShowHistory(false)
  }

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(outputText)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 下载结果
  const downloadResult = () => {
    const blob = new Blob([outputText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'replaced-text.txt'
    a.click()
    URL.revokeObjectURL(url)
  }

  // 上传文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputText(content)
      setOutputText('')
      setHighlightedText(content)
      setMatchCount(0)
    }
    reader.readAsText(file)
  }

  // 清空所有
  const clearAll = () => {
    setInputText('')
    setOutputText('')
    setFindText('')
    setReplaceText('')
    setHighlightedText('')
    setMatchCount(0)
  }

  // 加载示例
  const loadExample = () => {
    const example = `Hello World! This is a sample text.
The world is beautiful. Hello everyone!
Let's replace some words in this text.
Hello, hello, HELLO - different cases.
This is a test. This is only a test.`
    
    setInputText(example)
    setFindText('Hello')
    setReplaceText('Hi')
    setOutputText('')
    findMatches()
  }

  // 监听查找文本变化
  React.useEffect(() => {
    findMatches()
  }, [inputText, findText, options, findMatches])

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本查找替换</h2>
        <p className="text-gray-600">
          强大的文本查找和替换工具，支持正则表达式和多种匹配选项
        </p>
      </div>

      {/* 查找替换输入 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">查找内容</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={findText}
                onChange={(e) => {
                  setFindText(e.target.value)
                  findMatches()
                }}
                placeholder="输入要查找的文本..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            {matchCount > 0 && (
              <p className="mt-1 text-sm text-green-600">
                找到 {matchCount} 个匹配项
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">替换为</label>
            <div className="relative">
              <Replace className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={replaceText}
                onChange={(e) => setReplaceText(e.target.value)}
                placeholder="输入替换文本..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* 选项 */}
        <div className="mt-4 flex flex-wrap gap-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.caseSensitive}
              onChange={(e) => setOptions({ ...options, caseSensitive: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">区分大小写</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.wholeWord}
              onChange={(e) => setOptions({ ...options, wholeWord: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">全词匹配</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.useRegex}
              onChange={(e) => setOptions({ ...options, useRegex: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">使用正则表达式</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.global}
              onChange={(e) => setOptions({ ...options, global: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">替换全部</span>
          </label>
        </div>

        {/* 操作按钮 */}
        <div className="mt-4 flex gap-3">
          <button
            onClick={replaceAll}
            disabled={!inputText || !findText}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            执行替换
          </button>
          <button
            onClick={() => setShowHistory(!showHistory)}
            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            历史记录 ({history.length})
          </button>
          <button
            onClick={loadExample}
            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            加载示例
          </button>
          <button
            onClick={clearAll}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            清空
          </button>
        </div>
      </div>

      {/* 历史记录 */}
      {showHistory && history.length > 0 && (
        <div className="mb-6 p-4 bg-white border border-gray-200 rounded-lg">
          <h3 className="font-medium mb-3">替换历史</h3>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {history.map((item, index) => (
              <button
                key={index}
                onClick={() => applyHistoryItem(item)}
                className="w-full text-left p-2 hover:bg-gray-50 rounded transition-colors"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <span className="text-sm text-gray-600">查找: </span>
                    <span className="text-sm font-mono">{item.find}</span>
                    <span className="text-sm text-gray-600 mx-2">→</span>
                    <span className="text-sm font-mono">{item.replace}</span>
                  </div>
                  <span className="text-xs text-gray-400">
                    {new Date(item.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">原始文本</label>
            <label className="text-sm text-blue-500 hover:text-blue-600 cursor-pointer">
              <input
                type="file"
                accept=".txt"
                onChange={handleFileUpload}
                className="hidden"
              />
              <span className="flex items-center gap-1">
                <Upload className="w-4 h-4" />
                上传文件
              </span>
            </label>
          </div>
          <textarea
            value={inputText}
            onChange={(e) => {
              setInputText(e.target.value)
              setOutputText('')
            }}
            placeholder="请输入要处理的文本..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {highlightedText && inputText && (
            <div className="mt-2 p-2 bg-yellow-50 rounded text-sm">
              <div 
                className="whitespace-pre-wrap break-words"
                dangerouslySetInnerHTML={{ __html: highlightedText.substring(0, 200) + (highlightedText.length > 200 ? '...' : '') }}
              />
            </div>
          )}
        </div>

        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">替换结果</label>
            <div className="flex gap-2">
              {outputText && (
                <>
                  <button
                    onClick={downloadResult}
                    className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                  >
                    <Download className="w-4 h-4" />
                    下载
                  </button>
                  <button
                    onClick={copyToClipboard}
                    className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                  >
                    {copied ? (
                      <>
                        <Check className="w-4 h-4" />
                        已复制
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4" />
                        复制
                      </>
                    )}
                  </button>
                </>
              )}
            </div>
          </div>
          <textarea
            value={outputText}
            readOnly
            placeholder="替换结果将显示在这里..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none bg-gray-50"
          />
          {outputText && (
            <div className="mt-2 text-sm text-gray-600">
              替换了 {matchCount} 处
            </div>
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持普通文本和正则表达式查找替换</li>
              <li>可选择区分大小写、全词匹配等选项</li>
              <li>实时显示匹配数量和高亮预览</li>
              <li>保留最近的替换历史记录</li>
              <li>支持批量文件处理</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 正则表达式提示 */}
      {options.useRegex && (
        <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
            <div className="text-sm text-yellow-900">
              <h3 className="font-semibold mb-1">正则表达式提示</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                <div>
                  <code className="bg-yellow-100 px-1">.</code> - 匹配任意字符
                </div>
                <div>
                  <code className="bg-yellow-100 px-1">*</code> - 匹配0次或多次
                </div>
                <div>
                  <code className="bg-yellow-100 px-1">+</code> - 匹配1次或多次
                </div>
                <div>
                  <code className="bg-yellow-100 px-1">?</code> - 匹配0次或1次
                </div>
                <div>
                  <code className="bg-yellow-100 px-1">\d</code> - 匹配数字
                </div>
                <div>
                  <code className="bg-yellow-100 px-1">\w</code> - 匹配字母数字
                </div>
                <div>
                  <code className="bg-yellow-100 px-1">\s</code> - 匹配空白字符
                </div>
                <div>
                  <code className="bg-yellow-100 px-1">[abc]</code> - 匹配a、b或c
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 常见用例 */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">常见用例</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          <div>
            <strong>删除空行：</strong>
            <span className="text-gray-600">查找 <code>^\s*$\n</code> 替换为空</span>
          </div>
          <div>
            <strong>删除行尾空格：</strong>
            <span className="text-gray-600">查找 <code>\s+$</code> 替换为空</span>
          </div>
          <div>
            <strong>替换多个空格：</strong>
            <span className="text-gray-600">查找 <code>\s+</code> 替换为单个空格</span>
          </div>
          <div>
            <strong>添加引号：</strong>
            <span className="text-gray-600">查找 <code>(\w+)</code> 替换为 <code>&quot;$1&quot;</code></span>
          </div>
        </div>
      </div>
    </div>
  )
}
