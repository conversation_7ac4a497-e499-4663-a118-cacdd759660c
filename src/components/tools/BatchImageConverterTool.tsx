'use client'

import React, { useState, useRef } from 'react'
import Image from 'next/image'
import { Upload, Download, RefreshCw, Image as ImageIcon, FileImage, Settings, CheckCircle, XCircle, Loader2 } from 'lucide-react'

interface ImageFile {
  id: string
  file: File
  url: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  convertedUrl?: string
  convertedBlob?: Blob
  error?: string
}

interface ConversionSettings {
  format: 'jpeg' | 'png' | 'webp' | 'bmp'
  quality: number
  maxWidth?: number
  maxHeight?: number
  maintainAspectRatio: boolean
}

export default function BatchImageConverterTool() {
  const [images, setImages] = useState<ImageFile[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [settings, setSettings] = useState<ConversionSettings>({
    format: 'jpeg',
    quality: 0.9,
    maintainAspectRatio: true
  })
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    const newImages: ImageFile[] = files.map(file => ({
      id: `${Date.now()}-${Math.random()}`,
      file,
      url: URL.createObjectURL(file),
      status: 'pending'
    }))

    setImages(prev => [...prev, ...newImages])
  }

  // 转换单个图片
  const convertImage = async (imageFile: ImageFile): Promise<void> => {
    return new Promise((resolve, reject) => {
      const canvas = canvasRef.current
      if (!canvas) {
        reject(new Error('Canvas not available'))
        return
      }

      const ctx = canvas.getContext('2d')
      if (!ctx) {
        reject(new Error('Canvas context not available'))
        return
      }

      const img = new window.Image()
      img.onload = () => {
        try {
          // 计算输出尺寸
          let outputWidth = img.width
          let outputHeight = img.height

          if (settings.maxWidth || settings.maxHeight) {
            if (settings.maintainAspectRatio) {
              const aspectRatio = img.width / img.height
              
              if (settings.maxWidth && img.width > settings.maxWidth) {
                outputWidth = settings.maxWidth
                outputHeight = outputWidth / aspectRatio
              }
              
              if (settings.maxHeight && outputHeight > settings.maxHeight) {
                outputHeight = settings.maxHeight
                outputWidth = outputHeight * aspectRatio
              }
            } else {
              if (settings.maxWidth) outputWidth = Math.min(img.width, settings.maxWidth)
              if (settings.maxHeight) outputHeight = Math.min(img.height, settings.maxHeight)
            }
          }

          // 设置画布尺寸
          canvas.width = outputWidth
          canvas.height = outputHeight

          // 绘制图片
          ctx.drawImage(img, 0, 0, outputWidth, outputHeight)

          // 转换格式
          const mimeType = `image/${settings.format === 'jpeg' ? 'jpeg' : settings.format}`
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const convertedUrl = URL.createObjectURL(blob)
                setImages(prev => prev.map(img => 
                  img.id === imageFile.id 
                    ? { ...img, status: 'completed', convertedUrl, convertedBlob: blob }
                    : img
                ))
                resolve()
              } else {
                throw new Error('转换失败')
              }
            },
            mimeType,
            settings.quality
          )
        } catch (error) {
          setImages(prev => prev.map(img => 
            img.id === imageFile.id 
              ? { ...img, status: 'error', error: error instanceof Error ? error.message : '转换失败' }
              : img
          ))
          reject(error)
        }
      }

      img.onerror = () => {
        setImages(prev => prev.map(img => 
          img.id === imageFile.id 
            ? { ...img, status: 'error', error: '图片加载失败' }
            : img
        ))
        reject(new Error('图片加载失败'))
      }

      img.src = imageFile.url
    })
  }

  // 批量转换
  const convertAllImages = async () => {
    setIsProcessing(true)
    
    // 重置所有图片状态
    setImages(prev => prev.map(img => ({ ...img, status: 'processing' as const })))

    // 逐个转换图片
    for (const image of images) {
      try {
        await convertImage(image)
      } catch (error) {
        console.error(`转换图片 ${image.file.name} 失败:`, error)
      }
    }

    setIsProcessing(false)
  }

  // 下载单个图片
  const downloadImage = (image: ImageFile) => {
    if (!image.convertedUrl || !image.convertedBlob) return

    const link = document.createElement('a')
    link.href = image.convertedUrl
    const originalName = image.file.name.split('.').slice(0, -1).join('.')
    link.download = `${originalName}.${settings.format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 下载所有图片
  const downloadAllImages = async () => {
    const completedImages = images.filter(img => img.status === 'completed' && img.convertedBlob)
    
    if (completedImages.length === 0) return

    // 如果只有一张图片，直接下载
    if (completedImages.length === 1) {
      downloadImage(completedImages[0])
      return
    }

    // 多张图片，创建 zip 文件（这里简化处理，逐个下载）
    for (const image of completedImages) {
      downloadImage(image)
      // 添加延迟避免浏览器阻止多个下载
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  // 移除图片
  const removeImage = (id: string) => {
    setImages(prev => {
      const image = prev.find(img => img.id === id)
      if (image) {
        URL.revokeObjectURL(image.url)
        if (image.convertedUrl) URL.revokeObjectURL(image.convertedUrl)
      }
      return prev.filter(img => img.id !== id)
    })
  }

  // 清空所有
  const clearAll = () => {
    images.forEach(image => {
      URL.revokeObjectURL(image.url)
      if (image.convertedUrl) URL.revokeObjectURL(image.convertedUrl)
    })
    setImages([])
    if (fileInputRef.current) fileInputRef.current.value = ''
  }

  // 获取状态图标
  const getStatusIcon = (status: ImageFile['status']) => {
    switch (status) {
      case 'pending':
        return <div className="w-5 h-5 rounded-full border-2 border-gray-300" />
      case 'processing':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
    }
  }

  // 计算统计信息
  const stats = {
    total: images.length,
    completed: images.filter(img => img.status === 'completed').length,
    error: images.filter(img => img.status === 'error').length
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片格式批量转换</h2>
        <p className="text-gray-600">
          批量转换图片格式，支持调整尺寸和质量，一次处理多张图片
        </p>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* 设置面板 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 转换设置 */}
          <div className="bg-white rounded-lg border border-gray-300 p-4">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Settings className="w-4 h-4" />
              转换设置
            </h3>
            
            {/* 输出格式 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">输出格式</label>
              <select
                value={settings.format}
                onChange={(e) => setSettings(prev => ({ ...prev, format: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="jpeg">JPEG</option>
                <option value="png">PNG</option>
                <option value="webp">WebP</option>
                <option value="bmp">BMP</option>
              </select>
            </div>

            {/* 质量设置 */}
            {(settings.format === 'jpeg' || settings.format === 'webp') && (
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  图片质量: {Math.round(settings.quality * 100)}%
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={settings.quality}
                  onChange={(e) => setSettings(prev => ({ ...prev, quality: Number(e.target.value) }))}
                  className="w-full"
                />
              </div>
            )}

            {/* 尺寸设置 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">最大宽度（可选）</label>
              <input
                type="number"
                placeholder="保持原始宽度"
                value={settings.maxWidth || ''}
                onChange={(e) => setSettings(prev => ({ 
                  ...prev, 
                  maxWidth: e.target.value ? Number(e.target.value) : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">最大高度（可选）</label>
              <input
                type="number"
                placeholder="保持原始高度"
                value={settings.maxHeight || ''}
                onChange={(e) => setSettings(prev => ({ 
                  ...prev, 
                  maxHeight: e.target.value ? Number(e.target.value) : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={settings.maintainAspectRatio}
                  onChange={(e) => setSettings(prev => ({ 
                    ...prev, 
                    maintainAspectRatio: e.target.checked 
                  }))}
                  className="rounded"
                />
                <span className="text-sm">保持宽高比</span>
              </label>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-2">
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileUpload}
              accept="image/*"
              multiple
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              <Upload className="w-4 h-4 inline mr-2" />
              选择图片
            </button>
            <button
              onClick={convertAllImages}
              disabled={images.length === 0 || isProcessing}
              className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              {isProcessing ? '转换中...' : '开始转换'}
            </button>
            <button
              onClick={downloadAllImages}
              disabled={stats.completed === 0}
              className="w-full px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              <Download className="w-4 h-4 inline mr-2" />
              下载全部 ({stats.completed})
            </button>
            <button
              onClick={clearAll}
              className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
            >
              <RefreshCw className="w-4 h-4 inline mr-2" />
              清空列表
            </button>
          </div>

          {/* 统计信息 */}
          {images.length > 0 && (
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium mb-2">转换统计</h4>
              <div className="space-y-1 text-sm">
                <p>总计：{stats.total} 张</p>
                <p className="text-green-600">成功：{stats.completed} 张</p>
                {stats.error > 0 && (
                  <p className="text-red-600">失败：{stats.error} 张</p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 图片列表 */}
        <div className="lg:col-span-2">
          {images.length === 0 ? (
            <div className="bg-white rounded-lg border-2 border-dashed border-gray-300 p-12">
              <div className="text-center">
                <FileImage className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">选择要转换的图片</p>
                <p className="text-sm text-gray-500">支持 JPG、PNG、WebP、BMP 等格式</p>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4 px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  <Upload className="w-4 h-4 inline mr-2" />
                  选择图片
                </button>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-300 p-4">
              <h3 className="font-medium mb-4">图片列表</h3>
              <div className="space-y-3 max-h-[600px] overflow-y-auto">
                {images.map((image) => (
                  <div
                    key={image.id}
                    className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg"
                  >
                    {/* 缩略图 */}
                    <div className="w-16 h-16 bg-gray-200 rounded overflow-hidden flex-shrink-0 relative">
                      <Image
                        src={image.url}
                        alt={image.file.name}
                        fill
                        className="object-cover"
                        unoptimized
                      />
                    </div>

                    {/* 文件信息 */}
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">{image.file.name}</p>
                      <p className="text-xs text-gray-500">
                        {(image.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      {image.error && (
                        <p className="text-xs text-red-500 mt-1">{image.error}</p>
                      )}
                    </div>

                    {/* 状态 */}
                    <div className="flex items-center gap-2">
                      {getStatusIcon(image.status)}
                      {image.status === 'completed' && (
                        <button
                          onClick={() => downloadImage(image)}
                          className="p-1 text-blue-500 hover:bg-blue-50 rounded"
                          title="下载"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => removeImage(image.id)}
                        className="p-1 text-red-500 hover:bg-red-50 rounded"
                        title="删除"
                      >
                        <XCircle className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Canvas 用于转换 */}
      <canvas ref={canvasRef} className="hidden" />

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <ImageIcon className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持批量选择和转换多张图片</li>
              <li>可以设置输出格式、质量和尺寸</li>
              <li>JPEG 和 WebP 格式支持质量调整</li>
              <li>可以限制最大宽度和高度，自动缩放图片</li>
              <li>转换完成后可以单独下载或批量下载</li>
              <li>所有处理都在浏览器本地完成，保护您的隐私</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
