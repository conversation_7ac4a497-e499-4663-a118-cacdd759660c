'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Copy, FileText, Upload, Download, Trash2, CheckCircle, AlertCircle } from 'lucide-react';

export default function HtmlToTextTool() {
  // 状态定义
  const [htmlInput, setHtmlInput] = useState('');
  const [textOutput, setTextOutput] = useState('');
  const [preserveWhitespace, setPreserveWhitespace] = useState(false);
  const [preserveLineBreaks, setPreserveLineBreaks] = useState(true);
  const [preserveLinks, setPreserveLinks] = useState(false);
  const [addMarkdown, setAddMarkdown] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // HTML示例
  const htmlExample = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>示例HTML页面</title>
    <style>
        body { font-family: Arial, sans-serif; }
        .highlight { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <header>
        <h1>欢迎来到我的网站</h1>
        <nav>
            <ul>
                <li><a href="#home">首页</a></li>
                <li><a href="#about">关于我们</a></li>
                <li><a href="#contact">联系方式</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <article>
            <h2>文章标题</h2>
            <p>这是一段<strong>重要</strong>的文本内容，包含<em>强调</em>的部分。</p>
            <p>访问我们的网站：<a href="https://example.com">example.com</a></p>
            
            <blockquote>
                这是一段引用文本，来自某位著名人士。
            </blockquote>
            
            <ul>
                <li>列表项目一</li>
                <li>列表项目二</li>
                <li>列表项目三</li>
            </ul>
            
            <table>
                <tr>
                    <th>姓名</th>
                    <th>年龄</th>
                    <th>城市</th>
                </tr>
                <tr>
                    <td>张三</td>
                    <td>25</td>
                    <td>北京</td>
                </tr>
                <tr>
                    <td>李四</td>
                    <td>30</td>
                    <td>上海</td>
                </tr>
            </table>
        </article>
    </main>
    
    <footer>
        <p>&copy; 2024 我的网站. 保留所有权利。</p>
    </footer>
</body>
</html>`;

  // HTML转文本的核心函数
  const convertHtmlToText = useMemo(() => {
    if (!htmlInput.trim()) return '';

    try {
      setError('');
      
      // 创建临时DOM元素来解析HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlInput;

      // 移除script和style标签
      const scripts = tempDiv.querySelectorAll('script, style, noscript');
      scripts.forEach(element => element.remove());

      let result = '';

      // 递归处理节点
      const processNode = (node: Node): string => {
        let text = '';
        
        if (node.nodeType === Node.TEXT_NODE) {
          // 文本节点
          let nodeText = node.textContent || '';
          if (!preserveWhitespace) {
            nodeText = nodeText.replace(/\s+/g, ' ').trim();
          }
          return nodeText;
        }
        
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;
          const tagName = element.tagName.toLowerCase();
          
          // 处理不同的HTML元素
          switch (tagName) {
            case 'br':
              return preserveLineBreaks ? '\n' : ' ';
              
            case 'p':
            case 'div':
            case 'h1':
            case 'h2':
            case 'h3':
            case 'h4':
            case 'h5':
            case 'h6':
              // 块级元素，前后添加换行
              let content = '';
              for (const child of Array.from(element.childNodes)) {
                content += processNode(child);
              }
              
              if (addMarkdown && /^h[1-6]$/.test(tagName)) {
                const level = parseInt(tagName[1]);
                const prefix = '#'.repeat(level);
                return preserveLineBreaks ? `\n${prefix} ${content.trim()}\n` : `${prefix} ${content.trim()} `;
              }
              
              return preserveLineBreaks ? `\n${content}\n` : `${content} `;
              
            case 'li':
              let liContent = '';
              for (const child of Array.from(element.childNodes)) {
                liContent += processNode(child);
              }
              
              if (addMarkdown) {
                return preserveLineBreaks ? `\n- ${liContent.trim()}` : `- ${liContent.trim()} `;
              }
              
              return preserveLineBreaks ? `\n• ${liContent.trim()}` : `• ${liContent.trim()} `;
              
            case 'ol':
            case 'ul':
              let listContent = '';
              const listItems = element.querySelectorAll('li');
              listItems.forEach((li, index) => {
                let itemContent = '';
                for (const child of Array.from(li.childNodes)) {
                  itemContent += processNode(child);
                }
                
                if (tagName === 'ol') {
                  if (addMarkdown) {
                    listContent += preserveLineBreaks ? `\n${index + 1}. ${itemContent.trim()}` : `${index + 1}. ${itemContent.trim()} `;
                  } else {
                    listContent += preserveLineBreaks ? `\n${index + 1}. ${itemContent.trim()}` : `${index + 1}. ${itemContent.trim()} `;
                  }
                } else {
                  if (addMarkdown) {
                    listContent += preserveLineBreaks ? `\n- ${itemContent.trim()}` : `- ${itemContent.trim()} `;
                  } else {
                    listContent += preserveLineBreaks ? `\n• ${itemContent.trim()}` : `• ${itemContent.trim()} `;
                  }
                }
              });
              return listContent;
              
            case 'table':
              let tableContent = '';
              const rows = element.querySelectorAll('tr');
              rows.forEach((row) => {
                const cells = row.querySelectorAll('td, th');
                const cellTexts: string[] = [];
                cells.forEach((cell) => {
                  let cellText = '';
                  for (const child of Array.from(cell.childNodes)) {
                    cellText += processNode(child);
                  }
                  cellTexts.push(cellText.trim());
                });
                
                if (addMarkdown && cells.length > 0) {
                  tableContent += preserveLineBreaks ? `\n| ${cellTexts.join(' | ')} |` : `| ${cellTexts.join(' | ')} | `;
                } else {
                  tableContent += preserveLineBreaks ? `\n${cellTexts.join('\t')}` : `${cellTexts.join('\t')} `;
                }
              });
              return tableContent;
              
            case 'a':
              let linkText = '';
              for (const child of Array.from(element.childNodes)) {
                linkText += processNode(child);
              }
              
              if (preserveLinks) {
                const href = element.getAttribute('href');
                if (href && addMarkdown) {
                  return `[${linkText.trim()}](${href})`;
                } else if (href) {
                  return `${linkText.trim()} (${href})`;
                }
              }
              return linkText;
              
            case 'strong':
            case 'b':
              let strongText = '';
              for (const child of Array.from(element.childNodes)) {
                strongText += processNode(child);
              }
              
              if (addMarkdown) {
                return `**${strongText.trim()}**`;
              }
              return strongText;
              
            case 'em':
            case 'i':
              let emText = '';
              for (const child of Array.from(element.childNodes)) {
                emText += processNode(child);
              }
              
              if (addMarkdown) {
                return `*${emText.trim()}*`;
              }
              return emText;
              
            case 'blockquote':
              let quoteText = '';
              for (const child of Array.from(element.childNodes)) {
                quoteText += processNode(child);
              }
              
              if (addMarkdown) {
                return preserveLineBreaks ? `\n> ${quoteText.trim()}\n` : `> ${quoteText.trim()} `;
              }
              return preserveLineBreaks ? `\n"${quoteText.trim()}"\n` : `"${quoteText.trim()}" `;
              
            case 'code':
              let codeText = '';
              for (const child of Array.from(element.childNodes)) {
                codeText += processNode(child);
              }
              
              if (addMarkdown) {
                return `\`${codeText}\``;
              }
              return codeText;
              
            case 'pre':
              let preText = '';
              for (const child of Array.from(element.childNodes)) {
                preText += processNode(child);
              }
              
              if (addMarkdown) {
                return preserveLineBreaks ? `\n\`\`\`\n${preText}\n\`\`\`\n` : `\`\`\`${preText}\`\`\` `;
              }
              return preserveLineBreaks ? `\n${preText}\n` : `${preText} `;
              
            default:
              // 其他元素，递归处理子节点
              for (const child of Array.from(element.childNodes)) {
                text += processNode(child);
              }
              return text;
          }
        }
        
        return text;
      };

      // 处理所有节点
      for (const child of Array.from(tempDiv.childNodes)) {
        result += processNode(child);
      }

      // 清理结果
      if (!preserveWhitespace) {
        // 合并多个空格为一个
        result = result.replace(/[ \t]+/g, ' ');
      }
      
      if (preserveLineBreaks) {
        // 清理多余的换行符
        result = result.replace(/\n\s*\n\s*\n/g, '\n\n').trim();
      } else {
        // 移除所有换行符
        result = result.replace(/\n+/g, ' ').trim();
      }

      // 移除首尾空白
      result = result.trim();
      
      return result;
    } catch (err) {
      setError('HTML解析失败，请检查HTML格式是否正确');
      return '';
    }
  }, [htmlInput, preserveWhitespace, preserveLineBreaks, preserveLinks, addMarkdown]);

  // 转换函数
  const handleConvert = () => {
    setTextOutput(convertHtmlToText);
    if (convertHtmlToText) {
      setSuccess('HTML转换成功！');
    }
  };

  // 复制到剪贴板
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(textOutput);
      setSuccess('纯文本已复制到剪贴板');
    } catch (err) {
      setError('复制失败');
    }
  };

  // 清空
  const handleClear = () => {
    setHtmlInput('');
    setTextOutput('');
    setError('');
    setSuccess('');
  };

  // 加载示例
  const loadExample = () => {
    setHtmlInput(htmlExample);
    setError('');
    setSuccess('');
  };

  // 文件上传处理
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 10 * 1024 * 1024) { // 10MB限制
      setError('文件大小不能超过10MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setHtmlInput(content);
      setSuccess('文件上传成功');
    };
    reader.readAsText(file);
  };

  // 下载结果
  const handleDownload = () => {
    if (!textOutput) return;
    
    const blob = new Blob([textOutput], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'converted-text.txt';
    a.click();
    URL.revokeObjectURL(url);
    setSuccess('文件下载成功');
  };

  // 实时转换
  React.useEffect(() => {
    if (htmlInput.trim()) {
      setTextOutput(convertHtmlToText);
    }
  }, [htmlInput, convertHtmlToText]);

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      {/* 工具说明卡片 */}
      <Card>
        <CardHeader>
          <CardTitle>HTML转纯文本工具</CardTitle>
          <CardDescription>
            从HTML内容中提取纯文本，去除所有HTML标签和样式。支持保留格式、链接信息和Markdown格式输出。
            适用于网页内容提取、文档转换和数据清理等场景。
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 状态提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-4">
            <div className="flex items-center text-red-600">
              <AlertCircle className="h-4 w-4 mr-2" />
              <span className="text-sm">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {success && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-4">
            <div className="flex items-center text-green-600">
              <CheckCircle className="h-4 w-4 mr-2" />
              <span className="text-sm">{success}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 转换选项 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">转换选项</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="preserveLineBreaks"
                checked={preserveLineBreaks}
                onCheckedChange={setPreserveLineBreaks}
              />
              <Label htmlFor="preserveLineBreaks">保留换行符</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="preserveWhitespace"
                checked={preserveWhitespace}
                onCheckedChange={setPreserveWhitespace}
              />
              <Label htmlFor="preserveWhitespace">保留空白字符</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="preserveLinks"
                checked={preserveLinks}
                onCheckedChange={setPreserveLinks}
              />
              <Label htmlFor="preserveLinks">保留链接信息</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="addMarkdown"
                checked={addMarkdown}
                onCheckedChange={setAddMarkdown}
              />
              <Label htmlFor="addMarkdown">Markdown格式输出</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 转换区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* HTML输入 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">HTML 输入</CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadExample}
                >
                  加载示例
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <label>
                    <Upload className="h-4 w-4 mr-1" />
                    上传文件
                    <input
                      type="file"
                      accept=".html,.htm,.txt"
                      className="hidden"
                      onChange={handleFileUpload}
                    />
                  </label>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="html-input">请输入HTML内容</Label>
              <Textarea
                id="html-input"
                placeholder="<p>Hello <strong>World</strong>!</p>"
                value={htmlInput}
                onChange={(e) => setHtmlInput(e.target.value)}
                className="min-h-[400px] font-mono text-sm"
              />
            </div>
            
            {htmlInput && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <FileText className="h-4 w-4" />
                <span>字符数: {htmlInput.length}</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 文本输出 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">纯文本输出</CardTitle>
              <div className="flex gap-2">
                {textOutput && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopy}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      复制
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDownload}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      下载
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              value={textOutput}
              readOnly
              className="min-h-[400px] font-mono text-sm"
              placeholder="转换结果将显示在这里..."
            />
            
            {textOutput && (
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    <span>字符数: {textOutput.length}</span>
                  </div>
                  <div>
                    <span>行数: {textOutput.split('\n').length}</span>
                  </div>
                  <div>
                    <span>单词数: {textOutput.split(/\s+/).filter(w => w.length > 0).length}</span>
                  </div>
                </div>
                <div className="flex gap-1">
                  {preserveLineBreaks && <Badge variant="secondary">换行</Badge>}
                  {preserveWhitespace && <Badge variant="secondary">空白</Badge>}
                  {preserveLinks && <Badge variant="secondary">链接</Badge>}
                  {addMarkdown && <Badge variant="secondary">Markdown</Badge>}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 操作按钮 */}
      <Card>
        <CardContent className="pt-4">
          <div className="flex gap-2 justify-center">
            <Button onClick={handleConvert}>
              <FileText className="h-4 w-4 mr-2" />
              转换文本
            </Button>
            <Button variant="outline" onClick={handleClear}>
              <Trash2 className="h-4 w-4 mr-2" />
              清空所有
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <div><strong>支持的HTML元素：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>段落和标题：<code>&lt;p&gt;</code>、<code>&lt;h1-h6&gt;</code>、<code>&lt;div&gt;</code></li>
            <li>列表：<code>&lt;ul&gt;</code>、<code>&lt;ol&gt;</code>、<code>&lt;li&gt;</code></li>
            <li>表格：<code>&lt;table&gt;</code>、<code>&lt;tr&gt;</code>、<code>&lt;td&gt;</code>、<code>&lt;th&gt;</code></li>
            <li>链接：<code>&lt;a&gt;</code>（可选择保留链接信息）</li>
            <li>格式：<code>&lt;strong&gt;</code>、<code>&lt;em&gt;</code>、<code>&lt;code&gt;</code>、<code>&lt;pre&gt;</code></li>
            <li>引用：<code>&lt;blockquote&gt;</code></li>
          </ul>
          <div className="mt-4"><strong>转换选项说明：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li><strong>保留换行符</strong>：保持HTML中的段落和换行结构</li>
            <li><strong>保留空白字符</strong>：保持原始的空格和制表符</li>
            <li><strong>保留链接信息</strong>：在文本后显示链接地址</li>
            <li><strong>Markdown格式输出</strong>：转换为Markdown格式</li>
          </ul>
          <div className="mt-4"><strong>注意事项：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>自动移除<code>&lt;script&gt;</code>、<code>&lt;style&gt;</code>等标签</li>
            <li>支持上传HTML文件，最大10MB</li>
            <li>结果可以直接复制或下载为文本文件</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
