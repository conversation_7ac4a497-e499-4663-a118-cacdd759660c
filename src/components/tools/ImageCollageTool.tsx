'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { 
  Upload, 
  Download, 
  Image as ImageIcon,
  Layout,
  Grid,
  Settings,
  Eye,
  Layers,
  Move,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Palette,
  Type,
  Trash2,
  Copy,
  Maximize,
  Minimize,
  Square,
  Circle,
  Hexagon
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CollageImage {
  id: string
  file: File
  url: string
  x: number
  y: number
  width: number
  height: number
  rotation: number
  opacity: number
  zIndex: number
  borderRadius: number
  borderWidth: number
  borderColor: string
  shadow: boolean
  locked: boolean
}

interface CollageTemplate {
  id: string
  name: string
  description: string
  layout: {
    canvasWidth: number
    canvasHeight: number
    positions: {
      x: number
      y: number
      width: number
      height: number
    }[]
  }
}

interface ExportSettings {
  format: 'png' | 'jpg' | 'webp'
  quality: number
  width: number
  height: number
  backgroundColor: string
  includeWatermark: boolean
  watermarkText: string
  watermarkOpacity: number
}

const ImageCollageTool: React.FC = () => {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const [images, setImages] = useState<CollageImage[]>([])
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null)
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 })
  const [backgroundColor, setBackgroundColor] = useState('#ffffff')
  const [zoom, setZoom] = useState(100)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  const [exportSettings, setExportSettings] = useState<ExportSettings>({
    format: 'png',
    quality: 90,
    width: 1920,
    height: 1080,
    backgroundColor: '#ffffff',
    includeWatermark: false,
    watermarkText: 'Created with PoorLow Tools',
    watermarkOpacity: 50
  })

  const templates: CollageTemplate[] = [
    {
      id: 'grid-2x2',
      name: '2×2 网格',
      description: '四张图片等大小排列',
      layout: {
        canvasWidth: 800,
        canvasHeight: 800,
        positions: [
          { x: 0, y: 0, width: 400, height: 400 },
          { x: 400, y: 0, width: 400, height: 400 },
          { x: 0, y: 400, width: 400, height: 400 },
          { x: 400, y: 400, width: 400, height: 400 }
        ]
      }
    },
    {
      id: 'grid-3x3',
      name: '3×3 网格',
      description: '九张图片网格布局',
      layout: {
        canvasWidth: 900,
        canvasHeight: 900,
        positions: Array.from({ length: 9 }, (_, i) => ({
          x: (i % 3) * 300,
          y: Math.floor(i / 3) * 300,
          width: 300,
          height: 300
        }))
      }
    },
    {
      id: 'collage-magazine',
      name: '杂志风格',
      description: '不规则杂志风格拼贴',
      layout: {
        canvasWidth: 1200,
        canvasHeight: 800,
        positions: [
          { x: 0, y: 0, width: 600, height: 400 },
          { x: 600, y: 0, width: 300, height: 200 },
          { x: 900, y: 0, width: 300, height: 400 },
          { x: 600, y: 200, width: 300, height: 200 },
          { x: 0, y: 400, width: 400, height: 400 },
          { x: 400, y: 400, width: 400, height: 200 },
          { x: 800, y: 400, width: 400, height: 400 }
        ]
      }
    },
    {
      id: 'polaroid',
      name: '宝丽来风格',
      description: '仿宝丽来照片效果',
      layout: {
        canvasWidth: 1000,
        canvasHeight: 800,
        positions: [
          { x: 50, y: 50, width: 280, height: 320 },
          { x: 360, y: 100, width: 280, height: 320 },
          { x: 670, y: 150, width: 280, height: 320 },
          { x: 200, y: 450, width: 280, height: 320 }
        ]
      }
    },
    {
      id: 'timeline',
      name: '时间线',
      description: '水平时间线布局',
      layout: {
        canvasWidth: 1400,
        canvasHeight: 400,
        positions: [
          { x: 0, y: 0, width: 280, height: 400 },
          { x: 280, y: 50, width: 280, height: 300 },
          { x: 560, y: 0, width: 280, height: 400 },
          { x: 840, y: 50, width: 280, height: 300 },
          { x: 1120, y: 0, width: 280, height: 400 }
        ]
      }
    }
  ]

  // 文件上传处理
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    files.forEach((file, index) => {
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file)
        const newImage: CollageImage = {
          id: `image-${Date.now()}-${index}`,
          file,
          url,
          x: 50 + index * 20,
          y: 50 + index * 20,
          width: 200,
          height: 200,
          rotation: 0,
          opacity: 100,
          zIndex: images.length + index,
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#000000',
          shadow: false,
          locked: false
        }
        setImages(prev => [...prev, newImage])
      }
    })
  }, [images.length])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    const files = Array.from(event.dataTransfer.files)
    files.forEach((file, index) => {
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file)
        const newImage: CollageImage = {
          id: `image-${Date.now()}-${index}`,
          file,
          url,
          x: 50 + index * 20,
          y: 50 + index * 20,
          width: 200,
          height: 200,
          rotation: 0,
          opacity: 100,
          zIndex: images.length + index,
          borderRadius: 0,
          borderWidth: 0,
          borderColor: '#000000',
          shadow: false,
          locked: false
        }
        setImages(prev => [...prev, newImage])
      }
    })
  }, [images.length])

  // 应用模板
  const applyTemplate = useCallback((template: CollageTemplate) => {
    setCanvasSize({
      width: template.layout.canvasWidth,
      height: template.layout.canvasHeight
    })

    const updatedImages = images.slice(0, template.layout.positions.length).map((image, index) => {
      const position = template.layout.positions[index]
      return {
        ...image,
        x: position.x,
        y: position.y,
        width: position.width,
        height: position.height,
        zIndex: index
      }
    })

    setImages(updatedImages)
    toast({
      title: '模板已应用',
      description: `已应用"${template.name}"模板`
    })
  }, [images, toast])

  // 图片操作
  const updateImage = useCallback((imageId: string, updates: Partial<CollageImage>) => {
    setImages(prev => prev.map(img => 
      img.id === imageId ? { ...img, ...updates } : img
    ))
  }, [])

  const removeImage = useCallback((imageId: string) => {
    setImages(prev => prev.filter(img => img.id !== imageId))
    if (selectedImageId === imageId) {
      setSelectedImageId(null)
    }
  }, [selectedImageId])

  const duplicateImage = useCallback((imageId: string) => {
    const image = images.find(img => img.id === imageId)
    if (image) {
      const newImage: CollageImage = {
        ...image,
        id: `image-${Date.now()}`,
        x: image.x + 20,
        y: image.y + 20,
        zIndex: Math.max(...images.map(img => img.zIndex)) + 1
      }
      setImages(prev => [...prev, newImage])
    }
  }, [images])

  const bringToFront = useCallback((imageId: string) => {
    const maxZIndex = Math.max(...images.map(img => img.zIndex))
    updateImage(imageId, { zIndex: maxZIndex + 1 })
  }, [images, updateImage])

  const sendToBack = useCallback((imageId: string) => {
    const minZIndex = Math.min(...images.map(img => img.zIndex))
    updateImage(imageId, { zIndex: minZIndex - 1 })
  }, [images, updateImage])

  // 画布渲染
  const renderCanvas = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')!
    canvas.width = canvasSize.width
    canvas.height = canvasSize.height

    // 清空画布
    ctx.fillStyle = backgroundColor
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 按 z-index 排序绘制图片
    const sortedImages = [...images].sort((a, b) => a.zIndex - b.zIndex)

    sortedImages.forEach(image => {
      const img = new Image()
      img.onload = () => {
        ctx.save()
        
        // 设置透明度
        ctx.globalAlpha = image.opacity / 100

        // 移动到图片中心
        const centerX = image.x + image.width / 2
        const centerY = image.y + image.height / 2
        ctx.translate(centerX, centerY)

        // 旋转
        ctx.rotate((image.rotation * Math.PI) / 180)

        // 绘制阴影
        if (image.shadow) {
          ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
          ctx.shadowBlur = 10
          ctx.shadowOffsetX = 5
          ctx.shadowOffsetY = 5
        }

        // 绘制边框
        if (image.borderWidth > 0) {
          ctx.strokeStyle = image.borderColor
          ctx.lineWidth = image.borderWidth
          if (image.borderRadius > 0) {
            // 圆角矩形边框
            ctx.beginPath()
            ctx.roundRect(
              -image.width / 2 - image.borderWidth / 2,
              -image.height / 2 - image.borderWidth / 2,
              image.width + image.borderWidth,
              image.height + image.borderWidth,
              image.borderRadius
            )
            ctx.stroke()
          } else {
            ctx.strokeRect(
              -image.width / 2,
              -image.height / 2,
              image.width,
              image.height
            )
          }
        }

        // 裁剪圆角
        if (image.borderRadius > 0) {
          ctx.beginPath()
          ctx.roundRect(
            -image.width / 2,
            -image.height / 2,
            image.width,
            image.height,
            image.borderRadius
          )
          ctx.clip()
        }

        // 绘制图片
        ctx.drawImage(
          img,
          -image.width / 2,
          -image.height / 2,
          image.width,
          image.height
        )

        // 选择框
        if (selectedImageId === image.id) {
          ctx.strokeStyle = '#3b82f6'
          ctx.lineWidth = 2
          ctx.setLineDash([5, 5])
          ctx.strokeRect(
            -image.width / 2 - 2,
            -image.height / 2 - 2,
            image.width + 4,
            image.height + 4
          )
          ctx.setLineDash([])
        }

        ctx.restore()
      }
      img.src = image.url
    })
  }, [images, canvasSize, backgroundColor, selectedImageId])

  // 导出功能
  const exportCollage = useCallback(() => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = exportSettings.width
    canvas.height = exportSettings.height

    // 计算缩放比例
    const scaleX = exportSettings.width / canvasSize.width
    const scaleY = exportSettings.height / canvasSize.height

    // 背景
    ctx.fillStyle = exportSettings.backgroundColor
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 绘制图片
    const sortedImages = [...images].sort((a, b) => a.zIndex - b.zIndex)

    Promise.all(
      sortedImages.map(image => {
        return new Promise<void>((resolve) => {
          const img = new Image()
          img.onload = () => {
            ctx.save()
            ctx.globalAlpha = image.opacity / 100

            const centerX = (image.x + image.width / 2) * scaleX
            const centerY = (image.y + image.height / 2) * scaleY
            const width = image.width * scaleX
            const height = image.height * scaleY

            ctx.translate(centerX, centerY)
            ctx.rotate((image.rotation * Math.PI) / 180)

            if (image.shadow) {
              ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
              ctx.shadowBlur = 10 * Math.min(scaleX, scaleY)
              ctx.shadowOffsetX = 5 * scaleX
              ctx.shadowOffsetY = 5 * scaleY
            }

            if (image.borderRadius > 0) {
              ctx.beginPath()
              ctx.roundRect(
                -width / 2,
                -height / 2,
                width,
                height,
                image.borderRadius * Math.min(scaleX, scaleY)
              )
              ctx.clip()
            }

            ctx.drawImage(img, -width / 2, -height / 2, width, height)
            ctx.restore()
            resolve()
          }
          img.src = image.url
        })
      })
    ).then(() => {
      // 添加水印
      if (exportSettings.includeWatermark && exportSettings.watermarkText) {
        ctx.save()
        ctx.globalAlpha = exportSettings.watermarkOpacity / 100
        ctx.fillStyle = '#000000'
        ctx.font = `${Math.max(16, canvas.width / 50)}px Arial`
        ctx.textAlign = 'right'
        ctx.fillText(
          exportSettings.watermarkText,
          canvas.width - 20,
          canvas.height - 20
        )
        ctx.restore()
      }

      // 导出
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `collage-${Date.now()}.${exportSettings.format}`
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)

          toast({
            title: '导出完成',
            description: `拼图已导出为 ${exportSettings.format.toUpperCase()} 格式`
          })
        }
      }, `image/${exportSettings.format}`, exportSettings.quality / 100)
    })
  }, [images, canvasSize, exportSettings, toast])

  // 清理资源
  useEffect(() => {
    return () => {
      images.forEach(image => {
        URL.revokeObjectURL(image.url)
      })
    }
  }, [images])

  // 重新渲染画布
  useEffect(() => {
    renderCanvas()
  }, [renderCanvas])

  const selectedImage = images.find(img => img.id === selectedImageId)

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Layout className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">图片拼图工具</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          创建精美的图片拼贴，支持多种布局模板、自由拖拽和丰富的编辑功能
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 左侧控制面板 */}
        <div className="lg:col-span-1 space-y-6">
          {/* 图片管理 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                图片管理
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={() => fileInputRef.current?.click()}
                className="w-full"
                variant="outline"
              >
                <Upload className="h-4 w-4 mr-2" />
                添加图片
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleFileUpload}
                className="hidden"
              />

              <div className="text-sm text-gray-600">
                已添加: {images.length} 张图片
              </div>

              <div className="space-y-2 max-h-48 overflow-y-auto">
                {images.map((image, index) => (
                  <div
                    key={image.id}
                    className={`p-2 rounded border cursor-pointer transition-colors ${
                      selectedImageId === image.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedImageId(image.id)}
                  >
                    <div className="flex items-center gap-2">
                      <img
                        src={image.url}
                        alt="Preview"
                        className="w-8 h-8 object-cover rounded"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium">图片 {index + 1}</div>
                        <div className="text-xs text-gray-500">
                          {image.width} × {image.height}
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={(e) => {
                          e.stopPropagation()
                          removeImage(image.id)
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 模板选择 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Grid className="h-5 w-5" />
                布局模板
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {templates.map(template => (
                <div key={template.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <div className="font-medium text-sm">{template.name}</div>
                      <div className="text-xs text-gray-500">{template.description}</div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => applyTemplate(template)}
                      disabled={images.length === 0}
                    >
                      应用
                    </Button>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {template.layout.positions.length} 个位置
                  </Badge>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* 画布设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                画布设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">宽度</Label>
                  <Input
                    type="number"
                    value={canvasSize.width}
                    onChange={(e) => setCanvasSize(prev => ({ ...prev, width: parseInt(e.target.value) || 800 }))}
                    min={100}
                    max={4000}
                  />
                </div>
                <div>
                  <Label className="text-xs">高度</Label>
                  <Input
                    type="number"
                    value={canvasSize.height}
                    onChange={(e) => setCanvasSize(prev => ({ ...prev, height: parseInt(e.target.value) || 600 }))}
                    min={100}
                    max={4000}
                  />
                </div>
              </div>

              <div>
                <Label className="text-xs">背景色</Label>
                <div className="flex gap-2">
                  <Input
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="w-12 p-1"
                  />
                  <Input
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    placeholder="#ffffff"
                    className="flex-1"
                  />
                </div>
              </div>

              <div>
                <Label className="text-xs">缩放: {zoom}%</Label>
                <Slider
                  value={[zoom]}
                  onValueChange={([value]) => setZoom(value)}
                  min={25}
                  max={200}
                  step={25}
                  className="mt-2"
                />
              </div>
            </CardContent>
          </Card>

          {/* 图片属性编辑 */}
          {selectedImage && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layers className="h-5 w-5" />
                  图片属性
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs">X位置</Label>
                    <Input
                      type="number"
                      value={selectedImage.x}
                      onChange={(e) => updateImage(selectedImage.id, { x: parseInt(e.target.value) || 0 })}
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Y位置</Label>
                    <Input
                      type="number"
                      value={selectedImage.y}
                      onChange={(e) => updateImage(selectedImage.id, { y: parseInt(e.target.value) || 0 })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs">宽度</Label>
                    <Input
                      type="number"
                      value={selectedImage.width}
                      onChange={(e) => updateImage(selectedImage.id, { width: parseInt(e.target.value) || 100 })}
                      min={10}
                    />
                  </div>
                  <div>
                    <Label className="text-xs">高度</Label>
                    <Input
                      type="number"
                      value={selectedImage.height}
                      onChange={(e) => updateImage(selectedImage.id, { height: parseInt(e.target.value) || 100 })}
                      min={10}
                    />
                  </div>
                </div>

                <div>
                  <Label className="text-xs">旋转: {selectedImage.rotation}°</Label>
                  <Slider
                    value={[selectedImage.rotation]}
                    onValueChange={([value]) => updateImage(selectedImage.id, { rotation: value })}
                    min={-180}
                    max={180}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label className="text-xs">透明度: {selectedImage.opacity}%</Label>
                  <Slider
                    value={[selectedImage.opacity]}
                    onValueChange={([value]) => updateImage(selectedImage.id, { opacity: value })}
                    min={0}
                    max={100}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label className="text-xs">圆角: {selectedImage.borderRadius}px</Label>
                  <Slider
                    value={[selectedImage.borderRadius]}
                    onValueChange={([value]) => updateImage(selectedImage.id, { borderRadius: value })}
                    min={0}
                    max={50}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label className="text-xs">边框宽度: {selectedImage.borderWidth}px</Label>
                  <Slider
                    value={[selectedImage.borderWidth]}
                    onValueChange={([value]) => updateImage(selectedImage.id, { borderWidth: value })}
                    min={0}
                    max={20}
                    step={1}
                    className="mt-2"
                  />
                </div>

                {selectedImage.borderWidth > 0 && (
                  <div>
                    <Label className="text-xs">边框颜色</Label>
                    <Input
                      type="color"
                      value={selectedImage.borderColor}
                      onChange={(e) => updateImage(selectedImage.id, { borderColor: e.target.value })}
                      className="w-full p-1"
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <Label className="text-xs">阴影效果</Label>
                  <Switch
                    checked={selectedImage.shadow}
                    onCheckedChange={(checked) => updateImage(selectedImage.id, { shadow: checked })}
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => duplicateImage(selectedImage.id)}
                    variant="outline"
                    className="flex-1"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    复制
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => bringToFront(selectedImage.id)}
                    variant="outline"
                    className="flex-1"
                  >
                    置顶
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 右侧画布区域 */}
        <div className="lg:col-span-3 space-y-6">
          {/* 画布预览 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                画布预览
              </CardTitle>
            </CardHeader>
            <CardContent>
              {images.length === 0 ? (
                <div 
                  className="flex flex-col items-center justify-center py-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors"
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-lg font-medium text-gray-700">开始创建拼图</p>
                  <p className="text-sm text-gray-500">拖拽图片到此处或点击添加</p>
                </div>
              ) : (
                <div 
                  ref={containerRef}
                  className="relative border border-gray-200 rounded-lg overflow-hidden"
                  style={{ 
                    width: Math.min(800, canvasSize.width * (zoom / 100)),
                    height: Math.min(600, canvasSize.height * (zoom / 100))
                  }}
                >
                  <canvas
                    ref={canvasRef}
                    className="absolute inset-0 cursor-move"
                    style={{
                      transform: `scale(${zoom / 100})`,
                      transformOrigin: 'top left'
                    }}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* 导出设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                导出设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm">格式</Label>
                  <Select value={exportSettings.format} onValueChange={(value: 'png' | 'jpg' | 'webp') => setExportSettings(prev => ({ ...prev, format: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="png">PNG</SelectItem>
                      <SelectItem value="jpg">JPG</SelectItem>
                      <SelectItem value="webp">WebP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm">宽度</Label>
                  <Input
                    type="number"
                    value={exportSettings.width}
                    onChange={(e) => setExportSettings(prev => ({ ...prev, width: parseInt(e.target.value) || 1920 }))}
                    min={100}
                    max={8000}
                  />
                </div>

                <div>
                  <Label className="text-sm">高度</Label>
                  <Input
                    type="number"
                    value={exportSettings.height}
                    onChange={(e) => setExportSettings(prev => ({ ...prev, height: parseInt(e.target.value) || 1080 }))}
                    min={100}
                    max={8000}
                  />
                </div>
              </div>

              {exportSettings.format === 'jpg' && (
                <div>
                  <Label className="text-sm">质量: {exportSettings.quality}%</Label>
                  <Slider
                    value={[exportSettings.quality]}
                    onValueChange={([value]) => setExportSettings(prev => ({ ...prev, quality: value }))}
                    min={1}
                    max={100}
                    step={1}
                    className="mt-2"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label className="text-sm">包含水印</Label>
                <Switch
                  checked={exportSettings.includeWatermark}
                  onCheckedChange={(checked) => setExportSettings(prev => ({ ...prev, includeWatermark: checked }))}
                />
              </div>

              {exportSettings.includeWatermark && (
                <>
                  <div>
                    <Label className="text-sm">水印文字</Label>
                    <Input
                      value={exportSettings.watermarkText}
                      onChange={(e) => setExportSettings(prev => ({ ...prev, watermarkText: e.target.value }))}
                      placeholder="Created with PoorLow Tools"
                    />
                  </div>

                  <div>
                    <Label className="text-sm">水印透明度: {exportSettings.watermarkOpacity}%</Label>
                    <Slider
                      value={[exportSettings.watermarkOpacity]}
                      onValueChange={([value]) => setExportSettings(prev => ({ ...prev, watermarkOpacity: value }))}
                      min={0}
                      max={100}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </>
              )}

              <Button 
                onClick={exportCollage} 
                className="w-full" 
                size="lg"
                disabled={images.length === 0}
              >
                <Download className="h-5 w-5 mr-2" />
                导出拼图
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default ImageCollageTool