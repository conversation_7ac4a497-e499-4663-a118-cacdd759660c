'use client'

import React, { useState, useRef, useCallback } from 'react'
import NextImage from 'next/image'
import { Upload, Download, Type, Image as ImageIcon, Sliders, RefreshCw, X } from 'lucide-react'

interface WatermarkSettings {
  text: string
  fontSize: number
  fontFamily: string
  color: string
  opacity: number
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
  rotation: number
  offsetX: number
  offsetY: number
  mode: 'text' | 'image'
  imageUrl: string
  imageSize: number
}

export default function ImageWatermarkTool() {
  const [originalImage, setOriginalImage] = useState<string | null>(null)
  const [watermarkedImage, setWatermarkedImage] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 })
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const watermarkInputRef = useRef<HTMLInputElement>(null)
  
  const [settings, setSettings] = useState<WatermarkSettings>({
    text: '© 版权所有',
    fontSize: 30,
    fontFamily: 'Arial',
    color: '#ffffff',
    opacity: 0.5,
    position: 'bottom-right',
    rotation: 0,
    offsetX: 20,
    offsetY: 20,
    mode: 'text',
    imageUrl: '',
    imageSize: 100
  })

  // 处理图片上传
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      const img = new Image()
      img.onload = () => {
        setImageSize({ width: img.width, height: img.height })
        setOriginalImage(event.target?.result as string)
        applyWatermark(event.target?.result as string, img.width, img.height)
      }
      img.src = event.target?.result as string
    }
    reader.readAsDataURL(file)
  }

  // 处理水印图片上传
  const handleWatermarkImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      setSettings(prev => ({ ...prev, imageUrl: event.target?.result as string }))
      if (originalImage) {
        applyWatermark(originalImage, imageSize.width, imageSize.height)
      }
    }
    reader.readAsDataURL(file)
  }

  // 应用水印
  const applyWatermark = useCallback((imageSrc: string, width: number, height: number) => {
    if (!canvasRef.current) return
    
    setIsProcessing(true)
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = width
    canvas.height = height

    const img = new Image()
    img.onload = () => {
      // 绘制原图
      ctx.drawImage(img, 0, 0)

      // 设置水印样式
      ctx.globalAlpha = settings.opacity

      if (settings.mode === 'text') {
        // 文字水印
        ctx.font = `${settings.fontSize}px ${settings.fontFamily}`
        ctx.fillStyle = settings.color
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'

        // 计算位置
        let x = 0, y = 0
        const textWidth = ctx.measureText(settings.text).width
        const textHeight = settings.fontSize

        switch (settings.position) {
          case 'top-left':
            x = textWidth / 2 + settings.offsetX
            y = textHeight / 2 + settings.offsetY
            break
          case 'top-right':
            x = width - textWidth / 2 - settings.offsetX
            y = textHeight / 2 + settings.offsetY
            break
          case 'bottom-left':
            x = textWidth / 2 + settings.offsetX
            y = height - textHeight / 2 - settings.offsetY
            break
          case 'bottom-right':
            x = width - textWidth / 2 - settings.offsetX
            y = height - textHeight / 2 - settings.offsetY
            break
          case 'center':
            x = width / 2
            y = height / 2
            break
        }

        // 应用旋转
        ctx.save()
        ctx.translate(x, y)
        ctx.rotate((settings.rotation * Math.PI) / 180)
        ctx.fillText(settings.text, 0, 0)
        ctx.restore()
      } else if (settings.mode === 'image' && settings.imageUrl) {
        // 图片水印
        const watermarkImg = new Image()
        watermarkImg.onload = () => {
          const wmWidth = settings.imageSize
          const wmHeight = (watermarkImg.height / watermarkImg.width) * settings.imageSize

          let x = 0, y = 0
          switch (settings.position) {
            case 'top-left':
              x = settings.offsetX
              y = settings.offsetY
              break
            case 'top-right':
              x = width - wmWidth - settings.offsetX
              y = settings.offsetY
              break
            case 'bottom-left':
              x = settings.offsetX
              y = height - wmHeight - settings.offsetY
              break
            case 'bottom-right':
              x = width - wmWidth - settings.offsetX
              y = height - wmHeight - settings.offsetY
              break
            case 'center':
              x = (width - wmWidth) / 2
              y = (height - wmHeight) / 2
              break
          }

          ctx.save()
          ctx.translate(x + wmWidth / 2, y + wmHeight / 2)
          ctx.rotate((settings.rotation * Math.PI) / 180)
          ctx.drawImage(watermarkImg, -wmWidth / 2, -wmHeight / 2, wmWidth, wmHeight)
          ctx.restore()

          // 导出结果
          setWatermarkedImage(canvas.toDataURL('image/png'))
          setIsProcessing(false)
        }
        watermarkImg.src = settings.imageUrl
        return
      }

      // 导出结果
      setWatermarkedImage(canvas.toDataURL('image/png'))
      setIsProcessing(false)
    }
    img.src = imageSrc
  }, [settings])

  // 更新设置
  const updateSettings = (key: keyof WatermarkSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    if (originalImage) {
      applyWatermark(originalImage, imageSize.width, imageSize.height)
    }
  }

  // 下载图片
  const downloadImage = () => {
    if (!watermarkedImage) return
    
    const link = document.createElement('a')
    link.href = watermarkedImage
    link.download = `watermarked_${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 重置
  const reset = () => {
    setOriginalImage(null)
    setWatermarkedImage(null)
    setImageSize({ width: 0, height: 0 })
    if (fileInputRef.current) fileInputRef.current.value = ''
    if (watermarkInputRef.current) watermarkInputRef.current.value = ''
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片水印工具</h2>
        <p className="text-gray-600">
          为图片添加文字或图片水印，保护您的版权
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {/* 设置面板 */}
        <div className="md:col-span-1 space-y-4">
          <div className="bg-white rounded-lg border border-gray-300 p-4">
            <h3 className="font-medium mb-4">水印设置</h3>
            
            {/* 水印模式 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">水印类型</label>
              <div className="flex gap-2">
                <button
                  onClick={() => updateSettings('mode', 'text')}
                  className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    settings.mode === 'text'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  <Type className="w-4 h-4 inline mr-1" />
                  文字
                </button>
                <button
                  onClick={() => updateSettings('mode', 'image')}
                  className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    settings.mode === 'image'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  <ImageIcon className="w-4 h-4 inline mr-1" />
                  图片
                </button>
              </div>
            </div>

            {settings.mode === 'text' ? (
              <>
                {/* 文字内容 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">水印文字</label>
                  <input
                    type="text"
                    value={settings.text}
                    onChange={(e) => updateSettings('text', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="输入水印文字"
                  />
                </div>

                {/* 字体大小 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">
                    字体大小: {settings.fontSize}px
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="100"
                    value={settings.fontSize}
                    onChange={(e) => updateSettings('fontSize', Number(e.target.value))}
                    className="w-full"
                  />
                </div>

                {/* 字体 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">字体</label>
                  <select
                    value={settings.fontFamily}
                    onChange={(e) => updateSettings('fontFamily', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="Arial">Arial</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Courier New">Courier New</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Verdana">Verdana</option>
                  </select>
                </div>

                {/* 颜色 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">颜色</label>
                  <input
                    type="color"
                    value={settings.color}
                    onChange={(e) => updateSettings('color', e.target.value)}
                    className="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
                  />
                </div>
              </>
            ) : (
              <>
                {/* 水印图片上传 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">水印图片</label>
                  <input
                    ref={watermarkInputRef}
                    type="file"
                    onChange={handleWatermarkImageUpload}
                    accept="image/*"
                    className="hidden"
                  />
                  <button
                    onClick={() => watermarkInputRef.current?.click()}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-sm"
                  >
                    <Upload className="w-4 h-4 inline mr-2" />
                    选择水印图片
                  </button>
                </div>

                {/* 图片大小 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">
                    水印大小: {settings.imageSize}px
                  </label>
                  <input
                    type="range"
                    min="20"
                    max="300"
                    value={settings.imageSize}
                    onChange={(e) => updateSettings('imageSize', Number(e.target.value))}
                    className="w-full"
                  />
                </div>
              </>
            )}

            {/* 透明度 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                透明度: {Math.round(settings.opacity * 100)}%
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={settings.opacity * 100}
                onChange={(e) => updateSettings('opacity', Number(e.target.value) / 100)}
                className="w-full"
              />
            </div>

            {/* 位置 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">位置</label>
              <select
                value={settings.position}
                onChange={(e) => updateSettings('position', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="top-left">左上角</option>
                <option value="top-right">右上角</option>
                <option value="bottom-left">左下角</option>
                <option value="bottom-right">右下角</option>
                <option value="center">居中</option>
              </select>
            </div>

            {/* 旋转角度 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                旋转角度: {settings.rotation}°
              </label>
              <input
                type="range"
                min="-180"
                max="180"
                value={settings.rotation}
                onChange={(e) => updateSettings('rotation', Number(e.target.value))}
                className="w-full"
              />
            </div>

            {/* 偏移量 */}
            <div className="grid grid-cols-2 gap-2 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">水平偏移</label>
                <input
                  type="number"
                  value={settings.offsetX}
                  onChange={(e) => updateSettings('offsetX', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">垂直偏移</label>
                <input
                  type="number"
                  value={settings.offsetY}
                  onChange={(e) => updateSettings('offsetY', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 预览区域 */}
        <div className="md:col-span-2">
          {!originalImage ? (
            <div className="bg-white rounded-lg border-2 border-dashed border-gray-300 p-12">
              <div className="text-center">
                <ImageIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">上传图片以添加水印</p>
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleImageUpload}
                  accept="image/*"
                  className="hidden"
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  <Upload className="w-4 h-4 inline mr-2" />
                  选择图片
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-white rounded-lg border border-gray-300 p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-medium">预览</h3>
                  <div className="flex gap-2">
                    <button
                      onClick={downloadImage}
                      disabled={!watermarkedImage || isProcessing}
                      className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    >
                      <Download className="w-4 h-4 inline mr-2" />
                      下载
                    </button>
                    <button
                      onClick={reset}
                      className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                    >
                      <RefreshCw className="w-4 h-4 inline mr-2" />
                      重置
                    </button>
                  </div>
                </div>
                
                <div className="relative">
                  {watermarkedImage && (
                    <NextImage
                      src={watermarkedImage}
                      alt="Watermarked"
                      width={800}
                      height={600}
                      className="w-full rounded-lg object-contain"
                      unoptimized
                    />
                  )}
                  {isProcessing && (
                    <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-2"></div>
                        <p className="text-gray-600">处理中...</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 图片信息 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium mb-2">图片信息</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>尺寸: {imageSize.width} × {imageSize.height} 像素</p>
                  <p>格式: PNG (输出格式)</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Canvas 用于处理图片 */}
      <canvas ref={canvasRef} className="hidden" />

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Sliders className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持文字水印和图片水印两种模式</li>
              <li>可自定义水印的位置、大小、透明度和旋转角度</li>
              <li>文字水印支持多种字体和颜色选择</li>
              <li>图片水印支持调整大小和透明度</li>
              <li>实时预览水印效果，所见即所得</li>
              <li>支持常见图片格式（JPG、PNG、GIF等）</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
