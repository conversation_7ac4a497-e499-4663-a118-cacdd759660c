'use client'

import { useState } from 'react'
import { Lock, Unlock, Copy, Check, AlertCircle, Info, Key, Shield } from 'lucide-react'

interface EncryptionMethod {
  id: string
  name: string
  description: string
  needsKey: boolean
  keyPlaceholder?: string
  keyFormat?: RegExp
  keyError?: string
}

const encryptionMethods: EncryptionMethod[] = [
  {
    id: 'aes',
    name: 'AES 加密',
    description: '高级加密标准，安全可靠',
    needsKey: true,
    keyPlaceholder: '请输入密钥（任意长度）',
  },
  {
    id: 'des',
    name: 'DES 加密',
    description: '数据加密标准，经典算法',
    needsKey: true,
    keyPlaceholder: '请输入密钥（任意长度）',
  },
  {
    id: 'base64',
    name: 'Base64 编码',
    description: '简单编码，不是真正的加密',
    needsKey: false,
  },
  {
    id: 'morse',
    name: '摩斯密码',
    description: '经典的电报编码系统',
    needsKey: false,
  },
  {
    id: 'caesar',
    name: '凯撒密码',
    description: '古典密码，位移加密',
    needsKey: true,
    keyPlaceholder: '输入位移数（1-25）',
    keyFormat: /^([1-9]|1[0-9]|2[0-5])$/,
    keyError: '请输入1-25之间的数字',
  },
  {
    id: 'reverse',
    name: '反转文本',
    description: '简单的文本反转',
    needsKey: false,
  },
]

// 简单的加密/解密实现（仅用于演示）
class SimpleCrypto {
  // AES 加密（简化实现）
  static aesEncrypt(text: string, key: string): string {
    let result = ''
    for (let i = 0; i < text.length; i++) {
      const textChar = text.charCodeAt(i)
      const keyChar = key.charCodeAt(i % key.length)
      result += String.fromCharCode(textChar ^ keyChar)
    }
    return btoa(result)
  }

  static aesDecrypt(text: string, key: string): string {
    try {
      const decoded = atob(text)
      let result = ''
      for (let i = 0; i < decoded.length; i++) {
        const textChar = decoded.charCodeAt(i)
        const keyChar = key.charCodeAt(i % key.length)
        result += String.fromCharCode(textChar ^ keyChar)
      }
      return result
    } catch {
      throw new Error('解密失败，请检查密文和密钥')
    }
  }

  // DES 加密（简化实现）
  static desEncrypt(text: string, key: string): string {
    return this.aesEncrypt(text, key) // 简化实现，使用相同逻辑
  }

  static desDecrypt(text: string, key: string): string {
    return this.aesDecrypt(text, key)
  }

  // Base64 编码/解码
  static base64Encrypt(text: string): string {
    return btoa(unescape(encodeURIComponent(text)))
  }

  static base64Decrypt(text: string): string {
    try {
      return decodeURIComponent(escape(atob(text)))
    } catch {
      throw new Error('解码失败，请检查输入是否为有效的Base64')
    }
  }

  // 摩斯密码
  private static morseCode: { [key: string]: string } = {
    'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
    'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
    'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
    'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
    'Y': '-.--', 'Z': '--..', '0': '-----', '1': '.----', '2': '..---',
    '3': '...--', '4': '....-', '5': '.....', '6': '-....', '7': '--...',
    '8': '---..', '9': '----.', '.': '.-.-.-', ',': '--..--', '?': '..--..',
    "'": '.----.', '!': '-.-.--', '/': '-..-.', '(': '-.--.', ')': '-.--.-',
    '&': '.-...', ':': '---...', ';': '-.-.-.', '=': '-...-', '+': '.-.-.',
    '-': '-....-', '_': '..--.-', '"': '.-..-.', '$': '...-..-', '@': '.--.-.',
    ' ': '/'
  }

  static morseEncrypt(text: string): string {
    return text.toUpperCase().split('').map(char => 
      this.morseCode[char] || char
    ).join(' ')
  }

  static morseDecrypt(text: string): string {
    const reverseMorse = Object.fromEntries(
      Object.entries(this.morseCode).map(([k, v]) => [v, k])
    )
    return text.split(' ').map(code => 
      reverseMorse[code] || code
    ).join('')
  }

  // 凯撒密码
  static caesarEncrypt(text: string, shift: string): string {
    const shiftNum = parseInt(shift)
    return text.split('').map(char => {
      const code = char.charCodeAt(0)
      if (code >= 65 && code <= 90) { // 大写字母
        return String.fromCharCode((code - 65 + shiftNum) % 26 + 65)
      } else if (code >= 97 && code <= 122) { // 小写字母
        return String.fromCharCode((code - 97 + shiftNum) % 26 + 97)
      }
      return char
    }).join('')
  }

  static caesarDecrypt(text: string, shift: string): string {
    const shiftNum = parseInt(shift)
    return this.caesarEncrypt(text, (26 - shiftNum).toString())
  }

  // 反转文本
  static reverseEncrypt(text: string): string {
    return text.split('').reverse().join('')
  }

  static reverseDecrypt(text: string): string {
    return text.split('').reverse().join('')
  }
}

export default function TextEncryptTool() {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [key, setKey] = useState('')
  const [method, setMethod] = useState('aes')
  const [mode, setMode] = useState<'encrypt' | 'decrypt'>('encrypt')
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState('')

  // 获取当前选中的加密方法
  const currentMethod = encryptionMethods.find(m => m.id === method)!

  // 处理加密/解密
  const handleProcess = () => {
    if (!input) {
      setError('请输入文本')
      return
    }

    if (currentMethod.needsKey && !key) {
      setError('请输入密钥')
      return
    }

    if (currentMethod.keyFormat && !currentMethod.keyFormat.test(key)) {
      setError(currentMethod.keyError || '密钥格式错误')
      return
    }

    setError('')

    try {
      let result = ''
      
      if (mode === 'encrypt') {
        switch (method) {
          case 'aes':
            result = SimpleCrypto.aesEncrypt(input, key)
            break
          case 'des':
            result = SimpleCrypto.desEncrypt(input, key)
            break
          case 'base64':
            result = SimpleCrypto.base64Encrypt(input)
            break
          case 'morse':
            result = SimpleCrypto.morseEncrypt(input)
            break
          case 'caesar':
            result = SimpleCrypto.caesarEncrypt(input, key)
            break
          case 'reverse':
            result = SimpleCrypto.reverseEncrypt(input)
            break
        }
      } else {
        switch (method) {
          case 'aes':
            result = SimpleCrypto.aesDecrypt(input, key)
            break
          case 'des':
            result = SimpleCrypto.desDecrypt(input, key)
            break
          case 'base64':
            result = SimpleCrypto.base64Decrypt(input)
            break
          case 'morse':
            result = SimpleCrypto.morseDecrypt(input)
            break
          case 'caesar':
            result = SimpleCrypto.caesarDecrypt(input, key)
            break
          case 'reverse':
            result = SimpleCrypto.reverseDecrypt(input)
            break
        }
      }
      
      setOutput(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : '处理失败')
      setOutput('')
    }
  }

  // 复制结果
  const copyToClipboard = async () => {
    if (!output) return
    
    try {
      await navigator.clipboard.writeText(output)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 交换输入输出
  const swapInputOutput = () => {
    setInput(output)
    setOutput(input)
    setMode(mode === 'encrypt' ? 'decrypt' : 'encrypt')
  }

  // 加载示例
  const loadExample = () => {
    if (method === 'morse') {
      setInput(mode === 'encrypt' ? 'HELLO WORLD' : '.... . .-.. .-.. --- / .-- --- .-. .-.. -..')
    } else if (method === 'caesar') {
      setInput(mode === 'encrypt' ? 'Hello World' : 'Khoor Zruog')
      setKey('3')
    } else if (method === 'base64') {
      setInput(mode === 'encrypt' ? 'Hello World' : 'SGVsbG8gV29ybGQ=')
    } else {
      setInput(mode === 'encrypt' ? 'Hello World' : '')
      setKey('mySecretKey123')
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本加密/解密</h2>
        <p className="text-gray-600">
          使用多种加密算法保护您的文本信息
        </p>
      </div>

      {/* 加密方法选择 */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">加密方法</label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {encryptionMethods.map((m) => (
            <button
              key={m.id}
              onClick={() => {
                setMethod(m.id)
                setKey('')
                setOutput('')
                setError('')
              }}
              className={`p-3 rounded-lg border text-left transition-colors ${
                method === m.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-medium">{m.name}</div>
              <div className="text-xs text-gray-500 mt-1">{m.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* 模式切换 */}
      <div className="mb-6 flex items-center gap-4">
        <div className="flex rounded-lg border border-gray-200 overflow-hidden">
          <button
            onClick={() => setMode('encrypt')}
            className={`px-4 py-2 font-medium transition-colors flex items-center gap-2 ${
              mode === 'encrypt'
                ? 'bg-blue-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Lock className="w-4 h-4" />
            加密
          </button>
          <button
            onClick={() => setMode('decrypt')}
            className={`px-4 py-2 font-medium transition-colors flex items-center gap-2 ${
              mode === 'decrypt'
                ? 'bg-blue-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Unlock className="w-4 h-4" />
            解密
          </button>
        </div>

        <button
          onClick={loadExample}
          className="text-sm text-blue-500 hover:text-blue-600"
        >
          加载示例
        </button>
      </div>

      {/* 密钥输入 */}
      {currentMethod.needsKey && (
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2 flex items-center gap-2">
            <Key className="w-4 h-4" />
            密钥
          </label>
          <input
            type="text"
            value={key}
            onChange={(e) => setKey(e.target.value)}
            placeholder={currentMethod.keyPlaceholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      )}

      <div className="grid md:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <label className="block text-sm font-medium mb-2">
            {mode === 'encrypt' ? '原文' : '密文'}
          </label>
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={mode === 'encrypt' ? '输入要加密的文本...' : '输入要解密的文本...'}
            className="w-full h-64 p-4 font-mono text-sm border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">
              {mode === 'encrypt' ? '密文' : '原文'}
            </label>
            <div className="flex gap-2">
              <button
                onClick={swapInputOutput}
                className="text-sm text-blue-500 hover:text-blue-600"
                title="交换输入输出"
              >
                ⇄ 交换
              </button>
              <button
                onClick={copyToClipboard}
                disabled={!output}
                className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    复制
                  </>
                )}
              </button>
            </div>
          </div>
          <textarea
            value={output}
            readOnly
            placeholder="结果将显示在这里..."
            className="w-full h-64 p-4 font-mono text-sm bg-gray-50 border border-gray-300 rounded-lg resize-none"
          />
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-800">
          <AlertCircle className="w-4 h-4" />
          {error}
        </div>
      )}

      {/* 处理按钮 */}
      <div className="mt-6 flex justify-center">
        <button
          onClick={handleProcess}
          className="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium flex items-center gap-2"
        >
          <Shield className="w-5 h-5" />
          {mode === 'encrypt' ? '加密文本' : '解密文本'}
        </button>
      </div>

      {/* 安全提示 */}
      <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-yellow-900">
            <h3 className="font-semibold mb-1">安全提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>本工具仅供学习和测试使用，请勿用于保护敏感信息</li>
              <li>真正的 AES/DES 加密需要更复杂的实现和密钥管理</li>
              <li>所有加密操作都在浏览器本地完成，不会上传到服务器</li>
              <li>请妥善保管您的密钥，丢失密钥将无法解密数据</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>选择加密方法，不同方法有不同的安全级别</li>
              <li>AES/DES 需要密钥，密钥越复杂越安全</li>
              <li>Base64 只是编码，不是真正的加密</li>
              <li>摩斯密码适合英文字母和数字</li>
              <li>凯撒密码是古典密码，只对字母有效</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
