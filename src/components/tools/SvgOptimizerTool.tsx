'use client'

import React, { useState, use<PERSON><PERSON>back, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from '@/hooks/use-toast'
import { 
  Upload, 
  Download, 
  Trash2, 
  <PERSON>tings, 
  FileCode,
  Minimize,
  Eye,
  <PERSON><PERSON>ff,
  <PERSON><PERSON>,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  <PERSON><PERSON>,
  File<PERSON>ext,
  Code,
  BarChart3,
  CheckCircle,
  AlertCircle,
  Info,
  Scissors,
  Palette,
  Move,
  RotateCw
} from 'lucide-react'

interface SvgFile {
  id: string
  name: string
  originalContent: string
  optimizedContent?: string
  originalSize: number
  optimizedSize?: number
  status: 'pending' | 'processing' | 'completed' | 'error'
  error?: string
  compressionRatio?: number
}

interface OptimizationSettings {
  // 基础优化
  removeDoctype: boolean
  removeXMLProcInst: boolean
  removeComments: boolean
  removeMetadata: boolean
  removeEditorsNSData: boolean
  
  // 结构优化
  cleanupAttrs: boolean
  mergeClipPaths: boolean
  removeEmptyAttrs: boolean
  removeEmptyText: boolean
  removeEmptyContainers: boolean
  removeUnusedNS: boolean
  
  // 路径优化
  convertPathData: boolean
  convertTransform: boolean
  removeHiddenElems: boolean
  removeNonInheritGroupAttrs: boolean
  collapseGroups: boolean
  
  // 样式优化
  cleanupEnableBackground: boolean
  removeStyleElement: boolean
  removeScriptElement: boolean
  inlineStyles: boolean
  minifyStyles: boolean
  
  // 尺寸优化
  removeDimensions: boolean
  removeViewBox: boolean
  convertColors: boolean
  
  // 数值精度
  floatPrecision: number
  
  // 自定义选项
  addClassIdPrefix: string
  removeTitle: boolean
  removeDesc: boolean
}

const DEFAULT_SETTINGS: OptimizationSettings = {
  removeDoctype: true,
  removeXMLProcInst: true,
  removeComments: true,
  removeMetadata: true,
  removeEditorsNSData: true,
  cleanupAttrs: true,
  mergeClipPaths: true,
  removeEmptyAttrs: true,
  removeEmptyText: true,
  removeEmptyContainers: true,
  removeUnusedNS: true,
  convertPathData: true,
  convertTransform: true,
  removeHiddenElems: true,
  removeNonInheritGroupAttrs: true,
  collapseGroups: true,
  cleanupEnableBackground: true,
  removeStyleElement: false,
  removeScriptElement: false,
  inlineStyles: true,
  minifyStyles: true,
  removeDimensions: false,
  removeViewBox: false,
  convertColors: true,
  floatPrecision: 3,
  addClassIdPrefix: '',
  removeTitle: false,
  removeDesc: false
}

const OPTIMIZATION_PRESETS = {
  'safe': {
    name: '安全优化',
    description: '保守的优化设置，保持SVG的兼容性',
    settings: {
      ...DEFAULT_SETTINGS,
      removeStyleElement: false,
      removeScriptElement: false,
      removeDimensions: false,
      removeViewBox: false,
      floatPrecision: 5
    }
  },
  'aggressive': {
    name: '激进优化',
    description: '最大化压缩，可能影响某些特性',
    settings: {
      ...DEFAULT_SETTINGS,
      removeStyleElement: true,
      removeScriptElement: true,
      removeDimensions: true,
      removeTitle: true,
      removeDesc: true,
      floatPrecision: 1
    }
  },
  'web': {
    name: 'Web优化',
    description: '专为网页使用优化，平衡大小和兼容性',
    settings: {
      ...DEFAULT_SETTINGS,
      removeStyleElement: false,
      removeScriptElement: true,
      removeDimensions: false,
      removeViewBox: false,
      floatPrecision: 2
    }
  }
}

export default function SvgOptimizerTool() {
  const [files, setFiles] = useState<SvgFile[]>([])
  const [settings, setSettings] = useState<OptimizationSettings>(DEFAULT_SETTINGS)
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null)
  const [showPreview, setShowPreview] = useState(true)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = useCallback((fileList: FileList | null) => {
    if (!fileList) return

    const validFiles = Array.from(fileList).filter(file => {
      if (!file.type.includes('svg') && !file.name.toLowerCase().endsWith('.svg')) {
        toast({
          title: "文件类型错误",
          description: `"${file.name}" 不是有效的SVG文件`,
          variant: "destructive",
        })
        return false
      }
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast({
          title: "文件过大",
          description: `"${file.name}" 超过10MB限制`,
          variant: "destructive",
        })
        return false
      }
      return true
    })

    validFiles.forEach(file => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        const svgFile: SvgFile = {
          id: `${Date.now()}-${Math.random()}`,
          name: file.name,
          originalContent: content,
          originalSize: new Blob([content]).size,
          status: 'pending'
        }
        setFiles(prev => [...prev, svgFile])
      }
      reader.readAsText(file)
    })
  }, [])

  const removeFile = useCallback((id: string) => {
    setFiles(prev => prev.filter(file => file.id !== id))
    if (selectedFileId === id) {
      setSelectedFileId(null)
    }
  }, [selectedFileId])

  const clearAllFiles = useCallback(() => {
    setFiles([])
    setSelectedFileId(null)
  }, [])

  const updateSettings = useCallback((key: keyof OptimizationSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }, [])

  const applyPreset = useCallback((presetKey: keyof typeof OPTIMIZATION_PRESETS) => {
    const preset = OPTIMIZATION_PRESETS[presetKey]
    setSettings(preset.settings)
    toast({
      title: "预设已应用",
      description: `已应用 "${preset.name}" 优化预设`,
    })
  }, [])

  // 简化的SVG优化函数（实际项目中应使用SVGO库）
  const optimizeSvg = useCallback((content: string): string => {
    let optimized = content

    // 移除XML声明
    if (settings.removeXMLProcInst) {
      optimized = optimized.replace(/<\?xml[^>]*\?>\s*/g, '')
    }

    // 移除DOCTYPE
    if (settings.removeDoctype) {
      optimized = optimized.replace(/<!DOCTYPE[^>]*>\s*/g, '')
    }

    // 移除注释
    if (settings.removeComments) {
      optimized = optimized.replace(/<!--[\s\S]*?-->/g, '')
    }

    // 移除title和desc元素
    if (settings.removeTitle) {
      optimized = optimized.replace(/<title[^>]*>[\s\S]*?<\/title>/g, '')
    }
    if (settings.removeDesc) {
      optimized = optimized.replace(/<desc[^>]*>[\s\S]*?<\/desc>/g, '')
    }

    // 移除metadata
    if (settings.removeMetadata) {
      optimized = optimized.replace(/<metadata[^>]*>[\s\S]*?<\/metadata>/g, '')
    }

    // 清理属性
    if (settings.cleanupAttrs) {
      // 移除空属性
      optimized = optimized.replace(/\s+[a-zA-Z-]+=""\s*/g, ' ')
      // 清理多余空格
      optimized = optimized.replace(/\s+/g, ' ')
    }

    // 移除空元素
    if (settings.removeEmptyText) {
      optimized = optimized.replace(/<text[^>]*>\s*<\/text>/g, '')
    }
    if (settings.removeEmptyContainers) {
      optimized = optimized.replace(/<g[^>]*>\s*<\/g>/g, '')
      optimized = optimized.replace(/<defs[^>]*>\s*<\/defs>/g, '')
    }

    // 数值精度优化
    if (settings.convertPathData) {
      // 简化路径数据中的数值精度
      optimized = optimized.replace(/(\d+\.\d{4,})/g, (match) => {
        return parseFloat(match).toFixed(settings.floatPrecision)
      })
    }

    // 颜色优化
    if (settings.convertColors) {
      // 将long hex转为short hex
      optimized = optimized.replace(/#([0-9a-fA-F])\1([0-9a-fA-F])\2([0-9a-fA-F])\3/g, '#$1$2$3')
      // 将RGB转为hex
      optimized = optimized.replace(/rgb\(\s*(\d+),\s*(\d+),\s*(\d+)\s*\)/g, (match, r, g, b) => {
        const hex = (parseInt(r) << 16 | parseInt(g) << 8 | parseInt(b)).toString(16).padStart(6, '0')
        return `#${hex}`
      })
    }

    // 移除样式元素
    if (settings.removeStyleElement) {
      optimized = optimized.replace(/<style[^>]*>[\s\S]*?<\/style>/g, '')
    }

    // 移除脚本元素
    if (settings.removeScriptElement) {
      optimized = optimized.replace(/<script[^>]*>[\s\S]*?<\/script>/g, '')
    }

    // 清理最终输出
    optimized = optimized.trim()
    optimized = optimized.replace(/>\s+</g, '><') // 移除标签间空白

    return optimized
  }, [settings])

  const processFile = useCallback(async (file: SvgFile): Promise<SvgFile> => {
    try {
      const optimizedContent = optimizeSvg(file.originalContent)
      const optimizedSize = new Blob([optimizedContent]).size
      const compressionRatio = ((file.originalSize - optimizedSize) / file.originalSize) * 100

      return {
        ...file,
        optimizedContent,
        optimizedSize,
        compressionRatio,
        status: 'completed'
      }
    } catch (error) {
      return {
        ...file,
        status: 'error',
        error: error instanceof Error ? error.message : '优化失败'
      }
    }
  }, [optimizeSvg])

  const processAllFiles = useCallback(async () => {
    if (files.length === 0) {
      toast({
        title: "没有文件",
        description: "请先上传要优化的SVG文件",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setProcessingProgress(0)

    try {
      const totalFiles = files.length
      const processedFiles: SvgFile[] = []

      for (let i = 0; i < totalFiles; i++) {
        const file = files[i]
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'processing' } : f
        ))

        const processedFile = await processFile(file)
        processedFiles.push(processedFile)
        
        setFiles(prev => prev.map(f => 
          f.id === file.id ? processedFile : f
        ))

        setProcessingProgress(((i + 1) / totalFiles) * 100)
      }

      const successCount = processedFiles.filter(f => f.status === 'completed').length
      const errorCount = processedFiles.filter(f => f.status === 'error').length
      const totalSavings = processedFiles
        .filter(f => f.compressionRatio)
        .reduce((sum, f) => sum + (f.originalSize - f.optimizedSize!), 0)

      toast({
        title: "优化完成",
        description: `成功优化 ${successCount} 个文件${errorCount > 0 ? `，${errorCount} 个失败` : ''}，节省 ${formatFileSize(totalSavings)}`,
      })
    } catch (error) {
      toast({
        title: "优化失败",
        description: error instanceof Error ? error.message : '未知错误',
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
      setProcessingProgress(0)
    }
  }, [files, processFile])

  const downloadFile = useCallback((file: SvgFile) => {
    if (!file.optimizedContent) return

    const blob = new Blob([file.optimizedContent], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    const nameWithoutExt = file.name.replace(/\.svg$/i, '')
    link.download = `${nameWithoutExt}_optimized.svg`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }, [])

  const downloadAllFiles = useCallback(async () => {
    const completedFiles = files.filter(f => f.status === 'completed' && f.optimizedContent)
    
    if (completedFiles.length === 0) {
      toast({
        title: "没有可下载的文件",
        description: "请先优化SVG文件",
        variant: "destructive",
      })
      return
    }

    // 批量下载
    for (const file of completedFiles) {
      downloadFile(file)
      // 添加小延迟避免浏览器阻止多个下载
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    toast({
      title: "下载完成",
      description: `已下载 ${completedFiles.length} 个优化后的文件`,
    })
  }, [files, downloadFile])

  const copyToClipboard = useCallback(async (content: string, type: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast({
        title: "已复制",
        description: `${type} 已复制到剪贴板`,
      })
    } catch (err) {
      toast({
        title: "复制失败",
        description: "无法访问剪贴板，请手动复制",
        variant: "destructive",
      })
    }
  }, [])

  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }, [])

  const getOptimizationStats = useCallback(() => {
    const completedFiles = files.filter(f => f.status === 'completed')
    const totalOriginalSize = completedFiles.reduce((sum, f) => sum + f.originalSize, 0)
    const totalOptimizedSize = completedFiles.reduce((sum, f) => sum + (f.optimizedSize || 0), 0)
    const totalSavings = totalOriginalSize - totalOptimizedSize
    const averageCompression = totalOriginalSize > 0 ? (totalSavings / totalOriginalSize) * 100 : 0

    return {
      totalFiles: completedFiles.length,
      totalOriginalSize,
      totalOptimizedSize,
      totalSavings,
      averageCompression
    }
  }, [files])

  return (
    <div className="w-full max-w-7xl mx-auto p-4 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          SVG优化工具
        </h1>
        <p className="text-gray-600">
          批量优化SVG文件，减小文件大小，提升网页加载速度
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 设置面板 */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Settings className="w-4 h-4" />
                优化设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 预设选择 */}
              <div className="space-y-2">
                <Label className="text-xs">优化预设</Label>
                <Select onValueChange={applyPreset}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="选择优化预设" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(OPTIMIZATION_PRESETS).map(([key, preset]) => (
                      <SelectItem key={key} value={key} className="text-xs">
                        <div>
                          <div className="font-medium">{preset.name}</div>
                          <div className="text-gray-500 text-xs">{preset.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              {/* 基础优化选项 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">基础清理</Label>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">移除XML声明</Label>
                    <Switch
                      checked={settings.removeXMLProcInst}
                      onCheckedChange={(checked) => updateSettings('removeXMLProcInst', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">移除DOCTYPE</Label>
                    <Switch
                      checked={settings.removeDoctype}
                      onCheckedChange={(checked) => updateSettings('removeDoctype', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">移除注释</Label>
                    <Switch
                      checked={settings.removeComments}
                      onCheckedChange={(checked) => updateSettings('removeComments', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">移除元数据</Label>
                    <Switch
                      checked={settings.removeMetadata}
                      onCheckedChange={(checked) => updateSettings('removeMetadata', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">移除编辑器数据</Label>
                    <Switch
                      checked={settings.removeEditorsNSData}
                      onCheckedChange={(checked) => updateSettings('removeEditorsNSData', checked)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* 结构优化选项 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">结构优化</Label>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">清理属性</Label>
                    <Switch
                      checked={settings.cleanupAttrs}
                      onCheckedChange={(checked) => updateSettings('cleanupAttrs', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">移除空属性</Label>
                    <Switch
                      checked={settings.removeEmptyAttrs}
                      onCheckedChange={(checked) => updateSettings('removeEmptyAttrs', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">移除空元素</Label>
                    <Switch
                      checked={settings.removeEmptyContainers}
                      onCheckedChange={(checked) => updateSettings('removeEmptyContainers', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">合并路径</Label>
                    <Switch
                      checked={settings.collapseGroups}
                      onCheckedChange={(checked) => updateSettings('collapseGroups', checked)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* 路径优化选项 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">路径优化</Label>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">优化路径数据</Label>
                    <Switch
                      checked={settings.convertPathData}
                      onCheckedChange={(checked) => updateSettings('convertPathData', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">优化变换</Label>
                    <Switch
                      checked={settings.convertTransform}
                      onCheckedChange={(checked) => updateSettings('convertTransform', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">移除隐藏元素</Label>
                    <Switch
                      checked={settings.removeHiddenElems}
                      onCheckedChange={(checked) => updateSettings('removeHiddenElems', checked)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* 样式优化选项 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">样式优化</Label>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">内联样式</Label>
                    <Switch
                      checked={settings.inlineStyles}
                      onCheckedChange={(checked) => updateSettings('inlineStyles', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">压缩样式</Label>
                    <Switch
                      checked={settings.minifyStyles}
                      onCheckedChange={(checked) => updateSettings('minifyStyles', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">优化颜色</Label>
                    <Switch
                      checked={settings.convertColors}
                      onCheckedChange={(checked) => updateSettings('convertColors', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs text-red-600">移除样式元素</Label>
                    <Switch
                      checked={settings.removeStyleElement}
                      onCheckedChange={(checked) => updateSettings('removeStyleElement', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs text-red-600">移除脚本元素</Label>
                    <Switch
                      checked={settings.removeScriptElement}
                      onCheckedChange={(checked) => updateSettings('removeScriptElement', checked)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* 数值精度 */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label className="text-xs">数值精度</Label>
                  <span className="text-xs text-gray-500">{settings.floatPrecision} 位小数</span>
                </div>
                <Slider
                  value={[settings.floatPrecision]}
                  onValueChange={(value) => updateSettings('floatPrecision', value[0])}
                  min={0}
                  max={5}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* 危险选项 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-red-600">危险选项</Label>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs text-red-600">移除title元素</Label>
                    <Switch
                      checked={settings.removeTitle}
                      onCheckedChange={(checked) => updateSettings('removeTitle', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs text-red-600">移除desc元素</Label>
                    <Switch
                      checked={settings.removeDesc}
                      onCheckedChange={(checked) => updateSettings('removeDesc', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-xs text-red-600">移除尺寸属性</Label>
                    <Switch
                      checked={settings.removeDimensions}
                      onCheckedChange={(checked) => updateSettings('removeDimensions', checked)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 文件管理和预览区域 */}
        <div className="lg:col-span-2 space-y-4">
          {/* 上传区域 */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Upload className="w-4 h-4" />
                  SVG文件上传
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    size="sm"
                    className="text-xs"
                  >
                    <Upload className="w-3 h-3 mr-1" />
                    选择文件
                  </Button>
                  {files.length > 0 && (
                    <Button
                      onClick={clearAllFiles}
                      variant="outline"
                      size="sm"
                      className="text-xs"
                    >
                      <Trash2 className="w-3 h-3 mr-1" />
                      清空
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <input
                ref={fileInputRef}
                type="file"
                accept=".svg,image/svg+xml"
                multiple
                onChange={(e) => handleFileSelect(e.target.files)}
                className="hidden"
              />
              
              <div 
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
                onDrop={(e) => {
                  e.preventDefault()
                  handleFileSelect(e.dataTransfer.files)
                }}
                onDragOver={(e) => e.preventDefault()}
              >
                <FileCode className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-600 mb-2">
                  拖拽SVG文件到此处或 <button 
                    onClick={() => fileInputRef.current?.click()}
                    className="text-blue-500 hover:underline"
                  >
                    点击选择
                  </button>
                </p>
                <p className="text-xs text-gray-500">
                  支持 .svg 格式，单个文件最大 10MB
                </p>
              </div>

              {files.length > 0 && (
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      已选择 {files.length} 个文件
                    </span>
                    <div className="flex gap-2">
                      <Button
                        onClick={processAllFiles}
                        disabled={isProcessing}
                        size="sm"
                        className="text-xs"
                      >
                        {isProcessing ? (
                          <>
                            <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                            优化中...
                          </>
                        ) : (
                          <>
                            <Zap className="w-3 h-3 mr-1" />
                            批量优化
                          </>
                        )}
                      </Button>
                      <Button
                        onClick={downloadAllFiles}
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        disabled={!files.some(f => f.status === 'completed')}
                      >
                        <Download className="w-3 h-3 mr-1" />
                        全部下载
                      </Button>
                    </div>
                  </div>
                  
                  {isProcessing && (
                    <div className="space-y-2">
                      <Progress value={processingProgress} className="w-full" />
                      <p className="text-sm text-gray-600 text-center">
                        优化进度: {Math.round(processingProgress)}%
                      </p>
                    </div>
                  )}

                  {/* 统计信息 */}
                  {(() => {
                    const stats = getOptimizationStats()
                    if (stats.totalFiles === 0) return null

                    return (
                      <Card className="bg-green-50 border-green-200">
                        <CardContent className="p-3">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                            <div className="text-center">
                              <div className="font-semibold text-green-700">{stats.totalFiles}</div>
                              <div className="text-green-600">已优化</div>
                            </div>
                            <div className="text-center">
                              <div className="font-semibold text-green-700">{formatFileSize(stats.totalSavings)}</div>
                              <div className="text-green-600">已节省</div>
                            </div>
                            <div className="text-center">
                              <div className="font-semibold text-green-700">{stats.averageCompression.toFixed(1)}%</div>
                              <div className="text-green-600">平均压缩</div>
                            </div>
                            <div className="text-center">
                              <div className="font-semibold text-green-700">{formatFileSize(stats.totalOptimizedSize)}</div>
                              <div className="text-green-600">总大小</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })()}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 文件列表 */}
          {files.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2 text-sm">
                    <FileText className="w-4 h-4" />
                    文件列表
                  </CardTitle>
                  <Button
                    onClick={() => setShowPreview(!showPreview)}
                    variant="outline"
                    size="sm"
                    className="text-xs"
                  >
                    {showPreview ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {files.map((file) => (
                    <div
                      key={file.id}
                      className={`p-3 border rounded-lg transition-all cursor-pointer ${
                        selectedFileId === file.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedFileId(file.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <FileCode className="w-4 h-4 text-blue-500" />
                            <span className="font-medium text-sm truncate">{file.name}</span>
                            {file.status === 'completed' && (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            )}
                            {file.status === 'error' && (
                              <AlertCircle className="w-4 h-4 text-red-500" />
                            )}
                            {file.status === 'processing' && (
                              <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
                            )}
                          </div>
                          
                          <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                            <span>原始: {formatFileSize(file.originalSize)}</span>
                            {file.optimizedSize && (
                              <>
                                <span>优化: {formatFileSize(file.optimizedSize)}</span>
                                <span className="text-green-600 font-medium">
                                  压缩 {file.compressionRatio?.toFixed(1)}%
                                </span>
                              </>
                            )}
                            <Badge variant={
                              file.status === 'completed' ? 'default' :
                              file.status === 'error' ? 'destructive' :
                              file.status === 'processing' ? 'secondary' : 'outline'
                            } className="text-xs">
                              {file.status === 'pending' && '待优化'}
                              {file.status === 'processing' && '优化中'}
                              {file.status === 'completed' && '已完成'}
                              {file.status === 'error' && '失败'}
                            </Badge>
                          </div>
                          
                          {file.status === 'error' && file.error && (
                            <div className="text-xs text-red-600 mt-1">
                              错误: {file.error}
                            </div>
                          )}
                        </div>

                        <div className="flex gap-1">
                          {file.status === 'completed' && (
                            <Button
                              onClick={(e) => {
                                e.stopPropagation()
                                downloadFile(file)
                              }}
                              size="sm"
                              variant="outline"
                              className="text-xs"
                            >
                              <Download className="w-3 h-3" />
                            </Button>
                          )}
                          <Button
                            onClick={(e) => {
                              e.stopPropagation()
                              removeFile(file.id)
                            }}
                            size="sm"
                            variant="outline"
                            className="text-xs text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 代码预览 */}
          {selectedFileId && showPreview && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Code className="w-4 h-4" />
                  代码预览
                </CardTitle>
              </CardHeader>
              <CardContent>
                {(() => {
                  const selectedFile = files.find(f => f.id === selectedFileId)
                  if (!selectedFile) return null

                  return (
                    <Tabs defaultValue="original" className="w-full">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="original" className="text-xs">原始代码</TabsTrigger>
                        <TabsTrigger value="optimized" className="text-xs" disabled={!selectedFile.optimizedContent}>
                          优化后代码
                        </TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="original" className="space-y-3">
                        <div className="flex justify-between items-center">
                          <Label className="text-sm font-medium">原始SVG代码</Label>
                          <Button 
                            onClick={() => copyToClipboard(selectedFile.originalContent, '原始代码')} 
                            size="sm" 
                            variant="outline"
                            className="text-xs"
                          >
                            <Copy className="w-3 h-3 mr-1" />
                            复制
                          </Button>
                        </div>
                        <Textarea
                          value={selectedFile.originalContent}
                          readOnly
                          className="font-mono text-xs h-80 resize-none"
                        />
                      </TabsContent>
                      
                      <TabsContent value="optimized" className="space-y-3">
                        <div className="flex justify-between items-center">
                          <Label className="text-sm font-medium">优化后SVG代码</Label>
                          {selectedFile.optimizedContent && (
                            <Button 
                              onClick={() => copyToClipboard(selectedFile.optimizedContent!, '优化后代码')} 
                              size="sm" 
                              variant="outline"
                              className="text-xs"
                            >
                              <Copy className="w-3 h-3 mr-1" />
                              复制
                            </Button>
                          )}
                        </div>
                        <Textarea
                          value={selectedFile.optimizedContent || '等待优化...'}
                          readOnly
                          className="font-mono text-xs h-80 resize-none"
                        />
                      </TabsContent>
                    </Tabs>
                  )
                })()}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}