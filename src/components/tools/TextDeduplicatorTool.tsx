'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Filter, Copy, Check, Upload, Download, Info, Trash2, Hash, Type, ToggleLeft } from 'lucide-react'

interface DeduplicationOptions {
  caseSensitive: boolean
  trimWhitespace: boolean
  removeEmpty: boolean
  preserveOrder: boolean
  compareMode: 'exact' | 'fuzzy'
  fuzzyThreshold: number
}

interface DeduplicationResult {
  original: string[]
  unique: string[]
  duplicates: Map<string, number>
  removedCount: number
  uniqueCount: number
}

export default function TextDeduplicatorTool() {
  const [inputText, setInputText] = useState('')
  const [outputText, setOutputText] = useState('')
  const [delimiter, setDelimiter] = useState('newline')
  const [customDelimiter, setCustomDelimiter] = useState(',')
  const [options, setOptions] = useState<DeduplicationOptions>({
    caseSensitive: false,
    trimWhitespace: true,
    removeEmpty: true,
    preserveOrder: true,
    compareMode: 'exact',
    fuzzyThreshold: 0.8,
  })
  const [result, setResult] = useState<DeduplicationResult | null>(null)
  const [copied, setCopied] = useState(false)
  const [showDuplicates, setShowDuplicates] = useState(false)
  
  // 计算字符串相似度（Levenshtein距离）
  const calculateSimilarity = (str1: string, str2: string): number => {
    const len1 = str1.length
    const len2 = str2.length
    const matrix: number[][] = []
    
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        )
      }
    }
    
    const distance = matrix[len1][len2]
    const maxLen = Math.max(len1, len2)
    return maxLen === 0 ? 1 : 1 - distance / maxLen
  }
  
  // 执行去重
  const performDeduplication = useCallback(() => {
    if (!inputText.trim()) {
      setOutputText('')
      setResult(null)
      return
    }
    
    // 根据分隔符分割文本
    let lines: string[]
    if (delimiter === 'newline') {
      lines = inputText.split('\n')
    } else if (delimiter === 'comma') {
      lines = inputText.split(',')
    } else if (delimiter === 'space') {
      lines = inputText.split(/\s+/)
    } else if (delimiter === 'tab') {
      lines = inputText.split('\t')
    } else {
      lines = inputText.split(customDelimiter)
    }
    
    // 预处理
    if (options.trimWhitespace) {
      lines = lines.map(line => line.trim())
    }
    
    if (options.removeEmpty) {
      lines = lines.filter(line => line.length > 0)
    }
    
    // 去重逻辑
    const seen = new Map<string, number>()
    const duplicates = new Map<string, number>()
    const unique: string[] = []
    
    if (options.compareMode === 'exact') {
      // 精确匹配
      lines.forEach(line => {
        const key = options.caseSensitive ? line : line.toLowerCase()
        const count = seen.get(key) || 0
        
        if (count === 0) {
          unique.push(line)
          seen.set(key, 1)
        } else {
          seen.set(key, count + 1)
          duplicates.set(line, (duplicates.get(line) || 1) + 1)
        }
      })
    } else {
      // 模糊匹配
      lines.forEach(line => {
        const lineKey = options.caseSensitive ? line : line.toLowerCase()
        let isDuplicate = false
        
        for (const [seenKey, _] of Array.from(seen)) {
          const similarity = calculateSimilarity(lineKey, seenKey)
          if (similarity >= options.fuzzyThreshold) {
            isDuplicate = true
            duplicates.set(line, (duplicates.get(line) || 1) + 1)
            break
          }
        }
        
        if (!isDuplicate) {
          unique.push(line)
          seen.set(lineKey, 1)
        }
      })
    }
    
    // 如果不保留顺序，可以排序
    if (!options.preserveOrder) {
      unique.sort()
    }
    
    // 重新组合文本
    let output: string
    if (delimiter === 'newline') {
      output = unique.join('\n')
    } else if (delimiter === 'comma') {
      output = unique.join(', ')
    } else if (delimiter === 'space') {
      output = unique.join(' ')
    } else if (delimiter === 'tab') {
      output = unique.join('\t')
    } else {
      output = unique.join(customDelimiter)
    }
    
    setOutputText(output)
    setResult({
      original: lines,
      unique,
      duplicates,
      removedCount: lines.length - unique.length,
      uniqueCount: unique.length,
    })
  }, [inputText, delimiter, customDelimiter, options])
  
  // 复制结果
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(outputText)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }
  
  // 上传文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputText(content)
    }
    reader.readAsText(file)
  }
  
  // 下载结果
  const downloadResult = () => {
    if (!outputText) return
    
    const blob = new Blob([outputText], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `deduplicated_text_${new Date().getTime()}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }
  
  // 下载报告
  const downloadReport = () => {
    if (!result) return
    
    let report = '文本去重报告\n'
    report += '=============\n\n'
    report += `原始项目数: ${result.original.length}\n`
    report += `唯一项目数: ${result.uniqueCount}\n`
    report += `移除重复数: ${result.removedCount}\n`
    report += `去重率: ${((result.removedCount / result.original.length) * 100).toFixed(2)}%\n\n`
    
    if (result.duplicates.size > 0) {
      report += '重复项目列表:\n'
      report += '--------------\n'
      result.duplicates.forEach((count, item) => {
        report += `"${item}" - 重复 ${count} 次\n`
      })
    }
    
    const blob = new Blob([report], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `deduplication_report_${new Date().getTime()}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }
  
  // 预设示例
  const examples = [
    {
      name: '邮箱列表',
      text: '<EMAIL>\<EMAIL>\<EMAIL>\<EMAIL>\<EMAIL>\<EMAIL>',
    },
    {
      name: '产品清单',
      text: '苹果,香蕉,橙子,苹果,葡萄,香蕉,西瓜,橙子,苹果',
      delimiter: 'comma',
    },
    {
      name: '标签去重',
      text: 'JavaScript React Vue Angular React JavaScript TypeScript Vue React',
      delimiter: 'space',
    },
    {
      name: '相似文本',
      text: 'Hello World\nhello world\nHELLO WORLD\nHello, World!\nHi World',
      compareMode: 'fuzzy' as const,
    },
  ]
  
  // 应用示例
  const applyExample = (example: typeof examples[0]) => {
    setInputText(example.text)
    if (example.delimiter) {
      setDelimiter(example.delimiter)
    }
    if (example.compareMode) {
      setOptions(prev => ({ ...prev, compareMode: example.compareMode! }))
    }
  }
  
  // 监听变化自动去重
  useEffect(() => {
    performDeduplication()
  }, [performDeduplication])
  
  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本去重工具</h2>
        <p className="text-gray-600">
          快速去除文本中的重复项，支持精确匹配和模糊匹配
        </p>
      </div>
      
      {/* 示例 */}
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-3">快速示例</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {examples.map((example) => (
            <button
              key={example.name}
              onClick={() => applyExample(example)}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              {example.name}
            </button>
          ))}
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">输入文本</label>
            <label className="text-sm text-blue-500 hover:text-blue-600 cursor-pointer">
              <input
                type="file"
                accept=".txt"
                onChange={handleFileUpload}
                className="hidden"
              />
              <span className="flex items-center gap-1">
                <Upload className="w-4 h-4" />
                上传文件
              </span>
            </label>
          </div>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="请输入要去重的文本，每行一个项目..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
          />
          <div className="mt-2 text-sm text-gray-500">
            项目数: {inputText.split('\n').filter(line => line.trim()).length}
          </div>
        </div>
        
        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">去重结果</label>
            <div className="flex gap-2">
              <button
                onClick={copyToClipboard}
                disabled={!outputText}
                className="text-sm text-blue-500 hover:text-blue-600 disabled:text-gray-400 flex items-center gap-1"
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    复制
                  </>
                )}
              </button>
              <button
                onClick={downloadResult}
                disabled={!outputText}
                className="text-sm text-blue-500 hover:text-blue-600 disabled:text-gray-400 flex items-center gap-1"
              >
                <Download className="w-4 h-4" />
                下载
              </button>
            </div>
          </div>
          <textarea
            value={outputText}
            readOnly
            placeholder="去重结果将显示在这里..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none bg-gray-50 font-mono text-sm"
          />
          <div className="mt-2 text-sm text-gray-500">
            唯一项目数: {result?.uniqueCount || 0}
          </div>
        </div>
      </div>
      
      {/* 统计信息 */}
      {result && (
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm text-blue-600">原始项目</div>
            <div className="text-2xl font-bold text-blue-900">{result.original.length}</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-sm text-green-600">唯一项目</div>
            <div className="text-2xl font-bold text-green-900">{result.uniqueCount}</div>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <div className="text-sm text-orange-600">移除重复</div>
            <div className="text-2xl font-bold text-orange-900">{result.removedCount}</div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-sm text-purple-600">去重率</div>
            <div className="text-2xl font-bold text-purple-900">
              {((result.removedCount / result.original.length) * 100).toFixed(1)}%
            </div>
          </div>
        </div>
      )}
      
      {/* 去重选项 */}
      <div className="mt-6 p-6 bg-white border border-gray-200 rounded-lg">
        <h3 className="font-semibold mb-4">去重选项</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 比较模式 */}
          <div>
            <label className="block text-sm font-medium mb-2">比较模式</label>
            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="compareMode"
                  value="exact"
                  checked={options.compareMode === 'exact'}
                  onChange={(e) => setOptions(prev => ({ ...prev, compareMode: 'exact' }))}
                  className="text-blue-500"
                />
                <Type className="w-4 h-4" />
                <span>精确匹配</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="compareMode"
                  value="fuzzy"
                  checked={options.compareMode === 'fuzzy'}
                  onChange={(e) => setOptions(prev => ({ ...prev, compareMode: 'fuzzy' }))}
                  className="text-blue-500"
                />
                <Filter className="w-4 h-4" />
                <span>模糊匹配</span>
              </label>
            </div>
            
            {options.compareMode === 'fuzzy' && (
              <div className="mt-3">
                <label className="text-sm text-gray-600">
                  相似度阈值: {(options.fuzzyThreshold * 100).toFixed(0)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={options.fuzzyThreshold * 100}
                  onChange={(e) => setOptions(prev => ({ 
                    ...prev, 
                    fuzzyThreshold: parseInt(e.target.value) / 100 
                  }))}
                  className="w-full mt-1"
                />
              </div>
            )}
          </div>
          
          {/* 其他选项 */}
          <div>
            <label className="block text-sm font-medium mb-2">处理选项</label>
            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={options.caseSensitive}
                  onChange={(e) => setOptions(prev => ({ ...prev, caseSensitive: e.target.checked }))}
                  className="text-blue-500"
                />
                <span>区分大小写</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={options.trimWhitespace}
                  onChange={(e) => setOptions(prev => ({ ...prev, trimWhitespace: e.target.checked }))}
                  className="text-blue-500"
                />
                <span>修剪空白字符</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={options.removeEmpty}
                  onChange={(e) => setOptions(prev => ({ ...prev, removeEmpty: e.target.checked }))}
                  className="text-blue-500"
                />
                <span>移除空行</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={options.preserveOrder}
                  onChange={(e) => setOptions(prev => ({ ...prev, preserveOrder: e.target.checked }))}
                  className="text-blue-500"
                />
                <span>保持原始顺序</span>
              </label>
            </div>
          </div>
        </div>
        
        {/* 分隔符选项 */}
        <div className="mt-6">
          <label className="block text-sm font-medium mb-2">分隔符</label>
          <div className="flex flex-wrap gap-3">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="newline"
                checked={delimiter === 'newline'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>换行符</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="comma"
                checked={delimiter === 'comma'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>逗号</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="space"
                checked={delimiter === 'space'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>空格</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="tab"
                checked={delimiter === 'tab'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>制表符</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="custom"
                checked={delimiter === 'custom'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>自定义:</span>
              <input
                type="text"
                value={customDelimiter}
                onChange={(e) => setCustomDelimiter(e.target.value)}
                disabled={delimiter !== 'custom'}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm disabled:bg-gray-100"
                placeholder="分隔符"
              />
            </label>
          </div>
        </div>
      </div>
      
      {/* 重复项列表 */}
      {result && result.duplicates.size > 0 && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold">重复项列表</h3>
            <div className="flex gap-2">
              <button
                onClick={() => setShowDuplicates(!showDuplicates)}
                className="text-sm text-blue-500 hover:text-blue-600"
              >
                {showDuplicates ? '隐藏' : '显示'}
              </button>
              <button
                onClick={downloadReport}
                className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
              >
                <Download className="w-4 h-4" />
                下载报告
              </button>
            </div>
          </div>
          
          {showDuplicates && (
            <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
              <div className="space-y-2">
                {Array.from(result.duplicates.entries()).map(([item, count], index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-white rounded border border-gray-200">
                    <span className="font-mono text-sm truncate flex-1">{item}</span>
                    <span className="text-sm text-gray-500 ml-2">重复 {count} 次</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持精确匹配和模糊匹配两种去重模式</li>
              <li>模糊匹配可以识别相似但不完全相同的文本</li>
              <li>可以自定义分隔符处理不同格式的文本</li>
              <li>提供详细的去重统计和重复项报告</li>
              <li>支持批量处理大量文本数据</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
