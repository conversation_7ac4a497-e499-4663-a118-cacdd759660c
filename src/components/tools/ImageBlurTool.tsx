'use client'

import React, { useState, useCallback, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  Download, 
  RotateCcw, 
  MousePointer, 
  Square, 
  Circle, 
  Edit3,
  Eye,
  EyeOff,
  Trash2,
  Undo,
  Redo,
  Settings,
  Save,
  Image as ImageIcon
} from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface BlurArea {
  id: string
  type: 'rectangle' | 'circle' | 'polygon'
  x: number
  y: number
  width: number
  height: number
  radius?: number
  points?: Array<{ x: number; y: number }>
  intensity: number
  visible: boolean
}

interface HistoryState {
  areas: BlurArea[]
  timestamp: number
}

export default function ImageBlurTool() {
  // 状态管理
  const [originalImage, setOriginalImage] = useState<string | null>(null)
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [blurAreas, setBlurAreas] = useState<BlurArea[]>([])
  const [selectedTool, setSelectedTool] = useState<'select' | 'rectangle' | 'circle' | 'polygon'>('select')
  const [selectedArea, setSelectedArea] = useState<string | null>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [currentDrawing, setCurrentDrawing] = useState<Partial<BlurArea> | null>(null)
  const [defaultIntensity, setDefaultIntensity] = useState(10)
  const [history, setHistory] = useState<HistoryState[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [isProcessing, setIsProcessing] = useState(false)
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)

  /**
   * 添加历史记录
   */
  const addToHistory = useCallback((areas: BlurArea[]) => {
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push({
      areas: JSON.parse(JSON.stringify(areas)),
      timestamp: Date.now()
    })
    setHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
  }, [history, historyIndex])

  /**
   * 处理文件上传
   */
  const handleFileUpload = useCallback((file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('请选择有效的图片文件')
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      setOriginalImage(result)
      setPreviewImage(result)
      setBlurAreas([])
      setHistory([])
      setHistoryIndex(-1)
      setSelectedArea(null)
    }
    reader.readAsDataURL(file)
  }, [])

  /**
   * 获取画布坐标
   */
  const getCanvasCoordinates = useCallback((event: React.MouseEvent) => {
    const canvas = overlayCanvasRef.current
    if (!canvas) return { x: 0, y: 0 }

    const rect = canvas.getBoundingClientRect()
    const scaleX = canvas.width / rect.width
    const scaleY = canvas.height / rect.height

    return {
      x: (event.clientX - rect.left) * scaleX,
      y: (event.clientY - rect.top) * scaleY
    }
  }, [])

  /**
   * 开始绘制
   */
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (selectedTool === 'select') return

    const { x, y } = getCanvasCoordinates(event)
    setIsDrawing(true)
    
    const newArea: Partial<BlurArea> = {
      id: `area-${Date.now()}`,
      type: selectedTool,
      x,
      y,
      width: 0,
      height: 0,
      intensity: defaultIntensity,
      visible: true
    }

    if (selectedTool === 'circle') {
      newArea.radius = 0
    } else if (selectedTool === 'polygon') {
      newArea.points = [{ x, y }]
    }

    setCurrentDrawing(newArea)
  }, [selectedTool, defaultIntensity, getCanvasCoordinates])

  /**
   * 绘制过程中
   */
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!isDrawing || !currentDrawing) return

    const { x, y } = getCanvasCoordinates(event)

    if (currentDrawing.type === 'rectangle') {
      setCurrentDrawing(prev => ({
        ...prev,
        width: x - (prev?.x || 0),
        height: y - (prev?.y || 0)
      }))
    } else if (currentDrawing.type === 'circle') {
      const centerX = currentDrawing.x || 0
      const centerY = currentDrawing.y || 0
      const radius = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2))
      
      setCurrentDrawing(prev => ({
        ...prev,
        radius,
        width: radius * 2,
        height: radius * 2,
        x: centerX - radius,
        y: centerY - radius
      }))
    }
  }, [isDrawing, currentDrawing, getCanvasCoordinates])

  /**
   * 结束绘制
   */
  const handleMouseUp = useCallback(() => {
    if (!isDrawing || !currentDrawing) return

    // 检查面积是否足够大
    const minSize = 10
    if (currentDrawing.type === 'rectangle' && 
        (Math.abs(currentDrawing.width || 0) < minSize || Math.abs(currentDrawing.height || 0) < minSize)) {
      setIsDrawing(false)
      setCurrentDrawing(null)
      return
    }

    if (currentDrawing.type === 'circle' && (currentDrawing.radius || 0) < minSize) {
      setIsDrawing(false)
      setCurrentDrawing(null)
      return
    }

    const newAreas = [...blurAreas, currentDrawing as BlurArea]
    setBlurAreas(newAreas)
    addToHistory(newAreas)
    setIsDrawing(false)
    setCurrentDrawing(null)
    setSelectedArea(currentDrawing.id || null)
  }, [isDrawing, currentDrawing, blurAreas, addToHistory])

  /**
   * 处理多边形绘制
   */
  const handlePolygonClick = useCallback((event: React.MouseEvent) => {
    if (selectedTool !== 'polygon') return

    const { x, y } = getCanvasCoordinates(event)

    if (!currentDrawing) {
      // 开始新的多边形
      setCurrentDrawing({
        id: `area-${Date.now()}`,
        type: 'polygon',
        x,
        y,
        width: 0,
        height: 0,
        points: [{ x, y }],
        intensity: defaultIntensity,
        visible: true
      })
      setIsDrawing(true)
    } else {
      // 添加点到现有多边形
      const points = [...(currentDrawing.points || []), { x, y }]
      setCurrentDrawing(prev => ({
        ...prev,
        points
      }))
    }
  }, [selectedTool, currentDrawing, getCanvasCoordinates, defaultIntensity])

  /**
   * 完成多边形绘制
   */
  const finishPolygon = useCallback(() => {
    if (!currentDrawing || currentDrawing.type !== 'polygon' || !currentDrawing.points || currentDrawing.points.length < 3) {
      return
    }

    const newAreas = [...blurAreas, currentDrawing as BlurArea]
    setBlurAreas(newAreas)
    addToHistory(newAreas)
    setIsDrawing(false)
    setCurrentDrawing(null)
    setSelectedArea(currentDrawing.id || null)
  }, [currentDrawing, blurAreas, addToHistory])

  /**
   * 删除模糊区域
   */
  const deleteArea = useCallback((areaId: string) => {
    const newAreas = blurAreas.filter(area => area.id !== areaId)
    setBlurAreas(newAreas)
    addToHistory(newAreas)
    if (selectedArea === areaId) {
      setSelectedArea(null)
    }
  }, [blurAreas, selectedArea, addToHistory])

  /**
   * 更新模糊区域属性
   */
  const updateArea = useCallback((areaId: string, updates: Partial<BlurArea>) => {
    const newAreas = blurAreas.map(area => 
      area.id === areaId ? { ...area, ...updates } : area
    )
    setBlurAreas(newAreas)
  }, [blurAreas])

  /**
   * 切换区域可见性
   */
  const toggleAreaVisibility = useCallback((areaId: string) => {
    updateArea(areaId, { visible: !blurAreas.find(a => a.id === areaId)?.visible })
  }, [blurAreas, updateArea])

  /**
   * 撤销/重做
   */
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setBlurAreas(history[historyIndex - 1].areas)
    }
  }, [history, historyIndex])

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setBlurAreas(history[historyIndex + 1].areas)
    }
  }, [history, historyIndex])

  /**
   * 应用模糊效果
   */
  const applyBlur = useCallback(async () => {
    if (!originalImage || !canvasRef.current) return

    setIsProcessing(true)

    try {
      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')
      if (!ctx || !imageRef.current) return

      // 绘制原始图像
      canvas.width = imageRef.current.naturalWidth
      canvas.height = imageRef.current.naturalHeight
      ctx.drawImage(imageRef.current, 0, 0)

      // 应用模糊效果到每个区域
      for (const area of blurAreas) {
        if (!area.visible) continue

        ctx.save()
        
        // 创建裁剪路径
        ctx.beginPath()
        if (area.type === 'rectangle') {
          ctx.rect(area.x, area.y, area.width, area.height)
        } else if (area.type === 'circle') {
          ctx.arc(area.x + (area.radius || 0), area.y + (area.radius || 0), area.radius || 0, 0, 2 * Math.PI)
        } else if (area.type === 'polygon' && area.points) {
          ctx.moveTo(area.points[0].x, area.points[0].y)
          for (let i = 1; i < area.points.length; i++) {
            ctx.lineTo(area.points[i].x, area.points[i].y)
          }
          ctx.closePath()
        }
        ctx.clip()

        // 应用模糊滤镜
        ctx.filter = `blur(${area.intensity}px)`
        ctx.drawImage(imageRef.current, 0, 0)
        
        ctx.restore()
      }

      // 更新预览
      setPreviewImage(canvas.toDataURL('image/png'))
    } catch (error) {
      console.error('应用模糊效果失败:', error)
      alert('应用模糊效果失败，请重试')
    } finally {
      setIsProcessing(false)
    }
  }, [originalImage, blurAreas])

  /**
   * 下载处理后的图片
   */
  const downloadImage = useCallback(() => {
    if (!previewImage) return

    const link = document.createElement('a')
    link.download = `blurred-image-${Date.now()}.png`
    link.href = previewImage
    link.click()
  }, [previewImage])

  /**
   * 清除所有模糊区域
   */
  const clearAllAreas = useCallback(() => {
    if (blurAreas.length === 0) return
    
    if (confirm('确定要清除所有模糊区域吗？')) {
      setBlurAreas([])
      addToHistory([])
      setSelectedArea(null)
    }
  }, [blurAreas, addToHistory])

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-6">
      {/* 工具栏 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            图片模糊处理工具
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2 mb-4">
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              上传图片
            </Button>
            
            <Button
              onClick={undo}
              disabled={historyIndex <= 0}
              variant="outline"
              size="sm"
            >
              <Undo className="h-4 w-4" />
            </Button>
            
            <Button
              onClick={redo}
              disabled={historyIndex >= history.length - 1}
              variant="outline"
              size="sm"
            >
              <Redo className="h-4 w-4" />
            </Button>
            
            <Button
              onClick={clearAllAreas}
              disabled={blurAreas.length === 0}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4" />
              清除全部
            </Button>
            
            <Button
              onClick={applyBlur}
              disabled={!originalImage || blurAreas.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Settings className="h-4 w-4 mr-2" />
              {isProcessing ? '处理中...' : '应用模糊'}
            </Button>
            
            <Button
              onClick={downloadImage}
              disabled={!previewImage}
              className="bg-green-600 hover:bg-green-700"
            >
              <Download className="h-4 w-4 mr-2" />
              下载图片
            </Button>
          </div>

          {/* 工具选择 */}
          <div className="flex gap-2 mb-4">
            <Label>绘制工具：</Label>
            <div className="flex gap-1">
              <Button
                variant={selectedTool === 'select' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedTool('select')}
              >
                <MousePointer className="h-4 w-4" />
              </Button>
              <Button
                variant={selectedTool === 'rectangle' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedTool('rectangle')}
              >
                <Square className="h-4 w-4" />
              </Button>
              <Button
                variant={selectedTool === 'circle' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedTool('circle')}
              >
                <Circle className="h-4 w-4" />
              </Button>
              <Button
                variant={selectedTool === 'polygon' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedTool('polygon')}
              >
                <Edit3 className="h-4 w-4" />
              </Button>
            </div>
            
            {selectedTool === 'polygon' && isDrawing && (
              <Button
                onClick={finishPolygon}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                完成多边形
              </Button>
            )}
          </div>

          {/* 默认模糊强度 */}
          <div className="flex items-center gap-4 mb-4">
            <Label>默认模糊强度：</Label>
            <div className="flex items-center gap-2 flex-1 max-w-xs">
              <Slider
                value={[defaultIntensity]}
                onValueChange={(value) => setDefaultIntensity(value[0])}
                min={1}
                max={50}
                step={1}
                className="flex-1"
              />
              <span className="w-12 text-center">{defaultIntensity}px</span>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
            className="hidden"
          />
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 图片编辑区域 */}
        <div className="lg:col-span-3 space-y-4">
          {originalImage ? (
            <Card>
              <CardHeader>
                <CardTitle>图片编辑区域</CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  className="relative border-2 border-dashed border-gray-300 rounded-lg overflow-hidden"
                  style={{ maxHeight: '600px' }}
                >
                  <img
                    ref={imageRef}
                    src={originalImage}
                    alt="原图"
                    className="w-full h-auto"
                    onLoad={() => {
                      if (overlayCanvasRef.current && imageRef.current) {
                        const canvas = overlayCanvasRef.current
                        canvas.width = imageRef.current.offsetWidth
                        canvas.height = imageRef.current.offsetHeight
                      }
                    }}
                  />
                  
                  <canvas
                    ref={overlayCanvasRef}
                    className="absolute top-0 left-0 w-full h-full cursor-crosshair"
                    onMouseDown={selectedTool !== 'polygon' ? handleMouseDown : undefined}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onClick={selectedTool === 'polygon' ? handlePolygonClick : undefined}
                  />
                  
                  <canvas
                    ref={canvasRef}
                    className="hidden"
                  />
                </div>
                
                {selectedTool !== 'select' && (
                  <Alert className="mt-4">
                    <AlertDescription>
                      {selectedTool === 'rectangle' && '点击并拖拽绘制矩形模糊区域'}
                      {selectedTool === 'circle' && '点击并拖拽绘制圆形模糊区域'}
                      {selectedTool === 'polygon' && '点击多个点绘制多边形，点击"完成多边形"结束'}
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-96 border-2 border-dashed border-gray-300 rounded-lg">
                <div className="text-center">
                  <ImageIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">请上传图片开始编辑</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 预览区域 */}
          {previewImage && previewImage !== originalImage && (
            <Card>
              <CardHeader>
                <CardTitle>处理结果预览</CardTitle>
              </CardHeader>
              <CardContent>
                <img
                  src={previewImage}
                  alt="处理结果"
                  className="w-full h-auto rounded-lg"
                />
              </CardContent>
            </Card>
          )}
        </div>

        {/* 侧边栏 */}
        <div className="space-y-4">
          {/* 模糊区域列表 */}
          <Card>
            <CardHeader>
              <CardTitle>模糊区域列表</CardTitle>
            </CardHeader>
            <CardContent>
              {blurAreas.length === 0 ? (
                <p className="text-gray-500 text-center py-4">暂无模糊区域</p>
              ) : (
                <div className="space-y-2">
                  {blurAreas.map((area) => (
                    <div
                      key={area.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedArea === area.id 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedArea(selectedArea === area.id ? null : area.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {area.type === 'rectangle' && '矩形'}
                            {area.type === 'circle' && '圆形'}
                            {area.type === 'polygon' && '多边形'}
                          </Badge>
                          <Button
                            onClick={(e) => {
                              e.stopPropagation()
                              toggleAreaVisibility(area.id)
                            }}
                            variant="ghost"
                            size="sm"
                          >
                            {area.visible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                          </Button>
                        </div>
                        <Button
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteArea(area.id)
                          }}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label className="text-xs">强度:</Label>
                          <span className="text-xs">{area.intensity}px</span>
                        </div>
                        <Slider
                          value={[area.intensity]}
                          onValueChange={(value) => updateArea(area.id, { intensity: value[0] })}
                          min={1}
                          max={50}
                          step={1}
                          className="w-full"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle>使用说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm text-gray-600">
                <p>1. 上传需要处理的图片</p>
                <p>2. 选择绘制工具：矩形、圆形或多边形</p>
                <p>3. 在图片上绘制模糊区域</p>
                <p>4. 调整每个区域的模糊强度</p>
                <p>5. 点击&ldquo;应用模糊&rdquo;生成结果</p>
                <p>6. 下载处理后的图片</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
