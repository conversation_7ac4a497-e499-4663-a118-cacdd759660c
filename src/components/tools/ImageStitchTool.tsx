'use client'

import React, { useState, useRef } from 'react'
import NextImage from 'next/image'
import { Upload, Download, RefreshCw, Image as ImageIcon, Grid, Columns, Rows, Settings, X, Move } from 'lucide-react'

interface ImageItem {
  id: string
  url: string
  width: number
  height: number
  file: File
}

interface StitchSettings {
  direction: 'horizontal' | 'vertical' | 'grid'
  gap: number
  backgroundColor: string
  gridColumns: number
  alignment: 'start' | 'center' | 'end'
}

export default function ImageStitchTool() {
  const [images, setImages] = useState<ImageItem[]>([])
  const [stitchedImage, setStitchedImage] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  
  const [settings, setSettings] = useState<StitchSettings>({
    direction: 'horizontal',
    gap: 0,
    backgroundColor: '#ffffff',
    gridColumns: 2,
    alignment: 'center'
  })
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理图片上传
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    const newImages: ImageItem[] = []
    let loadedCount = 0

    files.forEach((file) => {
      const reader = new FileReader()
      reader.onload = (event) => {
        const img = new Image()
        img.onload = () => {
          newImages.push({
            id: `${Date.now()}-${Math.random()}`,
            url: event.target?.result as string,
            width: img.width,
            height: img.height,
            file
          })
          
          loadedCount++
          if (loadedCount === files.length) {
            setImages(prev => [...prev, ...newImages])
            setStitchedImage(null)
          }
        }
        img.src = event.target?.result as string
      }
      reader.readAsDataURL(file)
    })
  }

  // 删除图片
  const removeImage = (id: string) => {
    setImages(prev => prev.filter(img => img.id !== id))
    setStitchedImage(null)
  }

  // 拖拽开始
  const handleDragStart = (index: number) => {
    setDraggedIndex(index)
  }

  // 拖拽结束
  const handleDragEnd = () => {
    setDraggedIndex(null)
  }

  // 拖拽放置
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  // 拖拽放下
  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()
    if (draggedIndex === null || draggedIndex === dropIndex) return

    const newImages = [...images]
    const draggedImage = newImages[draggedIndex]
    newImages.splice(draggedIndex, 1)
    newImages.splice(dropIndex, 0, draggedImage)
    
    setImages(newImages)
    setDraggedIndex(null)
    setStitchedImage(null)
  }

  // 计算拼接后的尺寸
  const calculateStitchedSize = () => {
    if (images.length === 0) return { width: 0, height: 0 }

    const { direction, gap, gridColumns } = settings

    if (direction === 'horizontal') {
      const totalWidth = images.reduce((sum, img) => sum + img.width, 0) + gap * (images.length - 1)
      const maxHeight = Math.max(...images.map(img => img.height))
      return { width: totalWidth, height: maxHeight }
    } else if (direction === 'vertical') {
      const maxWidth = Math.max(...images.map(img => img.width))
      const totalHeight = images.reduce((sum, img) => sum + img.height, 0) + gap * (images.length - 1)
      return { width: maxWidth, height: totalHeight }
    } else {
      // Grid layout
      const rows = Math.ceil(images.length / gridColumns)
      const maxWidthPerColumn = Array(gridColumns).fill(0)
      const maxHeightPerRow = Array(rows).fill(0)
      
      images.forEach((img, index) => {
        const col = index % gridColumns
        const row = Math.floor(index / gridColumns)
        maxWidthPerColumn[col] = Math.max(maxWidthPerColumn[col], img.width)
        maxHeightPerRow[row] = Math.max(maxHeightPerRow[row], img.height)
      })
      
      const totalWidth = maxWidthPerColumn.reduce((sum, w) => sum + w, 0) + gap * (gridColumns - 1)
      const totalHeight = maxHeightPerRow.reduce((sum, h) => sum + h, 0) + gap * (rows - 1)
      
      return { width: totalWidth, height: totalHeight }
    }
  }

  // 拼接图片
  const stitchImages = () => {
    if (images.length === 0 || !canvasRef.current) return

    setIsProcessing(true)
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const { width, height } = calculateStitchedSize()
    canvas.width = width
    canvas.height = height

    // 填充背景色
    ctx.fillStyle = settings.backgroundColor
    ctx.fillRect(0, 0, width, height)

    const { direction, gap, gridColumns, alignment } = settings
    let currentX = 0
    let currentY = 0

    const loadAndDrawImage = (index: number): Promise<void> => {
      return new Promise((resolve) => {
        if (index >= images.length) {
          resolve()
          return
        }

        const imgData = images[index]
        const img = new Image()
        
        img.onload = () => {
          if (direction === 'horizontal') {
            // 水平拼接
            let y = 0
            if (alignment === 'center') {
              y = (height - imgData.height) / 2
            } else if (alignment === 'end') {
              y = height - imgData.height
            }
            
            ctx.drawImage(img, currentX, y, imgData.width, imgData.height)
            currentX += imgData.width + gap
          } else if (direction === 'vertical') {
            // 垂直拼接
            let x = 0
            if (alignment === 'center') {
              x = (width - imgData.width) / 2
            } else if (alignment === 'end') {
              x = width - imgData.width
            }
            
            ctx.drawImage(img, x, currentY, imgData.width, imgData.height)
            currentY += imgData.height + gap
          } else {
            // 网格拼接
            const col = index % gridColumns
            const row = Math.floor(index / gridColumns)
            
            // 计算每列的最大宽度和每行的最大高度
            const maxWidthPerColumn = Array(gridColumns).fill(0)
            const maxHeightPerRow = Array(Math.ceil(images.length / gridColumns)).fill(0)
            
            images.forEach((img, i) => {
              const c = i % gridColumns
              const r = Math.floor(i / gridColumns)
              maxWidthPerColumn[c] = Math.max(maxWidthPerColumn[c], img.width)
              maxHeightPerRow[r] = Math.max(maxHeightPerRow[r], img.height)
            })
            
            // 计算位置
            let x = 0
            for (let i = 0; i < col; i++) {
              x += maxWidthPerColumn[i] + gap
            }
            
            let y = 0
            for (let i = 0; i < row; i++) {
              y += maxHeightPerRow[i] + gap
            }
            
            // 根据对齐方式调整位置
            if (alignment === 'center') {
              x += (maxWidthPerColumn[col] - imgData.width) / 2
              y += (maxHeightPerRow[row] - imgData.height) / 2
            } else if (alignment === 'end') {
              x += maxWidthPerColumn[col] - imgData.width
              y += maxHeightPerRow[row] - imgData.height
            }
            
            ctx.drawImage(img, x, y, imgData.width, imgData.height)
          }
          
          // 继续加载下一张图片
          loadAndDrawImage(index + 1).then(resolve)
        }
        
        img.src = imgData.url
      })
    }

    // 开始加载和绘制图片
    loadAndDrawImage(0).then(() => {
      // 导出结果
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob)
          setStitchedImage(url)
        }
        setIsProcessing(false)
      }, 'image/png')
    })
  }

  // 下载图片
  const downloadImage = () => {
    if (!stitchedImage) return
    
    const link = document.createElement('a')
    link.href = stitchedImage
    link.download = `stitched_${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 重置
  const reset = () => {
    setImages([])
    setStitchedImage(null)
    setSettings({
      direction: 'horizontal',
      gap: 0,
      backgroundColor: '#ffffff',
      gridColumns: 2,
      alignment: 'center'
    })
    if (fileInputRef.current) fileInputRef.current.value = ''
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片拼接工具</h2>
        <p className="text-gray-600">
          将多张图片拼接成一张，支持水平、垂直和网格布局
        </p>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* 控制面板 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 拼接设置 */}
          <div className="bg-white rounded-lg border border-gray-300 p-4">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Settings className="w-4 h-4" />
              拼接设置
            </h3>
            
            {/* 拼接方向 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">拼接方向</label>
              <div className="grid grid-cols-3 gap-2">
                <button
                  onClick={() => setSettings(prev => ({ ...prev, direction: 'horizontal' }))}
                  className={`flex flex-col items-center gap-1 p-2 rounded-md border ${
                    settings.direction === 'horizontal'
                      ? 'border-blue-500 bg-blue-50 text-blue-600'
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <Columns className="w-5 h-5" />
                  <span className="text-xs">水平</span>
                </button>
                <button
                  onClick={() => setSettings(prev => ({ ...prev, direction: 'vertical' }))}
                  className={`flex flex-col items-center gap-1 p-2 rounded-md border ${
                    settings.direction === 'vertical'
                      ? 'border-blue-500 bg-blue-50 text-blue-600'
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <Rows className="w-5 h-5" />
                  <span className="text-xs">垂直</span>
                </button>
                <button
                  onClick={() => setSettings(prev => ({ ...prev, direction: 'grid' }))}
                  className={`flex flex-col items-center gap-1 p-2 rounded-md border ${
                    settings.direction === 'grid'
                      ? 'border-blue-500 bg-blue-50 text-blue-600'
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <Grid className="w-5 h-5" />
                  <span className="text-xs">网格</span>
                </button>
              </div>
            </div>

            {/* 网格列数 */}
            {settings.direction === 'grid' && (
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  网格列数: {settings.gridColumns}
                </label>
                <input
                  type="range"
                  min="2"
                  max="6"
                  value={settings.gridColumns}
                  onChange={(e) => setSettings(prev => ({ ...prev, gridColumns: Number(e.target.value) }))}
                  className="w-full"
                />
              </div>
            )}

            {/* 对齐方式 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">对齐方式</label>
              <select
                value={settings.alignment}
                onChange={(e) => setSettings(prev => ({ ...prev, alignment: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="start">起始对齐</option>
                <option value="center">居中对齐</option>
                <option value="end">末尾对齐</option>
              </select>
            </div>

            {/* 间距 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">
                间距: {settings.gap}px
              </label>
              <input
                type="range"
                min="0"
                max="50"
                value={settings.gap}
                onChange={(e) => setSettings(prev => ({ ...prev, gap: Number(e.target.value) }))}
                className="w-full"
              />
            </div>

            {/* 背景色 */}
            <div>
              <label className="block text-sm font-medium mb-2">背景色</label>
              <div className="flex gap-2">
                <input
                  type="color"
                  value={settings.backgroundColor}
                  onChange={(e) => setSettings(prev => ({ ...prev, backgroundColor: e.target.value }))}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={settings.backgroundColor}
                  onChange={(e) => setSettings(prev => ({ ...prev, backgroundColor: e.target.value }))}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-2">
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleImageUpload}
              accept="image/*"
              multiple
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            >
              <Upload className="w-4 h-4 inline mr-2" />
              添加图片
            </button>
            <button
              onClick={stitchImages}
              disabled={images.length < 2 || isProcessing}
              className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              拼接图片
            </button>
            {stitchedImage && (
              <button
                onClick={downloadImage}
                className="w-full px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
              >
                <Download className="w-4 h-4 inline mr-2" />
                下载结果
              </button>
            )}
            <button
              onClick={reset}
              className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
            >
              <RefreshCw className="w-4 h-4 inline mr-2" />
              重新开始
            </button>
          </div>

          {/* 提示信息 */}
          {images.length > 0 && (
            <div className="bg-blue-50 rounded-lg p-4">
              <p className="text-sm text-blue-900">
                已选择 {images.length} 张图片
              </p>
              <p className="text-xs text-blue-700 mt-1">
                拖拽图片可调整顺序
              </p>
            </div>
          )}
        </div>

        {/* 预览区域 */}
        <div className="lg:col-span-2">
          {images.length === 0 ? (
            <div className="bg-white rounded-lg border-2 border-dashed border-gray-300 p-12">
              <div className="text-center">
                <ImageIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">上传至少2张图片开始拼接</p>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  <Upload className="w-4 h-4 inline mr-2" />
                  选择图片
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 图片列表 */}
              <div className="bg-white rounded-lg border border-gray-300 p-4">
                <h3 className="font-medium mb-4">图片列表（拖拽调整顺序）</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {images.map((image, index) => (
                    <div
                      key={image.id}
                      draggable
                      onDragStart={() => handleDragStart(index)}
                      onDragEnd={handleDragEnd}
                      onDragOver={handleDragOver}
                      onDrop={(e) => handleDrop(e, index)}
                      className={`relative group cursor-move ${
                        draggedIndex === index ? 'opacity-50' : ''
                      }`}
                    >
                      <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                        <NextImage
                          src={image.url}
                          alt={`Image ${index + 1}`}
                          width={200}
                          height={200}
                          className="w-full h-full object-cover"
                          unoptimized
                        />
                      </div>
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity rounded-lg flex items-center justify-center">
                        <Move className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                      <button
                        onClick={() => removeImage(image.id)}
                        className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-4 h-4" />
                      </button>
                      <div className="absolute bottom-1 left-1 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                        {index + 1}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 拼接结果 */}
              {stitchedImage && (
                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <h3 className="font-medium mb-4">拼接结果</h3>
                  <div className="bg-gray-100 rounded-lg p-4 overflow-auto">
                    <NextImage
                      src={stitchedImage}
                      alt="Stitched result"
                      width={800}
                      height={600}
                      className="max-w-full object-contain"
                      unoptimized
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Canvas 用于拼接图片 */}
      <canvas ref={canvasRef} className="hidden" />

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Grid className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持一次上传多张图片，或分多次添加</li>
              <li>拖拽图片可以调整拼接顺序</li>
              <li>支持水平、垂直和网格三种拼接方式</li>
              <li>可以设置图片间距和背景颜色</li>
              <li>网格模式下可以自定义列数</li>
              <li>支持多种对齐方式，让拼接更美观</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
