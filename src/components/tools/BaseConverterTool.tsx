'use client'

import { useState } from 'react'
import { Bin<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>tor, Co<PERSON>, Check, AlertCircle, Info } from 'lucide-react'

interface ConversionResult {
  binary: string
  octal: string
  decimal: string
  hexadecimal: string
}

export default function BaseConverterTool() {
  const [inputValue, setInputValue] = useState('')
  const [inputBase, setInputBase] = useState<2 | 8 | 10 | 16>(10)
  const [result, setResult] = useState<ConversionResult | null>(null)
  const [error, setError] = useState('')
  const [copiedField, setCopiedField] = useState<string | null>(null)

  // 验证输入是否合法
  const validateInput = (value: string, base: number): boolean => {
    if (!value.trim()) return false
    
    const validChars: { [key: number]: RegExp } = {
      2: /^[01]+$/,
      8: /^[0-7]+$/,
      10: /^[0-9]+$/,
      16: /^[0-9A-Fa-f]+$/
    }
    
    return validChars[base].test(value.replace(/\s/g, ''))
  }

  // 转换进制
  const convertBase = () => {
    const cleanValue = inputValue.replace(/\s/g, '')
    
    if (!validateInput(cleanValue, inputBase)) {
      setError(`请输入有效的${getBaseName(inputBase)}数`)
      setResult(null)
      return
    }
    
    setError('')
    
    try {
      // 先转换为十进制
      const decimalValue = parseInt(cleanValue, inputBase)
      
      // 检查数值是否过大
      if (!isFinite(decimalValue) || decimalValue > Number.MAX_SAFE_INTEGER) {
        setError('数值过大，无法精确转换')
        setResult(null)
        return
      }
      
      // 转换为各种进制
      setResult({
        binary: formatNumber(decimalValue.toString(2), 4),
        octal: formatNumber(decimalValue.toString(8), 3),
        decimal: formatNumber(decimalValue.toString(10), 3),
        hexadecimal: decimalValue.toString(16).toUpperCase()
      })
    } catch (err) {
      setError('转换失败，请检查输入')
      setResult(null)
    }
  }

  // 格式化数字（添加分隔符）
  const formatNumber = (num: string, groupSize: number): string => {
    const reversed = num.split('').reverse()
    const groups = []
    
    for (let i = 0; i < reversed.length; i += groupSize) {
      groups.push(reversed.slice(i, i + groupSize).reverse().join(''))
    }
    
    return groups.reverse().join(' ')
  }

  // 获取进制名称
  const getBaseName = (base: number): string => {
    const names: { [key: number]: string } = {
      2: '二进制',
      8: '八进制',
      10: '十进制',
      16: '十六进制'
    }
    return names[base] || ''
  }

  // 复制到剪贴板
  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text.replace(/\s/g, ''))
      setCopiedField(field)
      setTimeout(() => setCopiedField(null), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 清空输入
  const clearInput = () => {
    setInputValue('')
    setResult(null)
    setError('')
  }

  // 加载示例
  const loadExample = () => {
    setInputBase(10)
    setInputValue('255')
    setError('')
  }

  // 快速输入按钮（针对二进制和十六进制）
  const QuickInputButtons = () => {
    if (inputBase === 2) {
      return (
        <div className="flex gap-2">
          <button
            onClick={() => setInputValue(inputValue + '0')}
            className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm font-mono"
          >
            0
          </button>
          <button
            onClick={() => setInputValue(inputValue + '1')}
            className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm font-mono"
          >
            1
          </button>
        </div>
      )
    }
    
    if (inputBase === 16) {
      const hexChars = ['A', 'B', 'C', 'D', 'E', 'F']
      return (
        <div className="flex gap-2 flex-wrap">
          {hexChars.map(char => (
            <button
              key={char}
              onClick={() => setInputValue(inputValue + char)}
              className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm font-mono"
            >
              {char}
            </button>
          ))}
        </div>
      )
    }
    
    return null
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">进制转换器</h2>
        <p className="text-gray-600">
          在二进制、八进制、十进制和十六进制之间自由转换
        </p>
      </div>

      {/* 输入区域 */}
      <div className="mb-6 bg-white rounded-lg border border-gray-200 p-6">
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">选择输入进制</label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <button
              onClick={() => setInputBase(2)}
              className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md transition-colors ${
                inputBase === 2
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Binary className="w-4 h-4" />
              二进制
            </button>
            <button
              onClick={() => setInputBase(8)}
              className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md transition-colors ${
                inputBase === 8
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Hash className="w-4 h-4" />
              八进制
            </button>
            <button
              onClick={() => setInputBase(10)}
              className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md transition-colors ${
                inputBase === 10
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Calculator className="w-4 h-4" />
              十进制
            </button>
            <button
              onClick={() => setInputBase(16)}
              className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md transition-colors ${
                inputBase === 16
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Hash className="w-4 h-4" />
              十六进制
            </button>
          </div>
        </div>

        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">
              输入{getBaseName(inputBase)}数
            </label>
            <div className="flex gap-2">
              <button
                onClick={loadExample}
                className="text-sm text-blue-500 hover:text-blue-600"
              >
                加载示例
              </button>
              <button
                onClick={clearInput}
                className="text-sm text-gray-500 hover:text-gray-600"
              >
                清空
              </button>
            </div>
          </div>
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value.toUpperCase())}
            placeholder={`请输入${getBaseName(inputBase)}数...`}
            className="w-full px-4 py-3 text-lg font-mono border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div className="mt-2">
            <QuickInputButtons />
          </div>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-800">
            <AlertCircle className="w-4 h-4 flex-shrink-0" />
            {error}
          </div>
        )}

        <button
          onClick={convertBase}
          disabled={!inputValue.trim()}
          className="w-full py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          转换
        </button>
      </div>

      {/* 结果区域 */}
      {result && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">转换结果</h3>
          
          {/* 二进制 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">二进制 (Binary)</span>
              <button
                onClick={() => copyToClipboard(result.binary, 'binary')}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                {copiedField === 'binary' ? (
                  <Check className="w-5 h-5 text-green-500" />
                ) : (
                  <Copy className="w-5 h-5" />
                )}
              </button>
            </div>
            <div className="font-mono text-lg break-all">{result.binary}</div>
          </div>

          {/* 八进制 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">八进制 (Octal)</span>
              <button
                onClick={() => copyToClipboard(result.octal, 'octal')}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                {copiedField === 'octal' ? (
                  <Check className="w-5 h-5 text-green-500" />
                ) : (
                  <Copy className="w-5 h-5" />
                )}
              </button>
            </div>
            <div className="font-mono text-lg break-all">{result.octal}</div>
          </div>

          {/* 十进制 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">十进制 (Decimal)</span>
              <button
                onClick={() => copyToClipboard(result.decimal, 'decimal')}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                {copiedField === 'decimal' ? (
                  <Check className="w-5 h-5 text-green-500" />
                ) : (
                  <Copy className="w-5 h-5" />
                )}
              </button>
            </div>
            <div className="font-mono text-lg break-all">{result.decimal}</div>
          </div>

          {/* 十六进制 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">十六进制 (Hexadecimal)</span>
              <button
                onClick={() => copyToClipboard(result.hexadecimal, 'hexadecimal')}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                {copiedField === 'hexadecimal' ? (
                  <Check className="w-5 h-5 text-green-500" />
                ) : (
                  <Copy className="w-5 h-5" />
                )}
              </button>
            </div>
            <div className="font-mono text-lg break-all">{result.hexadecimal}</div>
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>选择输入数值的进制类型</li>
              <li>输入要转换的数值（支持大小写字母）</li>
              <li>点击转换按钮查看所有进制的结果</li>
              <li>点击复制图标可将结果复制到剪贴板</li>
              <li>二进制和十六进制提供快速输入按钮</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 进制对照表 */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-3">常用进制对照表</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="text-left border-b border-gray-200">
                <th className="py-2 px-3">十进制</th>
                <th className="py-2 px-3">二进制</th>
                <th className="py-2 px-3">八进制</th>
                <th className="py-2 px-3">十六进制</th>
              </tr>
            </thead>
            <tbody className="font-mono">
              {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15].map(num => (
                <tr key={num} className="border-b border-gray-100">
                  <td className="py-2 px-3">{num}</td>
                  <td className="py-2 px-3">{num.toString(2).padStart(4, '0')}</td>
                  <td className="py-2 px-3">{num.toString(8)}</td>
                  <td className="py-2 px-3">{num.toString(16).toUpperCase()}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
