'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Calculator, TrendingUp, Copy, Download, BarChart3, PiggyBank } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

interface CompoundResult {
  finalAmount: number
  totalInterest: number
  totalContributions: number
  principal: number
  yearlyBreakdown: {
    year: number
    startingBalance: number
    interest: number
    contributions: number
    endingBalance: number
  }[]
}

export default function CompoundInterestCalculatorTool() {
  const [principal, setPrincipal] = useState('10000')
  const [rate, setRate] = useState('7')
  const [years, setYears] = useState('10')
  const [compoundFrequency, setCompoundFrequency] = useState('12')
  const [contribution, setContribution] = useState('0')
  const [contributionFrequency, setContributionFrequency] = useState('12')
  const [contributionTiming, setContributionTiming] = useState('end')
  const [result, setResult] = useState<CompoundResult | null>(null)
  const [activeTab, setActiveTab] = useState('calculator')

  const compoundFrequencies = [
    { value: '1', label: '年复利（1次/年）' },
    { value: '2', label: '半年复利（2次/年）' },
    { value: '4', label: '季度复利（4次/年）' },
    { value: '12', label: '月复利（12次/年）' },
    { value: '52', label: '周复利（52次/年）' },
    { value: '365', label: '日复利（365次/年）' },
  ]

  const contributionFrequencies = [
    { value: '1', label: '年投入' },
    { value: '2', label: '半年投入' },
    { value: '4', label: '季度投入' },
    { value: '12', label: '月投入' },
    { value: '52', label: '周投入' },
    { value: '365', label: '日投入' },
  ]

  const calculateCompoundInterest = () => {
    const p = parseFloat(principal) || 0
    const r = parseFloat(rate) / 100 || 0
    const t = parseFloat(years) || 0
    const n = parseInt(compoundFrequency) || 1
    const pmt = parseFloat(contribution) || 0
    const pmtFreq = parseInt(contributionFrequency) || 1
    const isBeginning = contributionTiming === 'beginning'

    if (p < 0 || r < 0 || t <= 0 || n <= 0) {
      toast({
        title: "输入错误",
        description: "请输入有效的数值",
        variant: "destructive"
      })
      return
    }

    const yearlyBreakdown: CompoundResult['yearlyBreakdown'] = []
    let currentBalance = p
    let totalContributions = p

    for (let year = 1; year <= t; year++) {
      const startingBalance = currentBalance
      let yearlyInterest = 0
      let yearlyContributions = 0

      // 计算该年度的复利和投入
      for (let period = 1; period <= n; period++) {
        // 如果是期初投入，先加投入再计算利息
        if (isBeginning && period === 1) {
          const periodicContribution = (pmt * pmtFreq) / n
          currentBalance += periodicContribution
          yearlyContributions += periodicContribution
          totalContributions += periodicContribution
        }

        // 计算利息
        const periodicInterest = currentBalance * (r / n)
        currentBalance += periodicInterest
        yearlyInterest += periodicInterest

        // 如果是期末投入，计算利息后加投入
        if (!isBeginning) {
          const periodicContribution = (pmt * pmtFreq) / n
          currentBalance += periodicContribution
          yearlyContributions += periodicContribution
          totalContributions += periodicContribution
        }
      }

      yearlyBreakdown.push({
        year,
        startingBalance,
        interest: yearlyInterest,
        contributions: yearlyContributions,
        endingBalance: currentBalance
      })
    }

    const finalAmount = currentBalance
    const totalInterest = finalAmount - totalContributions

    setResult({
      finalAmount,
      totalInterest,
      totalContributions,
      principal: p,
      yearlyBreakdown
    })

    setActiveTab('result')
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(value)
  }

  const formatPercentage = (value: number, total: number) => {
    return ((value / total) * 100).toFixed(1) + '%'
  }

  const copyResult = () => {
    if (!result) return

    const text = `复利计算结果
初始本金: ${formatCurrency(result.principal)}
总投入: ${formatCurrency(result.totalContributions)}
总利息: ${formatCurrency(result.totalInterest)}
最终金额: ${formatCurrency(result.finalAmount)}
投资回报率: ${formatPercentage(result.totalInterest, result.totalContributions)}
年化收益率: ${rate}%
投资年限: ${years}年`

    navigator.clipboard.writeText(text)
    toast({
      title: "已复制",
      description: "计算结果已复制到剪贴板",
    })
  }

  const downloadCSV = () => {
    if (!result) return

    const headers = ['年份', '期初余额', '年度利息', '年度投入', '期末余额']
    const rows = result.yearlyBreakdown.map(item => [
      item.year,
      item.startingBalance.toFixed(2),
      item.interest.toFixed(2),
      item.contributions.toFixed(2),
      item.endingBalance.toFixed(2)
    ])

    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', '复利计算结果.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "文件已下载",
      description: "复利计算结果已保存为CSV文件",
    })
  }

  const presetScenarios = [
    {
      name: '基础储蓄',
      principal: '10000',
      rate: '3',
      years: '10',
      contribution: '1000',
      description: '保守储蓄方案'
    },
    {
      name: '定期投资',
      principal: '50000',
      rate: '7',
      years: '20',
      contribution: '2000',
      description: '稳健投资方案'
    },
    {
      name: '退休规划',
      principal: '100000',
      rate: '8',
      years: '30',
      contribution: '5000',
      description: '长期退休规划'
    },
    {
      name: '教育基金',
      principal: '20000',
      rate: '6',
      years: '15',
      contribution: '1500',
      description: '子女教育基金'
    }
  ]

  const applyPreset = (preset: typeof presetScenarios[0]) => {
    setPrincipal(preset.principal)
    setRate(preset.rate)
    setYears(preset.years)
    setContribution(preset.contribution)
    setCompoundFrequency('12')
    setContributionFrequency('12')
    setContributionTiming('end')
  }

  const getInvestmentAdvice = () => {
    if (!result) return null

    const totalReturn = result.totalInterest / result.totalContributions
    const annualizedReturn = parseFloat(rate) / 100

    let advice = []
    
    if (annualizedReturn < 0.05) {
      advice.push("💡 考虑寻找更高收益率的投资机会")
    }
    
    if (parseFloat(years) < 10) {
      advice.push("⏰ 延长投资时间可以显著增加复利效果")
    }
    
    if (parseFloat(contribution) === 0) {
      advice.push("💰 定期投入可以大幅提升最终收益")
    }
    
    if (totalReturn > 2) {
      advice.push("🎉 出色的投资回报率！")
    }

    return advice
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-6 w-6" />
            <CardTitle>复利计算器</CardTitle>
          </div>
          <CardDescription>
            计算复利投资收益，支持定期投入和多种复利频率
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="calculator">计算器</TabsTrigger>
              <TabsTrigger value="result">结果分析</TabsTrigger>
              <TabsTrigger value="scenarios">投资场景</TabsTrigger>
            </TabsList>

            <TabsContent value="calculator" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="principal">初始本金（元）</Label>
                    <Input
                      id="principal"
                      type="number"
                      value={principal}
                      onChange={(e) => setPrincipal(e.target.value)}
                      placeholder="输入初始投资金额"
                    />
                  </div>

                  <div>
                    <Label htmlFor="rate">年化收益率（%）</Label>
                    <Input
                      id="rate"
                      type="number"
                      step="0.1"
                      value={rate}
                      onChange={(e) => setRate(e.target.value)}
                      placeholder="输入年化收益率"
                    />
                  </div>

                  <div>
                    <Label htmlFor="years">投资年限（年）</Label>
                    <Input
                      id="years"
                      type="number"
                      value={years}
                      onChange={(e) => setYears(e.target.value)}
                      placeholder="输入投资年限"
                    />
                  </div>

                  <div>
                    <Label htmlFor="compound">复利频率</Label>
                    <Select value={compoundFrequency} onValueChange={setCompoundFrequency}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {compoundFrequencies.map(freq => (
                          <SelectItem key={freq.value} value={freq.value}>
                            {freq.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="contribution">定期投入金额（元）</Label>
                    <Input
                      id="contribution"
                      type="number"
                      value={contribution}
                      onChange={(e) => setContribution(e.target.value)}
                      placeholder="输入定期投入金额"
                    />
                  </div>

                  <div>
                    <Label htmlFor="contributionFreq">投入频率</Label>
                    <Select value={contributionFrequency} onValueChange={setContributionFrequency}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {contributionFrequencies.map(freq => (
                          <SelectItem key={freq.value} value={freq.value}>
                            {freq.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="timing">投入时机</Label>
                    <Select value={contributionTiming} onValueChange={setContributionTiming}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="beginning">期初投入</SelectItem>
                        <SelectItem value="end">期末投入</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button onClick={calculateCompoundInterest} className="w-full">
                    <Calculator className="h-4 w-4 mr-2" />
                    计算复利收益
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="result" className="space-y-6">
              {result ? (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold">计算结果</h3>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" onClick={copyResult}>
                        <Copy className="h-4 w-4 mr-1" />
                        复制
                      </Button>
                      <Button variant="outline" size="sm" onClick={downloadCSV}>
                        <Download className="h-4 w-4 mr-1" />
                        导出CSV
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-green-600">
                          {formatCurrency(result.finalAmount)}
                        </div>
                        <div className="text-sm text-gray-600">最终金额</div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-blue-600">
                          {formatCurrency(result.totalInterest)}
                        </div>
                        <div className="text-sm text-gray-600">总利息收益</div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-orange-600">
                          {formatCurrency(result.totalContributions)}
                        </div>
                        <div className="text-sm text-gray-600">总投入</div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-purple-600">
                          {formatPercentage(result.totalInterest, result.totalContributions)}
                        </div>
                        <div className="text-sm text-gray-600">投资回报率</div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 投资建议 */}
                  {getInvestmentAdvice() && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">投资建议</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {getInvestmentAdvice()?.map((advice, index) => (
                            <div key={index} className="flex items-center space-x-2">
                              <Badge variant="outline">{advice}</Badge>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* 年度明细 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">年度明细</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full table-auto">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">年份</th>
                              <th className="text-right p-2">期初余额</th>
                              <th className="text-right p-2">年度利息</th>
                              <th className="text-right p-2">年度投入</th>
                              <th className="text-right p-2">期末余额</th>
                            </tr>
                          </thead>
                          <tbody>
                            {result.yearlyBreakdown.map(item => (
                              <tr key={item.year} className="border-b">
                                <td className="p-2">{item.year}</td>
                                <td className="text-right p-2">{formatCurrency(item.startingBalance)}</td>
                                <td className="text-right p-2 text-green-600">{formatCurrency(item.interest)}</td>
                                <td className="text-right p-2 text-blue-600">{formatCurrency(item.contributions)}</td>
                                <td className="text-right p-2 font-semibold">{formatCurrency(item.endingBalance)}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">请先进行复利计算</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="scenarios" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {presetScenarios.map(scenario => (
                  <Card key={scenario.name} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{scenario.name}</CardTitle>
                        <PiggyBank className="h-5 w-5 text-green-500" />
                      </div>
                      <CardDescription>{scenario.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>初始本金:</span>
                          <span>{formatCurrency(parseFloat(scenario.principal))}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>年化收益:</span>
                          <span>{scenario.rate}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>投资年限:</span>
                          <span>{scenario.years}年</span>
                        </div>
                        <div className="flex justify-between">
                          <span>月投入:</span>
                          <span>{formatCurrency(parseFloat(scenario.contribution))}</span>
                        </div>
                      </div>
                      <Button 
                        className="w-full mt-4" 
                        variant="outline"
                        onClick={() => applyPreset(scenario)}
                      >
                        应用此方案
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
