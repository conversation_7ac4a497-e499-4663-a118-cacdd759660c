'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Calculator, TrendingUp, BarChart3, Zap, Info } from 'lucide-react'

interface StatisticsResult {
  count: number
  sum: number
  mean: number
  median: number
  mode: number[]
  range: number
  variance: number
  standardDeviation: number
  min: number
  max: number
  q1: number
  q3: number
  iqr: number
  skewness: number
  kurtosis: number
}

const StatisticsCalculatorTool: React.FC = () => {
  const [inputText, setInputText] = useState('')
  const [result, setResult] = useState<StatisticsResult | null>(null)
  const [error, setError] = useState('')
  const [isCalculating, setIsCalculating] = useState(false)

  const parseNumbers = (text: string): number[] => {
    const numbers: number[] = []
    
    // 支持多种分隔符：逗号、空格、换行、分号
    const parts = text.split(/[\s,;\n]+/).filter(part => part.trim())
    
    for (const part of parts) {
      const num = parseFloat(part.trim())
      if (!isNaN(num)) {
        numbers.push(num)
      }
    }
    
    return numbers
  }

  const calculateStatistics = (numbers: number[]): StatisticsResult => {
    if (numbers.length === 0) {
      throw new Error('请输入至少一个数字')
    }

    const sorted = [...numbers].sort((a, b) => a - b)
    const n = numbers.length
    const sum = numbers.reduce((acc, num) => acc + num, 0)
    const mean = sum / n
    
    // 中位数
    const median = n % 2 === 0 
      ? (sorted[n / 2 - 1] + sorted[n / 2]) / 2
      : sorted[Math.floor(n / 2)]
    
    // 众数
    const frequency: { [key: number]: number } = {}
    numbers.forEach(num => {
      frequency[num] = (frequency[num] || 0) + 1
    })
    const maxFreq = Math.max(...Object.values(frequency))
    const mode = Object.keys(frequency)
      .filter(key => frequency[Number(key)] === maxFreq)
      .map(Number)
    
    // 范围
    const min = Math.min(...numbers)
    const max = Math.max(...numbers)
    const range = max - min
    
    // 方差和标准差
    const variance = numbers.reduce((acc, num) => acc + Math.pow(num - mean, 2), 0) / n
    const standardDeviation = Math.sqrt(variance)
    
    // 四分位数
    const q1Index = Math.floor(n * 0.25)
    const q3Index = Math.floor(n * 0.75)
    const q1 = sorted[q1Index]
    const q3 = sorted[q3Index]
    const iqr = q3 - q1
    
    // 偏度 (Skewness)
    const skewness = numbers.reduce((acc, num) => acc + Math.pow((num - mean) / standardDeviation, 3), 0) / n
    
    // 峰度 (Kurtosis)
    const kurtosis = numbers.reduce((acc, num) => acc + Math.pow((num - mean) / standardDeviation, 4), 0) / n - 3
    
    return {
      count: n,
      sum,
      mean,
      median,
      mode,
      range,
      variance,
      standardDeviation,
      min,
      max,
      q1,
      q3,
      iqr,
      skewness,
      kurtosis
    }
  }

  const handleCalculate = () => {
    if (!inputText.trim()) {
      setError('请输入数字数据')
      setResult(null)
      return
    }

    setIsCalculating(true)
    setError('')
    
    try {
      const numbers = parseNumbers(inputText)
      if (numbers.length === 0) {
        throw new Error('未找到有效的数字')
      }
      
      const stats = calculateStatistics(numbers)
      setResult(stats)
    } catch (err) {
      setError(err instanceof Error ? err.message : '计算失败')
      setResult(null)
    } finally {
      setIsCalculating(false)
    }
  }

  const handleClear = () => {
    setInputText('')
    setResult(null)
    setError('')
  }

  const generateSampleData = () => {
    const samples = [
      '10, 20, 30, 40, 50, 60, 70, 80, 90, 100',
      '1.5, 2.3, 3.8, 4.2, 5.1, 6.7, 7.9, 8.4, 9.6, 10.2',
      '85 92 78 94 87 91 89 83 96 88 90 82 95 86 93',
      '100\n200\n300\n150\n250\n180\n220\n280\n160\n240'
    ]
    
    const randomSample = samples[Math.floor(Math.random() * samples.length)]
    setInputText(randomSample)
  }

  const formatNumber = (num: number): string => {
    return num.toFixed(4).replace(/\.?0+$/, '')
  }

  return (
    <div className="max-w-6xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="w-5 h-5" />
            统计计算器
          </CardTitle>
          <CardDescription>
            计算数据集的各种统计指标，包括均值、中位数、标准差等
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium">输入数据</label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateSampleData}
                  className="text-xs"
                >
                  <Zap className="w-3 h-3 mr-1" />
                  生成示例数据
                </Button>
              </div>
              <Textarea
                placeholder="输入数字数据，支持多种分隔符（逗号、空格、换行、分号）&#10;例如：10, 20, 30, 40, 50&#10;或者：&#10;10&#10;20&#10;30"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                className="min-h-[120px] font-mono text-sm"
              />
            </div>
            
            <div className="flex gap-3">
              <Button
                onClick={handleCalculate}
                disabled={isCalculating}
                className="flex-1"
              >
                {isCalculating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    计算中...
                  </>
                ) : (
                  <>
                    <Calculator className="w-4 h-4 mr-2" />
                    计算统计指标
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={handleClear}
                disabled={isCalculating}
              >
                清空
              </Button>
            </div>
          </div>

          {error && (
            <Alert className="border-red-200 bg-red-50">
              <Info className="h-4 w-4" />
              <AlertDescription className="text-red-700">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {result && (
            <div className="space-y-4">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">基本统计</TabsTrigger>
                  <TabsTrigger value="advanced">高级统计</TabsTrigger>
                  <TabsTrigger value="distribution">分布特征</TabsTrigger>
                </TabsList>
                
                <TabsContent value="basic" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <BarChart3 className="w-4 h-4 text-blue-500" />
                        <span className="text-sm font-medium">样本数量</span>
                      </div>
                      <div className="text-2xl font-bold">{result.count}</div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="w-4 h-4 text-green-500" />
                        <span className="text-sm font-medium">总和</span>
                      </div>
                      <div className="text-2xl font-bold">{formatNumber(result.sum)}</div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Calculator className="w-4 h-4 text-purple-500" />
                        <span className="text-sm font-medium">平均值</span>
                      </div>
                      <div className="text-2xl font-bold">{formatNumber(result.mean)}</div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <BarChart3 className="w-4 h-4 text-orange-500" />
                        <span className="text-sm font-medium">中位数</span>
                      </div>
                      <div className="text-2xl font-bold">{formatNumber(result.median)}</div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="w-4 h-4 text-red-500" />
                        <span className="text-sm font-medium">众数</span>
                      </div>
                      <div className="text-lg font-bold">
                        {result.mode.length > 3 ? (
                          <span className="text-sm">
                            {result.mode.slice(0, 3).map(m => formatNumber(m)).join(', ')}...
                          </span>
                        ) : (
                          result.mode.map(m => formatNumber(m)).join(', ')
                        )}
                      </div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <BarChart3 className="w-4 h-4 text-indigo-500" />
                        <span className="text-sm font-medium">极差</span>
                      </div>
                      <div className="text-2xl font-bold">{formatNumber(result.range)}</div>
                    </Card>
                  </div>
                </TabsContent>
                
                <TabsContent value="advanced" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="p-4">
                      <div className="space-y-3">
                        <h4 className="font-medium">最值统计</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">最小值</span>
                            <span className="font-medium">{formatNumber(result.min)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">最大值</span>
                            <span className="font-medium">{formatNumber(result.max)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">极差</span>
                            <span className="font-medium">{formatNumber(result.range)}</span>
                          </div>
                        </div>
                      </div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="space-y-3">
                        <h4 className="font-medium">四分位数</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">第一四分位数 (Q1)</span>
                            <span className="font-medium">{formatNumber(result.q1)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">第三四分位数 (Q3)</span>
                            <span className="font-medium">{formatNumber(result.q3)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">四分位距 (IQR)</span>
                            <span className="font-medium">{formatNumber(result.iqr)}</span>
                          </div>
                        </div>
                      </div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="space-y-3">
                        <h4 className="font-medium">离散程度</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">方差</span>
                            <span className="font-medium">{formatNumber(result.variance)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">标准差</span>
                            <span className="font-medium">{formatNumber(result.standardDeviation)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">变异系数</span>
                            <span className="font-medium">
                              {formatNumber((result.standardDeviation / result.mean) * 100)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </div>
                </TabsContent>
                
                <TabsContent value="distribution" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="p-4">
                      <div className="space-y-3">
                        <h4 className="font-medium">分布形状</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">偏度 (Skewness)</span>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{formatNumber(result.skewness)}</span>
                              <Badge variant={Math.abs(result.skewness) < 0.5 ? "default" : "secondary"}>
                                {Math.abs(result.skewness) < 0.5 ? "对称" : 
                                 result.skewness > 0 ? "右偏" : "左偏"}
                              </Badge>
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">峰度 (Kurtosis)</span>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{formatNumber(result.kurtosis)}</span>
                              <Badge variant={Math.abs(result.kurtosis) < 0.5 ? "default" : "secondary"}>
                                {Math.abs(result.kurtosis) < 0.5 ? "正态" : 
                                 result.kurtosis > 0 ? "尖峰" : "平峰"}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                    
                    <Card className="p-4">
                      <div className="space-y-3">
                        <h4 className="font-medium">分布描述</h4>
                        <div className="space-y-2 text-sm text-gray-600">
                          <p>
                            <strong>偏度解释：</strong>
                            {Math.abs(result.skewness) < 0.5 
                              ? "数据分布基本对称" 
                              : result.skewness > 0 
                                ? "数据向右偏斜，右尾较长" 
                                : "数据向左偏斜，左尾较长"}
                          </p>
                          <p>
                            <strong>峰度解释：</strong>
                            {Math.abs(result.kurtosis) < 0.5 
                              ? "数据分布接近正态分布" 
                              : result.kurtosis > 0 
                                ? "数据分布比正态分布更尖峭" 
                                : "数据分布比正态分布更平坦"}
                          </p>
                        </div>
                      </div>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default StatisticsCalculatorTool
