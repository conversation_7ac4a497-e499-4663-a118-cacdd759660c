'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Calculator, History, Trash2, Copy, RotateCcw } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

interface CalculationHistory {
  expression: string
  result: string
  timestamp: Date
}

export default function ScientificCalculatorTool() {
  const [display, setDisplay] = useState('0')
  const [previousValue, setPreviousValue] = useState<string>('')
  const [operation, setOperation] = useState<string>('')
  const [waitingForNewValue, setWaitingForNewValue] = useState(false)
  const [history, setHistory] = useState<CalculationHistory[]>([])
  const [isRadians, setIsRadians] = useState(true)
  const [memory, setMemory] = useState(0)
  const [showHistory, setShowHistory] = useState(false)

  const addToHistory = (expression: string, result: string) => {
    const newEntry: CalculationHistory = {
      expression,
      result,
      timestamp: new Date()
    }
    setHistory(prev => [newEntry, ...prev.slice(0, 19)]) // 保留最近20条记录
  }

  const inputNumber = (num: string) => {
    if (waitingForNewValue) {
      setDisplay(num)
      setWaitingForNewValue(false)
    } else {
      setDisplay(display === '0' ? num : display + num)
    }
  }

  const inputDecimal = () => {
    if (waitingForNewValue) {
      setDisplay('0.')
      setWaitingForNewValue(false)
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.')
    }
  }

  const clear = () => {
    setDisplay('0')
    setPreviousValue('')
    setOperation('')
    setWaitingForNewValue(false)
  }

  const clearEntry = () => {
    setDisplay('0')
  }

  const performOperation = (nextOperation: string) => {
    const inputValue = parseFloat(display)

    if (previousValue === '') {
      setPreviousValue(display)
    } else if (operation) {
      const currentValue = parseFloat(previousValue)
      const newValue = calculate(currentValue, inputValue, operation)

      setDisplay(String(newValue))
      setPreviousValue(String(newValue))
      
      if (operation !== '=') {
        addToHistory(`${currentValue} ${operation} ${inputValue}`, String(newValue))
      }
    }

    setWaitingForNewValue(true)
    setOperation(nextOperation)
  }

  const calculate = (firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case '+':
        return firstValue + secondValue
      case '-':
        return firstValue - secondValue
      case '*':
        return firstValue * secondValue
      case '/':
        return firstValue / secondValue
      case '^':
        return Math.pow(firstValue, secondValue)
      case 'mod':
        return firstValue % secondValue
      case 'log':
        return Math.log10(firstValue) // 以10为底的对数
      case 'ln':
        return Math.log(firstValue) // 自然对数
      case 'exp':
        return Math.exp(firstValue)
      case 'sqrt':
        return Math.sqrt(firstValue)
      case 'cbrt':
        return Math.cbrt(firstValue)
      case 'sin':
        return Math.sin(isRadians ? firstValue : firstValue * Math.PI / 180)
      case 'cos':
        return Math.cos(isRadians ? firstValue : firstValue * Math.PI / 180)
      case 'tan':
        return Math.tan(isRadians ? firstValue : firstValue * Math.PI / 180)
      case 'asin':
        return isRadians ? Math.asin(firstValue) : Math.asin(firstValue) * 180 / Math.PI
      case 'acos':
        return isRadians ? Math.acos(firstValue) : Math.acos(firstValue) * 180 / Math.PI
      case 'atan':
        return isRadians ? Math.atan(firstValue) : Math.atan(firstValue) * 180 / Math.PI
      case 'factorial':
        return factorial(firstValue)
      case '1/x':
        return 1 / firstValue
      case 'x²':
        return firstValue * firstValue
      case 'x³':
        return firstValue * firstValue * firstValue
      case 'abs':
        return Math.abs(firstValue)
      case 'floor':
        return Math.floor(firstValue)
      case 'ceil':
        return Math.ceil(firstValue)
      case 'round':
        return Math.round(firstValue)
      default:
        return secondValue
    }
  }

  const factorial = (n: number): number => {
    if (n < 0) return NaN
    if (n === 0 || n === 1) return 1
    let result = 1
    for (let i = 2; i <= n; i++) {
      result *= i
    }
    return result
  }

  const performUnaryOperation = (operation: string) => {
    const inputValue = parseFloat(display)
    const result = calculate(inputValue, 0, operation)
    setDisplay(String(result))
    addToHistory(`${operation}(${inputValue})`, String(result))
    setWaitingForNewValue(true)
  }

  const insertConstant = (constant: string) => {
    let value: number
    switch (constant) {
      case 'π':
        value = Math.PI
        break
      case 'e':
        value = Math.E
        break
      case 'φ':
        value = (1 + Math.sqrt(5)) / 2 // 黄金比例
        break
      default:
        return
    }
    setDisplay(String(value))
    setWaitingForNewValue(true)
  }

  const memoryOperation = (operation: string) => {
    const value = parseFloat(display)
    switch (operation) {
      case 'MC':
        setMemory(0)
        toast({
          title: "内存已清除",
          description: "内存值已重置为0",
        })
        break
      case 'MR':
        setDisplay(String(memory))
        setWaitingForNewValue(true)
        break
      case 'M+':
        setMemory(memory + value)
        toast({
          title: "已添加到内存",
          description: `内存值: ${memory + value}`,
        })
        break
      case 'M-':
        setMemory(memory - value)
        toast({
          title: "已从内存减去",
          description: `内存值: ${memory - value}`,
        })
        break
      case 'MS':
        setMemory(value)
        toast({
          title: "已保存到内存",
          description: `内存值: ${value}`,
        })
        break
    }
  }

  const copyResult = () => {
    navigator.clipboard.writeText(display)
    toast({
      title: "已复制",
      description: "计算结果已复制到剪贴板",
    })
  }

  const clearHistory = () => {
    setHistory([])
    toast({
      title: "历史记录已清除",
      description: "所有计算历史记录已删除",
    })
  }

  const ButtonGrid = () => (
    <div className="space-y-4">
      {/* 内存和模式切换 */}
      <div className="grid grid-cols-6 gap-2">
        <Button variant="outline" size="sm" onClick={() => memoryOperation('MC')}>
          MC
        </Button>
        <Button variant="outline" size="sm" onClick={() => memoryOperation('MR')}>
          MR
        </Button>
        <Button variant="outline" size="sm" onClick={() => memoryOperation('M+')}>
          M+
        </Button>
        <Button variant="outline" size="sm" onClick={() => memoryOperation('M-')}>
          M-
        </Button>
        <Button variant="outline" size="sm" onClick={() => memoryOperation('MS')}>
          MS
        </Button>
        <Button
          variant={isRadians ? "default" : "outline"}
          size="sm"
          onClick={() => setIsRadians(!isRadians)}
        >
          {isRadians ? 'RAD' : 'DEG'}
        </Button>
      </div>

      {/* 三角函数 */}
      <div className="grid grid-cols-6 gap-2">
        <Button variant="outline" onClick={() => performUnaryOperation('sin')}>
          sin
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('cos')}>
          cos
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('tan')}>
          tan
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('asin')}>
          sin⁻¹
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('acos')}>
          cos⁻¹
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('atan')}>
          tan⁻¹
        </Button>
      </div>

      {/* 对数和指数 */}
      <div className="grid grid-cols-6 gap-2">
        <Button variant="outline" onClick={() => performUnaryOperation('log')}>
          log
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('ln')}>
          ln
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('exp')}>
          eˣ
        </Button>
        <Button variant="outline" onClick={() => performOperation('^')}>
          xʸ
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('sqrt')}>
          √
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('cbrt')}>
          ∛
        </Button>
      </div>

      {/* 其他函数 */}
      <div className="grid grid-cols-6 gap-2">
        <Button variant="outline" onClick={() => performUnaryOperation('factorial')}>
          x!
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('1/x')}>
          1/x
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('x²')}>
          x²
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('x³')}>
          x³
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('abs')}>
          |x|
        </Button>
        <Button variant="outline" onClick={() => performOperation('mod')}>
          mod
        </Button>
      </div>

      {/* 常数 */}
      <div className="grid grid-cols-6 gap-2">
        <Button variant="outline" onClick={() => insertConstant('π')}>
          π
        </Button>
        <Button variant="outline" onClick={() => insertConstant('e')}>
          e
        </Button>
        <Button variant="outline" onClick={() => insertConstant('φ')}>
          φ
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('floor')}>
          ⌊x⌋
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('ceil')}>
          ⌈x⌉
        </Button>
        <Button variant="outline" onClick={() => performUnaryOperation('round')}>
          round
        </Button>
      </div>

      {/* 基本计算器 */}
      <div className="grid grid-cols-4 gap-2">
        <Button variant="outline" onClick={clear}>
          AC
        </Button>
        <Button variant="outline" onClick={clearEntry}>
          CE
        </Button>
        <Button variant="outline" onClick={() => setDisplay(display.slice(0, -1) || '0')}>
          ⌫
        </Button>
        <Button variant="outline" onClick={() => performOperation('/')}>
          ÷
        </Button>
      </div>

      {/* 数字键盘 */}
      <div className="grid grid-cols-4 gap-2">
        <Button variant="outline" onClick={() => inputNumber('7')}>7</Button>
        <Button variant="outline" onClick={() => inputNumber('8')}>8</Button>
        <Button variant="outline" onClick={() => inputNumber('9')}>9</Button>
        <Button variant="outline" onClick={() => performOperation('*')}>×</Button>
      </div>

      <div className="grid grid-cols-4 gap-2">
        <Button variant="outline" onClick={() => inputNumber('4')}>4</Button>
        <Button variant="outline" onClick={() => inputNumber('5')}>5</Button>
        <Button variant="outline" onClick={() => inputNumber('6')}>6</Button>
        <Button variant="outline" onClick={() => performOperation('-')}>-</Button>
      </div>

      <div className="grid grid-cols-4 gap-2">
        <Button variant="outline" onClick={() => inputNumber('1')}>1</Button>
        <Button variant="outline" onClick={() => inputNumber('2')}>2</Button>
        <Button variant="outline" onClick={() => inputNumber('3')}>3</Button>
        <Button variant="outline" onClick={() => performOperation('+')}>+</Button>
      </div>

      <div className="grid grid-cols-4 gap-2">
        <Button variant="outline" onClick={() => inputNumber('0')} className="col-span-2">0</Button>
        <Button variant="outline" onClick={inputDecimal}>.</Button>
        <Button onClick={() => performOperation('=')} className="bg-blue-500 hover:bg-blue-600">
          =
        </Button>
      </div>
    </div>
  )

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Calculator className="h-6 w-6" />
            <CardTitle>科学计算器</CardTitle>
          </div>
          <CardDescription>
            功能强大的科学计算器，支持基本运算、三角函数、对数、指数等数学函数
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 显示屏 */}
          <div className="space-y-2">
            <div className="relative">
              <Input
                type="text"
                value={display}
                readOnly
                className="text-right text-2xl font-mono h-16 pr-12"
              />
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 -translate-y-1/2"
                onClick={copyResult}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            {previousValue && operation && (
              <div className="text-sm text-gray-500 text-right">
                {previousValue} {operation}
              </div>
            )}
          </div>

          {/* 状态显示 */}
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              {memory !== 0 && (
                <Badge variant="secondary">
                  M: {memory}
                </Badge>
              )}
              <Badge variant={isRadians ? "default" : "outline"}>
                {isRadians ? 'RAD' : 'DEG'}
              </Badge>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHistory(!showHistory)}
              >
                <History className="h-4 w-4 mr-1" />
                历史记录
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearHistory}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                清除历史
              </Button>
            </div>
          </div>

          {/* 计算器按钮 */}
          <ButtonGrid />

          {/* 历史记录 */}
          {showHistory && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg">计算历史</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {history.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">暂无历史记录</p>
                  ) : (
                    history.map((item, index) => (
                      <div
                        key={index}
                        className="flex justify-between items-center p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
                        onClick={() => {
                          setDisplay(item.result)
                          setWaitingForNewValue(true)
                        }}
                      >
                        <span className="text-sm">{item.expression}</span>
                        <span className="font-mono font-bold">{item.result}</span>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 使用说明 */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">使用说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-semibold mb-2">基本运算</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• 支持加减乘除运算</li>
                    <li>• AC: 全部清除</li>
                    <li>• CE: 清除当前输入</li>
                    <li>• ⌫: 删除最后一位</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">三角函数</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• sin, cos, tan: 正弦、余弦、正切</li>
                    <li>• sin⁻¹, cos⁻¹, tan⁻¹: 反三角函数</li>
                    <li>• RAD/DEG: 弧度/角度模式切换</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">对数指数</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• log: 常用对数(以10为底)</li>
                    <li>• ln: 自然对数(以e为底)</li>
                    <li>• eˣ: 自然指数函数</li>
                    <li>• xʸ: 幂运算</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">内存功能</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• MC: 内存清除</li>
                    <li>• MR: 内存读取</li>
                    <li>• M+: 内存加法</li>
                    <li>• M-: 内存减法</li>
                    <li>• MS: 内存保存</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}
