'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  Download, 
  Play, 
  Pause,
  SkipBack,
  SkipForward,
  Image as ImageIcon,
  Film,
  Zap,
  Settings,
  Eye,
  Layers,
  Grid,
  Package,
  Trash2,
  CheckSquare,
  Square,
  RotateCw,
  Maximize,
  Info
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface GifFrame {
  id: number
  canvas: HTMLCanvasElement
  delay: number
  selected: boolean
  timestamp: number
}

interface ExportSettings {
  format: 'png' | 'jpg' | 'webp'
  quality: number
  scale: number
  prefix: string
  startIndex: number
  addTimestamps: boolean
  createZip: boolean
}

const GifFrameExtractorTool: React.FC = () => {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const [gifFile, setGifFile] = useState<File | null>(null)
  const [gifUrl, setGifUrl] = useState<string>('')
  const [frames, setFrames] = useState<GifFrame[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [loadingProgress, setLoadingProgress] = useState(0)
  const [currentFrame, setCurrentFrame] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [playbackSpeed, setPlaybackSpeed] = useState(1)
  const [selectedFrames, setSelectedFrames] = useState<Set<number>>(new Set())
  const [exportSettings, setExportSettings] = useState<ExportSettings>({
    format: 'png',
    quality: 90,
    scale: 1,
    prefix: 'frame',
    startIndex: 1,
    addTimestamps: true,
    createZip: false
  })

  const [gifInfo, setGifInfo] = useState<{
    width: number
    height: number
    frameCount: number
    duration: number
    size: string
  } | null>(null)

  const playbackIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // GIF解析函数
  const parseGif = useCallback(async (file: File) => {
    setIsLoading(true)
    setLoadingProgress(0)
    
    try {
      const arrayBuffer = await file.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)
      
      // 简化的GIF解析（实际应用中建议使用专业的GIF解析库）
      const frames: GifFrame[] = []
      
      // 创建临时图片元素来加载GIF
      const img = new Image()
      const url = URL.createObjectURL(file)
      
      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        img.src = url
      })

      // 模拟帧提取过程（实际应用中需要使用gif-js或类似库）
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      canvas.width = img.width
      canvas.height = img.height

      // 绘制图片到canvas
      ctx.drawImage(img, 0, 0)
      
      // 创建第一帧（简化示例）
      const frameCanvas = document.createElement('canvas')
      frameCanvas.width = img.width
      frameCanvas.height = img.height
      const frameCtx = frameCanvas.getContext('2d')!
      frameCtx.drawImage(canvas, 0, 0)

      frames.push({
        id: 0,
        canvas: frameCanvas,
        delay: 100, // 默认延迟
        selected: false,
        timestamp: 0
      })

      // 模拟更多帧（实际应用中从GIF数据中提取）
      for (let i = 1; i < 10; i++) {
        setLoadingProgress((i / 10) * 100)
        
        const frameCanvas = document.createElement('canvas')
        frameCanvas.width = img.width
        frameCanvas.height = img.height
        const frameCtx = frameCanvas.getContext('2d')!
        
        // 创建变化效果（实际中是真实的GIF帧）
        frameCtx.drawImage(canvas, 0, 0)
        frameCtx.fillStyle = `rgba(255, 0, 0, ${0.1 * i})`
        frameCtx.fillRect(0, 0, 50, 50)
        
        frames.push({
          id: i,
          canvas: frameCanvas,
          delay: 100,
          selected: false,
          timestamp: i * 100
        })
        
        await new Promise(resolve => setTimeout(resolve, 50))
      }

      setFrames(frames)
      setGifInfo({
        width: img.width,
        height: img.height,
        frameCount: frames.length,
        duration: frames.length * 100,
        size: (file.size / 1024).toFixed(2) + ' KB'
      })

      URL.revokeObjectURL(url)
      setCurrentFrame(0)
      setIsLoading(false)
      
      toast({
        title: '解析完成',
        description: `成功提取 ${frames.length} 帧`
      })
    } catch (error) {
      console.error('GIF解析失败:', error)
      setIsLoading(false)
      toast({
        title: '解析失败',
        description: '无法解析GIF文件，请检查文件格式',
        variant: 'destructive'
      })
    }
  }, [toast])

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type === 'image/gif') {
      setGifFile(file)
      setGifUrl(URL.createObjectURL(file))
      parseGif(file)
    } else {
      toast({
        title: '文件格式错误',
        description: '请选择GIF格式的文件',
        variant: 'destructive'
      })
    }
  }, [parseGif, toast])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file && file.type === 'image/gif') {
      setGifFile(file)
      setGifUrl(URL.createObjectURL(file))
      parseGif(file)
    }
  }, [parseGif])

  // 播放控制
  const togglePlayback = useCallback(() => {
    if (isPlaying) {
      if (playbackIntervalRef.current) {
        clearInterval(playbackIntervalRef.current)
        playbackIntervalRef.current = null
      }
    } else {
      playbackIntervalRef.current = setInterval(() => {
        setCurrentFrame(prev => (prev + 1) % frames.length)
      }, frames[currentFrame]?.delay / playbackSpeed || 100)
    }
    setIsPlaying(!isPlaying)
  }, [isPlaying, frames, currentFrame, playbackSpeed])

  const goToFrame = useCallback((frameIndex: number) => {
    setCurrentFrame(Math.max(0, Math.min(frameIndex, frames.length - 1)))
  }, [frames.length])

  const nextFrame = useCallback(() => {
    goToFrame(currentFrame + 1)
  }, [currentFrame, goToFrame])

  const prevFrame = useCallback(() => {
    goToFrame(currentFrame - 1)
  }, [currentFrame, goToFrame])

  // 帧选择
  const toggleFrameSelection = useCallback((frameId: number) => {
    setSelectedFrames(prev => {
      const newSet = new Set(prev)
      if (newSet.has(frameId)) {
        newSet.delete(frameId)
      } else {
        newSet.add(frameId)
      }
      return newSet
    })
  }, [])

  const selectAllFrames = useCallback(() => {
    setSelectedFrames(new Set(frames.map(f => f.id)))
  }, [frames])

  const deselectAllFrames = useCallback(() => {
    setSelectedFrames(new Set())
  }, [])

  // 导出功能
  const exportFrames = useCallback(async () => {
    const framesToExport = selectedFrames.size > 0 
      ? frames.filter(f => selectedFrames.has(f.id))
      : frames

    if (framesToExport.length === 0) {
      toast({
        title: '没有选择帧',
        description: '请至少选择一帧进行导出',
        variant: 'destructive'
      })
      return
    }

    if (exportSettings.createZip) {
      // 创建ZIP文件（需要JSZip库）
      const JSZip = (await import('jszip')).default
      const zip = new JSZip()

      framesToExport.forEach((frame, index) => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')!
        
        canvas.width = frame.canvas.width * exportSettings.scale
        canvas.height = frame.canvas.height * exportSettings.scale
        
        ctx.imageSmoothingEnabled = true
        ctx.drawImage(frame.canvas, 0, 0, canvas.width, canvas.height)

        const filename = `${exportSettings.prefix}_${String(exportSettings.startIndex + index).padStart(3, '0')}${exportSettings.addTimestamps ? `_${frame.timestamp}ms` : ''}.${exportSettings.format}`
        
        canvas.toBlob((blob) => {
          if (blob) {
            zip.file(filename, blob)
          }
        }, `image/${exportSettings.format}`, exportSettings.quality / 100)
      })

      const zipBlob = await zip.generateAsync({ type: 'blob' })
      const url = URL.createObjectURL(zipBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = `gif-frames-${Date.now()}.zip`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } else {
      // 单独导出每一帧
      framesToExport.forEach((frame, index) => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')!
        
        canvas.width = frame.canvas.width * exportSettings.scale
        canvas.height = frame.canvas.height * exportSettings.scale
        
        ctx.imageSmoothingEnabled = true
        ctx.drawImage(frame.canvas, 0, 0, canvas.width, canvas.height)

        const filename = `${exportSettings.prefix}_${String(exportSettings.startIndex + index).padStart(3, '0')}${exportSettings.addTimestamps ? `_${frame.timestamp}ms` : ''}.${exportSettings.format}`
        
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = filename
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(url)
          }
        }, `image/${exportSettings.format}`, exportSettings.quality / 100)
      })
    }

    toast({
      title: '导出完成',
      description: `成功导出 ${framesToExport.length} 帧`
    })
  }, [frames, selectedFrames, exportSettings, toast])

  // 清理资源
  useEffect(() => {
    return () => {
      if (playbackIntervalRef.current) {
        clearInterval(playbackIntervalRef.current)
      }
      if (gifUrl) {
        URL.revokeObjectURL(gifUrl)
      }
    }
  }, [gifUrl])

  // 渲染当前帧到预览canvas
  useEffect(() => {
    if (frames.length > 0 && canvasRef.current) {
      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')!
      const currentFrameData = frames[currentFrame]
      
      if (currentFrameData) {
        canvas.width = currentFrameData.canvas.width
        canvas.height = currentFrameData.canvas.height
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.drawImage(currentFrameData.canvas, 0, 0)
      }
    }
  }, [frames, currentFrame])

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Film className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">GIF帧分割器</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          提取GIF动画中的每一帧，支持预览、选择和批量导出为静态图片
        </p>
      </div>

      {!gifFile ? (
        // 文件上传界面
        <Card className="border-2 border-dashed">
          <CardContent 
            className="flex flex-col items-center justify-center py-20 cursor-pointer hover:bg-gray-50 transition-colors"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">上传GIF文件</p>
            <p className="text-sm text-gray-500 mb-4">拖拽文件到此处或点击选择</p>
            <p className="text-xs text-gray-400">支持的格式: GIF</p>
            <input
              ref={fileInputRef}
              type="file"
              accept=".gif,image/gif"
              onChange={handleFileUpload}
              className="hidden"
            />
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧控制面板 */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  GIF信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {gifInfo && (
                  <>
                    <div className="text-sm">
                      <span className="text-gray-600">尺寸:</span>
                      <span className="ml-2 font-mono">{gifInfo.width} × {gifInfo.height}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">帧数:</span>
                      <span className="ml-2 font-mono">{gifInfo.frameCount}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">时长:</span>
                      <span className="ml-2 font-mono">{(gifInfo.duration / 1000).toFixed(2)}s</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">大小:</span>
                      <span className="ml-2 font-mono">{gifInfo.size}</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="h-5 w-5" />
                  播放控制
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Button size="sm" onClick={prevFrame} variant="outline">
                    <SkipBack className="h-4 w-4" />
                  </Button>
                  <Button size="sm" onClick={togglePlayback}>
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button size="sm" onClick={nextFrame} variant="outline">
                    <SkipForward className="h-4 w-4" />
                  </Button>
                </div>

                <div>
                  <Label className="text-sm">当前帧: {currentFrame + 1} / {frames.length}</Label>
                  <Slider
                    value={[currentFrame]}
                    onValueChange={([value]) => goToFrame(value)}
                    max={Math.max(0, frames.length - 1)}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label className="text-sm">播放速度: {playbackSpeed}x</Label>
                  <Slider
                    value={[playbackSpeed]}
                    onValueChange={([value]) => setPlaybackSpeed(value)}
                    min={0.1}
                    max={3}
                    step={0.1}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckSquare className="h-5 w-5" />
                  帧选择
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Button size="sm" onClick={selectAllFrames} variant="outline" className="flex-1">
                    全选
                  </Button>
                  <Button size="sm" onClick={deselectAllFrames} variant="outline" className="flex-1">
                    清除
                  </Button>
                </div>
                <div className="text-sm text-gray-600">
                  已选择: {selectedFrames.size} / {frames.length} 帧
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  导出设置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm">格式</Label>
                  <Select value={exportSettings.format} onValueChange={(value: 'png' | 'jpg' | 'webp') => setExportSettings(prev => ({ ...prev, format: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="png">PNG</SelectItem>
                      <SelectItem value="jpg">JPG</SelectItem>
                      <SelectItem value="webp">WebP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {exportSettings.format === 'jpg' && (
                  <div>
                    <Label className="text-sm">质量: {exportSettings.quality}%</Label>
                    <Slider
                      value={[exportSettings.quality]}
                      onValueChange={([value]) => setExportSettings(prev => ({ ...prev, quality: value }))}
                      min={1}
                      max={100}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                )}

                <div>
                  <Label className="text-sm">缩放倍数: {exportSettings.scale}x</Label>
                  <Slider
                    value={[exportSettings.scale]}
                    onValueChange={([value]) => setExportSettings(prev => ({ ...prev, scale: value }))}
                    min={0.1}
                    max={3}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label className="text-sm">文件名前缀</Label>
                  <Input
                    value={exportSettings.prefix}
                    onChange={(e) => setExportSettings(prev => ({ ...prev, prefix: e.target.value }))}
                    placeholder="frame"
                  />
                </div>

                <div>
                  <Label className="text-sm">起始序号</Label>
                  <Input
                    type="number"
                    value={exportSettings.startIndex}
                    onChange={(e) => setExportSettings(prev => ({ ...prev, startIndex: parseInt(e.target.value) || 1 }))}
                    min={0}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label className="text-sm">添加时间戳</Label>
                  <Switch
                    checked={exportSettings.addTimestamps}
                    onCheckedChange={(checked) => setExportSettings(prev => ({ ...prev, addTimestamps: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label className="text-sm">打包为ZIP</Label>
                  <Switch
                    checked={exportSettings.createZip}
                    onCheckedChange={(checked) => setExportSettings(prev => ({ ...prev, createZip: checked }))}
                  />
                </div>

                <Button onClick={exportFrames} className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  导出帧 ({selectedFrames.size > 0 ? selectedFrames.size : frames.length})
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* 右侧预览和帧列表 */}
          <div className="lg:col-span-3 space-y-6">
            {isLoading ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-20">
                  <RotateCw className="h-8 w-8 text-blue-600 animate-spin mb-4" />
                  <p className="text-lg font-medium text-gray-700 mb-2">正在解析GIF...</p>
                  <Progress value={loadingProgress} className="w-64" />
                  <p className="text-sm text-gray-500 mt-2">{Math.round(loadingProgress)}%</p>
                </CardContent>
              </Card>
            ) : (
              <>
                {/* 预览区域 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      预览区域
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-center">
                      <div className="border border-gray-200 rounded-lg p-4 bg-checkered">
                        <canvas
                          ref={canvasRef}
                          className="max-w-full max-h-96 shadow-sm"
                          style={{ imageRendering: 'pixelated' }}
                        />
                      </div>
                    </div>
                    {frames[currentFrame] && (
                      <div className="mt-4 text-center text-sm text-gray-600">
                        帧 {currentFrame + 1}: 延迟 {frames[currentFrame].delay}ms，时间戳 {frames[currentFrame].timestamp}ms
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* 帧缩略图网格 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Grid className="h-5 w-5" />
                      所有帧 ({frames.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-6 md:grid-cols-8 lg:grid-cols-10 gap-2">
                      {frames.map((frame, index) => (
                        <div
                          key={frame.id}
                          className={`relative cursor-pointer border-2 rounded-lg overflow-hidden transition-all ${
                            currentFrame === index
                              ? 'border-blue-500 ring-2 ring-blue-200'
                              : selectedFrames.has(frame.id)
                              ? 'border-green-500 ring-2 ring-green-200'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => goToFrame(index)}
                        >
                          <canvas
                            ref={(canvas) => {
                              if (canvas && frame.canvas) {
                                const ctx = canvas.getContext('2d')!
                                canvas.width = 60
                                canvas.height = 60
                                ctx.imageSmoothingEnabled = false
                                ctx.drawImage(frame.canvas, 0, 0, 60, 60)
                              }
                            }}
                            className="w-full h-auto"
                          />
                          <div className="absolute top-1 left-1">
                            <Badge variant="secondary" className="text-xs px-1 py-0">
                              {index + 1}
                            </Badge>
                          </div>
                          <div
                            className={`absolute top-1 right-1 w-4 h-4 border rounded cursor-pointer ${
                              selectedFrames.has(frame.id)
                                ? 'bg-green-500 border-green-500'
                                : 'bg-white border-gray-300 hover:border-gray-400'
                            }`}
                            onClick={(e) => {
                              e.stopPropagation()
                              toggleFrameSelection(frame.id)
                            }}
                          >
                            {selectedFrames.has(frame.id) && (
                              <CheckSquare className="w-3 h-3 text-white m-0.5" />
                            )}
                          </div>
                          <div className="absolute bottom-1 right-1">
                            <Badge variant="outline" className="text-xs px-1 py-0">
                              {frame.delay}ms
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </div>
      )}

      <style jsx>{`
        .bg-checkered {
          background-image: 
            linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
            linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
            linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
            linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
          background-size: 20px 20px;
          background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
      `}</style>
    </div>
  )
}

export default GifFrameExtractorTool