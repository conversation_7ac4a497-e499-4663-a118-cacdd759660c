'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { 
  Copy, 
  Download, 
  Type, 
  Plus, 
  Minus, 
  RotateCcw, 
  Play,
  Heart,
  Star,
  Eye,
  Palette,
  Zap,
  Settings,
  BookOpen,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface FontPair {
  id: string
  name: string
  headingFont: string
  bodyFont: string
  category: string
  style: string
  description: string
  tags: string[]
  popularity: number
  compatibility: string[]
  previewText: {
    heading: string
    body: string
  }
}

interface FontLibrary {
  name: string
  category: 'serif' | 'sans-serif' | 'display' | 'monospace' | 'handwriting'
  weights: string[]
  styles: string[]
  googleFont: boolean
  webSafe: boolean
  fallback: string
}

const FontPairingTool: React.FC = () => {
  const { toast } = useToast()

  const [selectedPairId, setSelectedPairId] = useState<string>('1')
  const [customHeadingFont, setCustomHeadingFont] = useState<string>('Inter')
  const [customBodyFont, setCustomBodyFont] = useState<string>('Inter')
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [previewMode, setPreviewMode] = useState<'pairs' | 'custom' | 'compare'>('pairs')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterStyle, setFilterStyle] = useState<string>('all')
  const [showOnlyGoogleFonts, setShowOnlyGoogleFonts] = useState(false)
  const [customPreviewText, setCustomPreviewText] = useState({
    heading: 'Typography is the art of arranging type',
    body: 'Good typography establishes a clear hierarchy, guides the reader through the content, and creates an emotional connection. When choosing fonts, consider readability, brand personality, and the overall user experience.'
  })

  const fontPairs: FontPair[] = [
    {
      id: '1',
      name: 'Modern Professional',
      headingFont: 'Inter',
      bodyFont: 'Inter',
      category: 'business',
      style: 'modern',
      description: '现代商务风格，简洁专业，适合企业网站和应用',
      tags: ['professional', 'clean', 'modern', 'versatile'],
      popularity: 95,
      compatibility: ['web', 'mobile', 'print'],
      previewText: {
        heading: 'Professional Excellence',
        body: 'Creating exceptional experiences through thoughtful design and clear communication.'
      }
    },
    {
      id: '2',
      name: 'Editorial Elegance',
      headingFont: 'Playfair Display',
      bodyFont: 'Source Sans Pro',
      category: 'editorial',
      style: 'elegant',
      description: '优雅的编辑风格，适合新闻、杂志和博客',
      tags: ['elegant', 'editorial', 'contrast', 'readable'],
      popularity: 88,
      compatibility: ['web', 'print'],
      previewText: {
        heading: 'The Art of Storytelling',
        body: 'Every great story begins with a compelling narrative that draws readers into a world of imagination and discovery.'
      }
    },
    {
      id: '3',
      name: 'Tech Startup',
      headingFont: 'Poppins',
      bodyFont: 'Open Sans',
      category: 'tech',
      style: 'friendly',
      description: '友好的科技风格，适合初创公司和科技产品',
      tags: ['friendly', 'tech', 'approachable', 'modern'],
      popularity: 92,
      compatibility: ['web', 'mobile', 'app'],
      previewText: {
        heading: 'Innovation Simplified',
        body: 'We believe technology should be accessible, intuitive, and designed to enhance human experiences.'
      }
    },
    {
      id: '4',
      name: 'Creative Studio',
      headingFont: 'Montserrat',
      bodyFont: 'Lato',
      category: 'creative',
      style: 'creative',
      description: '创意设计风格，适合设计工作室和创意机构',
      tags: ['creative', 'artistic', 'bold', 'expressive'],
      popularity: 85,
      compatibility: ['web', 'portfolio'],
      previewText: {
        heading: 'Design Beyond Boundaries',
        body: 'Pushing creative limits to deliver innovative solutions that inspire and engage audiences worldwide.'
      }
    },
    {
      id: '5',
      name: 'Academic Classic',
      headingFont: 'Merriweather',
      bodyFont: 'Merriweather',
      category: 'academic',
      style: 'traditional',
      description: '经典学术风格，适合教育机构和研究发布',
      tags: ['academic', 'traditional', 'scholarly', 'authoritative'],
      popularity: 78,
      compatibility: ['web', 'print', 'academic'],
      previewText: {
        heading: 'Knowledge and Discovery',
        body: 'The pursuit of knowledge drives us to explore new frontiers, challenge assumptions, and contribute to the advancement of human understanding.'
      }
    },
    {
      id: '6',
      name: 'Luxury Brand',
      headingFont: 'Cormorant Garamond',
      bodyFont: 'Lato',
      category: 'luxury',
      style: 'sophisticated',
      description: '奢华品牌风格，适合高端产品和服务',
      tags: ['luxury', 'sophisticated', 'premium', 'elegant'],
      popularity: 82,
      compatibility: ['web', 'branding'],
      previewText: {
        heading: 'Exceptional Craftsmanship',
        body: 'Every detail meticulously crafted to deliver an unparalleled experience that defines true luxury and sophistication.'
      }
    },
    {
      id: '7',
      name: 'Minimalist Clean',
      headingFont: 'Roboto',
      bodyFont: 'Roboto',
      category: 'minimal',
      style: 'clean',
      description: '极简清洁风格，适合现代应用和产品',
      tags: ['minimal', 'clean', 'simple', 'functional'],
      popularity: 90,
      compatibility: ['web', 'mobile', 'app', 'ui'],
      previewText: {
        heading: 'Less is More',
        body: 'Simplicity is the ultimate sophistication. Clean design focuses on what matters most, removing distractions to enhance user experience.'
      }
    },
    {
      id: '8',
      name: 'Vintage Retro',
      headingFont: 'Oswald',
      bodyFont: 'Noto Sans',
      category: 'vintage',
      style: 'retro',
      description: '复古怀旧风格，适合品牌故事和创意项目',
      tags: ['vintage', 'retro', 'nostalgic', 'character'],
      popularity: 75,
      compatibility: ['web', 'branding', 'creative'],
      previewText: {
        heading: 'Timeless Design',
        body: 'Drawing inspiration from classic design principles while adapting to modern sensibilities and contemporary needs.'
      }
    }
  ]

  const fontLibrary: FontLibrary[] = [
    {
      name: 'Inter',
      category: 'sans-serif',
      weights: ['300', '400', '500', '600', '700'],
      styles: ['normal', 'italic'],
      googleFont: true,
      webSafe: false,
      fallback: 'Arial, sans-serif'
    },
    {
      name: 'Playfair Display',
      category: 'serif',
      weights: ['400', '500', '600', '700', '800', '900'],
      styles: ['normal', 'italic'],
      googleFont: true,
      webSafe: false,
      fallback: 'Georgia, serif'
    },
    {
      name: 'Source Sans Pro',
      category: 'sans-serif',
      weights: ['300', '400', '600', '700'],
      styles: ['normal', 'italic'],
      googleFont: true,
      webSafe: false,
      fallback: 'Arial, sans-serif'
    },
    {
      name: 'Poppins',
      category: 'sans-serif',
      weights: ['300', '400', '500', '600', '700'],
      styles: ['normal'],
      googleFont: true,
      webSafe: false,
      fallback: 'Arial, sans-serif'
    },
    {
      name: 'Open Sans',
      category: 'sans-serif',
      weights: ['300', '400', '600', '700'],
      styles: ['normal', 'italic'],
      googleFont: true,
      webSafe: false,
      fallback: 'Arial, sans-serif'
    },
    {
      name: 'Montserrat',
      category: 'sans-serif',
      weights: ['300', '400', '500', '600', '700', '800'],
      styles: ['normal', 'italic'],
      googleFont: true,
      webSafe: false,
      fallback: 'Arial, sans-serif'
    },
    {
      name: 'Lato',
      category: 'sans-serif',
      weights: ['300', '400', '700'],
      styles: ['normal', 'italic'],
      googleFont: true,
      webSafe: false,
      fallback: 'Arial, sans-serif'
    },
    {
      name: 'Merriweather',
      category: 'serif',
      weights: ['300', '400', '700', '900'],
      styles: ['normal', 'italic'],
      googleFont: true,
      webSafe: false,
      fallback: 'Georgia, serif'
    },
    {
      name: 'Roboto',
      category: 'sans-serif',
      weights: ['300', '400', '500', '700'],
      styles: ['normal', 'italic'],
      googleFont: true,
      webSafe: false,
      fallback: 'Arial, sans-serif'
    },
    {
      name: 'Oswald',
      category: 'sans-serif',
      weights: ['300', '400', '500', '600', '700'],
      styles: ['normal'],
      googleFont: true,
      webSafe: false,
      fallback: 'Arial, sans-serif'
    },
    {
      name: 'Arial',
      category: 'sans-serif',
      weights: ['400', '700'],
      styles: ['normal', 'italic'],
      googleFont: false,
      webSafe: true,
      fallback: 'sans-serif'
    },
    {
      name: 'Georgia',
      category: 'serif',
      weights: ['400', '700'],
      styles: ['normal', 'italic'],
      googleFont: false,
      webSafe: true,
      fallback: 'serif'
    },
    {
      name: 'Times New Roman',
      category: 'serif',
      weights: ['400', '700'],
      styles: ['normal', 'italic'],
      googleFont: false,
      webSafe: true,
      fallback: 'serif'
    }
  ]

  const categories = [
    { value: 'all', label: '全部分类' },
    { value: 'business', label: '商务专业' },
    { value: 'editorial', label: '编辑出版' },
    { value: 'tech', label: '科技创新' },
    { value: 'creative', label: '创意设计' },
    { value: 'academic', label: '学术研究' },
    { value: 'luxury', label: '奢华品牌' },
    { value: 'minimal', label: '极简风格' },
    { value: 'vintage', label: '复古怀旧' }
  ]

  const styles = [
    { value: 'all', label: '全部风格' },
    { value: 'modern', label: '现代' },
    { value: 'elegant', label: '优雅' },
    { value: 'friendly', label: '友好' },
    { value: 'creative', label: '创意' },
    { value: 'traditional', label: '传统' },
    { value: 'sophisticated', label: '精致' },
    { value: 'clean', label: '简洁' },
    { value: 'retro', label: '复古' }
  ]

  const filteredPairs = useMemo(() => {
    return fontPairs.filter(pair => {
      const categoryMatch = filterCategory === 'all' || pair.category === filterCategory
      const styleMatch = filterStyle === 'all' || pair.style === filterStyle
      const googleFontMatch = !showOnlyGoogleFonts || (
        fontLibrary.find(f => f.name === pair.headingFont)?.googleFont &&
        fontLibrary.find(f => f.name === pair.bodyFont)?.googleFont
      )
      return categoryMatch && styleMatch && googleFontMatch
    })
  }, [filterCategory, filterStyle, showOnlyGoogleFonts])

  const selectedPair = useMemo(() => {
    return fontPairs.find(pair => pair.id === selectedPairId) || fontPairs[0]
  }, [selectedPairId])

  const getDeviceStyles = useCallback(() => {
    switch (previewDevice) {
      case 'mobile':
        return {
          container: { maxWidth: '375px', margin: '0 auto' },
          heading: { fontSize: '24px', lineHeight: '1.2' },
          body: { fontSize: '16px', lineHeight: '1.5' }
        }
      case 'tablet':
        return {
          container: { maxWidth: '768px', margin: '0 auto' },
          heading: { fontSize: '32px', lineHeight: '1.25' },
          body: { fontSize: '18px', lineHeight: '1.6' }
        }
      default:
        return {
          container: { width: '100%' },
          heading: { fontSize: '40px', lineHeight: '1.2' },
          body: { fontSize: '18px', lineHeight: '1.7' }
        }
    }
  }, [previewDevice])

  const generateGoogleFontsLink = useCallback((headingFont: string, bodyFont: string) => {
    const fonts = Array.from(new Set([headingFont, bodyFont]))
      .filter(font => fontLibrary.find(f => f.name === font)?.googleFont)
      .map(font => {
        const fontData = fontLibrary.find(f => f.name === font)
        const weights = fontData?.weights.join(',') || '400'
        const styles = fontData?.styles.includes('italic') ? ':ital,wght@0,400;1,400' : `:wght@${weights}`
        return `${font.replace(' ', '+')}${styles}`
      })

    return fonts.length > 0 ? `https://fonts.googleapis.com/css2?${fonts.map(f => `family=${f}`).join('&')}&display=swap` : ''
  }, [])

  const generateCSS = useCallback((headingFont: string, bodyFont: string) => {
    const headingFontData = fontLibrary.find(f => f.name === headingFont)
    const bodyFontData = fontLibrary.find(f => f.name === bodyFont)

    let css = '/* 字体配对 CSS */\n\n'

    // Google Fonts 导入
    const googleFontsLink = generateGoogleFontsLink(headingFont, bodyFont)
    if (googleFontsLink) {
      css += `@import url('${googleFontsLink}');\n\n`
    }

    // CSS 变量
    css += ':root {\n'
    css += `  --font-heading: "${headingFont}", ${headingFontData?.fallback || 'sans-serif'};\n`
    css += `  --font-body: "${bodyFont}", ${bodyFontData?.fallback || 'sans-serif'};\n`
    css += '}\n\n'

    // 基础样式
    css += '/* 标题样式 */\n'
    css += 'h1, h2, h3, h4, h5, h6 {\n'
    css += '  font-family: var(--font-heading);\n'
    css += '  font-weight: 600;\n'
    css += '  line-height: 1.2;\n'
    css += '  margin-bottom: 0.5em;\n'
    css += '}\n\n'

    css += '/* 正文样式 */\n'
    css += 'body, p {\n'
    css += '  font-family: var(--font-body);\n'
    css += '  font-weight: 400;\n'
    css += '  line-height: 1.6;\n'
    css += '  margin-bottom: 1em;\n'
    css += '}\n\n'

    // 响应式字体大小
    css += '/* 响应式字体大小 */\n'
    css += 'h1 { font-size: clamp(2rem, 5vw, 3rem); }\n'
    css += 'h2 { font-size: clamp(1.5rem, 4vw, 2.25rem); }\n'
    css += 'h3 { font-size: clamp(1.25rem, 3vw, 1.875rem); }\n'
    css += 'body, p { font-size: clamp(1rem, 2vw, 1.125rem); }\n'

    return css
  }, [generateGoogleFontsLink])

  const copyCSS = useCallback(() => {
    const headingFont = previewMode === 'custom' ? customHeadingFont : selectedPair.headingFont
    const bodyFont = previewMode === 'custom' ? customBodyFont : selectedPair.bodyFont
    const css = generateCSS(headingFont, bodyFont)
    
    navigator.clipboard.writeText(css)
    toast({
      title: '已复制',
      description: 'CSS代码已复制到剪贴板'
    })
  }, [previewMode, customHeadingFont, customBodyFont, selectedPair, generateCSS, toast])

  const downloadCSS = useCallback(() => {
    const headingFont = previewMode === 'custom' ? customHeadingFont : selectedPair.headingFont
    const bodyFont = previewMode === 'custom' ? customBodyFont : selectedPair.bodyFont
    const css = generateCSS(headingFont, bodyFont)
    
    const blob = new Blob([css], { type: 'text/css' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'font-pairing.css'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: '下载完成',
      description: 'CSS文件已下载'
    })
  }, [previewMode, customHeadingFont, customBodyFont, selectedPair, generateCSS, toast])

  const FontPreview: React.FC<{ headingFont: string; bodyFont: string; previewText: { heading: string; body: string } }> = ({ headingFont, bodyFont, previewText }) => {
    const deviceStyles = getDeviceStyles()
    
    return (
      <div style={deviceStyles.container} className="p-6 bg-white rounded-lg border">
        <h1 
          style={{
            fontFamily: `"${headingFont}", sans-serif`,
            fontWeight: 600,
            color: '#1f2937',
            marginBottom: '1rem',
            ...deviceStyles.heading
          }}
        >
          {previewText.heading}
        </h1>
        <p 
          style={{
            fontFamily: `"${bodyFont}", sans-serif`,
            fontWeight: 400,
            color: '#374151',
            marginBottom: 0,
            ...deviceStyles.body
          }}
        >
          {previewText.body}
        </p>
      </div>
    )
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Type className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">字体配对工具</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          专业的字体搭配推荐系统，帮助你选择完美的标题和正文字体组合
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 侧边栏 - 字体对列表 */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Settings className="w-4 h-4" />
                筛选选项
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-xs">分类</Label>
                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-xs">风格</Label>
                <Select value={filterStyle} onValueChange={setFilterStyle}>
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {styles.map(style => (
                      <SelectItem key={style.value} value={style.value}>
                        {style.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">仅Google字体</Label>
                <Switch 
                  checked={showOnlyGoogleFonts}
                  onCheckedChange={setShowOnlyGoogleFonts}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <Heart className="w-4 h-4" />
                推荐配对
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {filteredPairs.map((pair) => (
                  <div
                    key={pair.id}
                    onClick={() => setSelectedPairId(pair.id)}
                    className={`p-3 rounded-lg border cursor-pointer transition-all ${
                      selectedPairId === pair.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{pair.name}</h4>
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 text-yellow-500 fill-current" />
                        <span className="text-xs text-gray-500">{pair.popularity}</span>
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 mb-2">
                      <div style={{ fontFamily: `"${pair.headingFont}", sans-serif`, fontWeight: 600 }}>
                        {pair.headingFont}
                      </div>
                      <div style={{ fontFamily: `"${pair.bodyFont}", sans-serif` }}>
                        {pair.bodyFont}
                      </div>
                    </div>
                    <div className="flex gap-1 flex-wrap">
                      <Badge variant="secondary" className="text-xs">
                        {pair.category}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {pair.style}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 主内容区域 */}
        <div className="lg:col-span-3 space-y-6">
          {/* 预览模式切换 */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Eye className="w-4 h-4" />
                  字体预览
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant={previewMode === 'pairs' ? 'default' : 'outline'}
                    onClick={() => setPreviewMode('pairs')}
                    className="text-xs"
                  >
                    推荐配对
                  </Button>
                  <Button
                    size="sm"
                    variant={previewMode === 'custom' ? 'default' : 'outline'}
                    onClick={() => setPreviewMode('custom')}
                    className="text-xs"
                  >
                    自定义
                  </Button>
                  <Button
                    size="sm"
                    variant={previewMode === 'compare' ? 'default' : 'outline'}
                    onClick={() => setPreviewMode('compare')}
                    className="text-xs"
                  >
                    对比
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant={previewDevice === 'desktop' ? 'default' : 'outline'}
                    onClick={() => setPreviewDevice('desktop')}
                  >
                    <Monitor className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant={previewDevice === 'tablet' ? 'default' : 'outline'}
                    onClick={() => setPreviewDevice('tablet')}
                  >
                    <Tablet className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant={previewDevice === 'mobile' ? 'default' : 'outline'}
                    onClick={() => setPreviewDevice('mobile')}
                  >
                    <Smartphone className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex gap-2">
                  <Button onClick={copyCSS} size="sm" variant="outline">
                    <Copy className="w-4 h-4 mr-1" />
                    复制CSS
                  </Button>
                  <Button onClick={downloadCSS} size="sm">
                    <Download className="w-4 h-4 mr-1" />
                    下载
                  </Button>
                </div>
              </div>

              {previewMode === 'pairs' && (
                <div className="space-y-4">
                  <FontPreview
                    headingFont={selectedPair.headingFont}
                    bodyFont={selectedPair.bodyFont}
                    previewText={selectedPair.previewText}
                  />
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2">{selectedPair.name}</h4>
                    <p className="text-sm text-gray-600 mb-3">{selectedPair.description}</p>
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-medium">标题字体:</span>
                      <Badge variant="outline">{selectedPair.headingFont}</Badge>
                    </div>
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-sm font-medium">正文字体:</span>
                      <Badge variant="outline">{selectedPair.bodyFont}</Badge>
                    </div>
                    <div className="flex gap-1 flex-wrap">
                      {selectedPair.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {previewMode === 'custom' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm">标题字体</Label>
                      <Select value={customHeadingFont} onValueChange={setCustomHeadingFont}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {fontLibrary.map(font => (
                            <SelectItem key={font.name} value={font.name}>
                              <div className="flex items-center justify-between w-full">
                                <span style={{ fontFamily: `"${font.name}", ${font.fallback}` }}>
                                  {font.name}
                                </span>
                                {font.googleFont && (
                                  <Badge variant="outline" className="ml-2 text-xs">Google</Badge>
                                )}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm">正文字体</Label>
                      <Select value={customBodyFont} onValueChange={setCustomBodyFont}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {fontLibrary.map(font => (
                            <SelectItem key={font.name} value={font.name}>
                              <div className="flex items-center justify-between w-full">
                                <span style={{ fontFamily: `"${font.name}", ${font.fallback}` }}>
                                  {font.name}
                                </span>
                                {font.googleFont && (
                                  <Badge variant="outline" className="ml-2 text-xs">Google</Badge>
                                )}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <FontPreview
                    headingFont={customHeadingFont}
                    bodyFont={customBodyFont}
                    previewText={customPreviewText}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm">自定义标题文本</Label>
                      <Input
                        value={customPreviewText.heading}
                        onChange={(e) => setCustomPreviewText(prev => ({ ...prev, heading: e.target.value }))}
                        placeholder="输入标题文本"
                      />
                    </div>
                    <div>
                      <Label className="text-sm">自定义正文文本</Label>
                      <Textarea
                        value={customPreviewText.body}
                        onChange={(e) => setCustomPreviewText(prev => ({ ...prev, body: e.target.value }))}
                        placeholder="输入正文文本"
                        rows={2}
                      />
                    </div>
                  </div>
                </div>
              )}

              {previewMode === 'compare' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2 text-center">配对 A</h4>
                      <FontPreview
                        headingFont={selectedPair.headingFont}
                        bodyFont={selectedPair.bodyFont}
                        previewText={selectedPair.previewText}
                      />
                      <div className="text-center mt-2">
                        <Badge variant="outline">{selectedPair.headingFont}</Badge>
                        <span className="mx-2">+</span>
                        <Badge variant="outline">{selectedPair.bodyFont}</Badge>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2 text-center">配对 B</h4>
                      <FontPreview
                        headingFont={customHeadingFont}
                        bodyFont={customBodyFont}
                        previewText={customPreviewText}
                      />
                      <div className="text-center mt-2">
                        <Badge variant="outline">{customHeadingFont}</Badge>
                        <span className="mx-2">+</span>
                        <Badge variant="outline">{customBodyFont}</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* CSS代码展示 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <BookOpen className="w-4 h-4" />
                生成的CSS代码
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative">
                <Textarea
                  value={generateCSS(
                    previewMode === 'custom' ? customHeadingFont : selectedPair.headingFont,
                    previewMode === 'custom' ? customBodyFont : selectedPair.bodyFont
                  )}
                  readOnly
                  className="min-h-[300px] font-mono text-sm"
                />
                <Button
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={copyCSS}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default FontPairingTool