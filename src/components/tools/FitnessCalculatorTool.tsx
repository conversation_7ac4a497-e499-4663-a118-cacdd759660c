'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Heart, Activity, Calculator, Copy, RotateCcw } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

interface BMRResult {
  bmr: number
  tdee: number
  weightMaintenance: number
  weightLoss: {
    mild: number
    moderate: number
    aggressive: number
  }
  weightGain: {
    mild: number
    moderate: number
    aggressive: number
  }
}

interface CalorieResult {
  activity: string
  duration: number
  caloriesBurned: number
  perMinute: number
}

interface BodyCompositionResult {
  bmi: number
  bmiCategory: string
  idealWeight: {
    robinson: number
    miller: number
    devine: number
    hamwi: number
    healthy: { min: number; max: number }
  }
  bodyFat?: number
  leanMass?: number
}

export default function FitnessCalculatorTool() {
  const [activeTab, setActiveTab] = useState('bmr')
  
  // BMR/TDEE 计算
  const [age, setAge] = useState('')
  const [gender, setGender] = useState<'male' | 'female'>('male')
  const [weight, setWeight] = useState('')
  const [height, setHeight] = useState('')
  const [activityLevel, setActivityLevel] = useState('sedentary')
  const [bmrResult, setBmrResult] = useState<BMRResult | null>(null)
  
  // 卡路里消耗计算
  const [exerciseWeight, setExerciseWeight] = useState('')
  const [activityType, setActivityType] = useState('')
  const [duration, setDuration] = useState('')
  const [calorieResult, setCalorieResult] = useState<CalorieResult | null>(null)
  
  // 身体成分计算
  const [bodyWeight, setBodyWeight] = useState('')
  const [bodyHeight, setBodyHeight] = useState('')
  const [bodyAge, setBodyAge] = useState('')
  const [bodyGender, setBodyGender] = useState<'male' | 'female'>('male')
  const [neckCircumference, setNeckCircumference] = useState('')
  const [waistCircumference, setWaistCircumference] = useState('')
  const [hipCircumference, setHipCircumference] = useState('')
  const [bodyCompositionResult, setBodyCompositionResult] = useState<BodyCompositionResult | null>(null)

  const activityLevels = [
    { value: 'sedentary', label: '久坐（很少或没有运动）', multiplier: 1.2 },
    { value: 'light', label: '轻度活动（每周1-3天轻度运动）', multiplier: 1.375 },
    { value: 'moderate', label: '中度活动（每周3-5天中度运动）', multiplier: 1.55 },
    { value: 'active', label: '高度活动（每周6-7天高强度运动）', multiplier: 1.725 },
    { value: 'extra', label: '极度活动（每天2次运动或体力劳动）', multiplier: 1.9 },
  ]

  const activities = [
    { name: '步行（慢速，3.2km/h）', met: 2.3 },
    { name: '步行（中速，4.8km/h）', met: 3.5 },
    { name: '步行（快速，6.4km/h）', met: 5.0 },
    { name: '慢跑（8km/h）', met: 8.0 },
    { name: '跑步（10km/h）', met: 10.0 },
    { name: '跑步（12km/h）', met: 12.0 },
    { name: '骑自行车（休闲，16km/h）', met: 4.0 },
    { name: '骑自行车（中速，20km/h）', met: 8.0 },
    { name: '骑自行车（快速，25km/h）', met: 12.0 },
    { name: '游泳（自由泳，慢速）', met: 6.0 },
    { name: '游泳（自由泳，中速）', met: 8.0 },
    { name: '游泳（自由泳，快速）', met: 10.0 },
    { name: '举重训练', met: 6.0 },
    { name: '瑜伽', met: 2.5 },
    { name: '篮球', met: 8.0 },
    { name: '足球', met: 10.0 },
    { name: '网球', met: 7.0 },
    { name: '跳绳', met: 12.0 },
    { name: '登山', met: 8.0 },
    { name: '健身操', met: 7.0 },
  ]

  // 计算 BMR（基础代谢率）
  const calculateBMR = () => {
    if (!age || !weight || !height) {
      toast({
        title: "输入错误",
        description: "请填写所有必要信息",
        variant: "destructive"
      })
      return
    }

    const ageNum = parseInt(age)
    const weightNum = parseFloat(weight)
    const heightNum = parseFloat(height)

    // Mifflin-St Jeor 公式
    let bmr: number
    if (gender === 'male') {
      bmr = 88.362 + (13.397 * weightNum) + (4.799 * heightNum) - (5.677 * ageNum)
    } else {
      bmr = 447.593 + (9.247 * weightNum) + (3.098 * heightNum) - (4.330 * ageNum)
    }

    // 计算 TDEE（总日消耗）
    const selectedActivity = activityLevels.find(level => level.value === activityLevel)
    const tdee = bmr * (selectedActivity?.multiplier || 1.2)

    const result: BMRResult = {
      bmr: Math.round(bmr),
      tdee: Math.round(tdee),
      weightMaintenance: Math.round(tdee),
      weightLoss: {
        mild: Math.round(tdee - 250),      // 0.25kg/周
        moderate: Math.round(tdee - 500),   // 0.5kg/周
        aggressive: Math.round(tdee - 750), // 0.75kg/周
      },
      weightGain: {
        mild: Math.round(tdee + 250),      // 0.25kg/周
        moderate: Math.round(tdee + 500),   // 0.5kg/周
        aggressive: Math.round(tdee + 750), // 0.75kg/周
      }
    }

    setBmrResult(result)
  }

  // 计算卡路里消耗
  const calculateCalories = () => {
    if (!exerciseWeight || !activityType || !duration) {
      toast({
        title: "输入错误",
        description: "请填写所有必要信息",
        variant: "destructive"
      })
      return
    }

    const weightNum = parseFloat(exerciseWeight)
    const durationNum = parseFloat(duration)
    const selectedActivity = activities.find(activity => activity.name === activityType)

    if (!selectedActivity) {
      toast({
        title: "错误",
        description: "请选择有效的运动类型",
        variant: "destructive"
      })
      return
    }

    // 卡路里 = MET × 体重(kg) × 时间(小时)
    const caloriesBurned = selectedActivity.met * weightNum * (durationNum / 60)
    const perMinute = caloriesBurned / durationNum

    setCalorieResult({
      activity: selectedActivity.name,
      duration: durationNum,
      caloriesBurned: Math.round(caloriesBurned),
      perMinute: Math.round(perMinute * 10) / 10
    })
  }

  // 计算身体成分
  const calculateBodyComposition = () => {
    if (!bodyWeight || !bodyHeight || !bodyAge) {
      toast({
        title: "输入错误",
        description: "请填写基本信息",
        variant: "destructive"
      })
      return
    }

    const weightNum = parseFloat(bodyWeight)
    const heightNum = parseFloat(bodyHeight)

    // 计算 BMI
    const heightInMeters = heightNum / 100
    const bmi = weightNum / (heightInMeters * heightInMeters)

    // BMI 分类
    let bmiCategory = ''
    if (bmi < 18.5) bmiCategory = '体重过轻'
    else if (bmi < 24) bmiCategory = '正常体重'
    else if (bmi < 28) bmiCategory = '超重'
    else bmiCategory = '肥胖'

    // 计算理想体重（不同公式）
    const heightInCm = heightNum
    const idealWeight = {
      // Robinson 公式
      robinson: bodyGender === 'male' 
        ? 52 + 1.9 * ((heightInCm - 152.4) / 2.54)
        : 49 + 1.7 * ((heightInCm - 152.4) / 2.54),
      // Miller 公式
      miller: bodyGender === 'male'
        ? 56.2 + 1.41 * ((heightInCm - 152.4) / 2.54)
        : 53.1 + 1.36 * ((heightInCm - 152.4) / 2.54),
      // Devine 公式
      devine: bodyGender === 'male'
        ? 50 + 2.3 * ((heightInCm - 152.4) / 2.54)
        : 45.5 + 2.3 * ((heightInCm - 152.4) / 2.54),
      // Hamwi 公式
      hamwi: bodyGender === 'male'
        ? 48 + 2.7 * ((heightInCm - 152.4) / 2.54)
        : 45.5 + 2.2 * ((heightInCm - 152.4) / 2.54),
      // 健康体重范围（BMI 18.5-24）
      healthy: {
        min: 18.5 * heightInMeters * heightInMeters,
        max: 24 * heightInMeters * heightInMeters
      }
    }

    const result: BodyCompositionResult = {
      bmi: Math.round(bmi * 10) / 10,
      bmiCategory,
      idealWeight: {
        robinson: Math.round(idealWeight.robinson * 10) / 10,
        miller: Math.round(idealWeight.miller * 10) / 10,
        devine: Math.round(idealWeight.devine * 10) / 10,
        hamwi: Math.round(idealWeight.hamwi * 10) / 10,
        healthy: {
          min: Math.round(idealWeight.healthy.min * 10) / 10,
          max: Math.round(idealWeight.healthy.max * 10) / 10
        }
      }
    }

    // 如果提供了身体围度，计算体脂率
    if (neckCircumference && waistCircumference) {
      const neck = parseFloat(neckCircumference)
      const waist = parseFloat(waistCircumference)
      
      let bodyFat: number | undefined
      if (bodyGender === 'male') {
        // 男性体脂率公式
        bodyFat = 495 / (1.0324 - 0.19077 * Math.log10(waist - neck) + 0.15456 * Math.log10(heightNum)) - 450
      } else {
        // 女性需要臀围
        if (hipCircumference) {
          const hip = parseFloat(hipCircumference)
          bodyFat = 495 / (1.29579 - 0.35004 * Math.log10(waist + hip - neck) + 0.22100 * Math.log10(heightNum)) - 450
        } else {
          toast({
            title: "提示",
            description: "女性计算体脂率需要提供臀围",
            variant: "default"
          })
        }
      }

      if (bodyFat !== undefined && bodyFat > 0) {
        result.bodyFat = Math.round(bodyFat * 10) / 10
        result.leanMass = Math.round(weightNum * (1 - bodyFat / 100) * 10) / 10
      }
    }

    setBodyCompositionResult(result)
  }

  // 复制结果
  const copyResult = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "已复制",
      description: "结果已复制到剪贴板",
    })
  }

  // 清空所有输入
  const clearAll = () => {
    setAge('')
    setWeight('')
    setHeight('')
    setExerciseWeight('')
    setDuration('')
    setBodyWeight('')
    setBodyHeight('')
    setBodyAge('')
    setNeckCircumference('')
    setWaistCircumference('')
    setHipCircumference('')
    setBmrResult(null)
    setCalorieResult(null)
    setBodyCompositionResult(null)
  }

  const getBMIColor = (bmi: number) => {
    if (bmi < 18.5) return 'text-blue-600'
    if (bmi < 24) return 'text-green-600'
    if (bmi < 28) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Heart className="h-6 w-6" />
              <CardTitle>健身计算器</CardTitle>
            </div>
            <Button variant="outline" size="sm" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-1" />
              清空
            </Button>
          </div>
          <CardDescription>
            全面的健身计算工具，包括基础代谢率、卡路里消耗、身体成分分析
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="bmr">基础代谢率</TabsTrigger>
              <TabsTrigger value="calories">卡路里消耗</TabsTrigger>
              <TabsTrigger value="body">身体成分</TabsTrigger>
            </TabsList>

            <TabsContent value="bmr" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="age">年龄</Label>
                      <Input
                        id="age"
                        type="number"
                        value={age}
                        onChange={(e) => setAge(e.target.value)}
                        placeholder="25"
                      />
                    </div>
                    <div>
                      <Label>性别</Label>
                      <Select value={gender} onValueChange={(value: 'male' | 'female') => setGender(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">男性</SelectItem>
                          <SelectItem value="female">女性</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="weight">体重 (kg)</Label>
                      <Input
                        id="weight"
                        type="number"
                        value={weight}
                        onChange={(e) => setWeight(e.target.value)}
                        placeholder="70"
                      />
                    </div>
                    <div>
                      <Label htmlFor="height">身高 (cm)</Label>
                      <Input
                        id="height"
                        type="number"
                        value={height}
                        onChange={(e) => setHeight(e.target.value)}
                        placeholder="175"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label>活动水平</Label>
                    <Select value={activityLevel} onValueChange={setActivityLevel}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {activityLevels.map((level) => (
                          <SelectItem key={level.value} value={level.value}>
                            {level.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Button onClick={calculateBMR} className="w-full">
                <Calculator className="h-4 w-4 mr-2" />
                计算基础代谢率
              </Button>

              {bmrResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">代谢率计算结果</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{bmrResult.bmr}</div>
                        <div className="text-sm text-gray-600">基础代谢率 (BMR)</div>
                        <div className="text-xs text-gray-500">静息状态下的最低热量需求</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">{bmrResult.tdee}</div>
                        <div className="text-sm text-gray-600">总日消耗 (TDEE)</div>
                        <div className="text-xs text-gray-500">包含活动的总热量消耗</div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-semibold">每日热量摄入建议</h4>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-4 border rounded-lg">
                          <h5 className="font-medium text-red-600 mb-2">减重目标</h5>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>轻度减重 (0.25kg/周):</span>
                              <span className="font-medium">{bmrResult.weightLoss.mild} 卡</span>
                            </div>
                            <div className="flex justify-between">
                              <span>中度减重 (0.5kg/周):</span>
                              <span className="font-medium">{bmrResult.weightLoss.moderate} 卡</span>
                            </div>
                            <div className="flex justify-between">
                              <span>快速减重 (0.75kg/周):</span>
                              <span className="font-medium">{bmrResult.weightLoss.aggressive} 卡</span>
                            </div>
                          </div>
                        </div>

                        <div className="p-4 border rounded-lg">
                          <h5 className="font-medium text-gray-600 mb-2">维持体重</h5>
                          <div className="text-center">
                            <div className="text-xl font-bold">{bmrResult.weightMaintenance} 卡</div>
                            <div className="text-xs text-gray-500">保持当前体重的热量摄入</div>
                          </div>
                        </div>

                        <div className="p-4 border rounded-lg">
                          <h5 className="font-medium text-blue-600 mb-2">增重目标</h5>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>轻度增重 (0.25kg/周):</span>
                              <span className="font-medium">{bmrResult.weightGain.mild} 卡</span>
                            </div>
                            <div className="flex justify-between">
                              <span>中度增重 (0.5kg/周):</span>
                              <span className="font-medium">{bmrResult.weightGain.moderate} 卡</span>
                            </div>
                            <div className="flex justify-between">
                              <span>快速增重 (0.75kg/周):</span>
                              <span className="font-medium">{bmrResult.weightGain.aggressive} 卡</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResult(`BMR: ${bmrResult.bmr}卡，TDEE: ${bmrResult.tdee}卡，维持体重: ${bmrResult.weightMaintenance}卡`)}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制结果
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="calories" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="exerciseWeight">体重 (kg)</Label>
                    <Input
                      id="exerciseWeight"
                      type="number"
                      value={exerciseWeight}
                      onChange={(e) => setExerciseWeight(e.target.value)}
                      placeholder="70"
                    />
                  </div>

                  <div>
                    <Label htmlFor="duration">运动时长 (分钟)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={duration}
                      onChange={(e) => setDuration(e.target.value)}
                      placeholder="30"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label>运动类型</Label>
                    <Select value={activityType} onValueChange={setActivityType}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择运动类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {activities.map((activity) => (
                          <SelectItem key={activity.name} value={activity.name}>
                            {activity.name} (MET: {activity.met})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Button onClick={calculateCalories} className="w-full">
                <Activity className="h-4 w-4 mr-2" />
                计算卡路里消耗
              </Button>

              {calorieResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">卡路里消耗结果</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">{calorieResult.caloriesBurned}</div>
                        <div className="text-sm text-gray-600">总消耗 (卡路里)</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">{calorieResult.perMinute}</div>
                        <div className="text-sm text-gray-600">每分钟消耗</div>
                      </div>
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{calorieResult.duration}</div>
                        <div className="text-sm text-gray-600">运动时长 (分钟)</div>
                      </div>
                    </div>

                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <div className="text-sm">
                        <span className="font-medium">运动类型：</span>
                        <span>{calorieResult.activity}</span>
                      </div>
                    </div>

                    <div className="flex justify-end mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResult(`${calorieResult.activity} ${calorieResult.duration}分钟，消耗 ${calorieResult.caloriesBurned} 卡路里`)}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制结果
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="body" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">基本信息</h4>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="bodyAge">年龄</Label>
                      <Input
                        id="bodyAge"
                        type="number"
                        value={bodyAge}
                        onChange={(e) => setBodyAge(e.target.value)}
                        placeholder="25"
                      />
                    </div>
                    <div>
                      <Label>性别</Label>
                      <Select value={bodyGender} onValueChange={(value: 'male' | 'female') => setBodyGender(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">男性</SelectItem>
                          <SelectItem value="female">女性</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="bodyWeight">体重 (kg)</Label>
                      <Input
                        id="bodyWeight"
                        type="number"
                        value={bodyWeight}
                        onChange={(e) => setBodyWeight(e.target.value)}
                        placeholder="70"
                      />
                    </div>
                    <div>
                      <Label htmlFor="bodyHeight">身高 (cm)</Label>
                      <Input
                        id="bodyHeight"
                        type="number"
                        value={bodyHeight}
                        onChange={(e) => setBodyHeight(e.target.value)}
                        placeholder="175"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">身体围度（可选，用于体脂率计算）</h4>
                  
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <Label htmlFor="neckCircumference">颈围 (cm)</Label>
                      <Input
                        id="neckCircumference"
                        type="number"
                        value={neckCircumference}
                        onChange={(e) => setNeckCircumference(e.target.value)}
                        placeholder="36"
                      />
                    </div>
                    <div>
                      <Label htmlFor="waistCircumference">腰围 (cm)</Label>
                      <Input
                        id="waistCircumference"
                        type="number"
                        value={waistCircumference}
                        onChange={(e) => setWaistCircumference(e.target.value)}
                        placeholder="80"
                      />
                    </div>
                    {bodyGender === 'female' && (
                      <div>
                        <Label htmlFor="hipCircumference">臀围 (cm)</Label>
                        <Input
                          id="hipCircumference"
                          type="number"
                          value={hipCircumference}
                          onChange={(e) => setHipCircumference(e.target.value)}
                          placeholder="95"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <Button onClick={calculateBodyComposition} className="w-full">
                <Calculator className="h-4 w-4 mr-2" />
                计算身体成分
              </Button>

              {bodyCompositionResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">身体成分分析结果</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h4 className="font-semibold">BMI 指数</h4>
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className={`text-3xl font-bold ${getBMIColor(bodyCompositionResult.bmi)}`}>
                            {bodyCompositionResult.bmi}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {bodyCompositionResult.bmiCategory}
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h4 className="font-semibold">健康体重范围</h4>
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                          <div className="text-lg font-bold text-green-600">
                            {bodyCompositionResult.idealWeight.healthy.min} - {bodyCompositionResult.idealWeight.healthy.max} kg
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            基于 BMI 18.5-24
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-semibold">理想体重计算（不同公式）</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-3 border rounded-lg">
                          <div className="font-bold text-blue-600">{bodyCompositionResult.idealWeight.robinson} kg</div>
                          <div className="text-xs text-gray-600">Robinson 公式</div>
                        </div>
                        <div className="text-center p-3 border rounded-lg">
                          <div className="font-bold text-green-600">{bodyCompositionResult.idealWeight.miller} kg</div>
                          <div className="text-xs text-gray-600">Miller 公式</div>
                        </div>
                        <div className="text-center p-3 border rounded-lg">
                          <div className="font-bold text-purple-600">{bodyCompositionResult.idealWeight.devine} kg</div>
                          <div className="text-xs text-gray-600">Devine 公式</div>
                        </div>
                        <div className="text-center p-3 border rounded-lg">
                          <div className="font-bold text-orange-600">{bodyCompositionResult.idealWeight.hamwi} kg</div>
                          <div className="text-xs text-gray-600">Hamwi 公式</div>
                        </div>
                      </div>
                    </div>

                    {bodyCompositionResult.bodyFat && (
                      <div className="space-y-4">
                        <h4 className="font-semibold">体脂率分析</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="text-center p-4 bg-yellow-50 rounded-lg">
                            <div className="text-2xl font-bold text-yellow-600">
                              {bodyCompositionResult.bodyFat}%
                            </div>
                            <div className="text-sm text-gray-600">体脂率</div>
                          </div>
                          <div className="text-center p-4 bg-blue-50 rounded-lg">
                            <div className="text-2xl font-bold text-blue-600">
                              {bodyCompositionResult.leanMass} kg
                            </div>
                            <div className="text-sm text-gray-600">瘦体重</div>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResult(`BMI: ${bodyCompositionResult.bmi} (${bodyCompositionResult.bmiCategory})，健康体重: ${bodyCompositionResult.idealWeight.healthy.min}-${bodyCompositionResult.idealWeight.healthy.max}kg`)}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制结果
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
