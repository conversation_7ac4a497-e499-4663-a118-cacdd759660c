'use client'

import { useState } from 'react'
import { Code2, Copy, Check, AlertCircle, Info, Minimize2, Maximize2, FileCode } from 'lucide-react'

interface FormatOptions {
  uppercase: boolean
  indent: string
  linesBetweenQueries: number
}

const sqlKeywords = [
  'SELECT', 'FROM', 'WHERE', 'AND', 'OR', 'NOT', 'IN', 'LIKE', 'BETWEEN',
  'INSERT', 'INTO', 'VALUES', 'UPDATE', 'SET', 'DELETE',
  'CREATE', 'TABLE', 'DATABASE', 'INDEX', 'VIEW', 'PROCEDURE', 'FUNCTION',
  'ALTER', 'DROP', 'TRUNCATE', 'GRANT', 'REVOKE',
  'JOIN', 'INNER', 'LEFT', 'RIGHT', 'FULL', 'OUTER', 'CROSS', 'ON',
  'GROUP', 'BY', 'HAVING', 'ORDER', 'ASC', 'DESC',
  'UNION', 'ALL', 'EXISTS', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END',
  'AS', 'DISTINCT', 'COUNT', 'SUM', 'AVG', 'MAX', 'MIN',
  'PRIMARY', 'KEY', 'FOREIGN', 'REFERENCES', 'UNIQUE', 'DEFAULT',
  'NULL', 'NOT NULL', 'AUTO_INCREMENT', 'CONSTRAINT',
  'BEGIN', 'COMMIT', 'ROLLBACK', 'TRANSACTION',
  'IF', 'WHILE', 'FOR', 'LOOP', 'DECLARE', 'RETURN',
  'LIMIT', 'OFFSET', 'FETCH', 'FIRST', 'NEXT', 'ROWS', 'ONLY'
]

const sqlFunctions = [
  'COUNT', 'SUM', 'AVG', 'MAX', 'MIN', 'ROUND', 'FLOOR', 'CEIL',
  'CONCAT', 'SUBSTRING', 'LENGTH', 'UPPER', 'LOWER', 'TRIM',
  'NOW', 'CURRENT_DATE', 'CURRENT_TIME', 'DATE_FORMAT', 'DATEDIFF',
  'CAST', 'CONVERT', 'COALESCE', 'NULLIF', 'IFNULL'
]

const dataTypes = [
  'INT', 'INTEGER', 'BIGINT', 'SMALLINT', 'TINYINT',
  'DECIMAL', 'NUMERIC', 'FLOAT', 'DOUBLE', 'REAL',
  'CHAR', 'VARCHAR', 'TEXT', 'LONGTEXT', 'MEDIUMTEXT', 'TINYTEXT',
  'DATE', 'TIME', 'DATETIME', 'TIMESTAMP', 'YEAR',
  'BOOLEAN', 'BOOL', 'BIT', 'BINARY', 'VARBINARY', 'BLOB',
  'JSON', 'ENUM', 'SET'
]

export default function SqlFormatterTool() {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState('')
  const [options, setOptions] = useState<FormatOptions>({
    uppercase: true,
    indent: '  ',
    linesBetweenQueries: 1
  })

  // 格式化 SQL
  const formatSql = () => {
    if (!input.trim()) {
      setError('请输入 SQL 语句')
      return
    }

    setError('')
    
    try {
      let formatted = input
      
      // 1. 标准化空白字符
      formatted = formatted.replace(/\s+/g, ' ').trim()
      
      // 2. 在关键字前后添加换行
      const keywords = ['SELECT', 'FROM', 'WHERE', 'GROUP BY', 'ORDER BY', 'HAVING', 
                       'INSERT INTO', 'VALUES', 'UPDATE', 'SET', 'DELETE FROM',
                       'CREATE TABLE', 'ALTER TABLE', 'DROP TABLE',
                       'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'FULL JOIN', 'CROSS JOIN',
                       'UNION', 'UNION ALL']
      
      keywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
        formatted = formatted.replace(regex, `\n${keyword}`)
      })
      
      // 3. 处理逗号后的换行
      formatted = formatted.replace(/,\s*/g, ',\n' + options.indent)
      
      // 4. 处理 AND/OR
      formatted = formatted.replace(/\s+(AND|OR)\s+/gi, '\n' + options.indent + '$1 ')
      
      // 5. 处理括号
      formatted = formatted.replace(/\(\s*/g, '(\n' + options.indent)
      formatted = formatted.replace(/\s*\)/g, '\n)')
      
      // 6. 处理大小写
      if (options.uppercase) {
        // 将所有关键字转换为大写
        [...sqlKeywords, ...sqlFunctions, ...dataTypes].forEach(keyword => {
          const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
          formatted = formatted.replace(regex, keyword)
        })
      }
      
      // 7. 清理多余的空行和缩进
      const lines = formatted.split('\n')
      const cleanedLines: string[] = []
      let indentLevel = 0
      
      lines.forEach(line => {
        const trimmedLine = line.trim()
        if (!trimmedLine) return
        
        // 调整缩进级别
        if (trimmedLine.startsWith(')')) {
          indentLevel = Math.max(0, indentLevel - 1)
        }
        
        // 添加适当的缩进
        if (trimmedLine.match(/^(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)/i)) {
          cleanedLines.push(trimmedLine)
          indentLevel = 1
        } else if (trimmedLine.match(/^(FROM|WHERE|GROUP BY|ORDER BY|HAVING|SET|VALUES)/i)) {
          cleanedLines.push(trimmedLine)
          indentLevel = 1
        } else if (trimmedLine.match(/^(INNER JOIN|LEFT JOIN|RIGHT JOIN|FULL JOIN|CROSS JOIN)/i)) {
          cleanedLines.push(options.indent + trimmedLine)
        } else if (trimmedLine.match(/^(AND|OR)/i)) {
          cleanedLines.push(options.indent + trimmedLine)
        } else if (indentLevel > 0 && !trimmedLine.startsWith(')')) {
          cleanedLines.push(options.indent.repeat(indentLevel) + trimmedLine)
        } else {
          cleanedLines.push(trimmedLine)
        }
        
        // 更新缩进级别
        if (trimmedLine.endsWith('(')) {
          indentLevel++
        }
      })
      
      // 8. 分隔多个查询
      formatted = cleanedLines.join('\n')
      formatted = formatted.replace(/;\s*\n/g, ';\n' + '\n'.repeat(options.linesBetweenQueries))
      
      setOutput(formatted)
    } catch (err) {
      setError('格式化失败，请检查 SQL 语法')
      setOutput('')
    }
  }

  // 压缩 SQL
  const minifySql = () => {
    if (!input.trim()) {
      setError('请输入 SQL 语句')
      return
    }

    setError('')
    
    try {
      // 移除注释
      let minified = input
        .replace(/--.*$/gm, '') // 移除单行注释
        .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
      
      // 压缩空白字符
      minified = minified
        .replace(/\s+/g, ' ')
        .replace(/\s*([,;()])\s*/g, '$1')
        .replace(/\s*([=<>!]+)\s*/g, '$1')
        .trim()
      
      setOutput(minified)
    } catch (err) {
      setError('压缩失败')
      setOutput('')
    }
  }

  // 复制结果
  const copyToClipboard = async () => {
    if (!output) return
    
    try {
      await navigator.clipboard.writeText(output)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 加载示例
  const loadExample = () => {
    const example = `SELECT u.id, u.name, u.email, COUNT(o.id) as order_count, SUM(o.total_amount) as total_spent
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.created_at >= '2024-01-01' AND u.status = 'active'
GROUP BY u.id, u.name, u.email
HAVING COUNT(o.id) > 0
ORDER BY total_spent DESC
LIMIT 10;

INSERT INTO products (name, price, category_id, description)
VALUES ('Laptop', 999.99, 1, 'High-performance laptop'),
       ('Mouse', 29.99, 2, 'Wireless mouse'),
       ('Keyboard', 79.99, 2, 'Mechanical keyboard');`
    
    setInput(example)
    setOutput('')
    setError('')
  }

  // 清空内容
  const clearAll = () => {
    setInput('')
    setOutput('')
    setError('')
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">SQL 格式化工具</h2>
        <p className="text-gray-600">
          格式化、美化和压缩 SQL 查询语句
        </p>
      </div>

      {/* 选项设置 */}
      <div className="mb-6 bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="font-medium mb-4">格式化选项</h3>
        <div className="grid md:grid-cols-3 gap-4">
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={options.uppercase}
                onChange={(e) => setOptions({ ...options, uppercase: e.target.checked })}
                className="rounded"
              />
              <span className="text-sm">关键字大写</span>
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">缩进</label>
            <select
              value={options.indent}
              onChange={(e) => setOptions({ ...options, indent: e.target.value })}
              className="w-full px-3 py-1 text-sm border border-gray-300 rounded-md"
            >
              <option value="  ">2 空格</option>
              <option value="    ">4 空格</option>
              <option value="\t">Tab</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">查询间空行</label>
            <select
              value={options.linesBetweenQueries}
              onChange={(e) => setOptions({ ...options, linesBetweenQueries: parseInt(e.target.value) })}
              className="w-full px-3 py-1 text-sm border border-gray-300 rounded-md"
            >
              <option value="1">1 行</option>
              <option value="2">2 行</option>
              <option value="3">3 行</option>
            </select>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="mb-4 flex items-center gap-4">
        <button
          onClick={formatSql}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors flex items-center gap-2"
        >
          <Maximize2 className="w-4 h-4" />
          格式化
        </button>
        <button
          onClick={minifySql}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors flex items-center gap-2"
        >
          <Minimize2 className="w-4 h-4" />
          压缩
        </button>
        <button
          onClick={loadExample}
          className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
        >
          加载示例
        </button>
        <button
          onClick={clearAll}
          className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
        >
          清空
        </button>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">输入 SQL</label>
            <span className="text-xs text-gray-500">{input.length} 字符</span>
          </div>
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="在此输入 SQL 语句..."
            className="w-full h-96 p-4 font-mono text-sm border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            spellCheck={false}
          />
        </div>

        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">格式化结果</label>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">{output.length} 字符</span>
              <button
                onClick={copyToClipboard}
                disabled={!output}
                className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    复制
                  </>
                )}
              </button>
            </div>
          </div>
          <textarea
            value={output}
            readOnly
            placeholder="格式化结果将显示在这里..."
            className="w-full h-96 p-4 font-mono text-sm bg-gray-50 border border-gray-300 rounded-lg resize-none"
            spellCheck={false}
          />
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-800">
          <AlertCircle className="w-4 h-4" />
          {error}
        </div>
      )}

      {/* SQL 语法参考 */}
      <div className="mt-8 grid md:grid-cols-2 gap-6">
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <FileCode className="w-4 h-4" />
            常用 SQL 语句
          </h4>
          <div className="space-y-2 text-sm">
            <div>
              <p className="font-medium text-gray-700">查询数据</p>
              <code className="text-xs bg-white px-2 py-1 rounded">
                SELECT column1, column2 FROM table WHERE condition
              </code>
            </div>
            <div>
              <p className="font-medium text-gray-700">插入数据</p>
              <code className="text-xs bg-white px-2 py-1 rounded">
                INSERT INTO table (col1, col2) VALUES (val1, val2)
              </code>
            </div>
            <div>
              <p className="font-medium text-gray-700">更新数据</p>
              <code className="text-xs bg-white px-2 py-1 rounded">
                UPDATE table SET col1 = val1 WHERE condition
              </code>
            </div>
            <div>
              <p className="font-medium text-gray-700">删除数据</p>
              <code className="text-xs bg-white px-2 py-1 rounded">
                DELETE FROM table WHERE condition
              </code>
            </div>
          </div>
        </div>

        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Code2 className="w-4 h-4" />
            JOIN 类型
          </h4>
          <div className="space-y-2 text-sm">
            <div>
              <p className="font-medium text-gray-700">INNER JOIN</p>
              <p className="text-xs text-gray-600">返回两表中匹配的记录</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">LEFT JOIN</p>
              <p className="text-xs text-gray-600">返回左表所有记录，右表匹配的记录</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">RIGHT JOIN</p>
              <p className="text-xs text-gray-600">返回右表所有记录，左表匹配的记录</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">FULL JOIN</p>
              <p className="text-xs text-gray-600">返回两表中所有记录</p>
            </div>
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持标准 SQL 语法格式化</li>
              <li>自动识别和高亮 SQL 关键字</li>
              <li>支持多条 SQL 语句同时格式化</li>
              <li>可选择关键字大写、缩进方式等选项</li>
              <li>压缩功能可移除多余空白和注释</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
