'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Upload, FileText, Copy, Download, Loader2, AlertCircle, CheckCircle2, Languages, Image as ImageIcon } from 'lucide-react'
import { useTranslations } from '@/hooks/useTranslations'

interface OCRResult {
  text: string
  confidence: number
  language: string
}

export default function OCRTool() {
  const translate = useTranslations()
  const [files, setFiles] = useState<File[]>([])
  const [results, setResults] = useState<Map<string, OCRResult>>(new Map())
  const [processing, setProcessing] = useState<Set<string>>(new Set())
  const [language, setLanguage] = useState('auto')
  const [outputFormat, setOutputFormat] = useState('text')
  const [error, setError] = useState<string>('')

  // 简单的多语言支持函数
  const t = useCallback((zh: string, en: string, ja: string) => {
    // 这里暂时返回中文，实际应该根据当前语言环境返回对应的文本
    return zh
  }, [])

  const languages = [
    { value: 'auto', label: t('自动检测', 'Auto Detect', '自動検出') },
    { value: 'zh-CN', label: t('简体中文', 'Simplified Chinese', '簡体中国語') },
    { value: 'zh-TW', label: t('繁体中文', 'Traditional Chinese', '繁体中国語') },
    { value: 'en', label: t('英文', 'English', '英語') },
    { value: 'ja', label: t('日文', 'Japanese', '日本語') },
    { value: 'ko', label: t('韩文', 'Korean', '韓国語') },
    { value: 'es', label: t('西班牙文', 'Spanish', 'スペイン語') },
    { value: 'fr', label: t('法文', 'French', 'フランス語') },
    { value: 'de', label: t('德文', 'German', 'ドイツ語') },
    { value: 'ru', label: t('俄文', 'Russian', 'ロシア語') },
    { value: 'ar', label: t('阿拉伯文', 'Arabic', 'アラビア語') }
  ]

  const outputFormats = [
    { value: 'text', label: t('纯文本', 'Plain Text', 'プレーンテキスト') },
    { value: 'json', label: t('JSON格式', 'JSON Format', 'JSON形式') },
    { value: 'markdown', label: t('Markdown格式', 'Markdown Format', 'Markdown形式') },
    { value: 'html', label: t('HTML格式', 'HTML Format', 'HTML形式') }
  ]

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || [])
    const imageFiles = selectedFiles.filter(file => 
      file.type.startsWith('image/') || file.type === 'application/pdf'
    )
    
    if (imageFiles.length !== selectedFiles.length) {
      setError(t(
        '只支持图片和PDF文件',
        'Only image and PDF files are supported',
        '画像とPDFファイルのみサポートされています'
      ))
    }
    
    setFiles(prev => [...prev, ...imageFiles])
    setError('')
  }, [t])

  const simulateOCR = async (file: File): Promise<OCRResult> => {
    // 模拟OCR处理
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 2000))
    
    // 模拟OCR结果
    const sampleTexts = {
      'zh-CN': '这是一个示例文本。OCR技术可以将图片中的文字转换为可编辑的文本格式。',
      'en': 'This is a sample text. OCR technology can convert text in images into editable text format.',
      'ja': 'これはサンプルテキストです。OCR技術は画像内のテキストを編集可能なテキスト形式に変換できます。'
    }
    
    const detectedLang = language === 'auto' ? 'zh-CN' : language
    const text = sampleTexts[detectedLang as keyof typeof sampleTexts] || sampleTexts['en']
    
    return {
      text,
      confidence: 0.85 + Math.random() * 0.14,
      language: detectedLang
    }
  }

  const processFile = async (file: File) => {
    const fileName = file.name
    setProcessing(prev => new Set(prev).add(fileName))
    
    try {
      const result = await simulateOCR(file)
      setResults(prev => new Map(prev).set(fileName, result))
    } catch (err) {
      setError(t(
        `处理 ${fileName} 时出错`,
        `Error processing ${fileName}`,
        `${fileName} の処理中にエラーが発生しました`
      ))
    } finally {
      setProcessing(prev => {
        const newSet = new Set(prev)
        newSet.delete(fileName)
        return newSet
      })
    }
  }

  const handleProcess = () => {
    files.forEach(file => {
      if (!results.has(file.name) && !processing.has(file.name)) {
        processFile(file)
      }
    })
  }

  const formatOutput = (result: OCRResult): string => {
    switch (outputFormat) {
      case 'json':
        return JSON.stringify({
          text: result.text,
          confidence: result.confidence,
          language: result.language
        }, null, 2)
      case 'markdown':
        return `# OCR Result\n\n**Language:** ${result.language}\n**Confidence:** ${(result.confidence * 100).toFixed(1)}%\n\n## Text\n\n${result.text}`
      case 'html':
        return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>OCR Result</title>
</head>
<body>
  <h1>OCR Result</h1>
  <p><strong>Language:</strong> ${result.language}</p>
  <p><strong>Confidence:</strong> ${(result.confidence * 100).toFixed(1)}%</p>
  <h2>Text</h2>
  <p>${result.text.replace(/\n/g, '<br>')}</p>
</body>
</html>`
      default:
        return result.text
    }
  }

  const getAllText = (): string => {
    const allResults = Array.from(results.values())
    if (outputFormat === 'json') {
      return JSON.stringify(allResults.map(r => ({
        text: r.text,
        confidence: r.confidence,
        language: r.language
      })), null, 2)
    }
    return allResults.map(r => formatOutput(r)).join('\n\n---\n\n')
  }

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const handleDownload = (text: string, filename: string) => {
    const extension = outputFormat === 'json' ? 'json' : 
                     outputFormat === 'markdown' ? 'md' :
                     outputFormat === 'html' ? 'html' : 'txt'
    const blob = new Blob([text], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}.${extension}`
    a.click()
    URL.revokeObjectURL(url)
  }

  const removeFile = (fileName: string) => {
    setFiles(prev => prev.filter(f => f.name !== fileName))
    setResults(prev => {
      const newMap = new Map(prev)
      newMap.delete(fileName)
      return newMap
    })
  }

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {t('文档扫描OCR', 'Document OCR Scanner', 'ドキュメントOCRスキャナー')}
        </CardTitle>
        <CardDescription>
          {t(
            '从图片和PDF中提取文字，支持多语言识别',
            'Extract text from images and PDFs with multi-language support',
            '画像とPDFからテキストを抽出、多言語認識対応'
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 文件上传区域 */}
        <div className="space-y-4">
          <Label>{t('选择文件', 'Select Files', 'ファイルを選択')}</Label>
          <div className="border-2 border-dashed rounded-lg p-8 text-center">
            <input
              type="file"
              multiple
              accept="image/*,.pdf"
              onChange={handleFileSelect}
              className="hidden"
              id="file-upload"
            />
            <label htmlFor="file-upload" className="cursor-pointer">
              <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">
                {t(
                  '点击或拖拽文件到此处',
                  'Click or drag files here',
                  'クリックまたはファイルをドラッグ'
                )}
              </p>
              <p className="text-xs text-muted-foreground mt-2">
                {t(
                  '支持 JPG、PNG、PDF 等格式',
                  'Support JPG, PNG, PDF formats',
                  'JPG、PNG、PDF形式をサポート'
                )}
              </p>
            </label>
          </div>
        </div>

        {/* 设置选项 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>{t('识别语言', 'Recognition Language', '認識言語')}</Label>
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {languages.map(lang => (
                  <SelectItem key={lang.value} value={lang.value}>
                    <div className="flex items-center gap-2">
                      <Languages className="h-4 w-4" />
                      {lang.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>{t('输出格式', 'Output Format', '出力形式')}</Label>
            <Select value={outputFormat} onValueChange={setOutputFormat}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {outputFormats.map(format => (
                  <SelectItem key={format.value} value={format.value}>
                    {format.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 文件列表 */}
        {files.length > 0 && (
          <div className="space-y-2">
            <Label>{t('待处理文件', 'Files to Process', '処理待ちファイル')}</Label>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {files.map(file => (
                <div key={file.name} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex items-center gap-3">
                    <ImageIcon className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">{file.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {(file.size / 1024).toFixed(1)} KB
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {processing.has(file.name) && (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    )}
                    {results.has(file.name) && (
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(file.name)}
                    >
                      {t('移除', 'Remove', '削除')}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 处理按钮 */}
        {files.length > 0 && (
          <Button 
            onClick={handleProcess} 
            disabled={processing.size > 0}
            className="w-full"
          >
            {processing.size > 0 ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('处理中...', 'Processing...', '処理中...')}
              </>
            ) : (
              t('开始识别', 'Start Recognition', '認識開始')
            )}
          </Button>
        )}

        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* 识别结果 */}
        {results.size > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>{t('识别结果', 'Recognition Results', '認識結果')}</Label>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleCopy(getAllText())}
                >
                  <Copy className="mr-2 h-4 w-4" />
                  {t('复制全部', 'Copy All', 'すべてコピー')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownload(getAllText(), 'ocr-results')}
                >
                  <Download className="mr-2 h-4 w-4" />
                  {t('下载全部', 'Download All', 'すべてダウンロード')}
                </Button>
              </div>
            </div>

            {Array.from(results.entries()).map(([fileName, result]) => (
              <div key={fileName} className="space-y-2 p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{fileName}</h4>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{t('语言', 'Language', '言語')}: {result.language}</span>
                    <span>{t('置信度', 'Confidence', '信頼度')}: {(result.confidence * 100).toFixed(1)}%</span>
                  </div>
                </div>
                <Textarea
                  value={formatOutput(result)}
                  readOnly
                  className="min-h-[100px] font-mono text-sm"
                />
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCopy(formatOutput(result))}
                  >
                    <Copy className="mr-2 h-4 w-4" />
                    {t('复制', 'Copy', 'コピー')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownload(formatOutput(result), fileName.split('.')[0])}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    {t('下载', 'Download', 'ダウンロード')}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
