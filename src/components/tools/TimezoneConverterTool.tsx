'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Globe, Clock, Plus, Trash2, Copy, RefreshCw, MapPin, Sun, Moon } from 'lucide-react';

interface WorldClock {
  id: string;
  city: string;
  timezone: string;
  offset: string;
  time: string;
  date: string;
  isDaylight: boolean;
  abbreviation: string;
}

interface ConversionResult {
  sourceCity: string;
  sourceTimezone: string;
  sourceTime: string;
  targetCity: string;
  targetTimezone: string;
  targetTime: string;
  timeDifference: string;
}

export default function TimezoneConverterTool() {
  const [sourceTimezone, setSourceTimezone] = useState('Asia/Shanghai');
  const [targetTimezone, setTargetTimezone] = useState('America/New_York');
  const [inputTime, setInputTime] = useState('');
  const [inputDate, setInputDate] = useState('');
  const [conversionResult, setConversionResult] = useState<ConversionResult | null>(null);
  const [worldClocks, setWorldClocks] = useState<WorldClock[]>([]);
  const [selectedCityToAdd, setSelectedCityToAdd] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());

  // 主要时区列表
  const timezones = [
    { value: 'Asia/Shanghai', label: '上海 (CST)', country: '中国' },
    { value: 'Asia/Tokyo', label: '东京 (JST)', country: '日本' },
    { value: 'Asia/Seoul', label: '首尔 (KST)', country: '韩国' },
    { value: 'Asia/Hong_Kong', label: '香港 (HKT)', country: '香港' },
    { value: 'Asia/Singapore', label: '新加坡 (SGT)', country: '新加坡' },
    { value: 'Asia/Bangkok', label: '曼谷 (ICT)', country: '泰国' },
    { value: 'Asia/Mumbai', label: '孟买 (IST)', country: '印度' },
    { value: 'Asia/Dubai', label: '迪拜 (GST)', country: '阿联酋' },
    { value: 'Europe/London', label: '伦敦 (GMT/BST)', country: '英国' },
    { value: 'Europe/Paris', label: '巴黎 (CET/CEST)', country: '法国' },
    { value: 'Europe/Berlin', label: '柏林 (CET/CEST)', country: '德国' },
    { value: 'Europe/Rome', label: '罗马 (CET/CEST)', country: '意大利' },
    { value: 'Europe/Moscow', label: '莫斯科 (MSK)', country: '俄罗斯' },
    { value: 'America/New_York', label: '纽约 (EST/EDT)', country: '美国' },
    { value: 'America/Los_Angeles', label: '洛杉矶 (PST/PDT)', country: '美国' },
    { value: 'America/Chicago', label: '芝加哥 (CST/CDT)', country: '美国' },
    { value: 'America/Denver', label: '丹佛 (MST/MDT)', country: '美国' },
    { value: 'America/Toronto', label: '多伦多 (EST/EDT)', country: '加拿大' },
    { value: 'America/Vancouver', label: '温哥华 (PST/PDT)', country: '加拿大' },
    { value: 'America/Sao_Paulo', label: '圣保罗 (BRT)', country: '巴西' },
    { value: 'America/Mexico_City', label: '墨西哥城 (CST/CDT)', country: '墨西哥' },
    { value: 'Australia/Sydney', label: '悉尼 (AEST/AEDT)', country: '澳大利亚' },
    { value: 'Australia/Melbourne', label: '墨尔本 (AEST/AEDT)', country: '澳大利亚' },
    { value: 'Australia/Perth', label: '珀斯 (AWST)', country: '澳大利亚' },
    { value: 'Pacific/Auckland', label: '奥克兰 (NZST/NZDT)', country: '新西兰' },
    { value: 'Africa/Cairo', label: '开罗 (EET)', country: '埃及' },
    { value: 'Africa/Johannesburg', label: '约翰内斯堡 (SAST)', country: '南非' },
    { value: 'UTC', label: 'UTC (协调世界时)', country: '国际标准' },
  ];

  // 获取时区信息
  const getTimezoneInfo = (timezone: string, date: Date = new Date()) => {
    try {
      const formatter = new Intl.DateTimeFormat('zh-CN', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });

      const timeString = formatter.format(date);
      const [dateStr, timeStr] = timeString.split(' ');

      // 获取时区偏移
      const offset = getTimezoneOffset(timezone, date);
      
      // 获取时区缩写
      const abbreviation = getTimezoneAbbreviation(timezone, date);

      // 判断是否为夏令时
      const isDaylight = isDaylightSaving(timezone, date);

      return {
        time: timeStr,
        date: dateStr,
        offset,
        abbreviation,
        isDaylight,
      };
    } catch (error) {
      console.error('Error getting timezone info:', error);
      return {
        time: '00:00:00',
        date: '1970/01/01',
        offset: '+00:00',
        abbreviation: 'UTC',
        isDaylight: false,
      };
    }
  };

  // 获取时区偏移
  const getTimezoneOffset = (timezone: string, date: Date) => {
    const utc = new Date(date.getTime() + (date.getTimezoneOffset() * 60000));
    const targetTime = new Date(utc.toLocaleString('en-US', { timeZone: timezone }));
    const offset = (targetTime.getTime() - utc.getTime()) / (1000 * 60 * 60);
    
    const sign = offset >= 0 ? '+' : '-';
    const hours = Math.floor(Math.abs(offset));
    const minutes = Math.round((Math.abs(offset) - hours) * 60);
    
    return `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  // 获取时区缩写
  const getTimezoneAbbreviation = (timezone: string, date: Date) => {
    try {
      const formatter = new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        timeZoneName: 'short',
      });
      
      const parts = formatter.formatToParts(date);
      const timeZoneName = parts.find(part => part.type === 'timeZoneName');
      
      return timeZoneName ? timeZoneName.value : 'UTC';
    } catch (error) {
      return 'UTC';
    }
  };

  // 判断是否为夏令时
  const isDaylightSaving = (timezone: string, date: Date) => {
    try {
      const januaryOffset = getTimezoneOffset(timezone, new Date(date.getFullYear(), 0, 1));
      const julyOffset = getTimezoneOffset(timezone, new Date(date.getFullYear(), 6, 1));
      const currentOffset = getTimezoneOffset(timezone, date);
      
      // 如果当前偏移与1月偏移不同，且与7月偏移相同，则可能处于夏令时
      return currentOffset !== januaryOffset;
    } catch (error) {
      return false;
    }
  };

  // 初始化默认世界时钟
  useEffect(() => {
    const defaultCities = [
      'Asia/Shanghai',
      'America/New_York',
      'Europe/London',
      'Asia/Tokyo',
      'Australia/Sydney',
    ];

    const clocks = defaultCities.map((timezone, index) => {
      const cityInfo = timezones.find(tz => tz.value === timezone);
      const timeInfo = getTimezoneInfo(timezone, currentTime);
      
      return {
        id: `default-${index}`,
        city: cityInfo?.label.split(' ')[0] || timezone.split('/')[1],
        timezone,
        ...timeInfo,
      };
    });

    setWorldClocks(clocks);
  }, []);

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);
      
      // 更新世界时钟
      setWorldClocks(prev => prev.map(clock => ({
        ...clock,
        ...getTimezoneInfo(clock.timezone, now),
      })));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 设置当前时间
  const setCurrentDateTime = () => {
    const now = new Date();
    setInputDate(now.toISOString().slice(0, 10));
    setInputTime(now.toTimeString().slice(0, 5));
  };

  // 时区转换
  const convertTimezone = () => {
    try {
      if (!inputDate || !inputTime) {
        alert('请输入日期和时间');
        return;
      }

      const sourceDateTime = new Date(`${inputDate}T${inputTime}`);
      
      // 获取源时区信息
      const sourceInfo = getTimezoneInfo(sourceTimezone, sourceDateTime);
      const sourceCity = timezones.find(tz => tz.value === sourceTimezone)?.label.split(' ')[0] || sourceTimezone;
      
      // 计算目标时区时间
      const utcTime = new Date(sourceDateTime.getTime() - getOffsetMinutes(sourceTimezone, sourceDateTime));
      const targetDateTime = new Date(utcTime.getTime() + getOffsetMinutes(targetTimezone, utcTime));
      
      const targetInfo = getTimezoneInfo(targetTimezone, targetDateTime);
      const targetCity = timezones.find(tz => tz.value === targetTimezone)?.label.split(' ')[0] || targetTimezone;
      
      // 计算时差
      const timeDifferenceHours = (getOffsetMinutes(targetTimezone, targetDateTime) - getOffsetMinutes(sourceTimezone, sourceDateTime)) / 60;
      const timeDifference = timeDifferenceHours >= 0 ? `+${timeDifferenceHours}小时` : `${timeDifferenceHours}小时`;

      const result: ConversionResult = {
        sourceCity,
        sourceTimezone,
        sourceTime: `${inputDate} ${inputTime}`,
        targetCity,
        targetTimezone,
        targetTime: `${targetInfo.date} ${targetInfo.time}`,
        timeDifference,
      };

      setConversionResult(result);
    } catch (error) {
      console.error('转换失败:', error);
      alert('时区转换失败，请检查输入');
    }
  };

  // 获取时区偏移分钟数
  const getOffsetMinutes = (timezone: string, date: Date) => {
    const offset = getTimezoneOffset(timezone, date);
    const [sign, time] = [offset[0], offset.slice(1)];
    const [hours, minutes] = time.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes;
    return sign === '+' ? totalMinutes : -totalMinutes;
  };

  // 添加世界时钟
  const addWorldClock = () => {
    if (!selectedCityToAdd || worldClocks.some(clock => clock.timezone === selectedCityToAdd)) {
      return;
    }

    const cityInfo = timezones.find(tz => tz.value === selectedCityToAdd);
    const timeInfo = getTimezoneInfo(selectedCityToAdd, currentTime);
    
    const newClock: WorldClock = {
      id: `clock-${Date.now()}`,
      city: cityInfo?.label.split(' ')[0] || selectedCityToAdd.split('/')[1],
      timezone: selectedCityToAdd,
      ...timeInfo,
    };

    setWorldClocks(prev => [...prev, newClock]);
    setSelectedCityToAdd('');
  };

  // 删除世界时钟
  const removeWorldClock = (id: string) => {
    setWorldClocks(prev => prev.filter(clock => clock.id !== id));
  };

  // 复制结果
  const copyResult = () => {
    if (!conversionResult) return;

    const result = `时区转换结果：
源时间：${conversionResult.sourceCity} ${conversionResult.sourceTime}
目标时间：${conversionResult.targetCity} ${conversionResult.targetTime}
时差：${conversionResult.timeDifference}`;

    navigator.clipboard.writeText(result);
  };

  // 交换时区
  const swapTimezones = () => {
    const temp = sourceTimezone;
    setSourceTimezone(targetTimezone);
    setTargetTimezone(temp);
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            时区转换器
          </CardTitle>
          <CardDescription>
            全球时区转换工具，支持世界时钟显示和精确时区计算
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="converter" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="converter">时区转换</TabsTrigger>
          <TabsTrigger value="worldclock">世界时钟</TabsTrigger>
        </TabsList>

        <TabsContent value="converter" className="space-y-4">
          {/* 时区转换设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Clock className="h-4 w-4" />
                时区转换设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 时间输入 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="input-date">日期</Label>
                  <Input
                    id="input-date"
                    type="date"
                    value={inputDate}
                    onChange={(e) => setInputDate(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="input-time">时间</Label>
                  <Input
                    id="input-time"
                    type="time"
                    step="1"
                    value={inputTime}
                    onChange={(e) => setInputTime(e.target.value)}
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={setCurrentDateTime}
                    className="w-full"
                  >
                    设为当前时间
                  </Button>
                </div>
              </div>

              {/* 时区选择 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>源时区</Label>
                  <Select value={sourceTimezone} onValueChange={setSourceTimezone}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {timezones.map(tz => (
                        <SelectItem key={tz.value} value={tz.value}>
                          <div className="flex flex-col">
                            <span>{tz.label}</span>
                            <span className="text-xs text-muted-foreground">{tz.country}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>目标时区</Label>
                  <Select value={targetTimezone} onValueChange={setTargetTimezone}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {timezones.map(tz => (
                        <SelectItem key={tz.value} value={tz.value}>
                          <div className="flex flex-col">
                            <span>{tz.label}</span>
                            <span className="text-xs text-muted-foreground">{tz.country}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={convertTimezone} className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  转换时区
                </Button>
                <Button variant="outline" onClick={swapTimezones}>
                  交换时区
                </Button>
                {conversionResult && (
                  <Button variant="outline" onClick={copyResult} className="flex items-center gap-2">
                    <Copy className="h-4 w-4" />
                    复制结果
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 转换结果 */}
          {conversionResult && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">转换结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div className="text-sm text-muted-foreground">源时间</div>
                    <div className="space-y-1">
                      <div className="text-lg font-semibold">{conversionResult.sourceTime}</div>
                      <div className="text-sm text-muted-foreground">
                        {conversionResult.sourceCity} ({conversionResult.sourceTimezone})
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="text-sm text-muted-foreground">目标时间</div>
                    <div className="space-y-1">
                      <div className="text-lg font-semibold text-primary">{conversionResult.targetTime}</div>
                      <div className="text-sm text-muted-foreground">
                        {conversionResult.targetCity} ({conversionResult.targetTimezone})
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <div className="text-sm text-muted-foreground">时差</div>
                  <div className="font-medium">{conversionResult.timeDifference}</div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="worldclock" className="space-y-4">
          {/* 添加世界时钟 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Plus className="h-4 w-4" />
                添加城市
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <div className="flex-1">
                  <Select value={selectedCityToAdd} onValueChange={setSelectedCityToAdd}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择要添加的城市" />
                    </SelectTrigger>
                    <SelectContent>
                      {timezones
                        .filter(tz => !worldClocks.some(clock => clock.timezone === tz.value))
                        .map(tz => (
                          <SelectItem key={tz.value} value={tz.value}>
                            <div className="flex flex-col">
                              <span>{tz.label}</span>
                              <span className="text-xs text-muted-foreground">{tz.country}</span>
                            </div>
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={addWorldClock} disabled={!selectedCityToAdd}>
                  添加
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 世界时钟显示 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {worldClocks.map((clock) => (
              <Card key={clock.id} className="relative">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      {clock.city}
                    </CardTitle>
                    {!clock.id.startsWith('default-') && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeWorldClock(clock.id)}
                        className="h-6 w-6 p-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    <div className="text-2xl font-bold font-mono">{clock.time}</div>
                    <div className="text-sm text-muted-foreground">{clock.date}</div>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="text-xs">
                        {clock.abbreviation}
                      </Badge>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        {clock.isDaylight ? <Sun className="h-3 w-3" /> : <Moon className="h-3 w-3" />}
                        {clock.offset}
                      </div>
                    </div>
                    {clock.isDaylight && (
                      <div className="text-xs text-orange-600">
                        夏令时
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {worldClocks.length === 0 && (
            <Card>
              <CardContent className="py-8 text-center text-muted-foreground">
                暂无世界时钟，请添加城市
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
