'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Copy, Calculator, RotateCcw, HardDrive, Database, Download } from 'lucide-react'

// 文件大小单位定义
const FILE_SIZE_UNITS = [
  { value: 'B', label: 'Bytes (B)', factor: 1 },
  { value: 'KB', label: 'Kilobytes (KB)', factor: 1024 },
  { value: 'MB', label: 'Megabytes (MB)', factor: 1024 * 1024 },
  { value: 'GB', label: 'Gigabytes (GB)', factor: 1024 * 1024 * 1024 },
  { value: 'TB', label: 'Terabytes (TB)', factor: 1024 * 1024 * 1024 * 1024 },
  { value: 'PB', label: 'Petabytes (PB)', factor: 1024 * 1024 * 1024 * 1024 * 1024 },
]

// 二进制和十进制单位
const BINARY_UNITS = [
  { value: 'B', label: 'Bytes', factor: 1 },
  { value: 'KiB', label: 'Kibibytes (KiB)', factor: 1024 },
  { value: 'MiB', label: 'Mebibytes (MiB)', factor: 1024 ** 2 },
  { value: 'GiB', label: 'Gibibytes (GiB)', factor: 1024 ** 3 },
  { value: 'TiB', label: 'Tebibytes (TiB)', factor: 1024 ** 4 },
  { value: 'PiB', label: 'Pebibytes (PiB)', factor: 1024 ** 5 },
]

const DECIMAL_UNITS = [
  { value: 'B', label: 'Bytes', factor: 1 },
  { value: 'kB', label: 'Kilobytes (kB)', factor: 1000 },
  { value: 'MB', label: 'Megabytes (MB)', factor: 1000 ** 2 },
  { value: 'GB', label: 'Gigabytes (GB)', factor: 1000 ** 3 },
  { value: 'TB', label: 'Terabytes (TB)', factor: 1000 ** 4 },
  { value: 'PB', label: 'Petabytes (PB)', factor: 1000 ** 5 },
]

interface ConversionResult {
  bytes: number
  conversions: Array<{
    unit: string
    value: number
    label: string
  }>
}

export default function FileSizeCalculatorTool() {
  const [inputValue, setInputValue] = useState<string>('1')
  const [inputUnit, setInputUnit] = useState<string>('MB')
  const [result, setResult] = useState<ConversionResult | null>(null)
  const [unitSystem, setUnitSystem] = useState<'binary' | 'decimal'>('binary')

  // 计算文件大小转换
  const calculateConversions = useCallback((value: number, unit: string, system: 'binary' | 'decimal') => {
    const units = system === 'binary' ? BINARY_UNITS : DECIMAL_UNITS
    const inputUnitData = units.find(u => u.value === unit)
    
    if (!inputUnitData) return null

    const bytes = value * inputUnitData.factor
    
    const conversions = units.map(unitData => ({
      unit: unitData.value,
      value: bytes / unitData.factor,
      label: unitData.label
    }))

    return { bytes, conversions }
  }, [])

  // 处理转换
  const handleConvert = useCallback(() => {
    const value = parseFloat(inputValue)
    if (isNaN(value) || value < 0) {
      alert('请输入有效的正数')
      return
    }

    const conversionResult = calculateConversions(value, inputUnit, unitSystem)
    if (conversionResult) {
      setResult(conversionResult)
    }
  }, [inputValue, inputUnit, unitSystem, calculateConversions])

  // 重置
  const handleReset = useCallback(() => {
    setInputValue('1')
    setInputUnit('MB')
    setResult(null)
    setUnitSystem('binary')
  }, [])

  // 复制结果
  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      console.log('已复制到剪贴板')
    })
  }, [])

  // 格式化数字
  const formatNumber = useCallback((num: number): string => {
    if (num >= 1e12) return num.toExponential(3)
    if (num >= 1e9) return (num / 1e9).toFixed(3) + 'B'
    if (num >= 1e6) return (num / 1e6).toFixed(3) + 'M'
    if (num >= 1e3) return (num / 1e3).toFixed(3) + 'K'
    if (num >= 1) return num.toFixed(3)
    return num.toExponential(3)
  }, [])

  // 获取存储设备容量参考
  const getStorageReferences = useCallback((bytes: number) => {
    const references = []
    
    if (bytes >= 1024 ** 4) { // >= 1TB
      references.push(`约 ${(bytes / (1024 ** 4)).toFixed(2)} TB - 大型硬盘容量`)
    } else if (bytes >= 1024 ** 3) { // >= 1GB
      references.push(`约 ${(bytes / (1024 ** 3)).toFixed(2)} GB - 中等文件/小型硬盘`)
    } else if (bytes >= 1024 ** 2) { // >= 1MB
      references.push(`约 ${(bytes / (1024 ** 2)).toFixed(2)} MB - 高清图片/小视频`)
    } else if (bytes >= 1024) { // >= 1KB
      references.push(`约 ${(bytes / 1024).toFixed(2)} KB - 文档/小图片`)
    }

    // 添加具体对比
    if (bytes > 0) {
      const mp3Minutes = bytes / (128 * 1024 / 8) / 60 // 128kbps MP3
      const textPages = bytes / 2000 // 假设每页2KB
      const photos = bytes / (5 * 1024 * 1024) // 5MB高清照片
      
      if (mp3Minutes >= 1) references.push(`约 ${mp3Minutes.toFixed(1)} 分钟 MP3 音乐`)
      if (textPages >= 1) references.push(`约 ${Math.floor(textPages)} 页文本`)
      if (photos >= 1) references.push(`约 ${Math.floor(photos)} 张高清照片`)
    }

    return references
  }, [])

  const currentUnits = unitSystem === 'binary' ? BINARY_UNITS : DECIMAL_UNITS

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            文件大小计算器
          </CardTitle>
          <CardDescription>
            转换不同文件大小单位，支持二进制（1024）和十进制（1000）转换标准
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 单位系统选择 */}
          <div className="flex gap-4">
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="binary"
                name="unitSystem"
                checked={unitSystem === 'binary'}
                onChange={() => setUnitSystem('binary')}
                className="rounded"
              />
              <Label htmlFor="binary">二进制 (1024)</Label>
              <Badge variant="secondary">标准</Badge>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="decimal"
                name="unitSystem"
                checked={unitSystem === 'decimal'}
                onChange={() => setUnitSystem('decimal')}
                className="rounded"
              />
              <Label htmlFor="decimal">十进制 (1000)</Label>
              <Badge variant="outline">SI</Badge>
            </div>
          </div>

          {/* 输入区域 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="value">数值</Label>
              <Input
                id="value"
                type="number"
                min="0"
                step="any"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="输入文件大小"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="unit">单位</Label>
              <Select value={inputUnit} onValueChange={setInputUnit}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currentUnits.map((unit) => (
                    <SelectItem key={unit.value} value={unit.value}>
                      {unit.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end gap-2">
              <Button onClick={handleConvert} className="flex-1">
                <Calculator className="w-4 h-4 mr-2" />
                转换
              </Button>
              <Button onClick={handleReset} variant="outline" size="icon">
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* 结果显示 */}
          {result && (
            <div className="space-y-4">
              <hr className="border-t border-border" />
              
              <Tabs defaultValue="conversions" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="conversions">单位转换</TabsTrigger>
                  <TabsTrigger value="reference">容量参考</TabsTrigger>
                </TabsList>
                
                <TabsContent value="conversions" className="space-y-4">
                  <div className="grid gap-3">
                    {result.conversions.map((conversion) => (
                      <div
                        key={conversion.unit}
                        className="flex items-center justify-between p-3 bg-muted rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <Database className="w-4 h-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">
                              {formatNumber(conversion.value)} {conversion.unit}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {conversion.label}
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(`${conversion.value} ${conversion.unit}`)}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </TabsContent>
                
                <TabsContent value="reference" className="space-y-4">
                  <div className="space-y-3">
                    <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                      <h4 className="font-medium mb-2">精确字节数</h4>
                      <div className="flex items-center justify-between">
                        <span className="font-mono text-lg">{result.bytes.toLocaleString()} Bytes</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(result.bytes.toString())}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">容量对比参考</h4>
                      <div className="space-y-2">
                        {getStorageReferences(result.bytes).map((ref, index) => (
                          <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded">
                            <Download className="w-4 h-4 text-muted-foreground" />
                            <span className="text-sm">{ref}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}

          {/* 使用说明 */}
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">使用说明</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 二进制标准：1 KB = 1024 Bytes（计算机标准）</li>
              <li>• 十进制标准：1 kB = 1000 Bytes（国际单位制）</li>
              <li>• 支持从 Bytes 到 Petabytes 的转换</li>
              <li>• 提供存储设备和文件类型的容量参考</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
