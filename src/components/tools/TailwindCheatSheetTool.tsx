'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Copy, Search, Book, Palette, Layout, Type, Zap, Box, Grid, Star, ExternalLink, Filter } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface TailwindClass {
  class: string
  css: string
  description: string
  category: string
  subcategory?: string
  popular?: boolean
}

// Tailwind CSS 类的数据库
const tailwindClasses: TailwindClass[] = [
  // Layout
  { class: 'container', css: 'max-width: 100%; margin: 0 auto; padding: 0 1rem;', description: '响应式容器', category: 'layout', popular: true },
  { class: 'mx-auto', css: 'margin-left: auto; margin-right: auto;', description: '水平居中', category: 'layout', popular: true },
  { class: 'flex', css: 'display: flex;', description: '弹性布局', category: 'layout', popular: true },
  { class: 'grid', css: 'display: grid;', description: '网格布局', category: 'layout', popular: true },
  { class: 'block', css: 'display: block;', description: '块级元素', category: 'layout' },
  { class: 'inline', css: 'display: inline;', description: '内联元素', category: 'layout' },
  { class: 'inline-block', css: 'display: inline-block;', description: '内联块元素', category: 'layout' },
  { class: 'hidden', css: 'display: none;', description: '隐藏元素', category: 'layout', popular: true },
  
  // Flexbox & Grid
  { class: 'justify-center', css: 'justify-content: center;', description: '主轴居中', category: 'flexbox', popular: true },
  { class: 'justify-between', css: 'justify-content: space-between;', description: '主轴两端对齐', category: 'flexbox', popular: true },
  { class: 'items-center', css: 'align-items: center;', description: '交叉轴居中', category: 'flexbox', popular: true },
  { class: 'flex-col', css: 'flex-direction: column;', description: '垂直排列', category: 'flexbox', popular: true },
  { class: 'flex-wrap', css: 'flex-wrap: wrap;', description: '允许换行', category: 'flexbox' },
  { class: 'grid-cols-1', css: 'grid-template-columns: repeat(1, minmax(0, 1fr));', description: '1列网格', category: 'grid' },
  { class: 'grid-cols-2', css: 'grid-template-columns: repeat(2, minmax(0, 1fr));', description: '2列网格', category: 'grid', popular: true },
  { class: 'grid-cols-3', css: 'grid-template-columns: repeat(3, minmax(0, 1fr));', description: '3列网格', category: 'grid', popular: true },
  { class: 'gap-4', css: 'gap: 1rem;', description: '网格间距1rem', category: 'grid', popular: true },
  
  // Spacing
  { class: 'p-4', css: 'padding: 1rem;', description: '内边距1rem', category: 'spacing', popular: true },
  { class: 'px-4', css: 'padding-left: 1rem; padding-right: 1rem;', description: '水平内边距1rem', category: 'spacing', popular: true },
  { class: 'py-4', css: 'padding-top: 1rem; padding-bottom: 1rem;', description: '垂直内边距1rem', category: 'spacing', popular: true },
  { class: 'm-4', css: 'margin: 1rem;', description: '外边距1rem', category: 'spacing', popular: true },
  { class: 'mx-4', css: 'margin-left: 1rem; margin-right: 1rem;', description: '水平外边距1rem', category: 'spacing', popular: true },
  { class: 'my-4', css: 'margin-top: 1rem; margin-bottom: 1rem;', description: '垂直外边距1rem', category: 'spacing', popular: true },
  { class: 'space-x-4', css: '> * + * { margin-left: 1rem; }', description: '子元素水平间距', category: 'spacing' },
  { class: 'space-y-4', css: '> * + * { margin-top: 1rem; }', description: '子元素垂直间距', category: 'spacing' },
  
  // Sizing
  { class: 'w-full', css: 'width: 100%;', description: '全宽', category: 'sizing', popular: true },
  { class: 'h-full', css: 'height: 100%;', description: '全高', category: 'sizing', popular: true },
  { class: 'w-screen', css: 'width: 100vw;', description: '屏幕宽度', category: 'sizing' },
  { class: 'h-screen', css: 'height: 100vh;', description: '屏幕高度', category: 'sizing', popular: true },
  { class: 'w-1/2', css: 'width: 50%;', description: '50%宽度', category: 'sizing', popular: true },
  { class: 'w-1/3', css: 'width: 33.333333%;', description: '33.33%宽度', category: 'sizing' },
  { class: 'w-64', css: 'width: 16rem;', description: '固定宽度16rem', category: 'sizing' },
  { class: 'min-h-screen', css: 'min-height: 100vh;', description: '最小屏幕高度', category: 'sizing', popular: true },
  
  // Typography
  { class: 'text-xs', css: 'font-size: 0.75rem; line-height: 1rem;', description: '极小字体', category: 'typography' },
  { class: 'text-sm', css: 'font-size: 0.875rem; line-height: 1.25rem;', description: '小字体', category: 'typography', popular: true },
  { class: 'text-base', css: 'font-size: 1rem; line-height: 1.5rem;', description: '基础字体', category: 'typography', popular: true },
  { class: 'text-lg', css: 'font-size: 1.125rem; line-height: 1.75rem;', description: '大字体', category: 'typography', popular: true },
  { class: 'text-xl', css: 'font-size: 1.25rem; line-height: 1.75rem;', description: '超大字体', category: 'typography' },
  { class: 'text-2xl', css: 'font-size: 1.5rem; line-height: 2rem;', description: '2倍大字体', category: 'typography', popular: true },
  { class: 'font-normal', css: 'font-weight: 400;', description: '正常字重', category: 'typography' },
  { class: 'font-medium', css: 'font-weight: 500;', description: '中等字重', category: 'typography', popular: true },
  { class: 'font-semibold', css: 'font-weight: 600;', description: '半粗字重', category: 'typography', popular: true },
  { class: 'font-bold', css: 'font-weight: 700;', description: '粗体字重', category: 'typography', popular: true },
  { class: 'text-center', css: 'text-align: center;', description: '居中对齐', category: 'typography', popular: true },
  { class: 'text-left', css: 'text-align: left;', description: '左对齐', category: 'typography' },
  { class: 'text-right', css: 'text-align: right;', description: '右对齐', category: 'typography' },
  
  // Colors
  { class: 'text-white', css: 'color: rgb(255 255 255);', description: '白色文字', category: 'colors', popular: true },
  { class: 'text-black', css: 'color: rgb(0 0 0);', description: '黑色文字', category: 'colors', popular: true },
  { class: 'text-gray-500', css: 'color: rgb(107 114 128);', description: '灰色文字', category: 'colors', popular: true },
  { class: 'text-blue-500', css: 'color: rgb(59 130 246);', description: '蓝色文字', category: 'colors', popular: true },
  { class: 'text-red-500', css: 'color: rgb(239 68 68);', description: '红色文字', category: 'colors', popular: true },
  { class: 'text-green-500', css: 'color: rgb(34 197 94);', description: '绿色文字', category: 'colors', popular: true },
  { class: 'bg-white', css: 'background-color: rgb(255 255 255);', description: '白色背景', category: 'colors', popular: true },
  { class: 'bg-black', css: 'background-color: rgb(0 0 0);', description: '黑色背景', category: 'colors' },
  { class: 'bg-gray-100', css: 'background-color: rgb(243 244 246);', description: '浅灰背景', category: 'colors', popular: true },
  { class: 'bg-blue-500', css: 'background-color: rgb(59 130 246);', description: '蓝色背景', category: 'colors', popular: true },
  { class: 'bg-red-500', css: 'background-color: rgb(239 68 68);', description: '红色背景', category: 'colors' },
  { class: 'bg-green-500', css: 'background-color: rgb(34 197 94);', description: '绿色背景', category: 'colors' },
  
  // Borders
  { class: 'border', css: 'border-width: 1px;', description: '1px边框', category: 'borders', popular: true },
  { class: 'border-2', css: 'border-width: 2px;', description: '2px边框', category: 'borders' },
  { class: 'border-gray-300', css: 'border-color: rgb(209 213 219);', description: '灰色边框', category: 'borders', popular: true },
  { class: 'border-blue-500', css: 'border-color: rgb(59 130 246);', description: '蓝色边框', category: 'borders' },
  { class: 'rounded', css: 'border-radius: 0.25rem;', description: '圆角', category: 'borders', popular: true },
  { class: 'rounded-md', css: 'border-radius: 0.375rem;', description: '中等圆角', category: 'borders', popular: true },
  { class: 'rounded-lg', css: 'border-radius: 0.5rem;', description: '大圆角', category: 'borders', popular: true },
  { class: 'rounded-full', css: 'border-radius: 9999px;', description: '完全圆角', category: 'borders', popular: true },
  
  // Effects
  { class: 'shadow', css: 'box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);', description: '阴影', category: 'effects', popular: true },
  { class: 'shadow-md', css: 'box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);', description: '中等阴影', category: 'effects', popular: true },
  { class: 'shadow-lg', css: 'box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);', description: '大阴影', category: 'effects', popular: true },
  { class: 'hover:shadow-lg', css: '&:hover { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }', description: '悬停大阴影', category: 'effects' },
  { class: 'opacity-50', css: 'opacity: 0.5;', description: '50%透明度', category: 'effects' },
  { class: 'opacity-75', css: 'opacity: 0.75;', description: '75%透明度', category: 'effects' },
  
  // Positioning
  { class: 'relative', css: 'position: relative;', description: '相对定位', category: 'positioning', popular: true },
  { class: 'absolute', css: 'position: absolute;', description: '绝对定位', category: 'positioning', popular: true },
  { class: 'fixed', css: 'position: fixed;', description: '固定定位', category: 'positioning' },
  { class: 'sticky', css: 'position: sticky;', description: '粘性定位', category: 'positioning' },
  { class: 'top-0', css: 'top: 0px;', description: '顶部0', category: 'positioning', popular: true },
  { class: 'left-0', css: 'left: 0px;', description: '左侧0', category: 'positioning' },
  { class: 'right-0', css: 'right: 0px;', description: '右侧0', category: 'positioning' },
  { class: 'bottom-0', css: 'bottom: 0px;', description: '底部0', category: 'positioning' },
  { class: 'z-10', css: 'z-index: 10;', description: 'z-index 10', category: 'positioning', popular: true },
  
  // Responsive
  { class: 'sm:text-lg', css: '@media (min-width: 640px) { font-size: 1.125rem; line-height: 1.75rem; }', description: '小屏及以上大字体', category: 'responsive' },
  { class: 'md:flex', css: '@media (min-width: 768px) { display: flex; }', description: '中屏及以上弹性布局', category: 'responsive', popular: true },
  { class: 'lg:grid-cols-3', css: '@media (min-width: 1024px) { grid-template-columns: repeat(3, minmax(0, 1fr)); }', description: '大屏及以上3列网格', category: 'responsive' },
  { class: 'xl:px-8', css: '@media (min-width: 1280px) { padding-left: 2rem; padding-right: 2rem; }', description: '超大屏水平内边距', category: 'responsive' },
  
  // Transitions
  { class: 'transition', css: 'transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;', description: '过渡动画', category: 'transitions', popular: true },
  { class: 'duration-300', css: 'transition-duration: 300ms;', description: '300ms动画时长', category: 'transitions', popular: true },
  { class: 'ease-in-out', css: 'transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);', description: '缓入缓出', category: 'transitions' },
  { class: 'transform', css: 'transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));', description: '变换', category: 'transitions' },
  { class: 'hover:scale-105', css: '&:hover { transform: scale(1.05); }', description: '悬停放大', category: 'transitions', popular: true },
  
  // Interactivity
  { class: 'cursor-pointer', css: 'cursor: pointer;', description: '手型光标', category: 'interactivity', popular: true },
  { class: 'cursor-not-allowed', css: 'cursor: not-allowed;', description: '禁止光标', category: 'interactivity' },
  { class: 'select-none', css: 'user-select: none;', description: '禁止选择', category: 'interactivity' },
  { class: 'hover:bg-gray-100', css: '&:hover { background-color: rgb(243 244 246); }', description: '悬停背景色', category: 'interactivity', popular: true },
  { class: 'focus:outline-none', css: '&:focus { outline: 2px solid transparent; outline-offset: 2px; }', description: '聚焦无轮廓', category: 'interactivity', popular: true },
  { class: 'focus:ring-2', css: '&:focus { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }', description: '聚焦环', category: 'interactivity' }
]

const categories = [
  { id: 'all', name: '全部', icon: Book },
  { id: 'layout', name: '布局', icon: Layout },
  { id: 'flexbox', name: 'Flexbox', icon: Box },
  { id: 'grid', name: 'Grid', icon: Grid },
  { id: 'spacing', name: '间距', icon: Box },
  { id: 'sizing', name: '尺寸', icon: Box },
  { id: 'typography', name: '排版', icon: Type },
  { id: 'colors', name: '颜色', icon: Palette },
  { id: 'borders', name: '边框', icon: Box },
  { id: 'effects', name: '效果', icon: Zap },
  { id: 'positioning', name: '定位', icon: Layout },
  { id: 'responsive', name: '响应式', icon: Layout },
  { id: 'transitions', name: '动画', icon: Zap },
  { id: 'interactivity', name: '交互', icon: Zap }
]

export default function TailwindCheatSheetTool() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showPopularOnly, setShowPopularOnly] = useState(false)
  const [copiedClass, setCopiedClass] = useState<string | null>(null)

  const filteredClasses = useMemo(() => {
    let filtered = tailwindClasses

    // 分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory)
    }

    // 热门过滤
    if (showPopularOnly) {
      filtered = filtered.filter(item => item.popular)
    }

    // 搜索过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(item => 
        item.class.toLowerCase().includes(term) ||
        item.description.toLowerCase().includes(term) ||
        item.css.toLowerCase().includes(term)
      )
    }

    return filtered
  }, [searchTerm, selectedCategory, showPopularOnly])

  const copyToClipboard = useCallback((text: string, type: 'class' | 'css' = 'class') => {
    navigator.clipboard.writeText(text)
    setCopiedClass(text)
    setTimeout(() => setCopiedClass(null), 2000)
    
    toast.success(type === 'class' ? '类名已复制' : 'CSS已复制')
  }, [])

  const getClassCountByCategory = useCallback((categoryId: string) => {
    if (categoryId === 'all') return tailwindClasses.length
    return tailwindClasses.filter(item => item.category === categoryId).length
  }, [])

  const popularClasses = useMemo(() => {
    return tailwindClasses.filter(item => item.popular).slice(0, 12)
  }, [])

  const generateCombination = useCallback(() => {
    const combinations = [
      'flex items-center justify-center',
      'grid grid-cols-3 gap-4',
      'p-4 bg-white rounded-lg shadow-md',
      'text-lg font-semibold text-gray-900',
      'w-full px-4 py-2 border border-gray-300 rounded-md',
      'hover:bg-blue-500 hover:text-white transition duration-300',
      'absolute top-0 left-0 w-full h-full',
      'flex flex-col space-y-4',
      'sm:flex-row sm:space-y-0 sm:space-x-4',
      'bg-gradient-to-r from-blue-500 to-purple-600',
      'max-w-md mx-auto bg-white rounded-xl shadow-md',
      'transform hover:scale-105 transition duration-300'
    ]
    
    const randomCombination = combinations[Math.floor(Math.random() * combinations.length)]
    copyToClipboard(randomCombination)
  }, [copyToClipboard])

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Tailwind CSS 速查表
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          完整的Tailwind CSS类名参考，快速查找和复制常用样式类
        </p>
      </div>

      {/* 搜索和过滤 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索类名、描述或CSS..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex gap-3">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      <div className="flex items-center gap-2">
                        <category.icon className="h-4 w-4" />
                        {category.name}
                        <Badge variant="outline" className="ml-1">
                          {getClassCountByCategory(category.id)}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button
                variant={showPopularOnly ? "default" : "outline"}
                onClick={() => setShowPopularOnly(!showPopularOnly)}
                className="flex items-center gap-2"
              >
                <Star className="h-4 w-4" />
                热门
              </Button>
              
              <Button
                variant="outline"
                onClick={generateCombination}
                className="flex items-center gap-2"
              >
                <Zap className="h-4 w-4" />
                随机组合
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 热门类名 */}
      {selectedCategory === 'all' && !searchTerm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              热门类名
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {popularClasses.map((item, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="justify-start text-left h-auto p-3"
                  onClick={() => copyToClipboard(item.class)}
                >
                  <div className="w-full">
                    <div className="font-mono text-sm text-blue-600 dark:text-blue-400">
                      {item.class}
                    </div>
                    <div className="text-xs text-gray-500 mt-1 truncate">
                      {item.description}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 分类标签 */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
            className="flex items-center gap-2"
          >
            <category.icon className="h-4 w-4" />
            {category.name}
            <Badge variant="outline" className="ml-1">
              {getClassCountByCategory(category.id)}
            </Badge>
          </Button>
        ))}
      </div>

      {/* 类名列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredClasses.map((item, index) => (
          <Card key={index} className="group hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="space-y-3">
                {/* 类名和热门标识 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <code className="text-sm font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-blue-600 dark:text-blue-400">
                      {item.class}
                    </code>
                    {item.popular && (
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    )}
                  </div>
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(item.class)}
                      className="h-8 w-8 p-0"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {/* 描述 */}
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {item.description}
                </p>

                {/* CSS 代码 */}
                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                  <code className="text-xs font-mono text-gray-700 dark:text-gray-300 break-all">
                    {item.css}
                  </code>
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => copyToClipboard(item.class)}
                    className="flex-1"
                    variant={copiedClass === item.class ? "default" : "outline"}
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    {copiedClass === item.class ? '已复制' : '复制类名'}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(item.css, 'css')}
                    className="flex-1"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    复制CSS
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {filteredClasses.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              未找到匹配的类名
            </h3>
            <p className="text-gray-500 mb-4">
              尝试调整搜索词或选择不同的分类
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('')
                setSelectedCategory('all')
                setShowPopularOnly(false)
              }}
            >
              <Filter className="h-4 w-4 mr-2" />
              清除筛选
            </Button>
          </CardContent>
        </Card>
      )}

      {/* 底部信息 */}
      <Card>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Badge variant="outline">{tailwindClasses.length}</Badge>
                <span>总类名数</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{tailwindClasses.filter(c => c.popular).length}</Badge>
                <span>热门类名</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{categories.length - 1}</Badge>
                <span>分类数</span>
              </div>
            </div>
            
            <div className="flex items-center justify-center gap-4 text-sm">
              <Button variant="ghost" size="sm" asChild>
                <a 
                  href="https://tailwindcss.com/docs" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  官方文档
                </a>
              </Button>
              <Button variant="ghost" size="sm" asChild>
                <a 
                  href="https://tailwindui.com/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  TailwindUI
                </a>
              </Button>
              <Button variant="ghost" size="sm" asChild>
                <a 
                  href="https://headlessui.com/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  HeadlessUI
                </a>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}