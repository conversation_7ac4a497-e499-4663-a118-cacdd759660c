'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  Download, 
  Settings,
  SkipForward,
  SkipBack,
  RotateCcw,
  FileText,
  Mic,
  Languages,
  Clock,
  Zap
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Voice {
  name: string
  lang: string
  localService: boolean
  default: boolean
  voiceURI: string
}

interface SpeechSettings {
  rate: number
  pitch: number
  volume: number
  voice: string
}

interface PlaybackState {
  isPlaying: boolean
  isPaused: boolean
  currentPosition: number
  totalLength: number
  currentSentence: number
}

/**
 * 增强版文本转语音工具组件
 * 支持多语言、语音控制、播放控制、语音设置等功能
 */
export default function TextToSpeechEnhancedTool() {
  const { toast } = useToast()
  
  // 基础状态
  const [text, setText] = useState('')
  const [voices, setVoices] = useState<Voice[]>([])
  const [isSupported, setIsSupported] = useState(true)
  
  // 语音设置
  const [settings, setSettings] = useState<SpeechSettings>({
    rate: 1,
    pitch: 1,
    volume: 1,
    voice: ''
  })
  
  // 播放状态
  const [playbackState, setPlaybackState] = useState<PlaybackState>({
    isPlaying: false,
    isPaused: false,
    currentPosition: 0,
    totalLength: 0,
    currentSentence: 0
  })
  
  // 文本分析
  const [sentences, setSentences] = useState<string[]>([])
  const [wordCount, setWordCount] = useState(0)
  const [estimatedTime, setEstimatedTime] = useState(0)
  
  // 引用
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  
  // 检查浏览器支持
  useEffect(() => {
    if (!('speechSynthesis' in window)) {
      setIsSupported(false)
      toast({
        title: '不支持语音合成',
        description: '您的浏览器不支持语音合成功能',
        variant: 'destructive'
      })
      return
    }
    
    // 加载可用语音
    loadVoices()
    
    // 监听语音变化
    if (speechSynthesis.onvoiceschanged !== undefined) {
      speechSynthesis.onvoiceschanged = loadVoices
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      speechSynthesis.cancel()
    }
  }, [])
  
  // 加载可用语音
  const loadVoices = () => {
    const availableVoices = speechSynthesis.getVoices()
    const voiceList: Voice[] = availableVoices.map(voice => ({
      name: voice.name,
      lang: voice.lang,
      localService: voice.localService,
      default: voice.default,
      voiceURI: voice.voiceURI
    }))
    
    setVoices(voiceList)
    
    // 设置默认语音
    if (voiceList.length > 0 && !settings.voice) {
      const defaultVoice = voiceList.find(v => v.default) || voiceList[0]
      setSettings(prev => ({ ...prev, voice: defaultVoice.voiceURI }))
    }
  }
  
  // 分析文本
  useEffect(() => {
    if (text.trim()) {
      // 分割句子
      const sentenceList = text.split(/[.!?。！？]+/).filter(s => s.trim())
      setSentences(sentenceList)
      
      // 计算字数
      const words = text.trim().split(/\s+/).length
      setWordCount(words)
      
      // 估算时间（基于语速）
      const wordsPerMinute = 150 * settings.rate
      const minutes = words / wordsPerMinute
      setEstimatedTime(Math.ceil(minutes * 60))
    } else {
      setSentences([])
      setWordCount(0)
      setEstimatedTime(0)
    }
  }, [text, settings.rate])
  
  // 开始播放
  const startSpeech = () => {
    if (!text.trim()) {
      toast({
        title: '请输入文本',
        description: '请先输入要朗读的文本内容',
        variant: 'destructive'
      })
      return
    }
    
    // 停止当前播放
    speechSynthesis.cancel()
    
    // 创建新的语音合成
    const utterance = new SpeechSynthesisUtterance(text)
    
    // 设置语音参数
    const selectedVoice = voices.find(v => v.voiceURI === settings.voice)
    if (selectedVoice) {
      utterance.voice = speechSynthesis.getVoices().find(v => v.voiceURI === selectedVoice.voiceURI) || null
    }
    
    utterance.rate = settings.rate
    utterance.pitch = settings.pitch
    utterance.volume = settings.volume
    
    // 设置事件监听
    utterance.onstart = () => {
      setPlaybackState(prev => ({
        ...prev,
        isPlaying: true,
        isPaused: false,
        totalLength: text.length
      }))
      
      // 开始进度跟踪
      startProgressTracking()
    }
    
    utterance.onend = () => {
      setPlaybackState(prev => ({
        ...prev,
        isPlaying: false,
        isPaused: false,
        currentPosition: 0,
        currentSentence: 0
      }))
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      
      toast({
        title: '播放完成',
        description: '文本朗读已完成'
      })
    }
    
    utterance.onerror = (event) => {
      console.error('语音合成错误:', event)
      toast({
        title: '播放错误',
        description: '语音合成过程中发生错误',
        variant: 'destructive'
      })
      
      setPlaybackState(prev => ({
        ...prev,
        isPlaying: false,
        isPaused: false
      }))
    }
    
    utterance.onboundary = (event) => {
      if (event.name === 'sentence') {
        setPlaybackState(prev => ({
          ...prev,
          currentPosition: event.charIndex,
          currentSentence: Math.floor(event.charIndex / (text.length / sentences.length))
        }))
      }
    }
    
    utteranceRef.current = utterance
    speechSynthesis.speak(utterance)
  }
  
  // 暂停播放
  const pauseSpeech = () => {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause()
      setPlaybackState(prev => ({ ...prev, isPaused: true }))
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }
  
  // 恢复播放
  const resumeSpeech = () => {
    if (speechSynthesis.paused) {
      speechSynthesis.resume()
      setPlaybackState(prev => ({ ...prev, isPaused: false }))
      startProgressTracking()
    }
  }
  
  // 停止播放
  const stopSpeech = () => {
    speechSynthesis.cancel()
    setPlaybackState({
      isPlaying: false,
      isPaused: false,
      currentPosition: 0,
      totalLength: 0,
      currentSentence: 0
    })
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }
  
  // 开始进度跟踪
  const startProgressTracking = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
    
    intervalRef.current = setInterval(() => {
      if (!speechSynthesis.speaking || speechSynthesis.paused) {
        return
      }
      
      // 模拟进度更新（实际进度难以精确获取）
      setPlaybackState(prev => {
        const increment = text.length / (estimatedTime * 10) // 每100ms的增量
        const newPosition = Math.min(prev.currentPosition + increment, text.length)
        const newSentence = Math.floor((newPosition / text.length) * sentences.length)
        
        return {
          ...prev,
          currentPosition: newPosition,
          currentSentence: newSentence
        }
      })
    }, 100)
  }
  
  // 跳转到指定句子
  const jumpToSentence = (index: number) => {
    if (index < 0 || index >= sentences.length) return
    
    const sentenceText = sentences.slice(index).join('. ')
    setText(sentenceText)
    
    if (playbackState.isPlaying) {
      stopSpeech()
      setTimeout(() => startSpeech(), 100)
    }
  }
  
  // 导出音频（模拟）
  const exportAudio = () => {
    toast({
      title: '功能提示',
      description: '音频导出功能需要服务器端支持，当前版本暂不支持',
      variant: 'default'
    })
  }
  
  // 重置设置
  const resetSettings = () => {
    setSettings({
      rate: 1,
      pitch: 1,
      volume: 1,
      voice: voices.length > 0 ? voices[0].voiceURI : ''
    })
    
    toast({
      title: '设置已重置',
      description: '语音设置已恢复默认值'
    })
  }
  
  // 获取语言显示名称
  const getLanguageName = (langCode: string) => {
    const langMap: { [key: string]: string } = {
      'zh-CN': '中文（简体）',
      'zh-TW': '中文（繁体）',
      'en-US': '英语（美国）',
      'en-GB': '英语（英国）',
      'ja-JP': '日语',
      'ko-KR': '韩语',
      'fr-FR': '法语',
      'de-DE': '德语',
      'es-ES': '西班牙语',
      'it-IT': '意大利语',
      'ru-RU': '俄语'
    }
    
    return langMap[langCode] || langCode
  }
  
  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }
  
  if (!isSupported) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            增强版文本朗读器
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Volume2 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600">您的浏览器不支持语音合成功能</p>
            <p className="text-sm text-gray-500 mt-2">
              请使用现代浏览器（Chrome、Firefox、Safari、Edge）
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* 主要控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            增强版文本朗读器
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 文本输入区域 */}
          <div className="space-y-2">
            <Label htmlFor="text-input">输入文本</Label>
            <Textarea
              id="text-input"
              placeholder="请输入要朗读的文本内容..."
              value={text}
              onChange={(e) => setText(e.target.value)}
              className="min-h-[120px] resize-y"
            />
            
            {/* 文本统计 */}
            {text.trim() && (
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  <span>{wordCount} 词</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>约 {formatTime(estimatedTime)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Languages className="h-4 w-4" />
                  <span>{sentences.length} 句</span>
                </div>
              </div>
            )}
          </div>
          
          {/* 播放控制 */}
          <div className="flex items-center gap-2">
            {!playbackState.isPlaying ? (
              <Button onClick={startSpeech} className="flex items-center gap-2">
                <Play className="h-4 w-4" />
                开始播放
              </Button>
            ) : (
              <>
                {playbackState.isPaused ? (
                  <Button onClick={resumeSpeech} className="flex items-center gap-2">
                    <Play className="h-4 w-4" />
                    继续播放
                  </Button>
                ) : (
                  <Button onClick={pauseSpeech} variant="outline" className="flex items-center gap-2">
                    <Pause className="h-4 w-4" />
                    暂停
                  </Button>
                )}
                <Button onClick={stopSpeech} variant="outline" className="flex items-center gap-2">
                  <Square className="h-4 w-4" />
                  停止
                </Button>
              </>
            )}
            
            <Button onClick={exportAudio} variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              导出音频
            </Button>
          </div>
          
          {/* 播放进度 */}
          {playbackState.isPlaying && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-600">
                <span>播放进度</span>
                <span>
                  {Math.round((playbackState.currentPosition / playbackState.totalLength) * 100)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${(playbackState.currentPosition / playbackState.totalLength) * 100}%` 
                  }}
                />
              </div>
              <div className="text-sm text-gray-600">
                当前句子: {playbackState.currentSentence + 1} / {sentences.length}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 语音设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              语音设置
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 语音选择 */}
            <div className="space-y-2">
              <Label>选择语音</Label>
              <Select value={settings.voice} onValueChange={(value) => setSettings(prev => ({ ...prev, voice: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="选择语音" />
                </SelectTrigger>
                <SelectContent>
                  {voices.map((voice) => (
                    <SelectItem key={voice.voiceURI} value={voice.voiceURI}>
                      <div className="flex items-center justify-between w-full">
                        <span>{voice.name}</span>
                        <div className="flex items-center gap-2 ml-2">
                          <Badge variant="outline" className="text-xs">
                            {getLanguageName(voice.lang)}
                          </Badge>
                          {voice.localService && (
                            <Badge variant="secondary" className="text-xs">
                              本地
                            </Badge>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* 语速控制 */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label>语速</Label>
                <span className="text-sm text-gray-600">{settings.rate.toFixed(1)}x</span>
              </div>
              <Slider
                value={[settings.rate]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, rate: value }))}
                min={0.1}
                max={3}
                step={0.1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>慢速</span>
                <span>正常</span>
                <span>快速</span>
              </div>
            </div>
            
            {/* 音调控制 */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label>音调</Label>
                <span className="text-sm text-gray-600">{settings.pitch.toFixed(1)}</span>
              </div>
              <Slider
                value={[settings.pitch]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, pitch: value }))}
                min={0}
                max={2}
                step={0.1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>低音</span>
                <span>正常</span>
                <span>高音</span>
              </div>
            </div>
            
            {/* 音量控制 */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label>音量</Label>
                <span className="text-sm text-gray-600">{Math.round(settings.volume * 100)}%</span>
              </div>
              <Slider
                value={[settings.volume]}
                onValueChange={([value]) => setSettings(prev => ({ ...prev, volume: value }))}
                min={0}
                max={1}
                step={0.1}
                className="w-full"
              />
            </div>
            
            <div className="border-t my-4"></div>
            
            <Button onClick={resetSettings} variant="outline" className="w-full flex items-center gap-2">
              <RotateCcw className="h-4 w-4" />
              重置设置
            </Button>
          </CardContent>
        </Card>
        
        {/* 句子导航 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              句子导航
            </CardTitle>
          </CardHeader>
          <CardContent>
            {sentences.length > 0 ? (
              <div className="space-y-2 max-h-80 overflow-y-auto">
                {sentences.map((sentence, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      index === playbackState.currentSentence && playbackState.isPlaying
                        ? 'bg-blue-50 border-blue-200'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => jumpToSentence(index)}
                  >
                    <div className="flex items-start gap-2">
                      <Badge variant="outline" className="text-xs mt-1">
                        {index + 1}
                      </Badge>
                      <p className="text-sm flex-1">{sentence.trim()}</p>
                      {index === playbackState.currentSentence && playbackState.isPlaying && (
                        <Zap className="h-4 w-4 text-blue-600 mt-1" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>输入文本后将显示句子列表</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* 快捷操作 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            快捷操作
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              variant="outline"
              onClick={() => jumpToSentence(Math.max(0, playbackState.currentSentence - 1))}
              disabled={!sentences.length || playbackState.currentSentence === 0}
              className="flex items-center gap-2"
            >
              <SkipBack className="h-4 w-4" />
              上一句
            </Button>
            
            <Button
              variant="outline"
              onClick={() => jumpToSentence(Math.min(sentences.length - 1, playbackState.currentSentence + 1))}
              disabled={!sentences.length || playbackState.currentSentence >= sentences.length - 1}
              className="flex items-center gap-2"
            >
              <SkipForward className="h-4 w-4" />
              下一句
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setSettings(prev => ({ ...prev, rate: Math.max(0.1, prev.rate - 0.1) }))}
              className="flex items-center gap-2"
            >
              减速
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setSettings(prev => ({ ...prev, rate: Math.min(3, prev.rate + 0.1) }))}
              className="flex items-center gap-2"
            >
              加速
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
