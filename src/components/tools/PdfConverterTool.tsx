'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/use-toast'
import { 
  FileText, 
  Upload, 
  Download, 
  FileImage, 
  FileSpreadsheet,
  FileType,
  Loader2,
  CheckCircle,
  XCircle,
  FileIcon
} from 'lucide-react'

interface ConversionFile {
  id: string
  file: File
  status: 'pending' | 'converting' | 'completed' | 'error'
  progress: number
  outputUrl?: string
  outputName?: string
  error?: string
}

export default function PdfConverterTool() {
  const [files, setFiles] = useState<ConversionFile[]>([])
  const [conversionType, setConversionType] = useState<'to-pdf' | 'from-pdf'>('to-pdf')
  const [outputFormat, setOutputFormat] = useState<string>('jpg')
  const [isConverting, setIsConverting] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const { toast } = useToast()

  // 支持的转换格式
  const supportedFormats = {
    'to-pdf': {
      formats: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'png', 'txt', 'html'],
      accept: '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.txt,.html'
    },
    'from-pdf': {
      formats: ['jpg', 'png', 'docx', 'xlsx', 'pptx', 'txt', 'html'],
      accept: '.pdf'
    }
  }

  // 处理文件拖拽
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  // 处理文件放置
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const droppedFiles = Array.from(e.dataTransfer.files)
    handleFiles(droppedFiles)
  }, [])

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files)
      handleFiles(selectedFiles)
    }
  }

  // 处理文件
  const handleFiles = (selectedFiles: File[]) => {
    const newFiles: ConversionFile[] = selectedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'pending',
      progress: 0
    }))

    setFiles(prev => [...prev, ...newFiles])
  }

  // 移除文件
  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id))
  }

  // 模拟文件转换
  const convertFiles = async () => {
    setIsConverting(true)

    for (const file of files) {
      if (file.status !== 'pending') continue

      try {
        // 更新状态为转换中
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'converting' } : f
        ))

        // 模拟转换进度
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100))
          setFiles(prev => prev.map(f => 
            f.id === file.id ? { ...f, progress } : f
          ))
        }

        // 生成输出文件名
        const baseName = file.file.name.replace(/\.[^/.]+$/, '')
        const outputName = conversionType === 'to-pdf' 
          ? `${baseName}.pdf`
          : `${baseName}.${outputFormat}`

        // 模拟生成下载链接
        const outputUrl = URL.createObjectURL(file.file) // 实际应用中这里应该是转换后的文件

        // 更新状态为完成
        setFiles(prev => prev.map(f => 
          f.id === file.id 
            ? { ...f, status: 'completed', outputUrl, outputName } 
            : f
        ))

        toast({
          title: '转换成功',
          description: `${file.file.name} 已成功转换`,
        })
      } catch (error) {
        // 更新状态为错误
        setFiles(prev => prev.map(f => 
          f.id === file.id 
            ? { ...f, status: 'error', error: '转换失败，请重试' } 
            : f
        ))

        toast({
          title: '转换失败',
          description: `${file.file.name} 转换失败`,
          variant: 'destructive'
        })
      }
    }

    setIsConverting(false)
  }

  // 下载文件
  const downloadFile = (file: ConversionFile) => {
    if (file.outputUrl && file.outputName) {
      const a = document.createElement('a')
      a.href = file.outputUrl
      a.download = file.outputName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  }

  // 清空所有文件
  const clearAllFiles = () => {
    files.forEach(file => {
      if (file.outputUrl) {
        URL.revokeObjectURL(file.outputUrl)
      }
    })
    setFiles([])
  }

  // 获取文件图标
  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'doc':
      case 'docx':
        return <FileText className="w-8 h-8 text-blue-500" />
      case 'xls':
      case 'xlsx':
        return <FileSpreadsheet className="w-8 h-8 text-green-500" />
      case 'ppt':
      case 'pptx':
        return <FileType className="w-8 h-8 text-orange-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
        return <FileImage className="w-8 h-8 text-purple-500" />
      case 'pdf':
        return <FileText className="w-8 h-8 text-red-500" />
      default:
        return <FileIcon className="w-8 h-8 text-gray-500" />
    }
  }

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>PDF 转换器</CardTitle>
        <CardDescription>
          在线转换 PDF 文件，支持多种格式互转
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs value={conversionType} onValueChange={(v) => setConversionType(v as 'to-pdf' | 'from-pdf')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="to-pdf">转换为 PDF</TabsTrigger>
            <TabsTrigger value="from-pdf">从 PDF 转换</TabsTrigger>
          </TabsList>

          <TabsContent value="to-pdf" className="space-y-4">
            <div className="text-sm text-muted-foreground">
              支持格式：Word、Excel、PowerPoint、图片、文本、HTML
            </div>
          </TabsContent>

          <TabsContent value="from-pdf" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="output-format">输出格式</Label>
                <Select value={outputFormat} onValueChange={setOutputFormat}>
                  <SelectTrigger id="output-format">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="jpg">JPG 图片</SelectItem>
                    <SelectItem value="png">PNG 图片</SelectItem>
                    <SelectItem value="docx">Word 文档</SelectItem>
                    <SelectItem value="xlsx">Excel 表格</SelectItem>
                    <SelectItem value="pptx">PowerPoint 演示文稿</SelectItem>
                    <SelectItem value="txt">纯文本</SelectItem>
                    <SelectItem value="html">HTML 网页</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* 文件上传区域 */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive ? 'border-primary bg-primary/5' : 'border-gray-300'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <p className="text-lg mb-2">拖拽文件到此处或点击选择</p>
          <p className="text-sm text-muted-foreground mb-4">
            {conversionType === 'to-pdf' 
              ? '支持 Word、Excel、PowerPoint、图片等格式'
              : '仅支持 PDF 文件'
            }
          </p>
          <input
            type="file"
            className="hidden"
            id="file-input"
            multiple
            accept={supportedFormats[conversionType].accept}
            onChange={handleFileSelect}
          />
          <Button asChild>
            <label htmlFor="file-input" className="cursor-pointer">
              <Upload className="w-4 h-4 mr-2" />
              选择文件
            </label>
          </Button>
        </div>

        {/* 文件列表 */}
        {files.length > 0 && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">文件列表</h3>
              <Button variant="outline" size="sm" onClick={clearAllFiles}>
                清空列表
              </Button>
            </div>

            <div className="space-y-2">
              {files.map(file => (
                <div
                  key={file.id}
                  className="flex items-center gap-4 p-4 border rounded-lg"
                >
                  {getFileIcon(file.file.name)}
                  
                  <div className="flex-1">
                    <p className="font-medium">{file.file.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {(file.file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                    
                    {file.status === 'converting' && (
                      <Progress value={file.progress} className="mt-2" />
                    )}
                    
                    {file.status === 'error' && (
                      <p className="text-sm text-red-500 mt-1">{file.error}</p>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    {file.status === 'pending' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                      >
                        <XCircle className="w-4 h-4" />
                      </Button>
                    )}
                    
                    {file.status === 'converting' && (
                      <Loader2 className="w-5 h-5 animate-spin text-primary" />
                    )}
                    
                    {file.status === 'completed' && (
                      <>
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => downloadFile(file)}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          下载
                        </Button>
                      </>
                    )}
                    
                    {file.status === 'error' && (
                      <XCircle className="w-5 h-5 text-red-500" />
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* 转换按钮 */}
            {files.some(f => f.status === 'pending') && (
              <Button 
                className="w-full" 
                onClick={convertFiles}
                disabled={isConverting}
              >
                {isConverting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    转换中...
                  </>
                ) : (
                  <>
                    <FileText className="w-4 h-4 mr-2" />
                    开始转换
                  </>
                )}
              </Button>
            )}
          </div>
        )}

        {/* 功能说明 */}
        <div className="rounded-lg bg-muted p-4 space-y-2">
          <h4 className="font-medium">功能特点：</h4>
          <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
            <li>支持批量转换，一次处理多个文件</li>
            <li>保持原始文档格式和布局</li>
            <li>支持加密 PDF 文件的转换</li>
            <li>转换速度快，质量高</li>
            <li>所有文件在本地处理，保护隐私安全</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
