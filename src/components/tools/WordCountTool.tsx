'use client'

import { useState, useEffect } from 'react'
import { FileText, Hash, Type, Clock } from 'lucide-react'

interface WordCountStats {
  characters: number          // 总字符数
  charactersNoSpace: number   // 不含空格字符数
  words: number              // 单词数
  chineseChars: number       // 中文字符数
  lines: number              // 行数
  paragraphs: number         // 段落数
  readingTime: number        // 阅读时间（分钟）
}

export default function WordCountTool() {
  const [text, setText] = useState('')
  const [stats, setStats] = useState<WordCountStats>({
    characters: 0,
    charactersNoSpace: 0,
    words: 0,
    chineseChars: 0,
    lines: 0,
    paragraphs: 0,
    readingTime: 0,
  })

  useEffect(() => {
    if (!text) {
      setStats({
        characters: 0,
        charactersNoSpace: 0,
        words: 0,
        chineseChars: 0,
        lines: 0,
        paragraphs: 0,
        readingTime: 0,
      })
      return
    }

    // 计算各项统计数据
    const characters = text.length
    const charactersNoSpace = text.replace(/\s/g, '').length
    
    // 计算单词数（支持中英文混合）
    const englishWords = text.match(/[a-zA-Z]+/g) || []
    const chineseWords = text.match(/[\u4e00-\u9fa5]+/g) || []
    const words = englishWords.length + chineseWords.join('').length
    
    // 计算中文字符数
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length
    
    // 计算行数
    const lines = text.split('\n').length
    
    // 计算段落数（连续的非空行为一个段落）
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim()).length || 1
    
    // 计算阅读时间（假设每分钟阅读 300 字）
    const readingTime = Math.ceil(charactersNoSpace / 300)

    setStats({
      characters,
      charactersNoSpace,
      words,
      chineseChars,
      lines,
      paragraphs,
      readingTime,
    })
  }, [text])

  const StatCard = ({ icon: Icon, label, value }: { icon: any, label: string, value: number | string }) => (
    <div className="bg-white p-4 rounded-lg border border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Icon className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-600">{label}</span>
        </div>
        <span className="text-lg font-semibold">{value}</span>
      </div>
    </div>
  )

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">字数统计器</h2>
        <p className="text-gray-600">
          实时统计文本的字符数、单词数、行数等信息
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <StatCard icon={Type} label="总字符" value={stats.characters} />
        <StatCard icon={Type} label="不含空格" value={stats.charactersNoSpace} />
        <StatCard icon={Hash} label="单词数" value={stats.words} />
        <StatCard icon={Type} label="中文字符" value={stats.chineseChars} />
        <StatCard icon={FileText} label="行数" value={stats.lines} />
        <StatCard icon={FileText} label="段落数" value={stats.paragraphs} />
        <StatCard icon={Clock} label="阅读时间" value={`${stats.readingTime} 分钟`} />
      </div>

      {/* 文本输入区域 */}
      <div>
        <label htmlFor="text-input" className="block text-sm font-medium text-gray-700 mb-2">
          输入或粘贴文本
        </label>
        <textarea
          id="text-input"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="在这里输入或粘贴您的文本..."
          className="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          rows={12}
        />
      </div>

      {/* 功能按钮 */}
      <div className="mt-4 flex flex-wrap gap-2">
        <button
          onClick={() => setText('')}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          清空文本
        </button>
        <button
          onClick={() => navigator.clipboard.writeText(text)}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          复制文本
        </button>
        <button
          onClick={() => {
            const statsText = `字符数：${stats.characters}\n不含空格：${stats.charactersNoSpace}\n单词数：${stats.words}\n中文字符：${stats.chineseChars}\n行数：${stats.lines}\n段落数：${stats.paragraphs}\n阅读时间：${stats.readingTime} 分钟`
            navigator.clipboard.writeText(statsText)
          }}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          复制统计结果
        </button>
      </div>
    </div>
  )
}
