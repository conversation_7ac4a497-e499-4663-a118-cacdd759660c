'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Upload, Download, FileAudio, FileVideo, AlertCircle, Loader2, Volume2, Film, Music, Headphones } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface MediaFile {
  file: File
  name: string
  size: number
  type: string
  duration?: string
}

interface ConversionProgress {
  status: 'idle' | 'converting' | 'completed' | 'error'
  progress: number
  message: string
}

// 支持的媒体格式
const MEDIA_FORMATS = {
  video: {
    input: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v', 'mpg', 'mpeg', '3gp'],
    output: [
      { value: 'mp4', label: 'MP4', description: '最常用的视频格式' },
      { value: 'avi', label: 'AVI', description: 'Windows 视频格式' },
      { value: 'mov', label: 'MOV', description: 'QuickTime 格式' },
      { value: 'webm', label: 'WebM', description: 'Web 视频格式' },
      { value: 'mkv', label: 'MKV', description: '高质量视频格式' },
      { value: 'flv', label: 'FLV', description: 'Flash 视频格式' },
      { value: 'wmv', label: 'WMV', description: 'Windows Media 格式' },
      { value: 'gif', label: 'GIF', description: '动画图片格式' }
    ]
  },
  audio: {
    input: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus', 'ape', 'amr'],
    output: [
      { value: 'mp3', label: 'MP3', description: '最常用的音频格式' },
      { value: 'wav', label: 'WAV', description: '无损音频格式' },
      { value: 'flac', label: 'FLAC', description: '无损压缩格式' },
      { value: 'aac', label: 'AAC', description: '高质量压缩格式' },
      { value: 'ogg', label: 'OGG', description: '开源音频格式' },
      { value: 'm4a', label: 'M4A', description: 'Apple 音频格式' },
      { value: 'wma', label: 'WMA', description: 'Windows 音频格式' },
      { value: 'opus', label: 'OPUS', description: '高效音频编码' }
    ]
  }
}

// 质量选项
const QUALITY_OPTIONS = [
  { value: 'low', label: '低质量', description: '文件较小，质量一般' },
  { value: 'medium', label: '中等质量', description: '平衡文件大小和质量' },
  { value: 'high', label: '高质量', description: '文件较大，质量最佳' },
  { value: 'original', label: '原始质量', description: '保持原始质量' }
]

export default function MediaConverterTool() {
  const [mediaFile, setMediaFile] = useState<MediaFile | null>(null)
  const [mediaType, setMediaType] = useState<'video' | 'audio' | null>(null)
  const [outputFormat, setOutputFormat] = useState<string>('')
  const [quality, setQuality] = useState<string>('medium')
  const [conversionProgress, setConversionProgress] = useState<ConversionProgress>({
    status: 'idle',
    progress: 0,
    message: ''
  })
  const [isDragging, setIsDragging] = useState(false)
  const { toast } = useToast()

  // 检测文件类型
  const detectMediaType = (file: File): 'video' | 'audio' | null => {
    const extension = file.name.split('.').pop()?.toLowerCase() || ''
    
    if (MEDIA_FORMATS.video.input.includes(extension)) {
      return 'video'
    } else if (MEDIA_FORMATS.audio.input.includes(extension)) {
      return 'audio'
    }
    
    return null
  }

  // 处理文件选择
  const handleFileSelect = useCallback((file: File) => {
    const type = detectMediaType(file)
    
    if (!type) {
      toast({
        title: '不支持的文件格式',
        description: '请选择支持的音频或视频文件',
        variant: 'destructive'
      })
      return
    }

    // 文件大小限制：视频500MB，音频100MB
    const maxSize = type === 'video' ? 500 * 1024 * 1024 : 100 * 1024 * 1024
    if (file.size > maxSize) {
      toast({
        title: '文件过大',
        description: `${type === 'video' ? '视频' : '音频'}文件大小不能超过 ${type === 'video' ? '500MB' : '100MB'}`,
        variant: 'destructive'
      })
      return
    }

    setMediaFile({
      file,
      name: file.name,
      size: file.size,
      type: file.type
    })
    setMediaType(type)
    setOutputFormat('')
    setConversionProgress({
      status: 'idle',
      progress: 0,
      message: ''
    })
  }, [toast])

  // 处理拖拽
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  // 处理文件输入
  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 模拟转换过程
  const simulateConversion = async () => {
    if (!mediaFile || !outputFormat) return

    setConversionProgress({
      status: 'converting',
      progress: 0,
      message: '准备转换...'
    })

    // 模拟转换进度
    const steps = [
      { progress: 10, message: '读取文件信息...' },
      { progress: 20, message: '解码媒体流...' },
      { progress: 40, message: '转换格式中...' },
      { progress: 60, message: '编码输出文件...' },
      { progress: 80, message: '优化文件质量...' },
      { progress: 95, message: '完成最后处理...' },
      { progress: 100, message: '转换完成！' }
    ]

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 800))
      setConversionProgress({
        status: 'converting',
        progress: step.progress,
        message: step.message
      })
    }

    setConversionProgress({
      status: 'completed',
      progress: 100,
      message: '转换完成！'
    })

    // 模拟下载
    setTimeout(() => {
      const outputFileName = mediaFile.name.replace(/\.[^/.]+$/, `.${outputFormat}`)
      toast({
        title: '转换成功',
        description: `文件 ${outputFileName} 已准备好下载`
      })
    }, 500)
  }

  // 开始转换
  const handleConvert = () => {
    if (!mediaFile || !outputFormat) {
      toast({
        title: '请选择输出格式',
        description: '选择要转换的目标格式',
        variant: 'destructive'
      })
      return
    }

    simulateConversion()
  }

  // 重置
  const handleReset = () => {
    setMediaFile(null)
    setMediaType(null)
    setOutputFormat('')
    setQuality('medium')
    setConversionProgress({
      status: 'idle',
      progress: 0,
      message: ''
    })
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Film className="h-5 w-5" />
          音视频转换器
        </CardTitle>
        <CardDescription>
          在线转换音频和视频文件格式，支持多种常见格式互转
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {!mediaFile ? (
          // 文件上传区域
          <div
            className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
              isDragging ? 'border-primary bg-primary/5' : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="flex flex-col items-center gap-4">
              <div className="flex gap-4">
                <FileVideo className="h-12 w-12 text-gray-400" />
                <FileAudio className="h-12 w-12 text-gray-400" />
              </div>
              <div>
                <p className="text-lg font-medium">拖拽音频或视频文件到这里</p>
                <p className="text-sm text-gray-500 mt-1">或者点击选择文件</p>
              </div>
              <div className="text-xs text-gray-400 space-y-1">
                <p>支持的视频格式：MP4, AVI, MOV, WMV, FLV, MKV, WebM 等</p>
                <p>支持的音频格式：MP3, WAV, FLAC, AAC, OGG, WMA, M4A 等</p>
                <p>视频文件最大 500MB，音频文件最大 100MB</p>
              </div>
              <input
                type="file"
                accept="video/*,audio/*"
                onChange={handleFileInput}
                className="hidden"
                id="media-input"
              />
              <label htmlFor="media-input">
                <Button asChild>
                  <span>
                    <Upload className="h-4 w-4 mr-2" />
                    选择文件
                  </span>
                </Button>
              </label>
            </div>
          </div>
        ) : (
          <>
            {/* 文件信息 */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  {mediaType === 'video' ? (
                    <Film className="h-8 w-8 text-blue-500 mt-1" />
                  ) : (
                    <Music className="h-8 w-8 text-green-500 mt-1" />
                  )}
                  <div>
                    <p className="font-medium">{mediaFile.name}</p>
                    <p className="text-sm text-gray-500">
                      {formatFileSize(mediaFile.size)} • {mediaType === 'video' ? '视频文件' : '音频文件'}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleReset}
                  disabled={conversionProgress.status === 'converting'}
                >
                  更换文件
                </Button>
              </div>
            </div>

            {/* 转换选项 */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="output-format">输出格式</Label>
                <Select value={outputFormat} onValueChange={setOutputFormat}>
                  <SelectTrigger id="output-format" className="mt-2">
                    <SelectValue placeholder="选择输出格式" />
                  </SelectTrigger>
                  <SelectContent>
                    {mediaType && MEDIA_FORMATS[mediaType].output.map((format) => (
                      <SelectItem key={format.value} value={format.value}>
                        <div className="flex items-center justify-between w-full">
                          <span className="font-medium">{format.label}</span>
                          <span className="text-sm text-gray-500 ml-4">{format.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="quality">转换质量</Label>
                <Select value={quality} onValueChange={setQuality}>
                  <SelectTrigger id="quality" className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {QUALITY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center justify-between w-full">
                          <span className="font-medium">{option.label}</span>
                          <span className="text-sm text-gray-500 ml-4">{option.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 转换进度 */}
            {conversionProgress.status !== 'idle' && (
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">{conversionProgress.message}</span>
                  <span className="font-medium">{conversionProgress.progress}%</span>
                </div>
                <Progress value={conversionProgress.progress} className="h-2" />
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex gap-3">
              <Button
                onClick={handleConvert}
                disabled={!outputFormat || conversionProgress.status === 'converting'}
                className="flex-1"
              >
                {conversionProgress.status === 'converting' ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    转换中...
                  </>
                ) : (
                  '开始转换'
                )}
              </Button>
              {conversionProgress.status === 'completed' && (
                <Button variant="outline" className="flex-1">
                  <Download className="h-4 w-4 mr-2" />
                  下载文件
                </Button>
              )}
            </div>
          </>
        )}

        {/* 提示信息 */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            提示：这是一个演示工具，实际转换需要后端服务支持。在实际应用中，可以使用 FFmpeg 等工具进行音视频转换。
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )
}
