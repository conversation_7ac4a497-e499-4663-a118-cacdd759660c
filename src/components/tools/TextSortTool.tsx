'use client'

import React, { useState, useCallback } from 'react'
import { <PERSON>U<PERSON>Down, <PERSON><PERSON><PERSON>, <PERSON>Down, Copy, Check, Upload, Download, Shuffle, Hash, Calendar, Type, Info } from 'lucide-react'

interface SortOptions {
  order: 'asc' | 'desc' | 'random'
  type: 'alphabetical' | 'numerical' | 'length' | 'date'
  caseSensitive: boolean
  removeDuplicates: boolean
  reverseOrder: boolean
  trimLines: boolean
}

interface SortHistory {
  input: string
  output: string
  options: SortOptions
  timestamp: Date
}

export default function TextSortTool() {
  const [inputText, setInputText] = useState('')
  const [outputText, setOutputText] = useState('')
  const [sortOptions, setSortOptions] = useState<SortOptions>({
    order: 'asc',
    type: 'alphabetical',
    caseSensitive: false,
    removeDuplicates: false,
    reverseOrder: false,
    trimLines: true,
  })
  const [copied, setCopied] = useState(false)
  const [history, setHistory] = useState<SortHistory[]>([])
  const [delimiter, setDelimiter] = useState('newline')
  const [customDelimiter, setCustomDelimiter] = useState(',')
  
  // 执行排序
  const performSort = useCallback(() => {
    if (!inputText.trim()) {
      setOutputText('')
      return
    }
    
    // 根据分隔符分割文本
    let lines: string[]
    if (delimiter === 'newline') {
      lines = inputText.split('\n')
    } else if (delimiter === 'comma') {
      lines = inputText.split(',')
    } else if (delimiter === 'space') {
      lines = inputText.split(/\s+/)
    } else if (delimiter === 'tab') {
      lines = inputText.split('\t')
    } else {
      lines = inputText.split(customDelimiter)
    }
    
    // 修剪空白
    if (sortOptions.trimLines) {
      lines = lines.map(line => line.trim()).filter(line => line.length > 0)
    }
    
    // 移除重复项
    if (sortOptions.removeDuplicates) {
      lines = Array.from(new Set(lines))
    }
    
    // 执行排序
    let sortedLines = [...lines]
    
    if (sortOptions.order === 'random') {
      // 随机排序
      for (let i = sortedLines.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [sortedLines[i], sortedLines[j]] = [sortedLines[j], sortedLines[i]]
      }
    } else {
      // 根据类型排序
      sortedLines.sort((a, b) => {
        let compareA = a
        let compareB = b
        
        // 大小写处理
        if (!sortOptions.caseSensitive && sortOptions.type === 'alphabetical') {
          compareA = a.toLowerCase()
          compareB = b.toLowerCase()
        }
        
        // 根据类型比较
        switch (sortOptions.type) {
          case 'alphabetical':
            return compareA.localeCompare(compareB)
          
          case 'numerical':
            const numA = parseFloat(a.replace(/[^\d.-]/g, '')) || 0
            const numB = parseFloat(b.replace(/[^\d.-]/g, '')) || 0
            return numA - numB
          
          case 'length':
            return a.length - b.length
          
          case 'date':
            const dateA = new Date(a).getTime() || 0
            const dateB = new Date(b).getTime() || 0
            return dateA - dateB
          
          default:
            return 0
        }
      })
      
      // 降序处理
      if (sortOptions.order === 'desc') {
        sortedLines.reverse()
      }
    }
    
    // 反转顺序
    if (sortOptions.reverseOrder) {
      sortedLines.reverse()
    }
    
    // 重新组合文本
    let result: string
    if (delimiter === 'newline') {
      result = sortedLines.join('\n')
    } else if (delimiter === 'comma') {
      result = sortedLines.join(', ')
    } else if (delimiter === 'space') {
      result = sortedLines.join(' ')
    } else if (delimiter === 'tab') {
      result = sortedLines.join('\t')
    } else {
      result = sortedLines.join(customDelimiter)
    }
    
    setOutputText(result)
    
    // 添加到历史记录
    const newHistory: SortHistory = {
      input: inputText,
      output: result,
      options: { ...sortOptions },
      timestamp: new Date(),
    }
    setHistory(prev => [newHistory, ...prev.slice(0, 9)])
  }, [inputText, sortOptions, delimiter, customDelimiter])
  
  // 复制结果
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(outputText)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }
  
  // 上传文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputText(content)
    }
    reader.readAsText(file)
  }
  
  // 下载结果
  const downloadResult = () => {
    if (!outputText) return
    
    const blob = new Blob([outputText], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `sorted_text_${new Date().getTime()}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }
  
  // 应用历史记录
  const applyHistory = (item: SortHistory) => {
    setInputText(item.input)
    setOutputText(item.output)
    setSortOptions(item.options)
  }
  
  // 预设示例
  const examples = [
    {
      name: '字母排序',
      text: 'banana\napple\ncherry\ndate\nelderberry',
      options: { order: 'asc', type: 'alphabetical' } as Partial<SortOptions>,
    },
    {
      name: '数字排序',
      text: '100\n25\n3\n456\n78\n9',
      options: { order: 'asc', type: 'numerical' } as Partial<SortOptions>,
    },
    {
      name: '长度排序',
      text: 'short\nmedium length\nvery long text here\ntiny\nmoderate',
      options: { order: 'asc', type: 'length' } as Partial<SortOptions>,
    },
    {
      name: '日期排序',
      text: '2024-03-15\n2023-12-01\n2024-01-20\n2023-11-30\n2024-02-28',
      options: { order: 'asc', type: 'date' } as Partial<SortOptions>,
    },
  ]
  
  // 应用示例
  const applyExample = (example: typeof examples[0]) => {
    setInputText(example.text)
    setSortOptions(prev => ({ ...prev, ...example.options }))
  }
  
  // 监听选项变化自动排序
  React.useEffect(() => {
    performSort()
  }, [inputText, sortOptions, delimiter, customDelimiter, performSort])
  
  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本排序工具</h2>
        <p className="text-gray-600">
          对文本行进行排序，支持字母、数字、长度、日期等多种排序方式
        </p>
      </div>
      
      {/* 示例 */}
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-3">快速示例</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {examples.map((example) => (
            <button
              key={example.name}
              onClick={() => applyExample(example)}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              {example.name}
            </button>
          ))}
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">输入文本</label>
            <label className="text-sm text-blue-500 hover:text-blue-600 cursor-pointer">
              <input
                type="file"
                accept=".txt"
                onChange={handleFileUpload}
                className="hidden"
              />
              <span className="flex items-center gap-1">
                <Upload className="w-4 h-4" />
                上传文件
              </span>
            </label>
          </div>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="请输入要排序的文本，每行一个项目..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
          />
          <div className="mt-2 text-sm text-gray-500">
            行数: {inputText.split('\n').filter(line => line.trim()).length}
          </div>
        </div>
        
        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">排序结果</label>
            <div className="flex gap-2">
              <button
                onClick={copyToClipboard}
                disabled={!outputText}
                className="text-sm text-blue-500 hover:text-blue-600 disabled:text-gray-400 flex items-center gap-1"
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    复制
                  </>
                )}
              </button>
              <button
                onClick={downloadResult}
                disabled={!outputText}
                className="text-sm text-blue-500 hover:text-blue-600 disabled:text-gray-400 flex items-center gap-1"
              >
                <Download className="w-4 h-4" />
                下载
              </button>
            </div>
          </div>
          <textarea
            value={outputText}
            readOnly
            placeholder="排序结果将显示在这里..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none bg-gray-50 font-mono text-sm"
          />
          <div className="mt-2 text-sm text-gray-500">
            行数: {outputText.split('\n').filter(line => line.trim()).length}
          </div>
        </div>
      </div>
      
      {/* 排序选项 */}
      <div className="mt-6 p-6 bg-white border border-gray-200 rounded-lg">
        <h3 className="font-semibold mb-4">排序选项</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 排序顺序 */}
          <div>
            <label className="block text-sm font-medium mb-2">排序顺序</label>
            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="order"
                  value="asc"
                  checked={sortOptions.order === 'asc'}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, order: e.target.value as 'asc' }))}
                  className="text-blue-500"
                />
                <ArrowUp className="w-4 h-4" />
                <span>升序 (A-Z, 0-9)</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="order"
                  value="desc"
                  checked={sortOptions.order === 'desc'}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, order: e.target.value as 'desc' }))}
                  className="text-blue-500"
                />
                <ArrowDown className="w-4 h-4" />
                <span>降序 (Z-A, 9-0)</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="order"
                  value="random"
                  checked={sortOptions.order === 'random'}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, order: e.target.value as 'random' }))}
                  className="text-blue-500"
                />
                <Shuffle className="w-4 h-4" />
                <span>随机排序</span>
              </label>
            </div>
          </div>
          
          {/* 排序类型 */}
          <div>
            <label className="block text-sm font-medium mb-2">排序类型</label>
            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="type"
                  value="alphabetical"
                  checked={sortOptions.type === 'alphabetical'}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, type: e.target.value as 'alphabetical' }))}
                  className="text-blue-500"
                />
                <Type className="w-4 h-4" />
                <span>字母顺序</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="type"
                  value="numerical"
                  checked={sortOptions.type === 'numerical'}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, type: e.target.value as 'numerical' }))}
                  className="text-blue-500"
                />
                <Hash className="w-4 h-4" />
                <span>数字大小</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="type"
                  value="length"
                  checked={sortOptions.type === 'length'}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, type: e.target.value as 'length' }))}
                  className="text-blue-500"
                />
                <ArrowUpDown className="w-4 h-4" />
                <span>文本长度</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="type"
                  value="date"
                  checked={sortOptions.type === 'date'}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, type: e.target.value as 'date' }))}
                  className="text-blue-500"
                />
                <Calendar className="w-4 h-4" />
                <span>日期时间</span>
              </label>
            </div>
          </div>
          
          {/* 其他选项 */}
          <div>
            <label className="block text-sm font-medium mb-2">其他选项</label>
            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={sortOptions.caseSensitive}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, caseSensitive: e.target.checked }))}
                  className="text-blue-500"
                />
                <span>区分大小写</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={sortOptions.removeDuplicates}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, removeDuplicates: e.target.checked }))}
                  className="text-blue-500"
                />
                <span>移除重复项</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={sortOptions.reverseOrder}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, reverseOrder: e.target.checked }))}
                  className="text-blue-500"
                />
                <span>反转结果</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={sortOptions.trimLines}
                  onChange={(e) => setSortOptions(prev => ({ ...prev, trimLines: e.target.checked }))}
                  className="text-blue-500"
                />
                <span>修剪空白</span>
              </label>
            </div>
          </div>
        </div>
        
        {/* 分隔符选项 */}
        <div className="mt-6">
          <label className="block text-sm font-medium mb-2">分隔符</label>
          <div className="flex flex-wrap gap-3">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="newline"
                checked={delimiter === 'newline'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>换行符</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="comma"
                checked={delimiter === 'comma'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>逗号</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="space"
                checked={delimiter === 'space'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>空格</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="tab"
                checked={delimiter === 'tab'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>制表符</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="delimiter"
                value="custom"
                checked={delimiter === 'custom'}
                onChange={(e) => setDelimiter(e.target.value)}
                className="text-blue-500"
              />
              <span>自定义:</span>
              <input
                type="text"
                value={customDelimiter}
                onChange={(e) => setCustomDelimiter(e.target.value)}
                disabled={delimiter !== 'custom'}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm disabled:bg-gray-100"
                placeholder="分隔符"
              />
            </label>
          </div>
        </div>
      </div>
      
      {/* 历史记录 */}
      {history.length > 0 && (
        <div className="mt-6">
          <h3 className="font-semibold mb-3">历史记录</h3>
          <div className="space-y-2">
            {history.map((item, index) => (
              <div
                key={index}
                className="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => applyHistory(item)}
              >
                <div className="flex items-center justify-between">
                  <div className="text-sm">
                    <span className="font-medium">
                      {item.options.type === 'alphabetical' && '字母排序'}
                      {item.options.type === 'numerical' && '数字排序'}
                      {item.options.type === 'length' && '长度排序'}
                      {item.options.type === 'date' && '日期排序'}
                    </span>
                    <span className="text-gray-500 ml-2">
                      {item.options.order === 'asc' && '升序'}
                      {item.options.order === 'desc' && '降序'}
                      {item.options.order === 'random' && '随机'}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(item.timestamp).toLocaleTimeString()}
                  </div>
                </div>
                <div className="text-xs text-gray-600 mt-1 truncate">
                  {item.input.substring(0, 50)}...
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持多种排序方式：字母、数字、长度、日期</li>
              <li>可选择升序、降序或随机排序</li>
              <li>支持自定义分隔符，处理不同格式的文本</li>
              <li>可以移除重复项和修剪空白字符</li>
              <li>历史记录功能方便重复使用之前的排序设置</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
