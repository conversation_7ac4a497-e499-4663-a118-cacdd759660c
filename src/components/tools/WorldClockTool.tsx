'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Clock, Globe, Plus, X, MapPin, Sun, Moon } from 'lucide-react'

// 预定义的时区列表
const COMMON_TIMEZONES = [
  { label: '北京 (UTC+8)', value: 'Asia/Shanghai', flag: '🇨🇳' },
  { label: '东京 (UTC+9)', value: 'Asia/Tokyo', flag: '🇯🇵' },
  { label: '纽约 (UTC-5)', value: 'America/New_York', flag: '🇺🇸' },
  { label: '洛杉矶 (UTC-8)', value: 'America/Los_Angeles', flag: '🇺🇸' },
  { label: '伦敦 (UTC+0)', value: 'Europe/London', flag: '🇬🇧' },
  { label: '巴黎 (UTC+1)', value: 'Europe/Paris', flag: '🇫🇷' },
  { label: '悉尼 (UTC+11)', value: 'Australia/Sydney', flag: '🇦🇺' },
  { label: '迪拜 (UTC+4)', value: 'Asia/Dubai', flag: '🇦🇪' },
  { label: '新加坡 (UTC+8)', value: 'Asia/Singapore', flag: '🇸🇬' },
  { label: '首尔 (UTC+9)', value: 'Asia/Seoul', flag: '🇰🇷' },
  { label: '莫斯科 (UTC+3)', value: 'Europe/Moscow', flag: '🇷🇺' },
  { label: '孟买 (UTC+5:30)', value: 'Asia/Kolkata', flag: '🇮🇳' },
  { label: '开罗 (UTC+2)', value: 'Africa/Cairo', flag: '🇪🇬' },
  { label: '圣保罗 (UTC-3)', value: 'America/Sao_Paulo', flag: '🇧🇷' },
  { label: '墨西哥城 (UTC-6)', value: 'America/Mexico_City', flag: '🇲🇽' },
  { label: '温哥华 (UTC-8)', value: 'America/Vancouver', flag: '🇨🇦' },
  { label: '芝加哥 (UTC-6)', value: 'America/Chicago', flag: '🇺🇸' },
  { label: '迈阿密 (UTC-5)', value: 'America/Miami', flag: '🇺🇸' },
  { label: '柏林 (UTC+1)', value: 'Europe/Berlin', flag: '🇩🇪' },
  { label: '罗马 (UTC+1)', value: 'Europe/Rome', flag: '🇮🇹' },
]

interface TimeZoneInfo {
  id: string
  timezone: string
  label: string
  flag: string
  time: Date
  offset: string
  isDaytime: boolean
}

export default function WorldClockTool() {
  const [selectedTimezones, setSelectedTimezones] = useState<TimeZoneInfo[]>([
    {
      id: '1',
      timezone: 'Asia/Shanghai',
      label: '北京',
      flag: '🇨🇳',
      time: new Date(),
      offset: '',
      isDaytime: true,
    },
    {
      id: '2',
      timezone: 'America/New_York',
      label: '纽约',
      flag: '🇺🇸',
      time: new Date(),
      offset: '',
      isDaytime: true,
    },
    {
      id: '3',
      timezone: 'Europe/London',
      label: '伦敦',
      flag: '🇬🇧',
      time: new Date(),
      offset: '',
      isDaytime: true,
    },
  ])
  const [newTimezone, setNewTimezone] = useState<string>('')
  const [currentTime, setCurrentTime] = useState<Date>(new Date())

  // 更新时间
  const updateTimes = useCallback(() => {
    const now = new Date()
    setCurrentTime(now)
    
    setSelectedTimezones(prev => prev.map(tz => {
      const timeInZone = new Date(now.toLocaleString('en-US', { timeZone: tz.timezone }))
      const hour = timeInZone.getHours()
      const isDaytime = hour >= 6 && hour < 18
      
      // 计算时区偏移
      const utcTime = new Date(now.toISOString())
      const localTime = new Date(now.toLocaleString('en-US', { timeZone: tz.timezone }))
      const offsetMinutes = (localTime.getTime() - utcTime.getTime()) / (1000 * 60)
      const offsetHours = Math.floor(Math.abs(offsetMinutes) / 60)
      const offsetMins = Math.abs(offsetMinutes) % 60
      const offsetSign = offsetMinutes >= 0 ? '+' : '-'
      const offsetStr = `UTC${offsetSign}${offsetHours}${offsetMins > 0 ? `:${offsetMins.toString().padStart(2, '0')}` : ''}`
      
      return {
        ...tz,
        time: timeInZone,
        offset: offsetStr,
        isDaytime,
      }
    }))
  }, [])

  // 定时更新时间
  useEffect(() => {
    updateTimes()
    const interval = setInterval(updateTimes, 1000)
    return () => clearInterval(interval)
  }, [updateTimes])

  // 添加时区
  const handleAddTimezone = useCallback(() => {
    if (!newTimezone) return
    
    const timezoneData = COMMON_TIMEZONES.find(tz => tz.value === newTimezone)
    if (!timezoneData) return
    
    const newId = Date.now().toString()
    const newTz: TimeZoneInfo = {
      id: newId,
      timezone: timezoneData.value,
      label: timezoneData.label.split(' (')[0],
      flag: timezoneData.flag,
      time: new Date(),
      offset: '',
      isDaytime: true,
    }
    
    setSelectedTimezones(prev => [...prev, newTz])
    setNewTimezone('')
  }, [newTimezone])

  // 移除时区
  const handleRemoveTimezone = useCallback((id: string) => {
    setSelectedTimezones(prev => prev.filter(tz => tz.id !== id))
  }, [])

  // 格式化时间
  const formatTime = useCallback((date: Date, timezone: string) => {
    return date.toLocaleString('zh-CN', {
      timeZone: timezone,
      hour12: false,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
  }, [])

  // 格式化日期
  const formatDate = useCallback((date: Date, timezone: string) => {
    return date.toLocaleDateString('zh-CN', {
      timeZone: timezone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }, [])

  // 计算两个时区的时差
  const getTimeDifference = useCallback((timezone1: string, timezone2: string) => {
    const now = new Date()
    const time1 = new Date(now.toLocaleString('en-US', { timeZone: timezone1 }))
    const time2 = new Date(now.toLocaleString('en-US', { timeZone: timezone2 }))
    const diffHours = (time1.getTime() - time2.getTime()) / (1000 * 60 * 60)
    
    if (diffHours === 0) return '相同时区'
    const sign = diffHours > 0 ? '快' : '慢'
    const hours = Math.abs(diffHours)
    return `${sign} ${hours} 小时`
  }, [])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            世界时钟
          </CardTitle>
          <CardDescription>
            查看全球不同时区的当前时间，支持添加和管理多个城市
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 添加新时区 */}
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="timezone">添加时区</Label>
              <Select value={newTimezone} onValueChange={setNewTimezone}>
                <SelectTrigger>
                  <SelectValue placeholder="选择时区" />
                </SelectTrigger>
                <SelectContent>
                  {COMMON_TIMEZONES.filter(tz => 
                    !selectedTimezones.some(selected => selected.timezone === tz.value)
                  ).map((timezone) => (
                    <SelectItem key={timezone.value} value={timezone.value}>
                      <span className="flex items-center gap-2">
                        <span>{timezone.flag}</span>
                        <span>{timezone.label}</span>
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button onClick={handleAddTimezone} disabled={!newTimezone}>
              <Plus className="w-4 h-4 mr-2" />
              添加
            </Button>
          </div>

          {/* 时区列表 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {selectedTimezones.map((tz) => (
              <Card key={tz.id} className="relative">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{tz.flag}</span>
                      <div>
                        <CardTitle className="text-lg">{tz.label}</CardTitle>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Badge variant="outline" className="text-xs">
                            {tz.offset}
                          </Badge>
                          {tz.isDaytime ? (
                            <Sun className="w-3 h-3 text-yellow-500" />
                          ) : (
                            <Moon className="w-3 h-3 text-blue-500" />
                          )}
                        </div>
                      </div>
                    </div>
                    {selectedTimezones.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveTimezone(tz.id)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-2xl font-mono font-bold">
                      {formatTime(tz.time, tz.timezone).split(' ')[1]}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatDate(tz.time, tz.timezone)}
                    </div>
                    {selectedTimezones.length > 1 && selectedTimezones[0].id !== tz.id && (
                      <div className="text-xs text-muted-foreground">
                        与 {selectedTimezones[0].label} {getTimeDifference(tz.timezone, selectedTimezones[0].timezone)}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* 当前本地时间 */}
          <Card className="bg-muted/50">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Clock className="w-5 h-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">本地时间</div>
                    <div className="text-sm text-muted-foreground">
                      {Intl.DateTimeFormat().resolvedOptions().timeZone}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xl font-mono font-bold">
                    {currentTime.toLocaleTimeString('zh-CN', { hour12: false })}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {currentTime.toLocaleDateString('zh-CN', { 
                      weekday: 'short',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 使用说明 */}
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">使用说明</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 时间会自动更新，每秒刷新一次</li>
              <li>• 可以添加多个时区进行对比</li>
              <li>• 显示白天/夜晚状态和时区偏移</li>
              <li>• 计算与第一个时区的时间差</li>
              <li>• 支持全球主要城市和地区</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
