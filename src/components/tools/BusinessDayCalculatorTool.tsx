'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Calendar, Calculator, Plus, Minus, Clock, MapPin, History, Download, Trash2 } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface BusinessDayResult {
  startDate: string
  endDate: string
  totalDays: number
  businessDays: number
  weekendDays: number
  holidays: number
  region: string
  calculation: string
}

interface Holiday {
  date: string
  name: string
  type: 'national' | 'regional' | 'custom'
}

interface CustomDate {
  date: string
  type: 'holiday' | 'workday'
  name: string
}

// 各国节假日数据
const HOLIDAYS: Record<string, Holiday[]> = {
  'cn': [
    { date: '2024-01-01', name: '元旦', type: 'national' },
    { date: '2024-02-10', name: '春节', type: 'national' },
    { date: '2024-02-11', name: '春节', type: 'national' },
    { date: '2024-02-12', name: '春节', type: 'national' },
    { date: '2024-04-04', name: '清明节', type: 'national' },
    { date: '2024-05-01', name: '劳动节', type: 'national' },
    { date: '2024-06-10', name: '端午节', type: 'national' },
    { date: '2024-09-15', name: '中秋节', type: 'national' },
    { date: '2024-10-01', name: '国庆节', type: 'national' },
    { date: '2024-10-02', name: '国庆节', type: 'national' },
    { date: '2024-10-03', name: '国庆节', type: 'national' },
    { date: '2025-01-01', name: '元旦', type: 'national' },
    { date: '2025-01-29', name: '春节', type: 'national' },
    { date: '2025-01-30', name: '春节', type: 'national' },
    { date: '2025-01-31', name: '春节', type: 'national' },
    { date: '2025-04-05', name: '清明节', type: 'national' },
    { date: '2025-05-01', name: '劳动节', type: 'national' },
    { date: '2025-05-31', name: '端午节', type: 'national' },
    { date: '2025-10-01', name: '国庆节', type: 'national' },
    { date: '2025-10-02', name: '国庆节', type: 'national' },
    { date: '2025-10-03', name: '国庆节', type: 'national' },
  ],
  'us': [
    { date: '2024-01-01', name: 'New Year\'s Day', type: 'national' },
    { date: '2024-01-15', name: 'Martin Luther King Jr. Day', type: 'national' },
    { date: '2024-02-19', name: 'Presidents\' Day', type: 'national' },
    { date: '2024-05-27', name: 'Memorial Day', type: 'national' },
    { date: '2024-07-04', name: 'Independence Day', type: 'national' },
    { date: '2024-09-02', name: 'Labor Day', type: 'national' },
    { date: '2024-10-14', name: 'Columbus Day', type: 'national' },
    { date: '2024-11-11', name: 'Veterans Day', type: 'national' },
    { date: '2024-11-28', name: 'Thanksgiving', type: 'national' },
    { date: '2024-12-25', name: 'Christmas Day', type: 'national' },
    { date: '2025-01-01', name: 'New Year\'s Day', type: 'national' },
    { date: '2025-01-20', name: 'Martin Luther King Jr. Day', type: 'national' },
    { date: '2025-02-17', name: 'Presidents\' Day', type: 'national' },
    { date: '2025-05-26', name: 'Memorial Day', type: 'national' },
    { date: '2025-07-04', name: 'Independence Day', type: 'national' },
    { date: '2025-09-01', name: 'Labor Day', type: 'national' },
    { date: '2025-10-13', name: 'Columbus Day', type: 'national' },
    { date: '2025-11-11', name: 'Veterans Day', type: 'national' },
    { date: '2025-11-27', name: 'Thanksgiving', type: 'national' },
    { date: '2025-12-25', name: 'Christmas Day', type: 'national' },
  ],
  'jp': [
    { date: '2024-01-01', name: '元日', type: 'national' },
    { date: '2024-01-08', name: '成人の日', type: 'national' },
    { date: '2024-02-11', name: '建国記念の日', type: 'national' },
    { date: '2024-02-23', name: '天皇誕生日', type: 'national' },
    { date: '2024-03-20', name: '春分の日', type: 'national' },
    { date: '2024-04-29', name: '昭和の日', type: 'national' },
    { date: '2024-05-03', name: '憲法記念日', type: 'national' },
    { date: '2024-05-04', name: 'みどりの日', type: 'national' },
    { date: '2024-05-05', name: 'こどもの日', type: 'national' },
    { date: '2024-07-15', name: '海の日', type: 'national' },
    { date: '2024-08-11', name: '山の日', type: 'national' },
    { date: '2024-09-16', name: '敬老の日', type: 'national' },
    { date: '2024-09-22', name: '秋分の日', type: 'national' },
    { date: '2024-10-14', name: 'スポーツの日', type: 'national' },
    { date: '2024-11-03', name: '文化の日', type: 'national' },
    { date: '2024-11-23', name: '勤労感謝の日', type: 'national' },
    { date: '2025-01-01', name: '元日', type: 'national' },
    { date: '2025-01-13', name: '成人の日', type: 'national' },
    { date: '2025-02-11', name: '建国記念の日', type: 'national' },
    { date: '2025-02-23', name: '天皇誕生日', type: 'national' },
    { date: '2025-03-20', name: '春分の日', type: 'national' },
    { date: '2025-04-29', name: '昭和の日', type: 'national' },
    { date: '2025-05-03', name: '憲法記念日', type: 'national' },
    { date: '2025-05-04', name: 'みどりの日', type: 'national' },
    { date: '2025-05-05', name: 'こどもの日', type: 'national' },
    { date: '2025-07-21', name: '海の日', type: 'national' },
    { date: '2025-08-11', name: '山の日', type: 'national' },
    { date: '2025-09-15', name: '敬老の日', type: 'national' },
    { date: '2025-09-23', name: '秋分の日', type: 'national' },
    { date: '2025-10-13', name: 'スポーツの日', type: 'national' },
    { date: '2025-11-03', name: '文化の日', type: 'national' },
    { date: '2025-11-23', name: '勤労感謝の日', type: 'national' },
  ]
}

const REGIONS = [
  { value: 'cn', label: '中国' },
  { value: 'us', label: '美国' },
  { value: 'jp', label: '日本' },
  { value: 'custom', label: '自定义' }
]

/**
 * 工作日计算器工具组件
 * 
 * 功能特性：
 * - 精确计算两个日期间的工作日数量
 * - 支持多个国家/地区的节假日数据
 * - 自定义节假日和工作日设置
 * - 历史记录管理和结果导出
 * - 快速日期范围设置
 */
export default function BusinessDayCalculatorTool() {
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [region, setRegion] = useState('cn')
  const [includeWeekends, setIncludeWeekends] = useState(false)
  const [customDates, setCustomDates] = useState<CustomDate[]>([])
  const [customDateInput, setCustomDateInput] = useState('')
  const [customDateType, setCustomDateType] = useState<'holiday' | 'workday'>('holiday')
  const [customDateName, setCustomDateName] = useState('')
  const [result, setResult] = useState<BusinessDayResult | null>(null)
  const [history, setHistory] = useState<BusinessDayResult[]>([])
  const [activeTab, setActiveTab] = useState('calculator')

  // 获取今天的日期
  const today = new Date().toISOString().split('T')[0]

  /**
   * 判断是否为工作日
   */
  const isBusinessDay = useCallback((date: Date): boolean => {
    const dateStr = date.toISOString().split('T')[0]
    const dayOfWeek = date.getDay()
    
    // 检查是否为周末
    if (!includeWeekends && (dayOfWeek === 0 || dayOfWeek === 6)) {
      return false
    }
    
    // 检查是否为节假日
    const holidays = region === 'custom' ? [] : (HOLIDAYS[region] || [])
    const isHoliday = holidays.some(holiday => holiday.date === dateStr)
    if (isHoliday) return false
    
    // 检查自定义日期
    const customDate = customDates.find(cd => cd.date === dateStr)
    if (customDate) {
      return customDate.type === 'workday'
    }
    
    return true
  }, [region, includeWeekends, customDates])

  /**
   * 计算工作日
   */
  const calculateBusinessDays = useCallback(() => {
    if (!startDate || !endDate) {
      setResult(null)
      return
    }

    const start = new Date(startDate)
    const end = new Date(endDate)
    
    if (start > end) {
      setResult(null)
      return
    }

    let totalDays = 0
    let businessDays = 0
    let weekendDays = 0
    let holidayCount = 0
    
    const current = new Date(start)
    
    while (current <= end) {
      totalDays++
      
      const dayOfWeek = current.getDay()
      const dateStr = current.toISOString().split('T')[0]
      
      // 检查是否为周末
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        weekendDays++
      }
      
      // 检查是否为节假日
      const holidays = region === 'custom' ? [] : (HOLIDAYS[region] || [])
      const isHoliday = holidays.some(holiday => holiday.date === dateStr)
      
      // 检查自定义日期
      const customDate = customDates.find(cd => cd.date === dateStr)
      
      if (isHoliday || (customDate?.type === 'holiday')) {
        holidayCount++
      }
      
      if (isBusinessDay(current)) {
        businessDays++
      }
      
      current.setDate(current.getDate() + 1)
    }

    const newResult: BusinessDayResult = {
      startDate,
      endDate,
      totalDays,
      businessDays,
      weekendDays,
      holidays: holidayCount,
      region,
      calculation: `${totalDays}总天数 - ${weekendDays}周末 - ${holidayCount}节假日 = ${businessDays}工作日`
    }
    
    setResult(newResult)
  }, [startDate, endDate, region, includeWeekends, customDates, isBusinessDay])

  /**
   * 添加自定义日期
   */
  const addCustomDate = () => {
    if (!customDateInput) return
    
    const newCustomDate: CustomDate = {
      date: customDateInput,
      type: customDateType,
      name: customDateName || (customDateType === 'holiday' ? '自定义节假日' : '自定义工作日')
    }
    
    setCustomDates(prev => [...prev.filter(cd => cd.date !== customDateInput), newCustomDate])
    setCustomDateInput('')
    setCustomDateName('')
  }

  /**
   * 删除自定义日期
   */
  const removeCustomDate = (date: string) => {
    setCustomDates(prev => prev.filter(cd => cd.date !== date))
  }

  /**
   * 保存到历史记录
   */
  const saveToHistory = () => {
    if (!result) return
    
    setHistory(prev => {
      const newHistory = [result, ...prev.filter(h => 
        h.startDate !== result.startDate || h.endDate !== result.endDate || h.region !== result.region
      )]
      return newHistory.slice(0, 10) // 只保留最近10条记录
    })
  }

  /**
   * 从历史记录加载
   */
  const loadFromHistory = (historyItem: BusinessDayResult) => {
    setStartDate(historyItem.startDate)
    setEndDate(historyItem.endDate)
    setRegion(historyItem.region)
    setResult(historyItem)
    setActiveTab('calculator')
  }

  /**
   * 导出结果
   */
  const exportResult = () => {
    if (!result) return

    const data = {
      ...result,
      timestamp: new Date().toISOString(),
      customDates: customDates.filter(cd => {
        const date = new Date(cd.date)
        const start = new Date(result.startDate)
        const end = new Date(result.endDate)
        return date >= start && date <= end
      })
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `business-days-${result.startDate}-to-${result.endDate}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 清除历史记录
   */
  const clearHistory = () => {
    setHistory([])
  }

  /**
   * 快速设置日期范围
   */
  const setQuickDateRange = (days: number) => {
    const start = new Date()
    const end = new Date()
    end.setDate(start.getDate() + days)
    
    setStartDate(start.toISOString().split('T')[0])
    setEndDate(end.toISOString().split('T')[0])
  }

  // 自动计算当输入改变时
  React.useEffect(() => {
    calculateBusinessDays()
  }, [calculateBusinessDays])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">工作日计算器</h1>
        <p className="text-muted-foreground">
          精确计算两个日期之间的工作日数量，排除周末和节假日
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="calculator" className="flex items-center gap-2">
            <Calculator className="w-4 h-4" />
            工作日计算
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            设置与自定义
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="w-4 h-4" />
            历史记录
          </TabsTrigger>
        </TabsList>

        <TabsContent value="calculator" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 输入区域 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  日期范围设置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startDate">开始日期</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      max={endDate || undefined}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="endDate">结束日期</Label>
                    <Input
                      id="endDate"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      min={startDate || today}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>地区/国家</Label>
                  <Select value={region} onValueChange={setRegion}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {REGIONS.map(r => (
                        <SelectItem key={r.value} value={r.value}>
                          {r.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="includeWeekends"
                    checked={includeWeekends}
                    onCheckedChange={setIncludeWeekends}
                  />
                  <Label htmlFor="includeWeekends">将周末计为工作日</Label>
                </div>

                <div className="border-t pt-4">
                  <Label>快速设置</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setQuickDateRange(7)}
                    >
                      未来7天
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setQuickDateRange(30)}
                    >
                      未来30天
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setQuickDateRange(90)}
                    >
                      未来90天
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setQuickDateRange(365)}
                    >
                      未来1年
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 结果显示 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  计算结果
                </CardTitle>
              </CardHeader>
              <CardContent>
                {result ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-primary/10 rounded-lg">
                        <div className="text-2xl font-bold text-primary">{result.businessDays}</div>
                        <div className="text-sm text-muted-foreground">工作日</div>
                      </div>
                      <div className="text-center p-4 bg-secondary/10 rounded-lg">
                        <div className="text-2xl font-bold">{result.totalDays}</div>
                        <div className="text-sm text-muted-foreground">总天数</div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>周末天数：</span>
                        <Badge variant="secondary">{result.weekendDays}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>节假日天数：</span>
                        <Badge variant="secondary">{result.holidays}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>地区：</span>
                        <Badge variant="outline">{REGIONS.find(r => r.value === result.region)?.label}</Badge>
                      </div>
                    </div>

                    <div className="p-3 bg-muted rounded-lg">
                      <div className="text-sm font-medium">计算公式：</div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {result.calculation}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button onClick={saveToHistory} variant="outline" size="sm">
                        保存到历史
                      </Button>
                      <Button onClick={exportResult} variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        导出结果
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Alert>
                    <AlertDescription>
                      请选择开始日期和结束日期来计算工作日
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>自定义日期设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label>日期</Label>
                  <Input
                    type="date"
                    value={customDateInput}
                    onChange={(e) => setCustomDateInput(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>类型</Label>
                  <Select value={customDateType} onValueChange={(value: 'holiday' | 'workday') => setCustomDateType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="holiday">节假日</SelectItem>
                      <SelectItem value="workday">工作日</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>名称</Label>
                  <Input
                    placeholder="自定义名称"
                    value={customDateName}
                    onChange={(e) => setCustomDateName(e.target.value)}
                  />
                </div>
                
                <div className="flex items-end">
                  <Button onClick={addCustomDate} className="w-full">
                    <Plus className="w-4 h-4 mr-2" />
                    添加
                  </Button>
                </div>
              </div>

              {customDates.length > 0 && (
                <div className="space-y-2">
                  <Label>已添加的自定义日期</Label>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {customDates.map(cd => (
                      <div key={cd.date} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center gap-2">
                          <Badge variant={cd.type === 'holiday' ? 'destructive' : 'default'}>
                            {cd.type === 'holiday' ? '节假日' : '工作日'}
                          </Badge>
                          <span className="font-mono">{cd.date}</span>
                          <span className="text-sm text-muted-foreground">{cd.name}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCustomDate(cd.date)}
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {region !== 'custom' && (
            <Card>
              <CardHeader>
                <CardTitle>节假日列表</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {(HOLIDAYS[region] || []).map(holiday => (
                    <div key={holiday.date} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{holiday.date}</Badge>
                        <span>{holiday.name}</span>
                      </div>
                      <Badge variant="secondary">{holiday.type}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>历史记录</CardTitle>
              {history.length > 0 && (
                <Button variant="outline" size="sm" onClick={clearHistory}>
                  <Trash2 className="w-4 h-4 mr-2" />
                  清除历史
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {history.length === 0 ? (
                <Alert>
                  <AlertDescription>
                    暂无历史记录。完成计算后点击"保存到历史"来保存结果。
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-3">
                  {history.map((item, index) => (
                    <div key={index} className="p-4 border rounded-lg space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{item.startDate}</Badge>
                          <span>至</span>
                          <Badge variant="outline">{item.endDate}</Badge>
                          <Badge variant="secondary">
                            {REGIONS.find(r => r.value === item.region)?.label}
                          </Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => loadFromHistory(item)}
                        >
                          加载
                        </Button>
                      </div>
                      
                      <div className="grid grid-cols-4 gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-bold text-primary">{item.businessDays}</div>
                          <div className="text-muted-foreground">工作日</div>
                        </div>
                        <div className="text-center">
                          <div className="font-bold">{item.totalDays}</div>
                          <div className="text-muted-foreground">总天数</div>
                        </div>
                        <div className="text-center">
                          <div className="font-bold">{item.weekendDays}</div>
                          <div className="text-muted-foreground">周末</div>
                        </div>
                        <div className="text-center">
                          <div className="font-bold">{item.holidays}</div>
                          <div className="text-muted-foreground">节假日</div>
                        </div>
                      </div>
                      
                      <div className="text-xs text-muted-foreground font-mono">
                        {item.calculation}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
