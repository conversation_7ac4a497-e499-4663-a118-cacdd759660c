'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Copy, RefreshCw, Trash2, Download, Upload, List, ListOrdered } from 'lucide-react';

interface ListOptions {
  listType: 'ordered' | 'unordered';
  outputFormat: 'html' | 'markdown' | 'plain';
  separator: 'newline' | 'comma' | 'semicolon' | 'pipe' | 'custom';
  removeEmpty: boolean;
  removeDuplicates: boolean;
  trimWhitespace: boolean;
  sortItems: 'none' | 'asc' | 'desc';
  customSeparator: string;
  numberingStart: number;
  htmlListClass: string;
}

export default function TextToListTool() {
  const [input, setInput] = useState('');
  const [options, setOptions] = useState<ListOptions>({
    listType: 'unordered',
    outputFormat: 'html',
    separator: 'newline',
    removeEmpty: true,
    removeDuplicates: false,
    trimWhitespace: true,
    sortItems: 'none',
    customSeparator: '',
    numberingStart: 1,
    htmlListClass: ''
  });
  const [error, setError] = useState('');

  const parseTextToItems = useCallback((text: string): string[] => {
    if (!text.trim()) return [];
    
    let items: string[] = [];
    const { separator, customSeparator } = options;
    
    try {
      switch (separator) {
        case 'newline':
          items = text.split('\n');
          break;
        case 'comma':
          items = text.split(',');
          break;
        case 'semicolon':
          items = text.split(';');
          break;
        case 'pipe':
          items = text.split('|');
          break;
        case 'custom':
          if (!customSeparator) {
            throw new Error('请指定自定义分隔符');
          }
          items = text.split(customSeparator);
          break;
        default:
          items = text.split('\n');
      }
      
      // 处理选项
      if (options.trimWhitespace) {
        items = items.map(item => item.trim());
      }
      
      if (options.removeEmpty) {
        items = items.filter(item => item.length > 0);
      }
      
      if (options.removeDuplicates) {
        items = Array.from(new Set(items));
      }
      
      if (options.sortItems !== 'none') {
        items = items.sort((a, b) => {
          if (options.sortItems === 'asc') {
            return a.localeCompare(b, 'zh-CN');
          } else {
            return b.localeCompare(a, 'zh-CN');
          }
        });
      }
      
      return items;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : '解析文本失败');
    }
  }, [options]);

  const generateHtmlList = useCallback((items: string[]): string => {
    if (items.length === 0) return '';
    
    const listTag = options.listType === 'ordered' ? 'ol' : 'ul';
    const classAttr = options.htmlListClass ? ` class="${options.htmlListClass}"` : '';
    const startAttr = options.listType === 'ordered' && options.numberingStart !== 1 ? ` start="${options.numberingStart}"` : '';
    
    let html = `<${listTag}${classAttr}${startAttr}>\n`;
    
    items.forEach(item => {
      const escapedItem = escapeHtml(item);
      html += `  <li>${escapedItem}</li>\n`;
    });
    
    html += `</${listTag}>`;
    return html;
  }, [options.listType, options.htmlListClass, options.numberingStart]);

  const generateMarkdownList = useCallback((items: string[]): string => {
    if (items.length === 0) return '';
    
    return items.map((item, index) => {
      if (options.listType === 'ordered') {
        const number = options.numberingStart + index;
        return `${number}. ${item}`;
      } else {
        return `- ${item}`;
      }
    }).join('\n');
  }, [options.listType, options.numberingStart]);

  const generatePlainList = useCallback((items: string[]): string => {
    if (items.length === 0) return '';
    
    return items.map((item, index) => {
      if (options.listType === 'ordered') {
        const number = options.numberingStart + index;
        return `${number}. ${item}`;
      } else {
        return `• ${item}`;
      }
    }).join('\n');
  }, [options.listType, options.numberingStart]);

  const escapeHtml = (text: string): string => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  };

  const output = useMemo(() => {
    try {
      setError('');
      
      if (!input.trim()) return '';
      
      const items = parseTextToItems(input);
      
      if (items.length === 0) return '';
      
      switch (options.outputFormat) {
        case 'html':
          return generateHtmlList(items);
        case 'markdown':
          return generateMarkdownList(items);
        case 'plain':
          return generatePlainList(items);
        default:
          return '';
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '转换失败');
      return '';
    }
  }, [input, options.outputFormat, parseTextToItems, generateHtmlList, generateMarkdownList, generatePlainList]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(output);
    } catch (err) {
      setError('复制失败');
    }
  };

  const handleClear = () => {
    setInput('');
    setError('');
  };

  const handleReset = () => {
    setOptions({
      listType: 'unordered',
      outputFormat: 'html',
      separator: 'newline',
      removeEmpty: true,
      removeDuplicates: false,
      trimWhitespace: true,
      sortItems: 'none',
      customSeparator: '',
      numberingStart: 1,
      htmlListClass: ''
    });
  };

  const updateOption = <K extends keyof ListOptions>(key: K, value: ListOptions[K]) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 1024 * 1024) {
      setError('文件大小不能超过1MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setInput(content);
    };
    reader.readAsText(file);
  };

  const handleDownload = () => {
    if (!output) return;
    
    const extension = options.outputFormat === 'html' ? 'html' : 
                     options.outputFormat === 'markdown' ? 'md' : 'txt';
    const mimeType = options.outputFormat === 'html' ? 'text/html' : 
                    options.outputFormat === 'markdown' ? 'text/markdown' : 'text/plain';
    
    const blob = new Blob([output], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `list.${extension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const examples = [
    {
      name: '购物清单',
      data: '苹果\n香蕉\n橙子\n牛奶\n面包\n鸡蛋'
    },
    {
      name: '任务列表',
      data: '完成项目报告,review代码,参加团队会议,更新文档,测试新功能'
    },
    {
      name: '技能列表',
      data: 'JavaScript; Python; React; Node.js; TypeScript; Vue.js'
    },
    {
      name: '城市列表',
      data: '北京|上海|广州|深圳|杭州|成都|西安|武汉'
    }
  ];

  const separatorOptions = [
    { value: 'newline', label: '换行符' },
    { value: 'comma', label: '逗号 (,)' },
    { value: 'semicolon', label: '分号 (;)' },
    { value: 'pipe', label: '管道 (|)' },
    { value: 'custom', label: '自定义' }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>文本转列表工具</CardTitle>
          <CardDescription>
            将文本转换为有序或无序列表，支持HTML、Markdown和纯文本格式。
            可以按多种分隔符分割文本，并提供排序、去重、过滤等功能。
          </CardDescription>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">转换选项</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="listType">列表类型</Label>
              <Select value={options.listType} onValueChange={(value: 'ordered' | 'unordered') => updateOption('listType', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unordered">
                    <div className="flex items-center">
                      <List className="h-4 w-4 mr-2" />
                      无序列表
                    </div>
                  </SelectItem>
                  <SelectItem value="ordered">
                    <div className="flex items-center">
                      <ListOrdered className="h-4 w-4 mr-2" />
                      有序列表
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="outputFormat">输出格式</Label>
              <Select value={options.outputFormat} onValueChange={(value: 'html' | 'markdown' | 'plain') => updateOption('outputFormat', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="html">HTML</SelectItem>
                  <SelectItem value="markdown">Markdown</SelectItem>
                  <SelectItem value="plain">纯文本</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="separator">分隔符</Label>
              <Select value={options.separator} onValueChange={(value) => updateOption('separator', value as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {separatorOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {options.separator === 'custom' && (
              <div className="space-y-2">
                <Label htmlFor="customSeparator">自定义分隔符</Label>
                <Input
                  id="customSeparator"
                  value={options.customSeparator}
                  onChange={(e) => updateOption('customSeparator', e.target.value)}
                  placeholder="输入分隔符"
                  maxLength={5}
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="sortItems">排序</Label>
              <Select value={options.sortItems} onValueChange={(value) => updateOption('sortItems', value as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">不排序</SelectItem>
                  <SelectItem value="asc">升序</SelectItem>
                  <SelectItem value="desc">降序</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {options.listType === 'ordered' && (
              <div className="space-y-2">
                <Label htmlFor="numberingStart">起始编号</Label>
                <Input
                  id="numberingStart"
                  type="number"
                  min="1"
                  value={options.numberingStart}
                  onChange={(e) => updateOption('numberingStart', parseInt(e.target.value) || 1)}
                />
              </div>
            )}

            {options.outputFormat === 'html' && (
              <div className="space-y-2">
                <Label htmlFor="htmlListClass">CSS类名</Label>
                <Input
                  id="htmlListClass"
                  value={options.htmlListClass}
                  onChange={(e) => updateOption('htmlListClass', e.target.value)}
                  placeholder="list-class"
                />
              </div>
            )}
          </div>

          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="removeEmpty"
                checked={options.removeEmpty}
                onCheckedChange={(checked) => updateOption('removeEmpty', checked)}
              />
              <Label htmlFor="removeEmpty">移除空项</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="removeDuplicates"
                checked={options.removeDuplicates}
                onCheckedChange={(checked) => updateOption('removeDuplicates', checked)}
              />
              <Label htmlFor="removeDuplicates">去除重复</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="trimWhitespace"
                checked={options.trimWhitespace}
                onCheckedChange={(checked) => updateOption('trimWhitespace', checked)}
              />
              <Label htmlFor="trimWhitespace">去除空格</Label>
            </div>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" onClick={handleReset}>
              <RefreshCw className="h-4 w-4 mr-1" />
              重置选项
            </Button>
            <Button variant="outline" asChild>
              <label>
                <Upload className="h-4 w-4 mr-1" />
                上传文件
                <input
                  type="file"
                  accept=".txt,.csv"
                  className="hidden"
                  onChange={handleFileUpload}
                />
              </label>
            </Button>
          </div>

          {error && (
            <div className="text-sm text-red-500">{error}</div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">输入文本</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="input">请输入要转换的文本</Label>
              <Textarea
                id="input"
                placeholder="苹果&#10;香蕉&#10;橙子&#10;牛奶"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                className="min-h-[300px] font-mono text-sm"
              />
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClear}>
                <Trash2 className="h-4 w-4 mr-1" />
                清空
              </Button>
            </div>

            <div className="space-y-2">
              <Label>示例数据：</Label>
              <div className="flex flex-wrap gap-2">
                {examples.map((example, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setInput(example.data)}
                    className="text-xs"
                  >
                    {example.name}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">转换结果</CardTitle>
              <div className="flex gap-2">
                {output && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDownload}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      下载
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopy}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      复制
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Textarea
                value={output}
                readOnly
                className="min-h-[300px] font-mono text-xs"
                placeholder="转换结果将显示在这里..."
              />
              
              {output && (
                <div className="text-sm text-muted-foreground">
                  <p>格式: {options.outputFormat.toUpperCase()}</p>
                  <p>类型: {options.listType === 'ordered' ? '有序列表' : '无序列表'}</p>
                  <p>项目数: {output.split('\n').filter(line => line.trim().match(/^(\d+\.|[•-]|\s*<li>)/)).length}</p>
                  <p>字符数: {output.length}</p>
                </div>
              )}

              {output && options.outputFormat === 'html' && (
                <div className="space-y-2">
                  <Label>HTML预览：</Label>
                  <div 
                    className="p-3 bg-white border rounded text-sm max-h-48 overflow-auto"
                    dangerouslySetInnerHTML={{ __html: output }}
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <div><strong>支持的分隔符：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>换行符：每行一个项目</li>
            <li>逗号分隔：项目1,项目2,项目3</li>
            <li>分号分隔：项目1;项目2;项目3</li>
            <li>管道分隔：项目1|项目2|项目3</li>
            <li>自定义分隔符：可指定任意字符作为分隔符</li>
          </ul>
          <div className="mt-4"><strong>输出格式说明：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>HTML：标准的&lt;ul&gt;或&lt;ol&gt;列表，可添加CSS类</li>
            <li>Markdown：GitHub风格的列表格式</li>
            <li>纯文本：带项目符号或编号的简单文本列表</li>
          </ul>
          <div className="mt-4"><strong>功能特性：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>自动去除空项和多余空格</li>
            <li>支持去重和排序功能</li>
            <li>有序列表可自定义起始编号</li>
            <li>支持文件上传和结果下载</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
