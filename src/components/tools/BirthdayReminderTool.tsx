'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar, Gift, Plus, Trash2, Edit2, Save, X, Cake, Star, Bell } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Birthday {
  id: string
  name: string
  date: string // YYYY-MM-DD
  year?: number // 出生年份
  category: string
  notes?: string
  reminder: number // 提前几天提醒
  starred: boolean
}

interface Category {
  id: string
  name: string
  color: string
}

const DEFAULT_CATEGORIES: Category[] = [
  { id: 'family', name: '家人', color: '#ef4444' },
  { id: 'friend', name: '朋友', color: '#3b82f6' },
  { id: 'colleague', name: '同事', color: '#10b981' },
  { id: 'other', name: '其他', color: '#6b7280' },
]

const REMINDER_OPTIONS = [
  { value: 0, label: '当天' },
  { value: 1, label: '提前1天' },
  { value: 3, label: '提前3天' },
  { value: 7, label: '提前1周' },
  { value: 14, label: '提前2周' },
  { value: 30, label: '提前1个月' },
]

const ZODIAC_SIGNS = [
  { name: '鼠', years: [1924, 1936, 1948, 1960, 1972, 1984, 1996, 2008, 2020] },
  { name: '牛', years: [1925, 1937, 1949, 1961, 1973, 1985, 1997, 2009, 2021] },
  { name: '虎', years: [1926, 1938, 1950, 1962, 1974, 1986, 1998, 2010, 2022] },
  { name: '兔', years: [1927, 1939, 1951, 1963, 1975, 1987, 1999, 2011, 2023] },
  { name: '龙', years: [1928, 1940, 1952, 1964, 1976, 1988, 2000, 2012, 2024] },
  { name: '蛇', years: [1929, 1941, 1953, 1965, 1977, 1989, 2001, 2013, 2025] },
  { name: '马', years: [1930, 1942, 1954, 1966, 1978, 1990, 2002, 2014, 2026] },
  { name: '羊', years: [1931, 1943, 1955, 1967, 1979, 1991, 2003, 2015, 2027] },
  { name: '猴', years: [1932, 1944, 1956, 1968, 1980, 1992, 2004, 2016, 2028] },
  { name: '鸡', years: [1933, 1945, 1957, 1969, 1981, 1993, 2005, 2017, 2029] },
  { name: '狗', years: [1934, 1946, 1958, 1970, 1982, 1994, 2006, 2018, 2030] },
  { name: '猪', years: [1935, 1947, 1959, 1971, 1983, 1995, 2007, 2019, 2031] },
]

export default function BirthdayReminderTool() {
  const [birthdays, setBirthdays] = useState<Birthday[]>([])
  const [categories, setCategories] = useState<Category[]>(DEFAULT_CATEGORIES)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'date' | 'name'>('date')
  
  // 表单状态
  const [name, setName] = useState('')
  const [date, setDate] = useState('')
  const [year, setYear] = useState('')
  const [category, setCategory] = useState('friend')
  const [notes, setNotes] = useState('')
  const [reminder, setReminder] = useState(7)
  const [starred, setStarred] = useState(false)
  
  const { toast } = useToast()

  // 从 localStorage 加载数据
  useEffect(() => {
    const savedBirthdays = localStorage.getItem('birthdays')
    if (savedBirthdays) {
      setBirthdays(JSON.parse(savedBirthdays))
    }
    
    const savedCategories = localStorage.getItem('birthdayCategories')
    if (savedCategories) {
      setCategories(JSON.parse(savedCategories))
    }
  }, [])

  // 保存数据到 localStorage
  useEffect(() => {
    if (birthdays.length > 0) {
      localStorage.setItem('birthdays', JSON.stringify(birthdays))
    }
  }, [birthdays])

  useEffect(() => {
    localStorage.setItem('birthdayCategories', JSON.stringify(categories))
  }, [categories])

  const checkBirthdays = useCallback(() => {
    const today = new Date()
    const todayStr = `${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`
    
    birthdays.forEach(birthday => {
      const birthdayStr = birthday.date.slice(5) // MM-DD
      
      // 检查今天的生日
      if (birthdayStr === todayStr) {
        const age = birthday.year ? today.getFullYear() - birthday.year : null
        const message = age ? `${birthday.name} 今天 ${age} 岁生日！` : `今天是 ${birthday.name} 的生日！`
        
        // 显示通知
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('生日提醒', {
            body: message,
            icon: '/icons/icon-192x192.png',
          })
        }
        
        toast({
          title: '🎂 生日快乐！',
          description: message,
        })
      }
      
      // 检查即将到来的生日
      const daysUntil = getDaysUntilBirthday(birthday.date)
      if (daysUntil === birthday.reminder && daysUntil > 0) {
        const message = `${birthday.name} 的生日还有 ${daysUntil} 天`
        
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('生日提醒', {
            body: message,
            icon: '/icons/icon-192x192.png',
          })
        }
        
        toast({
          title: '📅 生日提醒',
          description: message,
        })
      }
    })
  }, [birthdays, toast])

  // 检查今天的生日和即将到来的生日
  useEffect(() => {
    checkBirthdays()
    
    // 每天检查一次
    const interval = setInterval(checkBirthdays, 24 * 60 * 60 * 1000)
    return () => clearInterval(interval)
  }, [checkBirthdays])

  const getDaysUntilBirthday = (dateStr: string): number => {
    const today = new Date()
    const currentYear = today.getFullYear()
    const [, month, day] = dateStr.split('-').map(Number)
    
    let birthdayThisYear = new Date(currentYear, month - 1, day)
    
    // 如果今年的生日已经过了，计算到明年生日的天数
    if (birthdayThisYear < today) {
      birthdayThisYear = new Date(currentYear + 1, month - 1, day)
    }
    
    const diffTime = birthdayThisYear.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  const getAge = (birthday: Birthday): number | null => {
    if (!birthday.year) return null
    const today = new Date()
    const birthDate = new Date(birthday.date)
    let age = today.getFullYear() - birthday.year
    
    // 如果今年生日还没到，年龄减1
    if (today.getMonth() < birthDate.getMonth() || 
        (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())) {
      age--
    }
    
    return age
  }

  const getZodiac = (year: number): string => {
    for (const zodiac of ZODIAC_SIGNS) {
      if (zodiac.years.includes(year)) {
        return zodiac.name
      }
    }
    return ''
  }

  const getConstellation = (dateStr: string): string => {
    const [, month, day] = dateStr.split('-').map(Number)
    
    const constellations = [
      { name: '水瓶座', start: [1, 20], end: [2, 18] },
      { name: '双鱼座', start: [2, 19], end: [3, 20] },
      { name: '白羊座', start: [3, 21], end: [4, 19] },
      { name: '金牛座', start: [4, 20], end: [5, 20] },
      { name: '双子座', start: [5, 21], end: [6, 21] },
      { name: '巨蟹座', start: [6, 22], end: [7, 22] },
      { name: '狮子座', start: [7, 23], end: [8, 22] },
      { name: '处女座', start: [8, 23], end: [9, 22] },
      { name: '天秤座', start: [9, 23], end: [10, 23] },
      { name: '天蝎座', start: [10, 24], end: [11, 22] },
      { name: '射手座', start: [11, 23], end: [12, 21] },
      { name: '摩羯座', start: [12, 22], end: [1, 19] },
    ]
    
    for (const constellation of constellations) {
      const [startMonth, startDay] = constellation.start
      const [endMonth, endDay] = constellation.end
      
      if (startMonth === endMonth) {
        if (month === startMonth && day >= startDay && day <= endDay) {
          return constellation.name
        }
      } else {
        if ((month === startMonth && day >= startDay) || 
            (month === endMonth && day <= endDay)) {
          return constellation.name
        }
      }
    }
    
    return ''
  }

  const saveBirthday = () => {
    if (!name || !date) {
      toast({
        title: '请填写完整信息',
        description: '姓名和日期为必填项',
        variant: 'destructive',
      })
      return
    }
    
    const birthday: Birthday = {
      id: editingId || Date.now().toString(),
      name,
      date,
      year: year ? parseInt(year) : undefined,
      category,
      notes,
      reminder,
      starred,
    }
    
    if (editingId) {
      setBirthdays(birthdays.map(b => b.id === editingId ? birthday : b))
      setEditingId(null)
    } else {
      setBirthdays([...birthdays, birthday])
    }
    
    // 重置表单
    resetForm()
    
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
    
    toast({
      title: editingId ? '生日已更新' : '生日已添加',
      description: `${name} 的生日已保存`,
    })
  }

  const editBirthday = (birthday: Birthday) => {
    setEditingId(birthday.id)
    setName(birthday.name)
    setDate(birthday.date)
    setYear(birthday.year?.toString() || '')
    setCategory(birthday.category)
    setNotes(birthday.notes || '')
    setReminder(birthday.reminder)
    setStarred(birthday.starred)
  }

  const deleteBirthday = (id: string) => {
    setBirthdays(birthdays.filter(b => b.id !== id))
    toast({
      title: '生日已删除',
    })
  }

  const resetForm = () => {
    setName('')
    setDate('')
    setYear('')
    setCategory('friend')
    setNotes('')
    setReminder(7)
    setStarred(false)
    setEditingId(null)
  }

  const toggleStar = (id: string) => {
    setBirthdays(birthdays.map(b => 
      b.id === id ? { ...b, starred: !b.starred } : b
    ))
  }

  const filteredBirthdays = birthdays
    .filter(b => filterCategory === 'all' || b.category === filterCategory)
    .sort((a, b) => {
      if (sortBy === 'date') {
        const daysA = getDaysUntilBirthday(a.date)
        const daysB = getDaysUntilBirthday(b.date)
        return daysA - daysB
      } else {
        return a.name.localeCompare(b.name)
      }
    })

  const upcomingBirthdays = birthdays
    .map(b => ({ ...b, daysUntil: getDaysUntilBirthday(b.date) }))
    .filter(b => b.daysUntil <= 30)
    .sort((a, b) => a.daysUntil - b.daysUntil)

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-6">生日提醒器</h2>
        
        <Tabs defaultValue="list" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="list">生日列表</TabsTrigger>
            <TabsTrigger value="upcoming">即将到来</TabsTrigger>
            <TabsTrigger value="add">添加生日</TabsTrigger>
          </TabsList>
          
          <TabsContent value="list" className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  {categories.map((cat) => (
                    <SelectItem key={cat.id} value={cat.id}>
                      <span className="flex items-center gap-2">
                        <span 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: cat.color }}
                        />
                        {cat.name}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={sortBy} onValueChange={(v) => setSortBy(v as 'date' | 'name')}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">按日期排序</SelectItem>
                  <SelectItem value="name">按姓名排序</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              {filteredBirthdays.length === 0 ? (
                <div className="text-center py-12">
                  <Gift className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">还没有添加生日</p>
                </div>
              ) : (
                filteredBirthdays.map((birthday) => {
                  const daysUntil = getDaysUntilBirthday(birthday.date)
                  const age = getAge(birthday)
                  const category = categories.find(c => c.id === birthday.category)
                  
                  return (
                    <div
                      key={birthday.id}
                      className="p-4 rounded-lg border hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-semibold text-lg">{birthday.name}</h4>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-6 w-6"
                              onClick={() => toggleStar(birthday.id)}
                            >
                              <Star 
                                className={`w-4 h-4 ${birthday.starred ? 'fill-yellow-400 text-yellow-400' : ''}`} 
                              />
                            </Button>
                            <span 
                              className="px-2 py-1 text-xs rounded-full text-white"
                              style={{ backgroundColor: category?.color }}
                            >
                              {category?.name}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Calendar className="w-4 h-4" />
                              {new Date(birthday.date).toLocaleDateString('zh-CN', {
                                month: 'long',
                                day: 'numeric',
                              })}
                            </span>
                            
                            {age !== null && (
                              <span>{age} 岁</span>
                            )}
                            
                            {birthday.year && (
                              <>
                                <span>{getZodiac(birthday.year)}年</span>
                                <span>{getConstellation(birthday.date)}</span>
                              </>
                            )}
                          </div>
                          
                          <div className="mt-2">
                            {daysUntil === 0 ? (
                              <span className="text-sm font-semibold text-red-500">
                                🎂 今天生日！
                              </span>
                            ) : daysUntil <= 7 ? (
                              <span className="text-sm font-semibold text-orange-500">
                                还有 {daysUntil} 天
                              </span>
                            ) : (
                              <span className="text-sm text-muted-foreground">
                                还有 {daysUntil} 天
                              </span>
                            )}
                          </div>
                          
                          {birthday.notes && (
                            <p className="text-sm text-muted-foreground mt-2">
                              {birthday.notes}
                            </p>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => editBirthday(birthday)}
                          >
                            <Edit2 className="w-4 h-4" />
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => deleteBirthday(birthday.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="upcoming" className="space-y-4">
            <h3 className="text-lg font-semibold">未来30天的生日</h3>
            
            {upcomingBirthdays.length === 0 ? (
              <div className="text-center py-12">
                <Calendar className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">未来30天没有生日</p>
              </div>
            ) : (
              <div className="space-y-4">
                {upcomingBirthdays.map((birthday) => {
                  const age = getAge(birthday)
                  const category = categories.find(c => c.id === birthday.category)
                  
                  return (
                    <div
                      key={birthday.id}
                      className={`p-4 rounded-lg border ${
                        birthday.daysUntil === 0
                          ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                          : birthday.daysUntil <= 7
                          ? 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800'
                          : ''
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="font-semibold">{birthday.name}</h4>
                            {birthday.starred && (
                              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            )}
                            <span 
                              className="px-2 py-1 text-xs rounded-full text-white"
                              style={{ backgroundColor: category?.color }}
                            >
                              {category?.name}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {new Date(birthday.date).toLocaleDateString('zh-CN', {
                              month: 'long',
                              day: 'numeric',
                            })}
                            {age !== null && ` · 将满 ${age + 1} 岁`}
                          </p>
                        </div>
                        
                        <div className="text-right">
                          {birthday.daysUntil === 0 ? (
                            <div>
                              <Cake className="w-8 h-8 text-red-500 mx-auto" />
                              <p className="text-sm font-semibold text-red-500">今天</p>
                            </div>
                          ) : (
                            <div>
                              <p className="text-2xl font-bold">{birthday.daysUntil}</p>
                              <p className="text-sm text-muted-foreground">天后</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="add" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">姓名 *</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="输入姓名"
                />
              </div>
              
              <div>
                <Label htmlFor="date">生日日期 *</Label>
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="year">出生年份（可选）</Label>
                <Input
                  id="year"
                  type="number"
                  value={year}
                  onChange={(e) => setYear(e.target.value)}
                  placeholder="例如：1990"
                  min="1900"
                  max={new Date().getFullYear()}
                />
              </div>
              
              <div>
                <Label htmlFor="category">分类</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger id="category">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((cat) => (
                      <SelectItem key={cat.id} value={cat.id}>
                        <span className="flex items-center gap-2">
                          <span 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: cat.color }}
                          />
                          {cat.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="reminder">提醒时间</Label>
                <Select value={reminder.toString()} onValueChange={(v) => setReminder(Number(v))}>
                  <SelectTrigger id="reminder">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {REMINDER_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="starred"
                  checked={starred}
                  onChange={(e) => setStarred(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="starred" className="cursor-pointer">
                  标记为重要
                </Label>
              </div>
            </div>
            
            <div>
              <Label htmlFor="notes">备注（可选）</Label>
              <Input
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="例如：喜欢的礼物、特殊纪念等"
              />
            </div>
            
            <div className="flex gap-2">
              <Button onClick={saveBirthday}>
                {editingId ? (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    更新生日
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    添加生日
                  </>
                )}
              </Button>
              
              {editingId && (
                <Button variant="outline" onClick={resetForm}>
                  <X className="w-4 h-4 mr-2" />
                  取消
                </Button>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </Card>
      
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">统计信息</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-3xl font-bold">{birthdays.length}</p>
            <p className="text-sm text-muted-foreground">总生日数</p>
          </div>
          <div className="text-center">
            <p className="text-3xl font-bold">{upcomingBirthdays.length}</p>
            <p className="text-sm text-muted-foreground">30天内生日</p>
          </div>
          <div className="text-center">
            <p className="text-3xl font-bold">
              {birthdays.filter(b => b.starred).length}
            </p>
            <p className="text-sm text-muted-foreground">重要生日</p>
          </div>
          <div className="text-center">
            <p className="text-3xl font-bold">
              {birthdays.filter(b => getDaysUntilBirthday(b.date) === 0).length}
            </p>
            <p className="text-sm text-muted-foreground">今日生日</p>
          </div>
        </div>
      </Card>
    </div>
  )
}
