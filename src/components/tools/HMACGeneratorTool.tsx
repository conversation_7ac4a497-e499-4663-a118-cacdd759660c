'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Shield, Key, Download, Hash, CheckCircle, AlertTriangle } from 'lucide-react'

const HMACGeneratorTool: React.FC = () => {
  const [algorithm, setAlgorithm] = useState('SHA-256')
  const [secretKey, setSecretKey] = useState('')
  const [message, setMessage] = useState('')
  const [hmacResult, setHmacResult] = useState('')
  const [keyFormat, setKeyFormat] = useState('text')
  const [messageFormat, setMessageFormat] = useState('text')
  const [outputFormat, setOutputFormat] = useState('hex')
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('generate')

  // 支持的HMAC算法
  const algorithms = [
    { value: 'SHA-1', label: 'HMAC-SHA1' },
    { value: 'SHA-256', label: 'HMAC-SHA256 (推荐)' },
    { value: 'SHA-384', label: 'HMAC-SHA384' },
    { value: 'SHA-512', label: 'HMAC-SHA512' }
  ]

  // 格式选项
  const formats = [
    { value: 'text', label: '纯文本' },
    { value: 'hex', label: 'Hex (十六进制)' },
    { value: 'base64', label: 'Base64' }
  ]

  // 生成HMAC
  const generateHMAC = async () => {
    if (!secretKey.trim()) {
      setError('请输入密钥')
      return
    }

    if (!message.trim()) {
      setError('请输入消息内容')
      return
    }

    try {
      setIsProcessing(true)
      setError('')

      // 处理密钥
      let keyBytes: Uint8Array
      if (keyFormat === 'text') {
        keyBytes = new TextEncoder().encode(secretKey)
      } else if (keyFormat === 'hex') {
        keyBytes = hexToBytes(secretKey)
      } else if (keyFormat === 'base64') {
        keyBytes = base64ToBytes(secretKey)
      } else {
        throw new Error('不支持的密钥格式')
      }

      // 处理消息
      let messageBytes: Uint8Array
      if (messageFormat === 'text') {
        messageBytes = new TextEncoder().encode(message)
      } else if (messageFormat === 'hex') {
        messageBytes = hexToBytes(message)
      } else if (messageFormat === 'base64') {
        messageBytes = base64ToBytes(message)
      } else {
        throw new Error('不支持的消息格式')
      }

      // 导入密钥
      const cryptoKey = await window.crypto.subtle.importKey(
        'raw',
        keyBytes,
        { name: 'HMAC', hash: algorithm },
        false,
        ['sign']
      )

      // 生成HMAC
      const signature = await window.crypto.subtle.sign('HMAC', cryptoKey, messageBytes)
      const signatureArray = new Uint8Array(signature)

      // 格式化输出
      let result: string
      if (outputFormat === 'hex') {
        result = bytesToHex(signatureArray)
      } else if (outputFormat === 'base64') {
        result = bytesToBase64(signatureArray)
      } else {
        result = bytesToHex(signatureArray)
      }

      setHmacResult(result)

    } catch (err) {
      setError('HMAC生成失败：' + (err as Error).message)
    } finally {
      setIsProcessing(false)
    }
  }

  // 工具函数
  const hexToBytes = (hex: string): Uint8Array => {
    const cleanHex = hex.replace(/\s+/g, '')
    if (cleanHex.length % 2 !== 0) {
      throw new Error('十六进制字符串长度必须为偶数')
    }
    const bytes = new Uint8Array(cleanHex.length / 2)
    for (let i = 0; i < cleanHex.length; i += 2) {
      bytes[i / 2] = parseInt(cleanHex.substr(i, 2), 16)
    }
    return bytes
  }

  const base64ToBytes = (base64: string): Uint8Array => {
    const binaryString = atob(base64)
    const bytes = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    return bytes
  }

  const bytesToHex = (bytes: Uint8Array): string => {
    return Array.from(bytes).map(byte => byte.toString(16).padStart(2, '0')).join('')
  }

  const bytesToBase64 = (bytes: Uint8Array): string => {
    return btoa(String.fromCharCode(...Array.from(bytes)))
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 生成随机密钥
  const generateRandomKey = () => {
    const keyLength = 32 // 256 bits
    const randomBytes = new Uint8Array(keyLength)
    window.crypto.getRandomValues(randomBytes)
    
    if (keyFormat === 'text') {
      // 生成随机字符串
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      let result = ''
      for (let i = 0; i < keyLength; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      setSecretKey(result)
    } else if (keyFormat === 'hex') {
      const hexKey = bytesToHex(randomBytes)
      setSecretKey(hexKey)
    } else if (keyFormat === 'base64') {
      const base64Key = bytesToBase64(randomBytes)
      setSecretKey(base64Key)
    }
  }

  // 清空所有内容
  const clearAll = () => {
    setSecretKey('')
    setMessage('')
    setHmacResult('')
    setError('')
  }

  // 下载结果
  const downloadResult = () => {
    const content = `HMAC Result:
Algorithm: ${algorithm}
Secret Key: ${secretKey}
Message: ${message}
HMAC: ${hmacResult}
Generated at: ${new Date().toISOString()}`

    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `hmac-${algorithm.toLowerCase()}-${Date.now()}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Hash className="w-8 h-8" />
          HMAC 生成器
        </h1>
        <p className="text-muted-foreground">
          生成和验证基于哈希的消息认证码（HMAC），确保消息完整性和真实性
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generate">
            <Shield className="w-4 h-4 mr-2" />
            生成 HMAC
          </TabsTrigger>
          <TabsTrigger value="verify">
            <CheckCircle className="w-4 h-4 mr-2" />
            验证 HMAC
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          {/* 算法设置 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">算法设置</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>HMAC算法</Label>
                <select
                  value={algorithm}
                  onChange={(e) => setAlgorithm(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  {algorithms.map(algo => (
                    <option key={algo.value} value={algo.value}>
                      {algo.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label>输出格式</Label>
                <select
                  value={outputFormat}
                  onChange={(e) => setOutputFormat(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  {formats.map(fmt => (
                    <option key={fmt.value} value={fmt.value}>
                      {fmt.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </Card>

          {/* 密钥输入 */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">密钥 (Secret Key)</h3>
              <Button 
                variant="outline" 
                size="sm"
                onClick={generateRandomKey}
              >
                <Key className="w-4 h-4 mr-2" />
                生成随机密钥
              </Button>
            </div>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>密钥格式</Label>
                <select
                  value={keyFormat}
                  onChange={(e) => setKeyFormat(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  {formats.map(fmt => (
                    <option key={fmt.value} value={fmt.value}>
                      {fmt.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label>密钥内容</Label>
                <Textarea
                  value={secretKey}
                  onChange={(e) => setSecretKey(e.target.value)}
                  placeholder="输入密钥..."
                  className="min-h-[100px] font-mono"
                />
              </div>
            </div>
          </Card>

          {/* 消息输入 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">消息内容</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>消息格式</Label>
                <select
                  value={messageFormat}
                  onChange={(e) => setMessageFormat(e.target.value)}
                  className="w-full p-2 border rounded-md"
                >
                  {formats.map(fmt => (
                    <option key={fmt.value} value={fmt.value}>
                      {fmt.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label>消息内容</Label>
                <Textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="输入要生成HMAC的消息..."
                  className="min-h-[120px] font-mono"
                />
              </div>
            </div>
          </Card>

          {/* 操作按钮 */}
          <div className="flex gap-3">
            <Button 
              onClick={generateHMAC} 
              disabled={isProcessing}
              className="flex items-center gap-2"
            >
              <Hash className="w-4 h-4" />
              {isProcessing ? '生成中...' : '生成 HMAC'}
            </Button>
            <Button variant="outline" onClick={clearAll}>
              清空
            </Button>
          </div>

          {/* 错误显示 */}
          {error && (
            <Card className="p-4 border-red-200 bg-red-50 dark:bg-red-900/20">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                <div>
                  <p className="font-medium text-red-800 dark:text-red-200">错误信息</p>
                  <p className="text-red-700 dark:text-red-300">{error}</p>
                </div>
              </div>
            </Card>
          )}

          {/* 结果显示 */}
          {hmacResult && (
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">HMAC 结果</h3>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(hmacResult)}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadResult}
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">算法：</span>
                    <span className="ml-2">{algorithm}</span>
                  </div>
                  <div>
                    <span className="font-medium">输出格式：</span>
                    <span className="ml-2">{outputFormat.toUpperCase()}</span>
                  </div>
                  <div>
                    <span className="font-medium">HMAC长度：</span>
                    <span className="ml-2">{hmacResult.length} 字符</span>
                  </div>
                  <div>
                    <span className="font-medium">生成时间：</span>
                    <span className="ml-2">{new Date().toLocaleString()}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>HMAC 值</Label>
                  <Textarea
                    value={hmacResult}
                    readOnly
                    className="min-h-[80px] font-mono text-sm bg-muted"
                  />
                </div>
              </div>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="verify" className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">HMAC 验证</h3>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                要验证HMAC，请在&ldquo;生成HMAC&rdquo;标签页中输入相同的密钥和消息，然后点击&ldquo;生成HMAC&rdquo;。
                如果生成的HMAC与原始值相同，则验证成功。
              </p>
              
              <div className="space-y-2">
                <Label>待验证的HMAC值</Label>
                <Textarea
                  placeholder="粘贴要验证的HMAC值..."
                  className="min-h-[80px] font-mono text-sm"
                />
              </div>
              
              <Button onClick={generateHMAC} disabled={isProcessing}>
                <CheckCircle className="w-4 h-4 mr-2" />
                验证 HMAC
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 安全警告 */}
      <Card className="p-6 border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20">
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-yellow-800 dark:text-yellow-200">安全提醒：</p>
            <ul className="mt-1 text-yellow-700 dark:text-yellow-300 space-y-1">
              <li>• 密钥应该保密，不要与他人分享</li>
              <li>• 使用足够长度的随机密钥（至少32字节）</li>
              <li>• 定期更换密钥以提高安全性</li>
              <li>• 避免在URL或日志中传输密钥</li>
              <li>• 推荐使用SHA-256或更高版本的算法</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* 使用说明 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">使用说明</h3>
        <div className="space-y-4 text-sm text-muted-foreground">
          <div>
            <h4 className="font-medium text-foreground mb-2">什么是HMAC？</h4>
            <p>HMAC（Hash-based Message Authentication Code）是一种基于哈希函数的消息认证码算法，用于验证消息的完整性和真实性。</p>
          </div>
          
          <div>
            <h4 className="font-medium text-foreground mb-2">使用步骤：</h4>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>选择HMAC算法（推荐SHA-256）</li>
              <li>输入或生成密钥</li>
              <li>输入要验证的消息</li>
              <li>点击&quot;生成HMAC&quot;获取结果</li>
              <li>将HMAC值与消息一起发送</li>
            </ol>
          </div>
          
          <div>
            <h4 className="font-medium text-foreground mb-2">应用场景：</h4>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>API请求签名验证</li>
              <li>数据完整性校验</li>
              <li>身份认证</li>
              <li>防止数据篡改</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default HMACGeneratorTool
