'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Sciss<PERSON>, <PERSON><PERSON><PERSON>, Copy, Check, Upload, Download, Info, ArrowRight, ArrowLeft } from 'lucide-react'

interface SplitOptions {
  delimiter: string
  customDelimiter: string
  trimItems: boolean
  removeEmpty: boolean
  removeDuplicates: boolean
  caseSensitive: boolean
}

interface MergeOptions {
  delimiter: string
  customDelimiter: string
  addSpaceAfterDelimiter: boolean
  wrapInQuotes: boolean
  escapeQuotes: boolean
}

export default function TextSplitMergeTool() {
  const [inputText, setInputText] = useState('')
  const [outputText, setOutputText] = useState('')
  const [mode, setMode] = useState<'split' | 'merge'>('split')
  const [splitOptions, setSplitOptions] = useState<SplitOptions>({
    delimiter: 'newline',
    customDelimiter: ',',
    trimItems: true,
    removeEmpty: true,
    removeDuplicates: false,
    caseSensitive: true,
  })
  const [mergeOptions, setMergeOptions] = useState<MergeOptions>({
    delimiter: 'comma',
    customDelimiter: ',',
    addSpaceAfterDelimiter: true,
    wrapInQuotes: false,
    escapeQuotes: true,
  })
  const [copied, setCopied] = useState(false)
  const [itemCount, setItemCount] = useState(0)
  
  // 获取实际的分隔符
  const getDelimiter = (delimiter: string, customDelimiter: string): string => {
    switch (delimiter) {
      case 'newline':
        return '\n'
      case 'comma':
        return ','
      case 'semicolon':
        return ';'
      case 'space':
        return ' '
      case 'tab':
        return '\t'
      case 'pipe':
        return '|'
      case 'custom':
        return customDelimiter
      default:
        return '\n'
    }
  }
  
  // 执行分割
  const performSplit = useCallback(() => {
    if (!inputText.trim()) {
      setOutputText('')
      setItemCount(0)
      return
    }
    
    const delimiter = getDelimiter(splitOptions.delimiter, splitOptions.customDelimiter)
    let items = inputText.split(delimiter)
    
    // 处理选项
    if (splitOptions.trimItems) {
      items = items.map(item => item.trim())
    }
    
    if (splitOptions.removeEmpty) {
      items = items.filter(item => item.length > 0)
    }
    
    if (splitOptions.removeDuplicates) {
      const seen = new Set<string>()
      items = items.filter(item => {
        const key = splitOptions.caseSensitive ? item : item.toLowerCase()
        if (seen.has(key)) {
          return false
        }
        seen.add(key)
        return true
      })
    }
    
    setOutputText(items.join('\n'))
    setItemCount(items.length)
  }, [inputText, splitOptions])
  
  // 执行合并
  const performMerge = useCallback(() => {
    if (!inputText.trim()) {
      setOutputText('')
      setItemCount(0)
      return
    }
    
    let items = inputText.split('\n').filter(item => item.trim().length > 0)
    
    // 处理引号包裹
    if (mergeOptions.wrapInQuotes) {
      items = items.map(item => {
        let processedItem = item
        if (mergeOptions.escapeQuotes) {
          processedItem = item.replace(/"/g, '\\"')
        }
        return `"${processedItem}"`
      })
    }
    
    // 获取合并分隔符
    let delimiter = getDelimiter(mergeOptions.delimiter, mergeOptions.customDelimiter)
    if (mergeOptions.addSpaceAfterDelimiter && delimiter !== ' ') {
      delimiter += ' '
    }
    
    setOutputText(items.join(delimiter))
    setItemCount(items.length)
  }, [inputText, mergeOptions])
  
  // 复制结果
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(outputText)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }
  
  // 上传文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputText(content)
    }
    reader.readAsText(file)
  }
  
  // 下载结果
  const downloadResult = () => {
    if (!outputText) return
    
    const blob = new Blob([outputText], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${mode === 'split' ? 'split' : 'merged'}_text_${new Date().getTime()}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }
  
  // 交换输入输出
  const swapTexts = () => {
    const temp = inputText
    setInputText(outputText)
    setOutputText(temp)
  }
  
  // 预设示例
  const examples = {
    split: [
      {
        name: '邮箱列表',
        text: '<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>',
        delimiter: 'comma',
      },
      {
        name: 'CSV数据',
        text: 'Name;Age;City;Country\nJohn;25;New York;USA\nMary;30;London;UK\nTom;28;Tokyo;Japan',
        delimiter: 'semicolon',
      },
      {
        name: '标签列表',
        text: 'JavaScript|React|Vue|Angular|TypeScript|Node.js|Express',
        delimiter: 'pipe',
      },
      {
        name: '句子分割',
        text: '这是第一句话。这是第二句话。这是第三句话。',
        delimiter: 'custom',
        customDelimiter: '。',
      },
    ],
    merge: [
      {
        name: '创建数组',
        text: 'apple\nbanana\norange\ngrape',
        wrapInQuotes: true,
        delimiter: 'comma',
      },
      {
        name: '创建SQL值',
        text: "John's Shop\nMary's Store\nTom's Market",
        wrapInQuotes: true,
        escapeQuotes: true,
        delimiter: 'comma',
      },
      {
        name: '创建标签',
        text: 'javascript\nreact\nvue\nangular',
        delimiter: 'pipe',
      },
    ],
  }
  
  // 应用示例
  const applyExample = (example: any) => {
    setInputText(example.text)
    if (mode === 'split') {
      setSplitOptions(prev => ({
        ...prev,
        delimiter: example.delimiter || 'newline',
        customDelimiter: example.customDelimiter || prev.customDelimiter,
      }))
    } else {
      setMergeOptions(prev => ({
        ...prev,
        delimiter: example.delimiter || 'comma',
        wrapInQuotes: example.wrapInQuotes || false,
        escapeQuotes: example.escapeQuotes || false,
      }))
    }
  }
  
  // 监听变化自动处理
  useEffect(() => {
    if (mode === 'split') {
      performSplit()
    } else {
      performMerge()
    }
  }, [inputText, mode, splitOptions, mergeOptions, performSplit, performMerge])
  
  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本分割合并工具</h2>
        <p className="text-gray-600">
          灵活地分割文本为多行，或将多行文本合并为一行
        </p>
      </div>
      
      {/* 模式切换 */}
      <div className="mb-6 flex justify-center">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          <button
            onClick={() => setMode('split')}
            className={`px-4 py-2 rounded-md flex items-center gap-2 transition-colors ${
              mode === 'split'
                ? 'bg-blue-500 text-white'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Scissors className="w-4 h-4" />
            分割模式
          </button>
          <button
            onClick={() => setMode('merge')}
            className={`px-4 py-2 rounded-md flex items-center gap-2 transition-colors ${
              mode === 'merge'
                ? 'bg-blue-500 text-white'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Combine className="w-4 h-4" />
            合并模式
          </button>
        </div>
      </div>
      
      {/* 示例 */}
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-3">快速示例</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {examples[mode].map((example, index) => (
            <button
              key={index}
              onClick={() => applyExample(example)}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              {example.name}
            </button>
          ))}
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">
              {mode === 'split' ? '输入文本（待分割）' : '输入文本（每行一个项目）'}
            </label>
            <label className="text-sm text-blue-500 hover:text-blue-600 cursor-pointer">
              <input
                type="file"
                accept=".txt"
                onChange={handleFileUpload}
                className="hidden"
              />
              <span className="flex items-center gap-1">
                <Upload className="w-4 h-4" />
                上传文件
              </span>
            </label>
          </div>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder={
              mode === 'split'
                ? '请输入要分割的文本...'
                : '请输入要合并的文本，每行一个项目...'
            }
            className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
          />
        </div>
        
        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">
              {mode === 'split' ? '分割结果（每行一个）' : '合并结果（单行）'}
            </label>
            <div className="flex gap-2">
              <button
                onClick={swapTexts}
                className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                title="交换输入输出"
              >
                <ArrowLeft className="w-4 h-4" />
                <ArrowRight className="w-4 h-4" />
              </button>
              <button
                onClick={copyToClipboard}
                disabled={!outputText}
                className="text-sm text-blue-500 hover:text-blue-600 disabled:text-gray-400 flex items-center gap-1"
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    复制
                  </>
                )}
              </button>
              <button
                onClick={downloadResult}
                disabled={!outputText}
                className="text-sm text-blue-500 hover:text-blue-600 disabled:text-gray-400 flex items-center gap-1"
              >
                <Download className="w-4 h-4" />
                下载
              </button>
            </div>
          </div>
          <textarea
            value={outputText}
            readOnly
            placeholder={
              mode === 'split'
                ? '分割结果将显示在这里...'
                : '合并结果将显示在这里...'
            }
            className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none bg-gray-50 font-mono text-sm"
          />
          <div className="mt-2 text-sm text-gray-500">
            项目数: {itemCount}
          </div>
        </div>
      </div>
      
      {/* 选项设置 */}
      <div className="mt-6 p-6 bg-white border border-gray-200 rounded-lg">
        <h3 className="font-semibold mb-4">
          {mode === 'split' ? '分割选项' : '合并选项'}
        </h3>
        
        {mode === 'split' ? (
          <div className="space-y-4">
            {/* 分隔符选择 */}
            <div>
              <label className="block text-sm font-medium mb-2">分隔符</label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="splitDelimiter"
                    value="comma"
                    checked={splitOptions.delimiter === 'comma'}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>逗号 (,)</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="splitDelimiter"
                    value="semicolon"
                    checked={splitOptions.delimiter === 'semicolon'}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>分号 (;)</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="splitDelimiter"
                    value="space"
                    checked={splitOptions.delimiter === 'space'}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>空格</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="splitDelimiter"
                    value="newline"
                    checked={splitOptions.delimiter === 'newline'}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>换行符</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="splitDelimiter"
                    value="tab"
                    checked={splitOptions.delimiter === 'tab'}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>制表符</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="splitDelimiter"
                    value="pipe"
                    checked={splitOptions.delimiter === 'pipe'}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>竖线 (|)</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="splitDelimiter"
                    value="custom"
                    checked={splitOptions.delimiter === 'custom'}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>自定义:</span>
                  <input
                    type="text"
                    value={splitOptions.customDelimiter}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, customDelimiter: e.target.value }))}
                    disabled={splitOptions.delimiter !== 'custom'}
                    className="w-20 px-2 py-1 border border-gray-300 rounded text-sm disabled:bg-gray-100"
                    placeholder="分隔符"
                  />
                </label>
              </div>
            </div>
            
            {/* 处理选项 */}
            <div>
              <label className="block text-sm font-medium mb-2">处理选项</label>
              <div className="space-y-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={splitOptions.trimItems}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, trimItems: e.target.checked }))}
                    className="text-blue-500"
                  />
                  <span>修剪空白字符</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={splitOptions.removeEmpty}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, removeEmpty: e.target.checked }))}
                    className="text-blue-500"
                  />
                  <span>移除空项</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={splitOptions.removeDuplicates}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, removeDuplicates: e.target.checked }))}
                    className="text-blue-500"
                  />
                  <span>去除重复项</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={splitOptions.caseSensitive}
                    onChange={(e) => setSplitOptions(prev => ({ ...prev, caseSensitive: e.target.checked }))}
                    className="text-blue-500"
                    disabled={!splitOptions.removeDuplicates}
                  />
                  <span>区分大小写（去重时）</span>
                </label>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* 合并分隔符选择 */}
            <div>
              <label className="block text-sm font-medium mb-2">合并分隔符</label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="mergeDelimiter"
                    value="comma"
                    checked={mergeOptions.delimiter === 'comma'}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>逗号 (,)</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="mergeDelimiter"
                    value="semicolon"
                    checked={mergeOptions.delimiter === 'semicolon'}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>分号 (;)</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="mergeDelimiter"
                    value="space"
                    checked={mergeOptions.delimiter === 'space'}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>空格</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="mergeDelimiter"
                    value="newline"
                    checked={mergeOptions.delimiter === 'newline'}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>换行符</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="mergeDelimiter"
                    value="tab"
                    checked={mergeOptions.delimiter === 'tab'}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>制表符</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="mergeDelimiter"
                    value="pipe"
                    checked={mergeOptions.delimiter === 'pipe'}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>竖线 (|)</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="mergeDelimiter"
                    value="custom"
                    checked={mergeOptions.delimiter === 'custom'}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, delimiter: e.target.value }))}
                    className="text-blue-500"
                  />
                  <span>自定义:</span>
                  <input
                    type="text"
                    value={mergeOptions.customDelimiter}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, customDelimiter: e.target.value }))}
                    disabled={mergeOptions.delimiter !== 'custom'}
                    className="w-20 px-2 py-1 border border-gray-300 rounded text-sm disabled:bg-gray-100"
                    placeholder="分隔符"
                  />
                </label>
              </div>
            </div>
            
            {/* 合并选项 */}
            <div>
              <label className="block text-sm font-medium mb-2">合并选项</label>
              <div className="space-y-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={mergeOptions.addSpaceAfterDelimiter}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, addSpaceAfterDelimiter: e.target.checked }))}
                    className="text-blue-500"
                  />
                  <span>分隔符后添加空格</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={mergeOptions.wrapInQuotes}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, wrapInQuotes: e.target.checked }))}
                    className="text-blue-500"
                  />
                  <span>用引号包裹每个项目</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={mergeOptions.escapeQuotes}
                    onChange={(e) => setMergeOptions(prev => ({ ...prev, escapeQuotes: e.target.checked }))}
                    className="text-blue-500"
                    disabled={!mergeOptions.wrapInQuotes}
                  />
                  <span>转义引号（用于SQL等）</span>
                </label>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>分割模式：将一段文本按指定分隔符分割成多行</li>
              <li>合并模式：将多行文本合并成一行，用指定分隔符连接</li>
              <li>支持多种常用分隔符和自定义分隔符</li>
              <li>可以去除重复项、修剪空白、添加引号等</li>
              <li>适用于处理CSV数据、创建数组、SQL值等场景</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
