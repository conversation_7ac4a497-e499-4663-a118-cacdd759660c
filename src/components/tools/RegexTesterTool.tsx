'use client'

import { useState, useEffect, useCallback } from 'react'
import { Co<PERSON>, Check, AlertCircle, FileText, Code, Search } from 'lucide-react'

interface RegexMatch {
  match: string
  index: number
  groups: string[]
}

interface RegexExample {
  name: string
  pattern: string
  description: string
  example: string
}

export default function RegexTesterTool() {
  const [pattern, setPattern] = useState('')
  const [flags, setFlags] = useState('g')
  const [testText, setTestText] = useState('')
  const [matches, setMatches] = useState<RegexMatch[]>([])
  const [error, setError] = useState('')
  const [copied, setCopied] = useState('')
  const [highlightedText, setHighlightedText] = useState('')
  const [showExamples, setShowExamples] = useState(true)

  // 常用正则表达式示例
  const examples: RegexExample[] = [
    {
      name: '邮箱地址',
      pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
      description: '匹配标准邮箱地址格式',
      example: '<EMAIL>',
    },
    {
      name: '手机号码（中国）',
      pattern: '^1[3-9]\\d{9}$',
      description: '匹配中国大陆手机号码',
      example: '13812345678',
    },
    {
      name: 'URL网址',
      pattern: '^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&\\/\\/=]*)$',
      description: '匹配HTTP/HTTPS网址',
      example: 'https://www.example.com/path',
    },
    {
      name: 'IPv4地址',
      pattern: '^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
      description: '匹配IPv4地址格式',
      example: '***********',
    },
    {
      name: '日期（YYYY-MM-DD）',
      pattern: '^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$',
      description: '匹配YYYY-MM-DD格式的日期',
      example: '2024-01-15',
    },
    {
      name: '身份证号（中国）',
      pattern: '^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$',
      description: '匹配18位中国身份证号码',
      example: '110101199001011234',
    },
    {
      name: 'HEX颜色代码',
      pattern: '^#?([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$',
      description: '匹配3位或6位的十六进制颜色代码',
      example: '#FF6B6B',
    },
    {
      name: '密码强度',
      pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$',
      description: '至少8位，包含大小写字母、数字和特殊字符',
      example: 'Pass123!',
    },
  ]

  // 测试正则表达式
  const testRegex = useCallback(() => {
    if (!pattern) {
      setMatches([])
      setError('')
      setHighlightedText(testText)
      return
    }

    try {
      const regex = new RegExp(pattern, flags)
      const foundMatches: RegexMatch[] = []
      let match
      let lastIndex = 0

      // 如果包含 g 标志，使用 exec 循环获取所有匹配
      if (flags.includes('g')) {
        while ((match = regex.exec(testText)) !== null) {
          foundMatches.push({
            match: match[0],
            index: match.index,
            groups: match.slice(1),
          })
          
          // 防止无限循环（零宽度匹配）
          if (match.index === regex.lastIndex) {
            regex.lastIndex++
          }
        }
      } else {
        // 不包含 g 标志，只获取第一个匹配
        match = regex.exec(testText)
        if (match) {
          foundMatches.push({
            match: match[0],
            index: match.index,
            groups: match.slice(1),
          })
        }
      }

      setMatches(foundMatches)
      setError('')

      // 高亮匹配的文本
      if (foundMatches.length > 0) {
        let highlighted = testText
        const sortedMatches = [...foundMatches].sort((a, b) => b.index - a.index)
        
        sortedMatches.forEach((m) => {
          const before = highlighted.substring(0, m.index)
          const matched = highlighted.substring(m.index, m.index + m.match.length)
          const after = highlighted.substring(m.index + m.match.length)
          highlighted = before + `<mark class="bg-yellow-200 px-0.5">${matched}</mark>` + after
        })
        
        setHighlightedText(highlighted)
      } else {
        setHighlightedText(testText)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '无效的正则表达式')
      setMatches([])
      setHighlightedText(testText)
    }
  }, [pattern, flags, testText])

  // 监听输入变化
  useEffect(() => {
    testRegex()
  }, [testRegex])

  // 复制到剪贴板
  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(type)
      setTimeout(() => setCopied(''), 2000)
    })
  }

  // 应用示例
  const applyExample = (example: RegexExample) => {
    setPattern(example.pattern)
    setTestText(example.example)
  }

  // 生成代码片段
  const generateCode = (language: string): string => {
    switch (language) {
      case 'javascript':
        return `const regex = /${pattern}/${flags};\nconst result = regex.test("${testText.replace(/"/g, '\\"')}");`
      case 'python':
        return `import re\n\npattern = r"${pattern}"\ntext = "${testText.replace(/"/g, '\\"')}"\nresult = re.search(pattern, text${flags.includes('i') ? ', re.IGNORECASE' : ''})`;
      case 'java':
        return `Pattern pattern = Pattern.compile("${pattern}"${flags.includes('i') ? ', Pattern.CASE_INSENSITIVE' : ''});\nMatcher matcher = pattern.matcher("${testText.replace(/"/g, '\\"')}");\nboolean found = matcher.find();`;
      case 'php':
        return `$pattern = '/${pattern}/${flags}';\n$text = "${testText.replace(/"/g, '\\"')}";\n$result = preg_match($pattern, $text);`;
      default:
        return ''
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">正则表达式测试器</h2>
        <p className="text-gray-600">
          在线测试和调试正则表达式，支持实时匹配和高亮显示
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧输入区域 */}
        <div className="space-y-4">
          {/* 正则表达式输入 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3">正则表达式</h3>
            
            <div className="space-y-3">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-gray-500">/</span>
                  <input
                    type="text"
                    value={pattern}
                    onChange={(e) => setPattern(e.target.value)}
                    placeholder="输入正则表达式"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md font-mono"
                  />
                  <span className="text-gray-500">/</span>
                  <input
                    type="text"
                    value={flags}
                    onChange={(e) => setFlags(e.target.value)}
                    placeholder="flags"
                    className="w-20 px-3 py-2 border border-gray-300 rounded-md font-mono"
                  />
                </div>
                
                {error && (
                  <div className="flex items-center gap-2 text-red-600 text-sm">
                    <AlertCircle className="w-4 h-4" />
                    <span>{error}</span>
                  </div>
                )}
              </div>

              {/* 标志选项 */}
              <div className="flex flex-wrap gap-4 text-sm">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={flags.includes('g')}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFlags(flags + 'g')
                      } else {
                        setFlags(flags.replace('g', ''))
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="font-mono">g</span> - 全局匹配
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={flags.includes('i')}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFlags(flags + 'i')
                      } else {
                        setFlags(flags.replace('i', ''))
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="font-mono">i</span> - 忽略大小写
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={flags.includes('m')}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFlags(flags + 'm')
                      } else {
                        setFlags(flags.replace('m', ''))
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="font-mono">m</span> - 多行模式
                </label>
              </div>
            </div>
          </div>

          {/* 测试文本输入 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3 flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              测试文本
            </h3>
            
            <textarea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              placeholder="输入要测试的文本..."
              className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md resize-none"
            />
          </div>

          {/* 匹配结果 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3 flex items-center">
              <Search className="w-5 h-5 mr-2" />
              匹配结果
            </h3>
            
            {matches.length > 0 ? (
              <div className="space-y-2">
                <div className="text-sm text-gray-600 mb-2">
                  找到 {matches.length} 个匹配
                </div>
                {matches.map((match, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded border border-gray-200">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium">匹配 #{index + 1}</span>
                      <span className="text-xs text-gray-500">索引: {match.index}</span>
                    </div>
                    <div className="font-mono text-sm bg-white p-2 rounded border">
                      {match.match}
                    </div>
                    {match.groups.length > 0 && (
                      <div className="mt-2">
                        <div className="text-xs text-gray-500 mb-1">捕获组:</div>
                        {match.groups.map((group, gIndex) => (
                          <div key={gIndex} className="text-sm font-mono">
                            ${gIndex + 1}: {group || '(空)'}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-gray-500 text-sm">
                {pattern ? '没有找到匹配' : '输入正则表达式开始测试'}
              </div>
            )}
          </div>
        </div>

        {/* 右侧功能区域 */}
        <div className="space-y-4">
          {/* 高亮显示 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3">高亮显示</h3>
            <div 
              className="p-3 bg-gray-50 rounded border border-gray-200 min-h-[100px] whitespace-pre-wrap break-words"
              dangerouslySetInnerHTML={{ __html: highlightedText || '(空)' }}
            />
          </div>

          {/* 代码生成 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3 flex items-center">
              <Code className="w-5 h-5 mr-2" />
              代码生成
            </h3>
            
            <div className="space-y-3">
              {['javascript', 'python', 'java', 'php'].map((lang) => (
                <div key={lang}>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium capitalize">{lang}</span>
                    <button
                      onClick={() => copyToClipboard(generateCode(lang), lang)}
                      className="p-1 hover:bg-gray-100 rounded"
                    >
                      {copied === lang ? (
                        <Check className="w-4 h-4 text-green-600" />
                      ) : (
                        <Copy className="w-4 h-4 text-gray-400" />
                      )}
                    </button>
                  </div>
                  <pre className="p-2 bg-gray-50 rounded text-xs overflow-x-auto">
                    <code>{generateCode(lang)}</code>
                  </pre>
                </div>
              ))}
            </div>
          </div>

          {/* 常用示例 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <button
              onClick={() => setShowExamples(!showExamples)}
              className="flex items-center justify-between w-full mb-3"
            >
              <h3 className="font-medium">常用正则表达式</h3>
              <span className="text-gray-400 text-sm">{showExamples ? '收起' : '展开'}</span>
            </button>
            
            {showExamples && (
              <div className="space-y-2">
                {examples.map((example) => (
                  <button
                    key={example.name}
                    onClick={() => applyExample(example)}
                    className="w-full text-left p-3 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
                  >
                    <div className="font-medium text-sm">{example.name}</div>
                    <div className="text-xs text-gray-600 mt-1">{example.description}</div>
                    <div className="font-mono text-xs text-gray-500 mt-1">{example.pattern}</div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持 JavaScript 正则表达式语法</li>
              <li>实时显示匹配结果和高亮</li>
              <li>支持捕获组和多种标志</li>
              <li>提供多种编程语言的代码示例</li>
              <li>内置常用正则表达式模板</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
