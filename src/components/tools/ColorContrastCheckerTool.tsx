'use client';

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { 
  Eye, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Palette, 
  Copy, 
  Download,
  Lightbulb,
  Users,
  FileText,
  Zap,
  Target,
  Settings
} from 'lucide-react';

interface Color {
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
}

interface ContrastResult {
  ratio: number;
  wcagAA: boolean;
  wcagAAA: boolean;
  wcagAALarge: boolean;
  wcagAAALarge: boolean;
}

interface AccessibilityReport {
  foreground: Color;
  background: Color;
  contrast: ContrastResult;
  recommendations: string[];
  simulationResults: {
    normal: Color;
    protanopia: Color;
    deuteranopia: Color;
    tritanopia: Color;
    achromatopsia: Color;
  };
}

const ColorContrastCheckerTool: React.FC = () => {
  const [foregroundColor, setForegroundColor] = useState('#000000');
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [fontSize, setFontSize] = useState([16]);
  const [fontWeight, setFontWeight] = useState('normal');
  const [textType, setTextType] = useState('normal');
  const [showColorBlindness, setShowColorBlindness] = useState(false);
  const [bulkColors, setBulkColors] = useState('');
  const [activeTab, setActiveTab] = useState('checker');
  const [previewText, setPreviewText] = useState('The quick brown fox jumps over the lazy dog');

  // Color conversion utilities
  const hexToRgb = useCallback((hex: string): { r: number; g: number; b: number } => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 };
  }, []);

  const rgbToHex = useCallback((r: number, g: number, b: number): string => {
    return `#${[r, g, b].map(x => {
      const hex = Math.round(x).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    }).join('')}`;
  }, []);

  const rgbToHsl = useCallback((r: number, g: number, b: number): { h: number; s: number; l: number } => {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r:
          h = (g - b) / d + (g < b ? 6 : 0);
          break;
        case g:
          h = (b - r) / d + 2;
          break;
        case b:
          h = (r - g) / d + 4;
          break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100 };
  }, []);

  const createColor = useCallback((hex: string): Color => {
    const rgb = hexToRgb(hex);
    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
    return { hex, rgb, hsl };
  }, [hexToRgb, rgbToHsl]);

  // Luminance calculation
  const getLuminance = useCallback((rgb: { r: number; g: number; b: number }): number => {
    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }, []);

  // Contrast ratio calculation
  const getContrastRatio = useCallback((color1: Color, color2: Color): number => {
    const l1 = getLuminance(color1.rgb);
    const l2 = getLuminance(color2.rgb);
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }, [getLuminance]);

  // WCAG compliance check
  const checkWCAGCompliance = useCallback((ratio: number, isLarge: boolean = false): ContrastResult => {
    return {
      ratio,
      wcagAA: isLarge ? ratio >= 3.0 : ratio >= 4.5,
      wcagAAA: isLarge ? ratio >= 4.5 : ratio >= 7.0,
      wcagAALarge: ratio >= 3.0,
      wcagAAALarge: ratio >= 4.5
    };
  }, []);

  // Color blindness simulation
  const simulateColorBlindness = useCallback((color: Color) => {
    const { r, g, b } = color.rgb;
    
    // Protanopia (red-blind)
    const protanopia = {
      r: 0.567 * r + 0.433 * g,
      g: 0.558 * r + 0.442 * g,
      b: 0.242 * g + 0.758 * b
    };

    // Deuteranopia (green-blind)
    const deuteranopia = {
      r: 0.625 * r + 0.375 * g,
      g: 0.7 * r + 0.3 * g,
      b: 0.3 * g + 0.7 * b
    };

    // Tritanopia (blue-blind)
    const tritanopia = {
      r: 0.95 * r + 0.05 * g,
      g: 0.433 * g + 0.567 * b,
      b: 0.475 * g + 0.525 * b
    };

    // Achromatopsia (total color blindness)
    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
    const achromatopsia = { r: gray, g: gray, b: gray };

    return {
      normal: color,
      protanopia: createColor(rgbToHex(protanopia.r, protanopia.g, protanopia.b)),
      deuteranopia: createColor(rgbToHex(deuteranopia.r, deuteranopia.g, deuteranopia.b)),
      tritanopia: createColor(rgbToHex(tritanopia.r, tritanopia.g, tritanopia.b)),
      achromatopsia: createColor(rgbToHex(achromatopsia.r, achromatopsia.g, achromatopsia.b))
    };
  }, [createColor, rgbToHex]);

  // Generate accessibility report
  const generateReport = useCallback((fg: Color, bg: Color): AccessibilityReport => {
    const contrast = getContrastRatio(fg, bg);
    const isLarge = fontSize[0] >= 18 || (fontSize[0] >= 14 && fontWeight === 'bold');
    const contrastResult = checkWCAGCompliance(contrast, isLarge);

    const recommendations: string[] = [];
    
    if (!contrastResult.wcagAA) {
      recommendations.push('Increase contrast to meet WCAG AA standards (4.5:1 minimum)');
    }
    if (!contrastResult.wcagAAA) {
      recommendations.push('Consider higher contrast for WCAG AAA standards (7:1 minimum)');
    }
    if (contrast < 2) {
      recommendations.push('Very poor contrast - text may be unreadable for many users');
    }
    if (contrast >= 7) {
      recommendations.push('Excellent contrast - accessible to all users');
    }

    return {
      foreground: fg,
      background: bg,
      contrast: contrastResult,
      recommendations,
      simulationResults: simulateColorBlindness(fg)
    };
  }, [getContrastRatio, checkWCAGCompliance, fontSize, fontWeight, simulateColorBlindness]);

  // Current analysis
  const currentReport = useMemo(() => {
    const fg = createColor(foregroundColor);
    const bg = createColor(backgroundColor);
    return generateReport(fg, bg);
  }, [foregroundColor, backgroundColor, createColor, generateReport]);

  // Bulk color analysis
  const bulkAnalysis = useMemo(() => {
    if (!bulkColors.trim()) return [];
    
    const lines = bulkColors.trim().split('\n');
    const results: AccessibilityReport[] = [];
    
    for (const line of lines) {
      const colors = line.split(',').map(c => c.trim());
      if (colors.length >= 2) {
        try {
          const fg = createColor(colors[0]);
          const bg = createColor(colors[1]);
          results.push(generateReport(fg, bg));
        } catch (error) {
          // Skip invalid color pairs
        }
      }
    }
    
    return results;
  }, [bulkColors, createColor, generateReport]);

  // Suggest better colors
  const suggestBetterColors = useCallback((fg: Color, bg: Color, targetRatio: number = 4.5) => {
    const suggestions: { foreground?: string; background?: string; ratio: number }[] = [];
    
    // Try adjusting lightness
    const fgHsl = fg.hsl;
    const bgHsl = bg.hsl;
    
    // Darken foreground
    for (let l = Math.max(0, fgHsl.l - 20); l >= 0; l -= 10) {
      const testColor = createColor(`hsl(${fgHsl.h}, ${fgHsl.s}%, ${l}%)`);
      const ratio = getContrastRatio(testColor, bg);
      if (ratio >= targetRatio) {
        suggestions.push({ foreground: testColor.hex, ratio });
        break;
      }
    }
    
    // Lighten background
    for (let l = Math.min(100, bgHsl.l + 20); l <= 100; l += 10) {
      const testColor = createColor(`hsl(${bgHsl.h}, ${bgHsl.s}%, ${l}%)`);
      const ratio = getContrastRatio(fg, testColor);
      if (ratio >= targetRatio) {
        suggestions.push({ background: testColor.hex, ratio });
        break;
      }
    }
    
    return suggestions.slice(0, 3);
  }, [createColor, getContrastRatio]);

  // Copy functions
  const copyReport = useCallback(() => {
    const report = `
Accessibility Report
==================
Foreground: ${currentReport.foreground.hex}
Background: ${currentReport.background.hex}
Contrast Ratio: ${currentReport.contrast.ratio.toFixed(2)}:1

WCAG Compliance:
- AA Normal: ${currentReport.contrast.wcagAA ? '✓' : '✗'}
- AAA Normal: ${currentReport.contrast.wcagAAA ? '✓' : '✗'}
- AA Large: ${currentReport.contrast.wcagAALarge ? '✓' : '✗'}
- AAA Large: ${currentReport.contrast.wcagAAALarge ? '✓' : '✗'}

Recommendations:
${currentReport.recommendations.map(r => `- ${r}`).join('\n')}
    `.trim();
    
    navigator.clipboard.writeText(report);
  }, [currentReport]);

  const downloadReport = useCallback(() => {
    const report = {
      timestamp: new Date().toISOString(),
      foreground: currentReport.foreground,
      background: currentReport.background,
      contrast: currentReport.contrast,
      recommendations: currentReport.recommendations,
      colorBlindnessSimulation: currentReport.simulationResults
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'accessibility-report.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [currentReport]);

  // Get status color and icon
  const getStatusInfo = (ratio: number) => {
    if (ratio >= 7) {
      return { color: 'text-green-600', bg: 'bg-green-50', icon: CheckCircle, label: 'Excellent' };
    } else if (ratio >= 4.5) {
      return { color: 'text-blue-600', bg: 'bg-blue-50', icon: CheckCircle, label: 'Good' };
    } else if (ratio >= 3) {
      return { color: 'text-yellow-600', bg: 'bg-yellow-50', icon: AlertTriangle, label: 'Fair' };
    } else {
      return { color: 'text-red-600', bg: 'bg-red-50', icon: XCircle, label: 'Poor' };
    }
  };

  const statusInfo = getStatusInfo(currentReport.contrast.ratio);
  const StatusIcon = statusInfo.icon;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl shadow-lg">
              <Eye className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Color Contrast Checker
            </h1>
          </div>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Ensure your designs meet WCAG accessibility standards with comprehensive contrast analysis,
            color blindness simulation, and detailed accessibility reports.
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="checker" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Checker
            </TabsTrigger>
            <TabsTrigger value="bulk" className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              Bulk Analysis
            </TabsTrigger>
            <TabsTrigger value="simulation" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Color Blindness
            </TabsTrigger>
            <TabsTrigger value="report" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Report
            </TabsTrigger>
          </TabsList>

          <TabsContent value="checker" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="lg:col-span-1">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Color Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="foreground-color">Foreground (Text)</Label>
                    <div className="flex gap-2">
                      <Input
                        id="foreground-color"
                        type="color"
                        value={foregroundColor}
                        onChange={(e) => setForegroundColor(e.target.value)}
                        className="w-16 h-10 p-1 border-2"
                      />
                      <Input
                        value={foregroundColor}
                        onChange={(e) => setForegroundColor(e.target.value)}
                        placeholder="#000000"
                        className="flex-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="background-color">Background</Label>
                    <div className="flex gap-2">
                      <Input
                        id="background-color"
                        type="color"
                        value={backgroundColor}
                        onChange={(e) => setBackgroundColor(e.target.value)}
                        className="w-16 h-10 p-1 border-2"
                      />
                      <Input
                        value={backgroundColor}
                        onChange={(e) => setBackgroundColor(e.target.value)}
                        placeholder="#ffffff"
                        className="flex-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Font Size: {fontSize[0]}px</Label>
                    <Slider
                      value={fontSize}
                      onValueChange={setFontSize}
                      min={10}
                      max={48}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="font-weight">Font Weight</Label>
                    <Select value={fontWeight} onValueChange={setFontWeight}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="bold">Bold</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="text-type">Text Type</Label>
                    <Select value={textType} onValueChange={setTextType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="normal">Normal Text</SelectItem>
                        <SelectItem value="large">Large Text</SelectItem>
                        <SelectItem value="ui">UI Components</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              <Card className="lg:col-span-2">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Analysis Results</CardTitle>
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full ${statusInfo.bg}`}>
                      <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />
                      <span className={`text-sm font-medium ${statusInfo.color}`}>
                        {statusInfo.label}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Preview */}
                  <div className="space-y-4">
                    <Label htmlFor="preview-text">Preview Text</Label>
                    <Input
                      id="preview-text"
                      value={previewText}
                      onChange={(e) => setPreviewText(e.target.value)}
                      placeholder="Enter text to preview"
                    />
                    <div 
                      className="p-6 border-2 border-dashed border-gray-300 rounded-lg"
                      style={{ backgroundColor: backgroundColor }}
                    >
                      <p 
                        style={{
                          color: foregroundColor,
                          fontSize: `${fontSize[0]}px`,
                          fontWeight: fontWeight,
                          lineHeight: '1.5'
                        }}
                      >
                        {previewText}
                      </p>
                    </div>
                  </div>

                  {/* Contrast Results */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">
                        {currentReport.contrast.ratio.toFixed(2)}:1
                      </div>
                      <div className="text-sm text-gray-600">Contrast Ratio</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className={`text-2xl font-bold ${currentReport.contrast.wcagAA ? 'text-green-600' : 'text-red-600'}`}>
                        {currentReport.contrast.wcagAA ? '✓' : '✗'}
                      </div>
                      <div className="text-sm text-gray-600">WCAG AA</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className={`text-2xl font-bold ${currentReport.contrast.wcagAAA ? 'text-green-600' : 'text-red-600'}`}>
                        {currentReport.contrast.wcagAAA ? '✓' : '✗'}
                      </div>
                      <div className="text-sm text-gray-600">WCAG AAA</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className={`text-2xl font-bold ${currentReport.contrast.wcagAALarge ? 'text-green-600' : 'text-red-600'}`}>
                        {currentReport.contrast.wcagAALarge ? '✓' : '✗'}
                      </div>
                      <div className="text-sm text-gray-600">Large Text</div>
                    </div>
                  </div>

                  {/* Recommendations */}
                  {currentReport.recommendations.length > 0 && (
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Lightbulb className="w-4 h-4" />
                        Recommendations
                      </Label>
                      <div className="space-y-2">
                        {currentReport.recommendations.map((rec, index) => (
                          <div key={index} className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                            <AlertTriangle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-blue-800">{rec}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Suggested Improvements */}
                  <div className="space-y-2">
                    <Label>Suggested Improvements</Label>
                    <div className="grid grid-cols-1 gap-2">
                      {suggestBetterColors(currentReport.foreground, currentReport.background).map((suggestion, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            {suggestion.foreground && (
                              <div className="flex items-center gap-2">
                                <div 
                                  className="w-4 h-4 rounded border"
                                  style={{ backgroundColor: suggestion.foreground }}
                                />
                                <span className="text-sm">Foreground: {suggestion.foreground}</span>
                              </div>
                            )}
                            {suggestion.background && (
                              <div className="flex items-center gap-2">
                                <div 
                                  className="w-4 h-4 rounded border"
                                  style={{ backgroundColor: suggestion.background }}
                                />
                                <span className="text-sm">Background: {suggestion.background}</span>
                              </div>
                            )}
                          </div>
                          <Badge variant="outline" className="text-green-700">
                            {suggestion.ratio.toFixed(2)}:1
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="bulk" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Bulk Color Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="bulk-colors">Color Pairs (Format: foreground,background per line)</Label>
                  <textarea
                    id="bulk-colors"
                    className="w-full h-32 p-3 border rounded-lg resize-none"
                    value={bulkColors}
                    onChange={(e) => setBulkColors(e.target.value)}
                    placeholder="#000000,#ffffff&#x0A;#333333,#f0f0f0&#x0A;#0066cc,#ffffff"
                  />
                </div>
                
                {bulkAnalysis.length > 0 && (
                  <div className="space-y-2">
                    <Label>Analysis Results ({bulkAnalysis.length} pairs)</Label>
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {bulkAnalysis.map((result, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-2">
                              <div 
                                className="w-4 h-4 rounded border"
                                style={{ backgroundColor: result.foreground.hex }}
                              />
                              <div 
                                className="w-4 h-4 rounded border"
                                style={{ backgroundColor: result.background.hex }}
                              />
                            </div>
                            <span className="text-sm font-mono">
                              {result.foreground.hex} / {result.background.hex}
                            </span>
                          </div>
                          <div className="flex items-center gap-3">
                            <span className="text-sm font-medium">
                              {result.contrast.ratio.toFixed(2)}:1
                            </span>
                            <div className="flex gap-1">
                              {result.contrast.wcagAA && <Badge className="text-xs bg-green-100 text-green-800">AA</Badge>}
                              {result.contrast.wcagAAA && <Badge className="text-xs bg-blue-100 text-blue-800">AAA</Badge>}
                              {!result.contrast.wcagAA && <Badge variant="destructive" className="text-xs">Fail</Badge>}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="simulation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Color Blindness Simulation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(currentReport.simulationResults).map(([type, color]) => (
                    <div key={type} className="space-y-2">
                      <Label className="capitalize">{type.replace(/([A-Z])/g, ' $1').trim()}</Label>
                      <div 
                        className="p-4 border rounded-lg"
                        style={{ backgroundColor: backgroundColor }}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <div 
                            className="w-6 h-6 rounded border"
                            style={{ backgroundColor: color.hex }}
                          />
                          <span className="text-sm font-mono">{color.hex}</span>
                        </div>
                        <p 
                          style={{
                            color: color.hex,
                            fontSize: `${fontSize[0]}px`,
                            fontWeight: fontWeight
                          }}
                        >
                          Sample text preview
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="report" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Accessibility Report
                  </CardTitle>
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={copyReport}>
                      <Copy className="w-4 h-4 mr-2" />
                      Copy
                    </Button>
                    <Button variant="outline" onClick={downloadReport}>
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-base font-semibold">Color Information</Label>
                      <div className="mt-2 space-y-2">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-4 h-4 rounded border"
                            style={{ backgroundColor: currentReport.foreground.hex }}
                          />
                          <span className="text-sm">Foreground: {currentReport.foreground.hex}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-4 h-4 rounded border"
                            style={{ backgroundColor: currentReport.background.hex }}
                          />
                          <span className="text-sm">Background: {currentReport.background.hex}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-base font-semibold">Contrast Analysis</Label>
                      <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-center">
                          {currentReport.contrast.ratio.toFixed(2)}:1
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label className="text-base font-semibold">WCAG Compliance</Label>
                      <div className="mt-2 space-y-2">
                        <div className="flex justify-between items-center">
                          <span>AA Normal Text</span>
                          <Badge variant={currentReport.contrast.wcagAA ? "default" : "destructive"}>
                            {currentReport.contrast.wcagAA ? 'Pass' : 'Fail'}
                          </Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>AAA Normal Text</span>
                          <Badge variant={currentReport.contrast.wcagAAA ? "default" : "destructive"}>
                            {currentReport.contrast.wcagAAA ? 'Pass' : 'Fail'}
                          </Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>AA Large Text</span>
                          <Badge variant={currentReport.contrast.wcagAALarge ? "default" : "destructive"}>
                            {currentReport.contrast.wcagAALarge ? 'Pass' : 'Fail'}
                          </Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>AAA Large Text</span>
                          <Badge variant={currentReport.contrast.wcagAAALarge ? "default" : "destructive"}>
                            {currentReport.contrast.wcagAAALarge ? 'Pass' : 'Fail'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {currentReport.recommendations.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-base font-semibold">Recommendations</Label>
                    <div className="space-y-2">
                      {currentReport.recommendations.map((rec, index) => (
                        <div key={index} className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                          <Lightbulb className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-blue-800">{rec}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ColorContrastCheckerTool;