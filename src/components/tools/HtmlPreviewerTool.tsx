'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Copy, Check, Play, Code, Eye, Download, RotateCcw, Maximize2 } from 'lucide-react'

interface PreviewState {
  html: string
  css: string
  javascript: string
  autoRun: boolean
  theme: 'light' | 'dark'
}

const defaultHTML = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML 预览器</title>
</head>
<body>
    <div class="container">
        <h1>欢迎使用 HTML 预览器</h1>
        <p>在左侧编辑器中编写 HTML、CSS 和 JavaScript 代码，右侧会实时预览效果。</p>
        <button id="myButton">点击我</button>
        <div id="output"></div>
    </div>
</body>
</html>`

const defaultCSS = `body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
    color: #333;
    margin-bottom: 20px;
}

p {
    color: #666;
    line-height: 1.6;
}

button {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #2563eb;
}

#output {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f9ff;
    border-radius: 4px;
    min-height: 50px;
}`

const defaultJS = `// 获取按钮和输出元素
const button = document.getElementById('myButton');
const output = document.getElementById('output');

// 添加点击事件
button.addEventListener('click', function() {
    const now = new Date();
    output.innerHTML = '<strong>你点击了按钮！</strong><br>当前时间：' + now.toLocaleString('zh-CN');
    output.style.display = 'block';
});

// 初始隐藏输出区域
output.style.display = 'none';`

export default function HtmlPreviewerTool() {
  const [state, setState] = useState<PreviewState>({
    html: defaultHTML,
    css: defaultCSS,
    javascript: defaultJS,
    autoRun: true,
    theme: 'light'
  })
  
  const [activeTab, setActiveTab] = useState<'html' | 'css' | 'javascript'>('html')
  const [copied, setCopied] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [error, setError] = useState<string | null>(null)

  // 更新预览
  const updatePreview = useCallback(() => {
    if (!iframeRef.current) return

    try {
      const doc = iframeRef.current.contentDocument
      if (!doc) return

      // 构建完整的 HTML
      const fullHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>${state.css}</style>
        </head>
        <body>
          ${state.html.replace(/<\/?html[^>]*>/gi, '').replace(/<\/?head[^>]*>/gi, '').replace(/<\/?body[^>]*>/gi, '')}
          <script>
            try {
              ${state.javascript}
            } catch (error) {
              console.error('JavaScript 错误:', error);
            }
          </script>
        </body>
        </html>
      `

      doc.open()
      doc.write(fullHTML)
      doc.close()
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : '预览更新失败')
    }
  }, [state.html, state.css, state.javascript])

  // 自动运行
  useEffect(() => {
    if (state.autoRun) {
      const timer = setTimeout(updatePreview, 500)
      return () => clearTimeout(timer)
    }
  }, [state.autoRun, updatePreview])

  // 手动运行
  const handleRun = () => {
    updatePreview()
  }

  // 复制代码
  const copyCode = () => {
    const fullCode = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
${state.css}
  </style>
</head>
<body>
${state.html.replace(/<\/?html[^>]*>/gi, '').replace(/<\/?head[^>]*>/gi, '').replace(/<\/?body[^>]*>/gi, '')}
  <script>
${state.javascript}
  </script>
</body>
</html>`

    navigator.clipboard.writeText(fullCode).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  // 下载 HTML 文件
  const downloadHTML = () => {
    const fullCode = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
${state.css}
  </style>
</head>
<body>
${state.html.replace(/<\/?html[^>]*>/gi, '').replace(/<\/?head[^>]*>/gi, '').replace(/<\/?body[^>]*>/gi, '')}
  <script>
${state.javascript}
  </script>
</body>
</html>`

    const blob = new Blob([fullCode], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'preview.html'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 重置代码
  const resetCode = () => {
    setState({
      html: defaultHTML,
      css: defaultCSS,
      javascript: defaultJS,
      autoRun: true,
      theme: 'light'
    })
  }

  // 更新代码
  const updateCode = (type: 'html' | 'css' | 'javascript', value: string) => {
    setState(prev => ({ ...prev, [type]: value }))
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">HTML 预览器</h2>
        <p className="text-gray-600">
          实时编辑和预览 HTML、CSS、JavaScript 代码，支持即时渲染和代码高亮
        </p>
      </div>

      <div className={`grid ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : 'lg:grid-cols-2'} gap-6`}>
        {/* 左侧：代码编辑器 */}
        <div className={isFullscreen ? 'hidden' : ''}>
          <div className="bg-white rounded-lg border border-gray-300">
            {/* 标签页和操作按钮 */}
            <div className="flex items-center justify-between border-b">
              <div className="flex">
                <button
                  onClick={() => setActiveTab('html')}
                  className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'html'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Code className="w-4 h-4 inline mr-1" />
                  HTML
                </button>
                <button
                  onClick={() => setActiveTab('css')}
                  className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'css'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Code className="w-4 h-4 inline mr-1" />
                  CSS
                </button>
                <button
                  onClick={() => setActiveTab('javascript')}
                  className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === 'javascript'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Code className="w-4 h-4 inline mr-1" />
                  JavaScript
                </button>
              </div>
              
              <div className="flex items-center gap-2 px-4">
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={state.autoRun}
                    onChange={(e) => setState(prev => ({ ...prev, autoRun: e.target.checked }))}
                    className="rounded"
                  />
                  自动运行
                </label>
                {!state.autoRun && (
                  <button
                    onClick={handleRun}
                    className="flex items-center gap-1 px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                  >
                    <Play className="w-4 h-4" />
                    运行
                  </button>
                )}
              </div>
            </div>

            {/* 代码编辑区 */}
            <div className="p-4">
              <textarea
                value={state[activeTab]}
                onChange={(e) => updateCode(activeTab, e.target.value)}
                className="w-full h-[500px] p-4 font-mono text-sm bg-gray-50 border border-gray-200 rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                spellCheck={false}
                placeholder={`在此输入 ${activeTab.toUpperCase()} 代码...`}
              />
            </div>

            {/* 底部操作栏 */}
            <div className="flex justify-between items-center p-4 border-t">
              <div className="flex gap-2">
                <button
                  onClick={copyCode}
                  className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  {copied ? '已复制' : '复制完整代码'}
                </button>
                <button
                  onClick={downloadHTML}
                  className="flex items-center gap-2 px-3 py-1.5 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  下载 HTML
                </button>
                <button
                  onClick={resetCode}
                  className="flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                >
                  <RotateCcw className="w-4 h-4" />
                  重置
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：预览区域 */}
        <div className={isFullscreen ? 'h-full' : ''}>
          <div className={`bg-white rounded-lg border border-gray-300 ${isFullscreen ? 'h-full' : ''}`}>
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Eye className="w-5 h-5" />
                预览
              </h3>
              <button
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="p-2 hover:bg-gray-100 rounded transition-colors"
                title={isFullscreen ? '退出全屏' : '全屏预览'}
              >
                <Maximize2 className="w-5 h-5" />
              </button>
            </div>
            
            {error && (
              <div className="p-4 bg-red-50 text-red-600 text-sm">
                错误：{error}
              </div>
            )}
            
            <div className={`${isFullscreen ? 'h-[calc(100%-60px)]' : 'h-[500px]'} bg-white`}>
              <iframe
                ref={iframeRef}
                className="w-full h-full border-0"
                title="HTML Preview"
                sandbox="allow-scripts allow-same-origin"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-8 bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-3">使用说明</h3>
        <ul className="space-y-2 text-sm text-gray-700">
          <li>• 在左侧编辑器中编写 HTML、CSS 和 JavaScript 代码</li>
          <li>• 开启&ldquo;自动运行&rdquo;后，代码会在停止输入 0.5 秒后自动更新预览</li>
          <li>• 关闭&ldquo;自动运行&rdquo;后，需要手动点击&ldquo;运行&rdquo;按钮更新预览</li>
          <li>• 支持完整的 HTML5、CSS3 和 ES6+ JavaScript 语法</li>
          <li>• 点击&ldquo;复制完整代码&rdquo;可以复制包含所有代码的完整 HTML 文件</li>
          <li>• 点击&ldquo;下载 HTML&rdquo;可以将代码保存为 HTML 文件</li>
          <li>• JavaScript 错误会在浏览器控制台中显示</li>
        </ul>
      </div>
    </div>
  )
}
