'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Copy, RefreshCw, Trash2, Download, Upload, CheckCircle, AlertCircle, Info, BookOpen, Lightbulb, FileText } from 'lucide-react';

interface GrammarError {
  id: string;
  type: 'grammar' | 'spelling' | 'punctuation' | 'style' | 'clarity';
  severity: 'error' | 'warning' | 'suggestion';
  message: string;
  suggestion: string;
  start: number;
  end: number;
  text: string;
  rule?: string;
}

interface GrammarRule {
  id: string;
  name: string;
  description: string;
  category: string;
  enabled: boolean;
  severity: 'error' | 'warning' | 'suggestion';
}

interface CheckOptions {
  language: 'en' | 'zh' | 'auto';
  checkSpelling: boolean;
  checkGrammar: boolean;
  checkPunctuation: boolean;
  checkStyle: boolean;
  checkClarity: boolean;
  strictMode: boolean;
  ignoreUppercase: boolean;
  ignoreMixedCase: boolean;
  ignoreNumbers: boolean;
}

const DEFAULT_RULES: GrammarRule[] = [
  {
    id: 'double-space',
    name: '双空格检查',
    description: '检查多余的空格',
    category: 'punctuation',
    enabled: true,
    severity: 'warning'
  },
  {
    id: 'sentence-start-uppercase',
    name: '句首大写',
    description: '句子开头应该大写',
    category: 'grammar',
    enabled: true,
    severity: 'error'
  },
  {
    id: 'repeated-words',
    name: '重复词汇',
    description: '检查连续重复的词汇',
    category: 'style',
    enabled: true,
    severity: 'warning'
  },
  {
    id: 'passive-voice',
    name: '被动语态',
    description: '建议使用主动语态',
    category: 'style',
    enabled: false,
    severity: 'suggestion'
  },
  {
    id: 'long-sentences',
    name: '长句检查',
    description: '句子过长可能影响阅读',
    category: 'clarity',
    enabled: true,
    severity: 'suggestion'
  },
  {
    id: 'comma-splice',
    name: '逗号拼接',
    description: '检查逗号拼接错误',
    category: 'grammar',
    enabled: true,
    severity: 'error'
  },
  {
    id: 'apostrophe-usage',
    name: '撇号使用',
    description: '检查撇号的正确使用',
    category: 'punctuation',
    enabled: true,
    severity: 'warning'
  },
  {
    id: 'redundant-phrases',
    name: '冗余短语',
    description: '检查可以简化的冗余表达',
    category: 'clarity',
    enabled: true,
    severity: 'suggestion'
  }
];

const COMMON_ERRORS = {
  en: [
    { pattern: /\bthere\s+is\s+\w+\s+that\b/gi, suggestion: '考虑简化表达', type: 'clarity' },
    { pattern: /\bit\s+is\s+\w+\s+that\b/gi, suggestion: '考虑简化表达', type: 'clarity' },
    { pattern: /\bvery\s+unique\b/gi, suggestion: '"unique"本身就是绝对的，不需要"very"', type: 'style' },
    { pattern: /\bcould\s+care\s+less\b/gi, suggestion: '应该是"couldn\'t care less"', type: 'grammar' },
    { pattern: /\bfor\s+all\s+intensive\s+purposes\b/gi, suggestion: '应该是"for all intents and purposes"', type: 'grammar' },
    { pattern: /\byour\s+welcome\b/gi, suggestion: '应该是"you\'re welcome"', type: 'grammar' },
    { pattern: /\bits\s+vs\s+it\'s\b/gi, suggestion: '"its"表示所有格，"it\'s"是"it is"的缩写', type: 'grammar' },
    { pattern: /\bthen\s+vs\s+than\b/gi, suggestion: '"then"表示时间，"than"用于比较', type: 'grammar' }
  ],
  zh: [
    { pattern: /的的/g, suggestion: '避免连续使用"的"', type: 'style' },
    { pattern: /了了/g, suggestion: '避免连续使用"了"', type: 'style' },
    { pattern: /在在/g, suggestion: '避免连续使用"在"', type: 'style' },
    { pattern: /是是/g, suggestion: '避免连续使用"是"', type: 'style' },
    { pattern: /。。/g, suggestion: '避免连续使用句号', type: 'punctuation' },
    { pattern: /，，/g, suggestion: '避免连续使用逗号', type: 'punctuation' },
    { pattern: /！！/g, suggestion: '避免连续使用感叹号', type: 'punctuation' },
    { pattern: /？？/g, suggestion: '避免连续使用问号', type: 'punctuation' }
  ]
};

export default function GrammarCheckerTool() {
  const [input, setInput] = useState('');
  const [options, setOptions] = useState<CheckOptions>({
    language: 'auto',
    checkSpelling: true,
    checkGrammar: true,
    checkPunctuation: true,
    checkStyle: true,
    checkClarity: true,
    strictMode: false,
    ignoreUppercase: false,
    ignoreMixedCase: false,
    ignoreNumbers: true
  });
  const [rules, setRules] = useState<GrammarRule[]>(DEFAULT_RULES);
  const [selectedError, setSelectedError] = useState<string | null>(null);
  const [error, setError] = useState('');

  const detectLanguage = useCallback((text: string): 'en' | 'zh' => {
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    const englishWords = text.match(/[a-zA-Z]+/g);
    
    const chineseRatio = chineseChars ? chineseChars.length / text.length : 0;
    const englishRatio = englishWords ? englishWords.join('').length / text.length : 0;
    
    return chineseRatio > englishRatio ? 'zh' : 'en';
  }, []);

  const checkGrammar = useMemo(() => {
    try {
      setError('');
      
      if (!input.trim()) return [];
      
      const detectedLang = options.language === 'auto' ? detectLanguage(input) : options.language;
      const errors: GrammarError[] = [];
      const enabledRules = rules.filter(rule => rule.enabled);
      
      // 基础检查
      if (options.checkPunctuation) {
        // 双空格检查
        if (enabledRules.find(r => r.id === 'double-space')) {
          const doubleSpaceMatches = input.match(/  +/g);
          if (doubleSpaceMatches) {
            let searchIndex = 0;
            doubleSpaceMatches.forEach((match, index) => {
              const matchIndex = input.indexOf(match, searchIndex);
              if (matchIndex !== -1) {
                errors.push({
                  id: `double-space-${index}`,
                  type: 'punctuation',
                  severity: 'warning',
                  message: '发现多余的空格',
                  suggestion: '删除多余的空格',
                  start: matchIndex,
                  end: matchIndex + match.length,
                  text: match,
                  rule: 'double-space'
                });
                searchIndex = matchIndex + match.length;
              }
            });
          }
        }
        
        // 标点符号前的空格
        const punctuationSpaceMatches = input.match(/\s+[,.!?;:]/g);
        if (punctuationSpaceMatches) {
          let searchIndex = 0;
          punctuationSpaceMatches.forEach((match, index) => {
            const matchIndex = input.indexOf(match, searchIndex);
            if (matchIndex !== -1) {
              errors.push({
                id: `punct-space-${index}`,
                type: 'punctuation',
                severity: 'warning',
                message: '标点符号前不应有空格',
                suggestion: '删除标点符号前的空格',
                start: matchIndex,
                end: matchIndex + match.length,
                text: match,
                rule: 'punctuation-spacing'
              });
              searchIndex = matchIndex + match.length;
            }
          });
        }
      }
      
      if (options.checkGrammar) {
        // 句首大写检查（英文）
        if (detectedLang === 'en' && enabledRules.find(r => r.id === 'sentence-start-uppercase')) {
          const sentences = input.split(/[.!?]+\s*/);
          let currentPos = 0;
          
          sentences.forEach((sentence, index) => {
            const trimmed = sentence.trim();
            if (trimmed && /^[a-z]/.test(trimmed)) {
              const sentenceStart = input.indexOf(trimmed, currentPos);
              if (sentenceStart !== -1) {
                errors.push({
                  id: `uppercase-${index}`,
                  type: 'grammar',
                  severity: 'error',
                  message: '句子开头应该大写',
                  suggestion: `将"${trimmed[0]}"改为"${trimmed[0].toUpperCase()}"`,
                  start: sentenceStart,
                  end: sentenceStart + 1,
                  text: trimmed[0],
                  rule: 'sentence-start-uppercase'
                });
              }
            }
            currentPos += sentence.length + 1;
          });
        }
      }
      
      if (options.checkStyle) {
        // 重复词汇检查
        if (enabledRules.find(r => r.id === 'repeated-words')) {
          const words = input.split(/\s+/);
          for (let i = 0; i < words.length - 1; i++) {
            const currentWord = words[i].toLowerCase().replace(/[^\w]/g, '');
            const nextWord = words[i + 1].toLowerCase().replace(/[^\w]/g, '');
            
            if (currentWord && currentWord === nextWord && currentWord.length > 2) {
              const wordStart = input.indexOf(words[i], i > 0 ? input.indexOf(words[i - 1]) + words[i - 1].length : 0);
              const wordEnd = wordStart + words[i].length + words[i + 1].length + 1;
              
              errors.push({
                id: `repeated-${i}`,
                type: 'style',
                severity: 'warning',
                message: '发现重复的词汇',
                suggestion: '删除重复的词汇',
                start: wordStart,
                end: wordEnd,
                text: `${words[i]} ${words[i + 1]}`,
                rule: 'repeated-words'
              });
            }
          }
        }
      }
      
      if (options.checkClarity) {
        // 长句检查
        if (enabledRules.find(r => r.id === 'long-sentences')) {
          const sentences = input.split(/[.!?]+/);
          let currentPos = 0;
          
          sentences.forEach((sentence, index) => {
            const trimmed = sentence.trim();
            const wordCount = trimmed.split(/\s+/).length;
            
            if (wordCount > 25) {
              const sentenceStart = input.indexOf(trimmed, currentPos);
              if (sentenceStart !== -1) {
                errors.push({
                  id: `long-sentence-${index}`,
                  type: 'clarity',
                  severity: 'suggestion',
                  message: `句子过长（${wordCount}个词）`,
                  suggestion: '考虑将长句分解为多个短句',
                  start: sentenceStart,
                  end: sentenceStart + trimmed.length,
                  text: trimmed,
                  rule: 'long-sentences'
                });
              }
            }
            currentPos += sentence.length + 1;
          });
        }
      }
      
      // 语言特定检查
      const commonErrors = COMMON_ERRORS[detectedLang] || [];
      commonErrors.forEach((errorPattern, index) => {
        const matches = input.match(errorPattern.pattern);
        if (matches) {
          let searchIndex = 0;
          matches.forEach((match, matchIndex) => {
            const matchPos = input.indexOf(match, searchIndex);
            if (matchPos !== -1) {
              errors.push({
                id: `common-${detectedLang}-${index}-${matchIndex}`,
                type: errorPattern.type as any,
                severity: 'warning',
                message: errorPattern.suggestion,
                suggestion: errorPattern.suggestion,
                start: matchPos,
                end: matchPos + match.length,
                text: match,
                rule: `common-${detectedLang}-${index}`
              });
              searchIndex = matchPos + match.length;
            }
          });
        }
      });
      
      return errors.sort((a, b) => a.start - b.start);
    } catch (err) {
      setError(err instanceof Error ? err.message : '语法检查失败');
      return [];
    }
  }, [input, options, rules, detectLanguage]);

  const statistics = useMemo(() => {
    const words = input.trim().split(/\s+/).filter(word => word.length > 0);
    const sentences = input.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = input.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const characters = input.length;
    const charactersNoSpaces = input.replace(/\s/g, '').length;
    
    const errorsByType = checkGrammar.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const errorsBySeverity = checkGrammar.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const avgWordsPerSentence = sentences.length > 0 ? Math.round(words.length / sentences.length) : 0;
    const avgSentencesPerParagraph = paragraphs.length > 0 ? Math.round(sentences.length / paragraphs.length) : 0;
    
    return {
      words: words.length,
      sentences: sentences.length,
      paragraphs: paragraphs.length,
      characters,
      charactersNoSpaces,
      avgWordsPerSentence,
      avgSentencesPerParagraph,
      errorsByType,
      errorsBySeverity,
      readingTime: Math.ceil(words.length / 200) // 假设每分钟200词
    };
  }, [input, checkGrammar]);

  const highlightedText = useMemo(() => {
    if (!input || checkGrammar.length === 0) return input;
    
    let result = input;
    const sortedErrors = [...checkGrammar].sort((a, b) => b.start - a.start);
    
    sortedErrors.forEach(error => {
      const before = result.substring(0, error.start);
      const after = result.substring(error.end);
      const errorText = result.substring(error.start, error.end);
      
      const className = `error-${error.severity} ${selectedError === error.id ? 'selected' : ''}`;
      const highlighted = `<span class="${className}" data-error-id="${error.id}" title="${error.message}">${errorText}</span>`;
      
      result = before + highlighted + after;
    });
    
    return result;
  }, [input, checkGrammar, selectedError]);

  const updateOption = <K extends keyof CheckOptions>(key: K, value: CheckOptions[K]) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const updateRule = (ruleId: string, updates: Partial<GrammarRule>) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, ...updates } : rule
    ));
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(input);
    } catch (err) {
      setError('复制失败');
    }
  };

  const handleClear = () => {
    setInput('');
    setSelectedError(null);
    setError('');
  };

  const handleReset = () => {
    setOptions({
      language: 'auto',
      checkSpelling: true,
      checkGrammar: true,
      checkPunctuation: true,
      checkStyle: true,
      checkClarity: true,
      strictMode: false,
      ignoreUppercase: false,
      ignoreMixedCase: false,
      ignoreNumbers: true
    });
    setRules(DEFAULT_RULES);
    setSelectedError(null);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 1024 * 1024) {
      setError('文件大小不能超过1MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setInput(content);
    };
    reader.readAsText(file);
  };

  const handleDownload = () => {
    if (!input) return;
    
    const report = `语法检查报告
================

文本统计：
- 字数：${statistics.words}
- 句数：${statistics.sentences}
- 段落数：${statistics.paragraphs}
- 字符数：${statistics.characters}
- 平均句长：${statistics.avgWordsPerSentence}词

错误统计：
- 总错误数：${checkGrammar.length}
- 语法错误：${statistics.errorsByType.grammar || 0}
- 拼写错误：${statistics.errorsByType.spelling || 0}
- 标点错误：${statistics.errorsByType.punctuation || 0}
- 风格问题：${statistics.errorsByType.style || 0}
- 清晰度问题：${statistics.errorsByType.clarity || 0}

详细错误列表：
${checkGrammar.map((error, index) => 
  `${index + 1}. [${error.severity.toUpperCase()}] ${error.message}
   位置：${error.start}-${error.end}
   文本："${error.text}"
   建议：${error.suggestion}
`).join('\n')}

原文：
${input}
`;
    
    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'grammar-check-report.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'error': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'suggestion': return <Info className="h-4 w-4 text-blue-500" />;
      default: return <CheckCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error': return 'bg-red-100 text-red-800 border-red-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'suggestion': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const examples = [
    {
      name: '英文示例',
      text: `This is a example of text with some grammar errors. There is many mistakes that needs to be fixed. Your going to find several issues here, including repeated repeated words and very unique phrases. Its important to check you're writing carefully.`,
      language: 'en'
    },
    {
      name: '中文示例',
      text: `这是一个包含语法错误的的示例文本。在这里你会发现一些问题，，包括重复的的词汇和标点符号的的使用错误。检查语法是是很重要的。`,
      language: 'zh'
    },
    {
      name: '长句示例',
      text: `This is an extremely long sentence that contains way too many words and clauses and subclauses and additional information that makes it very difficult to read and understand and follow the main point that the author is trying to make which could be expressed much more clearly and concisely.`,
      language: 'en'
    }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>语法检查器</CardTitle>
          <CardDescription>
            智能检查文本中的语法、拼写、标点和风格问题，支持多语言检测和自定义规则配置。
            提供详细的错误分析和改进建议。
          </CardDescription>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">检查选项</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="language">语言</Label>
              <Select value={options.language} onValueChange={(value: 'en' | 'zh' | 'auto') => updateOption('language', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">自动检测</SelectItem>
                  <SelectItem value="en">英文</SelectItem>
                  <SelectItem value="zh">中文</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="checkGrammar"
                checked={options.checkGrammar}
                onCheckedChange={(checked) => updateOption('checkGrammar', checked)}
              />
              <Label htmlFor="checkGrammar">语法检查</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="checkSpelling"
                checked={options.checkSpelling}
                onCheckedChange={(checked) => updateOption('checkSpelling', checked)}
              />
              <Label htmlFor="checkSpelling">拼写检查</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="checkPunctuation"
                checked={options.checkPunctuation}
                onCheckedChange={(checked) => updateOption('checkPunctuation', checked)}
              />
              <Label htmlFor="checkPunctuation">标点检查</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="checkStyle"
                checked={options.checkStyle}
                onCheckedChange={(checked) => updateOption('checkStyle', checked)}
              />
              <Label htmlFor="checkStyle">风格检查</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="checkClarity"
                checked={options.checkClarity}
                onCheckedChange={(checked) => updateOption('checkClarity', checked)}
              />
              <Label htmlFor="checkClarity">清晰度检查</Label>
            </div>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" onClick={handleReset}>
              <RefreshCw className="h-4 w-4 mr-1" />
              重置选项
            </Button>
            <Button variant="outline" asChild>
              <label>
                <Upload className="h-4 w-4 mr-1" />
                上传文件
                <input
                  type="file"
                  accept=".txt,.md,.doc,.docx"
                  className="hidden"
                  onChange={handleFileUpload}
                />
              </label>
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">输入文本</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="input">请输入要检查的文本</Label>
              <Textarea
                id="input"
                placeholder="在这里输入文本..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                className="min-h-[300px] font-mono text-sm"
              />
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClear}>
                <Trash2 className="h-4 w-4 mr-1" />
                清空
              </Button>
              <Button variant="outline" onClick={handleCopy}>
                <Copy className="h-4 w-4 mr-1" />
                复制
              </Button>
            </div>

            <div className="space-y-2">
              <Label>示例文本：</Label>
              <div className="space-y-2">
                {examples.map((example, index) => (
                  <div key={index} className="p-2 border rounded">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-sm">{example.name}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setInput(example.text)}
                      >
                        使用示例
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground line-clamp-2">
                      {example.text}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">检查结果</CardTitle>
              <div className="flex gap-2">
                {input && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    下载报告
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {error && (
                <div className="text-sm text-red-500">{error}</div>
              )}

              {input && (
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label>文本统计：</Label>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>字数: {statistics.words}</div>
                      <div>句数: {statistics.sentences}</div>
                      <div>段落数: {statistics.paragraphs}</div>
                      <div>字符数: {statistics.characters}</div>
                      <div>平均句长: {statistics.avgWordsPerSentence}词</div>
                      <div>阅读时间: {statistics.readingTime}分钟</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>错误统计：</Label>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline" className="bg-red-50">
                        错误: {statistics.errorsBySeverity.error || 0}
                      </Badge>
                      <Badge variant="outline" className="bg-yellow-50">
                        警告: {statistics.errorsBySeverity.warning || 0}
                      </Badge>
                      <Badge variant="outline" className="bg-blue-50">
                        建议: {statistics.errorsBySeverity.suggestion || 0}
                      </Badge>
                    </div>
                  </div>

                  {checkGrammar.length > 0 && (
                    <div className="space-y-2">
                      <Label>错误详情：</Label>
                      <div className="max-h-[300px] overflow-y-auto space-y-2">
                        {checkGrammar.map((error) => (
                          <div
                            key={error.id}
                            className={`p-2 border rounded cursor-pointer transition-colors ${
                              selectedError === error.id ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                            }`}
                            onClick={() => setSelectedError(selectedError === error.id ? null : error.id)}
                          >
                            <div className="flex items-start gap-2">
                              {getSeverityIcon(error.severity)}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <Badge variant="outline" className={getSeverityColor(error.severity)}>
                                    {error.severity}
                                  </Badge>
                                  <span className="text-xs text-muted-foreground">
                                    位置: {error.start}-{error.end}
                                  </span>
                                </div>
                                <div className="text-sm font-medium mb-1">{error.message}</div>
                                <div className="text-xs text-muted-foreground mb-1">
                                  文本: "{error.text}"
                                </div>
                                <div className="text-xs text-blue-600">
                                  建议: {error.suggestion}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {checkGrammar.length === 0 && input && (
                    <div className="text-center py-8 text-muted-foreground">
                      <CheckCircle className="h-12 w-12 mx-auto mb-2 text-green-500" />
                      <div className="text-lg font-medium">未发现错误</div>
                      <div className="text-sm">您的文本看起来很棒！</div>
                    </div>
                  )}
                </div>
              )}

              {!input && (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-2" />
                  <div className="text-lg font-medium">开始检查</div>
                  <div className="text-sm">请在左侧输入要检查的文本</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 规则配置面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">检查规则配置</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {rules.map((rule) => (
              <div key={rule.id} className="p-3 border rounded space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={rule.enabled}
                      onCheckedChange={(enabled) => updateRule(rule.id, { enabled })}
                    />
                    <span className="font-medium text-sm">{rule.name}</span>
                  </div>
                  <Badge variant="outline" className={getSeverityColor(rule.severity)}>
                    {rule.severity}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground">
                  {rule.description}
                </div>
                <div className="text-xs text-muted-foreground">
                  分类: {rule.category}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                功能特点
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 支持中英文语法检查</li>
                <li>• 自动语言检测</li>
                <li>• 多种错误类型检测</li>
                <li>• 可自定义检查规则</li>
                <li>• 详细的统计分析</li>
                <li>• 错误报告导出</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium">检查类型</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• <span className="text-red-600">错误</span>：严重的语法错误</li>
                <li>• <span className="text-yellow-600">警告</span>：可能的问题</li>
                <li>• <span className="text-blue-600">建议</span>：改进建议</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
