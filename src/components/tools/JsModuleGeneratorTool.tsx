'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Download, Code, FileText, Package, Zap } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface ModuleConfig {
  name: string
  description: string
  author: string
  version: string
  type: 'commonjs' | 'esmodule' | 'umd'
  includeTypes: boolean
  includeTests: boolean
  includeDocs: boolean
  license: string
  functions: FunctionConfig[]
}

interface FunctionConfig {
  name: string
  description: string
  parameters: string[]
  returnType: string
  isAsync: boolean
}

export default function JsModuleGeneratorTool() {
  const [config, setConfig] = useState<ModuleConfig>({
    name: '',
    description: '',
    author: '',
    version: '1.0.0',
    type: 'esmodule',
    includeTypes: true,
    includeTests: true,
    includeDocs: true,
    license: 'MIT',
    functions: []
  })
  const [newFunction, setNewFunction] = useState<FunctionConfig>({
    name: '',
    description: '',
    parameters: [],
    returnType: 'void',
    isAsync: false
  })
  const [paramInput, setParamInput] = useState('')
  const [generatedCode, setGeneratedCode] = useState('')
  const [activeTab, setActiveTab] = useState('main')
  const { toast } = useToast()

  // 添加函数
  const addFunction = useCallback(() => {
    if (!newFunction.name.trim()) {
      toast({
        title: '请输入函数名称',
        description: '函数名称不能为空',
        variant: 'destructive',
      })
      return
    }

    setConfig(prev => ({
      ...prev,
      functions: [...prev.functions, { ...newFunction }]
    }))

    setNewFunction({
      name: '',
      description: '',
      parameters: [],
      returnType: 'void',
      isAsync: false
    })
    setParamInput('')
    
    toast({
      title: '函数已添加',
      description: `函数 ${newFunction.name} 已添加到模块中`,
    })
  }, [newFunction, toast])

  // 删除函数
  const removeFunction = useCallback((index: number) => {
    setConfig(prev => ({
      ...prev,
      functions: prev.functions.filter((_, i) => i !== index)
    }))
  }, [])

  // 添加参数
  const addParameter = useCallback(() => {
    if (!paramInput.trim()) return
    
    setNewFunction(prev => ({
      ...prev,
      parameters: [...prev.parameters, paramInput.trim()]
    }))
    setParamInput('')
  }, [paramInput])

  // 删除参数
  const removeParameter = useCallback((index: number) => {
    setNewFunction(prev => ({
      ...prev,
      parameters: prev.parameters.filter((_, i) => i !== index)
    }))
  }, [])

  // 生成主模块代码
  const generateMainCode = useCallback(() => {
    const { functions, type } = config
    
    let code = ''
    
    // 添加文件头注释
    code += `/**\n * ${config.name}${config.description ? ` - ${config.description}` : ''}\n`
    code += ` * <AUTHOR>
    code += ` * @version ${config.version}\n`
    code += ` * @license ${config.license}\n */\n\n`

    // 生成函数
    functions.forEach(func => {
      code += `/**\n * ${func.description}\n`
      func.parameters.forEach(param => {
        code += ` * @param {*} ${param} - 参数描述\n`
      })
      code += ` * @returns {${func.returnType}}\n */\n`
      
      const params = func.parameters.join(', ')
      const asyncKeyword = func.isAsync ? 'async ' : ''
      
      code += `${type === 'esmodule' ? 'export ' : ''}${asyncKeyword}function ${func.name}(${params}) {\n`
      code += `  // TODO: 实现函数逻辑\n`
      if (func.returnType !== 'void') {
        code += `  return ${func.returnType === 'string' ? "''" : func.returnType === 'number' ? '0' : func.returnType === 'boolean' ? 'false' : 'null'};\n`
      }
      code += `}\n\n`
    })

    // CommonJS 导出
    if (type === 'commonjs' && functions.length > 0) {
      code += 'module.exports = {\n'
      code += functions.map(func => `  ${func.name}`).join(',\n')
      code += '\n};\n'
    }

    // UMD 包装
    if (type === 'umd') {
      const umdTemplate = `(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.${config.name.replace(/[^a-zA-Z0-9]/g, '')} = {}));
}(this, (function (exports) { 'use strict';

${code}

${functions.map(func => `exports.${func.name} = ${func.name};`).join('\n')}

Object.defineProperty(exports, '__esModule', { value: true });

})));`
      code = umdTemplate
    }

    return code
  }, [config])

  // 生成 package.json
  const generatePackageJson = useCallback(() => {
    const packageJson = {
      name: config.name,
      version: config.version,
      description: config.description,
      main: config.type === 'esmodule' ? 'dist/index.js' : 'index.js',
      module: config.type === 'esmodule' ? 'dist/index.esm.js' : undefined,
      types: config.includeTypes ? 'index.d.ts' : undefined,
      scripts: {
        build: 'rollup -c',
        test: config.includeTests ? 'jest' : undefined,
        dev: 'rollup -c -w'
      },
      keywords: [],
      author: config.author,
      license: config.license,
      devDependencies: {
        rollup: '^3.0.0',
        '@rollup/plugin-node-resolve': '^15.0.0',
        '@rollup/plugin-commonjs': '^24.0.0',
        ...(config.includeTypes && { typescript: '^4.9.0' }),
        ...(config.includeTests && { jest: '^29.0.0' })
      }
    }

    return JSON.stringify(packageJson, null, 2)
  }, [config])

  // 生成 TypeScript 定义
  const generateTypeDefinitions = useCallback(() => {
    if (!config.includeTypes) return ''
    
    let code = `/**\n * Type definitions for ${config.name}\n */\n\n`
    
    config.functions.forEach(func => {
      const params = func.parameters.map(param => `${param}: any`).join(', ')
      const returnType = func.returnType === 'void' ? 'void' : 'any'
      
      code += `export declare function ${func.name}(${params}): ${func.isAsync ? 'Promise<' + returnType + '>' : returnType};\n`
    })
    
    return code
  }, [config])

  // 生成测试文件
  const generateTestFile = useCallback(() => {
    if (!config.includeTests) return ''
    
    let code = `import { ${config.functions.map(f => f.name).join(', ')} } from '../index';\n\n`
    
    config.functions.forEach(func => {
      code += `describe('${func.name}', () => {\n`
      code += `  it('should work correctly', () => {\n`
      code += `    // TODO: 编写测试用例\n`
      code += `    expect(${func.name}).toBeDefined();\n`
      code += `  });\n`
      code += `});\n\n`
    })
    
    return code
  }, [config])

  // 生成 README
  const generateReadme = useCallback(() => {
    if (!config.includeDocs) return ''
    
    let readme = `# ${config.name}\n\n`
    readme += `${config.description}\n\n`
    readme += `## 安装\n\n`
    readme += `\`\`\`bash\nnpm install ${config.name}\n\`\`\`\n\n`
    readme += `## 使用\n\n`
    
    if (config.type === 'esmodule') {
      readme += `\`\`\`javascript\nimport { ${config.functions.map(f => f.name).join(', ')} } from '${config.name}';\n\`\`\`\n\n`
    } else {
      readme += `\`\`\`javascript\nconst { ${config.functions.map(f => f.name).join(', ')} } = require('${config.name}');\n\`\`\`\n\n`
    }
    
    readme += `## API\n\n`
    
    config.functions.forEach(func => {
      readme += `### ${func.name}\n\n`
      readme += `${func.description}\n\n`
      readme += `\`\`\`javascript\n${func.name}(${func.parameters.join(', ')})\n\`\`\`\n\n`
    })
    
    readme += `## 许可证\n\n${config.license}\n`
    
    return readme
  }, [config])

  // 生成所有代码
  const generateAllCode = useCallback(() => {
    const mainCode = generateMainCode()
    const packageJson = generatePackageJson()
    const typeDefinitions = generateTypeDefinitions()
    const testFile = generateTestFile()
    const readme = generateReadme()
    
    setGeneratedCode(mainCode)
    
    return {
      'index.js': mainCode,
      'package.json': packageJson,
      ...(config.includeTypes && { 'index.d.ts': typeDefinitions }),
      ...(config.includeTests && { 'test/index.test.js': testFile }),
      ...(config.includeDocs && { 'README.md': readme })
    }
  }, [generateMainCode, generatePackageJson, generateTypeDefinitions, generateTestFile, generateReadme, config])

  // 复制代码
  const copyCode = useCallback((code: string) => {
    navigator.clipboard.writeText(code).then(() => {
      toast({
        title: '复制成功',
        description: '代码已复制到剪贴板',
      })
    }).catch(() => {
      toast({
        title: '复制失败',
        description: '请手动选择并复制',
        variant: 'destructive',
      })
    })
  }, [toast])

  // 下载文件
  const downloadFile = useCallback((filename: string, content: string) => {
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }, [])

  // 下载所有文件
  const downloadAllFiles = useCallback(() => {
    const files = generateAllCode()
    
    Object.entries(files).forEach(([filename, content]) => {
      setTimeout(() => {
        downloadFile(filename, content)
      }, 100)
    })
  }, [generateAllCode, downloadFile])

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            JavaScript 模块生成器
          </CardTitle>
          <CardDescription>
            快速生成标准的 JavaScript 模块模板，支持 CommonJS、ES Module 和 UMD 格式
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 模块配置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">模块名称</Label>
              <Input
                id="name"
                value={config.name}
                onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                placeholder="my-awesome-module"
              />
            </div>
            <div>
              <Label htmlFor="version">版本</Label>
              <Input
                id="version"
                value={config.version}
                onChange={(e) => setConfig(prev => ({ ...prev, version: e.target.value }))}
                placeholder="1.0.0"
              />
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="description">描述</Label>
              <Input
                id="description"
                value={config.description}
                onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                placeholder="模块的功能描述"
              />
            </div>
            <div>
              <Label htmlFor="author">作者</Label>
              <Input
                id="author"
                value={config.author}
                onChange={(e) => setConfig(prev => ({ ...prev, author: e.target.value }))}
                placeholder="Your Name"
              />
            </div>
            <div>
              <Label htmlFor="license">许可证</Label>
              <Select value={config.license} onValueChange={(value) => setConfig(prev => ({ ...prev, license: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="MIT">MIT</SelectItem>
                  <SelectItem value="Apache-2.0">Apache-2.0</SelectItem>
                  <SelectItem value="GPL-3.0">GPL-3.0</SelectItem>
                  <SelectItem value="BSD-3-Clause">BSD-3-Clause</SelectItem>
                  <SelectItem value="ISC">ISC</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 模块类型和选项 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="type">模块类型</Label>
              <Select value={config.type} onValueChange={(value: any) => setConfig(prev => ({ ...prev, type: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="esmodule">ES Module</SelectItem>
                  <SelectItem value="commonjs">CommonJS</SelectItem>
                  <SelectItem value="umd">UMD</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="includeTypes"
                  checked={config.includeTypes}
                  onCheckedChange={(checked) => setConfig(prev => ({ ...prev, includeTypes: checked }))}
                />
                <Label htmlFor="includeTypes">包含 TypeScript 定义</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="includeTests"
                  checked={config.includeTests}
                  onCheckedChange={(checked) => setConfig(prev => ({ ...prev, includeTests: checked }))}
                />
                <Label htmlFor="includeTests">包含测试文件</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="includeDocs"
                  checked={config.includeDocs}
                  onCheckedChange={(checked) => setConfig(prev => ({ ...prev, includeDocs: checked }))}
                />
                <Label htmlFor="includeDocs">包含 README</Label>
              </div>
            </div>
          </div>

          {/* 添加函数 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">添加函数</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="functionName">函数名称</Label>
                  <Input
                    id="functionName"
                    value={newFunction.name}
                    onChange={(e) => setNewFunction(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="functionName"
                  />
                </div>
                <div>
                  <Label htmlFor="returnType">返回类型</Label>
                  <Select value={newFunction.returnType} onValueChange={(value) => setNewFunction(prev => ({ ...prev, returnType: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="void">void</SelectItem>
                      <SelectItem value="string">string</SelectItem>
                      <SelectItem value="number">number</SelectItem>
                      <SelectItem value="boolean">boolean</SelectItem>
                      <SelectItem value="object">object</SelectItem>
                      <SelectItem value="array">array</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="functionDescription">函数描述</Label>
                  <Input
                    id="functionDescription"
                    value={newFunction.description}
                    onChange={(e) => setNewFunction(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="函数的功能描述"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isAsync"
                  checked={newFunction.isAsync}
                  onCheckedChange={(checked) => setNewFunction(prev => ({ ...prev, isAsync: checked }))}
                />
                <Label htmlFor="isAsync">异步函数</Label>
              </div>

              <div>
                <Label>参数列表</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    value={paramInput}
                    onChange={(e) => setParamInput(e.target.value)}
                    placeholder="参数名称"
                    onKeyPress={(e) => e.key === 'Enter' && addParameter()}
                  />
                  <Button onClick={addParameter} variant="outline">
                    添加
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {newFunction.parameters.map((param, index) => (
                    <div key={index} className="flex items-center gap-1 bg-secondary px-2 py-1 rounded">
                      <span className="text-sm">{param}</span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeParameter(index)}
                        className="h-4 w-4 p-0"
                      >
                        ×
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              <Button onClick={addFunction} className="w-full">
                添加函数
              </Button>
            </CardContent>
          </Card>

          {/* 函数列表 */}
          {config.functions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">函数列表</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {config.functions.map((func, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{func.name}</span>
                          {func.isAsync && <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">async</span>}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {func.description}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          参数: {func.parameters.join(', ') || '无'} → {func.returnType}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFunction(index)}
                      >
                        删除
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 生成按钮 */}
          <div className="flex gap-2">
            <Button onClick={generateAllCode} className="flex-1">
              <Zap className="h-4 w-4 mr-2" />
              生成模块
            </Button>
            <Button onClick={downloadAllFiles} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              下载所有文件
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 代码预览 */}
      {generatedCode && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              生成的代码
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="main">主文件</TabsTrigger>
                <TabsTrigger value="package">package.json</TabsTrigger>
                {config.includeTypes && <TabsTrigger value="types">类型定义</TabsTrigger>}
                {config.includeTests && <TabsTrigger value="tests">测试文件</TabsTrigger>}
                {config.includeDocs && <TabsTrigger value="readme">README</TabsTrigger>}
              </TabsList>

              <TabsContent value="main" className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>index.js</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyCode(generateMainCode())}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    复制
                  </Button>
                </div>
                <Textarea
                  value={generateMainCode()}
                  readOnly
                  className="min-h-[400px] font-mono text-sm"
                />
              </TabsContent>

              <TabsContent value="package" className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>package.json</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyCode(generatePackageJson())}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    复制
                  </Button>
                </div>
                <Textarea
                  value={generatePackageJson()}
                  readOnly
                  className="min-h-[400px] font-mono text-sm"
                />
              </TabsContent>

              {config.includeTypes && (
                <TabsContent value="types" className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>index.d.ts</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyCode(generateTypeDefinitions())}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      复制
                    </Button>
                  </div>
                  <Textarea
                    value={generateTypeDefinitions()}
                    readOnly
                    className="min-h-[400px] font-mono text-sm"
                  />
                </TabsContent>
              )}

              {config.includeTests && (
                <TabsContent value="tests" className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>test/index.test.js</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyCode(generateTestFile())}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      复制
                    </Button>
                  </div>
                  <Textarea
                    value={generateTestFile()}
                    readOnly
                    className="min-h-[400px] font-mono text-sm"
                  />
                </TabsContent>
              )}

              {config.includeDocs && (
                <TabsContent value="readme" className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>README.md</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyCode(generateReadme())}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      复制
                    </Button>
                  </div>
                  <Textarea
                    value={generateReadme()}
                    readOnly
                    className="min-h-[400px] font-mono text-sm"
                  />
                </TabsContent>
              )}
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* 使用提示 */}
      <Card className="bg-muted/50">
        <CardHeader>
          <CardTitle className="text-lg">使用提示</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
            <li>支持生成 CommonJS、ES Module 和 UMD 三种格式的模块</li>
            <li>可选择包含 TypeScript 定义文件、测试文件和 README 文档</li>
            <li>支持异步函数和多参数函数定义</li>
            <li>生成的代码包含完整的 JSDoc 注释</li>
            <li>可一键下载所有生成的文件</li>
            <li>适用于快速创建 npm 包的基础结构</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
