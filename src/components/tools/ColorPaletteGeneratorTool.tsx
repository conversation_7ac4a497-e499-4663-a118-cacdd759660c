'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Copy, Download, Palette, RefreshCw, Lock, Unlock, Check } from 'lucide-react';

interface Color {
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
  name?: string;
  locked?: boolean;
}

const ColorPaletteGeneratorTool: React.FC = () => {
  const [baseColor, setBaseColor] = useState('#3B82F6');
  const [schemeType, setSchemeType] = useState('complementary');
  const [colorCount, setColorCount] = useState([5]);
  const [includeShades, setIncludeShades] = useState(false);
  const [palette, setPalette] = useState<Color[]>([]);
  const [exportFormat, setExportFormat] = useState('hex');
  const [notification, setNotification] = useState<string | null>(null);

  // Simple notification system
  const showNotification = useCallback((message: string) => {
    setNotification(message);
    setTimeout(() => setNotification(null), 2000);
  }, []);

  // Color conversion utilities
  const hexToRgb = useCallback((hex: string): { r: number; g: number; b: number } => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 };
  }, []);

  const rgbToHsl = useCallback(({ r, g, b }: { r: number; g: number; b: number }): { h: number; s: number; l: number } => {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r:
          h = (g - b) / d + (g < b ? 6 : 0);
          break;
        case g:
          h = (b - r) / d + 2;
          break;
        case b:
          h = (r - g) / d + 4;
          break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100 };
  }, []);

  const hslToRgb = useCallback(({ h, s, l }: { h: number; s: number; l: number }): { r: number; g: number; b: number } => {
    h /= 360;
    s /= 100;
    l /= 100;

    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    let r, g, b;

    if (s === 0) {
      r = g = b = l;
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  }, []);

  const rgbToHex = useCallback(({ r, g, b }: { r: number; g: number; b: number }): string => {
    return `#${[r, g, b].map(x => {
      const hex = x.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    }).join('')}`;
  }, []);

  const createColor = useCallback((hex: string): Color => {
    const rgb = hexToRgb(hex);
    const hsl = rgbToHsl(rgb);
    return { hex, rgb, hsl };
  }, [hexToRgb, rgbToHsl]);

  // Color scheme generators
  const generateComplementary = useCallback((baseHsl: { h: number; s: number; l: number }, count: number): Color[] => {
    const colors: Color[] = [];
    const complementaryHue = (baseHsl.h + 180) % 360;
    
    for (let i = 0; i < count; i++) {
      let h, s, l;
      if (i === 0) {
        h = baseHsl.h;
        s = baseHsl.s;
        l = baseHsl.l;
      } else if (i === 1) {
        h = complementaryHue;
        s = baseHsl.s;
        l = baseHsl.l;
      } else {
        const variation = (i - 2) * 30;
        h = i % 2 === 0 ? (baseHsl.h + variation) % 360 : (complementaryHue + variation) % 360;
        s = Math.max(20, baseHsl.s - (i - 2) * 10);
        l = Math.max(20, Math.min(80, baseHsl.l + (Math.random() - 0.5) * 20));
      }
      
      const rgb = hslToRgb({ h, s, l });
      colors.push(createColor(rgbToHex(rgb)));
    }
    
    return colors;
  }, [hslToRgb, rgbToHex, createColor]);

  const generateTriadic = useCallback((baseHsl: { h: number; s: number; l: number }, count: number): Color[] => {
    const colors: Color[] = [];
    const hues = [baseHsl.h, (baseHsl.h + 120) % 360, (baseHsl.h + 240) % 360];
    
    for (let i = 0; i < count; i++) {
      const baseHueIndex = i % 3;
      const h = hues[baseHueIndex];
      const variation = Math.floor(i / 3) * 15;
      const adjustedHue = (h + variation) % 360;
      
      const s = Math.max(30, baseHsl.s - Math.floor(i / 3) * 10);
      const l = Math.max(20, Math.min(80, baseHsl.l + (i % 2 === 0 ? 10 : -10)));
      
      const rgb = hslToRgb({ h: adjustedHue, s, l });
      colors.push(createColor(rgbToHex(rgb)));
    }
    
    return colors;
  }, [hslToRgb, rgbToHex, createColor]);

  const generateAnalogous = useCallback((baseHsl: { h: number; s: number; l: number }, count: number): Color[] => {
    const colors: Color[] = [];
    const step = 30;
    
    for (let i = 0; i < count; i++) {
      const hueOffset = (i - Math.floor(count / 2)) * step;
      const h = (baseHsl.h + hueOffset + 360) % 360;
      const s = Math.max(30, baseHsl.s - Math.abs(i - Math.floor(count / 2)) * 5);
      const l = Math.max(20, Math.min(80, baseHsl.l + (Math.random() - 0.5) * 15));
      
      const rgb = hslToRgb({ h, s, l });
      colors.push(createColor(rgbToHex(rgb)));
    }
    
    return colors;
  }, [hslToRgb, rgbToHex, createColor]);

  const generateMonochromatic = useCallback((baseHsl: { h: number; s: number; l: number }, count: number): Color[] => {
    const colors: Color[] = [];
    const lightStep = 80 / (count - 1);
    
    for (let i = 0; i < count; i++) {
      const l = 10 + i * lightStep;
      const s = baseHsl.s;
      const h = baseHsl.h;
      
      const rgb = hslToRgb({ h, s, l });
      colors.push(createColor(rgbToHex(rgb)));
    }
    
    return colors;
  }, [hslToRgb, rgbToHex, createColor]);

  const generateTetradic = useCallback((baseHsl: { h: number; s: number; l: number }, count: number): Color[] => {
    const colors: Color[] = [];
    const hues = [
      baseHsl.h,
      (baseHsl.h + 90) % 360,
      (baseHsl.h + 180) % 360,
      (baseHsl.h + 270) % 360
    ];
    
    for (let i = 0; i < count; i++) {
      const baseHueIndex = i % 4;
      const h = hues[baseHueIndex];
      const variation = Math.floor(i / 4) * 10;
      const adjustedHue = (h + variation) % 360;
      
      const s = Math.max(25, baseHsl.s - Math.floor(i / 4) * 8);
      const l = Math.max(20, Math.min(80, baseHsl.l + (Math.random() - 0.5) * 20));
      
      const rgb = hslToRgb({ h: adjustedHue, s, l });
      colors.push(createColor(rgbToHex(rgb)));
    }
    
    return colors;
  }, [hslToRgb, rgbToHex, createColor]);

  const generatePalette = useCallback(() => {
    const baseHsl = rgbToHsl(hexToRgb(baseColor));
    let newColors: Color[] = [];

    switch (schemeType) {
      case 'complementary':
        newColors = generateComplementary(baseHsl, colorCount[0]);
        break;
      case 'triadic':
        newColors = generateTriadic(baseHsl, colorCount[0]);
        break;
      case 'analogous':
        newColors = generateAnalogous(baseHsl, colorCount[0]);
        break;
      case 'monochromatic':
        newColors = generateMonochromatic(baseHsl, colorCount[0]);
        break;
      case 'tetradic':
        newColors = generateTetradic(baseHsl, colorCount[0]);
        break;
    }

    if (includeShades) {
      const extendedColors: Color[] = [];
      newColors.forEach(color => {
        extendedColors.push(color);
        const lighterHsl = { ...color.hsl, l: Math.min(90, color.hsl.l + 20) };
        const darkerHsl = { ...color.hsl, l: Math.max(10, color.hsl.l - 20) };
        
        const lighterRgb = hslToRgb(lighterHsl);
        const darkerRgb = hslToRgb(darkerHsl);
        
        extendedColors.push(createColor(rgbToHex(lighterRgb)));
        extendedColors.push(createColor(rgbToHex(darkerRgb)));
      });
      newColors = extendedColors;
    }

    setPalette(newColors);
    showNotification('Palette generated successfully!');
  }, [
    baseColor, schemeType, colorCount, includeShades,
    rgbToHsl, hexToRgb, generateComplementary, generateTriadic,
    generateAnalogous, generateMonochromatic, generateTetradic,
    hslToRgb, rgbToHex, createColor, showNotification
  ]);

  const copyColor = useCallback((color: Color) => {
    let copyText = '';
    switch (exportFormat) {
      case 'hex':
        copyText = color.hex;
        break;
      case 'rgb':
        copyText = `rgb(${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b})`;
        break;
      case 'hsl':
        copyText = `hsl(${Math.round(color.hsl.h)}, ${Math.round(color.hsl.s)}%, ${Math.round(color.hsl.l)}%)`;
        break;
      case 'css':
        copyText = `--color: ${color.hex};`;
        break;
    }
    navigator.clipboard.writeText(copyText);
    showNotification('Color copied to clipboard!');
  }, [exportFormat, showNotification]);

  const copyAllColors = useCallback(() => {
    let copyText = '';
    switch (exportFormat) {
      case 'hex':
        copyText = palette.map(c => c.hex).join('\n');
        break;
      case 'rgb':
        copyText = palette.map(c => `rgb(${c.rgb.r}, ${c.rgb.g}, ${c.rgb.b})`).join('\n');
        break;
      case 'hsl':
        copyText = palette.map(c => `hsl(${Math.round(c.hsl.h)}, ${Math.round(c.hsl.s)}%, ${Math.round(c.hsl.l)}%)`).join('\n');
        break;
      case 'css':
        copyText = palette.map((c, i) => `--color-${i + 1}: ${c.hex};`).join('\n');
        break;
      case 'json':
        copyText = JSON.stringify(palette.map(c => ({
          hex: c.hex,
          rgb: c.rgb,
          hsl: c.hsl
        })), null, 2);
        break;
      case 'scss':
        copyText = palette.map((c, i) => `$color-${i + 1}: ${c.hex};`).join('\n');
        break;
    }
    navigator.clipboard.writeText(copyText);
    showNotification('Palette copied to clipboard!');
  }, [palette, exportFormat, showNotification]);

  const downloadPalette = useCallback(() => {
    let content = '';
    let filename = '';
    let mimeType = 'text/plain';

    switch (exportFormat) {
      case 'json':
        content = JSON.stringify(palette.map(c => ({
          hex: c.hex,
          rgb: c.rgb,
          hsl: c.hsl
        })), null, 2);
        filename = 'color-palette.json';
        mimeType = 'application/json';
        break;
      case 'css':
        content = `:root {\n${palette.map((c, i) => `  --color-${i + 1}: ${c.hex};`).join('\n')}\n}`;
        filename = 'color-palette.css';
        mimeType = 'text/css';
        break;
      case 'scss':
        content = palette.map((c, i) => `$color-${i + 1}: ${c.hex};`).join('\n');
        filename = 'color-palette.scss';
        mimeType = 'text/plain';
        break;
      default:
        content = palette.map(c => c.hex).join('\n');
        filename = 'color-palette.txt';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showNotification('Palette downloaded!');
  }, [palette, exportFormat, showNotification]);

  // Helper function for contrast calculation
  const getContrastRatio = useCallback((rgb1: { r: number; g: number; b: number }, rgb2: { r: number; g: number; b: number }): number => {
    const getLuminance = (rgb: { r: number; g: number; b: number }) => {
      const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    const l1 = getLuminance(rgb1);
    const l2 = getLuminance(rgb2);
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }, []);

  const schemeTypes = [
    { value: 'complementary', label: 'Complementary', description: 'Colors opposite on the color wheel' },
    { value: 'triadic', label: 'Triadic', description: 'Three evenly spaced colors' },
    { value: 'analogous', label: 'Analogous', description: 'Adjacent colors on the wheel' },
    { value: 'monochromatic', label: 'Monochromatic', description: 'Same hue with different lightness' },
    { value: 'tetradic', label: 'Tetradic', description: 'Four colors forming a square' }
  ];

  const exportFormats = [
    { value: 'hex', label: 'HEX' },
    { value: 'rgb', label: 'RGB' },
    { value: 'hsl', label: 'HSL' },
    { value: 'css', label: 'CSS Variables' },
    { value: 'scss', label: 'SCSS Variables' },
    { value: 'json', label: 'JSON' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100 p-4">
      {/* Notification */}
      {notification && (
        <div className="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg flex items-center gap-2 z-50">
          <Check className="w-4 h-4" />
          {notification}
        </div>
      )}
      
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl shadow-lg">
              <Palette className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
              Color Palette Generator
            </h1>
          </div>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Create stunning color palettes with intelligent color harmony algorithms. 
            Generate, customize, and export professional color schemes for your projects.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="w-5 h-5" />
                Generator Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="base-color">Base Color</Label>
                <div className="flex gap-2">
                  <Input
                    id="base-color"
                    type="color"
                    value={baseColor}
                    onChange={(e) => setBaseColor(e.target.value)}
                    className="w-16 h-10 p-1 border-2"
                  />
                  <Input
                    value={baseColor}
                    onChange={(e) => setBaseColor(e.target.value)}
                    placeholder="#3B82F6"
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="scheme-type">Color Scheme</Label>
                <Select value={schemeType} onValueChange={setSchemeType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {schemeTypes.map(scheme => (
                      <SelectItem key={scheme.value} value={scheme.value}>
                        <div>
                          <div className="font-medium">{scheme.label}</div>
                          <div className="text-sm text-gray-500">{scheme.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Color Count: {colorCount[0]}</Label>
                <Slider
                  value={colorCount}
                  onValueChange={setColorCount}
                  min={3}
                  max={12}
                  step={1}
                  className="w-full"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="include-shades"
                  checked={includeShades}
                  onCheckedChange={setIncludeShades}
                />
                <Label htmlFor="include-shades">Include shades & tints</Label>
              </div>

              <Button onClick={generatePalette} className="w-full" size="lg">
                <RefreshCw className="w-4 h-4 mr-2" />
                Generate Palette
              </Button>
            </CardContent>
          </Card>

          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Generated Palette</CardTitle>
                <div className="flex gap-2">
                  <Select value={exportFormat} onValueChange={setExportFormat}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {exportFormats.map(format => (
                        <SelectItem key={format.value} value={format.value}>
                          {format.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button variant="outline" onClick={copyAllColors}>
                    <Copy className="w-4 h-4 mr-2" />
                    Copy All
                  </Button>
                  <Button variant="outline" onClick={downloadPalette}>
                    <Download className="w-4 h-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {palette.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                    {palette.map((color, index) => (
                      <div
                        key={index}
                        className="group relative border-2 border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                      >
                        <div
                          className="w-full h-24 cursor-pointer relative"
                          style={{ backgroundColor: color.hex }}
                          onClick={() => copyColor(color)}
                        >
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-opacity flex items-center justify-center">
                            <Copy className="w-5 h-5 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        </div>
                        <div className="p-2 bg-white">
                          <div className="text-xs font-mono text-center text-gray-800">
                            {color.hex.toUpperCase()}
                          </div>
                          <div className="text-xs text-gray-500 text-center mt-1">
                            RGB({color.rgb.r}, {color.rgb.g}, {color.rgb.b})
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <Tabs defaultValue="harmony" className="mt-6">
                    <TabsList>
                      <TabsTrigger value="harmony">Color Harmony</TabsTrigger>
                      <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
                      <TabsTrigger value="export">Export Options</TabsTrigger>
                    </TabsList>

                    <TabsContent value="harmony" className="mt-4">
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          {schemeTypes.map(scheme => (
                            <div
                              key={scheme.value}
                              className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                                schemeType === scheme.value
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                              onClick={() => setSchemeType(scheme.value)}
                            >
                              <div className="font-medium">{scheme.label}</div>
                              <div className="text-sm text-gray-500">{scheme.description}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="accessibility" className="mt-4">
                      <div className="space-y-4">
                        <h3 className="font-medium">Contrast Ratios</h3>
                        <div className="grid grid-cols-1 gap-2">
                          {palette.slice(0, 5).map((color, index) => {
                            const contrastWhite = getContrastRatio(color.rgb, { r: 255, g: 255, b: 255 });
                            const contrastBlack = getContrastRatio(color.rgb, { r: 0, g: 0, b: 0 });
                            
                            return (
                              <div key={index} className="flex items-center justify-between p-2 border rounded">
                                <div className="flex items-center gap-3">
                                  <div
                                    className="w-6 h-6 rounded border"
                                    style={{ backgroundColor: color.hex }}
                                  />
                                  <span className="font-mono text-sm">{color.hex}</span>
                                </div>
                                <div className="flex gap-4 text-sm">
                                  <div className="text-gray-600">
                                    vs White: <Badge variant={contrastWhite >= 4.5 ? "default" : "secondary"}>
                                      {contrastWhite.toFixed(2)}
                                    </Badge>
                                  </div>
                                  <div className="text-gray-600">
                                    vs Black: <Badge variant={contrastBlack >= 4.5 ? "default" : "secondary"}>
                                      {contrastBlack.toFixed(2)}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="export" className="mt-4">
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                          {exportFormats.map(format => (
                            <Button
                              key={format.value}
                              variant={exportFormat === format.value ? "default" : "outline"}
                              onClick={() => setExportFormat(format.value)}
                              className="w-full"
                            >
                              {format.label}
                            </Button>
                          ))}
                        </div>
                        
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <Label className="text-sm font-medium text-gray-600">Preview:</Label>
                          <pre className="mt-2 text-xs bg-white p-3 rounded border overflow-x-auto">
                            {palette.length > 0 && (() => {
                              switch (exportFormat) {
                                case 'hex':
                                  return palette.slice(0, 3).map(c => c.hex).join('\n') + (palette.length > 3 ? '\n...' : '');
                                case 'rgb':
                                  return palette.slice(0, 3).map(c => `rgb(${c.rgb.r}, ${c.rgb.g}, ${c.rgb.b})`).join('\n') + (palette.length > 3 ? '\n...' : '');
                                case 'css':
                                  return palette.slice(0, 3).map((c, i) => `--color-${i + 1}: ${c.hex};`).join('\n') + (palette.length > 3 ? '\n...' : '');
                                default:
                                  return 'Select a color format to see preview';
                              }
                            })()}
                          </pre>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <Palette className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Click &quot;Generate Palette&quot; to create your color scheme</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ColorPaletteGeneratorTool;