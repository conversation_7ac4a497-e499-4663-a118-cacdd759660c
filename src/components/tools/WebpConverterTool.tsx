'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Upload, Download, Image as ImageIcon, FileImage, Trash2, Eye, Settings, Info } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface ConversionFile {
  id: string
  file: File
  originalFormat: string
  preview: string
  size: number
  status: 'pending' | 'converting' | 'completed' | 'error'
  convertedBlob?: Blob
  convertedSize?: number
  progress: number
  error?: string
}

interface ConversionSettings {
  quality: number
  lossless: boolean
  method: number
  targetSize?: number
  preserveMetadata: boolean
}

const SUPPORTED_FORMATS = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp']

export default function WebpConverterTool() {
  const [files, setFiles] = useState<ConversionFile[]>([])
  const [settings, setSettings] = useState<ConversionSettings>({
    quality: 80,
    lossless: false,
    method: 4,
    preserveMetadata: false
  })
  const [isConverting, setIsConverting] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const { toast } = useToast()

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    const droppedFiles = Array.from(e.dataTransfer.files)
    handleFiles(droppedFiles)
  }, [])

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files)
      handleFiles(selectedFiles)
    }
  }

  const handleFiles = (fileList: File[]) => {
    const validFiles = fileList.filter(file => {
      const extension = file.name.split('.').pop()?.toLowerCase()
      return extension && SUPPORTED_FORMATS.includes(extension)
    })

    if (validFiles.length !== fileList.length) {
      toast({
        title: '部分文件不支持',
        description: `支持的格式：${SUPPORTED_FORMATS.join(', ')}`,
        variant: 'destructive'
      })
    }

    const newFiles: ConversionFile[] = validFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      originalFormat: file.name.split('.').pop()?.toLowerCase() || '',
      preview: URL.createObjectURL(file),
      size: file.size,
      status: 'pending',
      progress: 0
    }))

    setFiles(prev => [...prev, ...newFiles])
  }

  const removeFile = (id: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === id)
      if (file?.preview) {
        URL.revokeObjectURL(file.preview)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  const clearAll = () => {
    files.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview)
      }
    })
    setFiles([])
  }

  const convertToWebP = async (file: ConversionFile): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        canvas.width = img.width
        canvas.height = img.height
        
        if (ctx) {
          ctx.drawImage(img, 0, 0)
          
          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve(blob)
              } else {
                reject(new Error('转换失败'))
              }
            },
            'image/webp',
            settings.lossless ? 1 : settings.quality / 100
          )
        } else {
          reject(new Error('Canvas context 创建失败'))
        }
      }

      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = file.preview
    })
  }

  const convertFromWebP = async (file: ConversionFile, targetFormat: string): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        canvas.width = img.width
        canvas.height = img.height
        
        if (ctx) {
          ctx.drawImage(img, 0, 0)
          
          const mimeType = `image/${targetFormat === 'jpg' ? 'jpeg' : targetFormat}`
          const quality = targetFormat === 'png' ? undefined : settings.quality / 100
          
          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve(blob)
              } else {
                reject(new Error('转换失败'))
              }
            },
            mimeType,
            quality
          )
        } else {
          reject(new Error('Canvas context 创建失败'))
        }
      }

      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = file.preview
    })
  }

  const convertSingleFile = async (file: ConversionFile) => {
    setFiles(prev => prev.map(f => 
      f.id === file.id 
        ? { ...f, status: 'converting' as const, progress: 0 }
        : f
    ))

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(f => 
          f.id === file.id && f.progress < 90
            ? { ...f, progress: f.progress + 10 }
            : f
        ))
      }, 100)

      let convertedBlob: Blob

      if (file.originalFormat === 'webp') {
        // WebP 转其他格式
        convertedBlob = await convertFromWebP(file, 'png')
      } else {
        // 其他格式转 WebP
        convertedBlob = await convertToWebP(file)
      }

      clearInterval(progressInterval)

      setFiles(prev => prev.map(f => 
        f.id === file.id 
          ? { 
              ...f, 
              status: 'completed' as const, 
              progress: 100,
              convertedBlob,
              convertedSize: convertedBlob.size
            }
          : f
      ))

    } catch (error) {
      setFiles(prev => prev.map(f => 
        f.id === file.id 
          ? { 
              ...f, 
              status: 'error' as const, 
              progress: 0,
              error: error instanceof Error ? error.message : '转换失败'
            }
          : f
      ))
    }
  }

  const convertAll = async () => {
    setIsConverting(true)
    const pendingFiles = files.filter(f => f.status === 'pending')
    
    for (const file of pendingFiles) {
      await convertSingleFile(file)
    }
    
    setIsConverting(false)
    toast({
      title: '转换完成',
      description: `成功转换 ${pendingFiles.length} 个文件`
    })
  }

  const downloadFile = (file: ConversionFile) => {
    if (!file.convertedBlob) return

    const url = URL.createObjectURL(file.convertedBlob)
    const a = document.createElement('a')
    a.href = url
    
    const originalName = file.file.name.split('.').slice(0, -1).join('.')
    const newExtension = file.originalFormat === 'webp' ? 'png' : 'webp'
    a.download = `${originalName}.${newExtension}`
    
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const downloadAll = () => {
    const completedFiles = files.filter(f => f.status === 'completed' && f.convertedBlob)
    completedFiles.forEach(file => downloadFile(file))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getCompressionRatio = (original: number, converted: number) => {
    const ratio = ((original - converted) / original) * 100
    return ratio > 0 ? `压缩 ${ratio.toFixed(1)}%` : `增大 ${Math.abs(ratio).toFixed(1)}%`
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">WebP 转换器</h1>
        <p className="text-muted-foreground">
          高效的 WebP 格式转换工具，支持批量处理和质量调节
        </p>
      </div>

      <Tabs defaultValue="convert" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="convert">文件转换</TabsTrigger>
          <TabsTrigger value="settings">转换设置</TabsTrigger>
        </TabsList>

        <TabsContent value="convert" className="space-y-6">
          {/* 文件上传区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                上传文件
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive 
                    ? 'border-primary bg-primary/5' 
                    : 'border-muted-foreground/25 hover:border-primary/50'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <ImageIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">
                  拖拽文件到此处或点击选择
                </p>
                <p className="text-sm text-muted-foreground mb-4">
                  支持格式：{SUPPORTED_FORMATS.join(', ')}
                </p>
                <Input
                  type="file"
                  multiple
                  accept={SUPPORTED_FORMATS.map(f => `.${f}`).join(',')}
                  onChange={handleFileInput}
                  className="hidden"
                  id="file-upload"
                />
                <Label htmlFor="file-upload">
                  <Button variant="outline" className="cursor-pointer">
                    选择文件
                  </Button>
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* 文件列表 */}
          {files.length > 0 && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <FileImage className="h-5 w-5" />
                    文件列表 ({files.length})
                  </CardTitle>
                  <div className="flex gap-2">
                    <Button
                      onClick={convertAll}
                      disabled={isConverting || files.every(f => f.status !== 'pending')}
                      className="flex items-center gap-2"
                    >
                      <Settings className="h-4 w-4" />
                      {isConverting ? '转换中...' : '全部转换'}
                    </Button>
                    <Button
                      onClick={downloadAll}
                      disabled={!files.some(f => f.status === 'completed')}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      全部下载
                    </Button>
                    <Button
                      onClick={clearAll}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Trash2 className="h-4 w-4" />
                      清空
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {files.map((file) => (
                    <div key={file.id} className="border rounded-lg p-4">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                          <img
                            src={file.preview}
                            alt={file.file.name}
                            className="w-16 h-16 object-cover rounded border"
                          />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium truncate">{file.file.name}</h4>
                            <div className="flex items-center gap-2">
                              <Badge variant={
                                file.status === 'completed' ? 'default' :
                                file.status === 'error' ? 'destructive' :
                                file.status === 'converting' ? 'secondary' : 'outline'
                              }>
                                {file.status === 'pending' && '待转换'}
                                {file.status === 'converting' && '转换中'}
                                {file.status === 'completed' && '已完成'}
                                {file.status === 'error' && '失败'}
                              </Badge>
                              <Button
                                onClick={() => removeFile(file.id)}
                                variant="ghost"
                                size="sm"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          
                          <div className="text-sm text-muted-foreground mb-2">
                            <span>原始大小: {formatFileSize(file.size)}</span>
                            {file.convertedSize && (
                              <>
                                <span className="mx-2">→</span>
                                <span>转换后: {formatFileSize(file.convertedSize)}</span>
                                <span className="mx-2">•</span>
                                <span className={
                                  file.convertedSize < file.size ? 'text-green-600' : 'text-orange-600'
                                }>
                                  {getCompressionRatio(file.size, file.convertedSize)}
                                </span>
                              </>
                            )}
                          </div>

                          {file.status === 'converting' && (
                            <Progress value={file.progress} className="mb-2" />
                          )}

                          {file.status === 'error' && (
                            <p className="text-sm text-destructive mb-2">{file.error}</p>
                          )}

                          <div className="flex gap-2">
                            {file.status === 'pending' && (
                              <Button
                                onClick={() => convertSingleFile(file)}
                                size="sm"
                                variant="outline"
                              >
                                转换
                              </Button>
                            )}
                            {file.status === 'completed' && (
                              <Button
                                onClick={() => downloadFile(file)}
                                size="sm"
                                className="flex items-center gap-1"
                              >
                                <Download className="h-3 w-3" />
                                下载
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                转换设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>图片质量: {settings.quality}%</Label>
                  <Slider
                    value={[settings.quality]}
                    onValueChange={(value) => setSettings(prev => ({ ...prev, quality: value[0] }))}
                    max={100}
                    min={1}
                    step={1}
                    disabled={settings.lossless}
                    className="w-full"
                  />
                  <p className="text-sm text-muted-foreground">
                    质量越高，文件越大，图片越清晰
                  </p>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>无损压缩</Label>
                    <p className="text-sm text-muted-foreground">
                      启用无损压缩，保持原始图片质量
                    </p>
                  </div>
                  <Switch
                    checked={settings.lossless}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, lossless: checked }))}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>压缩方法: {settings.method}</Label>
                  <Slider
                    value={[settings.method]}
                    onValueChange={(value) => setSettings(prev => ({ ...prev, method: value[0] }))}
                    max={6}
                    min={0}
                    step={1}
                    className="w-full"
                  />
                  <p className="text-sm text-muted-foreground">
                    0=最快，6=最小文件（推荐4）
                  </p>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>保留元数据</Label>
                    <p className="text-sm text-muted-foreground">
                      保留 EXIF 信息、颜色配置文件等
                    </p>
                  </div>
                  <Switch
                    checked={settings.preserveMetadata}
                    onCheckedChange={(checked) => setSettings(prev => ({ ...prev, preserveMetadata: checked }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                WebP 格式说明
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">优势</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• 比 JPEG 小 25-35%</li>
                    <li>• 比 PNG 小 26%</li>
                    <li>• 支持透明度</li>
                    <li>• 支持动画</li>
                    <li>• 无损和有损压缩</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">兼容性</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Chrome 23+</li>
                    <li>• Firefox 65+</li>
                    <li>• Safari 14+</li>
                    <li>• Edge 18+</li>
                    <li>• Android 4.0+</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
