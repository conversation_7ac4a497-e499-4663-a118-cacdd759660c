'use client';

import React, { useState, useMemo, useRef, useC<PERSON>back } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { 
  Copy, 
  RefreshCw, 
  Trash2, 
  Download, 
  Upload, 
  MessageSquare, 
  FileText, 
  Tag, 
  Highlighter, 
  Type, 
  PlusCircle, 
  Edit3, 
  <PERSON>,
  <PERSON>O<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>h,
  Clock
} from 'lucide-react';

interface Annotation {
  id: string;
  start: number;
  end: number;
  text: string;
  type: 'comment' | 'highlight' | 'tag' | 'note' | 'question' | 'warning' | 'correction';
  content: string;
  color: string;
  author?: string;
  timestamp: string;
  isVisible: boolean;
}

interface AnnotationType {
  id: string;
  name: string;
  color: string;
  bgColor: string;
  icon: React.ReactNode;
  description: string;
}

const ANNOTATION_TYPES: AnnotationType[] = [
  {
    id: 'comment',
    name: '评论',
    color: '#3B82F6',
    bgColor: '#EFF6FF',
    icon: <MessageSquare className="h-4 w-4" />,
    description: '一般性评论和说明'
  },
  {
    id: 'highlight',
    name: '高亮',
    color: '#EAB308',
    bgColor: '#FEFCE8',
    icon: <Highlighter className="h-4 w-4" />,
    description: '重要内容高亮标记'
  },
  {
    id: 'tag',
    name: '标签',
    color: '#10B981',
    bgColor: '#ECFDF5',
    icon: <Tag className="h-4 w-4" />,
    description: '分类标签'
  },
  {
    id: 'note',
    name: '笔记',
    color: '#8B5CF6',
    bgColor: '#F3E8FF',
    icon: <FileText className="h-4 w-4" />,
    description: '详细笔记和说明'
  },
  {
    id: 'question',
    name: '疑问',
    color: '#F59E0B',
    bgColor: '#FEF3C7',
    icon: <Hash className="h-4 w-4" />,
    description: '疑问和待解决问题'
  },
  {
    id: 'warning',
    name: '警告',
    color: '#EF4444',
    bgColor: '#FEF2F2',
    icon: <Highlighter className="h-4 w-4" />,
    description: '警告和注意事项'
  },
  {
    id: 'correction',
    name: '修正',
    color: '#DC2626',
    bgColor: '#FEF2F2',
    icon: <Edit3 className="h-4 w-4" />,
    description: '错误修正和建议'
  }
];

const PREDEFINED_COLORS = [
  '#EF4444', '#F97316', '#EAB308', '#10B981', 
  '#06B6D4', '#3B82F6', '#8B5CF6', '#EC4899',
  '#64748B', '#374151', '#7C2D12', '#166534'
];

export default function TextAnnotatorTool() {
  const [input, setInput] = useState('');
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [selectedText, setSelectedText] = useState<{ start: number; end: number; text: string } | null>(null);
  const [newAnnotation, setNewAnnotation] = useState({
    type: 'comment' as string,
    content: '',
    color: '#3B82F6',
    author: '',
  });
  const [showAnnotations, setShowAnnotations] = useState(true);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterAuthor, setFilterAuthor] = useState<string>('all');
  const [isAnnotating, setIsAnnotating] = useState(false);
  const [error, setError] = useState('');
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const handleTextSelection = useCallback(() => {
    if (!textAreaRef.current) return;
    
    const textarea = textAreaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    
    if (start !== end) {
      const selectedText = input.substring(start, end);
      setSelectedText({ start, end, text: selectedText });
      setIsAnnotating(true);
    } else {
      setSelectedText(null);
    }
  }, [input]);

  const addAnnotation = () => {
    if (!selectedText || !newAnnotation.content.trim()) {
      setError('请选择文本并添加注释内容');
      return;
    }

    const annotation: Annotation = {
      id: Date.now().toString(),
      start: selectedText.start,
      end: selectedText.end,
      text: selectedText.text,
      type: newAnnotation.type as Annotation['type'],
      content: newAnnotation.content,
      color: newAnnotation.color,
      author: newAnnotation.author || '匿名用户',
      timestamp: new Date().toLocaleString(),
      isVisible: true,
    };

    setAnnotations(prev => [...prev, annotation].sort((a, b) => a.start - b.start));
    setNewAnnotation({ ...newAnnotation, content: '' });
    setSelectedText(null);
    setIsAnnotating(false);
    setError('');
  };

  const removeAnnotation = (id: string) => {
    setAnnotations(prev => prev.filter(ann => ann.id !== id));
  };

  const toggleAnnotationVisibility = (id: string) => {
    setAnnotations(prev => prev.map(ann => 
      ann.id === id ? { ...ann, isVisible: !ann.isVisible } : ann
    ));
  };

  const updateAnnotation = (id: string, updates: Partial<Annotation>) => {
    setAnnotations(prev => prev.map(ann => 
      ann.id === id ? { ...ann, ...updates } : ann
    ));
  };

  const filteredAnnotations = useMemo(() => {
    return annotations.filter(annotation => {
      const typeMatch = filterType === 'all' || annotation.type === filterType;
      const authorMatch = filterAuthor === 'all' || annotation.author === filterAuthor;
      return typeMatch && authorMatch;
    });
  }, [annotations, filterType, filterAuthor]);

  const annotatedText = useMemo(() => {
    if (!input || !showAnnotations) return input;

    const visibleAnnotations = filteredAnnotations.filter(ann => ann.isVisible);
    if (visibleAnnotations.length === 0) return input;

    let result = input;
    const sortedAnnotations = [...visibleAnnotations].sort((a, b) => b.start - a.start);

    sortedAnnotations.forEach(annotation => {
      const annotationType = ANNOTATION_TYPES.find(type => type.id === annotation.type);
      const before = result.substring(0, annotation.start);
      const after = result.substring(annotation.end);
      const annotatedText = result.substring(annotation.start, annotation.end);
      
      const style = `background-color: ${annotationType?.bgColor}; border-left: 3px solid ${annotation.color}; padding: 1px 2px; position: relative;`;
      const tooltip = `title="${annotation.type}: ${annotation.content} - ${annotation.author}"`;
      
      result = before + `<span class="annotation" style="${style}" ${tooltip} data-annotation-id="${annotation.id}">${annotatedText}</span>` + after;
    });

    return result;
  }, [input, filteredAnnotations, showAnnotations]);

  const statistics = useMemo(() => {
    const typeCount = annotations.reduce((acc, ann) => {
      acc[ann.type] = (acc[ann.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const authors = [...new Set(annotations.map(ann => ann.author))];
    const avgAnnotationLength = annotations.length > 0 
      ? Math.round(annotations.reduce((sum, ann) => sum + ann.content.length, 0) / annotations.length)
      : 0;

    return {
      total: annotations.length,
      visible: filteredAnnotations.filter(ann => ann.isVisible).length,
      typeCount,
      authors,
      avgAnnotationLength,
    };
  }, [annotations, filteredAnnotations]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(input);
    } catch (err) {
      setError('复制失败');
    }
  };

  const handleClear = () => {
    setInput('');
    setAnnotations([]);
    setSelectedText(null);
    setError('');
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 1024 * 1024) {
      setError('文件大小不能超过1MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setInput(content);
    };
    reader.readAsText(file);
  };

  const exportAnnotations = () => {
    const exportData = {
      text: input,
      annotations: annotations.map(ann => ({
        start: ann.start,
        end: ann.end,
        selectedText: ann.text,
        type: ann.type,
        content: ann.content,
        color: ann.color,
        author: ann.author,
        timestamp: ann.timestamp,
      })),
      statistics,
      exportTime: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'text-annotations.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportAnnotatedHTML = () => {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>标注文档</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
        .annotation { border-left: 3px solid; padding: 1px 2px; margin: 0 1px; }
        .annotation-list { margin-top: 30px; border-top: 1px solid #ccc; padding-top: 20px; }
        .annotation-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .annotation-header { font-weight: bold; margin-bottom: 5px; }
        .annotation-meta { font-size: 0.8em; color: #666; }
    </style>
</head>
<body>
    <h1>标注文档</h1>
    <div class="content">${annotatedText}</div>
    
    <div class="annotation-list">
        <h2>注释列表</h2>
        ${annotations.map(ann => `
            <div class="annotation-item">
                <div class="annotation-header">${ann.type}: "${ann.text}"</div>
                <div>${ann.content}</div>
                <div class="annotation-meta">作者: ${ann.author} | 时间: ${ann.timestamp}</div>
            </div>
        `).join('')}
    </div>
</body>
</html>`;

    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'annotated-document.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const examples = [
    {
      name: '学术论文',
      text: `人工智能技术的发展正在深刻改变我们的生活方式。机器学习作为人工智能的核心技术之一，通过大数据分析和模式识别，使计算机能够从经验中学习并做出预测。

深度学习是机器学习的一个分支，它模拟人脑神经网络的工作原理，通过多层神经网络来处理复杂的数据结构。这种技术在图像识别、自然语言处理、语音识别等领域取得了突破性进展。

然而，人工智能技术的发展也带来了一些挑战，包括数据隐私、算法偏见、就业影响等问题。我们需要在推进技术发展的同时，积极应对这些挑战，确保技术为人类社会带来更多福祉。`,
      description: '适合添加学术注释和分析'
    },
    {
      name: '商业计划',
      text: `我们的创业项目旨在开发一款智能健康管理应用。该应用将结合物联网设备和人工智能技术，为用户提供个性化的健康监测和建议服务。

目标市场主要是25-45岁的城市白领群体，他们工作繁忙但注重健康管理。我们的核心竞争优势在于独特的AI算法和用户体验设计。

财务预测显示，第一年预计用户增长至10万，第二年达到50万用户。预计在第三年实现盈利，年收入超过1000万元。主要收入来源包括付费订阅和企业合作。`,
      description: '适合商业评审和改进建议'
    }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>文本标注器</CardTitle>
          <CardDescription>
            强大的文本标注工具，支持多种注释类型、协作标注和导出功能。
            适用于文档审阅、学术研究、内容分析等场景。
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* 文本输入区域 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">文本内容</CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAnnotations(!showAnnotations)}
                >
                  {showAnnotations ? <Eye className="h-4 w-4 mr-1" /> : <EyeOff className="h-4 w-4 mr-1" />}
                  {showAnnotations ? '隐藏标注' : '显示标注'}
                </Button>
                <Button variant="outline" size="sm" onClick={handleClear}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  清空
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <label>
                    <Upload className="h-4 w-4 mr-1" />
                    上传文件
                    <input
                      type="file"
                      accept=".txt,.md,.doc,.docx"
                      className="hidden"
                      onChange={handleFileUpload}
                    />
                  </label>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <div className="text-sm text-red-500 bg-red-50 p-2 rounded">
                {error}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="textInput">
                选择文本添加标注 {selectedText && `(已选择: "${selectedText.text.substring(0, 20)}...")`}
              </Label>
              <Textarea
                ref={textAreaRef}
                id="textInput"
                placeholder="在这里输入文本，然后选择文字添加标注..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onSelect={handleTextSelection}
                className="min-h-[400px] font-mono text-sm"
              />
            </div>

            {/* 标注预览 */}
            {showAnnotations && input && (
              <div className="space-y-2">
                <Label>标注预览：</Label>
                <div 
                  className="p-3 border rounded bg-gray-50 min-h-[200px] font-mono text-sm whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{ __html: annotatedText }}
                />
              </div>
            )}

            {/* 示例文本 */}
            <div className="space-y-2">
              <Label>示例文本：</Label>
              <div className="space-y-2">
                {examples.map((example, index) => (
                  <div key={index} className="p-2 border rounded">
                    <div className="flex justify-between items-center mb-1">
                      <span className="font-medium text-sm">{example.name}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setInput(example.text)}
                      >
                        使用示例
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {example.description}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 标注控制面板 */}
        <div className="space-y-4">
          {/* 添加标注 */}
          {isAnnotating && selectedText && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">添加标注</CardTitle>
                <CardDescription>
                  为选中的文本 "{selectedText.text.substring(0, 20)}..." 添加标注
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>标注类型</Label>
                  <Select 
                    value={newAnnotation.type} 
                    onValueChange={(value) => setNewAnnotation(prev => ({ ...prev, type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {ANNOTATION_TYPES.map(type => (
                        <SelectItem key={type.id} value={type.id}>
                          <div className="flex items-center gap-2">
                            {type.icon}
                            {type.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>颜色</Label>
                  <div className="flex flex-wrap gap-1">
                    {PREDEFINED_COLORS.map(color => (
                      <button
                        key={color}
                        className={`w-6 h-6 rounded border-2 ${newAnnotation.color === color ? 'border-gray-800' : 'border-gray-300'}`}
                        style={{ backgroundColor: color }}
                        onClick={() => setNewAnnotation(prev => ({ ...prev, color }))}
                      />
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="annotationContent">标注内容</Label>
                  <Textarea
                    id="annotationContent"
                    placeholder="输入标注内容..."
                    value={newAnnotation.content}
                    onChange={(e) => setNewAnnotation(prev => ({ ...prev, content: e.target.value }))}
                    className="min-h-[80px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="annotationAuthor">作者（可选）</Label>
                  <Input
                    id="annotationAuthor"
                    placeholder="输入作者名称"
                    value={newAnnotation.author}
                    onChange={(e) => setNewAnnotation(prev => ({ ...prev, author: e.target.value }))}
                  />
                </div>

                <div className="flex gap-2">
                  <Button onClick={addAnnotation} disabled={!newAnnotation.content.trim()}>
                    <PlusCircle className="h-4 w-4 mr-1" />
                    添加标注
                  </Button>
                  <Button variant="outline" onClick={() => setIsAnnotating(false)}>
                    取消
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 标注统计 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">标注统计</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>总计: {statistics.total}</div>
                <div>可见: {statistics.visible}</div>
                <div>作者: {statistics.authors.length}</div>
                <div>平均长度: {statistics.avgAnnotationLength}</div>
              </div>

              <div className="space-y-2">
                <Label>类型分布：</Label>
                <div className="space-y-1">
                  {Object.entries(statistics.typeCount).map(([type, count]) => {
                    const typeInfo = ANNOTATION_TYPES.find(t => t.id === type);
                    return (
                      <div key={type} className="flex justify-between items-center text-sm">
                        <div className="flex items-center gap-2">
                          {typeInfo?.icon}
                          {typeInfo?.name}
                        </div>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 筛选控制 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">筛选和导出</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>按类型筛选</Label>
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    {ANNOTATION_TYPES.map(type => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>按作者筛选</Label>
                <Select value={filterAuthor} onValueChange={setFilterAuthor}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部作者</SelectItem>
                    {statistics.authors.map(author => (
                      <SelectItem key={author} value={author}>
                        {author}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="space-y-2">
                <Button variant="outline" onClick={exportAnnotations} className="w-full">
                  <Download className="h-4 w-4 mr-1" />
                  导出JSON
                </Button>
                <Button variant="outline" onClick={exportAnnotatedHTML} className="w-full">
                  <Download className="h-4 w-4 mr-1" />
                  导出HTML
                </Button>
                <Button variant="outline" onClick={handleCopy} className="w-full">
                  <Copy className="h-4 w-4 mr-1" />
                  复制文本
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 标注列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">标注列表</CardTitle>
        </CardHeader>
        <CardContent>
          {filteredAnnotations.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <MessageSquare className="h-12 w-12 mx-auto mb-2" />
              <div className="text-lg font-medium">暂无标注</div>
              <div className="text-sm">选择文本开始添加标注</div>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredAnnotations.map((annotation) => {
                const typeInfo = ANNOTATION_TYPES.find(t => t.id === annotation.type);
                return (
                  <div
                    key={annotation.id}
                    className={`p-4 border rounded ${annotation.isVisible ? 'bg-white' : 'bg-gray-50 opacity-60'}`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center gap-2">
                        {typeInfo?.icon}
                        <Badge variant="outline" style={{ borderColor: annotation.color }}>
                          {typeInfo?.name}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          "{annotation.text.substring(0, 30)}..."
                        </span>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleAnnotationVisibility(annotation.id)}
                        >
                          {annotation.isVisible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAnnotation(annotation.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-sm mb-2">{annotation.content}</div>
                    <div className="flex justify-between items-center text-xs text-muted-foreground">
                      <span>{annotation.author}</span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {annotation.timestamp}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium">基本操作</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 在文本框中输入或粘贴文本</li>
                <li>• 选中文本片段添加标注</li>
                <li>• 选择标注类型和颜色</li>
                <li>• 添加标注内容和作者信息</li>
                <li>• 使用筛选功能管理标注</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium">标注类型</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• <span className="text-blue-600">评论</span>：一般性评论说明</li>
                <li>• <span className="text-yellow-600">高亮</span>：重要内容标记</li>
                <li>• <span className="text-green-600">标签</span>：分类和标记</li>
                <li>• <span className="text-purple-600">笔记</span>：详细说明</li>
                <li>• <span className="text-orange-600">疑问</span>：问题和疑虑</li>
                <li>• <span className="text-red-600">警告</span>：注意事项</li>
                <li>• <span className="text-red-600">修正</span>：错误修正</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}