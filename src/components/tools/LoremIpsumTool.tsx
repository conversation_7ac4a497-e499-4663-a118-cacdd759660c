'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Copy, RefreshCw, Trash2 } from 'lucide-react';

export default function LoremIpsumTool() {
  // 状态定义
  const [type, setType] = useState<'paragraphs' | 'sentences' | 'words'>('paragraphs');
  const [count, setCount] = useState('3');
  const [language, setLanguage] = useState<'latin' | 'chinese' | 'english'>('latin');
  const [startWithLorem, setStartWithLorem] = useState(true);
  const [includeHtml, setIncludeHtml] = useState(false);
  const [output, setOutput] = useState('');
  const [error, setError] = useState('');

  // Lorem Ipsum 文本库
  const loremTexts = useMemo(() => ({
    latin: {
      words: [
        'lorem', 'ipsum', 'dolor', 'sit', 'amet', 'consectetur', 'adipiscing', 'elit',
        'sed', 'do', 'eiusmod', 'tempor', 'incididunt', 'ut', 'labore', 'et', 'dolore',
        'magna', 'aliqua', 'enim', 'ad', 'minim', 'veniam', 'quis', 'nostrud',
        'exercitation', 'ullamco', 'laboris', 'nisi', 'aliquip', 'ex', 'ea', 'commodo',
        'consequat', 'duis', 'aute', 'irure', 'in', 'reprehenderit', 'voluptate',
        'velit', 'esse', 'cillum', 'fugiat', 'nulla', 'pariatur', 'excepteur', 'sint',
        'occaecat', 'cupidatat', 'non', 'proident', 'sunt', 'culpa', 'qui', 'officia',
        'deserunt', 'mollit', 'anim', 'id', 'est', 'laborum', 'at', 'vero', 'eos',
        'accusamus', 'accusantium', 'doloremque', 'laudantium', 'totam', 'rem',
        'aperiam', 'eaque', 'ipsa', 'quae', 'ab', 'illo', 'inventore', 'veritatis'
      ],
      sentences: [
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
        'Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.',
        'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum.',
        'Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia.',
        'At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis.',
        'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium.',
        'Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.',
        'Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet.',
        'Consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt.'
      ]
    },
    chinese: {
      words: [
        '这里', '是', '一段', '中文', '占位', '文本', '内容', '用于', '测试', '和',
        '演示', '排版', '效果', '可以', '根据', '需要', '调整', '长度', '格式',
        '样式', '文字', '段落', '句子', '布局', '设计', '开发', '项目', '网站',
        '应用', '程序', '界面', '用户', '体验', '产品', '功能', '服务', '系统',
        '平台', '工具', '方案', '技术', '创新', '质量', '标准', '规范', '流程'
      ],
      sentences: [
        '这里是一段中文占位文本，用于测试和演示排版效果。',
        '可以根据需要调整文本的长度、格式和样式。',
        '在网站开发和设计过程中，经常需要使用占位文本来填充内容区域。',
        '这样可以更好地展示页面布局和文字排版的视觉效果。',
        '占位文本应该具有一定的可读性，同时不会分散注意力。',
        '中文占位文本相比拉丁文更适合中文网站的设计需求。',
        '通过使用合适的占位文本，可以提高设计和开发的效率。',
        '占位文本的长度和内容应该符合实际使用场景的需求。',
        '在正式发布之前，记得将所有占位文本替换为真实内容。',
        '好的占位文本能够帮助客户更好地理解最终产品的效果。'
      ]
    },
    english: {
      words: [
        'the', 'quick', 'brown', 'fox', 'jumps', 'over', 'lazy', 'dog', 'this',
        'is', 'sample', 'text', 'for', 'testing', 'layout', 'design', 'and',
        'typography', 'purposes', 'you', 'can', 'adjust', 'length', 'format',
        'style', 'according', 'to', 'your', 'needs', 'placeholder', 'content',
        'website', 'development', 'application', 'user', 'interface', 'experience',
        'product', 'service', 'system', 'platform', 'solution', 'technology',
        'innovation', 'quality', 'standard', 'process', 'framework', 'structure'
      ],
      sentences: [
        'This is sample placeholder text for testing and demonstration purposes.',
        'You can adjust the length, format, and style according to your needs.',
        'Placeholder text is commonly used in web development and design projects.',
        'It helps to visualize the layout and typography without being distracted by content.',
        'The text should be readable yet not draw attention away from the design elements.',
        'English placeholder text is widely used in international web projects.',
        'Using appropriate placeholder text can improve development efficiency.',
        'The length and content should match the requirements of actual use cases.',
        'Remember to replace all placeholder text with real content before publishing.',
        'Good placeholder text helps clients understand the final product better.'
      ]
    }
  }), []);

  // 生成文本的核心函数
  const generateText = useMemo(() => {
    try {
      const numCount = parseInt(count);
      if (isNaN(numCount) || numCount <= 0) {
        setError('请输入有效的数量');
        return '';
      }

      if (numCount > 100) {
        setError('数量不能超过100');
        return '';
      }

      setError('');
      
      const textData = loremTexts[language];
      let result = '';

      if (type === 'words') {
        const words = [];
        const availableWords = [...textData.words];
        
        // 如果是拉丁文且选择以Lorem开头
        if (language === 'latin' && startWithLorem && numCount > 0) {
          words.push('Lorem');
          for (let i = 1; i < numCount; i++) {
            const randomIndex = Math.floor(Math.random() * availableWords.length);
            words.push(availableWords[randomIndex]);
          }
        } else {
          for (let i = 0; i < numCount; i++) {
            const randomIndex = Math.floor(Math.random() * availableWords.length);
            words.push(availableWords[randomIndex]);
          }
        }
        
        result = words.join(' ') + '.';
        
      } else if (type === 'sentences') {
        const sentences = [];
        const availableSentences = [...textData.sentences];
        
        for (let i = 0; i < numCount; i++) {
          const randomIndex = Math.floor(Math.random() * availableSentences.length);
          sentences.push(availableSentences[randomIndex]);
        }
        
        result = sentences.join(' ');
        
      } else { // paragraphs
        const paragraphs = [];
        
        for (let i = 0; i < numCount; i++) {
          const sentencesInParagraph = Math.floor(Math.random() * 4) + 3; // 3-6句
          const sentences = [];
          const availableSentences = [...textData.sentences];
          
          for (let j = 0; j < sentencesInParagraph; j++) {
            const randomIndex = Math.floor(Math.random() * availableSentences.length);
            sentences.push(availableSentences[randomIndex]);
          }
          
          paragraphs.push(sentences.join(' '));
        }
        
        result = paragraphs.join('\n\n');
      }

      // 如果需要HTML格式
      if (includeHtml) {
        if (type === 'paragraphs') {
          result = result.split('\n\n').map(p => `<p>${p}</p>`).join('\n');
        } else if (type === 'sentences') {
          result = `<p>${result}</p>`;
        } else {
          result = `<span>${result}</span>`;
        }
      }

      return result;
    } catch (err) {
      setError('生成文本时出错');
      return '';
    }
  }, [type, count, language, startWithLorem, includeHtml, loremTexts]);

  // 生成新文本
  const handleGenerate = () => {
    setOutput(generateText);
  };

  // 复制到剪贴板
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(output);
      // 可选：显示复制成功提示
    } catch (err) {
      setError('复制失败');
    }
  };

  // 清空
  const handleClear = () => {
    setOutput('');
    setError('');
  };

  // 初始生成
  React.useEffect(() => {
    if (generateText) {
      setOutput(generateText);
    }
  }, [generateText]);

  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-4">
      {/* 工具说明卡片 */}
      <Card>
        <CardHeader>
          <CardTitle>Lorem Ipsum 生成器</CardTitle>
          <CardDescription>
            生成占位文本，支持多种语言和格式。适用于网站设计、排版测试和内容填充。
            可以生成段落、句子或单词，并支持HTML格式输出。
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 配置区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">生成配置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* 生成类型 */}
            <div className="space-y-2">
              <Label htmlFor="type">生成类型</Label>
              <Select value={type} onValueChange={(value: 'paragraphs' | 'sentences' | 'words') => setType(value)}>
                <SelectTrigger id="type">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="paragraphs">段落</SelectItem>
                  <SelectItem value="sentences">句子</SelectItem>
                  <SelectItem value="words">单词</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 数量 */}
            <div className="space-y-2">
              <Label htmlFor="count">数量</Label>
              <Input
                id="count"
                type="number"
                min="1"
                max="100"
                value={count}
                onChange={(e) => setCount(e.target.value)}
                placeholder="输入数量"
              />
            </div>

            {/* 语言 */}
            <div className="space-y-2">
              <Label htmlFor="language">语言</Label>
              <Select value={language} onValueChange={(value: 'latin' | 'chinese' | 'english') => setLanguage(value)}>
                <SelectTrigger id="language">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="latin">拉丁文</SelectItem>
                  <SelectItem value="chinese">中文</SelectItem>
                  <SelectItem value="english">英文</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 额外选项 */}
          <div className="flex flex-wrap gap-4">
            {language === 'latin' && (
              <div className="flex items-center space-x-2">
                <Switch
                  id="startWithLorem"
                  checked={startWithLorem}
                  onCheckedChange={setStartWithLorem}
                />
                <Label htmlFor="startWithLorem">以&quot;Lorem&quot;开头</Label>
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <Switch
                id="includeHtml"
                checked={includeHtml}
                onCheckedChange={setIncludeHtml}
              />
              <Label htmlFor="includeHtml">HTML格式</Label>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button onClick={handleGenerate}>
              <RefreshCw className="h-4 w-4 mr-2" />
              重新生成
            </Button>
            <Button variant="outline" onClick={handleClear}>
              <Trash2 className="h-4 w-4 mr-2" />
              清空
            </Button>
          </div>

          {error && (
            <div className="text-sm text-red-500">{error}</div>
          )}
        </CardContent>
      </Card>

      {/* 输出区域 */}
      {output && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">生成结果</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
              >
                <Copy className="h-4 w-4 mr-1" />
                复制
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Textarea
              value={output}
              readOnly
              className="min-h-[300px] font-mono text-sm"
              placeholder="生成的文本将显示在这里..."
            />
            <div className="mt-2 text-xs text-muted-foreground">
              {type === 'paragraphs' && `共 ${output.split('\n\n').length} 个段落`}
              {type === 'sentences' && `共 ${output.split('. ').length} 个句子`}
              {type === 'words' && `共 ${output.split(' ').length - 1} 个单词`}
              {includeHtml && ' (HTML格式)'}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
