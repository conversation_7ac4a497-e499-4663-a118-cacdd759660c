'use client'

import { useState, useRef, useEffect } from 'react'
import { QrCode, Download, Copy, Settings, Palette } from 'lucide-react'

// 简单的二维码生成函数（实际应用中应使用 qrcode.js 等专业库）
function generateQRCodeSVG(text: string, size: number = 200, color: string = '#000000'): string {
  // 这是一个简化的示例，实际应该使用专业的二维码库
  // 这里只是创建一个占位符 SVG
  const modules = 25 // 简化的模块数
  const moduleSize = size / modules
  
  let svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">`
  
  // 生成简单的图案（实际应该是二维码矩阵）
  for (let row = 0; row < modules; row++) {
    for (let col = 0; col < modules; col++) {
      // 简单的伪随机图案生成
      const shouldFill = (row + col + text.length) % 3 !== 0
      if (shouldFill) {
        svg += `<rect x="${col * moduleSize}" y="${row * moduleSize}" width="${moduleSize}" height="${moduleSize}" fill="${color}"/>`
      }
    }
  }
  
  // 添加中心标识
  const centerSize = moduleSize * 3
  const centerPos = (size - centerSize) / 2
  svg += `<rect x="${centerPos}" y="${centerPos}" width="${centerSize}" height="${centerSize}" fill="${color}"/>`
  svg += `<rect x="${centerPos + moduleSize}" y="${centerPos + moduleSize}" width="${moduleSize}" height="${moduleSize}" fill="white"/>`
  
  svg += '</svg>'
  return svg
}

export default function QRCodeGeneratorTool() {
  const [text, setText] = useState('')
  const [size, setSize] = useState(256)
  const [color, setColor] = useState('#000000')
  const [bgColor, setBgColor] = useState('#FFFFFF')
  const [errorLevel, setErrorLevel] = useState<'L' | 'M' | 'Q' | 'H'>('M')
  const [qrCodeSVG, setQrCodeSVG] = useState('')
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    if (text) {
      const svg = generateQRCodeSVG(text, size, color)
      setQrCodeSVG(svg)
    } else {
      setQrCodeSVG('')
    }
  }, [text, size, color])

  const downloadQRCode = (format: 'png' | 'svg') => {
    if (!qrCodeSVG) return

    if (format === 'svg') {
      const blob = new Blob([qrCodeSVG], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'qrcode.svg'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } else {
      // PNG 下载需要将 SVG 转换为 Canvas
      const canvas = canvasRef.current
      if (!canvas) return
      
      const ctx = canvas.getContext('2d')
      if (!ctx) return
      
      canvas.width = size
      canvas.height = size
      
      // 填充背景
      ctx.fillStyle = bgColor
      ctx.fillRect(0, 0, size, size)
      
      // 创建图像并绘制
      const img = new Image()
      img.onload = () => {
        ctx.drawImage(img, 0, 0)
        canvas.toBlob((blob) => {
          if (!blob) return
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = 'qrcode.png'
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)
        })
      }
      img.src = 'data:image/svg+xml;base64,' + btoa(qrCodeSVG)
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  const presets = [
    { label: '网址', value: 'https://example.com' },
    { label: 'WiFi', value: 'WIFI:T:WPA;S:MyNetwork;P:MyPassword;;' },
    { label: '邮箱', value: 'mailto:<EMAIL>' },
    { label: '电话', value: 'tel:+1234567890' },
  ]

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">二维码生成器</h2>
        <p className="text-gray-600">
          快速生成各种内容的二维码，支持自定义样式
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 左侧：输入和设置 */}
        <div className="space-y-6">
          {/* 输入区域 */}
          <div>
            <label htmlFor="qr-input" className="block text-sm font-medium text-gray-700 mb-2">
              输入内容
            </label>
            <textarea
              id="qr-input"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="输入文本、网址、WiFi信息等..."
              className="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              rows={4}
            />
            <div className="mt-2 text-sm text-gray-500">
              已输入 {text.length} 个字符
            </div>
          </div>

          {/* 快速预设 */}
          <div>
            <p className="text-sm font-medium text-gray-700 mb-2">快速预设</p>
            <div className="flex flex-wrap gap-2">
              {presets.map((preset) => (
                <button
                  key={preset.label}
                  onClick={() => setText(preset.value)}
                  className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  {preset.label}
                </button>
              ))}
            </div>
          </div>

          {/* 样式设置 */}
          <div className="space-y-4">
            <h3 className="flex items-center text-sm font-medium text-gray-700">
              <Settings className="w-4 h-4 mr-2" />
              样式设置
            </h3>

            {/* 尺寸 */}
            <div>
              <label htmlFor="size" className="block text-sm text-gray-600 mb-1">
                尺寸: {size}px
              </label>
              <input
                id="size"
                type="range"
                min="128"
                max="512"
                step="32"
                value={size}
                onChange={(e) => setSize(Number(e.target.value))}
                className="w-full"
              />
            </div>

            {/* 颜色 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="color" className="block text-sm text-gray-600 mb-1">
                  前景色
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    id="color"
                    type="color"
                    value={color}
                    onChange={(e) => setColor(e.target.value)}
                    className="h-10 w-20"
                  />
                  <input
                    type="text"
                    value={color}
                    onChange={(e) => setColor(e.target.value)}
                    className="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="bgColor" className="block text-sm text-gray-600 mb-1">
                  背景色
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    id="bgColor"
                    type="color"
                    value={bgColor}
                    onChange={(e) => setBgColor(e.target.value)}
                    className="h-10 w-20"
                  />
                  <input
                    type="text"
                    value={bgColor}
                    onChange={(e) => setBgColor(e.target.value)}
                    className="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              </div>
            </div>

            {/* 纠错级别 */}
            <div>
              <label htmlFor="errorLevel" className="block text-sm text-gray-600 mb-1">
                纠错级别
              </label>
              <select
                id="errorLevel"
                value={errorLevel}
                onChange={(e) => setErrorLevel(e.target.value as 'L' | 'M' | 'Q' | 'H')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="L">低 (7%)</option>
                <option value="M">中 (15%)</option>
                <option value="Q">较高 (25%)</option>
                <option value="H">高 (30%)</option>
              </select>
            </div>
          </div>
        </div>

        {/* 右侧：预览和下载 */}
        <div className="space-y-6">
          {/* 预览区域 */}
          <div className="bg-gray-50 rounded-lg p-8 flex items-center justify-center min-h-[400px]">
            {qrCodeSVG ? (
              <div 
                dangerouslySetInnerHTML={{ __html: qrCodeSVG }}
                style={{ backgroundColor: bgColor }}
                className="rounded-lg shadow-lg"
              />
            ) : (
              <div className="text-center text-gray-400">
                <QrCode className="w-24 h-24 mx-auto mb-4" />
                <p>输入内容后将在此显示二维码</p>
              </div>
            )}
          </div>

          {/* 下载按钮 */}
          {qrCodeSVG && (
            <div className="space-y-3">
              <button
                onClick={() => downloadQRCode('png')}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Download className="inline-block w-4 h-4 mr-2" />
                下载 PNG 图片
              </button>
              <button
                onClick={() => downloadQRCode('svg')}
                className="w-full px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Download className="inline-block w-4 h-4 mr-2" />
                下载 SVG 矢量图
              </button>
              <button
                onClick={copyToClipboard}
                className="w-full px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Copy className="inline-block w-4 h-4 mr-2" />
                复制原始内容
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 隐藏的 Canvas 用于 PNG 导出 */}
      <canvas ref={canvasRef} style={{ display: 'none' }} />

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <QrCode className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>二维码可存储文本、网址、WiFi密码、联系人等信息</li>
              <li>纠错级别越高，二维码越复杂，但容错能力越强</li>
              <li>建议使用高对比度的颜色组合以确保扫描成功率</li>
              <li>PNG 格式适合打印，SVG 格式适合缩放和编辑</li>
              <li>注：此为演示版本，实际应用需要专业二维码库</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
