'use client'

import { useState, useEffect, useCallback } from 'react'
import { Copy, Check, RefreshCw, Settings, Download, History } from 'lucide-react'

type UUIDVersion = 'v4' | 'v1' | 'timestamp' | 'nil'

interface GeneratedUUID {
  id: string
  value: string
  timestamp: number
  version: UUIDVersion
}

interface UUIDSettings {
  uppercase: boolean
  hyphens: boolean
  braces: boolean
  quantity: number
}

export default function UUIDGeneratorTool() {
  const [currentUUID, setCurrentUUID] = useState('')
  const [history, setHistory] = useState<GeneratedUUID[]>([])
  const [copied, setCopied] = useState<string>('')
  const [version, setVersion] = useState<UUIDVersion>('v4')
  const [settings, setSettings] = useState<UUIDSettings>({
    uppercase: false,
    hyphens: true,
    braces: false,
    quantity: 1,
  })
  const [showSettings, setShowSettings] = useState(false)
  const [bulkUUIDs, setBulkUUIDs] = useState<string[]>([])

  // 生成 UUID v4
  const generateUUIDv4 = (): string => {
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0
      const v = c === 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
    return uuid
  }

  // 生成 UUID v1 (基于时间戳和MAC地址的简化版本)
  const generateUUIDv1 = (): string => {
    // 简化版本，使用时间戳和随机数
    const timestamp = Date.now()
    const timestampHex = timestamp.toString(16).padStart(12, '0')
    const clockSeq = Math.floor(Math.random() * 0x3fff) | 0x8000
    const node = Array.from({ length: 6 }, () => 
      Math.floor(Math.random() * 256).toString(16).padStart(2, '0')
    ).join('')
    
    return `${timestampHex.slice(0, 8)}-${timestampHex.slice(8, 12)}-1${timestampHex.slice(12)}-${clockSeq.toString(16)}-${node}`
  }

  // 生成基于时间戳的 UUID
  const generateTimestampUUID = (): string => {
    const timestamp = Date.now().toString(16).padStart(12, '0')
    const random = Array.from({ length: 20 }, () => 
      Math.floor(Math.random() * 16).toString(16)
    ).join('')
    
    return `${timestamp.slice(0, 8)}-${timestamp.slice(8)}-${random.slice(0, 4)}-${random.slice(4, 8)}-${random.slice(8, 20)}`
  }

  // 生成 nil UUID
  const generateNilUUID = (): string => {
    return '00000000-0000-0000-0000-000000000000'
  }

  // 根据版本生成 UUID
  const generateUUID = useCallback((ver: UUIDVersion = version): string => {
    let uuid = ''
    
    switch (ver) {
      case 'v4':
        uuid = generateUUIDv4()
        break
      case 'v1':
        uuid = generateUUIDv1()
        break
      case 'timestamp':
        uuid = generateTimestampUUID()
        break
      case 'nil':
        uuid = generateNilUUID()
        break
    }

    // 应用格式设置
    if (!settings.hyphens) {
      uuid = uuid.replace(/-/g, '')
    }
    if (settings.uppercase) {
      uuid = uuid.toUpperCase()
    }
    if (settings.braces) {
      uuid = `{${uuid}}`
    }

    return uuid
  }, [version, settings])

  // 生成新的 UUID
  const handleGenerate = useCallback(() => {
    if (settings.quantity > 1) {
      // 批量生成
      const uuids = Array.from({ length: settings.quantity }, () => generateUUID())
      setBulkUUIDs(uuids)
      setCurrentUUID(uuids[0])
      
      // 添加到历史记录
      const newHistory: GeneratedUUID = {
        id: Date.now().toString(),
        value: `${uuids.length} UUIDs`,
        timestamp: Date.now(),
        version,
      }
      setHistory([newHistory, ...history.slice(0, 19)])
    } else {
      // 单个生成
      const uuid = generateUUID()
      setCurrentUUID(uuid)
      setBulkUUIDs([])
      
      // 添加到历史记录
      const newHistory: GeneratedUUID = {
        id: Date.now().toString(),
        value: uuid,
        timestamp: Date.now(),
        version,
      }
      setHistory([newHistory, ...history.slice(0, 19)])
    }
  }, [settings.quantity, version, history, generateUUID])

  // 复制到剪贴板
  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(id)
      setTimeout(() => setCopied(''), 2000)
    })
  }

  // 复制所有批量生成的 UUID
  const copyAllBulkUUIDs = () => {
    const text = bulkUUIDs.join('\n')
    navigator.clipboard.writeText(text).then(() => {
      setCopied('bulk')
      setTimeout(() => setCopied(''), 2000)
    })
  }

  // 下载批量生成的 UUID
  const downloadBulkUUIDs = () => {
    const text = bulkUUIDs.join('\n')
    const blob = new Blob([text], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `uuids_${Date.now()}.txt`
    link.click()
    URL.revokeObjectURL(url)
  }

  // 初始生成
  useEffect(() => {
    handleGenerate()
  }, [handleGenerate])

  // 格式化时间戳
  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">UUID 生成器</h2>
        <p className="text-gray-600">
          生成通用唯一识别码（UUID），支持多个版本和格式
        </p>
      </div>

      {/* UUID 版本选择 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <h3 className="font-medium mb-3">UUID 版本</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <button
            onClick={() => setVersion('v4')}
            className={`px-4 py-2 rounded-md border transition-colors ${
              version === 'v4'
                ? 'border-blue-500 bg-blue-50 text-blue-600'
                : 'border-gray-200 hover:bg-gray-50'
            }`}
          >
            <div className="font-medium">UUID v4</div>
            <div className="text-xs mt-1">随机生成</div>
          </button>
          <button
            onClick={() => setVersion('v1')}
            className={`px-4 py-2 rounded-md border transition-colors ${
              version === 'v1'
                ? 'border-blue-500 bg-blue-50 text-blue-600'
                : 'border-gray-200 hover:bg-gray-50'
            }`}
          >
            <div className="font-medium">UUID v1</div>
            <div className="text-xs mt-1">基于时间</div>
          </button>
          <button
            onClick={() => setVersion('timestamp')}
            className={`px-4 py-2 rounded-md border transition-colors ${
              version === 'timestamp'
                ? 'border-blue-500 bg-blue-50 text-blue-600'
                : 'border-gray-200 hover:bg-gray-50'
            }`}
          >
            <div className="font-medium">时间戳</div>
            <div className="text-xs mt-1">时间优先</div>
          </button>
          <button
            onClick={() => setVersion('nil')}
            className={`px-4 py-2 rounded-md border transition-colors ${
              version === 'nil'
                ? 'border-blue-500 bg-blue-50 text-blue-600'
                : 'border-gray-200 hover:bg-gray-50'
            }`}
          >
            <div className="font-medium">Nil UUID</div>
            <div className="text-xs mt-1">全零值</div>
          </button>
        </div>
      </div>

      {/* 当前生成的 UUID */}
      {bulkUUIDs.length <= 1 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium">生成的 UUID</h3>
            <button
              onClick={handleGenerate}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-md"
              title="重新生成"
            >
              <RefreshCw className="w-5 h-5" />
            </button>
          </div>
          
          <div className="flex items-center gap-3">
            <input
              type="text"
              value={currentUUID}
              readOnly
              className="flex-1 px-4 py-3 bg-gray-50 border border-gray-200 rounded-md font-mono text-sm"
            />
            <button
              onClick={() => copyToClipboard(currentUUID, 'current')}
              className="p-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              {copied === 'current' ? (
                <Check className="w-5 h-5" />
              ) : (
                <Copy className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
      )}

      {/* 批量生成的 UUID */}
      {bulkUUIDs.length > 1 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium">批量生成的 UUID（{bulkUUIDs.length} 个）</h3>
            <div className="flex gap-2">
              <button
                onClick={copyAllBulkUUIDs}
                className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center"
              >
                {copied === 'bulk' ? (
                  <>
                    <Check className="w-4 h-4 mr-1" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4 mr-1" />
                    复制全部
                  </>
                )}
              </button>
              <button
                onClick={downloadBulkUUIDs}
                className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center"
              >
                <Download className="w-4 h-4 mr-1" />
                下载
              </button>
              <button
                onClick={handleGenerate}
                className="p-1 text-blue-600 hover:bg-blue-50 rounded-md"
                title="重新生成"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          <div className="max-h-64 overflow-y-auto">
            <div className="space-y-2">
              {bulkUUIDs.map((uuid, index) => (
                <div key={index} className="flex items-center gap-2">
                  <span className="text-gray-500 text-sm w-12">{index + 1}.</span>
                  <input
                    type="text"
                    value={uuid}
                    readOnly
                    className="flex-1 px-3 py-2 bg-gray-50 border border-gray-200 rounded font-mono text-sm"
                  />
                  <button
                    onClick={() => copyToClipboard(uuid, `bulk-${index}`)}
                    className="p-2 hover:bg-gray-100 rounded"
                  >
                    {copied === `bulk-${index}` ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 设置面板 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="flex items-center justify-between w-full mb-4"
        >
          <h3 className="font-medium flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            格式设置
          </h3>
          <span className="text-gray-400 text-sm">{showSettings ? '收起' : '展开'}</span>
        </button>
        
        {showSettings && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.uppercase}
                  onChange={(e) => setSettings({ ...settings, uppercase: e.target.checked })}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium">大写字母</div>
                  <div className="text-sm text-gray-500">将 UUID 转换为大写</div>
                </div>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.hyphens}
                  onChange={(e) => setSettings({ ...settings, hyphens: e.target.checked })}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium">包含连字符</div>
                  <div className="text-sm text-gray-500">标准格式包含连字符</div>
                </div>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.braces}
                  onChange={(e) => setSettings({ ...settings, braces: e.target.checked })}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium">添加花括号</div>
                  <div className="text-sm text-gray-500">在 UUID 两端添加 {}</div>
                </div>
              </label>
            </div>
            
            <div>
              <label className="block font-medium mb-2">批量生成数量</label>
              <div className="flex items-center gap-3">
                <input
                  type="number"
                  min="1"
                  max="1000"
                  value={settings.quantity}
                  onChange={(e) => setSettings({ ...settings, quantity: Math.min(1000, Math.max(1, parseInt(e.target.value) || 1)) })}
                  className="w-24 px-3 py-2 border border-gray-300 rounded-md"
                />
                <span className="text-sm text-gray-500">最多 1000 个</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 生成按钮 */}
      <div className="text-center mb-6">
        <button
          onClick={handleGenerate}
          className="px-8 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-lg font-medium"
        >
          生成新的 UUID
        </button>
      </div>

      {/* 历史记录 */}
      {history.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="font-medium mb-4 flex items-center">
            <History className="w-5 h-5 mr-2" />
            生成历史
          </h3>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {history.map((item) => (
              <div key={item.id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                <div className="flex-1">
                  <div className="font-mono text-sm break-all">{item.value}</div>
                  <div className="text-xs text-gray-500">
                    {formatTimestamp(item.timestamp)} · {item.version.toUpperCase()}
                  </div>
                </div>
                {!item.value.includes('UUIDs') && (
                  <button
                    onClick={() => copyToClipboard(item.value, item.id)}
                    className="p-2 hover:bg-gray-200 rounded ml-2"
                  >
                    {copied === item.id ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Settings className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">UUID 版本说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li><strong>UUID v4</strong>：基于随机数生成，最常用的版本</li>
              <li><strong>UUID v1</strong>：基于时间戳和节点信息生成</li>
              <li><strong>时间戳 UUID</strong>：自定义格式，时间戳在前便于排序</li>
              <li><strong>Nil UUID</strong>：特殊的全零 UUID，用于表示空值</li>
              <li>支持批量生成、自定义格式和导出功能</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
