'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Key, Shield, Download, Upload, AlertTriangle, CheckCircle } from 'lucide-react'

const RSAKeyGeneratorTool: React.FC = () => {
  const [keySize, setKeySize] = useState('2048')
  const [keyFormat, setKeyFormat] = useState('PEM')
  const [publicKey, setPublicKey] = useState('')
  const [privateKey, setPrivateKey] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('generate')

  // RSA密钥生成
  const generateRSAKeys = async () => {
    try {
      setLoading(true)
      setError('')
      
      // 使用Web Crypto API生成RSA密钥对
      const keyPair = await window.crypto.subtle.generateKey(
        {
          name: 'RSA-OAEP',
          modulusLength: parseInt(keySize),
          publicExponent: new Uint8Array([1, 0, 1]),
          hash: 'SHA-256'
        },
        true,
        ['encrypt', 'decrypt']
      )

      // 导出公钥
      const exportedPublicKey = await window.crypto.subtle.exportKey(
        'spki',
        keyPair.publicKey
      )
      
      // 导出私钥
      const exportedPrivateKey = await window.crypto.subtle.exportKey(
        'pkcs8',
        keyPair.privateKey
      )

      // 转换为PEM格式
      const publicKeyPEM = arrayBufferToPEM(exportedPublicKey, 'PUBLIC KEY')
      const privateKeyPEM = arrayBufferToPEM(exportedPrivateKey, 'PRIVATE KEY')

      setPublicKey(publicKeyPEM)
      setPrivateKey(privateKeyPEM)

    } catch (err) {
      setError('密钥生成失败：' + (err as Error).message)
    } finally {
      setLoading(false)
    }
  }

  // 将ArrayBuffer转换为PEM格式
  const arrayBufferToPEM = (buffer: ArrayBuffer, type: string): string => {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.length; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    const base64 = btoa(binary)
    
    // 每64个字符换行
    const formatted = base64.match(/.{1,64}/g)?.join('\n') || ''
    
    return `-----BEGIN ${type}-----\n${formatted}\n-----END ${type}-----`
  }

  // 下载密钥文件
  const downloadKey = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 清空密钥
  const clearKeys = () => {
    setPublicKey('')
    setPrivateKey('')
    setError('')
  }

  // 验证密钥格式
  const validateKeyFormat = (key: string): boolean => {
    const pemPattern = /^-----BEGIN [A-Z ]+-----[\s\S]*-----END [A-Z ]+-----$/
    return pemPattern.test(key.trim())
  }

  // 获取密钥信息
  const getKeyInfo = (key: string): any => {
    if (!key) return null
    
    try {
      const lines = key.split('\n')
      const header = lines[0]
      const isPublic = header.includes('PUBLIC KEY')
      const isPrivate = header.includes('PRIVATE KEY')
      
      return {
        type: isPublic ? '公钥' : isPrivate ? '私钥' : '未知',
        format: 'PEM',
        size: keySize + ' bits',
        algorithm: 'RSA',
        valid: validateKeyFormat(key)
      }
    } catch {
      return null
    }
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Key className="w-8 h-8" />
          RSA 密钥生成器
        </h1>
        <p className="text-muted-foreground">
          生成 RSA 公钥和私钥对，用于加密、解密和数字签名
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generate">
            <Key className="w-4 h-4 mr-2" />
            生成密钥
          </TabsTrigger>
          <TabsTrigger value="info">
            <Shield className="w-4 h-4 mr-2" />
            密钥信息
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          {/* 配置区域 */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">密钥配置</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>密钥长度</Label>
                <Select value={keySize} onValueChange={setKeySize}>
                  <option value="1024">1024 位（不推荐）</option>
                  <option value="2048">2048 位（推荐）</option>
                  <option value="3072">3072 位</option>
                  <option value="4096">4096 位（高安全性）</option>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>输出格式</Label>
                <Select value={keyFormat} onValueChange={setKeyFormat}>
                  <option value="PEM">PEM 格式</option>
                </Select>
              </div>
            </div>

            <div className="mt-6 flex gap-3">
              <Button 
                onClick={generateRSAKeys} 
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Key className="w-4 h-4" />
                {loading ? '生成中...' : '生成密钥对'}
              </Button>
              <Button variant="outline" onClick={clearKeys}>
                清空
              </Button>
            </div>

            {/* 安全提示 */}
            <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-800 dark:text-yellow-200">安全提示：</p>
                  <ul className="mt-1 text-yellow-700 dark:text-yellow-300 space-y-1">
                    <li>• 私钥必须绝对保密，不要分享给任何人</li>
                    <li>• 公钥可以公开分享，用于加密和验证签名</li>
                    <li>• 推荐使用 2048 位或更高的密钥长度</li>
                    <li>• 定期更换密钥对以提高安全性</li>
                    <li>• 请在安全的环境中生成和存储密钥</li>
                  </ul>
                </div>
              </div>
            </div>
          </Card>

          {/* 错误显示 */}
          {error && (
            <Card className="p-6">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                <div>
                  <p className="font-medium text-red-800 dark:text-red-200">错误信息</p>
                  <p className="text-red-700 dark:text-red-300">{error}</p>
                </div>
              </div>
            </Card>
          )}

          {/* 密钥显示区域 */}
          {(publicKey || privateKey) && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 公钥 */}
              {publicKey && (
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">公钥 (Public Key)</h3>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(publicKey)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadKey(publicKey, 'rsa_public_key.pem')}
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  <Textarea
                    value={publicKey}
                    readOnly
                    className="min-h-[200px] font-mono text-sm bg-muted"
                  />
                </Card>
              )}

              {/* 私钥 */}
              {privateKey && (
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">私钥 (Private Key)</h3>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(privateKey)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadKey(privateKey, 'rsa_private_key.pem')}
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  <Textarea
                    value={privateKey}
                    readOnly
                    className="min-h-[200px] font-mono text-sm bg-muted"
                  />
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="info" className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">密钥信息查看</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>粘贴密钥内容</Label>
                <Textarea
                  placeholder="请粘贴 RSA 公钥或私钥..."
                  className="min-h-[120px] font-mono"
                  onChange={(e) => {
                    // 这里可以添加密钥信息解析逻辑
                  }}
                />
              </div>
            </div>
          </Card>

          {/* 密钥信息显示 */}
          {(publicKey || privateKey) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {publicKey && (
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4">公钥信息</h3>
                  <div className="space-y-3">
                    {(() => {
                      const info = getKeyInfo(publicKey)
                      return info ? (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-sm">密钥格式有效</span>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>类型：{info.type}</div>
                            <div>格式：{info.format}</div>
                            <div>长度：{info.size}</div>
                            <div>算法：{info.algorithm}</div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm text-muted-foreground">
                          生成密钥后查看信息
                        </div>
                      )
                    })()}
                  </div>
                </Card>
              )}

              {privateKey && (
                <Card className="p-6">
                  <h3 className="text-lg font-semibold mb-4">私钥信息</h3>
                  <div className="space-y-3">
                    {(() => {
                      const info = getKeyInfo(privateKey)
                      return info ? (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-sm">密钥格式有效</span>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>类型：{info.type}</div>
                            <div>格式：{info.format}</div>
                            <div>长度：{info.size}</div>
                            <div>算法：{info.algorithm}</div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm text-muted-foreground">
                          生成密钥后查看信息
                        </div>
                      )
                    })()}
                  </div>
                </Card>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* 使用说明 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">使用说明</h3>
        <div className="space-y-4 text-sm text-muted-foreground">
          <div>
            <h4 className="font-medium text-foreground mb-2">RSA 算法说明：</h4>
            <p>RSA 是一种非对称加密算法，使用一对密钥：公钥用于加密，私钥用于解密。</p>
          </div>

          <div>
            <h4 className="font-medium text-foreground mb-2">密钥用途：</h4>
            <ul className="space-y-1 ml-4">
              <li>• <strong>公钥</strong>：用于加密数据和验证数字签名</li>
              <li>• <strong>私钥</strong>：用于解密数据和创建数字签名</li>
              <li>• <strong>密钥交换</strong>：在 HTTPS、SSH 等协议中用于安全通信</li>
              <li>• <strong>数字证书</strong>：用于身份验证和信任链</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-foreground mb-2">密钥长度选择：</h4>
            <ul className="space-y-1 ml-4">
              <li>• <strong>1024 位</strong>：已不安全，不推荐使用</li>
              <li>• <strong>2048 位</strong>：目前推荐的标准长度</li>
              <li>• <strong>3072 位</strong>：更高安全性，适合长期使用</li>
              <li>• <strong>4096 位</strong>：最高安全性，但计算开销较大</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-foreground mb-2">安全注意事项：</h4>
            <ul className="space-y-1 ml-4">
              <li>• 私钥必须绝对保密，不要通过不安全的渠道传输</li>
              <li>• 定期更换密钥对，建议每1-2年更换一次</li>
              <li>• 使用强密码保护私钥文件</li>
              <li>• 备份密钥文件，避免丢失</li>
              <li>• 在安全的环境中生成和存储密钥</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default RSAKeyGeneratorTool
