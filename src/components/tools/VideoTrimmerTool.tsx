'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  Download, 
  Play, 
  Pause,
  SkipBack,
  SkipForward,
  Video,
  Scissors,
  Settings,
  Eye,
  Volume2,
  VolumeX,
  RotateCw,
  Maximize,
  Info,
  Clock,
  FileVideo,
  Zap
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface VideoSegment {
  id: string
  start: number
  end: number
  name: string
  selected: boolean
}

interface ExportSettings {
  format: 'mp4' | 'webm' | 'mov'
  quality: 'high' | 'medium' | 'low'
  resolution: 'original' | '1080p' | '720p' | '480p'
  fps: number | 'original'
  removeAudio: boolean
  fadeIn: boolean
  fadeOut: boolean
  fadeInDuration: number
  fadeOutDuration: number
}

interface VideoInfo {
  duration: number
  width: number
  height: number
  fps: number
  size: string
  codec: string
  hasAudio: boolean
}

const VideoTrimmerTool: React.FC = () => {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const timelineRef = useRef<HTMLDivElement>(null)

  const [videoFile, setVideoFile] = useState<File | null>(null)
  const [videoUrl, setVideoUrl] = useState<string>('')
  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [loadingProgress, setLoadingProgress] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [playbackSpeed, setPlaybackSpeed] = useState(1)

  const [trimStart, setTrimStart] = useState(0)
  const [trimEnd, setTrimEnd] = useState(0)
  const [isDragging, setIsDragging] = useState<'start' | 'end' | null>(null)
  const [segments, setSegments] = useState<VideoSegment[]>([])
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null)

  const [exportSettings, setExportSettings] = useState<ExportSettings>({
    format: 'mp4',
    quality: 'high',
    resolution: 'original',
    fps: 'original',
    removeAudio: false,
    fadeIn: false,
    fadeOut: false,
    fadeInDuration: 1,
    fadeOutDuration: 1
  })

  const [isProcessing, setIsProcessing] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)

  // 视频加载处理
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type.startsWith('video/')) {
      setVideoFile(file)
      const url = URL.createObjectURL(file)
      setVideoUrl(url)
      
      // 清理之前的状态
      setSegments([])
      setTrimStart(0)
      setTrimEnd(0)
      setCurrentTime(0)
    } else {
      toast({
        title: '文件格式错误',
        description: '请选择视频文件',
        variant: 'destructive'
      })
    }
  }, [toast])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    const file = event.dataTransfer.files[0]
    if (file && file.type.startsWith('video/')) {
      setVideoFile(file)
      const url = URL.createObjectURL(file)
      setVideoUrl(url)
      setSegments([])
      setTrimStart(0)
      setTrimEnd(0)
      setCurrentTime(0)
    }
  }, [])

  // 视频元数据加载
  const handleVideoLoad = useCallback(() => {
    const video = videoRef.current
    if (video) {
      const duration = video.duration
      setDuration(duration)
      setTrimEnd(duration)
      
      setVideoInfo({
        duration,
        width: video.videoWidth,
        height: video.videoHeight,
        fps: 30, // 简化，实际需要从视频中获取
        size: videoFile ? (videoFile.size / 1024 / 1024).toFixed(2) + ' MB' : '',
        codec: 'H.264', // 简化
        hasAudio: true // 简化
      })
    }
  }, [videoFile])

  // 播放控制
  const togglePlayback = useCallback(() => {
    const video = videoRef.current
    if (video) {
      if (isPlaying) {
        video.pause()
      } else {
        video.play()
      }
      setIsPlaying(!isPlaying)
    }
  }, [isPlaying])

  const handleTimeUpdate = useCallback(() => {
    const video = videoRef.current
    if (video) {
      setCurrentTime(video.currentTime)
      
      // 如果播放超出了裁剪范围，暂停视频
      if (video.currentTime >= trimEnd) {
        video.pause()
        setIsPlaying(false)
        video.currentTime = trimEnd
      }
    }
  }, [trimEnd])

  const seekTo = useCallback((time: number) => {
    const video = videoRef.current
    if (video) {
      video.currentTime = Math.max(trimStart, Math.min(time, trimEnd))
      setCurrentTime(video.currentTime)
    }
  }, [trimStart, trimEnd])

  const handleVolumeChange = useCallback((value: number) => {
    const video = videoRef.current
    if (video) {
      video.volume = value
      setVolume(value)
      setIsMuted(value === 0)
    }
  }, [])

  const toggleMute = useCallback(() => {
    const video = videoRef.current
    if (video) {
      if (isMuted) {
        video.volume = volume
        setIsMuted(false)
      } else {
        video.volume = 0
        setIsMuted(true)
      }
    }
  }, [isMuted, volume])

  const handlePlaybackSpeedChange = useCallback((speed: number) => {
    const video = videoRef.current
    if (video) {
      video.playbackRate = speed
      setPlaybackSpeed(speed)
    }
  }, [])

  // 裁剪控制
  const handleTrimChange = useCallback((type: 'start' | 'end', value: number) => {
    if (type === 'start') {
      const newStart = Math.max(0, Math.min(value, trimEnd - 0.1))
      setTrimStart(newStart)
      if (currentTime < newStart) {
        seekTo(newStart)
      }
    } else {
      const newEnd = Math.max(trimStart + 0.1, Math.min(value, duration))
      setTrimEnd(newEnd)
      if (currentTime > newEnd) {
        seekTo(newEnd)
      }
    }
  }, [trimStart, trimEnd, duration, currentTime, seekTo])

  // 时间轴点击处理
  const handleTimelineClick = useCallback((event: React.MouseEvent) => {
    if (timelineRef.current) {
      const rect = timelineRef.current.getBoundingClientRect()
      const x = event.clientX - rect.left
      const percentage = x / rect.width
      const time = percentage * duration
      seekTo(time)
    }
  }, [duration, seekTo])

  // 片段管理
  const addSegment = useCallback(() => {
    const newSegment: VideoSegment = {
      id: `segment-${Date.now()}`,
      start: trimStart,
      end: trimEnd,
      name: `片段 ${segments.length + 1}`,
      selected: false
    }
    setSegments(prev => [...prev, newSegment])
    toast({
      title: '片段已添加',
      description: `时长: ${(trimEnd - trimStart).toFixed(2)}秒`
    })
  }, [trimStart, trimEnd, segments.length, toast])

  const removeSegment = useCallback((segmentId: string) => {
    setSegments(prev => prev.filter(s => s.id !== segmentId))
    if (selectedSegment === segmentId) {
      setSelectedSegment(null)
    }
  }, [selectedSegment])

  const selectSegment = useCallback((segmentId: string) => {
    const segment = segments.find(s => s.id === segmentId)
    if (segment) {
      setSelectedSegment(segmentId)
      setTrimStart(segment.start)
      setTrimEnd(segment.end)
      seekTo(segment.start)
    }
  }, [segments, seekTo])

  // 导出功能（模拟）
  const exportVideo = useCallback(async () => {
    if (!videoFile) return

    setIsProcessing(true)
    setProcessingProgress(0)

    try {
      // 模拟视频处理过程
      const totalSteps = 100
      for (let i = 0; i <= totalSteps; i++) {
        await new Promise(resolve => setTimeout(resolve, 50))
        setProcessingProgress(i)
      }

      // 创建一个假的导出文件链接（实际应用中需要使用FFmpeg.js或后端服务）
      const blob = new Blob(['模拟视频数据'], { type: 'video/mp4' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `trimmed-video-${Date.now()}.${exportSettings.format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: '导出完成',
        description: `视频已成功导出为 ${exportSettings.format.toUpperCase()} 格式`
      })
    } catch (error) {
      toast({
        title: '导出失败',
        description: '视频处理过程中出现错误',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
      setProcessingProgress(0)
    }
  }, [videoFile, exportSettings, toast])

  // 格式化时间
  const formatTime = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    const milliseconds = Math.floor((seconds % 1) * 100)
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(2, '0')}`
  }, [])

  // 清理资源
  useEffect(() => {
    return () => {
      if (videoUrl) {
        URL.revokeObjectURL(videoUrl)
      }
    }
  }, [videoUrl])

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Video className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">视频剪辑工具</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          精确剪辑视频片段，支持预览、多段剪辑和高质量导出
        </p>
      </div>

      {!videoFile ? (
        // 文件上传界面
        <Card className="border-2 border-dashed">
          <CardContent 
            className="flex flex-col items-center justify-center py-20 cursor-pointer hover:bg-gray-50 transition-colors"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">上传视频文件</p>
            <p className="text-sm text-gray-500 mb-4">拖拽文件到此处或点击选择</p>
            <p className="text-xs text-gray-400">支持的格式: MP4, WebM, MOV, AVI</p>
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧控制面板 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 视频信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  视频信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {videoInfo && (
                  <>
                    <div className="text-sm">
                      <span className="text-gray-600">时长:</span>
                      <span className="ml-2 font-mono">{formatTime(videoInfo.duration)}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">尺寸:</span>
                      <span className="ml-2 font-mono">{videoInfo.width} × {videoInfo.height}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">帧率:</span>
                      <span className="ml-2 font-mono">{videoInfo.fps} fps</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">大小:</span>
                      <span className="ml-2 font-mono">{videoInfo.size}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">编码:</span>
                      <span className="ml-2 font-mono">{videoInfo.codec}</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* 播放控制 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="h-5 w-5" />
                  播放控制
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Button size="sm" onClick={() => seekTo(currentTime - 10)} variant="outline">
                    <SkipBack className="h-4 w-4" />
                  </Button>
                  <Button size="sm" onClick={togglePlayback}>
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button size="sm" onClick={() => seekTo(currentTime + 10)} variant="outline">
                    <SkipForward className="h-4 w-4" />
                  </Button>
                </div>

                <div>
                  <Label className="text-sm">当前时间: {formatTime(currentTime)}</Label>
                  <Slider
                    value={[currentTime]}
                    onValueChange={([value]) => seekTo(value)}
                    max={duration}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div className="flex items-center gap-2">
                  <Button size="sm" onClick={toggleMute} variant="outline">
                    {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                  </Button>
                  <Slider
                    value={[isMuted ? 0 : volume]}
                    onValueChange={([value]) => handleVolumeChange(value)}
                    max={1}
                    step={0.1}
                    className="flex-1"
                  />
                </div>

                <div>
                  <Label className="text-sm">播放速度: {playbackSpeed}x</Label>
                  <Slider
                    value={[playbackSpeed]}
                    onValueChange={([value]) => handlePlaybackSpeedChange(value)}
                    min={0.25}
                    max={2}
                    step={0.25}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 裁剪控制 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Scissors className="h-5 w-5" />
                  裁剪控制
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm">开始时间: {formatTime(trimStart)}</Label>
                  <Slider
                    value={[trimStart]}
                    onValueChange={([value]) => handleTrimChange('start', value)}
                    max={duration}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label className="text-sm">结束时间: {formatTime(trimEnd)}</Label>
                  <Slider
                    value={[trimEnd]}
                    onValueChange={([value]) => handleTrimChange('end', value)}
                    max={duration}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div className="text-sm text-gray-600">
                  裁剪后时长: {formatTime(trimEnd - trimStart)}
                </div>

                <Button onClick={addSegment} className="w-full" variant="outline">
                  <Clock className="h-4 w-4 mr-2" />
                  添加片段
                </Button>
              </CardContent>
            </Card>

            {/* 导出设置 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  导出设置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm">格式</Label>
                  <Select value={exportSettings.format} onValueChange={(value: 'mp4' | 'webm' | 'mov') => setExportSettings(prev => ({ ...prev, format: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mp4">MP4</SelectItem>
                      <SelectItem value="webm">WebM</SelectItem>
                      <SelectItem value="mov">MOV</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm">质量</Label>
                  <Select value={exportSettings.quality} onValueChange={(value: 'high' | 'medium' | 'low') => setExportSettings(prev => ({ ...prev, quality: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="high">高质量</SelectItem>
                      <SelectItem value="medium">中等质量</SelectItem>
                      <SelectItem value="low">低质量</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm">分辨率</Label>
                  <Select value={exportSettings.resolution} onValueChange={(value: any) => setExportSettings(prev => ({ ...prev, resolution: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="original">原始分辨率</SelectItem>
                      <SelectItem value="1080p">1080p</SelectItem>
                      <SelectItem value="720p">720p</SelectItem>
                      <SelectItem value="480p">480p</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <Label className="text-sm">移除音频</Label>
                  <Switch
                    checked={exportSettings.removeAudio}
                    onCheckedChange={(checked) => setExportSettings(prev => ({ ...prev, removeAudio: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label className="text-sm">淡入效果</Label>
                  <Switch
                    checked={exportSettings.fadeIn}
                    onCheckedChange={(checked) => setExportSettings(prev => ({ ...prev, fadeIn: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label className="text-sm">淡出效果</Label>
                  <Switch
                    checked={exportSettings.fadeOut}
                    onCheckedChange={(checked) => setExportSettings(prev => ({ ...prev, fadeOut: checked }))}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧视频预览和时间轴 */}
          <div className="lg:col-span-3 space-y-6">
            {/* 视频预览 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  视频预览
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative bg-black rounded-lg overflow-hidden">
                  <video
                    ref={videoRef}
                    src={videoUrl}
                    className="w-full h-auto"
                    onLoadedMetadata={handleVideoLoad}
                    onTimeUpdate={handleTimeUpdate}
                    controls={false}
                  />
                  <canvas
                    ref={canvasRef}
                    className="absolute inset-0 pointer-events-none"
                    style={{ mixBlendMode: 'overlay' }}
                  />
                </div>
              </CardContent>
            </Card>

            {/* 时间轴 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  时间轴
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  ref={timelineRef}
                  className="relative h-20 bg-gray-100 rounded-lg cursor-pointer overflow-hidden"
                  onClick={handleTimelineClick}
                >
                  {/* 背景时间刻度 */}
                  <div className="absolute inset-0 flex">
                    {Array.from({ length: Math.ceil(duration) }, (_, i) => (
                      <div
                        key={i}
                        className="border-l border-gray-300 flex-1 relative"
                        style={{ left: `${(i / duration) * 100}%` }}
                      >
                        <span className="absolute top-1 left-1 text-xs text-gray-500">
                          {formatTime(i)}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* 裁剪区域 */}
                  <div
                    className="absolute top-0 bottom-0 bg-blue-200 border-2 border-blue-500"
                    style={{
                      left: `${(trimStart / duration) * 100}%`,
                      width: `${((trimEnd - trimStart) / duration) * 100}%`
                    }}
                  >
                    <div className="absolute -left-1 top-0 bottom-0 w-2 bg-blue-500 cursor-w-resize" />
                    <div className="absolute -right-1 top-0 bottom-0 w-2 bg-blue-500 cursor-e-resize" />
                  </div>

                  {/* 当前播放位置 */}
                  <div
                    className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10"
                    style={{ left: `${(currentTime / duration) * 100}%` }}
                  >
                    <div className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 片段列表 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileVideo className="h-5 w-5" />
                  片段列表 ({segments.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {segments.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">
                    还没有添加任何片段，请在上方设置剪辑范围后点击"添加片段"
                  </p>
                ) : (
                  <div className="space-y-2">
                    {segments.map((segment, index) => (
                      <div
                        key={segment.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedSegment === segment.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => selectSegment(segment.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{segment.name}</div>
                            <div className="text-sm text-gray-500">
                              {formatTime(segment.start)} - {formatTime(segment.end)}
                              <span className="ml-2">
                                (时长: {formatTime(segment.end - segment.start)})
                              </span>
                            </div>
                          </div>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={(e) => {
                              e.stopPropagation()
                              removeSegment(segment.id)
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 导出按钮 */}
            <Card>
              <CardContent className="pt-6">
                {isProcessing ? (
                  <div className="text-center">
                    <RotateCw className="h-8 w-8 text-blue-600 animate-spin mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-700 mb-2">正在处理视频...</p>
                    <Progress value={processingProgress} className="max-w-md mx-auto" />
                    <p className="text-sm text-gray-500 mt-2">{processingProgress}%</p>
                  </div>
                ) : (
                  <div className="flex gap-4">
                    <Button onClick={exportVideo} className="flex-1" size="lg">
                      <Download className="h-5 w-5 mr-2" />
                      导出当前片段
                    </Button>
                    <Button 
                      onClick={() => {
                        // 批量导出所有片段的逻辑
                        if (segments.length > 0) {
                          toast({
                            title: '批量导出',
                            description: `将导出 ${segments.length} 个片段`
                          })
                        }
                      }}
                      variant="outline" 
                      className="flex-1" 
                      size="lg"
                      disabled={segments.length === 0}
                    >
                      <Zap className="h-5 w-5 mr-2" />
                      批量导出 ({segments.length})
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  )
}

export default VideoTrimmerTool