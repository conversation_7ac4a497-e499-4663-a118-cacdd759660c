'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Calculator, Square, Circle, Triangle, Hexagon, Box, Cylinder, Info } from 'lucide-react'

interface GeometryResult {
  area?: number
  perimeter?: number
  volume?: number
  surfaceArea?: number
  diagonal?: number
  circumference?: number
  radius?: number
  diameter?: number
  height?: number
  apothem?: number
  sideLength?: number
}

interface Shape2D {
  id: string
  name: string
  icon: React.ComponentType<{ className?: string }>
  fields: Array<{
    name: string
    label: string
    required: boolean
    min?: number
  }>
  calculate: (values: Record<string, number>) => GeometryResult
}

interface Shape3D {
  id: string
  name: string
  icon: React.ComponentType<{ className?: string }>
  fields: Array<{
    name: string
    label: string
    required: boolean
    min?: number
  }>
  calculate: (values: Record<string, number>) => GeometryResult
}

const GeometryCalculatorTool: React.FC = () => {
  const [activeTab, setActiveTab] = useState('2d')
  const [selectedShape2D, setSelectedShape2D] = useState<string>('')
  const [selectedShape3D, setSelectedShape3D] = useState<string>('')
  const [values, setValues] = useState<Record<string, string>>({})
  const [result, setResult] = useState<GeometryResult | null>(null)
  const [error, setError] = useState('')

  const shapes2D: Shape2D[] = [
    {
      id: 'square',
      name: '正方形',
      icon: Square,
      fields: [
        { name: 'side', label: '边长', required: true, min: 0 }
      ],
      calculate: (values) => {
        const side = values.side
        return {
          area: side * side,
          perimeter: 4 * side,
          diagonal: side * Math.sqrt(2)
        }
      }
    },
    {
      id: 'rectangle',
      name: '矩形',
      icon: Square,
      fields: [
        { name: 'width', label: '宽度', required: true, min: 0 },
        { name: 'height', label: '高度', required: true, min: 0 }
      ],
      calculate: (values) => {
        const width = values.width
        const height = values.height
        return {
          area: width * height,
          perimeter: 2 * (width + height),
          diagonal: Math.sqrt(width * width + height * height)
        }
      }
    },
    {
      id: 'circle',
      name: '圆形',
      icon: Circle,
      fields: [
        { name: 'radius', label: '半径', required: true, min: 0 }
      ],
      calculate: (values) => {
        const radius = values.radius
        return {
          area: Math.PI * radius * radius,
          circumference: 2 * Math.PI * radius,
          diameter: 2 * radius
        }
      }
    },
    {
      id: 'triangle',
      name: '三角形',
      icon: Triangle,
      fields: [
        { name: 'base', label: '底边', required: true, min: 0 },
        { name: 'height', label: '高', required: true, min: 0 },
        { name: 'side1', label: '边长1（计算周长）', required: false, min: 0 },
        { name: 'side2', label: '边长2（计算周长）', required: false, min: 0 }
      ],
      calculate: (values) => {
        const base = values.base
        const height = values.height
        const side1 = values.side1 || 0
        const side2 = values.side2 || 0
        
        const result: GeometryResult = {
          area: (base * height) / 2
        }
        
        if (side1 > 0 && side2 > 0) {
          result.perimeter = base + side1 + side2
        }
        
        return result
      }
    },
    {
      id: 'parallelogram',
      name: '平行四边形',
      icon: Square,
      fields: [
        { name: 'base', label: '底边', required: true, min: 0 },
        { name: 'height', label: '高', required: true, min: 0 },
        { name: 'side', label: '邻边（计算周长）', required: false, min: 0 }
      ],
      calculate: (values) => {
        const base = values.base
        const height = values.height
        const side = values.side || 0
        
        const result: GeometryResult = {
          area: base * height
        }
        
        if (side > 0) {
          result.perimeter = 2 * (base + side)
        }
        
        return result
      }
    },
    {
      id: 'trapezoid',
      name: '梯形',
      icon: Square,
      fields: [
        { name: 'base1', label: '上底', required: true, min: 0 },
        { name: 'base2', label: '下底', required: true, min: 0 },
        { name: 'height', label: '高', required: true, min: 0 },
        { name: 'side1', label: '腰长1（计算周长）', required: false, min: 0 },
        { name: 'side2', label: '腰长2（计算周长）', required: false, min: 0 }
      ],
      calculate: (values) => {
        const base1 = values.base1
        const base2 = values.base2
        const height = values.height
        const side1 = values.side1 || 0
        const side2 = values.side2 || 0
        
        const result: GeometryResult = {
          area: ((base1 + base2) * height) / 2
        }
        
        if (side1 > 0 && side2 > 0) {
          result.perimeter = base1 + base2 + side1 + side2
        }
        
        return result
      }
    },
    {
      id: 'rhombus',
      name: '菱形',
      icon: Square,
      fields: [
        { name: 'diagonal1', label: '对角线1', required: true, min: 0 },
        { name: 'diagonal2', label: '对角线2', required: true, min: 0 },
        { name: 'side', label: '边长（计算周长）', required: false, min: 0 }
      ],
      calculate: (values) => {
        const d1 = values.diagonal1
        const d2 = values.diagonal2
        const side = values.side || 0
        
        const result: GeometryResult = {
          area: (d1 * d2) / 2
        }
        
        if (side > 0) {
          result.perimeter = 4 * side
        } else {
          // 根据对角线计算边长
          const calculatedSide = Math.sqrt((d1 * d1 + d2 * d2) / 4)
          result.perimeter = 4 * calculatedSide
          result.sideLength = calculatedSide
        }
        
        return result
      }
    },
    {
      id: 'regular-polygon',
      name: '正多边形',
      icon: Hexagon,
      fields: [
        { name: 'sides', label: '边数', required: true, min: 3 },
        { name: 'side', label: '边长', required: true, min: 0 }
      ],
      calculate: (values) => {
        const n = values.sides
        const s = values.side
        
        const apothem = s / (2 * Math.tan(Math.PI / n))
        const area = (n * s * apothem) / 2
        const perimeter = n * s
        
        return {
          area,
          perimeter,
          apothem
        }
      }
    }
  ]

  const shapes3D: Shape3D[] = [
    {
      id: 'cube',
      name: '正方体',
      icon: Box,
      fields: [
        { name: 'side', label: '边长', required: true, min: 0 }
      ],
      calculate: (values) => {
        const side = values.side
        return {
          volume: side * side * side,
          surfaceArea: 6 * side * side,
          diagonal: side * Math.sqrt(3)
        }
      }
    },
    {
      id: 'cuboid',
      name: '长方体',
      icon: Box,
      fields: [
        { name: 'length', label: '长', required: true, min: 0 },
        { name: 'width', label: '宽', required: true, min: 0 },
        { name: 'height', label: '高', required: true, min: 0 }
      ],
      calculate: (values) => {
        const l = values.length
        const w = values.width
        const h = values.height
        
        return {
          volume: l * w * h,
          surfaceArea: 2 * (l * w + w * h + h * l),
          diagonal: Math.sqrt(l * l + w * w + h * h)
        }
      }
    },
    {
      id: 'sphere',
      name: '球体',
      icon: Circle,
      fields: [
        { name: 'radius', label: '半径', required: true, min: 0 }
      ],
      calculate: (values) => {
        const radius = values.radius
        return {
          volume: (4 / 3) * Math.PI * radius * radius * radius,
          surfaceArea: 4 * Math.PI * radius * radius,
          diameter: 2 * radius
        }
      }
    },
    {
      id: 'cylinder',
      name: '圆柱体',
      icon: Cylinder,
      fields: [
        { name: 'radius', label: '底面半径', required: true, min: 0 },
        { name: 'height', label: '高', required: true, min: 0 }
      ],
      calculate: (values) => {
        const radius = values.radius
        const height = values.height
        
        return {
          volume: Math.PI * radius * radius * height,
          surfaceArea: 2 * Math.PI * radius * (radius + height),
          diameter: 2 * radius
        }
      }
    },
    {
      id: 'cone',
      name: '圆锥体',
      icon: Triangle,
      fields: [
        { name: 'radius', label: '底面半径', required: true, min: 0 },
        { name: 'height', label: '高', required: true, min: 0 }
      ],
      calculate: (values) => {
        const radius = values.radius
        const height = values.height
        const slantHeight = Math.sqrt(radius * radius + height * height)
        
        return {
          volume: (1 / 3) * Math.PI * radius * radius * height,
          surfaceArea: Math.PI * radius * (radius + slantHeight),
          diameter: 2 * radius
        }
      }
    },
    {
      id: 'pyramid',
      name: '四角锥',
      icon: Triangle,
      fields: [
        { name: 'base', label: '底边长', required: true, min: 0 },
        { name: 'height', label: '高', required: true, min: 0 }
      ],
      calculate: (values) => {
        const base = values.base
        const height = values.height
        const slantHeight = Math.sqrt((base / 2) * (base / 2) + height * height)
        
        return {
          volume: (1 / 3) * base * base * height,
          surfaceArea: base * base + 2 * base * slantHeight
        }
      }
    }
  ]

  const getCurrentShape = () => {
    if (activeTab === '2d') {
      return shapes2D.find(s => s.id === selectedShape2D)
    } else {
      return shapes3D.find(s => s.id === selectedShape3D)
    }
  }

  const handleCalculate = () => {
    const shape = getCurrentShape()
    if (!shape) {
      setError('请选择一个图形')
      return
    }

    const numericValues: Record<string, number> = {}
    
    // 验证必填字段
    for (const field of shape.fields) {
      const value = values[field.name]
      if (field.required && (!value || value.trim() === '')) {
        setError(`请输入${field.label}`)
        return
      }
      
      if (value && value.trim() !== '') {
        const numValue = parseFloat(value.trim())
        if (isNaN(numValue)) {
          setError(`${field.label}必须是有效数字`)
          return
        }
        if (field.min !== undefined && numValue < field.min) {
          setError(`${field.label}必须大于${field.min}`)
          return
        }
        numericValues[field.name] = numValue
      }
    }

    try {
      const result = shape.calculate(numericValues)
      setResult(result)
      setError('')
    } catch (err) {
      setError('计算失败，请检查输入值')
    }
  }

  const handleClear = () => {
    setValues({})
    setResult(null)
    setError('')
  }

  const formatNumber = (num: number | undefined): string => {
    if (num === undefined) return ''
    return num.toFixed(4).replace(/\.?0+$/, '')
  }

  const handleShapeChange = (shapeId: string) => {
    if (activeTab === '2d') {
      setSelectedShape2D(shapeId)
    } else {
      setSelectedShape3D(shapeId)
    }
    setValues({})
    setResult(null)
    setError('')
  }

  const handleInputChange = (fieldName: string, value: string) => {
    setValues(prev => ({
      ...prev,
      [fieldName]: value
    }))
  }

  useEffect(() => {
    // 清空状态当切换标签时
    setValues({})
    setResult(null)
    setError('')
  }, [activeTab])

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="w-5 h-5" />
            几何计算器
          </CardTitle>
          <CardDescription>
            计算各种几何图形的面积、周长、体积和表面积
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="2d">平面图形</TabsTrigger>
              <TabsTrigger value="3d">立体图形</TabsTrigger>
            </TabsList>
            
            <TabsContent value="2d" className="space-y-4">
              <div className="space-y-2">
                <Label>选择图形</Label>
                <Select value={selectedShape2D} onValueChange={handleShapeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择一个平面图形" />
                  </SelectTrigger>
                  <SelectContent>
                    {shapes2D.map((shape) => (
                      <SelectItem key={shape.id} value={shape.id}>
                        <div className="flex items-center gap-2">
                          <shape.icon className="w-4 h-4" />
                          {shape.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
            
            <TabsContent value="3d" className="space-y-4">
              <div className="space-y-2">
                <Label>选择图形</Label>
                <Select value={selectedShape3D} onValueChange={handleShapeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择一个立体图形" />
                  </SelectTrigger>
                  <SelectContent>
                    {shapes3D.map((shape) => (
                      <SelectItem key={shape.id} value={shape.id}>
                        <div className="flex items-center gap-2">
                          <shape.icon className="w-4 h-4" />
                          {shape.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
          </Tabs>

          {getCurrentShape() && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {getCurrentShape()!.fields.map((field) => (
                  <div key={field.name} className="space-y-2">
                    <Label>
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>
                    <Input
                      type="number"
                      min={field.min}
                      step="0.01"
                      value={values[field.name] || ''}
                      onChange={(e) => handleInputChange(field.name, e.target.value)}
                      placeholder={`请输入${field.label}`}
                    />
                  </div>
                ))}
              </div>
              
              <div className="flex gap-3">
                <Button onClick={handleCalculate} className="flex-1">
                  <Calculator className="w-4 h-4 mr-2" />
                  计算
                </Button>
                <Button variant="outline" onClick={handleClear}>
                  清空
                </Button>
              </div>
            </div>
          )}

          {error && (
            <Alert className="border-red-200 bg-red-50">
              <Info className="h-4 w-4" />
              <AlertDescription className="text-red-700">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {result && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">计算结果</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {result.area !== undefined && (
                  <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Square className="w-4 h-4 text-blue-500" />
                      <span className="text-sm font-medium">面积</span>
                    </div>
                    <div className="text-xl font-bold">{formatNumber(result.area)}</div>
                    <div className="text-xs text-gray-500">平方单位</div>
                  </Card>
                )}
                
                {result.perimeter !== undefined && (
                  <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Square className="w-4 h-4 text-green-500" />
                      <span className="text-sm font-medium">周长</span>
                    </div>
                    <div className="text-xl font-bold">{formatNumber(result.perimeter)}</div>
                    <div className="text-xs text-gray-500">长度单位</div>
                  </Card>
                )}
                
                {result.circumference !== undefined && (
                  <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Circle className="w-4 h-4 text-green-500" />
                      <span className="text-sm font-medium">周长</span>
                    </div>
                    <div className="text-xl font-bold">{formatNumber(result.circumference)}</div>
                    <div className="text-xs text-gray-500">长度单位</div>
                  </Card>
                )}
                
                {result.volume !== undefined && (
                  <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Box className="w-4 h-4 text-purple-500" />
                      <span className="text-sm font-medium">体积</span>
                    </div>
                    <div className="text-xl font-bold">{formatNumber(result.volume)}</div>
                    <div className="text-xs text-gray-500">立方单位</div>
                  </Card>
                )}
                
                {result.surfaceArea !== undefined && (
                  <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Square className="w-4 h-4 text-orange-500" />
                      <span className="text-sm font-medium">表面积</span>
                    </div>
                    <div className="text-xl font-bold">{formatNumber(result.surfaceArea)}</div>
                    <div className="text-xs text-gray-500">平方单位</div>
                  </Card>
                )}
                
                {result.diagonal !== undefined && (
                  <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Square className="w-4 h-4 text-red-500" />
                      <span className="text-sm font-medium">对角线</span>
                    </div>
                    <div className="text-xl font-bold">{formatNumber(result.diagonal)}</div>
                    <div className="text-xs text-gray-500">长度单位</div>
                  </Card>
                )}
                
                {result.diameter !== undefined && (
                  <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Circle className="w-4 h-4 text-indigo-500" />
                      <span className="text-sm font-medium">直径</span>
                    </div>
                    <div className="text-xl font-bold">{formatNumber(result.diameter)}</div>
                    <div className="text-xs text-gray-500">长度单位</div>
                  </Card>
                )}
                
                {result.apothem !== undefined && (
                  <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Hexagon className="w-4 h-4 text-teal-500" />
                      <span className="text-sm font-medium">边心距</span>
                    </div>
                    <div className="text-xl font-bold">{formatNumber(result.apothem)}</div>
                    <div className="text-xs text-gray-500">长度单位</div>
                  </Card>
                )}
                
                {result.sideLength !== undefined && (
                  <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Square className="w-4 h-4 text-pink-500" />
                      <span className="text-sm font-medium">边长</span>
                    </div>
                    <div className="text-xl font-bold">{formatNumber(result.sideLength)}</div>
                    <div className="text-xs text-gray-500">长度单位</div>
                  </Card>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default GeometryCalculatorTool
