'use client'

import React, { useState, useEffect } from 'react'
import { Copy, Check, Palette, Square, Type, Layers, Sparkles, Download, RotateCcw } from 'lucide-react'

interface GeneratorState {
  // 盒模型
  width: string
  height: string
  padding: string
  margin: string
  
  // 边框
  border: string
  borderRadius: string
  
  // 背景
  backgroundColor: string
  backgroundImage: string
  
  // 文本
  color: string
  fontSize: string
  fontWeight: string
  fontFamily: string
  lineHeight: string
  textAlign: string
  
  // 布局
  display: string
  position: string
  
  // 效果
  opacity: string
  boxShadow: string
  transform: string
  transition: string
}

const defaultState: GeneratorState = {
  width: '',
  height: '',
  padding: '',
  margin: '',
  border: '',
  borderRadius: '',
  backgroundColor: '',
  backgroundImage: '',
  color: '',
  fontSize: '',
  fontWeight: '',
  fontFamily: '',
  lineHeight: '',
  textAlign: '',
  display: '',
  position: '',
  opacity: '',
  boxShadow: '',
  transform: '',
  transition: ''
}

export default function CssGeneratorTool() {
  const [state, setState] = useState<GeneratorState>(defaultState)
  const [activeTab, setActiveTab] = useState<'box' | 'background' | 'text' | 'layout' | 'effects'>('box')
  const [cssOutput, setCssOutput] = useState('')
  const [copied, setCopied] = useState(false)
  const [previewText, setPreviewText] = useState('预览文本 Preview Text')

  // 生成 CSS
  useEffect(() => {
    const css: string[] = []
    
    // 盒模型
    if (state.width) css.push(`width: ${state.width};`)
    if (state.height) css.push(`height: ${state.height};`)
    if (state.padding) css.push(`padding: ${state.padding};`)
    if (state.margin) css.push(`margin: ${state.margin};`)
    
    // 边框
    if (state.border) css.push(`border: ${state.border};`)
    if (state.borderRadius) css.push(`border-radius: ${state.borderRadius};`)
    
    // 背景
    if (state.backgroundColor) css.push(`background-color: ${state.backgroundColor};`)
    if (state.backgroundImage) css.push(`background-image: ${state.backgroundImage};`)
    
    // 文本
    if (state.color) css.push(`color: ${state.color};`)
    if (state.fontSize) css.push(`font-size: ${state.fontSize};`)
    if (state.fontWeight) css.push(`font-weight: ${state.fontWeight};`)
    if (state.fontFamily) css.push(`font-family: ${state.fontFamily};`)
    if (state.lineHeight) css.push(`line-height: ${state.lineHeight};`)
    if (state.textAlign) css.push(`text-align: ${state.textAlign};`)
    
    // 布局
    if (state.display) css.push(`display: ${state.display};`)
    if (state.position) css.push(`position: ${state.position};`)
    
    // 效果
    if (state.opacity) css.push(`opacity: ${state.opacity};`)
    if (state.boxShadow) css.push(`box-shadow: ${state.boxShadow};`)
    if (state.transform) css.push(`transform: ${state.transform};`)
    if (state.transition) css.push(`transition: ${state.transition};`)
    
    setCssOutput(css.join('\n'))
  }, [state])

  // 更新状态
  const updateState = (key: keyof GeneratorState, value: string) => {
    setState(prev => ({ ...prev, [key]: value }))
  }

  // 复制 CSS
  const copyCss = () => {
    navigator.clipboard.writeText(cssOutput).then(() => {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    })
  }

  // 下载 CSS
  const downloadCss = () => {
    const blob = new Blob([`.generated-style {\n${cssOutput.split('\n').map(line => '  ' + line).join('\n')}\n}`], { type: 'text/css' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'style.css'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 重置
  const reset = () => {
    setState(defaultState)
    setPreviewText('预览文本 Preview Text')
  }

  // 获取预览样式
  const getPreviewStyle = (): React.CSSProperties => {
    const style: React.CSSProperties = {}
    
    cssOutput.split('\n').forEach(line => {
      const match = line.match(/^(.+?):\s*(.+?);$/)
      if (match) {
        const [, prop, value] = match
        const camelProp = prop.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
        ;(style as any)[camelProp] = value
      }
    })
    
    return style
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">CSS 生成器</h2>
        <p className="text-gray-600">
          可视化生成 CSS 样式代码，支持盒模型、背景、文本、布局、效果等属性
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* 左侧：控制面板 */}
        <div>
          <div className="bg-white rounded-lg border border-gray-300">
            {/* 标签页 */}
            <div className="flex border-b overflow-x-auto">
              <button
                onClick={() => setActiveTab('box')}
                className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                  activeTab === 'box'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <Square className="w-4 h-4 inline mr-1" />
                盒模型
              </button>
              <button
                onClick={() => setActiveTab('background')}
                className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                  activeTab === 'background'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <Palette className="w-4 h-4 inline mr-1" />
                背景
              </button>
              <button
                onClick={() => setActiveTab('text')}
                className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                  activeTab === 'text'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <Type className="w-4 h-4 inline mr-1" />
                文本
              </button>
              <button
                onClick={() => setActiveTab('layout')}
                className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                  activeTab === 'layout'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <Layers className="w-4 h-4 inline mr-1" />
                布局
              </button>
              <button
                onClick={() => setActiveTab('effects')}
                className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                  activeTab === 'effects'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }`}
              >
                <Sparkles className="w-4 h-4 inline mr-1" />
                效果
              </button>
            </div>

            {/* 控制内容 */}
            <div className="p-6 space-y-4 max-h-[600px] overflow-y-auto">
              {activeTab === 'box' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">宽度</label>
                    <input
                      type="text"
                      value={state.width}
                      onChange={(e) => updateState('width', e.target.value)}
                      placeholder="如: 200px, 50%, auto"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">高度</label>
                    <input
                      type="text"
                      value={state.height}
                      onChange={(e) => updateState('height', e.target.value)}
                      placeholder="如: 100px, 50vh"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">内边距 (Padding)</label>
                    <input
                      type="text"
                      value={state.padding}
                      onChange={(e) => updateState('padding', e.target.value)}
                      placeholder="如: 10px, 10px 20px"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">外边距 (Margin)</label>
                    <input
                      type="text"
                      value={state.margin}
                      onChange={(e) => updateState('margin', e.target.value)}
                      placeholder="如: 10px, 0 auto"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">边框</label>
                    <input
                      type="text"
                      value={state.border}
                      onChange={(e) => updateState('border', e.target.value)}
                      placeholder="如: 1px solid #000"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">圆角</label>
                    <input
                      type="text"
                      value={state.borderRadius}
                      onChange={(e) => updateState('borderRadius', e.target.value)}
                      placeholder="如: 5px, 50%"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                </>
              )}

              {activeTab === 'background' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">背景颜色</label>
                    <input
                      type="text"
                      value={state.backgroundColor}
                      onChange={(e) => updateState('backgroundColor', e.target.value)}
                      placeholder="如: #ff0000, rgb(255,0,0), red"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">背景图片</label>
                    <input
                      type="text"
                      value={state.backgroundImage}
                      onChange={(e) => updateState('backgroundImage', e.target.value)}
                      placeholder="如: url('image.jpg'), linear-gradient(...)"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                </>
              )}

              {activeTab === 'text' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">文字颜色</label>
                    <input
                      type="text"
                      value={state.color}
                      onChange={(e) => updateState('color', e.target.value)}
                      placeholder="如: #000000, rgb(0,0,0), black"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">字体大小</label>
                    <input
                      type="text"
                      value={state.fontSize}
                      onChange={(e) => updateState('fontSize', e.target.value)}
                      placeholder="如: 16px, 1.2em, 1rem"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">字重</label>
                    <input
                      type="text"
                      value={state.fontWeight}
                      onChange={(e) => updateState('fontWeight', e.target.value)}
                      placeholder="如: bold, 600, normal"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">字体</label>
                    <input
                      type="text"
                      value={state.fontFamily}
                      onChange={(e) => updateState('fontFamily', e.target.value)}
                      placeholder="如: Arial, 'Helvetica Neue', sans-serif"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">行高</label>
                    <input
                      type="text"
                      value={state.lineHeight}
                      onChange={(e) => updateState('lineHeight', e.target.value)}
                      placeholder="如: 1.5, 24px"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">文本对齐</label>
                    <input
                      type="text"
                      value={state.textAlign}
                      onChange={(e) => updateState('textAlign', e.target.value)}
                      placeholder="如: left, center, right, justify"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                </>
              )}

              {activeTab === 'layout' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">显示类型</label>
                    <input
                      type="text"
                      value={state.display}
                      onChange={(e) => updateState('display', e.target.value)}
                      placeholder="如: block, flex, grid, inline"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">定位</label>
                    <input
                      type="text"
                      value={state.position}
                      onChange={(e) => updateState('position', e.target.value)}
                      placeholder="如: relative, absolute, fixed"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                </>
              )}

              {activeTab === 'effects' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">透明度</label>
                    <input
                      type="text"
                      value={state.opacity}
                      onChange={(e) => updateState('opacity', e.target.value)}
                      placeholder="如: 0.5, 1"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">阴影</label>
                    <input
                      type="text"
                      value={state.boxShadow}
                      onChange={(e) => updateState('boxShadow', e.target.value)}
                      placeholder="如: 0 2px 4px rgba(0,0,0,0.1)"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">变换</label>
                    <input
                      type="text"
                      value={state.transform}
                      onChange={(e) => updateState('transform', e.target.value)}
                      placeholder="如: rotate(45deg), scale(1.2)"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">过渡</label>
                    <input
                      type="text"
                      value={state.transition}
                      onChange={(e) => updateState('transition', e.target.value)}
                      placeholder="如: all 0.3s ease"
                      className="w-full px-3 py-2 border rounded-md"
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* 右侧：预览和输出 */}
        <div className="space-y-6">
          {/* 预览区域 */}
          <div className="bg-white rounded-lg border border-gray-300 p-6">
            <h3 className="text-lg font-semibold mb-4">预览</h3>
            <div className="bg-gray-50 p-8 rounded-lg min-h-[200px] flex items-center justify-center">
              <div style={getPreviewStyle()} className="transition-all duration-300">
                <input
                  type="text"
                  value={previewText}
                  onChange={(e) => setPreviewText(e.target.value)}
                  className="bg-transparent border-none outline-none text-center w-full"
                  placeholder="输入预览文本"
                />
              </div>
            </div>
          </div>

          {/* CSS 输出 */}
          <div className="bg-white rounded-lg border border-gray-300 p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">CSS 代码</h3>
              <div className="flex gap-2">
                <button
                  onClick={copyCss}
                  className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  {copied ? '已复制' : '复制'}
                </button>
                <button
                  onClick={downloadCss}
                  className="flex items-center gap-2 px-3 py-1.5 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  下载
                </button>
                <button
                  onClick={reset}
                  className="flex items-center gap-2 px-3 py-1.5 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                >
                  <RotateCcw className="w-4 h-4" />
                  重置
                </button>
              </div>
            </div>
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
              <code>{cssOutput || '/* 在左侧设置样式属性 */'}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}
