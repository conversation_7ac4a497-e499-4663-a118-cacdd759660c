'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, Lock, AlertTriangle, CheckCircle, XCircle, Info, Globe, Calendar, FileText, Key, Server, Copy } from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface CertificateInfo {
  subject: {
    commonName: string
    organization?: string
    organizationalUnit?: string
    country?: string
    state?: string
    locality?: string
  }
  issuer: {
    commonName: string
    organization?: string
    country?: string
  }
  serialNumber: string
  notBefore: string
  notAfter: string
  signatureAlgorithm: string
  publicKeyAlgorithm: string
  publicKeySize: number
  fingerprint: {
    sha1: string
    sha256: string
  }
  extensions: {
    subjectAltName?: string[]
    keyUsage?: string[]
    extendedKeyUsage?: string[]
    basicConstraints?: string
    authorityKeyIdentifier?: string
    subjectKeyIdentifier?: string
  }
  version: number
  isValid: boolean
  daysUntilExpiry: number
  validationLevel: 'DV' | 'OV' | 'EV' | 'Unknown'
  chainLength: number
}

const SSLCertificateAnalyzerTool: React.FC = () => {
  const [input, setInput] = useState('')
  const [inputType, setInputType] = useState<'domain' | 'pem'>('domain')
  const [certificate, setCertificate] = useState<CertificateInfo | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [rawCertificate, setRawCertificate] = useState<string>('')

  // 验证域名格式
  const validateDomain = (domain: string): boolean => {
    const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?$/i
    return domainRegex.test(domain)
  }

  // 验证 PEM 格式
  const validatePEM = (pem: string): boolean => {
    const pemRegex = /^-----BEGIN CERTIFICATE-----[\s\S]+-----END CERTIFICATE-----$/
    return pemRegex.test(pem.trim())
  }

  // 解析 PEM 格式证书
  const parsePEMCertificate = (pemData: string): CertificateInfo | null => {
    try {
      // 模拟证书解析（实际应用中需要使用 crypto 库）
      const mockCert: CertificateInfo = {
        subject: {
          commonName: 'example.com',
          organization: 'Example Inc',
          organizationalUnit: 'IT Department',
          country: 'US',
          state: 'California',
          locality: 'San Francisco'
        },
        issuer: {
          commonName: 'DigiCert SHA2 Secure Server CA',
          organization: 'DigiCert Inc',
          country: 'US'
        },
        serialNumber: '0x1234567890abcdef',
        notBefore: '2024-01-01T00:00:00Z',
        notAfter: '2025-01-01T00:00:00Z',
        signatureAlgorithm: 'SHA256WithRSAEncryption',
        publicKeyAlgorithm: 'RSA',
        publicKeySize: 2048,
        fingerprint: {
          sha1: '12:34:56:78:90:ab:cd:ef:12:34:56:78:90:ab:cd:ef:12:34:56:78',
          sha256: '12:34:56:78:90:ab:cd:ef:12:34:56:78:90:ab:cd:ef:12:34:56:78:90:ab:cd:ef:12:34:56:78'
        },
        extensions: {
          subjectAltName: ['example.com', 'www.example.com'],
          keyUsage: ['Digital Signature', 'Key Encipherment'],
          extendedKeyUsage: ['Server Authentication', 'Client Authentication'],
          basicConstraints: 'CA:FALSE',
          authorityKeyIdentifier: 'keyid:12:34:56:78:90:ab:cd:ef',
          subjectKeyIdentifier: '12:34:56:78:90:ab:cd:ef'
        },
        version: 3,
        isValid: true,
        daysUntilExpiry: 365,
        validationLevel: 'DV',
        chainLength: 3
      }

      return mockCert
    } catch (error) {
      return null
    }
  }

  // 获取域名证书信息
  const fetchDomainCertificate = async (domain: string): Promise<CertificateInfo | null> => {
    // 模拟 API 调用获取证书信息
    return new Promise((resolve) => {
      setTimeout(() => {
        const now = new Date()
        const expiry = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000)
        const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

        const mockCert: CertificateInfo = {
          subject: {
            commonName: domain,
            organization: 'Example Organization',
            country: 'US'
          },
          issuer: {
            commonName: 'Let\'s Encrypt Authority X3',
            organization: 'Let\'s Encrypt',
            country: 'US'
          },
          serialNumber: '0x' + Math.random().toString(16).substr(2, 16),
          notBefore: now.toISOString(),
          notAfter: expiry.toISOString(),
          signatureAlgorithm: 'SHA256WithRSAEncryption',
          publicKeyAlgorithm: 'RSA',
          publicKeySize: 2048,
          fingerprint: {
            sha1: Array.from({ length: 20 }, () => Math.floor(Math.random() * 256).toString(16).padStart(2, '0')).join(':'),
            sha256: Array.from({ length: 32 }, () => Math.floor(Math.random() * 256).toString(16).padStart(2, '0')).join(':')
          },
          extensions: {
            subjectAltName: [domain, `www.${domain}`],
            keyUsage: ['Digital Signature', 'Key Encipherment'],
            extendedKeyUsage: ['Server Authentication'],
            basicConstraints: 'CA:FALSE'
          },
          version: 3,
          isValid: daysUntilExpiry > 0,
          daysUntilExpiry,
          validationLevel: 'DV',
          chainLength: 3
        }

        resolve(mockCert)
      }, 1000)
    })
  }

  // 分析证书
  const analyzeCertificate = async () => {
    if (!input.trim()) {
      setError('请输入域名或 PEM 证书')
      return
    }

    setLoading(true)
    setError(null)
    setCertificate(null)

    try {
      let cert: CertificateInfo | null = null

      if (inputType === 'domain') {
        if (!validateDomain(input.trim())) {
          setError('请输入有效的域名')
          setLoading(false)
          return
        }
        cert = await fetchDomainCertificate(input.trim())
      } else {
        if (!validatePEM(input)) {
          setError('请输入有效的 PEM 格式证书')
          setLoading(false)
          return
        }
        cert = parsePEMCertificate(input)
        setRawCertificate(input)
      }

      if (!cert) {
        setError('无法解析证书信息')
        setLoading(false)
        return
      }

      setCertificate(cert)
    } catch (err) {
      setError('证书分析失败')
    } finally {
      setLoading(false)
    }
  }

  // 复制文本
  const copyText = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 获取证书状态
  const getCertificateStatus = () => {
    if (!certificate) return null

    if (!certificate.isValid) {
      return { status: 'expired', color: 'text-red-500', icon: XCircle, text: '已过期' }
    }

    if (certificate.daysUntilExpiry <= 7) {
      return { status: 'critical', color: 'text-red-500', icon: AlertTriangle, text: '即将过期' }
    }

    if (certificate.daysUntilExpiry <= 30) {
      return { status: 'warning', color: 'text-yellow-500', icon: AlertTriangle, text: '即将过期' }
    }

    return { status: 'valid', color: 'text-green-500', icon: CheckCircle, text: '有效' }
  }

  // 获取验证级别说明
  const getValidationLevelDescription = (level: string) => {
    switch (level) {
      case 'DV': return 'Domain Validation - 域名验证'
      case 'OV': return 'Organization Validation - 组织验证'
      case 'EV': return 'Extended Validation - 扩展验证'
      default: return '未知验证级别'
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Shield className="w-8 h-8" />
          SSL/TLS 证书解析器
        </h1>
        <p className="text-muted-foreground">
          分析 SSL/TLS 证书信息，检查证书有效性和安全性
        </p>
      </div>

      {/* 输入区域 */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex gap-4">
            <Button
              variant={inputType === 'domain' ? 'default' : 'outline'}
              onClick={() => setInputType('domain')}
              className="flex-1"
            >
              <Globe className="w-4 h-4 mr-2" />
              域名检查
            </Button>
            <Button
              variant={inputType === 'pem' ? 'default' : 'outline'}
              onClick={() => setInputType('pem')}
              className="flex-1"
            >
              <FileText className="w-4 h-4 mr-2" />
              PEM 证书
            </Button>
          </div>

          {inputType === 'domain' ? (
            <div className="space-y-2">
              <Label htmlFor="domain">域名</Label>
              <Input
                id="domain"
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="例如：example.com"
              />
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="pem">PEM 证书</Label>
              <Textarea
                id="pem"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="-----BEGIN CERTIFICATE-----&#10;...&#10;-----END CERTIFICATE-----"
                className="min-h-[200px] font-mono text-sm"
              />
            </div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button onClick={analyzeCertificate} disabled={loading} className="w-full">
            {loading ? '分析中...' : '分析证书'}
          </Button>
        </div>
      </Card>

      {/* 证书信息 */}
      {certificate && (
        <Card className="p-6">
          <div className="space-y-6">
            {/* 证书状态 */}
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">证书状态</h3>
              {(() => {
                const status = getCertificateStatus()
                if (!status) return null
                const { color, icon: Icon, text } = status
                return (
                  <div className={`flex items-center gap-2 ${color}`}>
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{text}</span>
                  </div>
                )
              })()}
            </div>

            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">概览</TabsTrigger>
                <TabsTrigger value="details">详细信息</TabsTrigger>
                <TabsTrigger value="security">安全性</TabsTrigger>
                <TabsTrigger value="extensions">扩展</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <InfoCard
                    title="证书主体"
                    icon={Server}
                    items={[
                      { label: '通用名称', value: certificate.subject.commonName },
                      { label: '组织', value: certificate.subject.organization || '未指定' },
                      { label: '国家', value: certificate.subject.country || '未指定' }
                    ]}
                  />

                  <InfoCard
                    title="证书颁发者"
                    icon={Shield}
                    items={[
                      { label: '颁发机构', value: certificate.issuer.commonName },
                      { label: '组织', value: certificate.issuer.organization || '未指定' },
                      { label: '国家', value: certificate.issuer.country || '未指定' }
                    ]}
                  />

                  <InfoCard
                    title="有效期"
                    icon={Calendar}
                    items={[
                      { label: '生效时间', value: formatDate(certificate.notBefore) },
                      { label: '过期时间', value: formatDate(certificate.notAfter) },
                      { label: '剩余天数', value: `${certificate.daysUntilExpiry} 天` }
                    ]}
                  />

                  <InfoCard
                    title="加密信息"
                    icon={Key}
                    items={[
                      { label: '签名算法', value: certificate.signatureAlgorithm },
                      { label: '公钥算法', value: certificate.publicKeyAlgorithm },
                      { label: '公钥长度', value: `${certificate.publicKeySize} 位` }
                    ]}
                  />
                </div>
              </TabsContent>

              <TabsContent value="details" className="space-y-4">
                <div className="space-y-4">
                  <DetailItem
                    label="序列号"
                    value={certificate.serialNumber}
                    copyable
                  />
                  <DetailItem
                    label="版本"
                    value={`v${certificate.version}`}
                  />
                  <DetailItem
                    label="SHA-1 指纹"
                    value={certificate.fingerprint.sha1}
                    copyable
                  />
                  <DetailItem
                    label="SHA-256 指纹"
                    value={certificate.fingerprint.sha256}
                    copyable
                  />
                  <DetailItem
                    label="验证级别"
                    value={getValidationLevelDescription(certificate.validationLevel)}
                  />
                  <DetailItem
                    label="证书链长度"
                    value={`${certificate.chainLength} 级`}
                  />
                </div>
              </TabsContent>

              <TabsContent value="security" className="space-y-4">
                <SecurityAnalysis certificate={certificate} />
              </TabsContent>

              <TabsContent value="extensions" className="space-y-4">
                <div className="space-y-4">
                  {certificate.extensions.subjectAltName && (
                    <div className="space-y-2">
                      <Label>使用者备用名称 (SAN)</Label>
                      <div className="flex flex-wrap gap-2">
                        {certificate.extensions.subjectAltName.map((name, index) => (
                          <Badge key={index} variant="secondary">{name}</Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {certificate.extensions.keyUsage && (
                    <div className="space-y-2">
                      <Label>密钥用途</Label>
                      <div className="flex flex-wrap gap-2">
                        {certificate.extensions.keyUsage.map((usage, index) => (
                          <Badge key={index} variant="outline">{usage}</Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {certificate.extensions.extendedKeyUsage && (
                    <div className="space-y-2">
                      <Label>扩展密钥用途</Label>
                      <div className="flex flex-wrap gap-2">
                        {certificate.extensions.extendedKeyUsage.map((usage, index) => (
                          <Badge key={index} variant="outline">{usage}</Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {certificate.extensions.basicConstraints && (
                    <DetailItem
                      label="基本约束"
                      value={certificate.extensions.basicConstraints}
                    />
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </Card>
      )}

      {/* 使用说明 */}
      <Card className="p-6 border-blue-200 bg-blue-50 dark:bg-blue-900/20">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-blue-800 dark:text-blue-200">使用说明：</p>
            <ul className="mt-1 text-blue-700 dark:text-blue-300 space-y-1">
              <li>• 域名检查：输入域名自动获取 SSL 证书信息</li>
              <li>• PEM 证书：直接粘贴 PEM 格式的证书内容</li>
              <li>• 支持检查证书有效性、过期时间和安全性</li>
              <li>• 显示证书链信息和扩展字段</li>
              <li>• 所有操作在浏览器本地完成，确保隐私安全</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}

// 信息卡片组件
const InfoCard: React.FC<{
  title: string
  icon: React.ComponentType<{ className?: string }>
  items: Array<{ label: string; value: string }>
}> = ({ title, icon: Icon, items }) => (
  <Card className="p-4">
    <div className="flex items-center gap-2 mb-3">
      <Icon className="w-5 h-5 text-blue-600" />
      <h4 className="font-semibold">{title}</h4>
    </div>
    <div className="space-y-2">
      {items.map((item, index) => (
        <div key={index} className="flex justify-between text-sm">
          <span className="text-muted-foreground">{item.label}:</span>
          <span className="font-medium">{item.value}</span>
        </div>
      ))}
    </div>
  </Card>
)

// 详细信息项组件
const DetailItem: React.FC<{
  label: string
  value: string
  copyable?: boolean
}> = ({ label, value, copyable = false }) => (
  <div className="flex justify-between items-start gap-4 p-3 border rounded-lg">
    <div className="flex-1">
      <div className="font-medium text-sm">{label}</div>
      <div className="text-sm text-muted-foreground mt-1 font-mono break-all">{value}</div>
    </div>
    {copyable && (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => navigator.clipboard.writeText(value)}
        className="shrink-0"
      >
        <Copy className="w-4 h-4" />
      </Button>
    )}
  </div>
)

// 安全性分析组件
const SecurityAnalysis: React.FC<{ certificate: CertificateInfo }> = ({ certificate }) => {
  const getSecurityScore = () => {
    let score = 0
    let total = 0

    // 公钥长度
    total += 30
    if (certificate.publicKeySize >= 2048) score += 30
    else if (certificate.publicKeySize >= 1024) score += 15

    // 签名算法
    total += 25
    if (certificate.signatureAlgorithm.includes('SHA256')) score += 25
    else if (certificate.signatureAlgorithm.includes('SHA1')) score += 10

    // 有效期
    total += 20
    if (certificate.daysUntilExpiry > 30) score += 20
    else if (certificate.daysUntilExpiry > 7) score += 10

    // 验证级别
    total += 15
    if (certificate.validationLevel === 'EV') score += 15
    else if (certificate.validationLevel === 'OV') score += 10
    else if (certificate.validationLevel === 'DV') score += 5

    // 证书链
    total += 10
    if (certificate.chainLength >= 3) score += 10
    else if (certificate.chainLength >= 2) score += 5

    return Math.round((score / total) * 100)
  }

  const securityScore = getSecurityScore()

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-500'
    if (score >= 70) return 'text-yellow-500'
    if (score >= 50) return 'text-orange-500'
    return 'text-red-500'
  }

  const getScoreLabel = (score: number) => {
    if (score >= 90) return '优秀'
    if (score >= 70) return '良好'
    if (score >= 50) return '一般'
    return '较差'
  }

  return (
    <div className="space-y-4">
      <div className="text-center">
        <div className={`text-3xl font-bold ${getScoreColor(securityScore)}`}>
          {securityScore}
        </div>
        <div className="text-sm text-muted-foreground">
          安全评分 - {getScoreLabel(securityScore)}
        </div>
      </div>

      <div className="space-y-3">
        <SecurityItem
          label="公钥长度"
          value={`${certificate.publicKeySize} 位`}
          status={certificate.publicKeySize >= 2048 ? 'good' : certificate.publicKeySize >= 1024 ? 'warning' : 'bad'}
          description={certificate.publicKeySize >= 2048 ? '符合现代安全标准' : '建议使用 2048 位或更高'}
        />

        <SecurityItem
          label="签名算法"
          value={certificate.signatureAlgorithm}
          status={certificate.signatureAlgorithm.includes('SHA256') ? 'good' : certificate.signatureAlgorithm.includes('SHA1') ? 'warning' : 'bad'}
          description={certificate.signatureAlgorithm.includes('SHA256') ? '安全的签名算法' : '建议使用 SHA-256 或更高'}
        />

        <SecurityItem
          label="证书有效期"
          value={`${certificate.daysUntilExpiry} 天`}
          status={certificate.daysUntilExpiry > 30 ? 'good' : certificate.daysUntilExpiry > 7 ? 'warning' : 'bad'}
          description={certificate.daysUntilExpiry > 30 ? '证书有效期充足' : '建议尽快续期证书'}
        />

        <SecurityItem
          label="验证级别"
          value={getValidationLevelDescription(certificate.validationLevel)}
          status={certificate.validationLevel === 'EV' ? 'good' : certificate.validationLevel === 'OV' ? 'warning' : 'info'}
          description={certificate.validationLevel === 'EV' ? '最高级别验证' : certificate.validationLevel === 'OV' ? '组织验证' : '基础域名验证'}
        />
      </div>
    </div>
  )
}

// 安全项组件
const SecurityItem: React.FC<{
  label: string
  value: string
  status: 'good' | 'warning' | 'bad' | 'info'
  description: string
}> = ({ label, value, status, description }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'good': return 'text-green-500'
      case 'warning': return 'text-yellow-500'
      case 'bad': return 'text-red-500'
      default: return 'text-blue-500'
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'good': return CheckCircle
      case 'warning': return AlertTriangle
      case 'bad': return XCircle
      default: return Info
    }
  }

  const StatusIcon = getStatusIcon()

  return (
    <div className="flex items-start gap-3 p-3 border rounded-lg">
      <StatusIcon className={`w-5 h-5 mt-0.5 ${getStatusColor()}`} />
      <div className="flex-1">
        <div className="flex justify-between items-start">
          <span className="font-medium">{label}</span>
          <span className="text-sm text-muted-foreground">{value}</span>
        </div>
        <div className="text-sm text-muted-foreground mt-1">{description}</div>
      </div>
    </div>
  )
}

const getValidationLevelDescription = (level: string) => {
  switch (level) {
    case 'DV': return 'Domain Validation - 域名验证'
    case 'OV': return 'Organization Validation - 组织验证'
    case 'EV': return 'Extended Validation - 扩展验证'
    default: return '未知验证级别'
  }
}

export default SSLCertificateAnalyzerTool