'use client'

import { useState, useEffect } from 'react'
import { Plus, Trash2, Check, Clock, Calendar, Filter, Download, Upload, Archive, AlertCircle } from 'lucide-react'

interface Todo {
  id: string
  text: string
  completed: boolean
  priority: 'low' | 'medium' | 'high'
  dueDate?: string
  createdAt: string
  completedAt?: string
  category?: string
}

interface TodoStats {
  total: number
  completed: number
  pending: number
  overdue: number
}

export default function TodoListTool() {
  const [todos, setTodos] = useState<Todo[]>([])
  const [newTodo, setNewTodo] = useState('')
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium')
  const [dueDate, setDueDate] = useState('')
  const [category, setCategory] = useState('')
  const [filter, setFilter] = useState<'all' | 'active' | 'completed' | 'overdue'>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // 从本地存储加载数据
  useEffect(() => {
    const savedTodos = localStorage.getItem('poorlow-todos')
    if (savedTodos) {
      setTodos(JSON.parse(savedTodos))
    }
  }, [])

  // 保存到本地存储
  useEffect(() => {
    localStorage.setItem('poorlow-todos', JSON.stringify(todos))
  }, [todos])

  // 添加新任务
  const addTodo = () => {
    if (!newTodo.trim()) return

    const todo: Todo = {
      id: Date.now().toString(),
      text: newTodo,
      completed: false,
      priority,
      dueDate: dueDate || undefined,
      createdAt: new Date().toISOString(),
      category: category || undefined
    }

    setTodos([todo, ...todos])
    setNewTodo('')
    setDueDate('')
    setCategory('')
  }

  // 切换任务状态
  const toggleTodo = (id: string) => {
    setTodos(todos.map(todo => 
      todo.id === id 
        ? { 
            ...todo, 
            completed: !todo.completed,
            completedAt: !todo.completed ? new Date().toISOString() : undefined
          }
        : todo
    ))
  }

  // 删除任务
  const deleteTodo = (id: string) => {
    setTodos(todos.filter(todo => todo.id !== id))
  }

  // 获取所有分类
  const getCategories = (): string[] => {
    const categories = todos
      .filter(todo => todo.category)
      .map(todo => todo.category!)
    return Array.from(new Set(categories))
  }

  // 过滤任务
  const getFilteredTodos = (): Todo[] => {
    let filtered = todos

    // 按状态过滤
    if (filter === 'active') {
      filtered = filtered.filter(todo => !todo.completed)
    } else if (filter === 'completed') {
      filtered = filtered.filter(todo => todo.completed)
    } else if (filter === 'overdue') {
      filtered = filtered.filter(todo => {
        if (todo.completed || !todo.dueDate) return false
        return new Date(todo.dueDate) < new Date()
      })
    }

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(todo => todo.category === selectedCategory)
    }

    // 按搜索词过滤
    if (searchTerm) {
      filtered = filtered.filter(todo => 
        todo.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (todo.category && todo.category.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    return filtered
  }

  // 计算统计数据
  const getStats = (): TodoStats => {
    const now = new Date()
    return {
      total: todos.length,
      completed: todos.filter(todo => todo.completed).length,
      pending: todos.filter(todo => !todo.completed).length,
      overdue: todos.filter(todo => {
        if (todo.completed || !todo.dueDate) return false
        return new Date(todo.dueDate) < now
      }).length
    }
  }

  // 导出任务
  const exportTodos = () => {
    const dataStr = JSON.stringify(todos, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `todos-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  // 导入任务
  const importTodos = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedTodos = JSON.parse(e.target?.result as string)
        setTodos([...todos, ...importedTodos])
      } catch (error) {
        alert('导入失败，请确保文件格式正确')
      }
    }
    reader.readAsText(file)
  }

  // 清空已完成任务
  const clearCompleted = () => {
    setTodos(todos.filter(todo => !todo.completed))
  }

  const filteredTodos = getFilteredTodos()
  const stats = getStats()
  const categories = getCategories()

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200'
      default:
        return ''
    }
  }

  // 检查是否过期
  const isOverdue = (todo: Todo) => {
    if (todo.completed || !todo.dueDate) return false
    return new Date(todo.dueDate) < new Date()
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">任务清单管理器</h2>
        <p className="text-gray-600">
          高效管理您的待办事项，支持优先级、分类和截止日期
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-2xl font-bold">{stats.total}</div>
          <div className="text-sm text-blue-700">总任务数</div>
        </div>
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-2xl font-bold">{stats.completed}</div>
          <div className="text-sm text-green-700">已完成</div>
        </div>
        <div className="bg-yellow-50 rounded-lg p-4">
          <div className="text-yellow-600 text-2xl font-bold">{stats.pending}</div>
          <div className="text-sm text-yellow-700">进行中</div>
        </div>
        <div className="bg-red-50 rounded-lg p-4">
          <div className="text-red-600 text-2xl font-bold">{stats.overdue}</div>
          <div className="text-sm text-red-700">已过期</div>
        </div>
      </div>

      {/* 添加任务表单 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="space-y-4">
          <div>
            <input
              type="text"
              value={newTodo}
              onChange={(e) => setNewTodo(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addTodo()}
              placeholder="输入新任务..."
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">优先级</label>
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value as 'low' | 'medium' | 'high')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">截止日期</label>
              <input
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">分类</label>
              <input
                type="text"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                placeholder="工作、生活..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex items-end">
              <button
                onClick={addTodo}
                disabled={!newTodo.trim()}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
              >
                <Plus className="w-4 h-4" />
                添加任务
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 过滤和搜索 */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-md transition-colors ${
              filter === 'all'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            全部 ({stats.total})
          </button>
          <button
            onClick={() => setFilter('active')}
            className={`px-4 py-2 rounded-md transition-colors ${
              filter === 'active'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            进行中 ({stats.pending})
          </button>
          <button
            onClick={() => setFilter('completed')}
            className={`px-4 py-2 rounded-md transition-colors ${
              filter === 'completed'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            已完成 ({stats.completed})
          </button>
          <button
            onClick={() => setFilter('overdue')}
            className={`px-4 py-2 rounded-md transition-colors ${
              filter === 'overdue'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            已过期 ({stats.overdue})
          </button>
        </div>

        {categories.length > 0 && (
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm font-medium">分类：</span>
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-3 py-1 rounded-md text-sm transition-colors ${
                selectedCategory === 'all'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              全部
            </button>
            {categories.map(cat => (
              <button
                key={cat}
                onClick={() => setSelectedCategory(cat)}
                className={`px-3 py-1 rounded-md text-sm transition-colors ${
                  selectedCategory === cat
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {cat}
              </button>
            ))}
          </div>
        )}

        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="搜索任务..."
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* 任务列表 */}
      <div className="space-y-2 mb-6">
        {filteredTodos.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <Archive className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            {filter === 'all' && todos.length === 0 ? '还没有任务，开始添加吧！' : '没有符合条件的任务'}
          </div>
        ) : (
          filteredTodos.map(todo => (
            <div
              key={todo.id}
              className={`bg-white rounded-lg border p-4 transition-all ${
                todo.completed ? 'opacity-60' : ''
              } ${isOverdue(todo) ? 'border-red-300' : 'border-gray-200'}`}
            >
              <div className="flex items-start gap-3">
                <button
                  onClick={() => toggleTodo(todo.id)}
                  className={`mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                    todo.completed
                      ? 'bg-blue-500 border-blue-500'
                      : 'border-gray-300 hover:border-blue-500'
                  }`}
                >
                  {todo.completed && <Check className="w-3 h-3 text-white" />}
                </button>
                
                <div className="flex-1">
                  <div className={`${todo.completed ? 'line-through text-gray-500' : ''}`}>
                    {todo.text}
                  </div>
                  
                  <div className="flex flex-wrap items-center gap-2 mt-2">
                    <span className={`px-2 py-1 rounded-md text-xs border ${getPriorityColor(todo.priority)}`}>
                      {todo.priority === 'high' ? '高' : todo.priority === 'medium' ? '中' : '低'}优先级
                    </span>
                    
                    {todo.category && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-md text-xs">
                        {todo.category}
                      </span>
                    )}
                    
                    {todo.dueDate && (
                      <span className={`px-2 py-1 rounded-md text-xs flex items-center gap-1 ${
                        isOverdue(todo)
                          ? 'bg-red-50 text-red-600'
                          : 'bg-gray-50 text-gray-600'
                      }`}>
                        <Calendar className="w-3 h-3" />
                        {new Date(todo.dueDate).toLocaleDateString('zh-CN')}
                      </span>
                    )}
                    
                    <span className="text-xs text-gray-400 flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {new Date(todo.createdAt).toLocaleDateString('zh-CN')}
                    </span>
                  </div>
                </div>
                
                <button
                  onClick={() => deleteTodo(todo.id)}
                  className="text-gray-400 hover:text-red-500 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={exportTodos}
          disabled={todos.length === 0}
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
        >
          <Download className="w-4 h-4" />
          导出任务
        </button>
        
        <label className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 cursor-pointer transition-colors flex items-center gap-2">
          <Upload className="w-4 h-4" />
          导入任务
          <input
            type="file"
            accept=".json"
            onChange={importTodos}
            className="hidden"
          />
        </label>
        
        <button
          onClick={clearCompleted}
          disabled={stats.completed === 0}
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
        >
          <Archive className="w-4 h-4" />
          清空已完成
        </button>
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>任务会自动保存到浏览器本地存储</li>
              <li>支持按优先级、分类和截止日期管理任务</li>
              <li>可以导出任务为 JSON 文件备份</li>
              <li>点击复选框标记任务完成状态</li>
              <li>过期任务会以红色边框标识</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
