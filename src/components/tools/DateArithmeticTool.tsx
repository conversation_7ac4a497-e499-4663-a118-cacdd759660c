'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar, Plus, Minus, Calculator, Clock, Info, Copy, Trash2, RotateCcw } from 'lucide-react'

interface DateCalculation {
  id: string
  baseDate: string
  operation: 'add' | 'subtract'
  amount: number
  unit: 'days' | 'weeks' | 'months' | 'years' | 'hours' | 'minutes'
  result: Date
  description: string
}

interface DateRange {
  start: Date
  end: Date
  days: number
  businessDays: number
  weekends: number
  weeks: number
  months: number
  years: number
}

export default function DateArithmeticTool() {
  const [baseDate, setBaseDate] = useState(new Date().toISOString().split('T')[0])
  const [operation, setOperation] = useState<'add' | 'subtract'>('add')
  const [amount, setAmount] = useState<number>(1)
  const [unit, setUnit] = useState<'days' | 'weeks' | 'months' | 'years' | 'hours' | 'minutes'>('days')
  const [result, setResult] = useState<Date | null>(null)
  const [calculations, setCalculations] = useState<DateCalculation[]>([])

  // 范围计算
  const [startDate, setStartDate] = useState(new Date().toISOString().split('T')[0])
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0])
  const [dateRange, setDateRange] = useState<DateRange | null>(null)

  /**
   * 计算日期加减
   */
  const calculateDate = () => {
    try {
      const date = new Date(baseDate)
      if (isNaN(date.getTime())) {
        return
      }

      let resultDate = new Date(date)
      const factor = operation === 'add' ? 1 : -1

      switch (unit) {
        case 'minutes':
          resultDate.setMinutes(resultDate.getMinutes() + amount * factor)
          break
        case 'hours':
          resultDate.setHours(resultDate.getHours() + amount * factor)
          break
        case 'days':
          resultDate.setDate(resultDate.getDate() + amount * factor)
          break
        case 'weeks':
          resultDate.setDate(resultDate.getDate() + amount * 7 * factor)
          break
        case 'months':
          resultDate.setMonth(resultDate.getMonth() + amount * factor)
          break
        case 'years':
          resultDate.setFullYear(resultDate.getFullYear() + amount * factor)
          break
      }

      setResult(resultDate)

      // 添加到计算历史
      const calculation: DateCalculation = {
        id: Date.now().toString(),
        baseDate,
        operation,
        amount,
        unit,
        result: resultDate,
        description: `${formatDate(date)} ${operation === 'add' ? '+' : '-'} ${amount} ${getUnitLabel(unit)}`
      }

      setCalculations(prev => [calculation, ...prev.slice(0, 9)]) // 保留最近10次计算
    } catch (error) {
      console.error('日期计算错误:', error)
    }
  }

  /**
   * 计算日期范围
   */
  const calculateDateRange = () => {
    try {
      const start = new Date(startDate)
      const end = new Date(endDate)

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return
      }

      if (start > end) {
        return
      }

      const diffTime = end.getTime() - start.getTime()
      const totalDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      // 计算工作日和周末
      let businessDays = 0
      let weekends = 0
      const current = new Date(start)
      
      while (current <= end) {
        const dayOfWeek = current.getDay()
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          weekends++
        } else {
          businessDays++
        }
        current.setDate(current.getDate() + 1)
      }

      // 计算其他单位
      const weeks = Math.floor(totalDays / 7)
      const months = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth())
      const years = end.getFullYear() - start.getFullYear()

      const range: DateRange = {
        start,
        end,
        days: totalDays,
        businessDays,
        weekends,
        weeks,
        months,
        years
      }

      setDateRange(range)
    } catch (error) {
      console.error('日期范围计算错误:', error)
    }
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  }

  /**
   * 格式化日期时间
   */
  const formatDateTime = (date: Date): string => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  /**
   * 获取单位标签
   */
  const getUnitLabel = (unit: string): string => {
    const labels = {
      minutes: '分钟',
      hours: '小时',
      days: '天',
      weeks: '周',
      months: '个月',
      years: '年'
    }
    return labels[unit as keyof typeof labels] || unit
  }

  /**
   * 复制结果
   */
  const copyResult = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  /**
   * 清除计算历史
   */
  const clearHistory = () => {
    setCalculations([])
  }

  /**
   * 重置所有
   */
  const resetAll = () => {
    const today = new Date().toISOString().split('T')[0]
    setBaseDate(today)
    setOperation('add')
    setAmount(1)
    setUnit('days')
    setResult(null)
    setStartDate(today)
    setEndDate(today)
    setDateRange(null)
    setCalculations([])
  }

  /**
   * 设置快速预设
   */
  const setQuickPreset = (preset: { amount: number, unit: typeof unit, operation: typeof operation }) => {
    setAmount(preset.amount)
    setUnit(preset.unit)
    setOperation(preset.operation)
  }

  /**
   * 获取相对时间描述
   */
  const getRelativeDescription = (date: Date): string => {
    const now = new Date()
    const diffTime = date.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return '今天'
    if (diffDays === 1) return '明天'
    if (diffDays === -1) return '昨天'
    if (diffDays > 0) return `${diffDays}天后`
    return `${Math.abs(diffDays)}天前`
  }

  // 自动计算
  useEffect(() => {
    if (baseDate && amount > 0) {
      calculateDate()
    }
  }, [baseDate, operation, amount, unit])

  useEffect(() => {
    if (startDate && endDate) {
      calculateDateRange()
    }
  }, [startDate, endDate])

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            日期加减计算器
          </CardTitle>
          <CardDescription>
            精确计算日期的加减运算，支持多种时间单位和日期范围计算
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="calculator" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="calculator">日期计算</TabsTrigger>
          <TabsTrigger value="range">范围统计</TabsTrigger>
          <TabsTrigger value="history">历史记录</TabsTrigger>
        </TabsList>

        <TabsContent value="calculator" className="space-y-4">
          {/* 日期加减计算 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="w-5 h-5" />
                日期加减计算
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="baseDate">基准日期</Label>
                  <Input
                    id="baseDate"
                    type="date"
                    value={baseDate}
                    onChange={(e) => setBaseDate(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="amount">数量</Label>
                  <Input
                    id="amount"
                    type="number"
                    min="1"
                    value={amount}
                    onChange={(e) => setAmount(parseInt(e.target.value) || 1)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>操作</Label>
                  <Select value={operation} onValueChange={(value: 'add' | 'subtract') => setOperation(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="add">
                        <div className="flex items-center gap-2">
                          <Plus className="w-4 h-4" />
                          加上
                        </div>
                      </SelectItem>
                      <SelectItem value="subtract">
                        <div className="flex items-center gap-2">
                          <Minus className="w-4 h-4" />
                          减去
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>时间单位</Label>
                  <Select value={unit} onValueChange={(value: typeof unit) => setUnit(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="minutes">分钟</SelectItem>
                      <SelectItem value="hours">小时</SelectItem>
                      <SelectItem value="days">天</SelectItem>
                      <SelectItem value="weeks">周</SelectItem>
                      <SelectItem value="months">月</SelectItem>
                      <SelectItem value="years">年</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 快速预设 */}
              <div className="space-y-2">
                <Label>快速预设</Label>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuickPreset({ amount: 1, unit: 'days', operation: 'add' })}
                  >
                    明天
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuickPreset({ amount: 1, unit: 'weeks', operation: 'add' })}
                  >
                    下周
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuickPreset({ amount: 1, unit: 'months', operation: 'add' })}
                  >
                    下月
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuickPreset({ amount: 100, unit: 'days', operation: 'add' })}
                  >
                    100天后
                  </Button>
                </div>
              </div>

              {/* 计算结果 */}
              {result && (
                <div className="space-y-3 p-4 bg-muted rounded-lg">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">计算结果</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyResult(formatDateTime(result))}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <p className="text-lg font-semibold">{formatDate(result)}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDateTime(result)}
                    </p>
                    <Badge variant="secondary">
                      {getRelativeDescription(result)}
                    </Badge>
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                <Button onClick={calculateDate} className="flex items-center gap-2">
                  <Calculator className="h-4 w-4" />
                  重新计算
                </Button>
                <Button variant="outline" onClick={resetAll}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  重置
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="range" className="space-y-4">
          {/* 日期范围计算 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                日期范围计算
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">开始日期</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">结束日期</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </div>

              {/* 范围结果 */}
              {dateRange && (
                <div className="space-y-3 p-4 bg-muted rounded-lg">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">范围统计</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyResult(`总天数: ${dateRange.days}天\n工作日: ${dateRange.businessDays}天\n周末: ${dateRange.weekends}天`)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span>总天数:</span>
                        <span className="font-medium">{dateRange.days}天</span>
                      </div>
                      <div className="flex justify-between">
                        <span>工作日:</span>
                        <span className="font-medium">{dateRange.businessDays}天</span>
                      </div>
                      <div className="flex justify-between">
                        <span>周末:</span>
                        <span className="font-medium">{dateRange.weekends}天</span>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span>总周数:</span>
                        <span className="font-medium">{dateRange.weeks}周</span>
                      </div>
                      <div className="flex justify-between">
                        <span>总月数:</span>
                        <span className="font-medium">{dateRange.months}月</span>
                      </div>
                      <div className="flex justify-between">
                        <span>总年数:</span>
                        <span className="font-medium">{dateRange.years}年</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          {/* 计算历史 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  计算历史
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearHistory}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  清除历史
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {calculations.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  暂无计算历史
                </div>
              ) : (
                <div className="space-y-3">
                  {calculations.map((calc) => (
                    <div
                      key={calc.id}
                      className="flex items-center justify-between p-3 bg-muted rounded-lg"
                    >
                      <div className="space-y-1">
                        <p className="text-sm font-medium">{calc.description}</p>
                        <p className="text-xs text-muted-foreground">
                          结果: {formatDate(calc.result)}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyResult(formatDateTime(calc.result))}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 工具说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="w-5 h-5" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">日期加减计算</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 选择基准日期和操作类型</li>
                <li>• 输入要加减的数量和单位</li>
                <li>• 支持分钟、小时、天、周、月、年</li>
                <li>• 提供快速预设选项</li>
                <li>• 显示相对时间描述</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">日期范围计算</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 计算两个日期之间的间隔</li>
                <li>• 统计总天数、工作日、周末</li>
                <li>• 显示周数、月数、年数</li>
                <li>• 自动排除周末计算工作日</li>
                <li>• 支持结果复制</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
