'use client'

import { useState } from 'react'
import { Search, Globe, Server, Calendar, Shield, Info, Copy, ExternalLink } from 'lucide-react'

interface DomainInfo {
  domain: string
  available: boolean
  registrar?: string
  registrationDate?: string
  expirationDate?: string
  lastUpdated?: string
  nameServers?: string[]
  status?: string[]
  dnssec?: boolean
  whoisData?: string
}

// 模拟的域名数据
const mockDomainData: { [key: string]: DomainInfo } = {
  'example.com': {
    domain: 'example.com',
    available: false,
    registrar: 'Example Registrar, Inc.',
    registrationDate: '1995-08-14',
    expirationDate: '2030-08-13',
    lastUpdated: '2023-08-14',
    nameServers: [
      'a.iana-servers.net',
      'b.iana-servers.net',
    ],
    status: ['clientDeleteProhibited', 'clientTransferProhibited', 'clientUpdateProhibited'],
    dnssec: false,
    whoisData: `Domain Name: EXAMPLE.COM
Registry Domain ID: 2336799_DOMAIN_COM-VRSN
Registrar WHOIS Server: whois.iana.org
Registrar URL: http://www.iana.org
Updated Date: 2023-08-14T07:01:38Z
Creation Date: 1995-08-14T04:00:00Z
Registry Expiry Date: 2030-08-13T04:00:00Z
Registrar: RESERVED-Internet Assigned Numbers Authority
Registrar IANA ID: 376
Registrar Abuse Contact Email: <EMAIL>
Registrar Abuse Contact Phone: *************`,
  },
  'google.com': {
    domain: 'google.com',
    available: false,
    registrar: 'MarkMonitor Inc.',
    registrationDate: '1997-09-15',
    expirationDate: '2028-09-14',
    lastUpdated: '2023-09-09',
    nameServers: [
      'ns1.google.com',
      'ns2.google.com',
      'ns3.google.com',
      'ns4.google.com',
    ],
    status: ['clientDeleteProhibited', 'clientTransferProhibited', 'clientUpdateProhibited', 'serverDeleteProhibited', 'serverTransferProhibited', 'serverUpdateProhibited'],
    dnssec: true,
  },
}

export default function DomainLookupTool() {
  const [domain, setDomain] = useState('')
  const [domainInfo, setDomainInfo] = useState<DomainInfo | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [searchHistory, setSearchHistory] = useState<string[]>([])

  // 验证域名格式
  const validateDomain = (domain: string): boolean => {
    const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/
    return domainRegex.test(domain)
  }

  // 查询域名信息
  const lookupDomain = async () => {
    const trimmedDomain = domain.trim().toLowerCase()
    
    if (!trimmedDomain) {
      setError('请输入域名')
      return
    }

    if (!validateDomain(trimmedDomain)) {
      setError('域名格式不正确')
      return
    }

    setLoading(true)
    setError('')
    setDomainInfo(null)

    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 检查是否有模拟数据
    if (mockDomainData[trimmedDomain]) {
      setDomainInfo(mockDomainData[trimmedDomain])
    } else {
      // 生成随机数据
      const isAvailable = Math.random() > 0.7
      if (isAvailable) {
        setDomainInfo({
          domain: trimmedDomain,
          available: true,
        })
      } else {
        const registrationDate = new Date(2010 + Math.floor(Math.random() * 10), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28))
        const expirationDate = new Date(registrationDate)
        expirationDate.setFullYear(expirationDate.getFullYear() + Math.floor(Math.random() * 5) + 1)
        
        setDomainInfo({
          domain: trimmedDomain,
          available: false,
          registrar: ['GoDaddy', 'Namecheap', 'Cloudflare', 'Google Domains', 'AWS Route 53'][Math.floor(Math.random() * 5)],
          registrationDate: registrationDate.toISOString().split('T')[0],
          expirationDate: expirationDate.toISOString().split('T')[0],
          lastUpdated: new Date().toISOString().split('T')[0],
          nameServers: [
            `ns1.${trimmedDomain}`,
            `ns2.${trimmedDomain}`,
          ],
          status: ['clientTransferProhibited'],
          dnssec: Math.random() > 0.5,
        })
      }
    }

    // 添加到搜索历史
    setSearchHistory(prev => {
      const newHistory = [trimmedDomain, ...prev.filter(d => d !== trimmedDomain)]
      return newHistory.slice(0, 10)
    })

    setLoading(false)
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  // 计算剩余天数
  const getDaysRemaining = (expirationDate: string) => {
    const expiry = new Date(expirationDate)
    const today = new Date()
    const diffTime = expiry.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">域名查询工具</h2>
        <p className="text-gray-600">
          查询域名的注册信息、DNS记录和WHOIS数据
        </p>
      </div>

      {/* 搜索区域 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex gap-3">
          <div className="flex-1">
            <input
              type="text"
              value={domain}
              onChange={(e) => setDomain(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && lookupDomain()}
              placeholder="输入域名，例如：example.com"
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <button
            onClick={lookupDomain}
            disabled={loading}
            className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 flex items-center gap-2"
          >
            <Search className="w-5 h-5" />
            {loading ? '查询中...' : '查询'}
          </button>
        </div>
        
        {error && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* 查询结果 */}
      {domainInfo && (
        <div className="space-y-6">
          {/* 域名状态 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Globe className="w-5 h-5" />
                域名状态
              </h3>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                domainInfo.available 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {domainInfo.available ? '可注册' : '已注册'}
              </span>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">域名</span>
                <div className="flex items-center gap-2">
                  <span className="font-mono font-medium">{domainInfo.domain}</span>
                  <button
                    onClick={() => copyToClipboard(domainInfo.domain)}
                    className="p-1 hover:bg-gray-100 rounded"
                    title="复制"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              {!domainInfo.available && (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">注册商</span>
                    <span>{domainInfo.registrar}</span>
                  </div>
                  
                  {domainInfo.dnssec !== undefined && (
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">DNSSEC</span>
                      <span className={domainInfo.dnssec ? 'text-green-600' : 'text-gray-500'}>
                        {domainInfo.dnssec ? '已启用' : '未启用'}
                      </span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {/* 重要日期 */}
          {!domainInfo.available && domainInfo.registrationDate && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
                <Calendar className="w-5 h-5" />
                重要日期
              </h3>
              
              <div className="grid md:grid-cols-3 gap-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-sm text-gray-600 mb-1">注册日期</div>
                  <div className="font-medium">{formatDate(domainInfo.registrationDate)}</div>
                </div>
                
                {domainInfo.expirationDate && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="text-sm text-gray-600 mb-1">到期日期</div>
                    <div className="font-medium">{formatDate(domainInfo.expirationDate)}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      剩余 {getDaysRemaining(domainInfo.expirationDate)} 天
                    </div>
                  </div>
                )}
                
                {domainInfo.lastUpdated && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="text-sm text-gray-600 mb-1">最后更新</div>
                    <div className="font-medium">{formatDate(domainInfo.lastUpdated)}</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* DNS服务器 */}
          {!domainInfo.available && domainInfo.nameServers && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
                <Server className="w-5 h-5" />
                DNS服务器
              </h3>
              
              <div className="space-y-2">
                {domainInfo.nameServers.map((ns, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-mono text-sm">{ns}</span>
                    <button
                      onClick={() => copyToClipboard(ns)}
                      className="p-1 hover:bg-gray-200 rounded"
                      title="复制"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 域名状态 */}
          {!domainInfo.available && domainInfo.status && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
                <Shield className="w-5 h-5" />
                域名状态标记
              </h3>
              
              <div className="flex flex-wrap gap-2">
                {domainInfo.status.map((status, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm"
                  >
                    {status}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* WHOIS数据 */}
          {domainInfo.whoisData && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold mb-4">WHOIS 数据</h3>
              <pre className="bg-gray-50 p-4 rounded-lg text-sm font-mono overflow-x-auto">
                {domainInfo.whoisData}
              </pre>
            </div>
          )}
        </div>
      )}

      {/* 搜索历史 */}
      {searchHistory.length > 0 && (
        <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold mb-4">最近查询</h3>
          <div className="flex flex-wrap gap-2">
            {searchHistory.map((historyDomain, index) => (
              <button
                key={index}
                onClick={() => {
                  setDomain(historyDomain)
                  lookupDomain()
                }}
                className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm"
              >
                {historyDomain}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>输入域名进行查询，支持各种顶级域名</li>
              <li>查看域名的注册状态、到期时间等信息</li>
              <li>获取DNS服务器和域名状态标记</li>
              <li>数据仅供参考，实际信息以注册商为准</li>
              <li>WHOIS数据可能因隐私保护而部分隐藏</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
