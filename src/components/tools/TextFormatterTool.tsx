'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Copy, RefreshCw, Trash2, Download, Upload, Settings, Type, FileText, List, Hash, Quote, Scissors, ArrowRight } from 'lucide-react';

interface FormatRule {
  id: string;
  name: string;
  description: string;
  category: 'case' | 'spacing' | 'punctuation' | 'structure' | 'special';
  enabled: boolean;
  options?: Record<string, any>;
}

interface FormatOptions {
  removeExtraSpaces: boolean;
  removeEmptyLines: boolean;
  trimLines: boolean;
  normalizeLineEndings: boolean;
  indentType: 'spaces' | 'tabs';
  indentSize: number;
  listStyle: 'bullet' | 'numbered' | 'dashed' | 'custom';
  customListMarker: string;
  wrapLines: boolean;
  lineWrapLength: number;
  addLineNumbers: boolean;
  preserveStructure: boolean;
  caseConversion: 'none' | 'lower' | 'upper' | 'title' | 'sentence' | 'camel' | 'pascal' | 'snake' | 'kebab';
  quotationStyle: 'none' | 'double' | 'single' | 'smart';
  paragraphSpacing: number;
  bulletPoint: string;
}

const DEFAULT_RULES: FormatRule[] = [
  {
    id: 'remove-extra-spaces',
    name: '移除多余空格',
    description: '将多个连续空格替换为单个空格',
    category: 'spacing',
    enabled: true,
  },
  {
    id: 'remove-empty-lines',
    name: '移除空行',
    description: '删除文本中的空行',
    category: 'spacing',
    enabled: false,
  },
  {
    id: 'trim-lines',
    name: '修剪行首尾空格',
    description: '移除每行开头和结尾的空格',
    category: 'spacing',
    enabled: true,
  },
  {
    id: 'normalize-punctuation',
    name: '标准化标点符号',
    description: '规范标点符号的使用和间距',
    category: 'punctuation',
    enabled: true,
  },
  {
    id: 'fix-capitalization',
    name: '修正大小写',
    description: '修正句首大写和专有名词',
    category: 'case',
    enabled: false,
  },
  {
    id: 'format-lists',
    name: '格式化列表',
    description: '统一列表项的格式',
    category: 'structure',
    enabled: false,
  },
  {
    id: 'add-line-numbers',
    name: '添加行号',
    description: '为每行添加行号',
    category: 'special',
    enabled: false,
  },
  {
    id: 'wrap-long-lines',
    name: '换行长句',
    description: '将超过指定长度的行进行换行',
    category: 'structure',
    enabled: false,
  },
];

const CASE_CONVERSION_OPTIONS = [
  { value: 'none', label: '保持原样' },
  { value: 'lower', label: '全部小写' },
  { value: 'upper', label: '全部大写' },
  { value: 'title', label: '标题格式' },
  { value: 'sentence', label: '句首大写' },
  { value: 'camel', label: '驼峰命名' },
  { value: 'pascal', label: 'Pascal命名' },
  { value: 'snake', label: '蛇形命名' },
  { value: 'kebab', label: '短横线命名' },
];

export default function TextFormatterTool() {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [options, setOptions] = useState<FormatOptions>({
    removeExtraSpaces: true,
    removeEmptyLines: false,
    trimLines: true,
    normalizeLineEndings: true,
    indentType: 'spaces',
    indentSize: 2,
    listStyle: 'bullet',
    customListMarker: '•',
    wrapLines: false,
    lineWrapLength: 80,
    addLineNumbers: false,
    preserveStructure: true,
    caseConversion: 'none',
    quotationStyle: 'none',
    paragraphSpacing: 1,
    bulletPoint: '•',
  });
  const [rules, setRules] = useState<FormatRule[]>(DEFAULT_RULES);
  const [customRules, setCustomRules] = useState<{ pattern: string; replacement: string; }[]>([
    { pattern: '', replacement: '' }
  ]);
  const [error, setError] = useState('');

  const applyCaseConversion = useCallback((text: string, caseType: string): string => {
    switch (caseType) {
      case 'lower':
        return text.toLowerCase();
      case 'upper':
        return text.toUpperCase();
      case 'title':
        return text.replace(/\w\S*/g, (txt) => 
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
      case 'sentence':
        return text.replace(/(^\s*\w|[.!?]\s*\w)/g, (c) => c.toUpperCase());
      case 'camel':
        return text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
          index === 0 ? word.toLowerCase() : word.toUpperCase()
        ).replace(/\s+/g, '');
      case 'pascal':
        return text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => 
          word.toUpperCase()
        ).replace(/\s+/g, '');
      case 'snake':
        return text.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
      case 'kebab':
        return text.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      default:
        return text;
    }
  }, []);

  const formatText = useMemo(() => {
    try {
      setError('');
      
      if (!input.trim()) {
        setOutput('');
        return '';
      }

      let result = input;
      const enabledRules = rules.filter(rule => rule.enabled);

      // 应用基础格式化规则
      if (options.normalizeLineEndings) {
        result = result.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      }

      if (enabledRules.find(r => r.id === 'remove-extra-spaces') || options.removeExtraSpaces) {
        result = result.replace(/[ \t]+/g, ' ');
      }

      if (enabledRules.find(r => r.id === 'trim-lines') || options.trimLines) {
        result = result.split('\n').map(line => line.trim()).join('\n');
      }

      if (enabledRules.find(r => r.id === 'remove-empty-lines') || options.removeEmptyLines) {
        result = result.replace(/\n\s*\n\s*\n/g, '\n\n');
      }

      // 标点符号标准化
      if (enabledRules.find(r => r.id === 'normalize-punctuation')) {
        // 中英文标点符号处理
        result = result.replace(/，\s*/g, '，');
        result = result.replace(/。\s*/g, '。');
        result = result.replace(/！\s*/g, '！');
        result = result.replace(/？\s*/g, '？');
        result = result.replace(/；\s*/g, '；');
        result = result.replace(/：\s*/g, '：');
        
        // 英文标点符号处理
        result = result.replace(/\s*,\s*/g, ', ');
        result = result.replace(/\s*\.\s*/g, '. ');
        result = result.replace(/\s*!\s*/g, '! ');
        result = result.replace(/\s*\?\s*/g, '? ');
        result = result.replace(/\s*;\s*/g, '; ');
        result = result.replace(/\s*:\s*/g, ': ');
      }

      // 大小写转换
      if (options.caseConversion !== 'none') {
        result = applyCaseConversion(result, options.caseConversion);
      }

      // 引号样式
      if (options.quotationStyle !== 'none') {
        switch (options.quotationStyle) {
          case 'double':
            result = result.replace(/[''"]/g, '"');
            break;
          case 'single':
            result = result.replace(/[""']/g, "'");
            break;
          case 'smart':
            result = result.replace(/"([^"]*)"/g, '"$1"');
            result = result.replace(/'([^']*)'/g, '\u2018$1\u2019');
            break;
        }
      }

      // 列表格式化
      if (enabledRules.find(r => r.id === 'format-lists')) {
        const lines = result.split('\n');
        const formattedLines = lines.map(line => {
          // 检测列表项
          if (/^\s*[-*•·]\s/.test(line) || /^\s*\d+[.)]\s/.test(line)) {
            const content = line.replace(/^\s*[-*•·\d+.)\s]+/, '').trim();
            const indent = ' '.repeat(options.indentSize);
            
            switch (options.listStyle) {
              case 'bullet':
                return `${indent}${options.bulletPoint} ${content}`;
              case 'dashed':
                return `${indent}- ${content}`;
              case 'numbered':
                const index = lines.indexOf(line) + 1;
                return `${indent}${index}. ${content}`;
              case 'custom':
                return `${indent}${options.customListMarker} ${content}`;
              default:
                return line;
            }
          }
          return line;
        });
        result = formattedLines.join('\n');
      }

      // 行长度控制
      if (enabledRules.find(r => r.id === 'wrap-long-lines') || options.wrapLines) {
        const lines = result.split('\n');
        const wrappedLines = lines.flatMap(line => {
          if (line.length <= options.lineWrapLength) return [line];
          
          const words = line.split(' ');
          const newLines: string[] = [];
          let currentLine = '';
          
          words.forEach(word => {
            if ((currentLine + ' ' + word).length <= options.lineWrapLength) {
              currentLine = currentLine ? currentLine + ' ' + word : word;
            } else {
              if (currentLine) newLines.push(currentLine);
              currentLine = word;
            }
          });
          
          if (currentLine) newLines.push(currentLine);
          return newLines;
        });
        result = wrappedLines.join('\n');
      }

      // 应用自定义规则
      customRules.forEach(rule => {
        if (rule.pattern && rule.replacement !== undefined) {
          try {
            const regex = new RegExp(rule.pattern, 'g');
            result = result.replace(regex, rule.replacement);
          } catch (e) {
            // 忽略无效的正则表达式
          }
        }
      });

      // 段落间距
      if (options.paragraphSpacing > 1) {
        const spacing = '\n'.repeat(options.paragraphSpacing - 1);
        result = result.replace(/\n\n/g, '\n' + spacing + '\n');
      }

      // 添加行号
      if (enabledRules.find(r => r.id === 'add-line-numbers') || options.addLineNumbers) {
        const lines = result.split('\n');
        const maxDigits = lines.length.toString().length;
        result = lines.map((line, index) => {
          const lineNum = (index + 1).toString().padStart(maxDigits, ' ');
          return `${lineNum}: ${line}`;
        }).join('\n');
      }

      setOutput(result);
      return result;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '格式化失败';
      setError(errorMsg);
      setOutput('');
      return '';
    }
  }, [input, options, rules, customRules, applyCaseConversion]);

  const updateOption = <K extends keyof FormatOptions>(key: K, value: FormatOptions[K]) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const updateRule = (ruleId: string, updates: Partial<FormatRule>) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, ...updates } : rule
    ));
  };

  const addCustomRule = () => {
    setCustomRules(prev => [...prev, { pattern: '', replacement: '' }]);
  };

  const updateCustomRule = (index: number, field: 'pattern' | 'replacement', value: string) => {
    setCustomRules(prev => prev.map((rule, i) => 
      i === index ? { ...rule, [field]: value } : rule
    ));
  };

  const removeCustomRule = (index: number) => {
    setCustomRules(prev => prev.filter((_, i) => i !== index));
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(output);
    } catch (err) {
      setError('复制失败');
    }
  };

  const handleClear = () => {
    setInput('');
    setOutput('');
    setError('');
  };

  const handleReset = () => {
    setOptions({
      removeExtraSpaces: true,
      removeEmptyLines: false,
      trimLines: true,
      normalizeLineEndings: true,
      indentType: 'spaces',
      indentSize: 2,
      listStyle: 'bullet',
      customListMarker: '•',
      wrapLines: false,
      lineWrapLength: 80,
      addLineNumbers: false,
      preserveStructure: true,
      caseConversion: 'none',
      quotationStyle: 'none',
      paragraphSpacing: 1,
      bulletPoint: '•',
    });
    setRules(DEFAULT_RULES);
    setCustomRules([{ pattern: '', replacement: '' }]);
    setError('');
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 1024 * 1024) {
      setError('文件大小不能超过1MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setInput(content);
    };
    reader.readAsText(file);
  };

  const handleDownload = () => {
    if (!output) return;
    
    const blob = new Blob([output], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'formatted-text.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const statistics = useMemo(() => {
    const inputStats = {
      chars: input.length,
      lines: input.split('\n').length,
      words: input.trim().split(/\s+/).filter(w => w.length > 0).length,
      paragraphs: input.split(/\n\s*\n/).filter(p => p.trim().length > 0).length,
    };
    
    const outputStats = {
      chars: output.length,
      lines: output.split('\n').length,
      words: output.trim().split(/\s+/).filter(w => w.length > 0).length,
      paragraphs: output.split(/\n\s*\n/).filter(p => p.trim().length > 0).length,
    };

    return { input: inputStats, output: outputStats };
  }, [input, output]);

  const examples = [
    {
      name: '混乱文本',
      text: `这是一个    格式   混乱的文本。
      
      
它包含多余的    空格，     不规范的标点符号  ，  还有    不一致的换行。

•第一个列表项
   -  第二个列表项
3）第三个列表项

"引号"和'单引号'混用。还有一些  非常长的句子需要进行合理的换行处理，这样可以提高文本的可读性和美观程度。`,
      description: '包含多种格式问题的示例文本'
    },
    {
      name: '英文示例',
      text: `this is a sample text with various formatting issues.    it contains extra     spaces,improper punctuation,and inconsistent capitalization.

- first item
  * second item  
    1. third item

"mixed quotes" and 'different styles' throughout the text. some very long sentences that need to be wrapped properly for better readability and improved presentation quality.`,
      description: '英文格式化示例'
    },
    {
      name: '代码文本',
      text: `function helloWorld(){
console.log("Hello, World!");
  var x=1;
      var y = 2;
return x+y;
}

const myArray=[1,2,3,4,5];
  for(let i=0;i<myArray.length;i++){
console.log(myArray[i]);
    }`,
      description: '需要格式化的代码文本'
    }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>文本格式化器</CardTitle>
          <CardDescription>
            智能文本格式化工具，支持多种格式化规则、自定义配置和批量处理。
            可以处理空格、标点、大小写、列表、换行等各种格式问题。
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">基础设置</TabsTrigger>
          <TabsTrigger value="advanced">高级选项</TabsTrigger>
          <TabsTrigger value="custom">自定义规则</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">快速格式化</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="removeExtraSpaces"
                    checked={options.removeExtraSpaces}
                    onCheckedChange={(checked) => updateOption('removeExtraSpaces', checked)}
                  />
                  <Label htmlFor="removeExtraSpaces">移除多余空格</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="trimLines"
                    checked={options.trimLines}
                    onCheckedChange={(checked) => updateOption('trimLines', checked)}
                  />
                  <Label htmlFor="trimLines">修剪行首尾</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="removeEmptyLines"
                    checked={options.removeEmptyLines}
                    onCheckedChange={(checked) => updateOption('removeEmptyLines', checked)}
                  />
                  <Label htmlFor="removeEmptyLines">移除空行</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="addLineNumbers"
                    checked={options.addLineNumbers}
                    onCheckedChange={(checked) => updateOption('addLineNumbers', checked)}
                  />
                  <Label htmlFor="addLineNumbers">添加行号</Label>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="caseConversion">大小写转换</Label>
                  <Select value={options.caseConversion} onValueChange={(value: any) => updateOption('caseConversion', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CASE_CONVERSION_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quotationStyle">引号样式</Label>
                  <Select value={options.quotationStyle} onValueChange={(value: any) => updateOption('quotationStyle', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">保持原样</SelectItem>
                      <SelectItem value="double">统一双引号</SelectItem>
                      <SelectItem value="single">统一单引号</SelectItem>
                      <SelectItem value="smart">智能引号</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">高级选项</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>换行设置</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="wrapLines"
                        checked={options.wrapLines}
                        onCheckedChange={(checked) => updateOption('wrapLines', checked)}
                      />
                      <Label htmlFor="wrapLines">自动换行</Label>
                    </div>
                    {options.wrapLines && (
                      <div className="space-y-1">
                        <Label htmlFor="lineWrapLength" className="text-sm">换行长度</Label>
                        <Input
                          id="lineWrapLength"
                          type="number"
                          min="20"
                          max="200"
                          value={options.lineWrapLength}
                          onChange={(e) => updateOption('lineWrapLength', parseInt(e.target.value))}
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>缩进设置</Label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="spaces"
                          name="indentType"
                          checked={options.indentType === 'spaces'}
                          onChange={() => updateOption('indentType', 'spaces')}
                        />
                        <Label htmlFor="spaces">空格</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="tabs"
                          name="indentType"
                          checked={options.indentType === 'tabs'}
                          onChange={() => updateOption('indentType', 'tabs')}
                        />
                        <Label htmlFor="tabs">制表符</Label>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="indentSize" className="text-sm">缩进大小</Label>
                      <Input
                        id="indentSize"
                        type="number"
                        min="1"
                        max="8"
                        value={options.indentSize}
                        onChange={(e) => updateOption('indentSize', parseInt(e.target.value))}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>列表格式化</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Select value={options.listStyle} onValueChange={(value: any) => updateOption('listStyle', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bullet">项目符号</SelectItem>
                        <SelectItem value="dashed">短横线</SelectItem>
                        <SelectItem value="numbered">数字编号</SelectItem>
                        <SelectItem value="custom">自定义</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {options.listStyle === 'custom' && (
                    <div className="space-y-1">
                      <Label htmlFor="customListMarker" className="text-sm">自定义标记</Label>
                      <Input
                        id="customListMarker"
                        value={options.customListMarker}
                        onChange={(e) => updateOption('customListMarker', e.target.value)}
                        placeholder="输入自定义列表标记"
                      />
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="custom" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">自定义替换规则</CardTitle>
              <CardDescription>
                使用正则表达式创建自定义的文本替换规则
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {customRules.map((rule, index) => (
                  <div key={index} className="flex gap-2 items-center">
                    <Input
                      placeholder="正则表达式模式"
                      value={rule.pattern}
                      onChange={(e) => updateCustomRule(index, 'pattern', e.target.value)}
                      className="flex-1"
                    />
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="替换内容"
                      value={rule.replacement}
                      onChange={(e) => updateCustomRule(index, 'replacement', e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeCustomRule(index)}
                      disabled={customRules.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              <Button variant="outline" onClick={addCustomRule}>
                添加规则
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">原始文本</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleClear}>
                <Trash2 className="h-4 w-4 mr-1" />
                清空
              </Button>
              <Button variant="outline" size="sm" asChild>
                <label>
                  <Upload className="h-4 w-4 mr-1" />
                  上传文件
                  <input
                    type="file"
                    accept=".txt,.md,.csv"
                    className="hidden"
                    onChange={handleFileUpload}
                  />
                </label>
              </Button>
              <Button variant="outline" size="sm" onClick={handleReset}>
                <RefreshCw className="h-4 w-4 mr-1" />
                重置
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="在这里输入要格式化的文本..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="min-h-[400px] font-mono text-sm"
            />
            
            <div className="space-y-2">
              <Label>示例文本：</Label>
              <div className="space-y-2">
                {examples.map((example, index) => (
                  <div key={index} className="p-2 border rounded">
                    <div className="flex justify-between items-center mb-1">
                      <span className="font-medium text-sm">{example.name}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setInput(example.text)}
                      >
                        使用示例
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {example.description}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 输入统计 */}
            <div className="text-sm text-muted-foreground">
              <Badge variant="outline">字符: {statistics.input.chars}</Badge>
              <Badge variant="outline" className="ml-2">行数: {statistics.input.lines}</Badge>
              <Badge variant="outline" className="ml-2">词数: {statistics.input.words}</Badge>
              <Badge variant="outline" className="ml-2">段落: {statistics.input.paragraphs}</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">格式化结果</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleCopy} disabled={!output}>
                <Copy className="h-4 w-4 mr-1" />
                复制
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload} disabled={!output}>
                <Download className="h-4 w-4 mr-1" />
                下载
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <div className="text-sm text-red-500 bg-red-50 p-2 rounded">
                {error}
              </div>
            )}
            
            <Textarea
              value={output}
              readOnly
              placeholder="格式化结果将在这里显示..."
              className="min-h-[400px] font-mono text-sm bg-gray-50"
            />

            {/* 输出统计和变化对比 */}
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">
                <Badge variant="outline">字符: {statistics.output.chars}</Badge>
                <Badge variant="outline" className="ml-2">行数: {statistics.output.lines}</Badge>
                <Badge variant="outline" className="ml-2">词数: {statistics.output.words}</Badge>
                <Badge variant="outline" className="ml-2">段落: {statistics.output.paragraphs}</Badge>
              </div>
              
              {input && output && (
                <div className="text-xs text-muted-foreground">
                  变化: 
                  <span className={`ml-1 ${statistics.output.chars - statistics.input.chars >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    字符 {statistics.output.chars > statistics.input.chars ? '+' : ''}{statistics.output.chars - statistics.input.chars}
                  </span>
                  <span className={`ml-2 ${statistics.output.lines - statistics.input.lines >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    行 {statistics.output.lines > statistics.input.lines ? '+' : ''}{statistics.output.lines - statistics.input.lines}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 格式化规则配置 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Settings className="h-5 w-5" />
            格式化规则
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {rules.map((rule) => (
              <div key={rule.id} className="p-3 border rounded space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={rule.enabled}
                      onCheckedChange={(enabled) => updateRule(rule.id, { enabled })}
                    />
                    <span className="font-medium text-sm">{rule.name}</span>
                  </div>
                  <Badge variant="outline">
                    {rule.category}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground">
                  {rule.description}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-5 w-5" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium">功能特点</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 智能文本格式化</li>
                <li>• 多种预设规则</li>
                <li>• 自定义格式化选项</li>
                <li>• 正则表达式支持</li>
                <li>• 实时预览和统计</li>
                <li>• 批量文件处理</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium">使用技巧</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 可以组合多个格式化规则</li>
                <li>• 自定义规则支持正则表达式</li>
                <li>• 支持上传文本文件批量处理</li>
                <li>• 可以保存格式化结果到文件</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
