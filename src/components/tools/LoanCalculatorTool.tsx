'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { DollarSign, Calculator, TrendingUp, Calendar, Info, Download, PieChart } from 'lucide-react'

interface LoanDetails {
  // 基本参数
  principal: number // 贷款本金
  annualRate: number // 年利率(%)
  termMonths: number // 贷款期限(月)
  
  // 计算结果
  monthlyPayment: number // 月供
  totalPayment: number // 总还款额
  totalInterest: number // 总利息
  
  // 还款计划
  schedule: RepaymentItem[]
}

interface RepaymentItem {
  month: number
  payment: number
  principal: number
  interest: number
  balance: number
}

interface ComparisonItem {
  termMonths: number
  monthlyPayment: number
  totalPayment: number
  totalInterest: number
}

export default function LoanCalculatorTool() {
  const [loanType, setLoanType] = useState<'equal-principal' | 'equal-payment'>('equal-payment')
  const [principal, setPrincipal] = useState<string>('1000000')
  const [annualRate, setAnnualRate] = useState<string>('4.9')
  const [termYears, setTermYears] = useState<string>('30')
  const [loanDetails, setLoanDetails] = useState<LoanDetails | null>(null)
  const [showSchedule, setShowSchedule] = useState(false)
  const [comparisonData, setComparisonData] = useState<ComparisonItem[]>([])
  
  // 等额本息计算
  const calculateEqualPayment = (p: number, r: number, n: number): LoanDetails => {
    const monthlyRate = r / 100 / 12
    const monthlyPayment = p * monthlyRate * Math.pow(1 + monthlyRate, n) / (Math.pow(1 + monthlyRate, n) - 1)
    
    const schedule: RepaymentItem[] = []
    let balance = p
    
    for (let i = 1; i <= n; i++) {
      const interest = balance * monthlyRate
      const principalPayment = monthlyPayment - interest
      balance -= principalPayment
      
      schedule.push({
        month: i,
        payment: monthlyPayment,
        principal: principalPayment,
        interest: interest,
        balance: Math.max(0, balance),
      })
    }
    
    const totalPayment = monthlyPayment * n
    const totalInterest = totalPayment - p
    
    return {
      principal: p,
      annualRate: r,
      termMonths: n,
      monthlyPayment,
      totalPayment,
      totalInterest,
      schedule,
    }
  }
  
  // 等额本金计算
  const calculateEqualPrincipal = (p: number, r: number, n: number): LoanDetails => {
    const monthlyRate = r / 100 / 12
    const principalPayment = p / n
    
    const schedule: RepaymentItem[] = []
    let balance = p
    let totalPayment = 0
    
    for (let i = 1; i <= n; i++) {
      const interest = balance * monthlyRate
      const payment = principalPayment + interest
      balance -= principalPayment
      totalPayment += payment
      
      schedule.push({
        month: i,
        payment,
        principal: principalPayment,
        interest,
        balance: Math.max(0, balance),
      })
    }
    
    const totalInterest = totalPayment - p
    const monthlyPayment = schedule[0].payment // 首月还款额
    
    return {
      principal: p,
      annualRate: r,
      termMonths: n,
      monthlyPayment,
      totalPayment,
      totalInterest,
      schedule,
    }
  }
  
  // 计算贷款
  const calculateLoan = useCallback(() => {
    const p = parseFloat(principal) || 0
    const r = parseFloat(annualRate) || 0
    const n = (parseFloat(termYears) || 0) * 12
    
    if (p <= 0 || r <= 0 || n <= 0) return
    
    let details: LoanDetails
    if (loanType === 'equal-payment') {
      details = calculateEqualPayment(p, r, n)
    } else {
      details = calculateEqualPrincipal(p, r, n)
    }
    
    setLoanDetails(details)
    
    // 生成对比数据
    const comparisons: ComparisonItem[] = []
    const terms = [10, 15, 20, 25, 30]
    terms.forEach(years => {
      const months = years * 12
      const data = loanType === 'equal-payment' 
        ? calculateEqualPayment(p, r, months)
        : calculateEqualPrincipal(p, r, months)
      comparisons.push({
        termMonths: months,
        monthlyPayment: data.monthlyPayment,
        totalPayment: data.totalPayment,
        totalInterest: data.totalInterest,
      })
    })
    setComparisonData(comparisons)
  }, [loanType, principal, annualRate, termYears])
  
  // 格式化货币
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)
  }
  
  // 格式化数字输入
  const formatNumberInput = (value: string): string => {
    const num = value.replace(/[^\d.]/g, '')
    const parts = num.split('.')
    if (parts.length > 2) return formatNumberInput(parts[0] + '.' + parts.slice(1).join(''))
    if (parts[0]) {
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    }
    return parts.join('.')
  }
  
  // 下载还款计划
  const downloadSchedule = () => {
    if (!loanDetails) return
    
    let csv = '期数,月供,本金,利息,剩余本金\n'
    loanDetails.schedule.forEach(item => {
      csv += `${item.month},${item.payment.toFixed(2)},${item.principal.toFixed(2)},${item.interest.toFixed(2)},${item.balance.toFixed(2)}\n`
    })
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `贷款还款计划_${loanType === 'equal-payment' ? '等额本息' : '等额本金'}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }
  
  // 预设方案
  const presetPlans = [
    { name: '首套房贷', principal: 1000000, rate: 4.2, years: 30 },
    { name: '二套房贷', principal: 1500000, rate: 4.9, years: 30 },
    { name: '商业贷款', principal: 500000, rate: 4.35, years: 20 },
    { name: '公积金贷款', principal: 800000, rate: 3.25, years: 25 },
  ]
  
  // 应用预设
  const applyPreset = (preset: typeof presetPlans[0]) => {
    setPrincipal(preset.principal.toString())
    setAnnualRate(preset.rate.toString())
    setTermYears(preset.years.toString())
  }
  
  // 自动计算
  useEffect(() => {
    calculateLoan()
  }, [loanType, principal, annualRate, termYears, calculateLoan])
  
  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">贷款计算器</h2>
        <p className="text-gray-600">
          计算房贷、车贷等各类贷款的月供、利息和还款计划
        </p>
      </div>
      
      {/* 预设方案 */}
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-3">快速选择</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {presetPlans.map((preset) => (
            <button
              key={preset.name}
              onClick={() => applyPreset(preset)}
              className="p-3 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="font-medium">{preset.name}</div>
              <div className="text-xs text-gray-500 mt-1">
                {(preset.principal / 10000).toFixed(0)}万 · {preset.rate}% · {preset.years}年
              </div>
            </button>
          ))}
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 输入区域 */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="font-semibold mb-4">贷款参数</h3>
            
            {/* 还款方式 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">还款方式</label>
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => setLoanType('equal-payment')}
                  className={`p-2 text-sm rounded-md transition-colors ${
                    loanType === 'equal-payment'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 hover:bg-gray-200'
                  }`}
                >
                  等额本息
                </button>
                <button
                  onClick={() => setLoanType('equal-principal')}
                  className={`p-2 text-sm rounded-md transition-colors ${
                    loanType === 'equal-principal'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 hover:bg-gray-200'
                  }`}
                >
                  等额本金
                </button>
              </div>
            </div>
            
            {/* 贷款本金 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">贷款本金（元）</label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={formatNumberInput(principal)}
                  onChange={(e) => setPrincipal(e.target.value.replace(/,/g, ''))}
                  placeholder="1,000,000"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            {/* 年利率 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">年利率（%）</label>
              <div className="relative">
                <TrendingUp className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="number"
                  value={annualRate}
                  onChange={(e) => setAnnualRate(e.target.value)}
                  placeholder="4.9"
                  step="0.01"
                  min="0"
                  max="100"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            {/* 贷款期限 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">贷款期限（年）</label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="number"
                  value={termYears}
                  onChange={(e) => setTermYears(e.target.value)}
                  placeholder="30"
                  min="1"
                  max="30"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            {/* 说明信息 */}
            <div className="mt-4 p-3 bg-gray-50 rounded-md text-xs text-gray-600">
              <p className="mb-1">
                <strong>等额本息：</strong>每月还款金额固定，前期利息多本金少
              </p>
              <p>
                <strong>等额本金：</strong>每月本金固定，还款额逐月递减
              </p>
            </div>
          </div>
        </div>
        
        {/* 结果区域 */}
        <div className="lg:col-span-2">
          {loanDetails && (
            <>
              {/* 核心数据 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-sm text-blue-600">月供金额</div>
                  <div className="text-xl font-bold text-blue-900 mt-1">
                    {formatCurrency(loanDetails.monthlyPayment)}
                  </div>
                  {loanType === 'equal-principal' && (
                    <div className="text-xs text-blue-600 mt-1">
                      首月，每月递减
                    </div>
                  )}
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-sm text-green-600">贷款本金</div>
                  <div className="text-xl font-bold text-green-900 mt-1">
                    {formatCurrency(loanDetails.principal)}
                  </div>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="text-sm text-orange-600">支付利息</div>
                  <div className="text-xl font-bold text-orange-900 mt-1">
                    {formatCurrency(loanDetails.totalInterest)}
                  </div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-sm text-purple-600">还款总额</div>
                  <div className="text-xl font-bold text-purple-900 mt-1">
                    {formatCurrency(loanDetails.totalPayment)}
                  </div>
                </div>
              </div>
              
              {/* 可视化图表 */}
              <div className="bg-white p-6 rounded-lg border border-gray-200 mb-6">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <PieChart className="w-5 h-5" />
                  还款构成
                </h3>
                <div className="flex items-center justify-center">
                  <div className="relative w-48 h-48">
                    <svg viewBox="0 0 100 100" className="transform -rotate-90">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="none"
                        stroke="#10B981"
                        strokeWidth="20"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="none"
                        stroke="#F97316"
                        strokeWidth="20"
                        strokeDasharray={`${(loanDetails.totalInterest / loanDetails.totalPayment) * 251.2} 251.2`}
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-2xl font-bold">
                          {((loanDetails.totalInterest / loanDetails.totalPayment) * 100).toFixed(1)}%
                        </div>
                        <div className="text-sm text-gray-500">利息占比</div>
                      </div>
                    </div>
                  </div>
                  <div className="ml-8 space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-green-500 rounded"></div>
                      <span className="text-sm">本金：{formatCurrency(loanDetails.principal)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-orange-500 rounded"></div>
                      <span className="text-sm">利息：{formatCurrency(loanDetails.totalInterest)}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 不同期限对比 */}
              <div className="bg-white p-6 rounded-lg border border-gray-200 mb-6">
                <h3 className="font-semibold mb-4">不同期限对比</h3>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">贷款期限</th>
                        <th className="text-right py-2">月供</th>
                        <th className="text-right py-2">总利息</th>
                        <th className="text-right py-2">还款总额</th>
                      </tr>
                    </thead>
                    <tbody>
                      {comparisonData.map((item) => (
                        <tr
                          key={item.termMonths}
                          className={`border-b ${
                            item.termMonths === loanDetails.termMonths ? 'bg-blue-50' : ''
                          }`}
                        >
                          <td className="py-2">{item.termMonths / 12}年</td>
                          <td className="text-right py-2">{formatCurrency(item.monthlyPayment)}</td>
                          <td className="text-right py-2">{formatCurrency(item.totalInterest)}</td>
                          <td className="text-right py-2">{formatCurrency(item.totalPayment)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              
              {/* 还款计划 */}
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">还款计划表</h3>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setShowSchedule(!showSchedule)}
                      className="text-sm text-blue-500 hover:text-blue-600"
                    >
                      {showSchedule ? '收起' : '展开'}
                    </button>
                    <button
                      onClick={downloadSchedule}
                      className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                    >
                      <Download className="w-4 h-4" />
                      下载
                    </button>
                  </div>
                </div>
                
                {showSchedule && (
                  <div className="max-h-96 overflow-y-auto">
                    <table className="w-full text-sm">
                      <thead className="sticky top-0 bg-white">
                        <tr className="border-b">
                          <th className="text-left py-2">期数</th>
                          <th className="text-right py-2">月供</th>
                          <th className="text-right py-2">本金</th>
                          <th className="text-right py-2">利息</th>
                          <th className="text-right py-2">剩余本金</th>
                        </tr>
                      </thead>
                      <tbody>
                        {loanDetails.schedule.map((item) => (
                          <tr key={item.month} className="border-b">
                            <td className="py-2">{item.month}</td>
                            <td className="text-right py-2">{formatCurrency(item.payment)}</td>
                            <td className="text-right py-2">{formatCurrency(item.principal)}</td>
                            <td className="text-right py-2">{formatCurrency(item.interest)}</td>
                            <td className="text-right py-2">{formatCurrency(item.balance)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
      
      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持等额本息和等额本金两种还款方式</li>
              <li>提供详细的还款计划表，可下载为CSV文件</li>
              <li>自动计算总利息、还款总额等关键数据</li>
              <li>提供不同贷款期限的对比分析</li>
              <li>预设常见贷款方案，方便快速计算</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
