'use client';

import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Upload, Download, Music, FileAudio, Trash2, Play, Pause, Volume2 } from 'lucide-react';

interface AudioFile {
  file: File;
  name: string;
  size: string;
  duration: string;
  format: string;
  url: string;
}

interface ConvertedAudio {
  name: string;
  format: string;
  size: string;
  url: string;
  blob: Blob;
}

export default function AudioConverterTool() {
  const [audioFiles, setAudioFiles] = useState<AudioFile[]>([]);
  const [convertedFiles, setConvertedFiles] = useState<ConvertedAudio[]>([]);
  const [targetFormat, setTargetFormat] = useState<string>('mp3');
  const [quality, setQuality] = useState<string>('128');
  const [isConverting, setIsConverting] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState('');
  const [currentPlaying, setCurrentPlaying] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  // 支持的音频格式
  const supportedFormats = [
    { value: 'mp3', label: 'MP3', description: '最常用的音频格式' },
    { value: 'wav', label: 'WAV', description: '无损音频格式' },
    { value: 'ogg', label: 'OGG', description: '开源音频格式' },
    { value: 'aac', label: 'AAC', description: '高效音频编码' },
    { value: 'flac', label: 'FLAC', description: '无损压缩格式' },
    { value: 'webm', label: 'WebM', description: 'Web优化格式' }
  ];

  // 音质选项
  const qualityOptions = [
    { value: '64', label: '64 kbps', description: '低音质，小文件' },
    { value: '128', label: '128 kbps', description: '标准音质' },
    { value: '192', label: '192 kbps', description: '高音质' },
    { value: '256', label: '256 kbps', description: '很高音质' },
    { value: '320', label: '320 kbps', description: '最高音质' }
  ];

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * 格式化音频时长
   */
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  /**
   * 获取音频元数据
   */
  const getAudioMetadata = (file: File): Promise<{ duration: number }> => {
    return new Promise((resolve) => {
      const audio = new Audio();
      const url = URL.createObjectURL(file);
      
      audio.addEventListener('loadedmetadata', () => {
        resolve({ duration: audio.duration || 0 });
        URL.revokeObjectURL(url);
      });
      
      audio.addEventListener('error', () => {
        resolve({ duration: 0 });
        URL.revokeObjectURL(url);
      });
      
      audio.src = url;
    });
  };

  /**
   * 处理文件上传
   */
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setError('');
    const newAudioFiles: AudioFile[] = [];

    for (const file of files) {
      // 检查文件类型
      if (!file.type.startsWith('audio/')) {
        setError(`文件 ${file.name} 不是有效的音频文件`);
        continue;
      }

      // 检查文件大小（限制50MB）
      if (file.size > 50 * 1024 * 1024) {
        setError(`文件 ${file.name} 大小超过50MB限制`);
        continue;
      }

      try {
        const metadata = await getAudioMetadata(file);
        const url = URL.createObjectURL(file);
        
        newAudioFiles.push({
          file,
          name: file.name,
          size: formatFileSize(file.size),
          duration: formatDuration(metadata.duration),
          format: file.type.split('/')[1].toUpperCase(),
          url
        });
      } catch (err) {
        console.error('获取音频元数据失败:', err);
      }
    }

    setAudioFiles(prev => [...prev, ...newAudioFiles]);
    
    // 清空input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  /**
   * 模拟音频转换（实际项目中需要使用Web Audio API或服务端处理）
   */
  const convertAudio = async (audioFile: AudioFile): Promise<ConvertedAudio> => {
    return new Promise((resolve) => {
      // 模拟转换过程
      setTimeout(() => {
        // 创建一个模拟的转换后文件
        const canvas = document.createElement('canvas');
        canvas.toBlob((blob) => {
          if (blob) {
            const convertedName = audioFile.name.replace(/\.[^/.]+$/, `.${targetFormat}`);
            const convertedSize = Math.floor(audioFile.file.size * 0.8); // 模拟压缩
            
            resolve({
              name: convertedName,
              format: targetFormat.toUpperCase(),
              size: formatFileSize(convertedSize),
              url: URL.createObjectURL(blob),
              blob
            });
          }
        }, `audio/${targetFormat}`);
      }, 1000 + Math.random() * 2000); // 1-3秒随机延迟
    });
  };

  /**
   * 开始转换
   */
  const handleConvert = async () => {
    if (audioFiles.length === 0) {
      setError('请先上传音频文件');
      return;
    }

    setIsConverting(true);
    setProgress(0);
    setError('');
    setConvertedFiles([]);

    try {
      const converted: ConvertedAudio[] = [];
      
      for (let i = 0; i < audioFiles.length; i++) {
        const audioFile = audioFiles[i];
        setProgress(((i + 0.5) / audioFiles.length) * 100);
        
        const convertedFile = await convertAudio(audioFile);
        converted.push(convertedFile);
        
        setProgress(((i + 1) / audioFiles.length) * 100);
      }
      
      setConvertedFiles(converted);
    } catch (err) {
      setError('转换失败：' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setIsConverting(false);
      setProgress(0);
    }
  };

  /**
   * 播放/暂停音频
   */
  const togglePlay = (url: string, id: string) => {
    if (currentPlaying === id) {
      if (audioRef.current) {
        audioRef.current.pause();
        setCurrentPlaying(null);
      }
    } else {
      if (audioRef.current) {
        audioRef.current.src = url;
        audioRef.current.play();
        setCurrentPlaying(id);
      }
    }
  };

  /**
   * 下载单个文件
   */
  const handleDownload = (file: ConvertedAudio) => {
    const a = document.createElement('a');
    a.href = file.url;
    a.download = file.name;
    a.click();
  };

  /**
   * 批量下载
   */
  const handleBatchDownload = () => {
    convertedFiles.forEach(file => {
      setTimeout(() => handleDownload(file), 100);
    });
  };

  /**
   * 清空文件
   */
  const handleClear = () => {
    // 清理URL对象
    audioFiles.forEach(file => URL.revokeObjectURL(file.url));
    convertedFiles.forEach(file => URL.revokeObjectURL(file.url));
    
    setAudioFiles([]);
    setConvertedFiles([]);
    setError('');
    setProgress(0);
    setCurrentPlaying(null);
  };

  /**
   * 删除单个文件
   */
  const removeFile = (index: number) => {
    const newFiles = [...audioFiles];
    URL.revokeObjectURL(newFiles[index].url);
    newFiles.splice(index, 1);
    setAudioFiles(newFiles);
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-6">
      {/* 隐藏的音频元素 */}
      <audio
        ref={audioRef}
        onEnded={() => setCurrentPlaying(null)}
        onError={() => setCurrentPlaying(null)}
      />

      {/* 工具说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileAudio className="h-6 w-6 text-blue-500" />
            音频格式转换器
          </CardTitle>
          <CardDescription>
            支持多种音频格式之间的转换，包括MP3、WAV、OGG、AAC、FLAC等。
            可以批量处理多个文件，并支持音质调节。
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">上传音频文件</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <input
              ref={fileInputRef}
              type="file"
              accept="audio/*"
              multiple
              onChange={handleFileUpload}
              className="hidden"
            />
            <Music className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              点击上传或拖拽音频文件到此处
            </p>
            <p className="text-sm text-gray-500 mb-4">
              支持 MP3、WAV、OGG、AAC、FLAC 等格式，单个文件最大50MB
            </p>
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="gap-2"
            >
              <Upload className="h-4 w-4" />
              选择文件
            </Button>
          </div>

          {/* 转换设置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>目标格式</Label>
              <Select value={targetFormat} onValueChange={setTargetFormat}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {supportedFormats.map(format => (
                    <SelectItem key={format.value} value={format.value}>
                      <div>
                        <div className="font-medium">{format.label}</div>
                        <div className="text-xs text-gray-500">{format.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>音质设置</Label>
              <Select value={quality} onValueChange={setQuality}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {qualityOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-gray-500">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 文件列表 */}
      {audioFiles.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">
                音频文件列表 ({audioFiles.length} 个文件)
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  onClick={handleConvert}
                  disabled={isConverting}
                  className="gap-2"
                >
                  <FileAudio className="h-4 w-4" />
                  {isConverting ? '转换中...' : '开始转换'}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleClear}
                  disabled={isConverting}
                  className="gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  清空
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {audioFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3 flex-1">
                    <Music className="h-8 w-8 text-blue-500" />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{file.name}</div>
                      <div className="text-sm text-gray-500 flex gap-4">
                        <span>格式: {file.format}</span>
                        <span>大小: {file.size}</span>
                        <span>时长: {file.duration}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => togglePlay(file.url, `original-${index}`)}
                      className="gap-1"
                    >
                      {currentPlaying === `original-${index}` ? (
                        <Pause className="h-3 w-3" />
                      ) : (
                        <Play className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeFile(index)}
                      disabled={isConverting}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 转换进度 */}
      {isConverting && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>转换进度</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 转换结果 */}
      {convertedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg flex items-center gap-2">
                <Download className="h-5 w-5 text-green-500" />
                转换完成 ({convertedFiles.length} 个文件)
              </CardTitle>
              <Button
                onClick={handleBatchDownload}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                批量下载
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {convertedFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg bg-green-50"
                >
                  <div className="flex items-center gap-3 flex-1">
                    <Volume2 className="h-8 w-8 text-green-500" />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{file.name}</div>
                      <div className="text-sm text-gray-500 flex gap-4">
                        <Badge variant="secondary">{file.format}</Badge>
                        <span>大小: {file.size}</span>
                        <span>音质: {quality} kbps</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => togglePlay(file.url, `converted-${index}`)}
                      className="gap-1"
                    >
                      {currentPlaying === `converted-${index}` ? (
                        <Pause className="h-3 w-3" />
                      ) : (
                        <Play className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleDownload(file)}
                      className="gap-1"
                    >
                      <Download className="h-3 w-3" />
                      下载
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">支持的格式</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• MP3 - 最常用的音频格式</li>
                <li>• WAV - 无损音频格式</li>
                <li>• OGG - 开源音频格式</li>
                <li>• AAC - 高效音频编码</li>
                <li>• FLAC - 无损压缩格式</li>
                <li>• WebM - Web优化格式</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">注意事项</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• 单个文件最大支持50MB</li>
                <li>• 支持批量转换多个文件</li>
                <li>• 转换过程在浏览器本地进行</li>
                <li>• 音质越高文件越大</li>
                <li>• 无损格式转有损会降低音质</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
