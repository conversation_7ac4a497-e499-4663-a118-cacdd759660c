'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Copy, Download, Upload, RotateCcw, Eye, Palette, Settings, Type, Image as ImageIcon, Sparkles, Grid, Package } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface FaviconConfig {
  text: string
  fontSize: number
  fontFamily: string
  fontWeight: string
  textColor: string
  backgroundColor: string
  backgroundType: 'solid' | 'gradient' | 'transparent'
  gradientStart: string
  gradientEnd: string
  gradientDirection: number
  borderRadius: number
  padding: number
  shadow: boolean
  shadowColor: string
  shadowBlur: number
  shadowOffset: number
}

interface FaviconSize {
  size: number
  name: string
  description: string
}

const faviconSizes: FaviconSize[] = [
  { size: 16, name: '16x16', description: '浏览器标签页' },
  { size: 32, name: '32x32', description: '桌面快捷方式' },
  { size: 48, name: '48x48', description: 'Windows图标' },
  { size: 57, name: '57x57', description: 'iOS设备' },
  { size: 60, name: '60x60', description: 'iOS设备' },
  { size: 72, name: '72x72', description: 'iPad' },
  { size: 76, name: '76x76', description: 'iPad' },
  { size: 96, name: '96x96', description: 'Android' },
  { size: 114, name: '114x114', description: 'iOS Retina' },
  { size: 120, name: '120x120', description: 'iOS Retina' },
  { size: 144, name: '144x144', description: 'Windows磁贴' },
  { size: 152, name: '152x152', description: 'iPad Retina' },
  { size: 180, name: '180x180', description: 'iOS设备' },
  { size: 192, name: '192x192', description: 'Android' },
  { size: 256, name: '256x256', description: 'Windows' },
  { size: 512, name: '512x512', description: 'PWA' }
]

const fontFamilies = [
  'Arial, sans-serif',
  'Georgia, serif',
  'Times New Roman, serif',
  'Helvetica, sans-serif',
  'Verdana, sans-serif',
  'Trebuchet MS, sans-serif',
  'Impact, sans-serif',
  'Courier New, monospace',
  'Comic Sans MS, cursive',
  'Palatino, serif'
]

const gradientPresets = [
  { name: '蓝色渐变', start: '#3b82f6', end: '#1d4ed8' },
  { name: '紫色渐变', start: '#8b5cf6', end: '#7c3aed' },
  { name: '绿色渐变', start: '#10b981', end: '#059669' },
  { name: '橙色渐变', start: '#f59e0b', end: '#d97706' },
  { name: '红色渐变', start: '#ef4444', end: '#dc2626' },
  { name: '青色渐变', start: '#06b6d4', end: '#0891b2' },
  { name: '粉色渐变', start: '#ec4899', end: '#db2777' },
  { name: '黄色渐变', start: '#eab308', end: '#ca8a04' }
]

export default function FaviconGeneratorTool() {
  const [config, setConfig] = useState<FaviconConfig>({
    text: 'F',
    fontSize: 24,
    fontFamily: 'Arial, sans-serif',
    fontWeight: 'bold',
    textColor: '#ffffff',
    backgroundColor: '#3b82f6',
    backgroundType: 'solid',
    gradientStart: '#3b82f6',
    gradientEnd: '#1d4ed8',
    gradientDirection: 45,
    borderRadius: 8,
    padding: 8,
    shadow: false,
    shadowColor: '#000000',
    shadowBlur: 4,
    shadowOffset: 2
  })

  const [selectedSizes, setSelectedSizes] = useState<number[]>([16, 32, 48, 192])
  const [sourceImage, setSourceImage] = useState<string | null>(null)
  const [useSourceImage, setUseSourceImage] = useState(false)
  const [previewSize, setPreviewSize] = useState(128)
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const updateConfig = useCallback((updates: Partial<FaviconConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }))
  }, [])

  const drawFavicon = useCallback((canvas: HTMLCanvasElement, size: number) => {
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = size
    canvas.height = size

    // 清除画布
    ctx.clearRect(0, 0, size, size)

    // 绘制背景
    if (config.backgroundType === 'transparent') {
      // 透明背景，绘制透明度棋盘格
      const checkerSize = Math.max(2, size / 16)
      for (let x = 0; x < size; x += checkerSize) {
        for (let y = 0; y < size; y += checkerSize) {
          if ((Math.floor(x / checkerSize) + Math.floor(y / checkerSize)) % 2 === 0) {
            ctx.fillStyle = '#ffffff'
          } else {
            ctx.fillStyle = '#e5e5e5'
          }
          ctx.fillRect(x, y, checkerSize, checkerSize)
        }
      }
    } else {
      // 绘制圆角矩形背景
      const radius = (config.borderRadius / 32) * size
      ctx.fillStyle = config.backgroundType === 'solid' ? config.backgroundColor : 
                     createGradient(ctx, size, config.gradientStart, config.gradientEnd, config.gradientDirection)
      roundRect(ctx, 0, 0, size, size, radius)
      ctx.fill()
    }

    // 绘制阴影
    if (config.shadow && config.backgroundType !== 'transparent') {
      ctx.save()
      ctx.shadowColor = config.shadowColor
      ctx.shadowBlur = (config.shadowBlur / 32) * size
      ctx.shadowOffsetX = (config.shadowOffset / 32) * size
      ctx.shadowOffsetY = (config.shadowOffset / 32) * size
      ctx.restore()
    }

    // 绘制内容
    if (useSourceImage && sourceImage) {
      // 绘制图片
      const img = new Image()
      img.onload = () => {
        const padding = (config.padding / 32) * size
        const drawSize = size - padding * 2
        ctx.drawImage(img, padding, padding, drawSize, drawSize)
      }
      img.src = sourceImage
    } else {
      // 绘制文字
      const fontSize = (config.fontSize / 32) * size
      ctx.fillStyle = config.textColor
      ctx.font = `${config.fontWeight} ${fontSize}px ${config.fontFamily.split(',')[0]}`
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText(config.text, size / 2, size / 2)
    }
  }, [config, sourceImage, useSourceImage])

  const createGradient = (ctx: CanvasRenderingContext2D, size: number, start: string, end: string, direction: number) => {
    const angle = (direction * Math.PI) / 180
    const x1 = size / 2 + Math.cos(angle) * size / 2
    const y1 = size / 2 + Math.sin(angle) * size / 2
    const x2 = size / 2 - Math.cos(angle) * size / 2
    const y2 = size / 2 - Math.sin(angle) * size / 2

    const gradient = ctx.createLinearGradient(x1, y1, x2, y2)
    gradient.addColorStop(0, start)
    gradient.addColorStop(1, end)
    return gradient
  }

  const roundRect = (ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius: number) => {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }

  useEffect(() => {
    const canvas = canvasRef.current
    if (canvas) {
      drawFavicon(canvas, previewSize)
    }
  }, [config, previewSize, sourceImage, useSourceImage, drawFavicon])

  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setSourceImage(e.target?.result as string)
        setUseSourceImage(true)
      }
      reader.readAsDataURL(file)
    }
  }, [])

  const toggleSizeSelection = useCallback((size: number) => {
    setSelectedSizes(prev => 
      prev.includes(size) 
        ? prev.filter(s => s !== size)
        : [...prev, size].sort((a, b) => a - b)
    )
  }, [])

  const selectAllSizes = useCallback(() => {
    setSelectedSizes(faviconSizes.map(s => s.size))
  }, [])

  const selectCommonSizes = useCallback(() => {
    setSelectedSizes([16, 32, 48, 192])
  }, [])

  const downloadSingle = useCallback((size: number) => {
    const canvas = document.createElement('canvas')
    drawFavicon(canvas, size)
    
    canvas.toBlob(blob => {
      if (blob) {
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `favicon-${size}x${size}.png`
        a.click()
        URL.revokeObjectURL(url)
        toast.success(`已下载 ${size}x${size} 图标`)
      }
    })
  }, [drawFavicon])

  const downloadAll = useCallback(() => {
    selectedSizes.forEach((size, index) => {
      setTimeout(() => {
        downloadSingle(size)
      }, index * 100) // 延迟下载以避免浏览器阻止
    })
    toast.success(`正在下载 ${selectedSizes.length} 个图标文件`)
  }, [selectedSizes, downloadSingle])

  const generateICO = useCallback(() => {
    // 简化的ICO生成（实际实现会更复杂）
    const canvas = document.createElement('canvas')
    drawFavicon(canvas, 32)
    
    canvas.toBlob(blob => {
      if (blob) {
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'favicon.ico'
        a.click()
        URL.revokeObjectURL(url)
        toast.success('已生成 favicon.ico 文件')
      }
    })
  }, [drawFavicon])

  const copyHtmlCode = useCallback(() => {
    const htmlCode = `<!-- 基础favicon -->
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">

<!-- Apple设备 -->
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon-152x152.png">
<link rel="apple-touch-icon" sizes="120x120" href="/apple-touch-icon-120x120.png">
<link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon-76x76.png">

<!-- Android设备 -->
<link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png">
<link rel="icon" type="image/png" sizes="96x96" href="/android-chrome-96x96.png">

<!-- Windows磁贴 -->
<meta name="msapplication-TileImage" content="/mstile-144x144.png">
<meta name="msapplication-TileColor" content="${config.backgroundColor}">

<!-- Web App Manifest -->
<link rel="manifest" href="/site.webmanifest">`

    navigator.clipboard.writeText(htmlCode)
    toast.success('HTML代码已复制到剪贴板')
  }, [config.backgroundColor])

  const resetConfig = useCallback(() => {
    setConfig({
      text: 'F',
      fontSize: 24,
      fontFamily: 'Arial, sans-serif',
      fontWeight: 'bold',
      textColor: '#ffffff',
      backgroundColor: '#3b82f6',
      backgroundType: 'solid',
      gradientStart: '#3b82f6',
      gradientEnd: '#1d4ed8',
      gradientDirection: 45,
      borderRadius: 8,
      padding: 8,
      shadow: false,
      shadowColor: '#000000',
      shadowBlur: 4,
      shadowOffset: 2
    })
    setUseSourceImage(false)
    setSourceImage(null)
    toast.success('已重置所有设置')
  }, [])

  const applyGradientPreset = useCallback((preset: typeof gradientPresets[0]) => {
    updateConfig({
      backgroundType: 'gradient',
      gradientStart: preset.start,
      gradientEnd: preset.end
    })
    toast.success(`已应用 ${preset.name}`)
  }, [updateConfig])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Favicon生成器
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          创建多尺寸图标文件，支持文字、图片、渐变背景和完整的HTML代码生成
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 预览区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              实时预览
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 预览尺寸控制 */}
            <div className="flex items-center gap-4">
              <Label>预览尺寸: {previewSize}px</Label>
              <Slider
                value={[previewSize]}
                onValueChange={([value]) => setPreviewSize(value)}
                min={64}
                max={256}
                step={16}
                className="flex-1"
              />
            </div>

            {/* 预览画布 */}
            <div className="flex justify-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <canvas
                ref={canvasRef}
                className="border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg"
                style={{ 
                  imageRendering: 'pixelated',
                  width: previewSize,
                  height: previewSize
                }}
              />
            </div>

            {/* 多尺寸预览 */}
            <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              {[16, 32, 48, 64].map(size => (
                <div key={size} className="text-center">
                  <canvas
                    className="border border-gray-300 dark:border-gray-600 rounded mx-auto"
                    ref={canvas => {
                      if (canvas) drawFavicon(canvas, size)
                    }}
                    width={size}
                    height={size}
                    style={{ width: size, height: size }}
                  />
                  <div className="text-xs text-gray-500 mt-1">{size}×{size}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 设计面板 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                设计设置
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={resetConfig}>
                  <RotateCcw className="h-4 w-4 mr-1" />
                  重置
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="content" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="content">内容</TabsTrigger>
                <TabsTrigger value="background">背景</TabsTrigger>
                <TabsTrigger value="effects">效果</TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="space-y-4">
                {/* 内容类型选择 */}
                <div className="flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Switch
                      id="use-image"
                      checked={useSourceImage}
                      onCheckedChange={setUseSourceImage}
                    />
                    <Label htmlFor="use-image">使用图片</Label>
                  </div>
                  {useSourceImage && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="h-4 w-4 mr-1" />
                      上传图片
                    </Button>
                  )}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>

                {!useSourceImage ? (
                  <div className="space-y-4">
                    {/* 文字设置 */}
                    <div className="space-y-2">
                      <Label>显示文字</Label>
                      <Input
                        value={config.text}
                        onChange={(e) => updateConfig({ text: e.target.value.slice(0, 4) })}
                        placeholder="输入1-4个字符"
                        maxLength={4}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>字体大小: {config.fontSize}px</Label>
                        <Slider
                          value={[config.fontSize]}
                          onValueChange={([value]) => updateConfig({ fontSize: value })}
                          min={8}
                          max={48}
                          step={1}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>内边距: {config.padding}px</Label>
                        <Slider
                          value={[config.padding]}
                          onValueChange={([value]) => updateConfig({ padding: value })}
                          min={0}
                          max={16}
                          step={1}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>字体样式</Label>
                      <Select value={config.fontFamily} onValueChange={(value) => updateConfig({ fontFamily: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {fontFamilies.map(font => (
                            <SelectItem key={font} value={font}>
                              {font.split(',')[0]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>字体粗细</Label>
                        <Select value={config.fontWeight} onValueChange={(value) => updateConfig({ fontWeight: value })}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="normal">Normal</SelectItem>
                            <SelectItem value="bold">Bold</SelectItem>
                            <SelectItem value="lighter">Lighter</SelectItem>
                            <SelectItem value="bolder">Bolder</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>文字颜色</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            type="color"
                            value={config.textColor}
                            onChange={(e) => updateConfig({ textColor: e.target.value })}
                            className="w-12 h-8 p-0 border-0"
                          />
                          <Input
                            value={config.textColor}
                            onChange={(e) => updateConfig({ textColor: e.target.value })}
                            className="flex-1 text-sm"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {sourceImage && (
                      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <img src={sourceImage} alt="Source" className="w-16 h-16 object-cover rounded mx-auto" />
                        <p className="text-sm text-gray-500 text-center mt-2">已上传图片</p>
                      </div>
                    )}
                    <div className="space-y-2">
                      <Label>内边距: {config.padding}px</Label>
                      <Slider
                        value={[config.padding]}
                        onValueChange={([value]) => updateConfig({ padding: value })}
                        min={0}
                        max={16}
                        step={1}
                      />
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="background" className="space-y-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>背景类型</Label>
                    <Select value={config.backgroundType} onValueChange={(value: 'solid' | 'gradient' | 'transparent') => updateConfig({ backgroundType: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="solid">纯色</SelectItem>
                        <SelectItem value="gradient">渐变</SelectItem>
                        <SelectItem value="transparent">透明</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {config.backgroundType === 'solid' && (
                    <div className="space-y-2">
                      <Label>背景色</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          type="color"
                          value={config.backgroundColor}
                          onChange={(e) => updateConfig({ backgroundColor: e.target.value })}
                          className="w-12 h-8 p-0 border-0"
                        />
                        <Input
                          value={config.backgroundColor}
                          onChange={(e) => updateConfig({ backgroundColor: e.target.value })}
                          className="flex-1 text-sm"
                        />
                      </div>
                    </div>
                  )}

                  {config.backgroundType === 'gradient' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>起始色</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              type="color"
                              value={config.gradientStart}
                              onChange={(e) => updateConfig({ gradientStart: e.target.value })}
                              className="w-12 h-8 p-0 border-0"
                            />
                            <Input
                              value={config.gradientStart}
                              onChange={(e) => updateConfig({ gradientStart: e.target.value })}
                              className="flex-1 text-sm"
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>结束色</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              type="color"
                              value={config.gradientEnd}
                              onChange={(e) => updateConfig({ gradientEnd: e.target.value })}
                              className="w-12 h-8 p-0 border-0"
                            />
                            <Input
                              value={config.gradientEnd}
                              onChange={(e) => updateConfig({ gradientEnd: e.target.value })}
                              className="flex-1 text-sm"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>渐变方向: {config.gradientDirection}°</Label>
                        <Slider
                          value={[config.gradientDirection]}
                          onValueChange={([value]) => updateConfig({ gradientDirection: value })}
                          min={0}
                          max={360}
                          step={15}
                        />
                      </div>

                      {/* 渐变预设 */}
                      <div className="space-y-2">
                        <Label>渐变预设</Label>
                        <div className="grid grid-cols-4 gap-2">
                          {gradientPresets.map((preset, index) => (
                            <Button
                              key={index}
                              variant="outline"
                              size="sm"
                              className="h-8 p-1"
                              onClick={() => applyGradientPreset(preset)}
                              style={{
                                background: `linear-gradient(45deg, ${preset.start}, ${preset.end})`
                              }}
                            >
                              <span className="sr-only">{preset.name}</span>
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {config.backgroundType !== 'transparent' && (
                    <div className="space-y-2">
                      <Label>圆角: {config.borderRadius}px</Label>
                      <Slider
                        value={[config.borderRadius]}
                        onValueChange={([value]) => updateConfig({ borderRadius: value })}
                        min={0}
                        max={16}
                        step={1}
                      />
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="effects" className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Switch
                      id="shadow"
                      checked={config.shadow}
                      onCheckedChange={(checked) => updateConfig({ shadow: checked })}
                    />
                    <Label htmlFor="shadow">添加阴影</Label>
                  </div>

                  {config.shadow && (
                    <div className="space-y-4 ml-6">
                      <div className="space-y-2">
                        <Label>阴影颜色</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            type="color"
                            value={config.shadowColor}
                            onChange={(e) => updateConfig({ shadowColor: e.target.value })}
                            className="w-12 h-8 p-0 border-0"
                          />
                          <Input
                            value={config.shadowColor}
                            onChange={(e) => updateConfig({ shadowColor: e.target.value })}
                            className="flex-1 text-sm"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>模糊: {config.shadowBlur}px</Label>
                          <Slider
                            value={[config.shadowBlur]}
                            onValueChange={([value]) => updateConfig({ shadowBlur: value })}
                            min={0}
                            max={10}
                            step={1}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>偏移: {config.shadowOffset}px</Label>
                          <Slider
                            value={[config.shadowOffset]}
                            onValueChange={([value]) => updateConfig({ shadowOffset: value })}
                            min={0}
                            max={8}
                            step={1}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* 尺寸选择和导出 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 尺寸选择 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Grid className="h-5 w-5" />
                选择尺寸
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={selectCommonSizes}>
                  常用尺寸
                </Button>
                <Button variant="outline" size="sm" onClick={selectAllSizes}>
                  全选
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-3">
              {faviconSizes.map(({ size, name, description }) => (
                <div
                  key={size}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedSizes.includes(size) 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => toggleSizeSelection(size)}
                >
                  <div className="text-sm font-medium">{name}</div>
                  <div className="text-xs text-gray-500">{description}</div>
                  {selectedSizes.includes(size) && (
                    <Badge variant="secondary" className="text-xs mt-1">
                      已选择
                    </Badge>
                  )}
                </div>
              ))}
            </div>
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-sm text-gray-600">
                已选择 <Badge variant="outline">{selectedSizes.length}</Badge> 个尺寸
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 导出选项 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              导出文件
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <Button onClick={downloadAll} className="flex flex-col items-center gap-2 h-auto py-4">
                <Download className="h-5 w-5" />
                <div className="text-center">
                  <div className="font-medium">下载PNG</div>
                  <div className="text-xs text-gray-500">{selectedSizes.length}个文件</div>
                </div>
              </Button>
              
              <Button onClick={generateICO} variant="outline" className="flex flex-col items-center gap-2 h-auto py-4">
                <ImageIcon className="h-5 w-5" />
                <div className="text-center">
                  <div className="font-medium">生成ICO</div>
                  <div className="text-xs text-gray-500">单个文件</div>
                </div>
              </Button>
            </div>

            <Separator />

            <div className="space-y-3">
              <Label className="text-sm font-medium">HTML代码</Label>
              <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <code className="text-xs break-all">
                  &lt;link rel="icon" href="/favicon.ico"&gt;<br />
                  &lt;link rel="icon" sizes="32x32" href="/favicon-32x32.png"&gt;<br />
                  &lt;link rel="apple-touch-icon" href="/apple-touch-icon.png"&gt;
                </code>
              </div>
              <Button onClick={copyHtmlCode} variant="outline" className="w-full">
                <Copy className="h-4 w-4 mr-2" />
                复制完整HTML代码
              </Button>
            </div>

            <Separator />

            <div className="text-xs text-gray-500 space-y-1">
              <p>• PNG格式支持透明背景</p>
              <p>• ICO格式适用于传统浏览器</p>
              <p>• 建议同时生成多个尺寸以适配不同设备</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}