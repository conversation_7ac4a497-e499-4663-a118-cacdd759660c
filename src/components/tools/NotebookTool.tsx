'use client'

import { useState, useEffect } from 'react'
import { FileText, Plus, Search, Trash2, Edit3, Save, X, Calendar, Tag, Download, Upload, FolderOpen, AlertCircle } from 'lucide-react'

interface Note {
  id: string
  title: string
  content: string
  tags: string[]
  createdAt: string
  updatedAt: string
  category: string
}

interface Category {
  name: string
  count: number
}

export default function NotebookTool() {
  const [notes, setNotes] = useState<Note[]>([])
  const [selectedNote, setSelectedNote] = useState<Note | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [editingNote, setEditingNote] = useState<Note | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedTag, setSelectedTag] = useState<string>('all')
  const [showNewNoteForm, setShowNewNoteForm] = useState(false)
  const [newNote, setNewNote] = useState({
    title: '',
    content: '',
    tags: '',
    category: '默认'
  })

  // 从本地存储加载笔记
  useEffect(() => {
    const savedNotes = localStorage.getItem('poorlow-notes')
    if (savedNotes) {
      setNotes(JSON.parse(savedNotes))
    }
  }, [])

  // 保存到本地存储
  useEffect(() => {
    localStorage.setItem('poorlow-notes', JSON.stringify(notes))
  }, [notes])

  // 创建新笔记
  const createNote = () => {
    if (!newNote.title.trim() || !newNote.content.trim()) return

    const note: Note = {
      id: Date.now().toString(),
      title: newNote.title,
      content: newNote.content,
      tags: newNote.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      category: newNote.category || '默认'
    }

    setNotes([note, ...notes])
    setSelectedNote(note)
    setShowNewNoteForm(false)
    setNewNote({ title: '', content: '', tags: '', category: '默认' })
  }

  // 更新笔记
  const updateNote = () => {
    if (!editingNote) return

    setNotes(notes.map(note => 
      note.id === editingNote.id 
        ? { ...editingNote, updatedAt: new Date().toISOString() }
        : note
    ))
    setSelectedNote(editingNote)
    setIsEditing(false)
    setEditingNote(null)
  }

  // 删除笔记
  const deleteNote = (id: string) => {
    if (confirm('确定要删除这篇笔记吗？')) {
      setNotes(notes.filter(note => note.id !== id))
      if (selectedNote?.id === id) {
        setSelectedNote(null)
      }
    }
  }

  // 获取所有分类
  const getCategories = (): Category[] => {
    const categoryMap = new Map<string, number>()
    notes.forEach(note => {
      categoryMap.set(note.category, (categoryMap.get(note.category) || 0) + 1)
    })
    return Array.from(categoryMap.entries()).map(([name, count]) => ({ name, count }))
  }

  // 获取所有标签
  const getAllTags = (): string[] => {
    const tagSet = new Set<string>()
    notes.forEach(note => {
      note.tags.forEach(tag => tagSet.add(tag))
    })
    return Array.from(tagSet)
  }

  // 过滤笔记
  const getFilteredNotes = (): Note[] => {
    let filtered = notes

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(note => note.category === selectedCategory)
    }

    // 按标签过滤
    if (selectedTag !== 'all') {
      filtered = filtered.filter(note => note.tags.includes(selectedTag))
    }

    // 按搜索词过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(note => 
        note.title.toLowerCase().includes(term) ||
        note.content.toLowerCase().includes(term) ||
        note.tags.some(tag => tag.toLowerCase().includes(term))
      )
    }

    return filtered
  }

  // 导出笔记
  const exportNotes = () => {
    const dataStr = JSON.stringify(notes, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `notes-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  // 导入笔记
  const importNotes = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedNotes = JSON.parse(e.target?.result as string)
        setNotes([...notes, ...importedNotes])
      } catch (error) {
        alert('导入失败，请确保文件格式正确')
      }
    }
    reader.readAsText(file)
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const filteredNotes = getFilteredNotes()
  const categories = getCategories()
  const allTags = getAllTags()

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">笔记本</h2>
        <p className="text-gray-600">
          创建、管理和搜索您的个人笔记，支持分类和标签
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 侧边栏 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 操作按钮 */}
          <div className="space-y-2">
            <button
              onClick={() => setShowNewNoteForm(true)}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center justify-center gap-2"
            >
              <Plus className="w-4 h-4" />
              新建笔记
            </button>
            
            <div className="flex gap-2">
              <button
                onClick={exportNotes}
                disabled={notes.length === 0}
                className="flex-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-1 text-sm"
              >
                <Download className="w-4 h-4" />
                导出
              </button>
              
              <label className="flex-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 cursor-pointer transition-colors flex items-center justify-center gap-1 text-sm">
                <Upload className="w-4 h-4" />
                导入
                <input
                  type="file"
                  accept=".json"
                  onChange={importNotes}
                  className="hidden"
                />
              </label>
            </div>
          </div>

          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索笔记..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* 分类过滤 */}
          <div>
            <h3 className="font-medium mb-2 flex items-center gap-2">
              <FolderOpen className="w-4 h-4" />
              分类
            </h3>
            <div className="space-y-1">
              <button
                onClick={() => setSelectedCategory('all')}
                className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                  selectedCategory === 'all'
                    ? 'bg-blue-50 text-blue-600'
                    : 'hover:bg-gray-50'
                }`}
              >
                全部 ({notes.length})
              </button>
              {categories.map(category => (
                <button
                  key={category.name}
                  onClick={() => setSelectedCategory(category.name)}
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    selectedCategory === category.name
                      ? 'bg-blue-50 text-blue-600'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  {category.name} ({category.count})
                </button>
              ))}
            </div>
          </div>

          {/* 标签过滤 */}
          {allTags.length > 0 && (
            <div>
              <h3 className="font-medium mb-2 flex items-center gap-2">
                <Tag className="w-4 h-4" />
                标签
              </h3>
              <div className="flex flex-wrap gap-1">
                <button
                  onClick={() => setSelectedTag('all')}
                  className={`px-2 py-1 rounded-md text-sm transition-colors ${
                    selectedTag === 'all'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  全部
                </button>
                {allTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => setSelectedTag(tag)}
                    className={`px-2 py-1 rounded-md text-sm transition-colors ${
                      selectedTag === tag
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 笔记列表 */}
        <div className="lg:col-span-1 space-y-2">
          {filteredNotes.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              {searchTerm || selectedCategory !== 'all' || selectedTag !== 'all' 
                ? '没有找到符合条件的笔记' 
                : '还没有笔记，点击"新建笔记"开始'}
            </div>
          ) : (
            filteredNotes.map(note => (
              <div
                key={note.id}
                onClick={() => setSelectedNote(note)}
                className={`p-4 rounded-lg border cursor-pointer transition-all ${
                  selectedNote?.id === note.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 bg-white'
                }`}
              >
                <h4 className="font-medium mb-1 line-clamp-1">{note.title}</h4>
                <p className="text-sm text-gray-600 mb-2 line-clamp-2">{note.content}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {new Date(note.updatedAt).toLocaleDateString('zh-CN')}
                  </span>
                  {note.tags.length > 0 && (
                    <div className="flex gap-1">
                      {note.tags.slice(0, 2).map(tag => (
                        <span key={tag} className="px-1.5 py-0.5 bg-gray-100 rounded">
                          {tag}
                        </span>
                      ))}
                      {note.tags.length > 2 && (
                        <span className="px-1.5 py-0.5 bg-gray-100 rounded">
                          +{note.tags.length - 2}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* 笔记内容 */}
        <div className="lg:col-span-2">
          {showNewNoteForm ? (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold mb-4">新建笔记</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">标题</label>
                  <input
                    type="text"
                    value={newNote.title}
                    onChange={(e) => setNewNote({ ...newNote, title: e.target.value })}
                    placeholder="输入笔记标题..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">分类</label>
                  <input
                    type="text"
                    value={newNote.category}
                    onChange={(e) => setNewNote({ ...newNote, category: e.target.value })}
                    placeholder="输入分类名称..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">标签（用逗号分隔）</label>
                  <input
                    type="text"
                    value={newNote.tags}
                    onChange={(e) => setNewNote({ ...newNote, tags: e.target.value })}
                    placeholder="标签1, 标签2, 标签3..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">内容</label>
                  <textarea
                    value={newNote.content}
                    onChange={(e) => setNewNote({ ...newNote, content: e.target.value })}
                    placeholder="输入笔记内容..."
                    rows={10}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  />
                </div>
                
                <div className="flex gap-2">
                  <button
                    onClick={createNote}
                    disabled={!newNote.title.trim() || !newNote.content.trim()}
                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    保存
                  </button>
                  <button
                    onClick={() => {
                      setShowNewNoteForm(false)
                      setNewNote({ title: '', content: '', tags: '', category: '默认' })
                    }}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center gap-2"
                  >
                    <X className="w-4 h-4" />
                    取消
                  </button>
                </div>
              </div>
            </div>
          ) : selectedNote ? (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              {isEditing ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">标题</label>
                    <input
                      type="text"
                      value={editingNote?.title || ''}
                      onChange={(e) => setEditingNote(editingNote ? { ...editingNote, title: e.target.value } : null)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">分类</label>
                    <input
                      type="text"
                      value={editingNote?.category || ''}
                      onChange={(e) => setEditingNote(editingNote ? { ...editingNote, category: e.target.value } : null)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">标签（用逗号分隔）</label>
                    <input
                      type="text"
                      value={editingNote?.tags.join(', ') || ''}
                      onChange={(e) => setEditingNote(editingNote ? { 
                        ...editingNote, 
                        tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
                      } : null)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">内容</label>
                    <textarea
                      value={editingNote?.content || ''}
                      onChange={(e) => setEditingNote(editingNote ? { ...editingNote, content: e.target.value } : null)}
                      rows={15}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={updateNote}
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center gap-2"
                    >
                      <Save className="w-4 h-4" />
                      保存
                    </button>
                    <button
                      onClick={() => {
                        setIsEditing(false)
                        setEditingNote(null)
                      }}
                      className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center gap-2"
                    >
                      <X className="w-4 h-4" />
                      取消
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold mb-2">{selectedNote.title}</h3>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <FolderOpen className="w-4 h-4" />
                          {selectedNote.category}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {formatDate(selectedNote.updatedAt)}
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => {
                          setIsEditing(true)
                          setEditingNote(selectedNote)
                        }}
                        className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                        title="编辑"
                      >
                        <Edit3 className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => deleteNote(selectedNote.id)}
                        className="p-2 text-gray-500 hover:text-red-500 transition-colors"
                        title="删除"
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                  
                  {selectedNote.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {selectedNote.tags.map(tag => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-sm flex items-center gap-1"
                        >
                          <Tag className="w-3 h-3" />
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  <div className="prose prose-sm max-w-none">
                    <p className="whitespace-pre-wrap">{selectedNote.content}</p>
                  </div>
                  
                  <div className="mt-6 pt-4 border-t border-gray-200 text-xs text-gray-500">
                    <p>创建时间：{formatDate(selectedNote.createdAt)}</p>
                    {selectedNote.createdAt !== selectedNote.updatedAt && (
                      <p>最后更新：{formatDate(selectedNote.updatedAt)}</p>
                    )}
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-200 p-12 text-center text-gray-500">
              <FileText className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <p>选择一篇笔记查看内容</p>
            </div>
          )}
        </div>
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>笔记会自动保存到浏览器本地存储</li>
              <li>支持按分类和标签组织笔记</li>
              <li>可以导出笔记为 JSON 文件备份</li>
              <li>使用搜索功能快速查找笔记</li>
              <li>点击笔记标题可以查看和编辑内容</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
