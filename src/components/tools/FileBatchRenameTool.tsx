'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { FileText, Download, Upload, RefreshCw, AlertCircle, CheckCircle2, X, Plus, Edit3, Hash, Calendar, Type, FileType, Folder, Settings } from 'lucide-react'

interface FileItem {
  id: string
  originalName: string
  newName: string
  size: number
  type: string
  lastModified: Date
  extension: string
  status: 'pending' | 'success' | 'error'
  errorMessage?: string
}

interface RenameRule {
  id: string
  type: 'prefix' | 'suffix' | 'replace' | 'pattern' | 'case' | 'numbering' | 'date'
  enabled: boolean
  config: {
    prefix?: string
    suffix?: string
    search?: string
    replace?: string
    pattern?: string
    caseType?: 'upper' | 'lower' | 'title' | 'camel' | 'pascal' | 'snake' | 'kebab'
    startNumber?: number
    digits?: number
    dateFormat?: string
    separator?: string
  }
}

const DEFAULT_RULES: RenameRule[] = [
  {
    id: '1',
    type: 'prefix',
    enabled: true,
    config: { prefix: '' }
  },
  {
    id: '2',
    type: 'suffix',
    enabled: false,
    config: { suffix: '' }
  },
  {
    id: '3',
    type: 'replace',
    enabled: false,
    config: { search: '', replace: '' }
  },
  {
    id: '4',
    type: 'numbering',
    enabled: false,
    config: { startNumber: 1, digits: 3, separator: '_' }
  },
  {
    id: '5',
    type: 'case',
    enabled: false,
    config: { caseType: 'lower' }
  },
  {
    id: '6',
    type: 'date',
    enabled: false,
    config: { dateFormat: 'YYYY-MM-DD', separator: '_' }
  }
]

const PRESET_RULES = {
  'clean-filename': {
    name: '清理文件名',
    description: '移除特殊字符，统一命名格式',
    rules: [
      { type: 'replace', config: { search: '[^a-zA-Z0-9\\-_.]', replace: '_' } },
      { type: 'case', config: { caseType: 'lower' as const } }
    ]
  },
  'add-date': {
    name: '添加日期前缀',
    description: '在文件名前添加当前日期',
    rules: [
      { type: 'date', config: { dateFormat: 'YYYY-MM-DD', separator: '_' } }
    ]
  },
  'sequential-numbering': {
    name: '序号命名',
    description: '按顺序重新编号',
    rules: [
      { type: 'numbering', config: { startNumber: 1, digits: 3, separator: '_' } }
    ]
  },
  'remove-spaces': {
    name: '移除空格',
    description: '将空格替换为下划线',
    rules: [
      { type: 'replace', config: { search: ' ', replace: '_' } }
    ]
  }
}

export default function FileBatchRenameTool() {
  const [files, setFiles] = useState<FileItem[]>([])
  const [rules, setRules] = useState<RenameRule[]>(DEFAULT_RULES)
  const [dragOver, setDragOver] = useState(false)
  const [selectedPreset, setSelectedPreset] = useState<string>('')

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }, [])

  // 文件拖放处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  // 文件选择处理
  const handleFileSelect = useCallback((selectedFiles: File[]) => {
    const newFiles: FileItem[] = selectedFiles.map((file, index) => ({
      id: Date.now() + index + '',
      originalName: file.name,
      newName: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified),
      extension: file.name.split('.').pop()?.toLowerCase() || '',
      status: 'pending'
    }))
    setFiles(prev => [...prev, ...newFiles])
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    const droppedFiles = Array.from(e.dataTransfer.files)
    handleFileSelect(droppedFiles)
  }, [handleFileSelect])

  // 应用命名规则
  const applyRules = useCallback((fileName: string, index: number) => {
    let result = fileName
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'))
    const extension = fileName.substring(fileName.lastIndexOf('.'))

    rules.forEach(rule => {
      if (!rule.enabled) return

      switch (rule.type) {
        case 'prefix':
          if (rule.config.prefix) {
            result = rule.config.prefix + result
          }
          break
        case 'suffix':
          if (rule.config.suffix) {
            result = nameWithoutExt + rule.config.suffix + extension
          }
          break
        case 'replace':
          if (rule.config.search) {
            const regex = new RegExp(rule.config.search, 'g')
            result = result.replace(regex, rule.config.replace || '')
          }
          break
        case 'case':
          const caseType = rule.config.caseType
          switch (caseType) {
            case 'upper':
              result = result.toUpperCase()
              break
            case 'lower':
              result = result.toLowerCase()
              break
            case 'title':
              result = result.replace(/\w\S*/g, txt => 
                txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
              )
              break
            case 'camel':
              result = result.replace(/[-_\s]+(.)?/g, (_, c) => c ? c.toUpperCase() : '')
              break
            case 'pascal':
              result = result.replace(/[-_\s]+(.)?/g, (_, c) => c ? c.toUpperCase() : '')
              result = result.charAt(0).toUpperCase() + result.slice(1)
              break
            case 'snake':
              result = result.replace(/[-\s]+/g, '_').toLowerCase()
              break
            case 'kebab':
              result = result.replace(/[_\s]+/g, '-').toLowerCase()
              break
          }
          break
        case 'numbering':
          const { startNumber = 1, digits = 3, separator = '_' } = rule.config
          const number = String(startNumber + index).padStart(digits, '0')
          result = number + separator + nameWithoutExt + extension
          break
        case 'date':
          const { dateFormat = 'YYYY-MM-DD', separator: dateSeparator = '_' } = rule.config
          const now = new Date()
          const year = now.getFullYear()
          const month = String(now.getMonth() + 1).padStart(2, '0')
          const day = String(now.getDate()).padStart(2, '0')
          const hour = String(now.getHours()).padStart(2, '0')
          const minute = String(now.getMinutes()).padStart(2, '0')
          
          let dateStr = dateFormat
            .replace('YYYY', year.toString())
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hour)
            .replace('mm', minute)
          
          result = dateStr + dateSeparator + nameWithoutExt + extension
          break
      }
    })

    return result
  }, [rules])

  // 预览重命名结果
  const previewRename = useCallback(() => {
    setFiles(prev => prev.map((file, index) => ({
      ...file,
      newName: applyRules(file.originalName, index)
    })))
  }, [applyRules])

  // 更新规则
  const updateRule = useCallback((ruleId: string, updates: Partial<RenameRule>) => {
    setRules(prev => prev.map(rule => 
      rule.id === ruleId ? { ...rule, ...updates } : rule
    ))
  }, [])

  // 应用预设规则
  const applyPreset = useCallback((presetKey: string) => {
    const preset = PRESET_RULES[presetKey as keyof typeof PRESET_RULES]
    if (!preset) return

    const newRules = [...DEFAULT_RULES]
    preset.rules.forEach((presetRule, index) => {
      if (newRules[index]) {
        newRules[index] = {
          ...newRules[index],
          type: presetRule.type as RenameRule['type'],
          enabled: true,
          config: { ...newRules[index].config, ...presetRule.config }
        }
      }
    })
    setRules(newRules)
    setSelectedPreset(presetKey)
  }, [])

  // 生成下载链接
  const generateDownloadScript = useCallback(() => {
    const script = files.map(file => 
      `mv '${file.originalName}' '${file.newName}'`
    ).join('\n')
    
    const blob = new Blob([script], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'batch_rename_script.sh'
    link.click()
    URL.revokeObjectURL(url)
  }, [files])

  // 模拟重命名执行
  const executeRename = useCallback(() => {
    setFiles(prev => prev.map(file => ({
      ...file,
      status: 'success'
    })))
  }, [])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            文件批量重命名工具
          </CardTitle>
          <CardDescription>
            批量重命名多个文件，支持多种命名规则和预设模板
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 文件上传区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragOver ? 'border-primary bg-primary/10' : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-lg font-medium">
                拖放文件到此处或点击选择文件
              </p>
              <p className="text-sm text-muted-foreground">
                支持多文件选择，可批量重命名
              </p>
            </div>
            <Input
              type="file"
              multiple
              className="hidden"
              id="file-input"
              onChange={(e) => e.target.files && handleFileSelect(Array.from(e.target.files))}
            />
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => document.getElementById('file-input')?.click()}
            >
              选择文件
            </Button>
          </div>

          {/* 规则配置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                重命名规则
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="rules" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="rules">自定义规则</TabsTrigger>
                  <TabsTrigger value="presets">预设模板</TabsTrigger>
                </TabsList>

                <TabsContent value="presets" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(PRESET_RULES).map(([key, preset]) => (
                      <Card key={key} className="cursor-pointer hover:bg-gray-50" onClick={() => applyPreset(key)}>
                        <CardContent className="pt-4">
                          <h4 className="font-medium mb-2">{preset.name}</h4>
                          <p className="text-sm text-muted-foreground">{preset.description}</p>
                          {selectedPreset === key && (
                            <Badge className="mt-2">已选择</Badge>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="rules" className="space-y-4">
                  {rules.map((rule) => (
                    <Card key={rule.id}>
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-medium">
                            {rule.type === 'prefix' && '前缀'}
                            {rule.type === 'suffix' && '后缀'}
                            {rule.type === 'replace' && '替换'}
                            {rule.type === 'case' && '大小写'}
                            {rule.type === 'numbering' && '编号'}
                            {rule.type === 'date' && '日期'}
                          </h4>
                          <div className="flex items-center gap-2">
                            <Label htmlFor={`rule-${rule.id}`}>启用</Label>
                            <Input
                              id={`rule-${rule.id}`}
                              type="checkbox"
                              checked={rule.enabled}
                              onChange={(e) => updateRule(rule.id, { enabled: e.target.checked })}
                              className="w-4 h-4"
                            />
                          </div>
                        </div>

                        {rule.enabled && (
                          <div className="space-y-2">
                            {rule.type === 'prefix' && (
                              <div>
                                <Label>前缀文本</Label>
                                <Input
                                  value={rule.config.prefix || ''}
                                  onChange={(e) => updateRule(rule.id, { 
                                    config: { ...rule.config, prefix: e.target.value }
                                  })}
                                  placeholder="要添加的前缀"
                                />
                              </div>
                            )}

                            {rule.type === 'suffix' && (
                              <div>
                                <Label>后缀文本</Label>
                                <Input
                                  value={rule.config.suffix || ''}
                                  onChange={(e) => updateRule(rule.id, { 
                                    config: { ...rule.config, suffix: e.target.value }
                                  })}
                                  placeholder="要添加的后缀"
                                />
                              </div>
                            )}

                            {rule.type === 'replace' && (
                              <div className="grid grid-cols-2 gap-2">
                                <div>
                                  <Label>查找</Label>
                                  <Input
                                    value={rule.config.search || ''}
                                    onChange={(e) => updateRule(rule.id, { 
                                      config: { ...rule.config, search: e.target.value }
                                    })}
                                    placeholder="要替换的文本"
                                  />
                                </div>
                                <div>
                                  <Label>替换为</Label>
                                  <Input
                                    value={rule.config.replace || ''}
                                    onChange={(e) => updateRule(rule.id, { 
                                      config: { ...rule.config, replace: e.target.value }
                                    })}
                                    placeholder="新文本"
                                  />
                                </div>
                              </div>
                            )}

                            {rule.type === 'case' && (
                              <div>
                                <Label>大小写转换</Label>
                                <Select 
                                  value={rule.config.caseType || 'lower'}
                                  onValueChange={(value) => updateRule(rule.id, { 
                                    config: { ...rule.config, caseType: value as any }
                                  })}
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="lower">小写</SelectItem>
                                    <SelectItem value="upper">大写</SelectItem>
                                    <SelectItem value="title">标题格式</SelectItem>
                                    <SelectItem value="camel">驼峰格式</SelectItem>
                                    <SelectItem value="pascal">帕斯卡格式</SelectItem>
                                    <SelectItem value="snake">下划线格式</SelectItem>
                                    <SelectItem value="kebab">短横线格式</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            )}

                            {rule.type === 'numbering' && (
                              <div className="grid grid-cols-3 gap-2">
                                <div>
                                  <Label>起始数字</Label>
                                  <Input
                                    type="number"
                                    value={rule.config.startNumber || 1}
                                    onChange={(e) => updateRule(rule.id, { 
                                      config: { ...rule.config, startNumber: parseInt(e.target.value) }
                                    })}
                                  />
                                </div>
                                <div>
                                  <Label>数字位数</Label>
                                  <Input
                                    type="number"
                                    value={rule.config.digits || 3}
                                    onChange={(e) => updateRule(rule.id, { 
                                      config: { ...rule.config, digits: parseInt(e.target.value) }
                                    })}
                                  />
                                </div>
                                <div>
                                  <Label>分隔符</Label>
                                  <Input
                                    value={rule.config.separator || '_'}
                                    onChange={(e) => updateRule(rule.id, { 
                                      config: { ...rule.config, separator: e.target.value }
                                    })}
                                  />
                                </div>
                              </div>
                            )}

                            {rule.type === 'date' && (
                              <div className="grid grid-cols-2 gap-2">
                                <div>
                                  <Label>日期格式</Label>
                                  <Select 
                                    value={rule.config.dateFormat || 'YYYY-MM-DD'}
                                    onValueChange={(value) => updateRule(rule.id, { 
                                      config: { ...rule.config, dateFormat: value }
                                    })}
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                                      <SelectItem value="YYYY-MM-DD-HH-mm">YYYY-MM-DD-HH-mm</SelectItem>
                                      <SelectItem value="YYYYMMDD">YYYYMMDD</SelectItem>
                                      <SelectItem value="DD-MM-YYYY">DD-MM-YYYY</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div>
                                  <Label>分隔符</Label>
                                  <Input
                                    value={rule.config.separator || '_'}
                                    onChange={(e) => updateRule(rule.id, { 
                                      config: { ...rule.config, separator: e.target.value }
                                    })}
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </TabsContent>
              </Tabs>

              <div className="flex gap-2 mt-4">
                <Button onClick={previewRename} disabled={files.length === 0}>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  预览重命名
                </Button>
                <Button onClick={executeRename} disabled={files.length === 0} variant="outline">
                  <CheckCircle2 className="w-4 h-4 mr-2" />
                  执行重命名
                </Button>
                <Button onClick={generateDownloadScript} disabled={files.length === 0} variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  下载脚本
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 文件预览 */}
          {files.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Folder className="h-4 w-4" />
                  文件预览 ({files.length} 个文件)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {files.map((file, index) => (
                    <div key={file.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {file.status === 'success' ? (
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                          ) : file.status === 'error' ? (
                            <AlertCircle className="h-4 w-4 text-red-500" />
                          ) : (
                            <FileText className="h-4 w-4 text-gray-500" />
                          )}
                          <span className="text-sm font-medium">{index + 1}</span>
                        </div>
                        <div className="flex flex-col">
                          <div className="text-sm text-gray-600">{file.originalName}</div>
                          <div className="text-sm font-medium text-blue-600">→ {file.newName}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{file.extension.toUpperCase()}</Badge>
                        <span className="text-xs text-gray-500">{formatFileSize(file.size)}</span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setFiles(prev => prev.filter(f => f.id !== file.id))}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 使用说明 */}
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">使用说明</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 选择或拖拽多个文件到上传区域</li>
              <li>• 配置重命名规则或选择预设模板</li>
              <li>• 点击&ldquo;预览重命名&rdquo;查看结果</li>
              <li>• 确认无误后点击&ldquo;执行重命名&rdquo;</li>
              <li>• 可下载批量重命名脚本在系统中执行</li>
              <li>• 支持多种命名格式和规则组合</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
