'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Copy, FileText, Zap, BarChart3, Clock, Target } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface SummaryStats {
  originalLength: number
  summaryLength: number
  compressionRatio: number
  wordsRemoved: number
  sentencesOriginal: number
  sentencesSummary: number
}

interface SummaryOptions {
  method: 'extractive' | 'frequency' | 'position'
  ratio: number
  preserveStructure: boolean
  includeKeywords: boolean
}

/**
 * 文本缩略器工具组件
 * 提供智能文本摘要生成功能，支持多种摘要算法和自定义选项
 */
export default function TextSummarizerTool() {
  const [inputText, setInputText] = useState('')
  const [summary, setSummary] = useState('')
  const [keywords, setKeywords] = useState<string[]>([])
  const [stats, setStats] = useState<SummaryStats | null>(null)
  const [options, setOptions] = useState<SummaryOptions>({
    method: 'extractive',
    ratio: 30,
    preserveStructure: true,
    includeKeywords: true
  })
  const [isProcessing, setIsProcessing] = useState(false)
  const { toast } = useToast()

  /**
   * 计算文本统计信息
   */
  const calculateStats = useCallback((original: string, summary: string): SummaryStats => {
    const originalWords = original.trim().split(/\s+/).filter(word => word.length > 0)
    const summaryWords = summary.trim().split(/\s+/).filter(word => word.length > 0)
    const originalSentences = original.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const summarySentences = summary.split(/[.!?]+/).filter(s => s.trim().length > 0)

    return {
      originalLength: originalWords.length,
      summaryLength: summaryWords.length,
      compressionRatio: Math.round((1 - summaryWords.length / originalWords.length) * 100),
      wordsRemoved: originalWords.length - summaryWords.length,
      sentencesOriginal: originalSentences.length,
      sentencesSummary: summarySentences.length
    }
  }, [])

  /**
   * 提取关键词
   */
  const extractKeywords = useCallback((text: string): string[] => {
    // 停用词列表
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall',
      'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
      'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
    ])

    // 提取单词并计算频率
    const words = text.toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))

    const wordFreq = new Map<string, number>()
    words.forEach(word => {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1)
    })

    // 按频率排序并返回前10个关键词
    return Array.from(wordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word)
  }, [])

  /**
   * 计算句子重要性分数
   */
  const calculateSentenceScore = useCallback((sentence: string, keywords: string[], position: number, totalSentences: number): number => {
    let score = 0

    // 关键词密度分数
    const sentenceWords = sentence.toLowerCase().split(/\s+/)
    const keywordCount = sentenceWords.filter(word => keywords.includes(word)).length
    score += (keywordCount / sentenceWords.length) * 100

    // 位置分数（开头和结尾的句子更重要）
    if (position < totalSentences * 0.2) {
      score += 20 // 开头20%
    } else if (position > totalSentences * 0.8) {
      score += 15 // 结尾20%
    }

    // 句子长度分数（中等长度的句子更重要）
    const wordCount = sentenceWords.length
    if (wordCount >= 10 && wordCount <= 30) {
      score += 10
    }

    // 数字和特殊标记分数
    if (/\d/.test(sentence)) score += 5
    if (/[A-Z]{2,}/.test(sentence)) score += 5
    if (/["']/.test(sentence)) score += 3

    return score
  }, [])

  /**
   * 抽取式摘要算法
   */
  const extractiveSummarization = useCallback((text: string, ratio: number, keywords: string[]): string => {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10)
    if (sentences.length === 0) return text

    // 计算每个句子的分数
    const sentenceScores = sentences.map((sentence, index) => ({
      sentence: sentence.trim(),
      score: calculateSentenceScore(sentence, keywords, index, sentences.length),
      originalIndex: index
    }))

    // 根据比例选择最重要的句子
    const targetCount = Math.max(1, Math.floor(sentences.length * (ratio / 100)))
    const selectedSentences = sentenceScores
      .sort((a, b) => b.score - a.score)
      .slice(0, targetCount)
      .sort((a, b) => a.originalIndex - b.originalIndex)

    return selectedSentences.map(item => item.sentence).join('. ') + '.'
  }, [calculateSentenceScore])

  /**
   * 基于词频的摘要算法
   */
  const frequencyBasedSummarization = useCallback((text: string, ratio: number, keywords: string[]): string => {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10)
    if (sentences.length === 0) return text

    // 计算词频分数
    const sentenceScores = sentences.map((sentence, index) => {
      const words = sentence.toLowerCase().split(/\s+/)
      const keywordScore = words.filter(word => keywords.includes(word)).length
      const totalWords = words.length
      
      return {
        sentence: sentence.trim(),
        score: totalWords > 0 ? (keywordScore / totalWords) * 100 : 0,
        originalIndex: index
      }
    })

    const targetCount = Math.max(1, Math.floor(sentences.length * (ratio / 100)))
    const selectedSentences = sentenceScores
      .sort((a, b) => b.score - a.score)
      .slice(0, targetCount)
      .sort((a, b) => a.originalIndex - b.originalIndex)

    return selectedSentences.map(item => item.sentence).join('. ') + '.'
  }, [])

  /**
   * 基于位置的摘要算法
   */
  const positionBasedSummarization = useCallback((text: string, ratio: number): string => {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10)
    if (sentences.length === 0) return text

    const targetCount = Math.max(1, Math.floor(sentences.length * (ratio / 100)))
    const selectedIndices = new Set<number>()

    // 优先选择开头和结尾的句子
    const startCount = Math.ceil(targetCount * 0.4)
    const endCount = Math.ceil(targetCount * 0.3)
    const middleCount = targetCount - startCount - endCount

    // 选择开头句子
    for (let i = 0; i < Math.min(startCount, sentences.length); i++) {
      selectedIndices.add(i)
    }

    // 选择结尾句子
    for (let i = Math.max(0, sentences.length - endCount); i < sentences.length; i++) {
      selectedIndices.add(i)
    }

    // 选择中间句子
    if (middleCount > 0 && sentences.length > startCount + endCount) {
      const middleStart = startCount
      const middleEnd = sentences.length - endCount
      const step = Math.floor((middleEnd - middleStart) / middleCount)
      
      for (let i = 0; i < middleCount; i++) {
        const index = middleStart + i * step
        if (index < middleEnd) {
          selectedIndices.add(index)
        }
      }
    }

    const selectedSentences = Array.from(selectedIndices)
      .sort((a, b) => a - b)
      .map(index => sentences[index].trim())

    return selectedSentences.join('. ') + '.'
  }, [])

  /**
   * 生成摘要
   */
  const generateSummary = useCallback(async () => {
    if (!inputText.trim()) {
      toast({
        title: '请输入文本',
        description: '请先输入需要生成摘要的文本内容',
        variant: 'destructive'
      })
      return
    }

    setIsProcessing(true)

    try {
      // 提取关键词
      const extractedKeywords = extractKeywords(inputText)
      setKeywords(extractedKeywords)

      let generatedSummary = ''

      // 根据选择的方法生成摘要
      switch (options.method) {
        case 'extractive':
          generatedSummary = extractiveSummarization(inputText, options.ratio, extractedKeywords)
          break
        case 'frequency':
          generatedSummary = frequencyBasedSummarization(inputText, options.ratio, extractedKeywords)
          break
        case 'position':
          generatedSummary = positionBasedSummarization(inputText, options.ratio)
          break
      }

      setSummary(generatedSummary)
      setStats(calculateStats(inputText, generatedSummary))

      toast({
        title: '摘要生成成功',
        description: `已生成 ${Math.round(generatedSummary.split(/\s+/).length)} 词的摘要`
      })
    } catch (error) {
      console.error('生成摘要时出错:', error)
      toast({
        title: '生成失败',
        description: '生成摘要时发生错误，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }, [inputText, options, extractKeywords, extractiveSummarization, frequencyBasedSummarization, positionBasedSummarization, calculateStats, toast])

  /**
   * 复制摘要到剪贴板
   */
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: '复制成功',
        description: '摘要已复制到剪贴板'
      })
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法复制到剪贴板，请手动复制',
        variant: 'destructive'
      })
    }
  }, [toast])

  /**
   * 清空所有内容
   */
  const clearAll = useCallback(() => {
    setInputText('')
    setSummary('')
    setKeywords([])
    setStats(null)
  }, [])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">文本缩略器</h1>
        <p className="text-muted-foreground">
          智能生成文本摘要，支持多种算法和自定义选项
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 输入区域 */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                输入文本
              </CardTitle>
              <CardDescription>
                输入需要生成摘要的文本内容
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                placeholder="请输入需要生成摘要的文本内容..."
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                className="min-h-[200px] resize-none"
              />
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>字符数: {inputText.length}</span>
                <span>词数: {inputText.trim() ? inputText.trim().split(/\s+/).length : 0}</span>
              </div>
            </CardContent>
          </Card>

          {/* 摘要结果 */}
          {summary && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  生成的摘要
                </CardTitle>
                <CardDescription>
                  基于 {options.method === 'extractive' ? '抽取式' : options.method === 'frequency' ? '词频' : '位置'} 算法生成
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm leading-relaxed">{summary}</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => copyToClipboard(summary)}
                    variant="outline"
                    size="sm"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    复制摘要
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 关键词 */}
          {keywords.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  提取的关键词
                </CardTitle>
                <CardDescription>
                  基于词频分析提取的重要关键词
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {keywords.map((keyword, index) => (
                    <Badge key={index} variant="secondary">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 设置面板 */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                摘要设置
              </CardTitle>
              <CardDescription>
                配置摘要生成参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 摘要方法 */}
              <div className="space-y-2">
                <Label>摘要算法</Label>
                <Select
                  value={options.method}
                  onValueChange={(value: 'extractive' | 'frequency' | 'position') =>
                    setOptions(prev => ({ ...prev, method: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="extractive">抽取式摘要</SelectItem>
                    <SelectItem value="frequency">词频摘要</SelectItem>
                    <SelectItem value="position">位置摘要</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  {options.method === 'extractive' && '基于句子重要性分数选择关键句子'}
                  {options.method === 'frequency' && '基于关键词频率选择重要句子'}
                  {options.method === 'position' && '基于句子位置选择开头和结尾句子'}
                </p>
              </div>

              {/* 压缩比例 */}
              <div className="space-y-2">
                <Label>摘要比例: {options.ratio}%</Label>
                <Slider
                  value={[options.ratio]}
                  onValueChange={([value]) =>
                    setOptions(prev => ({ ...prev, ratio: value }))
                  }
                  min={10}
                  max={80}
                  step={5}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  保留原文 {options.ratio}% 的内容
                </p>
              </div>

              {/* 生成按钮 */}
              <Button
                onClick={generateSummary}
                disabled={!inputText.trim() || isProcessing}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    生成摘要
                  </>
                )}
              </Button>

              <Button
                onClick={clearAll}
                variant="outline"
                className="w-full"
              >
                清空内容
              </Button>
            </CardContent>
          </Card>

          {/* 统计信息 */}
          {stats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  统计信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">原文词数</p>
                    <p className="font-medium">{stats.originalLength}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">摘要词数</p>
                    <p className="font-medium">{stats.summaryLength}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">压缩率</p>
                    <p className="font-medium">{stats.compressionRatio}%</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">减少词数</p>
                    <p className="font-medium">{stats.wordsRemoved}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">原文句数</p>
                    <p className="font-medium">{stats.sentencesOriginal}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">摘要句数</p>
                    <p className="font-medium">{stats.sentencesSummary}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
