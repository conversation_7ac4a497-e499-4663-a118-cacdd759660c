'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileText, Copy, Check, Download, Upload, Info } from 'lucide-react'

interface TextStats {
  // 基础统计
  totalCharacters: number
  charactersNoSpaces: number
  totalWords: number
  totalSentences: number
  totalParagraphs: number
  totalLines: number
  
  // 字符类型统计
  letters: number
  digits: number
  spaces: number
  punctuation: number
  symbols: number
  chinese: number
  english: number
  
  // 词频统计
  wordFrequency: Map<string, number>
  charFrequency: Map<string, number>
  
  // 高级统计
  averageWordLength: number
  averageSentenceLength: number
  readingTime: number // 分钟
  speakingTime: number // 分钟
  lexicalDiversity: number // 词汇多样性
}

interface FrequencyItem {
  item: string
  count: number
  percentage: number
}

export default function TextStatisticsTool() {
  const [inputText, setInputText] = useState('')
  const [stats, setStats] = useState<TextStats | null>(null)
  const [activeTab, setActiveTab] = useState<'basic' | 'frequency' | 'charts'>('basic')
  const [copied, setCopied] = useState(false)
  const [showTopN, setShowTopN] = useState(20)

  // 计算文本统计信息
  const calculateStats = (text: string): TextStats => {
    // 基础统计
    const totalCharacters = text.length
    const charactersNoSpaces = text.replace(/\s/g, '').length
    const words = text.match(/\b[\w']+\b/g) || []
    const totalWords = words.length
    const sentences = text.match(/[.!?]+/g) || []
    const totalSentences = sentences.length || 1
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim())
    const totalParagraphs = paragraphs.length || 1
    const lines = text.split('\n')
    const totalLines = lines.length

    // 字符类型统计
    const letters = (text.match(/[a-zA-Z]/g) || []).length
    const digits = (text.match(/\d/g) || []).length
    const spaces = (text.match(/\s/g) || []).length
    const punctuation = (text.match(/[.,;:!?'"()\-\[\]{}]/g) || []).length
    const chinese = (text.match(/[\u4e00-\u9fa5]/g) || []).length
    const english = (text.match(/[a-zA-Z]+/g) || []).length
    const symbols = totalCharacters - letters - digits - spaces - punctuation - chinese

    // 词频统计
    const wordFrequency = new Map<string, number>()
    const normalizedWords = words.map(w => w.toLowerCase())
    normalizedWords.forEach(word => {
      wordFrequency.set(word, (wordFrequency.get(word) || 0) + 1)
    })

    // 字符频率统计
    const charFrequency = new Map<string, number>()
    for (const char of text) {
      if (char.trim()) {
        charFrequency.set(char, (charFrequency.get(char) || 0) + 1)
      }
    }

    // 高级统计
    const averageWordLength = totalWords > 0 
      ? words.reduce((sum, word) => sum + word.length, 0) / totalWords 
      : 0
    const averageSentenceLength = totalSentences > 0 
      ? totalWords / totalSentences 
      : 0
    
    // 阅读时间（按每分钟200-250字计算）
    const readingSpeed = chinese > english ? 300 : 200 // 中文快一些
    const readingTime = Math.ceil((totalWords + chinese) / readingSpeed)
    
    // 演讲时间（按每分钟100-150字计算）
    const speakingSpeed = chinese > english ? 150 : 100
    const speakingTime = Math.ceil((totalWords + chinese) / speakingSpeed)
    
    // 词汇多样性（唯一词汇数/总词汇数）
    const uniqueWords = new Set(normalizedWords)
    const lexicalDiversity = totalWords > 0 
      ? (uniqueWords.size / totalWords) * 100 
      : 0

    return {
      totalCharacters,
      charactersNoSpaces,
      totalWords,
      totalSentences,
      totalParagraphs,
      totalLines,
      letters,
      digits,
      spaces,
      punctuation,
      symbols,
      chinese,
      english,
      wordFrequency,
      charFrequency,
      averageWordLength,
      averageSentenceLength,
      readingTime,
      speakingTime,
      lexicalDiversity,
    }
  }

  // 获取排序后的频率列表
  const getTopFrequencies = (
    frequencyMap: Map<string, number>,
    topN: number
  ): FrequencyItem[] => {
    const total = Array.from(frequencyMap.values()).reduce((sum, count) => sum + count, 0)
    
    return Array.from(frequencyMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, topN)
      .map(([item, count]) => ({
        item,
        count,
        percentage: (count / total) * 100,
      }))
  }

  // 复制统计结果
  const copyStats = async () => {
    if (!stats) return
    
    const report = `文本统计分析报告
==================

基础统计
--------
总字符数：${stats.totalCharacters}
无空格字符数：${stats.charactersNoSpaces}
总词数：${stats.totalWords}
总句数：${stats.totalSentences}
总段落数：${stats.totalParagraphs}
总行数：${stats.totalLines}

字符类型统计
------------
字母：${stats.letters}
数字：${stats.digits}
空格：${stats.spaces}
标点：${stats.punctuation}
中文字符：${stats.chinese}
符号：${stats.symbols}

高级统计
--------
平均词长：${stats.averageWordLength.toFixed(2)}
平均句长：${stats.averageSentenceLength.toFixed(2)} 词
阅读时间：约 ${stats.readingTime} 分钟
演讲时间：约 ${stats.speakingTime} 分钟
词汇多样性：${stats.lexicalDiversity.toFixed(2)}%
`
    
    try {
      await navigator.clipboard.writeText(report)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 下载统计报告
  const downloadReport = () => {
    if (!stats) return
    
    const report = generateDetailedReport()
    const blob = new Blob([report], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'text-statistics-report.txt'
    a.click()
    URL.revokeObjectURL(url)
  }

  // 生成详细报告
  const generateDetailedReport = (): string => {
    if (!stats) return ''
    
    const topWords = getTopFrequencies(stats.wordFrequency, 50)
    const topChars = getTopFrequencies(stats.charFrequency, 30)
    
    let report = `文本统计分析详细报告
====================
生成时间：${new Date().toLocaleString()}

一、基础统计
============
总字符数：${stats.totalCharacters}
无空格字符数：${stats.charactersNoSpaces}
总词数：${stats.totalWords}
总句数：${stats.totalSentences}
总段落数：${stats.totalParagraphs}
总行数：${stats.totalLines}

二、字符类型统计
================
字母：${stats.letters} (${((stats.letters / stats.totalCharacters) * 100).toFixed(2)}%)
数字：${stats.digits} (${((stats.digits / stats.totalCharacters) * 100).toFixed(2)}%)
空格：${stats.spaces} (${((stats.spaces / stats.totalCharacters) * 100).toFixed(2)}%)
标点：${stats.punctuation} (${((stats.punctuation / stats.totalCharacters) * 100).toFixed(2)}%)
中文字符：${stats.chinese} (${((stats.chinese / stats.totalCharacters) * 100).toFixed(2)}%)
其他符号：${stats.symbols} (${((stats.symbols / stats.totalCharacters) * 100).toFixed(2)}%)

三、高级统计
============
平均词长：${stats.averageWordLength.toFixed(2)} 字符
平均句长：${stats.averageSentenceLength.toFixed(2)} 词
预计阅读时间：${stats.readingTime} 分钟
预计演讲时间：${stats.speakingTime} 分钟
词汇多样性：${stats.lexicalDiversity.toFixed(2)}%

四、词频统计（前50）
===================
`
    topWords.forEach((item, index) => {
      report += `${index + 1}. "${item.item}" - ${item.count}次 (${item.percentage.toFixed(2)}%)\n`
    })
    
    report += `\n五、字符频率统计（前30）
========================
`
    topChars.forEach((item, index) => {
      report += `${index + 1}. "${item.item}" - ${item.count}次 (${item.percentage.toFixed(2)}%)\n`
    })
    
    return report
  }

  // 上传文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputText(content)
    }
    reader.readAsText(file)
  }

  // 生成图表数据
  const generateChartData = () => {
    if (!stats) return null

    const charTypeData = [
      { name: '字母', value: stats.letters, color: '#3B82F6' },
      { name: '数字', value: stats.digits, color: '#10B981' },
      { name: '空格', value: stats.spaces, color: '#6B7280' },
      { name: '标点', value: stats.punctuation, color: '#F59E0B' },
      { name: '中文', value: stats.chinese, color: '#EF4444' },
      { name: '符号', value: stats.symbols, color: '#8B5CF6' },
    ].filter(item => item.value > 0)

    return charTypeData
  }

  // 更新统计信息
  useEffect(() => {
    if (inputText) {
      const newStats = calculateStats(inputText)
      setStats(newStats)
    } else {
      setStats(null)
    }
  }, [inputText])

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本统计分析</h2>
        <p className="text-gray-600">
          全面分析文本的各项统计指标，包括字数、词频、阅读时间等
        </p>
      </div>

      {/* 输入区域 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium">输入文本</label>
          <label className="text-sm text-blue-500 hover:text-blue-600 cursor-pointer">
            <input
              type="file"
              accept=".txt"
              onChange={handleFileUpload}
              className="hidden"
            />
            <span className="flex items-center gap-1">
              <Upload className="w-4 h-4" />
              上传文件
            </span>
          </label>
        </div>
        <textarea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="请输入或粘贴要分析的文本..."
          className="w-full h-48 p-4 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {stats && (
        <>
          {/* 标签页 */}
          <div className="flex gap-2 mb-6 border-b">
            <button
              onClick={() => setActiveTab('basic')}
              className={`px-4 py-2 font-medium transition-colors ${
                activeTab === 'basic'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              基础统计
            </button>
            <button
              onClick={() => setActiveTab('frequency')}
              className={`px-4 py-2 font-medium transition-colors ${
                activeTab === 'frequency'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              词频分析
            </button>
            <button
              onClick={() => setActiveTab('charts')}
              className={`px-4 py-2 font-medium transition-colors ${
                activeTab === 'charts'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              可视化图表
            </button>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-3 mb-6">
            <button
              onClick={copyStats}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              {copied ? (
                <>
                  <Check className="w-4 h-4" />
                  已复制
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4" />
                  复制报告
                </>
              )}
            </button>
            <button
              onClick={downloadReport}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <Download className="w-4 h-4" />
              下载报告
            </button>
          </div>

          {/* 基础统计 */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <div className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="text-sm text-gray-600">总字符数</div>
                  <div className="text-2xl font-bold text-gray-900">{stats.totalCharacters.toLocaleString()}</div>
                </div>
                <div className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="text-sm text-gray-600">无空格字符</div>
                  <div className="text-2xl font-bold text-gray-900">{stats.charactersNoSpaces.toLocaleString()}</div>
                </div>
                <div className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="text-sm text-gray-600">总词数</div>
                  <div className="text-2xl font-bold text-gray-900">{stats.totalWords.toLocaleString()}</div>
                </div>
                <div className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="text-sm text-gray-600">总句数</div>
                  <div className="text-2xl font-bold text-gray-900">{stats.totalSentences.toLocaleString()}</div>
                </div>
                <div className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="text-sm text-gray-600">总段落数</div>
                  <div className="text-2xl font-bold text-gray-900">{stats.totalParagraphs.toLocaleString()}</div>
                </div>
                <div className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="text-sm text-gray-600">总行数</div>
                  <div className="text-2xl font-bold text-gray-900">{stats.totalLines.toLocaleString()}</div>
                </div>
                <div className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="text-sm text-gray-600">平均词长</div>
                  <div className="text-2xl font-bold text-gray-900">{stats.averageWordLength.toFixed(1)}</div>
                </div>
                <div className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="text-sm text-gray-600">平均句长</div>
                  <div className="text-2xl font-bold text-gray-900">{stats.averageSentenceLength.toFixed(1)}</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="w-5 h-5 text-blue-600" />
                    <h3 className="font-semibold text-blue-900">阅读时间</h3>
                  </div>
                  <div className="text-3xl font-bold text-blue-600">约 {stats.readingTime} 分钟</div>
                  <div className="text-sm text-blue-700 mt-1">按平均阅读速度计算</div>
                </div>
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <BarChart className="w-5 h-5 text-green-600" />
                    <h3 className="font-semibold text-green-900">演讲时间</h3>
                  </div>
                  <div className="text-3xl font-bold text-green-600">约 {stats.speakingTime} 分钟</div>
                  <div className="text-sm text-green-700 mt-1">按平均演讲速度计算</div>
                </div>
              </div>

              <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                <h3 className="font-semibold text-purple-900 mb-2">词汇多样性</h3>
                <div className="flex items-center gap-4">
                  <div className="text-3xl font-bold text-purple-600">{stats.lexicalDiversity.toFixed(1)}%</div>
                  <div className="text-sm text-purple-700">
                    独特词汇占总词汇的比例，反映文本的词汇丰富程度
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 词频分析 */}
          {activeTab === 'frequency' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">显示前</h3>
                <select
                  value={showTopN}
                  onChange={(e) => setShowTopN(Number(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded-md"
                >
                  <option value={10}>10个</option>
                  <option value={20}>20个</option>
                  <option value={50}>50个</option>
                  <option value={100}>100个</option>
                </select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">高频词汇</h4>
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {getTopFrequencies(stats.wordFrequency, showTopN).map((item, index) => (
                      <div key={item.item} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-500 w-8">{index + 1}.</span>
                          <span className="font-mono">{item.item}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-600">{item.count}次</span>
                          <span className="text-xs text-gray-500">({item.percentage.toFixed(1)}%)</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">高频字符</h4>
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {getTopFrequencies(stats.charFrequency, showTopN).map((item, index) => (
                      <div key={item.item} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-500 w-8">{index + 1}.</span>
                          <span className="font-mono bg-white px-2 py-1 border rounded">&quot;{item.item}&quot;</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-600">{item.count}次</span>
                          <span className="text-xs text-gray-500">({item.percentage.toFixed(1)}%)</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 可视化图表 */}
          {activeTab === 'charts' && (
            <div className="space-y-6">
              <div className="p-6 bg-white border border-gray-200 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">字符类型分布</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {generateChartData()?.map((item) => (
                    <div key={item.name} className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="text-sm">{item.name}</span>
                      <span className="text-sm font-medium ml-auto">
                        {item.value} ({((item.value / stats.totalCharacters) * 100).toFixed(1)}%)
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-4 space-y-2">
                  {generateChartData()?.map((item) => (
                    <div key={item.name} className="relative">
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>{item.name}</span>
                        <span>{item.value}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-6">
                        <div
                          className="h-6 rounded-full transition-all duration-500"
                          style={{
                            width: `${(item.value / stats.totalCharacters) * 100}%`,
                            backgroundColor: item.color,
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="p-6 bg-white border border-gray-200 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">文本组成概览</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-600 mb-2">语言分布</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>中文字符</span>
                        <span className="font-medium">{stats.chinese}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>英文单词</span>
                        <span className="font-medium">{stats.english}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-600 mb-2">结构统计</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>段落数</span>
                        <span className="font-medium">{stats.totalParagraphs}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>句子数</span>
                        <span className="font-medium">{stats.totalSentences}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持中英文混合文本的统计分析</li>
              <li>自动计算阅读时间和演讲时间</li>
              <li>词频和字符频率分析帮助了解文本特征</li>
              <li>词汇多样性指标反映文本的丰富程度</li>
              <li>可导出详细的统计分析报告</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
