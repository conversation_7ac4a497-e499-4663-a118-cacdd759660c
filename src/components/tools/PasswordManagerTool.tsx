'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { AlertCircle, CheckCircle2, Copy, Download, Edit, Eye, EyeOff, Key, Lock, Plus, Search, Shield, Star, Trash2, Upload, User, Globe, Smartphone, CreditCard, FileText, Calendar, AlertTriangle } from 'lucide-react'

interface PasswordEntry {
  id: string
  name: string
  username: string
  password: string
  url: string
  category: string
  notes: string
  createdAt: Date
  updatedAt: Date
  isFavorite: boolean
  tags: string[]
  strength: number
}

interface PasswordStrength {
  score: number
  feedback: string[]
  color: string
  label: string
}

interface GeneratorOptions {
  length: number
  includeUppercase: boolean
  includeLowercase: boolean
  includeNumbers: boolean
  includeSymbols: boolean
  excludeSimilar: boolean
  excludeAmbiguous: boolean
  customCharacters: string
}

const DEFAULT_GENERATOR_OPTIONS: GeneratorOptions = {
  length: 16,
  includeUppercase: true,
  includeLowercase: true,
  includeNumbers: true,
  includeSymbols: true,
  excludeSimilar: false,
  excludeAmbiguous: false,
  customCharacters: ''
}

const CATEGORIES = [
  { value: 'website', label: '网站账号', icon: Globe },
  { value: 'email', label: '邮箱', icon: FileText },
  { value: 'social', label: '社交媒体', icon: User },
  { value: 'work', label: '工作相关', icon: User },
  { value: 'banking', label: '银行金融', icon: CreditCard },
  { value: 'shopping', label: '购物', icon: CreditCard },
  { value: 'app', label: '应用程序', icon: Smartphone },
  { value: 'other', label: '其他', icon: FileText }
]

export default function PasswordManagerTool() {
  const [passwords, setPasswords] = useState<PasswordEntry[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [generatorOptions, setGeneratorOptions] = useState<GeneratorOptions>(DEFAULT_GENERATOR_OPTIONS)
  const [generatedPassword, setGeneratedPassword] = useState('')
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({})
  const [editingId, setEditingId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    password: '',
    url: '',
    category: 'website',
    notes: '',
    tags: ''
  })

  // 从 localStorage 加载密码
  useEffect(() => {
    const savedPasswords = localStorage.getItem('passwordManager')
    if (savedPasswords) {
      try {
        const parsed = JSON.parse(savedPasswords)
        setPasswords(parsed.map((p: any) => ({
          ...p,
          createdAt: new Date(p.createdAt),
          updatedAt: new Date(p.updatedAt)
        })))
      } catch (error) {
        console.error('Error parsing saved passwords:', error)
      }
    }
  }, [])

  // 保存密码到 localStorage
  const savePasswords = useCallback((newPasswords: PasswordEntry[]) => {
    localStorage.setItem('passwordManager', JSON.stringify(newPasswords))
    setPasswords(newPasswords)
  }, [])

  // 生成密码
  const generatePassword = useCallback(() => {
    const options = generatorOptions
    let charset = ''
    
    if (options.includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    if (options.includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz'
    if (options.includeNumbers) charset += '0123456789'
    if (options.includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?'
    
    if (options.excludeSimilar) {
      charset = charset.replace(/[il1Lo0O]/g, '')
    }
    
    if (options.excludeAmbiguous) {
      charset = charset.replace(/[{}[\]()\/\\'"~,;.<>]/g, '')
    }
    
    if (options.customCharacters) {
      charset = options.customCharacters
    }
    
    if (charset === '') {
      return 'abcdefghijklmnopqrstuvwxyz0123456789'.split('').sort(() => Math.random() - 0.5).join('').slice(0, options.length)
    }
    
    let password = ''
    for (let i = 0; i < options.length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length))
    }
    
    setGeneratedPassword(password)
    return password
  }, [generatorOptions])

  // 检查密码强度
  const checkPasswordStrength = useCallback((password: string): PasswordStrength => {
    let score = 0
    const feedback: string[] = []
    
    if (password.length >= 8) score += 1
    else feedback.push('至少8个字符')
    
    if (password.length >= 12) score += 1
    else if (password.length >= 8) feedback.push('建议12个字符以上')
    
    if (/[a-z]/.test(password)) score += 1
    else feedback.push('包含小写字母')
    
    if (/[A-Z]/.test(password)) score += 1
    else feedback.push('包含大写字母')
    
    if (/[0-9]/.test(password)) score += 1
    else feedback.push('包含数字')
    
    if (/[^A-Za-z0-9]/.test(password)) score += 1
    else feedback.push('包含特殊字符')
    
    if (!/(.)\1{2,}/.test(password)) score += 1
    else feedback.push('避免重复字符')
    
    let color = 'red'
    let label = '弱'
    
    if (score >= 6) {
      color = 'green'
      label = '强'
    } else if (score >= 4) {
      color = 'yellow'
      label = '中'
    }
    
    return { score, feedback, color, label }
  }, [])

  // 过滤密码
  const filteredPasswords = passwords.filter(password => {
    const matchesSearch = password.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         password.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         password.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         password.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || password.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  // 添加或更新密码
  const handleSave = useCallback(() => {
    if (!formData.name || !formData.password) return
    
    const now = new Date()
    const tags = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    
    if (editingId) {
      // 更新现有密码
      const updated = passwords.map(password => 
        password.id === editingId 
          ? { ...password, ...formData, tags, updatedAt: now, strength: checkPasswordStrength(formData.password).score }
          : password
      )
      savePasswords(updated)
    } else {
      // 添加新密码
      const newPassword: PasswordEntry = {
        id: Date.now().toString(),
        ...formData,
        tags,
        createdAt: now,
        updatedAt: now,
        isFavorite: false,
        strength: checkPasswordStrength(formData.password).score
      }
      savePasswords([...passwords, newPassword])
    }
    
    // 重置表单
    setFormData({
      name: '',
      username: '',
      password: '',
      url: '',
      category: 'website',
      notes: '',
      tags: ''
    })
    setEditingId(null)
  }, [formData, editingId, passwords, savePasswords, checkPasswordStrength])

  // 删除密码
  const handleDelete = useCallback((id: string) => {
    if (confirm('确定要删除这个密码吗？')) {
      const updated = passwords.filter(password => password.id !== id)
      savePasswords(updated)
    }
  }, [passwords, savePasswords])

  // 编辑密码
  const handleEdit = useCallback((password: PasswordEntry) => {
    setFormData({
      name: password.name,
      username: password.username,
      password: password.password,
      url: password.url,
      category: password.category,
      notes: password.notes,
      tags: password.tags.join(', ')
    })
    setEditingId(password.id)
  }, [])

  // 切换收藏状态
  const toggleFavorite = useCallback((id: string) => {
    const updated = passwords.map(password => 
      password.id === id 
        ? { ...password, isFavorite: !password.isFavorite }
        : password
    )
    savePasswords(updated)
  }, [passwords, savePasswords])

  // 复制到剪贴板
  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // 这里可以添加成功提示
    })
  }, [])

  // 导出密码
  const exportPasswords = useCallback(() => {
    const dataStr = JSON.stringify(passwords, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `passwords_${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }, [passwords])

  // 导入密码
  const importPasswords = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string)
        if (Array.isArray(imported)) {
          const newPasswords = imported.map(p => ({
            ...p,
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            createdAt: new Date(p.createdAt),
            updatedAt: new Date(p.updatedAt)
          }))
          savePasswords([...passwords, ...newPasswords])
        }
      } catch (error) {
        alert('导入失败，请检查文件格式')
      }
    }
    reader.readAsText(file)
    event.target.value = ''
  }, [passwords, savePasswords])

  // 安全分析
  const securityAnalysis = useCallback(() => {
    const weakPasswords = passwords.filter(p => p.strength < 4)
    const oldPasswords = passwords.filter(p => {
      const daysSinceUpdate = (Date.now() - p.updatedAt.getTime()) / (1000 * 60 * 60 * 24)
      return daysSinceUpdate > 90
    })
    const duplicatePasswords = passwords.filter((p, i) => 
      passwords.findIndex(p2 => p2.password === p.password) !== i
    )
    
    return {
      total: passwords.length,
      weak: weakPasswords.length,
      old: oldPasswords.length,
      duplicate: duplicatePasswords.length,
      strong: passwords.length - weakPasswords.length
    }
  }, [passwords])

  const analysis = securityAnalysis()

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="h-5 w-5" />
            密码管理器
          </CardTitle>
          <CardDescription>
            安全地生成、存储和管理您的密码
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="passwords" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="passwords">密码库</TabsTrigger>
              <TabsTrigger value="generator">密码生成器</TabsTrigger>
              <TabsTrigger value="security">安全分析</TabsTrigger>
              <TabsTrigger value="settings">设置</TabsTrigger>
            </TabsList>

            <TabsContent value="passwords" className="space-y-6">
              {/* 搜索和过滤 */}
              <div className="flex gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="搜索密码..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有分类</SelectItem>
                    {CATEGORIES.map(category => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 添加密码表单 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {editingId ? '编辑密码' : '添加密码'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>名称 *</Label>
                      <Input
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        placeholder="例如：GitHub"
                      />
                    </div>
                    <div>
                      <Label>用户名/邮箱</Label>
                      <Input
                        value={formData.username}
                        onChange={(e) => setFormData({...formData, username: e.target.value})}
                        placeholder="用户名或邮箱地址"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>密码 *</Label>
                      <div className="flex gap-2">
                        <Input
                          type="password"
                          value={formData.password}
                          onChange={(e) => setFormData({...formData, password: e.target.value})}
                          placeholder="输入密码"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setFormData({...formData, password: generatePassword()})}
                        >
                          生成
                        </Button>
                      </div>
                      {formData.password && (
                        <div className="mt-2">
                          <div className="flex items-center gap-2">
                            <span className="text-sm">强度：</span>
                            <Badge variant={checkPasswordStrength(formData.password).color === 'green' ? 'default' : 'secondary'}>
                              {checkPasswordStrength(formData.password).label}
                            </Badge>
                          </div>
                        </div>
                      )}
                    </div>
                    <div>
                      <Label>网址</Label>
                      <Input
                        value={formData.url}
                        onChange={(e) => setFormData({...formData, url: e.target.value})}
                        placeholder="https://example.com"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>分类</Label>
                      <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {CATEGORIES.map(category => (
                            <SelectItem key={category.value} value={category.value}>
                              {category.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>标签</Label>
                      <Input
                        value={formData.tags}
                        onChange={(e) => setFormData({...formData, tags: e.target.value})}
                        placeholder="工作,重要,个人（用逗号分隔）"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label>备注</Label>
                    <Input
                      value={formData.notes}
                      onChange={(e) => setFormData({...formData, notes: e.target.value})}
                      placeholder="添加备注..."
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={handleSave} disabled={!formData.name || !formData.password}>
                      <Plus className="w-4 h-4 mr-2" />
                      {editingId ? '更新' : '添加'}
                    </Button>
                    {editingId && (
                      <Button variant="outline" onClick={() => {
                        setEditingId(null)
                        setFormData({
                          name: '',
                          username: '',
                          password: '',
                          url: '',
                          category: 'website',
                          notes: '',
                          tags: ''
                        })
                      }}>
                        取消
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 密码列表 */}
              <div className="space-y-4">
                {filteredPasswords.map((password) => {
                  const IconComponent = CATEGORIES.find(c => c.value === password.category)?.icon || FileText
                  const strength = checkPasswordStrength(password.password)
                  
                  return (
                    <Card key={password.id} className="relative">
                      <CardContent className="pt-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3 flex-1">
                            <div className="p-2 bg-gray-100 rounded-lg">
                              <IconComponent className="h-5 w-5 text-gray-600" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-medium">{password.name}</h3>
                                {password.isFavorite && (
                                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                )}
                                <Badge variant={strength.color === 'green' ? 'default' : 'secondary'}>
                                  {strength.label}
                                </Badge>
                              </div>
                              {password.username && (
                                <p className="text-sm text-gray-600 mb-1">{password.username}</p>
                              )}
                              {password.url && (
                                <p className="text-sm text-blue-600 mb-1">{password.url}</p>
                              )}
                              <div className="flex items-center gap-2 mb-2">
                                <span className="text-sm text-gray-500">
                                  {showPasswords[password.id] ? password.password : '••••••••'}
                                </span>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => setShowPasswords(prev => ({
                                    ...prev,
                                    [password.id]: !prev[password.id]
                                  }))}
                                >
                                  {showPasswords[password.id] ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => copyToClipboard(password.password)}
                                >
                                  <Copy className="h-4 w-4" />
                                </Button>
                              </div>
                              {password.tags.length > 0 && (
                                <div className="flex gap-1 flex-wrap">
                                  {password.tags.map(tag => (
                                    <Badge key={tag} variant="outline" className="text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => toggleFavorite(password.id)}
                            >
                              <Star className={`h-4 w-4 ${password.isFavorite ? 'text-yellow-500 fill-current' : ''}`} />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleEdit(password)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleDelete(password.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
                
                {filteredPasswords.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    {searchTerm || selectedCategory !== 'all' ? '没有找到匹配的密码' : '还没有添加任何密码'}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="generator" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>密码生成器</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>密码长度: {generatorOptions.length}</Label>
                    <Input
                      type="range"
                      min="4"
                      max="50"
                      value={generatorOptions.length}
                      onChange={(e) => setGeneratorOptions(prev => ({
                        ...prev,
                        length: parseInt(e.target.value)
                      }))}
                      className="mt-2"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={generatorOptions.includeUppercase}
                        onChange={(e) => setGeneratorOptions(prev => ({
                          ...prev,
                          includeUppercase: e.target.checked
                        }))}
                        className="rounded"
                      />
                      <Label>大写字母 (A-Z)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={generatorOptions.includeLowercase}
                        onChange={(e) => setGeneratorOptions(prev => ({
                          ...prev,
                          includeLowercase: e.target.checked
                        }))}
                        className="rounded"
                      />
                      <Label>小写字母 (a-z)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={generatorOptions.includeNumbers}
                        onChange={(e) => setGeneratorOptions(prev => ({
                          ...prev,
                          includeNumbers: e.target.checked
                        }))}
                        className="rounded"
                      />
                      <Label>数字 (0-9)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={generatorOptions.includeSymbols}
                        onChange={(e) => setGeneratorOptions(prev => ({
                          ...prev,
                          includeSymbols: e.target.checked
                        }))}
                        className="rounded"
                      />
                      <Label>符号 (!@#$%)</Label>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={generatorOptions.excludeSimilar}
                        onChange={(e) => setGeneratorOptions(prev => ({
                          ...prev,
                          excludeSimilar: e.target.checked
                        }))}
                        className="rounded"
                      />
                      <Label>排除相似字符 (i, l, 1, L, o, 0, O)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={generatorOptions.excludeAmbiguous}
                        onChange={(e) => setGeneratorOptions(prev => ({
                          ...prev,
                          excludeAmbiguous: e.target.checked
                        }))}
                        className="rounded"
                      />
                      <Label>排除模糊字符 ({`{}, [], (), /, \\, ', ", ~, ,, ;, ., <, >`})</Label>
                    </div>
                  </div>
                  
                  <div>
                    <Label>自定义字符集</Label>
                    <Input
                      value={generatorOptions.customCharacters}
                      onChange={(e) => setGeneratorOptions(prev => ({
                        ...prev,
                        customCharacters: e.target.value
                      }))}
                      placeholder="留空使用默认字符集"
                    />
                  </div>
                  
                  <Button onClick={generatePassword} className="w-full">
                    <Key className="w-4 h-4 mr-2" />
                    生成密码
                  </Button>
                  
                  {generatedPassword && (
                    <div className="space-y-2">
                      <Label>生成的密码:</Label>
                      <div className="flex gap-2">
                        <Input
                          value={generatedPassword}
                          readOnly
                          className="font-mono"
                        />
                        <Button
                          variant="outline"
                          onClick={() => copyToClipboard(generatedPassword)}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">强度：</span>
                        <Badge variant={checkPasswordStrength(generatedPassword).color === 'green' ? 'default' : 'secondary'}>
                          {checkPasswordStrength(generatedPassword).label}
                        </Badge>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>安全分析</CardTitle>
                  <CardDescription>
                    检查您的密码库安全状况
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{analysis.total}</div>
                      <div className="text-sm text-gray-600">总计</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{analysis.strong}</div>
                      <div className="text-sm text-gray-600">强密码</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{analysis.weak}</div>
                      <div className="text-sm text-gray-600">弱密码</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{analysis.old}</div>
                      <div className="text-sm text-gray-600">旧密码</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">{analysis.duplicate}</div>
                      <div className="text-sm text-gray-600">重复密码</div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    {analysis.weak > 0 && (
                      <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
                        <AlertTriangle className="h-5 w-5 text-red-600" />
                        <span className="text-red-800">
                          您有 {analysis.weak} 个弱密码需要更换
                        </span>
                      </div>
                    )}
                    
                    {analysis.old > 0 && (
                      <div className="flex items-center gap-2 p-3 bg-orange-50 rounded-lg">
                        <Calendar className="h-5 w-5 text-orange-600" />
                        <span className="text-orange-800">
                          您有 {analysis.old} 个密码超过90天未更新
                        </span>
                      </div>
                    )}
                    
                    {analysis.duplicate > 0 && (
                      <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg">
                        <Copy className="h-5 w-5 text-purple-600" />
                        <span className="text-purple-800">
                          您有 {analysis.duplicate} 个重复密码
                        </span>
                      </div>
                    )}
                    
                    {analysis.weak === 0 && analysis.old === 0 && analysis.duplicate === 0 && (
                      <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                        <span className="text-green-800">
                          您的密码库安全状况良好！
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>数据管理</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-4">
                    <Button onClick={exportPasswords} variant="outline">
                      <Download className="w-4 h-4 mr-2" />
                      导出密码
                    </Button>
                    <div>
                      <Input
                        type="file"
                        accept=".json"
                        onChange={importPasswords}
                        className="hidden"
                        id="import-file"
                      />
                      <Button
                        variant="outline"
                        onClick={() => document.getElementById('import-file')?.click()}
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        导入密码
                      </Button>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                      <span className="font-medium text-yellow-800">安全提醒</span>
                    </div>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      <li>• 所有密码都存储在您的浏览器本地，不会上传到服务器</li>
                      <li>• 定期备份您的密码数据</li>
                      <li>• 不要在不安全的设备上使用此工具</li>
                      <li>• 建议定期更换重要账户的密码</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
