'use client'

import React, { useState, useCallback, useMemo, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Copy, Download, RotateCcw, Eye, Settings, Grid, Plus, Trash2, Move, ArrowRight, ArrowDown } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface GridItem {
  id: string
  name: string
  gridColumn: string
  gridRow: string
  backgroundColor: string
  content: string
  justifySelf: string
  alignSelf: string
}

interface GridConfig {
  columns: string[]
  rows: string[]
  gap: { row: number; column: number }
  justifyContent: string
  alignContent: string
  justifyItems: string
  alignItems: string
  autoColumns: string
  autoRows: string
  autoFlow: string
  containerWidth: number
  containerHeight: number
}

interface GridTemplate {
  name: string
  description: string
  config: GridConfig
  items: Omit<GridItem, 'id'>[]
}

const gridTemplates: GridTemplate[] = [
  {
    name: '经典布局',
    description: '页头-侧边栏-内容-页脚布局',
    config: {
      columns: ['1fr'],
      rows: ['80px', '1fr', '60px'],
      gap: { row: 10, column: 10 },
      justifyContent: 'stretch',
      alignContent: 'stretch',
      justifyItems: 'stretch',
      alignItems: 'stretch',
      autoColumns: '1fr',
      autoRows: 'auto',
      autoFlow: 'row',
      containerWidth: 100,
      containerHeight: 400
    },
    items: [
      { name: 'Header', gridColumn: '1', gridRow: '1', backgroundColor: '#3b82f6', content: '页头', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Main', gridColumn: '1', gridRow: '2', backgroundColor: '#10b981', content: '主内容', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Footer', gridColumn: '1', gridRow: '3', backgroundColor: '#f59e0b', content: '页脚', justifySelf: 'stretch', alignSelf: 'stretch' }
    ]
  },
  {
    name: '侧边栏布局',
    description: '带侧边栏的两栏布局',
    config: {
      columns: ['250px', '1fr'],
      rows: ['1fr'],
      gap: { row: 20, column: 20 },
      justifyContent: 'stretch',
      alignContent: 'stretch',
      justifyItems: 'stretch',
      alignItems: 'stretch',
      autoColumns: '1fr',
      autoRows: 'auto',
      autoFlow: 'row',
      containerWidth: 100,
      containerHeight: 300
    },
    items: [
      { name: 'Sidebar', gridColumn: '1', gridRow: '1', backgroundColor: '#8b5cf6', content: '侧边栏', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Content', gridColumn: '2', gridRow: '1', backgroundColor: '#06b6d4', content: '内容区', justifySelf: 'stretch', alignSelf: 'stretch' }
    ]
  },
  {
    name: '圣杯布局',
    description: '页头-三栏-页脚的经典布局',
    config: {
      columns: ['200px', '1fr', '200px'],
      rows: ['80px', '1fr', '60px'],
      gap: { row: 15, column: 15 },
      justifyContent: 'stretch',
      alignContent: 'stretch',
      justifyItems: 'stretch',
      alignItems: 'stretch',
      autoColumns: '1fr',
      autoRows: 'auto',
      autoFlow: 'row',
      containerWidth: 100,
      containerHeight: 400
    },
    items: [
      { name: 'Header', gridColumn: '1 / -1', gridRow: '1', backgroundColor: '#3b82f6', content: '页头', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Sidebar', gridColumn: '1', gridRow: '2', backgroundColor: '#8b5cf6', content: '左侧栏', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Main', gridColumn: '2', gridRow: '2', backgroundColor: '#10b981', content: '主内容', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Aside', gridColumn: '3', gridRow: '2', backgroundColor: '#ec4899', content: '右侧栏', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Footer', gridColumn: '1 / -1', gridRow: '3', backgroundColor: '#f59e0b', content: '页脚', justifySelf: 'stretch', alignSelf: 'stretch' }
    ]
  },
  {
    name: '卡片网格',
    description: '响应式卡片布局',
    config: {
      columns: ['repeat(auto-fit, minmax(200px, 1fr))'],
      rows: ['auto'],
      gap: { row: 20, column: 20 },
      justifyContent: 'stretch',
      alignContent: 'start',
      justifyItems: 'stretch',
      alignItems: 'stretch',
      autoColumns: 'minmax(200px, 1fr)',
      autoRows: 'auto',
      autoFlow: 'row',
      containerWidth: 100,
      containerHeight: 300
    },
    items: [
      { name: 'Card 1', gridColumn: 'auto', gridRow: 'auto', backgroundColor: '#3b82f6', content: '卡片 1', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Card 2', gridColumn: 'auto', gridRow: 'auto', backgroundColor: '#10b981', content: '卡片 2', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Card 3', gridColumn: 'auto', gridRow: 'auto', backgroundColor: '#f59e0b', content: '卡片 3', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Card 4', gridColumn: 'auto', gridRow: 'auto', backgroundColor: '#ec4899', content: '卡片 4', justifySelf: 'stretch', alignSelf: 'stretch' }
    ]
  },
  {
    name: '仪表板',
    description: '复杂的仪表板布局',
    config: {
      columns: ['1fr', '1fr', '1fr', '1fr'],
      rows: ['100px', '200px', '150px'],
      gap: { row: 15, column: 15 },
      justifyContent: 'stretch',
      alignContent: 'stretch',
      justifyItems: 'stretch',
      alignItems: 'stretch',
      autoColumns: '1fr',
      autoRows: 'auto',
      autoFlow: 'row',
      containerWidth: 100,
      containerHeight: 450
    },
    items: [
      { name: 'Title', gridColumn: '1 / -1', gridRow: '1', backgroundColor: '#1f2937', content: '仪表板标题', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Chart 1', gridColumn: '1 / 3', gridRow: '2', backgroundColor: '#3b82f6', content: '主图表', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Stats', gridColumn: '3 / 5', gridRow: '2', backgroundColor: '#10b981', content: '统计数据', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Widget 1', gridColumn: '1', gridRow: '3', backgroundColor: '#f59e0b', content: '小组件1', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Widget 2', gridColumn: '2', gridRow: '3', backgroundColor: '#ec4899', content: '小组件2', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Widget 3', gridColumn: '3', gridRow: '3', backgroundColor: '#8b5cf6', content: '小组件3', justifySelf: 'stretch', alignSelf: 'stretch' },
      { name: 'Activity', gridColumn: '4', gridRow: '3', backgroundColor: '#06b6d4', content: '活动', justifySelf: 'stretch', alignSelf: 'stretch' }
    ]
  }
]

const alignmentOptions = [
  { value: 'stretch', label: '拉伸 (stretch)' },
  { value: 'start', label: '开始 (start)' },
  { value: 'end', label: '结束 (end)' },
  { value: 'center', label: '居中 (center)' },
  { value: 'baseline', label: '基线 (baseline)' }
]

const justifyOptions = [
  { value: 'stretch', label: '拉伸 (stretch)' },
  { value: 'start', label: '开始 (start)' },
  { value: 'end', label: '结束 (end)' },
  { value: 'center', label: '居中 (center)' },
  { value: 'space-between', label: '两端对齐 (space-between)' },
  { value: 'space-around', label: '环绕分布 (space-around)' },
  { value: 'space-evenly', label: '均匀分布 (space-evenly)' }
]

const autoFlowOptions = [
  { value: 'row', label: '行优先 (row)' },
  { value: 'column', label: '列优先 (column)' },
  { value: 'row dense', label: '行优先密集 (row dense)' },
  { value: 'column dense', label: '列优先密集 (column dense)' }
]

export default function CssGridGeneratorTool() {
  const [config, setConfig] = useState<GridConfig>({
    columns: ['1fr', '1fr', '1fr'],
    rows: ['100px', '100px'],
    gap: { row: 10, column: 10 },
    justifyContent: 'stretch',
    alignContent: 'stretch',
    justifyItems: 'stretch',
    alignItems: 'stretch',
    autoColumns: '1fr',
    autoRows: 'auto',
    autoFlow: 'row',
    containerWidth: 100,
    containerHeight: 300
  })

  const [items, setItems] = useState<GridItem[]>([
    {
      id: '1',
      name: 'Item 1',
      gridColumn: '1',
      gridRow: '1',
      backgroundColor: '#3b82f6',
      content: '项目 1',
      justifySelf: 'stretch',
      alignSelf: 'stretch'
    },
    {
      id: '2',
      name: 'Item 2',
      gridColumn: '2',
      gridRow: '1',
      backgroundColor: '#10b981',
      content: '项目 2',
      justifySelf: 'stretch',
      alignSelf: 'stretch'
    }
  ])

  const [selectedItem, setSelectedItem] = useState<string | null>('1')
  const [showGridLines, setShowGridLines] = useState(true)
  const [activeTab, setActiveTab] = useState('layout')
  const containerRef = useRef<HTMLDivElement>(null)

  const updateConfig = useCallback((updates: Partial<GridConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }))
  }, [])

  const updateItem = useCallback((id: string, updates: Partial<GridItem>) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, ...updates } : item
    ))
  }, [])

  const addColumn = useCallback(() => {
    setConfig(prev => ({
      ...prev,
      columns: [...prev.columns, '1fr']
    }))
  }, [])

  const removeColumn = useCallback((index: number) => {
    if (config.columns.length <= 1) return
    setConfig(prev => ({
      ...prev,
      columns: prev.columns.filter((_, i) => i !== index)
    }))
  }, [config.columns.length])

  const updateColumn = useCallback((index: number, value: string) => {
    setConfig(prev => ({
      ...prev,
      columns: prev.columns.map((col, i) => i === index ? value : col)
    }))
  }, [])

  const addRow = useCallback(() => {
    setConfig(prev => ({
      ...prev,
      rows: [...prev.rows, 'auto']
    }))
  }, [])

  const removeRow = useCallback((index: number) => {
    if (config.rows.length <= 1) return
    setConfig(prev => ({
      ...prev,
      rows: prev.rows.filter((_, i) => i !== index)
    }))
  }, [config.rows.length])

  const updateRow = useCallback((index: number, value: string) => {
    setConfig(prev => ({
      ...prev,
      rows: prev.rows.map((row, i) => i === index ? value : row)
    }))
  }, [])

  const addItem = useCallback(() => {
    const newItem: GridItem = {
      id: Date.now().toString(),
      name: `Item ${items.length + 1}`,
      gridColumn: '1',
      gridRow: '1',
      backgroundColor: `#${Math.floor(Math.random()*16777215).toString(16)}`,
      content: `项目 ${items.length + 1}`,
      justifySelf: 'stretch',
      alignSelf: 'stretch'
    }
    setItems(prev => [...prev, newItem])
    setSelectedItem(newItem.id)
  }, [items.length])

  const removeItem = useCallback((id: string) => {
    setItems(prev => prev.filter(item => item.id !== id))
    if (selectedItem === id) {
      setSelectedItem(items.find(item => item.id !== id)?.id || null)
    }
  }, [selectedItem, items])

  const applyTemplate = useCallback((template: GridTemplate) => {
    setConfig(template.config)
    const newItems = template.items.map((item, index) => ({
      ...item,
      id: (index + 1).toString()
    }))
    setItems(newItems)
    setSelectedItem(newItems[0]?.id || null)
    toast.success(`已应用模板: ${template.name}`)
  }, [])

  const generateGridCSS = useMemo(() => {
    const containerCSS = `
.grid-container {
  display: grid;
  grid-template-columns: ${config.columns.join(' ')};
  grid-template-rows: ${config.rows.join(' ')};
  gap: ${config.gap.row}px ${config.gap.column}px;
  justify-content: ${config.justifyContent};
  align-content: ${config.alignContent};
  justify-items: ${config.justifyItems};
  align-items: ${config.alignItems};
  grid-auto-columns: ${config.autoColumns};
  grid-auto-rows: ${config.autoRows};
  grid-auto-flow: ${config.autoFlow};
  width: ${config.containerWidth}%;
  height: ${config.containerHeight}px;
}`

    const itemsCSS = items.map(item => `
.grid-item-${item.id} {
  grid-column: ${item.gridColumn};
  grid-row: ${item.gridRow};
  background-color: ${item.backgroundColor};
  justify-self: ${item.justifySelf};
  align-self: ${item.alignSelf};
  padding: 1rem;
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}`).join('')

    return containerCSS + itemsCSS
  }, [config, items])

  const copyCSS = useCallback(() => {
    navigator.clipboard.writeText(generateGridCSS)
    toast.success('CSS代码已复制到剪贴板')
  }, [generateGridCSS])

  const copyHTML = useCallback(() => {
    const htmlCode = `<div class="grid-container">
${items.map(item => `  <div class="grid-item-${item.id}">${item.content}</div>`).join('\n')}
</div>`
    
    navigator.clipboard.writeText(htmlCode)
    toast.success('HTML代码已复制到剪贴板')
  }, [items])

  const exportPreview = useCallback(() => {
    const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSS Grid Layout</title>
  <style>
    body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
    ${generateGridCSS}
  </style>
</head>
<body>
  <div class="grid-container">
${items.map(item => `    <div class="grid-item-${item.id}">${item.content}</div>`).join('\n')}
  </div>
</body>
</html>`

    const blob = new Blob([htmlContent], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'grid-layout.html'
    a.click()
    URL.revokeObjectURL(url)
    toast.success('HTML文件已导出')
  }, [generateGridCSS, items])

  const resetGrid = useCallback(() => {
    setConfig({
      columns: ['1fr', '1fr', '1fr'],
      rows: ['100px', '100px'],
      gap: { row: 10, column: 10 },
      justifyContent: 'stretch',
      alignContent: 'stretch',
      justifyItems: 'stretch',
      alignItems: 'stretch',
      autoColumns: '1fr',
      autoRows: 'auto',
      autoFlow: 'row',
      containerWidth: 100,
      containerHeight: 300
    })
    setItems([
      {
        id: '1',
        name: 'Item 1',
        gridColumn: '1',
        gridRow: '1',
        backgroundColor: '#3b82f6',
        content: '项目 1',
        justifySelf: 'stretch',
        alignSelf: 'stretch'
      }
    ])
    setSelectedItem('1')
    toast.success('已重置网格布局')
  }, [])

  const selectedItemData = items.find(item => item.id === selectedItem)

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          CSS Grid生成器
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          可视化网格布局编辑器，拖拽设计、实时预览、代码生成一体化解决方案
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 预览区域 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                网格预览
              </CardTitle>
              <div className="flex items-center gap-2">
                <Switch
                  id="show-grid"
                  checked={showGridLines}
                  onCheckedChange={setShowGridLines}
                />
                <Label htmlFor="show-grid" className="text-sm">显示网格线</Label>
                <Button variant="outline" size="sm" onClick={resetGrid}>
                  <RotateCcw className="h-4 w-4 mr-1" />
                  重置
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* 容器尺寸控制 */}
            <div className="mb-4 grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>容器宽度: {config.containerWidth}%</Label>
                <Slider
                  value={[config.containerWidth]}
                  onValueChange={([value]) => updateConfig({ containerWidth: value })}
                  min={50}
                  max={100}
                  step={5}
                />
              </div>
              <div className="space-y-2">
                <Label>容器高度: {config.containerHeight}px</Label>
                <Slider
                  value={[config.containerHeight]}
                  onValueChange={([value]) => updateConfig({ containerHeight: value })}
                  min={200}
                  max={600}
                  step={10}
                />
              </div>
            </div>

            {/* 网格预览容器 */}
            <div 
              className="relative border-2 border-dashed border-gray-300 dark:border-gray-600 p-4 overflow-auto"
              style={{ minHeight: `${config.containerHeight + 50}px` }}
            >
              <div
                ref={containerRef}
                className={`relative ${showGridLines ? 'grid-lines' : ''}`}
                style={{
                  display: 'grid',
                  gridTemplateColumns: config.columns.join(' '),
                  gridTemplateRows: config.rows.join(' '),
                  gap: `${config.gap.row}px ${config.gap.column}px`,
                  justifyContent: config.justifyContent,
                  alignContent: config.alignContent,
                  justifyItems: config.justifyItems,
                  alignItems: config.alignItems,
                  gridAutoColumns: config.autoColumns,
                  gridAutoRows: config.autoRows,
                  gridAutoFlow: config.autoFlow,
                  width: `${config.containerWidth}%`,
                  height: `${config.containerHeight}px`,
                  backgroundColor: showGridLines ? 'rgba(59, 130, 246, 0.05)' : 'transparent',
                  backgroundImage: showGridLines ? 
                    `linear-gradient(rgba(59, 130, 246, 0.2) 1px, transparent 1px),
                     linear-gradient(90deg, rgba(59, 130, 246, 0.2) 1px, transparent 1px)` : 'none',
                  backgroundSize: showGridLines ? 
                    `calc(100% / ${config.columns.length}) calc(100% / ${config.rows.length})` : 'auto'
                }}
              >
                {items.map(item => (
                  <div
                    key={item.id}
                    className={`grid-item cursor-pointer transition-all duration-200 ${
                      selectedItem === item.id ? 'ring-2 ring-blue-500 ring-offset-2' : ''
                    }`}
                    style={{
                      gridColumn: item.gridColumn,
                      gridRow: item.gridRow,
                      backgroundColor: item.backgroundColor,
                      justifySelf: item.justifySelf,
                      alignSelf: item.alignSelf,
                      padding: '1rem',
                      borderRadius: '0.5rem',
                      color: 'white',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      textAlign: 'center',
                      fontSize: '0.875rem',
                      boxShadow: selectedItem === item.id ? '0 4px 12px rgba(0,0,0,0.15)' : '0 2px 4px rgba(0,0,0,0.1)'
                    }}
                    onClick={() => setSelectedItem(item.id)}
                  >
                    {item.content}
                  </div>
                ))}
              </div>
              
              {/* 网格信息显示 */}
              <div className="absolute bottom-2 left-2 text-xs text-gray-500 bg-white dark:bg-gray-800 px-2 py-1 rounded">
                {config.columns.length} × {config.rows.length} 网格 | {items.length} 项目
              </div>
            </div>

            {/* 快速操作 */}
            <div className="mt-4 flex flex-wrap gap-2">
              <Button onClick={addItem} size="sm" variant="outline">
                <Plus className="h-4 w-4 mr-1" />
                添加项目
              </Button>
              <Button onClick={addColumn} size="sm" variant="outline">
                <ArrowRight className="h-4 w-4 mr-1" />
                添加列
              </Button>
              <Button onClick={addRow} size="sm" variant="outline">
                <ArrowDown className="h-4 w-4 mr-1" />
                添加行
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 控制面板 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              网格设置
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="layout">布局</TabsTrigger>
                <TabsTrigger value="items">项目</TabsTrigger>
                <TabsTrigger value="templates">模板</TabsTrigger>
              </TabsList>

              <TabsContent value="layout" className="space-y-4">
                {/* 列设置 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="font-semibold">列定义</Label>
                    <Button onClick={addColumn} size="sm" variant="outline">
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                  
                  {config.columns.map((column, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        value={column}
                        onChange={(e) => updateColumn(index, e.target.value)}
                        placeholder="1fr, 100px, auto..."
                        className="flex-1 text-sm"
                      />
                      <Button
                        onClick={() => removeColumn(index)}
                        size="sm"
                        variant="ghost"
                        disabled={config.columns.length <= 1}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>

                <Separator />

                {/* 行设置 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="font-semibold">行定义</Label>
                    <Button onClick={addRow} size="sm" variant="outline">
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                  
                  {config.rows.map((row, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        value={row}
                        onChange={(e) => updateRow(index, e.target.value)}
                        placeholder="auto, 100px, 1fr..."
                        className="flex-1 text-sm"
                      />
                      <Button
                        onClick={() => removeRow(index)}
                        size="sm"
                        variant="ghost"
                        disabled={config.rows.length <= 1}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>

                <Separator />

                {/* 间距设置 */}
                <div className="space-y-3">
                  <Label className="font-semibold">间距设置</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label className="text-sm">行间距: {config.gap.row}px</Label>
                      <Slider
                        value={[config.gap.row]}
                        onValueChange={([value]) => updateConfig({ gap: { ...config.gap, row: value } })}
                        min={0}
                        max={50}
                        step={1}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm">列间距: {config.gap.column}px</Label>
                      <Slider
                        value={[config.gap.column]}
                        onValueChange={([value]) => updateConfig({ gap: { ...config.gap, column: value } })}
                        min={0}
                        max={50}
                        step={1}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 对齐设置 */}
                <div className="space-y-3">
                  <Label className="font-semibold">对齐设置</Label>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label className="text-sm">水平对齐</Label>
                      <Select value={config.justifyContent} onValueChange={(value) => updateConfig({ justifyContent: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {justifyOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-sm">垂直对齐</Label>
                      <Select value={config.alignContent} onValueChange={(value) => updateConfig({ alignContent: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {alignmentOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label className="text-sm">项目水平对齐</Label>
                      <Select value={config.justifyItems} onValueChange={(value) => updateConfig({ justifyItems: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {alignmentOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-sm">项目垂直对齐</Label>
                      <Select value={config.alignItems} onValueChange={(value) => updateConfig({ alignItems: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {alignmentOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 自动设置 */}
                <div className="space-y-3">
                  <Label className="font-semibold">自动网格</Label>
                  
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-sm">自动列</Label>
                      <Input
                        value={config.autoColumns}
                        onChange={(e) => updateConfig({ autoColumns: e.target.value })}
                        placeholder="1fr, 100px, auto..."
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-sm">自动行</Label>
                      <Input
                        value={config.autoRows}
                        onChange={(e) => updateConfig({ autoRows: e.target.value })}
                        placeholder="auto, 100px, minmax(100px, auto)..."
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label className="text-sm">自动流动</Label>
                      <Select value={config.autoFlow} onValueChange={(value) => updateConfig({ autoFlow: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {autoFlowOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="items" className="space-y-4">
                {/* 项目列表 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="font-semibold">网格项目</Label>
                    <Button onClick={addItem} size="sm">
                      <Plus className="h-3 w-3 mr-1" />
                      添加项目
                    </Button>
                  </div>

                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {items.map(item => (
                      <div
                        key={item.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedItem === item.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedItem(item.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div
                              className="w-4 h-4 rounded"
                              style={{ backgroundColor: item.backgroundColor }}
                            />
                            <span className="font-medium text-sm">{item.name}</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              removeItem(item.id)
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          列: {item.gridColumn} | 行: {item.gridRow}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 选中项目编辑 */}
                {selectedItemData && (
                  <div className="space-y-4 pt-4 border-t">
                    <Label className="font-semibold">编辑项目: {selectedItemData.name}</Label>
                    
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Label className="text-sm">项目名称</Label>
                        <Input
                          value={selectedItemData.name}
                          onChange={(e) => updateItem(selectedItem!, { name: e.target.value })}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label className="text-sm">显示内容</Label>
                        <Input
                          value={selectedItemData.content}
                          onChange={(e) => updateItem(selectedItem!, { content: e.target.value })}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div className="space-y-2">
                          <Label className="text-sm">网格列</Label>
                          <Input
                            value={selectedItemData.gridColumn}
                            onChange={(e) => updateItem(selectedItem!, { gridColumn: e.target.value })}
                            placeholder="1, 1/3, span 2..."
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">网格行</Label>
                          <Input
                            value={selectedItemData.gridRow}
                            onChange={(e) => updateItem(selectedItem!, { gridRow: e.target.value })}
                            placeholder="1, 1/3, span 2..."
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm">背景色</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            type="color"
                            value={selectedItemData.backgroundColor}
                            onChange={(e) => updateItem(selectedItem!, { backgroundColor: e.target.value })}
                            className="w-12 h-8 p-0 border-0"
                          />
                          <Input
                            value={selectedItemData.backgroundColor}
                            onChange={(e) => updateItem(selectedItem!, { backgroundColor: e.target.value })}
                            className="flex-1 text-sm"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div className="space-y-2">
                          <Label className="text-sm">水平对齐</Label>
                          <Select 
                            value={selectedItemData.justifySelf} 
                            onValueChange={(value) => updateItem(selectedItem!, { justifySelf: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {alignmentOptions.map(option => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">垂直对齐</Label>
                          <Select 
                            value={selectedItemData.alignSelf} 
                            onValueChange={(value) => updateItem(selectedItem!, { alignSelf: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {alignmentOptions.map(option => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="templates" className="space-y-4">
                <div className="space-y-4">
                  {gridTemplates.map((template, index) => (
                    <div
                      key={index}
                      className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-gray-300 dark:hover:border-gray-600 cursor-pointer transition-colors"
                      onClick={() => applyTemplate(template)}
                    >
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-gray-900 dark:text-gray-100">
                            {template.name}
                          </h3>
                          <Badge variant="outline">
                            {template.items.length} 项目
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500">
                          {template.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-400">
                          <span>列: {template.config.columns.length}</span>
                          <span>行: {template.config.rows.length}</span>
                          <span>间距: {template.config.gap.row}px</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* 代码导出 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Grid className="h-5 w-5" />
            生成的代码
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Tabs defaultValue="css" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="css">CSS</TabsTrigger>
              <TabsTrigger value="html">HTML</TabsTrigger>
              <TabsTrigger value="react">React</TabsTrigger>
            </TabsList>

            <TabsContent value="css" className="space-y-3">
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <pre className="text-sm overflow-x-auto">
                  <code>{generateGridCSS}</code>
                </pre>
              </div>
              <Button onClick={copyCSS} className="w-full">
                <Copy className="h-4 w-4 mr-2" />
                复制CSS代码
              </Button>
            </TabsContent>

            <TabsContent value="html" className="space-y-3">
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <pre className="text-sm overflow-x-auto">
                  <code>
{`<div class="grid-container">
${items.map(item => `  <div class="grid-item-${item.id}">${item.content}</div>`).join('\n')}
</div>`}
                  </code>
                </pre>
              </div>
              <Button onClick={copyHTML} className="w-full">
                <Copy className="h-4 w-4 mr-2" />
                复制HTML代码
              </Button>
            </TabsContent>

            <TabsContent value="react" className="space-y-3">
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <pre className="text-sm overflow-x-auto">
                  <code>
{`const GridLayout = () => {
  return (
    <div className="grid-container">
${items.map(item => `      <div className="grid-item-${item.id}">${item.content}</div>`).join('\n')}
    </div>
  );
};`}
                  </code>
                </pre>
              </div>
              <Button onClick={() => {
                const reactCode = `const GridLayout = () => {
  return (
    <div className="grid-container">
${items.map(item => `      <div className="grid-item-${item.id}">${item.content}</div>`).join('\n')}
    </div>
  );
};`
                navigator.clipboard.writeText(reactCode)
                toast.success('React代码已复制到剪贴板')
              }} className="w-full">
                <Copy className="h-4 w-4 mr-2" />
                复制React代码
              </Button>
            </TabsContent>
          </Tabs>

          <Separator />

          <div className="flex gap-2">
            <Button onClick={exportPreview} variant="outline" className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              导出HTML文件
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}