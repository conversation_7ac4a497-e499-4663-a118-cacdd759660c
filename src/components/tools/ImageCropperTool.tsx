'use client'

import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { Upload, Download, Crop, RotateCw, Move, ZoomIn, ZoomOut, RefreshCw } from 'lucide-react'
import Image from 'next/image'

interface CropArea {
  x: number
  y: number
  width: number
  height: number
}

interface ImageTransform {
  scale: number
  rotation: number
  translateX: number
  translateY: number
}

export default function ImageCropperTool() {
  const [originalImage, setOriginalImage] = useState<string | null>(null)
  const [croppedImage, setCroppedImage] = useState<string | null>(null)
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 })
  const [cropArea, setCropArea] = useState<CropArea>({ x: 0, y: 0, width: 200, height: 200 })
  const [transform, setTransform] = useState<ImageTransform>({
    scale: 1,
    rotation: 0,
    translateX: 0,
    translateY: 0
  })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [aspectRatio, setAspectRatio] = useState<string>('free')
  const [isProcessing, setIsProcessing] = useState(false)
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const previewCanvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 预设的宽高比
  const aspectRatios = useMemo(() => [
    { label: '自由', value: 'free', ratio: null },
    { label: '1:1', value: '1:1', ratio: 1 },
    { label: '4:3', value: '4:3', ratio: 4/3 },
    { label: '16:9', value: '16:9', ratio: 16/9 },
    { label: '3:2', value: '3:2', ratio: 3/2 },
    { label: '2:3', value: '2:3', ratio: 2/3 },
    { label: '9:16', value: '9:16', ratio: 9/16 },
  ], [])

  // 处理图片上传
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      const img = new window.Image()
      img.onload = () => {
        setImageSize({ width: img.width, height: img.height })
        setOriginalImage(event.target?.result as string)
        
        // 初始化裁剪区域为图片中心
        const initialSize = Math.min(img.width, img.height) * 0.8
        setCropArea({
          x: (img.width - initialSize) / 2,
          y: (img.height - initialSize) / 2,
          width: initialSize,
          height: initialSize
        })
        
        // 重置变换
        setTransform({
          scale: 1,
          rotation: 0,
          translateX: 0,
          translateY: 0
        })
      }
      img.src = event.target?.result as string
    }
    reader.readAsDataURL(file)
  }

  // 更新裁剪区域
  const updateCropArea = useCallback((newArea: Partial<CropArea>) => {
    setCropArea(prev => {
      const updated = { ...prev, ...newArea }
      
      // 确保裁剪区域在图片范围内
      updated.x = Math.max(0, Math.min(updated.x, imageSize.width - updated.width))
      updated.y = Math.max(0, Math.min(updated.y, imageSize.height - updated.height))
      updated.width = Math.max(50, Math.min(updated.width, imageSize.width - updated.x))
      updated.height = Math.max(50, Math.min(updated.height, imageSize.height - updated.y))
      
      // 应用宽高比
      const ratio = aspectRatios.find(r => r.value === aspectRatio)?.ratio
      if (ratio) {
        if (newArea.width !== undefined) {
          updated.height = updated.width / ratio
        } else if (newArea.height !== undefined) {
          updated.width = updated.height * ratio
        }
      }
      
      return updated
    })
  }, [aspectRatio, imageSize, aspectRatios])

  // 处理鼠标事件
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX, y: e.clientY })
  }

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging) return
    
    const deltaX = e.clientX - dragStart.x
    const deltaY = e.clientY - dragStart.y
    
    updateCropArea({
      x: cropArea.x + deltaX,
      y: cropArea.y + deltaY
    })
    
    setDragStart({ x: e.clientX, y: e.clientY })
  }, [isDragging, dragStart, cropArea, updateCropArea])

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // 绘制预览
  useEffect(() => {
    if (!originalImage || !previewCanvasRef.current) return
    
    const canvas = previewCanvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const img = new window.Image()
    img.onload = () => {
      // 设置画布大小
      const maxSize = 500
      const scale = Math.min(maxSize / img.width, maxSize / img.height, 1)
      canvas.width = img.width * scale
      canvas.height = img.height * scale
      
      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // 应用变换
      ctx.save()
      ctx.translate(canvas.width / 2, canvas.height / 2)
      ctx.rotate((transform.rotation * Math.PI) / 180)
      ctx.scale(transform.scale * scale, transform.scale * scale)
      ctx.translate(-img.width / 2, -img.height / 2)
      
      // 绘制图片
      ctx.drawImage(img, 0, 0)
      ctx.restore()
      
      // 绘制裁剪框
      ctx.strokeStyle = '#3B82F6'
      ctx.lineWidth = 2
      ctx.setLineDash([5, 5])
      ctx.strokeRect(
        cropArea.x * scale,
        cropArea.y * scale,
        cropArea.width * scale,
        cropArea.height * scale
      )
      
      // 绘制裁剪框外的遮罩
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
      ctx.fillRect(0, 0, canvas.width, cropArea.y * scale)
      ctx.fillRect(0, cropArea.y * scale, cropArea.x * scale, cropArea.height * scale)
      ctx.fillRect(
        (cropArea.x + cropArea.width) * scale,
        cropArea.y * scale,
        canvas.width - (cropArea.x + cropArea.width) * scale,
        cropArea.height * scale
      )
      ctx.fillRect(
        0,
        (cropArea.y + cropArea.height) * scale,
        canvas.width,
        canvas.height - (cropArea.y + cropArea.height) * scale
      )
    }
    img.src = originalImage
  }, [originalImage, cropArea, transform])

  // 执行裁剪
  const performCrop = useCallback(() => {
    if (!originalImage || !canvasRef.current) return
    
    setIsProcessing(true)
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const img = new window.Image()
    img.onload = () => {
      // 设置画布大小为裁剪区域大小
      canvas.width = cropArea.width
      canvas.height = cropArea.height
      
      // 应用变换并裁剪
      ctx.save()
      ctx.translate(canvas.width / 2, canvas.height / 2)
      ctx.rotate((transform.rotation * Math.PI) / 180)
      ctx.scale(transform.scale, transform.scale)
      ctx.translate(-canvas.width / 2, -canvas.height / 2)
      
      // 绘制裁剪区域
      ctx.drawImage(
        img,
        cropArea.x,
        cropArea.y,
        cropArea.width,
        cropArea.height,
        0,
        0,
        cropArea.width,
        cropArea.height
      )
      ctx.restore()
      
      // 导出结果
      setCroppedImage(canvas.toDataURL('image/png'))
      setIsProcessing(false)
    }
    img.src = originalImage
  }, [originalImage, cropArea, transform])

  // 旋转图片
  const rotateImage = (angle: number) => {
    setTransform(prev => ({
      ...prev,
      rotation: (prev.rotation + angle) % 360
    }))
  }

  // 缩放图片
  const zoomImage = (delta: number) => {
    setTransform(prev => ({
      ...prev,
      scale: Math.max(0.1, Math.min(3, prev.scale + delta))
    }))
  }

  // 下载图片
  const downloadImage = () => {
    if (!croppedImage) return
    
    const link = document.createElement('a')
    link.href = croppedImage
    link.download = `cropped_${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 重置
  const reset = () => {
    setOriginalImage(null)
    setCroppedImage(null)
    setImageSize({ width: 0, height: 0 })
    setCropArea({ x: 0, y: 0, width: 200, height: 200 })
    setTransform({ scale: 1, rotation: 0, translateX: 0, translateY: 0 })
    if (fileInputRef.current) fileInputRef.current.value = ''
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片裁剪工具</h2>
        <p className="text-gray-600">
          上传图片并自由裁剪，支持多种宽高比和旋转功能
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {/* 控制面板 */}
        <div className="md:col-span-1 space-y-4">
          <div className="bg-white rounded-lg border border-gray-300 p-4">
            <h3 className="font-medium mb-4">裁剪设置</h3>
            
            {/* 宽高比选择 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">宽高比</label>
              <select
                value={aspectRatio}
                onChange={(e) => setAspectRatio(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {aspectRatios.map(ratio => (
                  <option key={ratio.value} value={ratio.value}>
                    {ratio.label}
                  </option>
                ))}
              </select>
            </div>

            {/* 裁剪区域输入 */}
            <div className="grid grid-cols-2 gap-2 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">X 坐标</label>
                <input
                  type="number"
                  value={Math.round(cropArea.x)}
                  onChange={(e) => updateCropArea({ x: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Y 坐标</label>
                <input
                  type="number"
                  value={Math.round(cropArea.y)}
                  onChange={(e) => updateCropArea({ y: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">宽度</label>
                <input
                  type="number"
                  value={Math.round(cropArea.width)}
                  onChange={(e) => updateCropArea({ width: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">高度</label>
                <input
                  type="number"
                  value={Math.round(cropArea.height)}
                  onChange={(e) => updateCropArea({ height: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* 变换控制 */}
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium mb-1">
                  缩放: {(transform.scale * 100).toFixed(0)}%
                </label>
                <div className="flex gap-2">
                  <button
                    onClick={() => zoomImage(-0.1)}
                    className="flex-1 px-3 py-2 bg-gray-200 rounded-md hover:bg-gray-300"
                  >
                    <ZoomOut className="w-4 h-4 mx-auto" />
                  </button>
                  <input
                    type="range"
                    min="10"
                    max="300"
                    value={transform.scale * 100}
                    onChange={(e) => setTransform(prev => ({ ...prev, scale: Number(e.target.value) / 100 }))}
                    className="flex-1"
                  />
                  <button
                    onClick={() => zoomImage(0.1)}
                    className="flex-1 px-3 py-2 bg-gray-200 rounded-md hover:bg-gray-300"
                  >
                    <ZoomIn className="w-4 h-4 mx-auto" />
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">旋转</label>
                <div className="flex gap-2">
                  <button
                    onClick={() => rotateImage(-90)}
                    className="flex-1 px-3 py-2 bg-gray-200 rounded-md hover:bg-gray-300 text-sm"
                  >
                    逆时针 90°
                  </button>
                  <button
                    onClick={() => rotateImage(90)}
                    className="flex-1 px-3 py-2 bg-gray-200 rounded-md hover:bg-gray-300 text-sm"
                  >
                    顺时针 90°
                  </button>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="mt-6 space-y-2">
              <button
                onClick={performCrop}
                disabled={!originalImage || isProcessing}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                <Crop className="w-4 h-4 inline mr-2" />
                裁剪图片
              </button>
              {croppedImage && (
                <button
                  onClick={downloadImage}
                  className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                >
                  <Download className="w-4 h-4 inline mr-2" />
                  下载结果
                </button>
              )}
              <button
                onClick={reset}
                className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
              >
                <RefreshCw className="w-4 h-4 inline mr-2" />
                重置
              </button>
            </div>
          </div>
        </div>

        {/* 预览区域 */}
        <div className="md:col-span-2">
          {!originalImage ? (
            <div className="bg-white rounded-lg border-2 border-dashed border-gray-300 p-12">
              <div className="text-center">
                <Crop className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">上传图片开始裁剪</p>
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleImageUpload}
                  accept="image/*"
                  className="hidden"
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  <Upload className="w-4 h-4 inline mr-2" />
                  选择图片
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 编辑区域 */}
              <div className="bg-white rounded-lg border border-gray-300 p-4">
                <h3 className="font-medium mb-4">编辑区域</h3>
                <div 
                  ref={containerRef}
                  className="relative overflow-hidden bg-gray-100 rounded-lg"
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseUp}
                >
                  <canvas
                    ref={previewCanvasRef}
                    className="max-w-full mx-auto cursor-move"
                    onMouseDown={handleMouseDown}
                  />
                </div>
              </div>

              {/* 结果预览 */}
              {croppedImage && (
                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <h3 className="font-medium mb-4">裁剪结果</h3>
                  <Image
                    src={croppedImage}
                    alt="Cropped"
                    width={Math.round(cropArea.width)}
                    height={Math.round(cropArea.height)}
                    className="max-w-full mx-auto rounded-lg"
                    unoptimized
                  />
                  <div className="mt-4 text-sm text-gray-600">
                    尺寸: {Math.round(cropArea.width)} × {Math.round(cropArea.height)} 像素
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Canvas 用于处理图片 */}
      <canvas ref={canvasRef} className="hidden" />

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Move className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>上传图片后，拖动虚线框选择裁剪区域</li>
              <li>支持多种预设宽高比，也可自由调整</li>
              <li>可以旋转和缩放图片</li>
              <li>精确输入坐标和尺寸进行微调</li>
              <li>裁剪后的图片保持原始质量</li>
              <li>支持常见图片格式（JPG、PNG、GIF等）</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
