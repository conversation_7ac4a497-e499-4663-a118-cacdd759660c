'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Download, Upload, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { ToolLayout } from '@/components/layout/ToolLayout';

export default function FileEncryptorTool() {
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [mode, setMode] = useState<'encrypt' | 'decrypt'>('encrypt');
  const [fileName, setFileName] = useState('');
  const [error, setError] = useState('');

  // 简单的加密算法（AES-256-GCM 的模拟）
  const encrypt = async (text: string, password: string): Promise<string> => {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(text);
      
      // 生成密钥
      const passwordKey = await crypto.subtle.importKey(
        'raw',
        encoder.encode(password.padEnd(32, '0').slice(0, 32)),
        { name: 'AES-GCM' },
        false,
        ['encrypt']
      );
      
      // 生成随机 IV
      const iv = crypto.getRandomValues(new Uint8Array(12));
      
      // 加密数据
      const encryptedData = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        passwordKey,
        data
      );
      
      // 合并 IV 和加密数据
      const combined = new Uint8Array(iv.length + encryptedData.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encryptedData), iv.length);
      
      // 转换为 Base64
      return btoa(Array.from(combined).map(b => String.fromCharCode(b)).join(''));
    } catch (err) {
      throw new Error('加密失败');
    }
  };

  const decrypt = async (encryptedText: string, password: string): Promise<string> => {
    try {
      const encoder = new TextEncoder();
      const decoder = new TextDecoder();
      
      // Base64 解码
      const combined = new Uint8Array(
        atob(encryptedText).split('').map(c => c.charCodeAt(0))
      );
      
      // 提取 IV 和加密数据
      const iv = combined.slice(0, 12);
      const encryptedData = combined.slice(12);
      
      // 生成密钥
      const passwordKey = await crypto.subtle.importKey(
        'raw',
        encoder.encode(password.padEnd(32, '0').slice(0, 32)),
        { name: 'AES-GCM' },
        false,
        ['decrypt']
      );
      
      // 解密数据
      const decryptedData = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        passwordKey,
        encryptedData
      );
      
      return decoder.decode(decryptedData);
    } catch (err) {
      throw new Error('解密失败，请检查密码是否正确');
    }
  };

  const handleProcess = async () => {
    if (!password) {
      setError('请输入密码');
      return;
    }

    if (!inputText) {
      setError('请输入要处理的内容');
      return;
    }

    setError('');
    
    try {
      if (mode === 'encrypt') {
        const encrypted = await encrypt(inputText, password);
        setOutputText(encrypted);
      } else {
        const decrypted = await decrypt(inputText, password);
        setOutputText(decrypted);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '处理失败');
      setOutputText('');
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setFileName(file.name);
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const content = event.target?.result as string;
      setInputText(content);
    };
    
    reader.onerror = () => {
      setError('文件读取失败');
    };

    if (mode === 'encrypt') {
      reader.readAsText(file);
    } else {
      reader.readAsText(file);
    }
  };

  const handleDownload = () => {
    if (!outputText) return;

    const blob = new Blob([outputText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    
    if (mode === 'encrypt') {
      a.download = fileName ? `${fileName}.encrypted` : 'encrypted.txt';
    } else {
      a.download = fileName ? fileName.replace('.encrypted', '') : 'decrypted.txt';
    }
    
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleClear = () => {
    setPassword('');
    setInputText('');
    setOutputText('');
    setFileName('');
    setError('');
  };

  return (
    <ToolLayout
      title="文件加密解密"
      description="使用 AES 加密算法安全地加密和解密文件内容"
    >
      <div className="space-y-6">
        {/* 模式选择 */}
        <Card className="p-6">
          <div className="flex gap-4 mb-6">
            <Button
              onClick={() => {
                setMode('encrypt');
                handleClear();
              }}
              variant={mode === 'encrypt' ? 'default' : 'outline'}
              className="flex-1"
            >
              <Shield className="w-4 h-4 mr-2" />
              加密
            </Button>
            <Button
              onClick={() => {
                setMode('decrypt');
                handleClear();
              }}
              variant={mode === 'decrypt' ? 'default' : 'outline'}
              className="flex-1"
            >
              <Shield className="w-4 h-4 mr-2" />
              解密
            </Button>
          </div>

          {/* 密码输入 */}
          <div className="space-y-2 mb-6">
            <Label htmlFor="password">密码</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="请输入加密/解密密码"
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              请使用强密码，建议包含字母、数字和特殊字符
            </p>
          </div>

          {/* 文件上传 */}
          <div className="space-y-2 mb-6">
            <Label htmlFor="file">上传文件（可选）</Label>
            <div className="flex gap-2">
              <Input
                id="file"
                type="file"
                onChange={handleFileUpload}
                accept=".txt,.json,.csv,.xml,.html,.js,.css,.md"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => document.getElementById('file')?.click()}
              >
                <Upload className="w-4 h-4 mr-2" />
                选择文件
              </Button>
            </div>
            {fileName && (
              <p className="text-sm text-muted-foreground">
                已选择文件：{fileName}
              </p>
            )}
          </div>

          {/* 输入内容 */}
          <div className="space-y-2 mb-6">
            <Label htmlFor="input">
              {mode === 'encrypt' ? '原始内容' : '加密内容'}
            </Label>
            <Textarea
              id="input"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder={
                mode === 'encrypt'
                  ? '请输入要加密的内容或上传文件'
                  : '请输入要解密的内容或上传加密文件'
              }
              className="min-h-[200px] font-mono"
            />
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button onClick={handleProcess} className="flex-1">
              {mode === 'encrypt' ? '加密' : '解密'}
            </Button>
            <Button onClick={handleClear} variant="outline">
              清空
            </Button>
          </div>
        </Card>

        {/* 输出结果 */}
        {outputText && (
          <Card className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Label>
                  {mode === 'encrypt' ? '加密结果' : '解密结果'}
                </Label>
                <Button onClick={handleDownload} size="sm" variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  下载
                </Button>
              </div>
              <Textarea
                value={outputText}
                readOnly
                className="min-h-[200px] font-mono bg-muted"
              />
            </div>
          </Card>
        )}

        {/* 使用说明 */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">使用说明</h3>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>• 本工具使用 AES-GCM 加密算法，提供强大的加密保护</p>
            <p>• 加密和解密都在浏览器本地进行，不会上传到服务器</p>
            <p>• 请妥善保管密码，忘记密码将无法解密文件</p>
            <p>• 支持文本文件的加密和解密</p>
            <p>• 加密后的文件以 Base64 格式保存，可以安全传输</p>
          </div>
        </Card>
      </div>
    </ToolLayout>
  );
}
