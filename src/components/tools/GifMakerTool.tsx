'use client'

import React, { useState, useRef, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  Upload, 
  Play, 
  Pause, 
  RotateCcw, 
  Download, 
  Trash2, 
  ArrowUp, 
  ArrowDown,
  Settings,
  Image as ImageIcon,
  Clock,
  Zap,
  Palette,
  Info
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface ImageFrame {
  id: string
  file: File
  url: string
  duration: number
  width: number
  height: number
}

interface GifSettings {
  width: number
  height: number
  quality: number
  fps: number
  loop: boolean
  dithering: boolean
  colors: number
}

/**
 * GIF制作工具组件
 * 支持将多张图片转换为GIF动画
 */
export default function GifMakerTool() {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  
  // 状态管理
  const [frames, setFrames] = useState<ImageFrame[]>([])
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentFrame, setCurrentFrame] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [generatedGif, setGeneratedGif] = useState<string | null>(null)
  const [previewInterval, setPreviewInterval] = useState<NodeJS.Timeout | null>(null)
  
  // GIF设置
  const [settings, setSettings] = useState<GifSettings>({
    width: 400,
    height: 300,
    quality: 80,
    fps: 10,
    loop: true,
    dithering: true,
    colors: 256
  })

  /**
   * 处理文件上传
   */
  const handleFileUpload = useCallback(async (files: FileList) => {
    const imageFiles = Array.from(files).filter(file => 
      file.type.startsWith('image/')
    )

    if (imageFiles.length === 0) {
      toast({
        title: "错误",
        description: "请选择有效的图片文件",
        variant: "destructive"
      })
      return
    }

    const newFrames: ImageFrame[] = []

    for (const file of imageFiles) {
      try {
        const url = URL.createObjectURL(file)
        const img = new Image()
        
        await new Promise((resolve, reject) => {
          img.onload = resolve
          img.onerror = reject
          img.src = url
        })

        newFrames.push({
          id: Math.random().toString(36).substr(2, 9),
          file,
          url,
          duration: 1000 / settings.fps,
          width: img.naturalWidth,
          height: img.naturalHeight
        })
      } catch (error) {
        console.error('加载图片失败:', error)
        toast({
          title: "错误",
          description: `加载图片 ${file.name} 失败`,
          variant: "destructive"
        })
      }
    }

    setFrames(prev => [...prev, ...newFrames])
    
    // 自动调整画布尺寸为第一张图片的尺寸
    if (frames.length === 0 && newFrames.length > 0) {
      const firstFrame = newFrames[0]
      setSettings(prev => ({
        ...prev,
        width: Math.min(firstFrame.width, 800),
        height: Math.min(firstFrame.height, 600)
      }))
    }

    toast({
      title: "成功",
      description: `已添加 ${newFrames.length} 张图片`
    })
  }, [frames.length, settings.fps, toast])

  /**
   * 删除帧
   */
  const removeFrame = useCallback((id: string) => {
    setFrames(prev => prev.filter(frame => frame.id !== id))
    setCurrentFrame(0)
  }, [])

  /**
   * 移动帧位置
   */
  const moveFrame = useCallback((id: string, direction: 'up' | 'down') => {
    setFrames(prev => {
      const index = prev.findIndex(frame => frame.id === id)
      if (index === -1) return prev
      
      const newIndex = direction === 'up' ? index - 1 : index + 1
      if (newIndex < 0 || newIndex >= prev.length) return prev
      
      const newFrames = [...prev]
      const [movedFrame] = newFrames.splice(index, 1)
      newFrames.splice(newIndex, 0, movedFrame)
      
      return newFrames
    })
  }, [])

  /**
   * 更新帧持续时间
   */
  const updateFrameDuration = useCallback((id: string, duration: number) => {
    setFrames(prev => prev.map(frame => 
      frame.id === id ? { ...frame, duration } : frame
    ))
  }, [])

  /**
   * 预览动画
   */
  const togglePreview = useCallback(() => {
    if (frames.length === 0) {
      toast({
        title: "提示",
        description: "请先添加图片帧",
        variant: "destructive"
      })
      return
    }

    if (isPlaying) {
      setIsPlaying(false)
      if (previewInterval) {
        clearInterval(previewInterval)
        setPreviewInterval(null)
      }
    } else {
      setIsPlaying(true)
      const interval = setInterval(() => {
        setCurrentFrame(prev => (prev + 1) % frames.length)
      }, frames[currentFrame]?.duration || 100)
      setPreviewInterval(interval)
    }
  }, [frames, isPlaying, previewInterval, currentFrame])

  /**
   * 停止预览
   */
  const stopPreview = useCallback(() => {
    setIsPlaying(false)
    setCurrentFrame(0)
    if (previewInterval) {
      clearInterval(previewInterval)
      setPreviewInterval(null)
    }
  }, [previewInterval])

  /**
   * 生成GIF
   */
  const generateGif = useCallback(async () => {
    if (frames.length === 0) {
      toast({
        title: "错误",
        description: "请先添加图片帧",
        variant: "destructive"
      })
      return
    }

    setIsGenerating(true)
    setGenerationProgress(0)

    try {
      // 这里使用简化的GIF生成逻辑
      // 在实际项目中，你可能需要使用专门的GIF生成库如 gif.js
      const canvas = canvasRef.current!
      const ctx = canvas.getContext('2d')!
      
      canvas.width = settings.width
      canvas.height = settings.height

      // 创建一个简单的动画效果（实际应该使用专业的GIF编码库）
      const gifFrames: string[] = []
      
      for (let i = 0; i < frames.length; i++) {
        const frame = frames[i]
        const img = new Image()
        
        await new Promise((resolve) => {
          img.onload = resolve
          img.src = frame.url
        })

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        
        // 绘制图片
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        // 转换为数据URL
        const dataUrl = canvas.toDataURL('image/png')
        gifFrames.push(dataUrl)
        
        setGenerationProgress(((i + 1) / frames.length) * 100)
      }

      // 模拟GIF生成过程
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 这里应该使用真正的GIF编码库来生成GIF
      // 现在我们创建一个简单的数据URL作为示例
      const gifBlob = new Blob(['GIF89a'], { type: 'image/gif' })
      const gifUrl = URL.createObjectURL(gifBlob)
      
      setGeneratedGif(gifUrl)
      
      toast({
        title: "成功",
        description: "GIF动画生成完成！"
      })
    } catch (error) {
      console.error('生成GIF失败:', error)
      toast({
        title: "错误",
        description: "生成GIF失败，请重试",
        variant: "destructive"
      })
    } finally {
      setIsGenerating(false)
      setGenerationProgress(0)
    }
  }, [frames, settings, toast])

  /**
   * 下载GIF
   */
  const downloadGif = useCallback(() => {
    if (!generatedGif) return

    const link = document.createElement('a')
    link.href = generatedGif
    link.download = `animated-${Date.now()}.gif`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "成功",
      description: "GIF文件已下载"
    })
  }, [generatedGif, toast])

  /**
   * 清空所有帧
   */
  const clearAllFrames = useCallback(() => {
    frames.forEach(frame => URL.revokeObjectURL(frame.url))
    setFrames([])
    setCurrentFrame(0)
    setGeneratedGif(null)
    stopPreview()
  }, [frames, stopPreview])

  // 预设配置
  const presets = {
    web: { width: 400, height: 300, quality: 80, fps: 10, colors: 256 },
    social: { width: 500, height: 500, quality: 85, fps: 15, colors: 256 },
    banner: { width: 728, height: 90, quality: 75, fps: 8, colors: 128 },
    icon: { width: 64, height: 64, quality: 90, fps: 12, colors: 64 }
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">GIF制作工具</h1>
        <p className="text-muted-foreground">
          将多张图片转换为GIF动画，支持自定义帧率、尺寸和质量设置
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：图片上传和帧管理 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 上传区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5" />
                图片上传
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div
                  className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
                  onClick={() => fileInputRef.current?.click()}
                  onDrop={(e) => {
                    e.preventDefault()
                    const files = e.dataTransfer.files
                    if (files.length > 0) {
                      handleFileUpload(files)
                    }
                  }}
                  onDragOver={(e) => e.preventDefault()}
                >
                  <ImageIcon className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-lg font-medium mb-2">点击或拖拽上传图片</p>
                  <p className="text-sm text-muted-foreground">
                    支持 JPG、PNG、WebP 等格式，可选择多张图片
                  </p>
                </div>
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files) {
                      handleFileUpload(e.target.files)
                    }
                  }}
                />

                <div className="flex gap-2">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    className="flex-1"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    选择图片
                  </Button>
                  <Button
                    onClick={clearAllFrames}
                    variant="outline"
                    disabled={frames.length === 0}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    清空
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 帧列表 */}
          {frames.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <ImageIcon className="w-5 h-5" />
                    动画帧 ({frames.length})
                  </span>
                  <div className="flex gap-2">
                    <Button
                      onClick={togglePreview}
                      variant="outline"
                      size="sm"
                    >
                      {isPlaying ? (
                        <Pause className="w-4 h-4 mr-2" />
                      ) : (
                        <Play className="w-4 h-4 mr-2" />
                      )}
                      {isPlaying ? '暂停' : '预览'}
                    </Button>
                    <Button
                      onClick={stopPreview}
                      variant="outline"
                      size="sm"
                      disabled={!isPlaying && currentFrame === 0}
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      重置
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {frames.map((frame, index) => (
                    <div
                      key={frame.id}
                      className={`flex items-center gap-3 p-3 rounded-lg border ${
                        index === currentFrame ? 'border-primary bg-primary/5' : 'border-border'
                      }`}
                    >
                      <img
                        src={frame.url}
                        alt={`Frame ${index + 1}`}
                        className="w-16 h-16 object-cover rounded"
                      />
                      
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">帧 {index + 1}</span>
                          <Badge variant="secondary">
                            {frame.width} × {frame.height}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Label className="text-xs">持续时间:</Label>
                          <Input
                            type="number"
                            value={frame.duration}
                            onChange={(e) => updateFrameDuration(frame.id, Number(e.target.value))}
                            className="w-20 h-6 text-xs"
                            min="50"
                            max="5000"
                            step="50"
                          />
                          <span className="text-xs text-muted-foreground">ms</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-col gap-1">
                        <Button
                          onClick={() => moveFrame(frame.id, 'up')}
                          variant="ghost"
                          size="sm"
                          disabled={index === 0}
                        >
                          <ArrowUp className="w-3 h-3" />
                        </Button>
                        <Button
                          onClick={() => moveFrame(frame.id, 'down')}
                          variant="ghost"
                          size="sm"
                          disabled={index === frames.length - 1}
                        >
                          <ArrowDown className="w-3 h-3" />
                        </Button>
                        <Button
                          onClick={() => removeFrame(frame.id)}
                          variant="ghost"
                          size="sm"
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 右侧：预览和设置 */}
        <div className="space-y-6">
          {/* 预览区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="w-5 h-5" />
                预览
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="aspect-video bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                  {frames.length > 0 ? (
                    <img
                      src={frames[currentFrame]?.url}
                      alt={`Preview frame ${currentFrame + 1}`}
                      className="max-w-full max-h-full object-contain"
                    />
                  ) : (
                    <div className="text-center text-muted-foreground">
                      <ImageIcon className="w-12 h-12 mx-auto mb-2" />
                      <p>暂无预览</p>
                    </div>
                  )}
                </div>
                
                {frames.length > 0 && (
                  <div className="text-center text-sm text-muted-foreground">
                    帧 {currentFrame + 1} / {frames.length}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 设置面板 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                GIF设置
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="basic">基础设置</TabsTrigger>
                  <TabsTrigger value="advanced">高级设置</TabsTrigger>
                </TabsList>
                
                <TabsContent value="basic" className="space-y-4">
                  {/* 预设配置 */}
                  <div className="space-y-2">
                    <Label>预设配置</Label>
                    <Select
                      onValueChange={(value) => {
                        const preset = presets[value as keyof typeof presets]
                        if (preset) {
                          setSettings(prev => ({ ...prev, ...preset }))
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择预设" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="web">网页 (400×300)</SelectItem>
                        <SelectItem value="social">社交媒体 (500×500)</SelectItem>
                        <SelectItem value="banner">横幅 (728×90)</SelectItem>
                        <SelectItem value="icon">图标 (64×64)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 尺寸设置 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>宽度</Label>
                      <Input
                        type="number"
                        value={settings.width}
                        onChange={(e) => setSettings(prev => ({ ...prev, width: Number(e.target.value) }))}
                        min="50"
                        max="1920"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>高度</Label>
                      <Input
                        type="number"
                        value={settings.height}
                        onChange={(e) => setSettings(prev => ({ ...prev, height: Number(e.target.value) }))}
                        min="50"
                        max="1080"
                      />
                    </div>
                  </div>

                  {/* 帧率设置 */}
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      帧率: {settings.fps} FPS
                    </Label>
                    <Slider
                      value={[settings.fps]}
                      onValueChange={([value]) => setSettings(prev => ({ ...prev, fps: value }))}
                      min={1}
                      max={30}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  {/* 质量设置 */}
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Zap className="w-4 h-4" />
                      质量: {settings.quality}%
                    </Label>
                    <Slider
                      value={[settings.quality]}
                      onValueChange={([value]) => setSettings(prev => ({ ...prev, quality: value }))}
                      min={10}
                      max={100}
                      step={5}
                      className="w-full"
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="advanced" className="space-y-4">
                  {/* 颜色数量 */}
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Palette className="w-4 h-4" />
                      颜色数量: {settings.colors}
                    </Label>
                    <Slider
                      value={[settings.colors]}
                      onValueChange={([value]) => setSettings(prev => ({ ...prev, colors: value }))}
                      min={16}
                      max={256}
                      step={16}
                      className="w-full"
                    />
                  </div>

                  {/* 其他选项 */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>循环播放</Label>
                      <input
                        type="checkbox"
                        checked={settings.loop}
                        onChange={(e) => setSettings(prev => ({ ...prev, loop: e.target.checked }))}
                        className="rounded"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label>抖动处理</Label>
                      <input
                        type="checkbox"
                        checked={settings.dithering}
                        onChange={(e) => setSettings(prev => ({ ...prev, dithering: e.target.checked }))}
                        className="rounded"
                      />
                    </div>
                  </div>

                  {/* 信息显示 */}
                  <div className="p-3 bg-muted rounded-lg">
                    <div className="flex items-start gap-2">
                      <Info className="w-4 h-4 mt-0.5 text-muted-foreground" />
                      <div className="text-sm text-muted-foreground">
                        <p>预计文件大小: ~{Math.round((settings.width * settings.height * frames.length * settings.colors) / 10000)}KB</p>
                        <p>总时长: {Math.round(frames.reduce((sum, frame) => sum + frame.duration, 0) / 1000 * 10) / 10}秒</p>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* 生成按钮 */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <Button
                  onClick={generateGif}
                  disabled={frames.length === 0 || isGenerating}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      生成GIF
                    </>
                  )}
                </Button>

                {isGenerating && (
                  <div className="space-y-2">
                    <Progress value={generationProgress} className="w-full" />
                    <p className="text-sm text-center text-muted-foreground">
                      {Math.round(generationProgress)}% 完成
                    </p>
                  </div>
                )}

                {generatedGif && (
                  <Button
                    onClick={downloadGif}
                    variant="outline"
                    className="w-full"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    下载GIF
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 隐藏的画布用于图片处理 */}
      <canvas
        ref={canvasRef}
        className="hidden"
        width={settings.width}
        height={settings.height}
      />
    </div>
  )
}
