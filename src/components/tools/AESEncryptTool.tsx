'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Lock, Unlock, Key, Shield, Eye, EyeOff, AlertTriangle } from 'lucide-react'
import * as CryptoJS from 'crypto-js'

const AESEncryptTool: React.FC = () => {
  const [activeTab, setActiveTab] = useState('encrypt')
  const [plaintext, setPlaintext] = useState('')
  const [ciphertext, setCiphertext] = useState('')
  const [secretKey, setSecretKey] = useState('')
  const [keySize, setKeySize] = useState('256')
  const [mode, setMode] = useState('CBC')
  const [padding, setPadding] = useState('Pkcs7')
  const [iv, setIv] = useState('')
  const [result, setResult] = useState('')
  const [showKey, setShowKey] = useState(false)
  const [error, setError] = useState('')

  // AES 加密
  const handleEncrypt = () => {
    try {
      setError('')
      if (!plaintext.trim()) {
        setError('请输入要加密的文本')
        return
      }
      if (!secretKey.trim()) {
        setError('请输入密钥')
        return
      }

      const keyBytes = CryptoJS.enc.Utf8.parse(secretKey)
      const encrypted = CryptoJS.AES.encrypt(plaintext, keyBytes, {
        mode: CryptoJS.mode[mode as keyof typeof CryptoJS.mode],
        padding: CryptoJS.pad[padding as keyof typeof CryptoJS.pad],
        iv: iv ? CryptoJS.enc.Utf8.parse(iv) : undefined
      })

      setResult(encrypted.toString())
    } catch (err) {
      setError('加密失败：' + (err as Error).message)
    }
  }

  // AES 解密
  const handleDecrypt = () => {
    try {
      setError('')
      if (!ciphertext.trim()) {
        setError('请输入要解密的密文')
        return
      }
      if (!secretKey.trim()) {
        setError('请输入密钥')
        return
      }

      const keyBytes = CryptoJS.enc.Utf8.parse(secretKey)
      const decrypted = CryptoJS.AES.decrypt(ciphertext, keyBytes, {
        mode: CryptoJS.mode[mode as keyof typeof CryptoJS.mode],
        padding: CryptoJS.pad[padding as keyof typeof CryptoJS.pad],
        iv: iv ? CryptoJS.enc.Utf8.parse(iv) : undefined
      })

      const plaintext = decrypted.toString(CryptoJS.enc.Utf8)
      if (!plaintext) {
        throw new Error('解密失败，请检查密钥和密文是否正确')
      }
      
      setResult(plaintext)
    } catch (err) {
      setError('解密失败：' + (err as Error).message)
    }
  }

  // 生成随机密钥
  const generateRandomKey = () => {
    const keyLength = parseInt(keySize) / 8 // 转换为字节
    const randomKey = CryptoJS.lib.WordArray.random(keyLength).toString()
    setSecretKey(randomKey)
  }

  // 生成随机IV
  const generateRandomIV = () => {
    const randomIV = CryptoJS.lib.WordArray.random(16).toString()
    setIv(randomIV)
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 清空所有输入
  const clearAll = () => {
    setPlaintext('')
    setCiphertext('')
    setResult('')
    setError('')
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Shield className="w-8 h-8" />
          AES 加密解密工具
        </h1>
        <p className="text-muted-foreground">
          使用 AES 算法进行数据加密和解密，支持多种加密模式和填充方式
        </p>
      </div>

      {/* 配置区域 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">加密配置</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label>密钥长度</Label>
            <Select value={keySize} onValueChange={setKeySize}>
              <option value="128">128 位</option>
              <option value="192">192 位</option>
              <option value="256">256 位</option>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>加密模式</Label>
            <Select value={mode} onValueChange={setMode}>
              <option value="CBC">CBC</option>
              <option value="CFB">CFB</option>
              <option value="CTR">CTR</option>
              <option value="OFB">OFB</option>
              <option value="ECB">ECB</option>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>填充方式</Label>
            <Select value={padding} onValueChange={setPadding}>
              <option value="Pkcs7">PKCS7</option>
              <option value="AnsiX923">ANSI X9.23</option>
              <option value="Iso10126">ISO 10126</option>
              <option value="Iso97971">ISO/IEC 9797-1</option>
              <option value="ZeroPadding">Zero Padding</option>
              <option value="NoPadding">No Padding</option>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>输出格式</Label>
            <Select value="base64" disabled>
              <option value="base64">Base64</option>
            </Select>
          </div>
        </div>
      </Card>

      {/* 密钥配置 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">密钥配置</h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>密钥 (Secret Key)</Label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowKey(!showKey)}
                >
                  {showKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateRandomKey}
                >
                  <Key className="w-4 h-4 mr-2" />
                  生成随机密钥
                </Button>
              </div>
            </div>
            <Input
              type={showKey ? 'text' : 'password'}
              value={secretKey}
              onChange={(e) => setSecretKey(e.target.value)}
              placeholder="输入密钥或点击生成随机密钥"
              className="font-mono"
            />
          </div>

          {mode !== 'ECB' && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>初始化向量 (IV) - 可选</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateRandomIV}
                >
                  生成随机IV
                </Button>
              </div>
              <Input
                value={iv}
                onChange={(e) => setIv(e.target.value)}
                placeholder="输入初始化向量（16字节）或留空自动生成"
                className="font-mono"
              />
            </div>
          )}
        </div>

        {/* 安全提示 */}
        <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800 dark:text-yellow-200">安全提示：</p>
              <ul className="mt-1 text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• 密钥应保密存储，不要在不安全的环境中使用</li>
                <li>• 推荐使用256位密钥以获得更高的安全性</li>
                <li>• ECB模式不推荐用于敏感数据，建议使用CBC或CTR模式</li>
                <li>• 每次加密使用不同的IV可以提高安全性</li>
              </ul>
            </div>
          </div>
        </div>
      </Card>

      {/* 加密解密区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="encrypt">
            <Lock className="w-4 h-4 mr-2" />
            加密
          </TabsTrigger>
          <TabsTrigger value="decrypt">
            <Unlock className="w-4 h-4 mr-2" />
            解密
          </TabsTrigger>
        </TabsList>

        <TabsContent value="encrypt" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">文本加密</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>明文 (要加密的文本)</Label>
                <Textarea
                  value={plaintext}
                  onChange={(e) => setPlaintext(e.target.value)}
                  placeholder="输入要加密的文本..."
                  className="min-h-[120px] font-mono"
                />
              </div>

              <div className="flex gap-3">
                <Button onClick={handleEncrypt} className="flex items-center gap-2">
                  <Lock className="w-4 h-4" />
                  加密
                </Button>
                <Button variant="outline" onClick={clearAll}>
                  清空
                </Button>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="decrypt" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">密文解密</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>密文 (要解密的文本)</Label>
                <Textarea
                  value={ciphertext}
                  onChange={(e) => setCiphertext(e.target.value)}
                  placeholder="输入要解密的密文..."
                  className="min-h-[120px] font-mono"
                />
              </div>

              <div className="flex gap-3">
                <Button onClick={handleDecrypt} className="flex items-center gap-2">
                  <Unlock className="w-4 h-4" />
                  解密
                </Button>
                <Button variant="outline" onClick={clearAll}>
                  清空
                </Button>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 结果显示区域 */}
      {(result || error) && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">
              {error ? '错误信息' : (activeTab === 'encrypt' ? '加密结果' : '解密结果')}
            </h3>
            {result && !error && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(result)}
                className="flex items-center gap-2"
              >
                <Copy className="w-4 h-4" />
                复制
              </Button>
            )}
          </div>

          {error ? (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
              <p className="text-red-700 dark:text-red-300">{error}</p>
            </div>
          ) : (
            <Textarea
              value={result}
              readOnly
              className="min-h-[120px] font-mono bg-muted"
              placeholder="结果将显示在这里..."
            />
          )}
        </Card>
      )}

      {/* 使用说明 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">使用说明</h3>
        <div className="space-y-4 text-sm text-muted-foreground">
          <div>
            <h4 className="font-medium text-foreground mb-2">AES 算法说明：</h4>
            <p>AES（Advanced Encryption Standard）是一种对称加密算法，广泛用于数据保护。</p>
          </div>

          <div>
            <h4 className="font-medium text-foreground mb-2">加密模式：</h4>
            <ul className="space-y-1 ml-4">
              <li>• <strong>CBC</strong>：密码块链接模式，最常用的模式</li>
              <li>• <strong>CFB</strong>：密码反馈模式，适用于流式数据</li>
              <li>• <strong>CTR</strong>：计数器模式，支持并行处理</li>
              <li>• <strong>OFB</strong>：输出反馈模式，适用于实时应用</li>
              <li>• <strong>ECB</strong>：电子密码本模式，不推荐用于敏感数据</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-foreground mb-2">使用步骤：</h4>
            <ol className="space-y-1 ml-4">
              <li>1. 选择加密参数（密钥长度、模式、填充方式）</li>
              <li>2. 输入或生成密钥和IV（如需要）</li>
              <li>3. 在对应标签页输入要加密或解密的内容</li>
              <li>4. 点击加密或解密按钮</li>
              <li>5. 复制结果或继续其他操作</li>
            </ol>
          </div>

          <div>
            <h4 className="font-medium text-foreground mb-2">注意事项：</h4>
            <ul className="space-y-1 ml-4">
              <li>• 解密时必须使用与加密时相同的参数</li>
              <li>• 密钥长度必须与选择的位数匹配</li>
              <li>• IV用于增强安全性，每次加密建议使用不同的IV</li>
              <li>• 请妥善保管密钥，密钥丢失将无法解密数据</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default AESEncryptTool
