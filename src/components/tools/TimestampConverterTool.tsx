'use client'

import { useState, useEffect, useCallback } from 'react'
import { Co<PERSON>, Check, Clock, Calendar, RefreshCw, Globe } from 'lucide-react'

interface TimeFormat {
  timestamp: number
  seconds: number
  milliseconds: number
  iso8601: string
  utc: string
  local: string
  unix: string
  relative: string
}

interface TimeZoneInfo {
  name: string
  offset: string
  time: string
}

export default function TimestampConverterTool() {
  const [currentTime, setCurrentTime] = useState(Date.now())
  const [inputValue, setInputValue] = useState('')
  const [convertedTime, setConvertedTime] = useState<TimeFormat | null>(null)
  const [copied, setCopied] = useState<string>('')
  const [dateInput, setDateInput] = useState({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    day: new Date().getDate(),
    hour: new Date().getHours(),
    minute: new Date().getMinutes(),
    second: new Date().getSeconds(),
  })
  const [showTimeZones, setShowTimeZones] = useState(false)

  // 常用时区
  const timeZones: { name: string; offset: number; label: string }[] = [
    { name: 'UTC', offset: 0, label: '协调世界时' },
    { name: 'PST', offset: -8, label: '太平洋标准时间' },
    { name: 'EST', offset: -5, label: '东部标准时间' },
    { name: 'GMT', offset: 0, label: '格林威治标准时间' },
    { name: 'CET', offset: 1, label: '中欧时间' },
    { name: 'CST', offset: 8, label: '中国标准时间' },
    { name: 'JST', offset: 9, label: '日本标准时间' },
    { name: 'IST', offset: 5.5, label: '印度标准时间' },
    { name: 'AEST', offset: 10, label: '澳大利亚东部标准时间' },
  ]

  // 格式化时间
  const formatTime = useCallback((timestamp: number): TimeFormat => {
    const date = new Date(timestamp)
    const seconds = Math.floor(timestamp / 1000)
    
    return {
      timestamp,
      seconds,
      milliseconds: timestamp,
      iso8601: date.toISOString(),
      utc: date.toUTCString(),
      local: date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      }),
      unix: seconds.toString(),
      relative: getRelativeTime(date),
    }
  }, [])

  // 获取相对时间
  const getRelativeTime = (date: Date): string => {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const absDiff = Math.abs(diff)
    
    const seconds = Math.floor(absDiff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    const months = Math.floor(days / 30)
    const years = Math.floor(days / 365)
    
    let relative = ''
    
    if (years > 0) {
      relative = `${years}年`
    } else if (months > 0) {
      relative = `${months}个月`
    } else if (days > 0) {
      relative = `${days}天`
    } else if (hours > 0) {
      relative = `${hours}小时`
    } else if (minutes > 0) {
      relative = `${minutes}分钟`
    } else {
      relative = `${seconds}秒`
    }
    
    return diff > 0 ? `${relative}前` : `${relative}后`
  }

  // 解析输入值
  const parseInput = useCallback((value: string) => {
    if (!value.trim()) {
      setConvertedTime(null)
      return
    }

    let timestamp: number | null = null

    // 尝试解析为数字（时间戳）
    const numValue = Number(value)
    if (!isNaN(numValue)) {
      // 判断是秒还是毫秒
      if (numValue < 10000000000) {
        // 小于 10 位数，认为是秒
        timestamp = numValue * 1000
      } else {
        // 毫秒
        timestamp = numValue
      }
    } else {
      // 尝试解析为日期字符串
      const date = new Date(value)
      if (!isNaN(date.getTime())) {
        timestamp = date.getTime()
      }
    }

    if (timestamp !== null && !isNaN(timestamp)) {
      setConvertedTime(formatTime(timestamp))
    } else {
      setConvertedTime(null)
    }
  }, [formatTime])

  // 从日期输入生成时间戳
  const generateFromDateInput = () => {
    const date = new Date(
      dateInput.year,
      dateInput.month - 1,
      dateInput.day,
      dateInput.hour,
      dateInput.minute,
      dateInput.second
    )
    
    const timestamp = date.getTime()
    setInputValue(timestamp.toString())
    setConvertedTime(formatTime(timestamp))
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string, format: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(format)
      setTimeout(() => setCopied(''), 2000)
    })
  }

  // 获取当前时间
  const getCurrentTime = useCallback(() => {
    const now = Date.now()
    setCurrentTime(now)
    setInputValue(now.toString())
    setConvertedTime(formatTime(now))
  }, [formatTime])

  // 获取时区时间
  const getTimeZoneTime = (offset: number): string => {
    const date = new Date(currentTime)
    const utc = date.getTime() + (date.getTimezoneOffset() * 60000)
    const tzTime = new Date(utc + (3600000 * offset))
    
    return tzTime.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
  }

  // 初始化
  useEffect(() => {
    getCurrentTime()
    
    // 每秒更新当前时间
    const timer = setInterval(() => {
      setCurrentTime(Date.now())
    }, 1000)
    
    return () => clearInterval(timer)
  }, [getCurrentTime])

  // 监听输入变化
  useEffect(() => {
    parseInput(inputValue)
  }, [inputValue, parseInput])

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">时间戳转换器</h2>
        <p className="text-gray-600">
          Unix 时间戳与日期时间格式相互转换
        </p>
      </div>

      {/* 当前时间 */}
      <div className="bg-blue-50 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Clock className="w-5 h-5 text-blue-600 mr-2" />
            <span className="text-blue-900 font-medium">当前时间戳：</span>
            <span className="ml-2 font-mono text-blue-900">{Math.floor(currentTime / 1000)}</span>
          </div>
          <button
            onClick={getCurrentTime}
            className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center"
          >
            <RefreshCw className="w-4 h-4 mr-1" />
            使用当前时间
          </button>
        </div>
      </div>

      {/* 时间戳输入 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <h3 className="font-medium mb-4">时间戳转换</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              输入时间戳或日期时间
            </label>
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="例如：1640995200 或 2022-01-01 00:00:00"
              className="w-full px-4 py-2 border border-gray-300 rounded-md"
            />
            <div className="mt-2 text-xs text-gray-500">
              支持秒级时间戳（10位）、毫秒级时间戳（13位）或标准日期格式
            </div>
          </div>

          {convertedTime && (
            <div className="space-y-3 pt-4 border-t">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <div className="text-xs text-gray-500">秒时间戳</div>
                    <div className="font-mono">{convertedTime.seconds}</div>
                  </div>
                  <button
                    onClick={() => copyToClipboard(convertedTime.seconds.toString(), 'seconds')}
                    className="p-2 hover:bg-gray-200 rounded"
                  >
                    {copied === 'seconds' ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <div className="text-xs text-gray-500">毫秒时间戳</div>
                    <div className="font-mono text-sm">{convertedTime.milliseconds}</div>
                  </div>
                  <button
                    onClick={() => copyToClipboard(convertedTime.milliseconds.toString(), 'milliseconds')}
                    className="p-2 hover:bg-gray-200 rounded"
                  >
                    {copied === 'milliseconds' ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <div className="text-xs text-gray-500">ISO 8601</div>
                    <div className="font-mono text-sm">{convertedTime.iso8601}</div>
                  </div>
                  <button
                    onClick={() => copyToClipboard(convertedTime.iso8601, 'iso8601')}
                    className="p-2 hover:bg-gray-200 rounded"
                  >
                    {copied === 'iso8601' ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <div className="text-xs text-gray-500">UTC 时间</div>
                    <div className="text-sm">{convertedTime.utc}</div>
                  </div>
                  <button
                    onClick={() => copyToClipboard(convertedTime.utc, 'utc')}
                    className="p-2 hover:bg-gray-200 rounded"
                  >
                    {copied === 'utc' ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <div className="text-xs text-gray-500">本地时间</div>
                    <div className="text-sm">{convertedTime.local}</div>
                  </div>
                  <button
                    onClick={() => copyToClipboard(convertedTime.local, 'local')}
                    className="p-2 hover:bg-gray-200 rounded"
                  >
                    {copied === 'local' ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <div className="text-xs text-gray-500">相对时间</div>
                    <div className="text-sm">{convertedTime.relative}</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 日期时间生成器 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <h3 className="font-medium mb-4 flex items-center">
          <Calendar className="w-5 h-5 mr-2" />
          日期时间生成器
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-4">
          <div>
            <label className="block text-xs text-gray-500 mb-1">年</label>
            <input
              type="number"
              value={dateInput.year}
              onChange={(e) => setDateInput({ ...dateInput, year: parseInt(e.target.value) || 2024 })}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              min="1970"
              max="2100"
            />
          </div>
          
          <div>
            <label className="block text-xs text-gray-500 mb-1">月</label>
            <input
              type="number"
              value={dateInput.month}
              onChange={(e) => setDateInput({ ...dateInput, month: parseInt(e.target.value) || 1 })}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              min="1"
              max="12"
            />
          </div>
          
          <div>
            <label className="block text-xs text-gray-500 mb-1">日</label>
            <input
              type="number"
              value={dateInput.day}
              onChange={(e) => setDateInput({ ...dateInput, day: parseInt(e.target.value) || 1 })}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              min="1"
              max="31"
            />
          </div>
          
          <div>
            <label className="block text-xs text-gray-500 mb-1">时</label>
            <input
              type="number"
              value={dateInput.hour}
              onChange={(e) => setDateInput({ ...dateInput, hour: parseInt(e.target.value) || 0 })}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              min="0"
              max="23"
            />
          </div>
          
          <div>
            <label className="block text-xs text-gray-500 mb-1">分</label>
            <input
              type="number"
              value={dateInput.minute}
              onChange={(e) => setDateInput({ ...dateInput, minute: parseInt(e.target.value) || 0 })}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              min="0"
              max="59"
            />
          </div>
          
          <div>
            <label className="block text-xs text-gray-500 mb-1">秒</label>
            <input
              type="number"
              value={dateInput.second}
              onChange={(e) => setDateInput({ ...dateInput, second: parseInt(e.target.value) || 0 })}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              min="0"
              max="59"
            />
          </div>
        </div>
        
        <button
          onClick={generateFromDateInput}
          className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          生成时间戳
        </button>
      </div>

      {/* 时区转换 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <button
          onClick={() => setShowTimeZones(!showTimeZones)}
          className="flex items-center justify-between w-full mb-4"
        >
          <h3 className="font-medium flex items-center">
            <Globe className="w-5 h-5 mr-2" />
            世界时区
          </h3>
          <span className="text-gray-400 text-sm">{showTimeZones ? '收起' : '展开'}</span>
        </button>
        
        {showTimeZones && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {timeZones.map((tz) => (
              <div key={tz.name} className="p-3 bg-gray-50 rounded">
                <div className="flex items-center justify-between mb-1">
                  <span className="font-medium">{tz.name}</span>
                  <span className="text-sm text-gray-500">UTC{tz.offset > 0 ? '+' : ''}{tz.offset}</span>
                </div>
                <div className="text-sm text-gray-600">{tz.label}</div>
                <div className="font-mono text-sm mt-1">{getTimeZoneTime(tz.offset)}</div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Clock className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持秒级时间戳（10位数字）和毫秒级时间戳（13位数字）</li>
              <li>可以输入标准日期格式，如 &quot;2024-01-01&quot; 或 &quot;2024-01-01 12:00:00&quot;</li>
              <li>日期时间生成器可以精确到秒级别</li>
              <li>时区转换显示主要城市的当前时间</li>
              <li>所有时间都基于您的本地时区设置</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
