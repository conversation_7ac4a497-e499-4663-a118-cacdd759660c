'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Copy, RefreshCw, Trash2 } from 'lucide-react';

interface SlugifyOptions {
  separator: string;
  lowercase: boolean;
  strict: boolean;
  trim: boolean;
  maxLength: number;
}

export default function SlugifyTool() {
  const [input, setInput] = useState('');
  const [options, setOptions] = useState<SlugifyOptions>({
    separator: '-',
    lowercase: true,
    strict: false,
    trim: true,
    maxLength: 50
  });

  const slugify = (text: string, opts: SlugifyOptions): string => {
    if (!text) return '';
    
    let result = text;
    
    // 去除首尾空格
    if (opts.trim) {
      result = result.trim();
    }
    
    // 转换为小写
    if (opts.lowercase) {
      result = result.toLowerCase();
    }
    
    // 处理特殊字符
    if (opts.strict) {
      // 严格模式：只保留字母、数字、中文字符和分隔符
      result = result.replace(/[^a-zA-Z0-9\u4e00-\u9fff\s\-_]/g, '');
    } else {
      // 替换常见的特殊字符
      const charMap: { [key: string]: string } = {
        'à': 'a', 'á': 'a', 'ä': 'a', 'â': 'a', 'ā': 'a', 'ã': 'a',
        'è': 'e', 'é': 'e', 'ë': 'e', 'ê': 'e', 'ē': 'e',
        'ì': 'i', 'í': 'i', 'ï': 'i', 'î': 'i', 'ī': 'i',
        'ò': 'o', 'ó': 'o', 'ö': 'o', 'ô': 'o', 'ō': 'o', 'õ': 'o',
        'ù': 'u', 'ú': 'u', 'ü': 'u', 'û': 'u', 'ū': 'u',
        'ñ': 'n', 'ç': 'c',
        '&': 'and',
        '@': 'at',
        '©': 'c',
        '®': 'r',
        '™': 'tm'
      };
      
      for (const [char, replacement] of Object.entries(charMap)) {
        result = result.replace(new RegExp(char, 'g'), replacement);
      }
      
      // 移除其他特殊字符，但保留中文字符
      result = result.replace(/[^a-zA-Z0-9\u4e00-\u9fff\s\-_]/g, '');
    }
    
    // 将空格和多个分隔符替换为单个分隔符
    if (opts.separator === 'none') {
      // 无分隔符模式：直接移除所有空格和分隔符
      result = result.replace(/[\s\-_]+/g, '');
    } else {
      result = result.replace(/[\s\-_]+/g, opts.separator);
      // 去除开头和结尾的分隔符
      result = result.replace(new RegExp(`^${opts.separator}+|${opts.separator}+$`, 'g'), '');
    }
    
    // 限制长度
    if (opts.maxLength > 0 && result.length > opts.maxLength) {
      result = result.substring(0, opts.maxLength);
      // 确保不以分隔符结尾（除非是无分隔符模式）
      if (opts.separator !== 'none') {
        result = result.replace(new RegExp(`${opts.separator}+$`), '');
      }
    }
    
    return result;
  };

  const output = useMemo(() => {
    return slugify(input, options);
  }, [input, options]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(output);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const handleClear = () => {
    setInput('');
  };

  const handleReset = () => {
    setOptions({
      separator: '-',
      lowercase: true,
      strict: false,
      trim: true,
      maxLength: 50
    });
  };

  const updateOption = <K extends keyof SlugifyOptions>(key: K, value: SlugifyOptions[K]) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const examples = [
    'Hello World!',
    'My Awesome Blog Post Title',
    'Product Name & Description',
    '中文标题转换测试',
    'Special Characters: @#$%^&*()',
    'Multiple   Spaces   Test'
  ];

  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-4">
      {/* 工具说明卡片 */}
      <Card>
        <CardHeader>
          <CardTitle>Slugify工具</CardTitle>
          <CardDescription>
            将任意文本转换为URL友好的字符串（slug），常用于创建SEO友好的URL、文件名或标识符。
            支持多种配置选项，包括分隔符选择、大小写转换、严格模式等。
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 配置选项 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">配置选项</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="separator">分隔符</Label>
              <Select value={options.separator} onValueChange={(value) => updateOption('separator', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="-">连字符 (-)</SelectItem>
                  <SelectItem value="_">下划线 (_)</SelectItem>
                  <SelectItem value=".">点号 (.)</SelectItem>
                  <SelectItem value="none">无分隔符</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxLength">最大长度</Label>
              <Select value={options.maxLength.toString()} onValueChange={(value) => updateOption('maxLength', parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">无限制</SelectItem>
                  <SelectItem value="25">25字符</SelectItem>
                  <SelectItem value="50">50字符</SelectItem>
                  <SelectItem value="100">100字符</SelectItem>
                  <SelectItem value="200">200字符</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="lowercase">转换为小写</Label>
                <Switch
                  id="lowercase"
                  checked={options.lowercase}
                  onCheckedChange={(checked) => updateOption('lowercase', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="strict">严格模式</Label>
                <Switch
                  id="strict"
                  checked={options.strict}
                  onCheckedChange={(checked) => updateOption('strict', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="trim">去除首尾空格</Label>
                <Switch
                  id="trim"
                  checked={options.trim}
                  onCheckedChange={(checked) => updateOption('trim', checked)}
                />
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleReset}>
              <RefreshCw className="h-4 w-4 mr-1" />
              重置配置
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            <p><strong>严格模式说明：</strong></p>
            <p>• 开启：只保留字母、数字、中文字符和分隔符，移除所有其他字符</p>
            <p>• 关闭：智能转换特殊字符（如 á → a, & → and），保留中文字符</p>
          </div>
        </CardContent>
      </Card>

      {/* 输入区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">输入文本</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="input">请输入要转换的文本</Label>
            <Textarea
              id="input"
              placeholder="在此输入要转换为slug的文本..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
              className="min-h-[120px]"
            />
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClear}>
              <Trash2 className="h-4 w-4 mr-1" />
              清空
            </Button>
          </div>

          {/* 示例按钮 */}
          <div className="space-y-2">
            <Label>常用示例：</Label>
            <div className="flex flex-wrap gap-2">
              {examples.map((example, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setInput(example)}
                  className="text-xs"
                >
                  {example}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 输出区域 */}
      {input && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">转换结果</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
                disabled={!output}
              >
                <Copy className="h-4 w-4 mr-1" />
                复制
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-muted rounded-lg font-mono text-sm break-all">
                {output || '(空结果)'}
              </div>
              
              <div className="text-sm text-muted-foreground">
                <p>原始长度: {input.length} 字符</p>
                <p>转换后长度: {output.length} 字符</p>
                {options.maxLength > 0 && output.length >= options.maxLength && (
                  <p className="text-orange-600">⚠️ 已达到最大长度限制</p>
                )}
              </div>

              {/* URL预览 */}
              {output && (
                <div className="space-y-2">
                  <Label>URL预览示例：</Label>
                  <div className="p-2 bg-blue-50 rounded text-sm font-mono break-all">
                    https://example.com/blog/{output}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
