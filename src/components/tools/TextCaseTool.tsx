'use client'

import { useState } from 'react'
import { Type, Copy, Check, RefreshCw, Download, Upload, Settings } from 'lucide-react'

type CaseType = 'upper' | 'lower' | 'title' | 'sentence' | 'camel' | 'pascal' | 'snake' | 'kebab' | 'constant' | 'dot' | 'path' | 'alternating' | 'inverse' | 'random'

interface ConversionOptions {
  trimSpaces: boolean
  removeExtraSpaces: boolean
  preserveLineBreaks: boolean
}

export default function TextCaseTool() {
  const [inputText, setInputText] = useState('')
  const [outputText, setOutputText] = useState('')
  const [selectedCase, setSelectedCase] = useState<CaseType>('upper')
  const [copied, setCopied] = useState(false)
  const [options, setOptions] = useState<ConversionOptions>({
    trimSpaces: true,
    removeExtraSpaces: true,
    preserveLineBreaks: true,
  })

  // 预处理文本
  const preprocessText = (text: string): string => {
    let result = text

    if (options.trimSpaces) {
      result = result.trim()
    }

    if (options.removeExtraSpaces) {
      result = result.replace(/\s+/g, ' ')
    }

    return result
  }

  // 转换文本大小写
  const convertCase = (text: string, caseType: CaseType): string => {
    const processedText = preprocessText(text)
    
    if (!options.preserveLineBreaks) {
      // 如果不保留换行，将所有换行替换为空格
      const singleLineText = processedText.replace(/\n+/g, ' ')
      return applyCaseConversion(singleLineText, caseType)
    } else {
      // 保留换行，分行处理
      return processedText.split('\n').map(line => applyCaseConversion(line, caseType)).join('\n')
    }
  }

  // 应用大小写转换
  const applyCaseConversion = (text: string, caseType: CaseType): string => {
    switch (caseType) {
      case 'upper':
        return text.toUpperCase()
      
      case 'lower':
        return text.toLowerCase()
      
      case 'title':
        return text.replace(/\w\S*/g, (txt) => 
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        )
      
      case 'sentence':
        return text.replace(/(^\w|\.\s+\w)/g, (letter) => letter.toUpperCase())
      
      case 'camel':
        return text
          .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
            index === 0 ? word.toLowerCase() : word.toUpperCase()
          )
          .replace(/\s+/g, '')
      
      case 'pascal':
        return text
          .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase())
          .replace(/\s+/g, '')
      
      case 'snake':
        return text
          .replace(/\W+/g, ' ')
          .split(/ |\B(?=[A-Z])/)
          .map(word => word.toLowerCase())
          .join('_')
      
      case 'kebab':
        return text
          .replace(/\W+/g, ' ')
          .split(/ |\B(?=[A-Z])/)
          .map(word => word.toLowerCase())
          .join('-')
      
      case 'constant':
        return text
          .replace(/\W+/g, ' ')
          .split(/ |\B(?=[A-Z])/)
          .map(word => word.toUpperCase())
          .join('_')
      
      case 'dot':
        return text
          .replace(/\W+/g, ' ')
          .split(/ |\B(?=[A-Z])/)
          .map(word => word.toLowerCase())
          .join('.')
      
      case 'path':
        return text
          .replace(/\W+/g, ' ')
          .split(/ |\B(?=[A-Z])/)
          .map(word => word.toLowerCase())
          .join('/')
      
      case 'alternating':
        return text
          .split('')
          .map((char, index) => 
            index % 2 === 0 ? char.toLowerCase() : char.toUpperCase()
          )
          .join('')
      
      case 'inverse':
        return text
          .split('')
          .map(char => 
            char === char.toUpperCase() ? char.toLowerCase() : char.toUpperCase()
          )
          .join('')
      
      case 'random':
        return text
          .split('')
          .map(char => 
            Math.random() > 0.5 ? char.toUpperCase() : char.toLowerCase()
          )
          .join('')
      
      default:
        return text
    }
  }

  // 处理转换
  const handleConvert = () => {
    if (!inputText.trim()) return
    const converted = convertCase(inputText, selectedCase)
    setOutputText(converted)
  }

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(outputText)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 下载文本
  const downloadText = () => {
    const blob = new Blob([outputText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `converted-${selectedCase}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }

  // 上传文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputText(content)
    }
    reader.readAsText(file)
  }

  // 交换输入输出
  const swapText = () => {
    setInputText(outputText)
    setOutputText(inputText)
  }

  // 清空所有
  const clearAll = () => {
    setInputText('')
    setOutputText('')
  }

  const caseOptions = [
    { value: 'upper', label: '大写 (UPPERCASE)', example: 'HELLO WORLD' },
    { value: 'lower', label: '小写 (lowercase)', example: 'hello world' },
    { value: 'title', label: '标题 (Title Case)', example: 'Hello World' },
    { value: 'sentence', label: '句子 (Sentence case)', example: 'Hello world' },
    { value: 'camel', label: '驼峰 (camelCase)', example: 'helloWorld' },
    { value: 'pascal', label: '帕斯卡 (PascalCase)', example: 'HelloWorld' },
    { value: 'snake', label: '蛇形 (snake_case)', example: 'hello_world' },
    { value: 'kebab', label: '短横线 (kebab-case)', example: 'hello-world' },
    { value: 'constant', label: '常量 (CONSTANT_CASE)', example: 'HELLO_WORLD' },
    { value: 'dot', label: '点号 (dot.case)', example: 'hello.world' },
    { value: 'path', label: '路径 (path/case)', example: 'hello/world' },
    { value: 'alternating', label: '交替 (aLtErNaTiNg)', example: 'hElLo WoRlD' },
    { value: 'inverse', label: '反转 (InVeRsE)', example: 'hELLO wORLD' },
    { value: 'random', label: '随机 (RaNdOm)', example: 'HeLLo WoRLd' },
  ]

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本大小写转换</h2>
        <p className="text-gray-600">
          转换文本的大小写格式，支持多种编程命名规范和文本样式
        </p>
      </div>

      {/* 转换类型选择 */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-3">选择转换类型</label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {caseOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => setSelectedCase(option.value as CaseType)}
              className={`p-3 rounded-lg border text-left transition-colors ${
                selectedCase === option.value
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <div className="font-medium">{option.label}</div>
              <div className="text-sm text-gray-500 mt-1">{option.example}</div>
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">输入文本</label>
            <div className="flex gap-2">
              <label className="text-sm text-blue-500 hover:text-blue-600 cursor-pointer">
                <input
                  type="file"
                  accept=".txt"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <span className="flex items-center gap-1">
                  <Upload className="w-4 h-4" />
                  上传文件
                </span>
              </label>
              <button
                onClick={clearAll}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                清空
              </button>
            </div>
          </div>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="请输入要转换的文本..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div className="mt-2 text-sm text-gray-500">
            字符数：{inputText.length}
          </div>
        </div>

        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">转换结果</label>
            <div className="flex gap-2">
              {outputText && (
                <>
                  <button
                    onClick={downloadText}
                    className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                  >
                    <Download className="w-4 h-4" />
                    下载
                  </button>
                  <button
                    onClick={copyToClipboard}
                    className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                  >
                    {copied ? (
                      <>
                        <Check className="w-4 h-4" />
                        已复制
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4" />
                        复制
                      </>
                    )}
                  </button>
                </>
              )}
            </div>
          </div>
          <textarea
            value={outputText}
            readOnly
            placeholder="转换结果将显示在这里..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none bg-gray-50"
          />
          <div className="mt-2 text-sm text-gray-500">
            字符数：{outputText.length}
          </div>
        </div>
      </div>

      {/* 选项设置 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2 mb-3">
          <Settings className="w-4 h-4" />
          <span className="font-medium">转换选项</span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.trimSpaces}
              onChange={(e) => setOptions({ ...options, trimSpaces: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">去除首尾空格</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.removeExtraSpaces}
              onChange={(e) => setOptions({ ...options, removeExtraSpaces: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">删除多余空格</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={options.preserveLineBreaks}
              onChange={(e) => setOptions({ ...options, preserveLineBreaks: e.target.checked })}
              className="rounded text-blue-500 focus:ring-blue-500"
            />
            <span className="text-sm">保留换行符</span>
          </label>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="mt-6 flex gap-3">
        <button
          onClick={handleConvert}
          disabled={!inputText.trim()}
          className="flex-1 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
        >
          <Type className="w-5 h-5" />
          转换文本
        </button>
        <button
          onClick={swapText}
          disabled={!inputText && !outputText}
          className="px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
        >
          <RefreshCw className="w-5 h-5" />
          交换
        </button>
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold mb-2">使用说明</h3>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• <strong>编程命名规范</strong>：支持驼峰、帕斯卡、蛇形、短横线等常见命名规范</li>
          <li>• <strong>文本样式</strong>：支持大写、小写、标题格式、句子格式等</li>
          <li>• <strong>特殊格式</strong>：支持交替大小写、反转大小写、随机大小写等</li>
          <li>• <strong>批量处理</strong>：可以上传文本文件进行批量转换</li>
          <li>• <strong>灵活选项</strong>：可以选择是否保留换行符、去除空格等</li>
        </ul>
      </div>

      {/* 常见用途 */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">常见用途</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          <div>
            <strong>编程开发：</strong>
            <span className="text-gray-600">变量命名、函数命名、常量定义</span>
          </div>
          <div>
            <strong>文档编写：</strong>
            <span className="text-gray-600">标题格式化、大小写统一</span>
          </div>
          <div>
            <strong>数据处理：</strong>
            <span className="text-gray-600">批量格式化、数据清洗</span>
          </div>
          <div>
            <strong>SEO优化：</strong>
            <span className="text-gray-600">URL格式化、标题优化</span>
          </div>
        </div>
      </div>
    </div>
  )
}
