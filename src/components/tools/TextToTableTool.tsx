'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Copy, RefreshCw, Trash2, Download, Upload } from 'lucide-react';

interface TableOptions {
  separator: string;
  hasHeader: boolean;
  outputFormat: 'html' | 'markdown' | 'csv';
  alignment: 'left' | 'center' | 'right';
  tableClass: string;
  borderStyle: 'none' | 'simple' | 'bordered' | 'striped';
}

export default function TextToTableTool() {
  const [input, setInput] = useState('');
  const [options, setOptions] = useState<TableOptions>({
    separator: ',',
    hasHeader: true,
    outputFormat: 'html',
    alignment: 'left',
    tableClass: 'table',
    borderStyle: 'bordered'
  });
  const [customSeparator, setCustomSeparator] = useState('');
  const [error, setError] = useState('');

  const parseTextToTable = useCallback((text: string, sep: string): string[][] => {
    if (!text.trim()) return [];
    
    const lines = text.trim().split('\n').filter(line => line.trim() !== '');
    const separator = sep === 'custom' ? customSeparator : sep;
    
    if (!separator) {
      throw new Error('请指定分隔符');
    }

    const rows: string[][] = [];
    
    for (const line of lines) {
      let cells: string[] = [];
      
      if (separator === 'tab') {
        cells = line.split('\t');
      } else if (separator === 'space') {
        cells = line.trim().split(/\s+/);
      } else if (separator === 'pipe') {
        cells = line.split('|').map(cell => cell.trim());
      } else {
        cells = line.split(separator);
      }
      
      rows.push(cells.map(cell => cell.trim()));
    }
    
    return rows;
  }, [customSeparator]);

  const generateHtmlTable = useCallback((rows: string[][]): string => {
    if (rows.length === 0) return '';
    
    const alignmentClass = options.alignment === 'center' ? 'text-center' : 
                          options.alignment === 'right' ? 'text-right' : 'text-left';
    
    const borderClass = options.borderStyle === 'bordered' ? 'border border-collapse' :
                       options.borderStyle === 'striped' ? 'table-striped' :
                       options.borderStyle === 'simple' ? 'border-collapse' : '';
    
    const tableClassAttr = options.tableClass ? ` class="${options.tableClass} ${borderClass}"` : ` class="${borderClass}"`;
    
    let html = `<table${tableClassAttr}>\n`;
    
    if (options.hasHeader && rows.length > 0) {
      html += '  <thead>\n    <tr>\n';
      rows[0].forEach(cell => {
        html += `      <th class="${alignmentClass}">${escapeHtml(cell)}</th>\n`;
      });
      html += '    </tr>\n  </thead>\n';
      
      if (rows.length > 1) {
        html += '  <tbody>\n';
        rows.slice(1).forEach(row => {
          html += '    <tr>\n';
          row.forEach(cell => {
            html += `      <td class="${alignmentClass}">${escapeHtml(cell)}</td>\n`;
          });
          html += '    </tr>\n';
        });
        html += '  </tbody>\n';
      }
    } else {
      html += '  <tbody>\n';
      rows.forEach(row => {
        html += '    <tr>\n';
        row.forEach(cell => {
          html += `      <td class="${alignmentClass}">${escapeHtml(cell)}</td>\n`;
        });
        html += '    </tr>\n';
      });
      html += '  </tbody>\n';
    }
    
    html += '</table>';
    return html;
  }, [options]);

  const generateMarkdownTable = useCallback((rows: string[][]): string => {
    if (rows.length === 0) return '';
    
    const alignChar = options.alignment === 'center' ? ':---:' :
                     options.alignment === 'right' ? '---:' : '---';
    
    let markdown = '';
    
    if (options.hasHeader && rows.length > 0) {
      const headerRow = rows[0];
      markdown += '| ' + headerRow.join(' | ') + ' |\n';
      markdown += '| ' + headerRow.map(() => alignChar).join(' | ') + ' |\n';
      
      if (rows.length > 1) {
        rows.slice(1).forEach(row => {
          markdown += '| ' + row.join(' | ') + ' |\n';
        });
      }
    } else {
      rows.forEach((row, index) => {
        if (index === 0) {
          markdown += '| ' + row.join(' | ') + ' |\n';
          markdown += '| ' + row.map(() => alignChar).join(' | ') + ' |\n';
        } else {
          markdown += '| ' + row.join(' | ') + ' |\n';
        }
      });
    }
    
    return markdown;
  }, [options]);

  const generateCsvTable = (rows: string[][]): string => {
    if (rows.length === 0) return '';
    
    return rows.map(row => 
      row.map(cell => {
        if (cell.includes(',') || cell.includes('"') || cell.includes('\n')) {
          return `"${cell.replace(/"/g, '""')}"`;
        }
        return cell;
      }).join(',')
    ).join('\n');
  };

  const escapeHtml = (text: string): string => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  };

  const output = useMemo(() => {
    try {
      setError('');
      
      if (!input.trim()) return '';
      
      const separator = options.separator === 'custom' ? customSeparator : options.separator;
      const rows = parseTextToTable(input, options.separator);
      
      if (rows.length === 0) return '';
      
      switch (options.outputFormat) {
        case 'html':
          return generateHtmlTable(rows);
        case 'markdown':
          return generateMarkdownTable(rows);
        case 'csv':
          return generateCsvTable(rows);
        default:
          return '';
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '转换失败');
      return '';
    }
  }, [input, options, customSeparator, generateHtmlTable, generateMarkdownTable, parseTextToTable]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(output);
    } catch (err) {
      setError('复制失败');
    }
  };

  const handleClear = () => {
    setInput('');
    setError('');
  };

  const handleReset = () => {
    setOptions({
      separator: ',',
      hasHeader: true,
      outputFormat: 'html',
      alignment: 'left',
      tableClass: 'table',
      borderStyle: 'bordered'
    });
    setCustomSeparator('');
  };

  const updateOption = <K extends keyof TableOptions>(key: K, value: TableOptions[K]) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 1024 * 1024) {
      setError('文件大小不能超过1MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setInput(content);
    };
    reader.readAsText(file);
  };

  const handleDownload = () => {
    if (!output) return;
    
    const extension = options.outputFormat === 'html' ? 'html' : 
                     options.outputFormat === 'markdown' ? 'md' : 'csv';
    const mimeType = options.outputFormat === 'html' ? 'text/html' : 
                    options.outputFormat === 'markdown' ? 'text/markdown' : 'text/csv';
    
    const blob = new Blob([output], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `table.${extension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const examples = [
    {
      name: 'CSV格式',
      data: '姓名,年龄,城市\n张三,25,北京\n李四,30,上海\n王五,28,广州'
    },
    {
      name: 'Tab分隔',
      data: '产品名称\t价格\t库存\niPhone 14\t5999\t100\nMacBook Pro\t12999\t50\niPad Air\t4399\t200'
    },
    {
      name: '管道分隔',
      data: '部门 | 员工数 | 预算\n研发部 | 50 | 500万\n销售部 | 30 | 300万\n运营部 | 20 | 200万'
    },
    {
      name: '空格分隔',
      data: '语言 分数 排名\nJavaScript 95 1\nPython 88 2\nJava 82 3\nGo 79 4'
    }
  ];

  const separatorOptions = [
    { value: ',', label: '逗号 (,)' },
    { value: 'tab', label: 'Tab制表符' },
    { value: 'space', label: '空格' },
    { value: ';', label: '分号 (;)' },
    { value: 'pipe', label: '管道 (|)' },
    { value: 'custom', label: '自定义' }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>文本转表格工具</CardTitle>
          <CardDescription>
            将结构化文本转换为HTML表格、Markdown表格或CSV格式。
            支持多种分隔符，可自定义表格样式和对齐方式。
          </CardDescription>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">转换选项</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="separator">分隔符</Label>
              <Select value={options.separator} onValueChange={(value) => updateOption('separator', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {separatorOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {options.separator === 'custom' && (
              <div className="space-y-2">
                <Label htmlFor="customSeparator">自定义分隔符</Label>
                <Input
                  id="customSeparator"
                  value={customSeparator}
                  onChange={(e) => setCustomSeparator(e.target.value)}
                  placeholder="输入分隔符"
                  maxLength={5}
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="outputFormat">输出格式</Label>
              <Select value={options.outputFormat} onValueChange={(value: 'html' | 'markdown' | 'csv') => updateOption('outputFormat', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="html">HTML表格</SelectItem>
                  <SelectItem value="markdown">Markdown表格</SelectItem>
                  <SelectItem value="csv">CSV格式</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="alignment">对齐方式</Label>
              <Select value={options.alignment} onValueChange={(value: 'left' | 'center' | 'right') => updateOption('alignment', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">左对齐</SelectItem>
                  <SelectItem value="center">居中对齐</SelectItem>
                  <SelectItem value="right">右对齐</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {options.outputFormat === 'html' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="borderStyle">边框样式</Label>
                  <Select value={options.borderStyle} onValueChange={(value: 'none' | 'simple' | 'bordered' | 'striped') => updateOption('borderStyle', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">无边框</SelectItem>
                      <SelectItem value="simple">简单边框</SelectItem>
                      <SelectItem value="bordered">完整边框</SelectItem>
                      <SelectItem value="striped">条纹样式</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tableClass">CSS类名</Label>
                  <Input
                    id="tableClass"
                    value={options.tableClass}
                    onChange={(e) => updateOption('tableClass', e.target.value)}
                    placeholder="table"
                  />
                </div>
              </>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="hasHeader"
                checked={options.hasHeader}
                onCheckedChange={(checked) => updateOption('hasHeader', checked)}
              />
              <Label htmlFor="hasHeader">第一行作为表头</Label>
            </div>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" onClick={handleReset}>
              <RefreshCw className="h-4 w-4 mr-1" />
              重置选项
            </Button>
            <Button variant="outline" asChild>
              <label>
                <Upload className="h-4 w-4 mr-1" />
                上传文件
                <input
                  type="file"
                  accept=".txt,.csv,.tsv"
                  className="hidden"
                  onChange={handleFileUpload}
                />
              </label>
            </Button>
          </div>

          {error && (
            <div className="text-sm text-red-500">{error}</div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">输入文本</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="input">请输入要转换的文本</Label>
              <Textarea
                id="input"
                placeholder="姓名,年龄,城市&#10;张三,25,北京&#10;李四,30,上海"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                className="min-h-[300px] font-mono text-sm"
              />
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClear}>
                <Trash2 className="h-4 w-4 mr-1" />
                清空
              </Button>
            </div>

            <div className="space-y-2">
              <Label>示例数据：</Label>
              <div className="flex flex-wrap gap-2">
                {examples.map((example, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setInput(example.data)}
                    className="text-xs"
                  >
                    {example.name}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">转换结果</CardTitle>
              <div className="flex gap-2">
                {output && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDownload}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      下载
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopy}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      复制
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Textarea
                value={output}
                readOnly
                className="min-h-[300px] font-mono text-xs"
                placeholder="转换结果将显示在这里..."
              />
              
              {output && (
                <div className="text-sm text-muted-foreground">
                  <p>格式: {options.outputFormat.toUpperCase()}</p>
                  <p>字符数: {output.length}</p>
                  <p>行数: {output.split('\n').length}</p>
                </div>
              )}

              {output && options.outputFormat === 'html' && (
                <div className="space-y-2">
                  <Label>HTML预览：</Label>
                  <div 
                    className="p-3 bg-white border rounded text-sm max-h-48 overflow-auto"
                    dangerouslySetInnerHTML={{ __html: output }}
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">使用说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <div><strong>支持的输入格式：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>CSV格式：使用逗号分隔的值</li>
            <li>TSV格式：使用Tab制表符分隔</li>
            <li>管道分隔：使用 | 符号分隔</li>
            <li>空格分隔：使用空格分隔（适合简单数据）</li>
            <li>自定义分隔符：可以指定任意分隔符</li>
          </ul>
          <div className="mt-4"><strong>输出格式说明：</strong></div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>HTML表格：完整的HTML表格代码，支持CSS样式</li>
            <li>Markdown表格：GitHub风格的Markdown表格</li>
            <li>CSV格式：标准的CSV格式，可导入Excel等工具</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}