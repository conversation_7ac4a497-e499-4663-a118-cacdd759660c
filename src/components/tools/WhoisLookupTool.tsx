'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Search, AlertCircle, Info, Globe, Calendar, User, Building, Phone } from 'lucide-react'

interface WhoisInfo {
  domain: string
  registrar: string
  registrationDate: string
  expirationDate: string
  status: string[]
  nameServers: string[]
  registrant: {
    name?: string
    organization?: string
    email?: string
    phone?: string
    country?: string
    state?: string
    city?: string
  }
  adminContact: {
    name?: string
    organization?: string
    email?: string
    phone?: string
    country?: string
    state?: string
    city?: string
  }
  techContact: {
    name?: string
    organization?: string
    email?: string
    phone?: string
    country?: string
    state?: string
    city?: string
  }
  raw: string
}

export default function WhoisLookupTool() {
  const [domain, setDomain] = useState('')
  const [whoisInfo, setWhoisInfo] = useState<WhoisInfo | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  // 验证域名格式
  const validateDomain = (domain: string): boolean => {
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?(\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?)*\.[a-zA-Z]{2,}$/
    return domainRegex.test(domain)
  }

  // 模拟WHOIS查询（实际应用中需要后端支持）
  const mockWhoisLookup = async (domain: string): Promise<WhoisInfo> => {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
    
    const mockData: WhoisInfo = {
      domain: domain.toLowerCase(),
      registrar: 'Example Registrar Inc.',
      registrationDate: '2020-01-15',
      expirationDate: '2025-01-15',
      status: ['CLIENT_TRANSFER_PROHIBITED', 'CLIENT_DELETE_PROHIBITED'],
      nameServers: ['ns1.example.com', 'ns2.example.com'],
      registrant: {
        name: 'John Doe',
        organization: 'Example Organization',
        email: '<EMAIL>',
        phone: '*************',
        country: 'US',
        state: 'CA',
        city: 'San Francisco',
      },
      adminContact: {
        name: 'Jane Smith',
        organization: 'Example Organization',
        email: '<EMAIL>',
        phone: '*************',
        country: 'US',
        state: 'CA',
        city: 'San Francisco',
      },
      techContact: {
        name: 'Tech Support',
        organization: 'Example Organization',
        email: '<EMAIL>',
        phone: '*************',
        country: 'US',
        state: 'CA',
        city: 'San Francisco',
      },
      raw: `Domain Name: ${domain.toUpperCase()}
Registry Domain ID: 123456789_DOMAIN_COM-VRSN
Registrar WHOIS Server: whois.example.com
Registrar URL: http://www.example.com
Updated Date: 2024-01-15T10:30:00Z
Creation Date: 2020-01-15T10:30:00Z
Registry Expiry Date: 2025-01-15T10:30:00Z
Registrar: Example Registrar Inc.
Registrar IANA ID: 123
Registrar Abuse Contact Email: <EMAIL>
Registrar Abuse Contact Phone: *************
Domain Status: clientTransferProhibited
Domain Status: clientDeleteProhibited
Name Server: NS1.EXAMPLE.COM
Name Server: NS2.EXAMPLE.COM
DNSSEC: unsigned

Registrant Name: John Doe
Registrant Organization: Example Organization
Registrant Street: 123 Main St
Registrant City: San Francisco
Registrant State/Province: CA
Registrant Postal Code: 94101
Registrant Country: US
Registrant Phone: *************
Registrant Phone Ext:
Registrant Fax:
Registrant Fax Ext:
Registrant Email: <EMAIL>`
    }
    
    return mockData
  }

  const handleLookup = async () => {
    if (!domain.trim()) {
      setError('请输入有效的域名')
      return
    }

    if (!validateDomain(domain.trim())) {
      setError('请输入有效的域名格式')
      return
    }

    setIsLoading(true)
    setError('')
    setWhoisInfo(null)

    try {
      const result = await mockWhoisLookup(domain.trim())
      setWhoisInfo(result)
    } catch (err) {
      setError('查询失败，请检查域名格式')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClear = () => {
    setDomain('')
    setWhoisInfo(null)
    setError('')
  }

  const handleCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (err) {
      console.error('复制失败: ', err)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleLookup()
    }
  }

  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    } catch {
      return dateString
    }
  }

  const ContactCard = ({ title, contact, icon: Icon }: { 
    title: string, 
    contact: WhoisInfo['registrant'] | WhoisInfo['adminContact'] | WhoisInfo['techContact'], 
    icon: React.ElementType 
  }) => (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <Icon className="w-4 h-4" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {contact.name && (
          <div className="flex justify-between">
            <span className="font-medium">姓名:</span>
            <span className="text-muted-foreground">{contact.name}</span>
          </div>
        )}
        {contact.organization && (
          <div className="flex justify-between">
            <span className="font-medium">组织:</span>
            <span className="text-muted-foreground">{contact.organization}</span>
          </div>
        )}
        {contact.email && (
          <div className="flex justify-between">
            <span className="font-medium">邮箱:</span>
            <span className="text-muted-foreground">{contact.email}</span>
          </div>
        )}
        {contact.phone && (
          <div className="flex justify-between">
            <span className="font-medium">电话:</span>
            <span className="text-muted-foreground">{contact.phone}</span>
          </div>
        )}
        {contact.country && (
          <div className="flex justify-between">
            <span className="font-medium">国家:</span>
            <span className="text-muted-foreground">{contact.country}</span>
          </div>
        )}
        {contact.state && (
          <div className="flex justify-between">
            <span className="font-medium">省份:</span>
            <span className="text-muted-foreground">{contact.state}</span>
          </div>
        )}
        {contact.city && (
          <div className="flex justify-between">
            <span className="font-medium">城市:</span>
            <span className="text-muted-foreground">{contact.city}</span>
          </div>
        )}
        {!contact.name && !contact.organization && !contact.email && !contact.phone && !contact.country && (
          <div className="text-muted-foreground italic">未提供</div>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">WHOIS 查询工具</h1>
        <p className="text-muted-foreground">查询域名的注册信息和WHOIS数据</p>
      </div>

      {/* 查询表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            WHOIS 查询
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="domain">域名</Label>
            <Input
              id="domain"
              type="text"
              value={domain}
              onChange={(e) => setDomain(e.target.value)}
              placeholder="请输入域名（如：example.com）"
              onKeyPress={handleKeyPress}
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button
              onClick={handleLookup}
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? '查询中...' : '查询'}
            </Button>
            <Button
              variant="outline"
              onClick={handleClear}
              disabled={isLoading}
            >
              清除
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 查询结果 */}
      {whoisInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              查询结果
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">基本信息</TabsTrigger>
                <TabsTrigger value="contacts">联系信息</TabsTrigger>
                <TabsTrigger value="raw">原始数据</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div className="grid gap-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>域名</Label>
                      <div className="p-3 bg-muted rounded-md font-mono">
                        {whoisInfo.domain}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>注册商</Label>
                      <div className="p-3 bg-muted rounded-md">
                        {whoisInfo.registrar}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        注册日期
                      </Label>
                      <div className="p-3 bg-muted rounded-md">
                        {formatDate(whoisInfo.registrationDate)}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        过期日期
                      </Label>
                      <div className="p-3 bg-muted rounded-md">
                        {formatDate(whoisInfo.expirationDate)}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>状态</Label>
                    <div className="p-3 bg-muted rounded-md">
                      {whoisInfo.status.map((status, index) => (
                        <div key={index} className="font-mono text-sm">
                          {status}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>域名服务器</Label>
                    <div className="p-3 bg-muted rounded-md">
                      {whoisInfo.nameServers.map((ns, index) => (
                        <div key={index} className="font-mono text-sm">
                          {ns}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="contacts" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <ContactCard
                    title="注册人"
                    contact={whoisInfo.registrant}
                    icon={User}
                  />
                  <ContactCard
                    title="管理联系人"
                    contact={whoisInfo.adminContact}
                    icon={Building}
                  />
                  <ContactCard
                    title="技术联系人"
                    contact={whoisInfo.techContact}
                    icon={Phone}
                  />
                </div>
              </TabsContent>

              <TabsContent value="raw" className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>原始数据</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCopyToClipboard(whoisInfo.raw)}
                    >
                      <Copy className="w-4 h-4 mr-2" />
                      复制
                    </Button>
                  </div>
                  <Textarea
                    value={whoisInfo.raw}
                    readOnly
                    className="font-mono text-sm min-h-[400px]"
                  />
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* 使用提示 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="w-5 h-5" />
            使用提示
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li className="flex items-start gap-2">
              <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
              输入不带协议的域名（如：example.com）
            </li>
            <li className="flex items-start gap-2">
              <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
              支持查询主流域名后缀（.com、.net、.org等）
            </li>
            <li className="flex items-start gap-2">
              <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
              某些域名可能受隐私保护，信息有限
            </li>
            <li className="flex items-start gap-2">
              <span className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></span>
              查询结果来自公开的WHOIS数据库
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
