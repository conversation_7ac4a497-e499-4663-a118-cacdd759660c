'use client'

import { useState } from 'react'
import { Calendar, Plus, Minus, Clock, CalendarDays, AlertCircle, Info } from 'lucide-react'

interface DateCalculation {
  type: 'add' | 'subtract' | 'difference'
  result: Date | number
  formatted: string
}

export default function DateCalculatorTool() {
  // 日期加减计算
  const [startDate, setStartDate] = useState(new Date().toISOString().split('T')[0])
  const [years, setYears] = useState(0)
  const [months, setMonths] = useState(0)
  const [days, setDays] = useState(0)
  const [calculationType, setCalculationType] = useState<'add' | 'subtract'>('add')
  const [dateResult, setDateResult] = useState<DateCalculation | null>(null)

  // 日期间隔计算
  const [date1, setDate1] = useState(new Date().toISOString().split('T')[0])
  const [date2, setDate2] = useState(new Date().toISOString().split('T')[0])
  const [diffResult, setDiffResult] = useState<{
    days: number
    weeks: number
    months: number
    years: number
    workdays: number
  } | null>(null)

  // 特殊日期计算
  const [specialDate, setSpecialDate] = useState(new Date().toISOString().split('T')[0])
  const [specialResults, setSpecialResults] = useState<{
    dayOfWeek: string
    dayOfYear: number
    weekOfYear: number
    quarter: number
    isLeapYear: boolean
    daysInMonth: number
    zodiacSign: string
  } | null>(null)

  // 格式化日期
  const formatDate = (date: Date): string => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    }
    return date.toLocaleDateString('zh-CN', options)
  }

  // 计算工作日
  const calculateWorkdays = (start: Date, end: Date): number => {
    let count = 0
    const current = new Date(start)
    
    while (current <= end) {
      const dayOfWeek = current.getDay()
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        count++
      }
      current.setDate(current.getDate() + 1)
    }
    
    return count
  }

  // 获取星座
  const getZodiacSign = (date: Date): string => {
    const month = date.getMonth() + 1
    const day = date.getDate()
    
    const zodiacSigns = [
      { name: '水瓶座', start: [1, 20], end: [2, 18] },
      { name: '双鱼座', start: [2, 19], end: [3, 20] },
      { name: '白羊座', start: [3, 21], end: [4, 19] },
      { name: '金牛座', start: [4, 20], end: [5, 20] },
      { name: '双子座', start: [5, 21], end: [6, 21] },
      { name: '巨蟹座', start: [6, 22], end: [7, 22] },
      { name: '狮子座', start: [7, 23], end: [8, 22] },
      { name: '处女座', start: [8, 23], end: [9, 22] },
      { name: '天秤座', start: [9, 23], end: [10, 23] },
      { name: '天蝎座', start: [10, 24], end: [11, 22] },
      { name: '射手座', start: [11, 23], end: [12, 21] },
      { name: '摩羯座', start: [12, 22], end: [1, 19] }
    ]
    
    for (const sign of zodiacSigns) {
      if (
        (month === sign.start[0] && day >= sign.start[1]) ||
        (month === sign.end[0] && day <= sign.end[1])
      ) {
        return sign.name
      }
    }
    
    return '摩羯座' // 跨年的情况
  }

  // 日期加减计算
  const handleDateCalculation = () => {
    const date = new Date(startDate)
    
    if (calculationType === 'add') {
      date.setFullYear(date.getFullYear() + years)
      date.setMonth(date.getMonth() + months)
      date.setDate(date.getDate() + days)
    } else {
      date.setFullYear(date.getFullYear() - years)
      date.setMonth(date.getMonth() - months)
      date.setDate(date.getDate() - days)
    }
    
    setDateResult({
      type: calculationType,
      result: date,
      formatted: formatDate(date)
    })
  }

  // 日期间隔计算
  const handleDifferenceCalculation = () => {
    const d1 = new Date(date1)
    const d2 = new Date(date2)
    
    const diffTime = Math.abs(d2.getTime() - d1.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    // 计算月份差
    let monthsDiff = (d2.getFullYear() - d1.getFullYear()) * 12
    monthsDiff += d2.getMonth() - d1.getMonth()
    monthsDiff = Math.abs(monthsDiff)
    
    // 计算年份差
    const yearsDiff = Math.abs(d2.getFullYear() - d1.getFullYear())
    
    // 计算工作日
    const workdays = calculateWorkdays(
      d1 < d2 ? d1 : d2,
      d1 < d2 ? d2 : d1
    )
    
    setDiffResult({
      days: diffDays,
      weeks: Math.floor(diffDays / 7),
      months: monthsDiff,
      years: yearsDiff,
      workdays: workdays
    })
  }

  // 特殊日期信息计算
  const handleSpecialDateCalculation = () => {
    const date = new Date(specialDate)
    
    // 星期几
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    const dayOfWeek = weekdays[date.getDay()]
    
    // 一年中的第几天
    const start = new Date(date.getFullYear(), 0, 0)
    const diff = date.getTime() - start.getTime()
    const dayOfYear = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    // 第几周
    const weekOfYear = Math.ceil(dayOfYear / 7)
    
    // 季度
    const quarter = Math.floor(date.getMonth() / 3) + 1
    
    // 是否闰年
    const year = date.getFullYear()
    const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0
    
    // 当月天数
    const daysInMonth = new Date(year, date.getMonth() + 1, 0).getDate()
    
    // 星座
    const zodiacSign = getZodiacSign(date)
    
    setSpecialResults({
      dayOfWeek,
      dayOfYear,
      weekOfYear,
      quarter,
      isLeapYear,
      daysInMonth,
      zodiacSign
    })
  }

  // 重置所有结果
  const resetAll = () => {
    setDateResult(null)
    setDiffResult(null)
    setSpecialResults(null)
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">日期计算器</h2>
        <p className="text-gray-600">
          日期加减、间隔计算、节假日查询等多功能日期工具
        </p>
      </div>

      {/* 日期加减计算 */}
      <div className="mb-8 bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Calendar className="w-5 h-5" />
          日期加减计算
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">起始日期</label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">计算类型</label>
            <div className="flex gap-4">
              <button
                onClick={() => setCalculationType('add')}
                className={`flex items-center gap-2 px-4 py-2 rounded-md ${
                  calculationType === 'add'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Plus className="w-4 h-4" />
                加法
              </button>
              <button
                onClick={() => setCalculationType('subtract')}
                className={`flex items-center gap-2 px-4 py-2 rounded-md ${
                  calculationType === 'subtract'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Minus className="w-4 h-4" />
                减法
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">年</label>
              <input
                type="number"
                value={years}
                onChange={(e) => setYears(parseInt(e.target.value) || 0)}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">月</label>
              <input
                type="number"
                value={months}
                onChange={(e) => setMonths(parseInt(e.target.value) || 0)}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">日</label>
              <input
                type="number"
                value={days}
                onChange={(e) => setDays(parseInt(e.target.value) || 0)}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <button
            onClick={handleDateCalculation}
            className="w-full py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            计算结果
          </button>
          
          {dateResult && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-gray-600 mb-1">计算结果：</p>
              <p className="text-lg font-semibold text-blue-900">
                {dateResult.formatted}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 日期间隔计算 */}
      <div className="mb-8 bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <CalendarDays className="w-5 h-5" />
          日期间隔计算
        </h3>
        
        <div className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">开始日期</label>
              <input
                type="date"
                value={date1}
                onChange={(e) => setDate1(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">结束日期</label>
              <input
                type="date"
                value={date2}
                onChange={(e) => setDate2(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <button
            onClick={handleDifferenceCalculation}
            className="w-full py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            计算间隔
          </button>
          
          {diffResult && (
            <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="p-3 bg-gray-50 rounded-lg text-center">
                <p className="text-2xl font-bold text-gray-900">{diffResult.days}</p>
                <p className="text-sm text-gray-600">天</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg text-center">
                <p className="text-2xl font-bold text-gray-900">{diffResult.weeks}</p>
                <p className="text-sm text-gray-600">周</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg text-center">
                <p className="text-2xl font-bold text-gray-900">{diffResult.months}</p>
                <p className="text-sm text-gray-600">月</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg text-center">
                <p className="text-2xl font-bold text-gray-900">{diffResult.years}</p>
                <p className="text-sm text-gray-600">年</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg text-center">
                <p className="text-2xl font-bold text-gray-900">{diffResult.workdays}</p>
                <p className="text-sm text-gray-600">工作日</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 特殊日期信息 */}
      <div className="mb-8 bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Clock className="w-5 h-5" />
          日期详细信息
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">选择日期</label>
            <input
              type="date"
              value={specialDate}
              onChange={(e) => setSpecialDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <button
            onClick={handleSpecialDateCalculation}
            className="w-full py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            查看详情
          </button>
          
          {specialResults && (
            <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">星期</p>
                <p className="font-semibold">{specialResults.dayOfWeek}</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">本年第几天</p>
                <p className="font-semibold">第 {specialResults.dayOfYear} 天</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">本年第几周</p>
                <p className="font-semibold">第 {specialResults.weekOfYear} 周</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">季度</p>
                <p className="font-semibold">第 {specialResults.quarter} 季度</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">是否闰年</p>
                <p className="font-semibold">{specialResults.isLeapYear ? '是' : '否'}</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">当月天数</p>
                <p className="font-semibold">{specialResults.daysInMonth} 天</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg col-span-2 md:col-span-3">
                <p className="text-sm text-gray-600">星座</p>
                <p className="font-semibold">{specialResults.zodiacSign}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 清空按钮 */}
      <div className="flex justify-center mb-6">
        <button
          onClick={resetAll}
          className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
        >
          清空所有结果
        </button>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>日期加减：可以对指定日期进行年、月、日的加减运算</li>
              <li>间隔计算：计算两个日期之间的天数、周数、月数等</li>
              <li>工作日计算：自动排除周末，计算实际工作日数量</li>
              <li>特殊信息：查看日期的星期、季度、星座等详细信息</li>
              <li>支持公历日期，暂不支持农历转换</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 常用日期提示 */}
      <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-yellow-900">
            <h3 className="font-semibold mb-1">温馨提示</h3>
            <p>工作日计算仅排除周末，未考虑法定节假日。如需精确计算，请根据实际情况调整。</p>
          </div>
        </div>
      </div>
    </div>
  )
}
