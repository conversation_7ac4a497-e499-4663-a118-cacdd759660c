'use client';

import React, { useState, use<PERSON>emo, use<PERSON><PERSON>back } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Copy, 
  RefreshCw, 
  Trash2, 
  Download, 
  Upload, 
  GitCommit, 
  FileText, 
  ArrowLeftRight, 
  Plus, 
  Minus, 
  Equal,
  Eye,
  EyeOff,
  <PERSON>,
  <PERSON>r,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Lightbulb
} from 'lucide-react';

interface TextVersion {
  id: string;
  name: string;
  content: string;
  author: string;
  timestamp: string;
  description: string;
}

interface DiffChange {
  type: 'added' | 'removed' | 'modified' | 'unchanged';
  oldText: string;
  newText: string;
  lineNumber: number;
  length: number;
}

interface DiffOptions {
  ignoreCase: boolean;
  ignoreWhitespace: boolean;
  ignoreEmptyLines: boolean;
  contextLines: number;
  diffMode: 'chars' | 'words' | 'lines';
  showLineNumbers: boolean;
  highlightMoved: boolean;
  compactView: boolean;
}

export default function TextVersionDiffTool() {
  const [versions, setVersions] = useState<TextVersion[]>([
    {
      id: '1',
      name: '版本 1',
      content: '',
      author: '用户',
      timestamp: new Date().toLocaleString(),
      description: '原始版本'
    },
    {
      id: '2',
      name: '版本 2',
      content: '',
      author: '用户',
      timestamp: new Date().toLocaleString(),
      description: '修改版本'
    }
  ]);
  
  const [selectedVersions, setSelectedVersions] = useState<{ old: string; new: string }>({
    old: '1',
    new: '2'
  });
  
  const [options, setOptions] = useState<DiffOptions>({
    ignoreCase: false,
    ignoreWhitespace: false,
    ignoreEmptyLines: false,
    contextLines: 3,
    diffMode: 'lines',
    showLineNumbers: true,
    highlightMoved: false,
    compactView: false,
  });

  const [newVersionData, setNewVersionData] = useState({
    name: '',
    author: '用户',
    description: '',
  });
  
  const [error, setError] = useState('');

  const getVersionById = useCallback((id: string) => {
    return versions.find(v => v.id === id);
  }, [versions]);

  const normalizeText = useCallback((text: string): string => {
    let normalized = text;
    
    if (options.ignoreCase) {
      normalized = normalized.toLowerCase();
    }
    
    if (options.ignoreWhitespace) {
      normalized = normalized.replace(/\s+/g, ' ').trim();
    }
    
    if (options.ignoreEmptyLines) {
      normalized = normalized.replace(/\n\s*\n/g, '\n');
    }
    
    return normalized;
  }, [options]);

  const calculateDiff = useMemo(() => {
    const oldVersion = getVersionById(selectedVersions.old);
    const newVersion = getVersionById(selectedVersions.new);
    
    if (!oldVersion || !newVersion) return [];

    const oldText = normalizeText(oldVersion.content);
    const newText = normalizeText(newVersion.content);
    
    const changes: DiffChange[] = [];
    
    if (options.diffMode === 'lines') {
      const oldLines = oldText.split('\n');
      const newLines = newText.split('\n');
      
      // 简化的行级差异算法
      const maxLines = Math.max(oldLines.length, newLines.length);
      
      for (let i = 0; i < maxLines; i++) {
        const oldLine = oldLines[i] || '';
        const newLine = newLines[i] || '';
        
        if (oldLine === newLine) {
          changes.push({
            type: 'unchanged',
            oldText: oldLine,
            newText: newLine,
            lineNumber: i + 1,
            length: oldLine.length,
          });
        } else if (oldLine && newLine) {
          changes.push({
            type: 'modified',
            oldText: oldLine,
            newText: newLine,
            lineNumber: i + 1,
            length: Math.max(oldLine.length, newLine.length),
          });
        } else if (oldLine && !newLine) {
          changes.push({
            type: 'removed',
            oldText: oldLine,
            newText: '',
            lineNumber: i + 1,
            length: oldLine.length,
          });
        } else if (!oldLine && newLine) {
          changes.push({
            type: 'added',
            oldText: '',
            newText: newLine,
            lineNumber: i + 1,
            length: newLine.length,
          });
        }
      }
    } else if (options.diffMode === 'words') {
      const oldWords = oldText.split(/\s+/);
      const newWords = newText.split(/\s+/);
      
      const maxWords = Math.max(oldWords.length, newWords.length);
      
      for (let i = 0; i < maxWords; i++) {
        const oldWord = oldWords[i] || '';
        const newWord = newWords[i] || '';
        
        if (oldWord === newWord) {
          changes.push({
            type: 'unchanged',
            oldText: oldWord,
            newText: newWord,
            lineNumber: 0,
            length: oldWord.length,
          });
        } else if (oldWord && newWord) {
          changes.push({
            type: 'modified',
            oldText: oldWord,
            newText: newWord,
            lineNumber: 0,
            length: Math.max(oldWord.length, newWord.length),
          });
        } else if (oldWord && !newWord) {
          changes.push({
            type: 'removed',
            oldText: oldWord,
            newText: '',
            lineNumber: 0,
            length: oldWord.length,
          });
        } else if (!oldWord && newWord) {
          changes.push({
            type: 'added',
            oldText: '',
            newText: newWord,
            lineNumber: 0,
            length: newWord.length,
          });
        }
      }
    } else {
      // 字符级差异 (简化实现)
      const minLength = Math.min(oldText.length, newText.length);
      const maxLength = Math.max(oldText.length, newText.length);
      
      for (let i = 0; i < maxLength; i++) {
        const oldChar = oldText[i] || '';
        const newChar = newText[i] || '';
        
        if (oldChar === newChar) {
          changes.push({
            type: 'unchanged',
            oldText: oldChar,
            newText: newChar,
            lineNumber: 0,
            length: 1,
          });
        } else if (i < minLength) {
          changes.push({
            type: 'modified',
            oldText: oldChar,
            newText: newChar,
            lineNumber: 0,
            length: 1,
          });
        } else if (i < oldText.length) {
          changes.push({
            type: 'removed',
            oldText: oldChar,
            newText: '',
            lineNumber: 0,
            length: 1,
          });
        } else {
          changes.push({
            type: 'added',
            oldText: '',
            newText: newChar,
            lineNumber: 0,
            length: 1,
          });
        }
      }
    }
    
    return changes;
  }, [selectedVersions, versions, options, getVersionById, normalizeText]);

  const statistics = useMemo(() => {
    const stats = calculateDiff.reduce((acc, change) => {
      acc[change.type] = (acc[change.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalChanges = (stats.added || 0) + (stats.removed || 0) + (stats.modified || 0);
    const totalItems = calculateDiff.length;
    const changePercentage = totalItems > 0 ? Math.round((totalChanges / totalItems) * 100) : 0;

    return {
      ...stats,
      total: totalItems,
      totalChanges,
      changePercentage,
      unchanged: stats.unchanged || 0,
    };
  }, [calculateDiff]);

  const renderDiff = useMemo(() => {
    if (options.compactView) {
      // 紧凑视图：只显示变化的部分
      const changedItems = calculateDiff.filter(change => change.type !== 'unchanged');
      if (changedItems.length === 0) {
        return <div className="text-center py-8 text-muted-foreground">没有发现差异</div>;
      }
      
      return changedItems.map((change, index) => (
        <div key={index} className={`p-2 rounded border-l-4 ${
          change.type === 'added' ? 'bg-green-50 border-green-400' :
          change.type === 'removed' ? 'bg-red-50 border-red-400' :
          change.type === 'modified' ? 'bg-blue-50 border-blue-400' :
          'bg-gray-50 border-gray-400'
        }`}>
          {options.showLineNumbers && options.diffMode === 'lines' && (
            <span className="text-xs text-muted-foreground mr-2">L{change.lineNumber}</span>
          )}
          <div className="flex gap-2">
            {change.oldText && (
              <span className="flex-1">
                <Badge variant="outline" className="mr-1 text-xs">旧</Badge>
                {change.oldText}
              </span>
            )}
            {change.newText && (
              <span className="flex-1">
                <Badge variant="outline" className="mr-1 text-xs">新</Badge>
                {change.newText}
              </span>
            )}
          </div>
        </div>
      ));
    }

    // 完整视图：显示所有内容
    return calculateDiff.map((change, index) => {
      const showContext = change.type === 'unchanged' && 
        (index < options.contextLines || 
         index >= calculateDiff.length - options.contextLines ||
         calculateDiff.slice(Math.max(0, index - options.contextLines), index + options.contextLines + 1)
           .some(c => c.type !== 'unchanged'));

      if (change.type === 'unchanged' && !showContext) {
        return null;
      }

      return (
        <div key={index} className={`p-2 font-mono text-sm border-l-4 ${
          change.type === 'added' ? 'bg-green-50 border-green-400' :
          change.type === 'removed' ? 'bg-red-50 border-red-400' :
          change.type === 'modified' ? 'bg-blue-50 border-blue-400' :
          'bg-gray-50 border-gray-300'
        }`}>
          <div className="flex items-center gap-2">
            {options.showLineNumbers && options.diffMode === 'lines' && (
              <span className="text-xs text-muted-foreground w-8">
                {change.lineNumber}
              </span>
            )}
            <div className="flex-1">
              {change.type === 'added' && <Plus className="h-3 w-3 text-green-600 inline mr-1" />}
              {change.type === 'removed' && <Minus className="h-3 w-3 text-red-600 inline mr-1" />}
              {change.type === 'modified' && <ArrowLeftRight className="h-3 w-3 text-blue-600 inline mr-1" />}
              {change.type === 'unchanged' && <Equal className="h-3 w-3 text-gray-400 inline mr-1" />}
              
              {change.type === 'modified' ? (
                <div className="space-y-1">
                  <div className="text-red-600 line-through">{change.oldText}</div>
                  <div className="text-green-600">{change.newText}</div>
                </div>
              ) : (
                <span>{change.newText || change.oldText}</span>
              )}
            </div>
          </div>
        </div>
      );
    }).filter(Boolean);
  }, [calculateDiff, options]);

  const updateVersion = (id: string, updates: Partial<TextVersion>) => {
    setVersions(prev => prev.map(version => 
      version.id === id ? { ...version, ...updates, timestamp: new Date().toLocaleString() } : version
    ));
  };

  const addVersion = () => {
    if (!newVersionData.name.trim()) {
      setError('请输入版本名称');
      return;
    }

    const newVersion: TextVersion = {
      id: Date.now().toString(),
      name: newVersionData.name,
      content: '',
      author: newVersionData.author,
      timestamp: new Date().toLocaleString(),
      description: newVersionData.description || '新版本',
    };

    setVersions(prev => [...prev, newVersion]);
    setNewVersionData({ name: '', author: '用户', description: '' });
    setError('');
  };

  const removeVersion = (id: string) => {
    if (versions.length <= 2) {
      setError('至少需要保留两个版本');
      return;
    }

    setVersions(prev => prev.filter(v => v.id !== id));
    
    // 重置选择如果被删除的版本被选中
    if (selectedVersions.old === id) {
      setSelectedVersions(prev => ({ ...prev, old: versions.filter(v => v.id !== id)[0]?.id || '' }));
    }
    if (selectedVersions.new === id) {
      setSelectedVersions(prev => ({ ...prev, new: versions.filter(v => v.id !== id)[1]?.id || '' }));
    }
  };

  const handleExport = () => {
    const oldVersion = getVersionById(selectedVersions.old);
    const newVersion = getVersionById(selectedVersions.new);
    
    const exportData = {
      comparison: {
        oldVersion: oldVersion?.name,
        newVersion: newVersion?.name,
        timestamp: new Date().toISOString(),
      },
      statistics,
      options,
      changes: calculateDiff.map(change => ({
        type: change.type,
        oldText: change.oldText,
        newText: change.newText,
        lineNumber: change.lineNumber,
      })),
      versions: {
        old: oldVersion,
        new: newVersion,
      },
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'version-comparison.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleClear = () => {
    setVersions(prev => prev.map(version => ({ ...version, content: '' })));
    setError('');
  };

  const examples = [
    {
      name: '代码版本对比',
      oldText: `function calculateTotal(items) {
  let total = 0;
  for (let i = 0; i < items.length; i++) {
    total += items[i].price;
  }
  return total;
}`,
      newText: `function calculateTotal(items) {
  return items.reduce((total, item) => {
    return total + (item.price || 0);
  }, 0);
}`,
      description: '代码重构前后的对比'
    },
    {
      name: '文档修改',
      oldText: `产品说明书

我们的产品具有以下特点：
1. 高性能处理器
2. 大容量存储
3. 长续航电池

注意事项：
请在使用前阅读说明书。`,
      newText: `产品说明书 v2.0

我们的产品具有以下特点：
1. 高性能处理器（最新一代）
2. 大容量SSD存储
3. 超长续航电池（24小时）
4. 防水设计

注意事项：
请在使用前仔细阅读说明书。
保修期为一年。`,
      description: '产品文档的更新版本'
    }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>文本版本对比</CardTitle>
          <CardDescription>
            强大的多版本文本对比工具，支持字符级、词级、行级差异检测，
            提供详细的统计分析和可视化对比结果。
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="compare" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="compare">版本对比</TabsTrigger>
          <TabsTrigger value="manage">版本管理</TabsTrigger>
          <TabsTrigger value="settings">对比设置</TabsTrigger>
        </TabsList>

        <TabsContent value="compare" className="space-y-4">
          {/* 版本选择 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">选择对比版本</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>原始版本</Label>
                  <Select 
                    value={selectedVersions.old} 
                    onValueChange={(value) => setSelectedVersions(prev => ({ ...prev, old: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {versions.map(version => (
                        <SelectItem key={version.id} value={version.id}>
                          {version.name} - {version.author}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>新版本</Label>
                  <Select 
                    value={selectedVersions.new} 
                    onValueChange={(value) => setSelectedVersions(prev => ({ ...prev, new: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {versions.map(version => (
                        <SelectItem key={version.id} value={version.id}>
                          {version.name} - {version.author}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 统计信息 */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">对比统计</CardTitle>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleExport}>
                    <Download className="h-4 w-4 mr-1" />
                    导出报告
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{statistics.added || 0}</div>
                  <div className="text-sm text-muted-foreground">新增</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{statistics.removed || 0}</div>
                  <div className="text-sm text-muted-foreground">删除</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{statistics.modified || 0}</div>
                  <div className="text-sm text-muted-foreground">修改</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{statistics.unchanged}</div>
                  <div className="text-sm text-muted-foreground">未变化</div>
                </div>
              </div>
              
              <div className="mt-4 text-center">
                <Badge variant="outline" className="text-lg px-4 py-2">
                  变化率: {statistics.changePercentage}%
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* 对比结果 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">对比结果</CardTitle>
              <CardDescription>
                {options.diffMode === 'lines' ? '按行对比' : 
                 options.diffMode === 'words' ? '按词对比' : '按字符对比'} | 
                {options.compactView ? '紧凑视图' : '完整视图'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <div className="text-sm text-red-500 bg-red-50 p-2 rounded mb-4">
                  {error}
                </div>
              )}
              
              <div className="space-y-1 max-h-[600px] overflow-y-auto border rounded p-2 bg-gray-50">
                {renderDiff.length > 0 ? renderDiff : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Equal className="h-12 w-12 mx-auto mb-2" />
                    <div className="text-lg font-medium">版本完全相同</div>
                    <div className="text-sm">没有发现任何差异</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="manage" className="space-y-4">
          {/* 添加新版本 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">添加新版本</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="versionName">版本名称</Label>
                  <Input
                    id="versionName"
                    placeholder="例如：版本 3"
                    value={newVersionData.name}
                    onChange={(e) => setNewVersionData(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="versionAuthor">作者</Label>
                  <Input
                    id="versionAuthor"
                    placeholder="作者名称"
                    value={newVersionData.author}
                    onChange={(e) => setNewVersionData(prev => ({ ...prev, author: e.target.value }))}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="versionDescription">版本描述</Label>
                <Input
                  id="versionDescription"
                  placeholder="版本描述（可选）"
                  value={newVersionData.description}
                  onChange={(e) => setNewVersionData(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
              
              <Button onClick={addVersion}>
                <Plus className="h-4 w-4 mr-1" />
                添加版本
              </Button>
            </CardContent>
          </Card>

          {/* 版本列表 */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">版本管理</CardTitle>
                <Button variant="outline" size="sm" onClick={handleClear}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  清空内容
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {versions.map((version) => (
                <div key={version.id} className="border rounded p-4 space-y-4">
                  <div className="flex justify-between items-start">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{version.name}</h4>
                        <Badge variant="outline">
                          <User className="h-3 w-3 mr-1" />
                          {version.author}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground flex items-center gap-2">
                        <Clock className="h-3 w-3" />
                        {version.timestamp}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {version.description}
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeVersion(version.id)}
                      disabled={versions.length <= 2}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <Textarea
                    placeholder={`输入${version.name}的内容...`}
                    value={version.content}
                    onChange={(e) => updateVersion(version.id, { content: e.target.value })}
                    className="min-h-[200px] font-mono text-sm"
                  />
                </div>
              ))}
            </CardContent>
          </Card>

          {/* 示例数据 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">示例数据</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {examples.map((example, index) => (
                <div key={index} className="p-3 border rounded">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">{example.name}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        updateVersion('1', { content: example.oldText, name: '原始版本' });
                        updateVersion('2', { content: example.newText, name: '修改版本' });
                      }}
                    >
                      使用示例
                    </Button>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {example.description}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">对比设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-medium">对比模式</h4>
                <div className="space-y-2">
                  <Label>对比粒度</Label>
                  <Select 
                    value={options.diffMode} 
                    onValueChange={(value: 'chars' | 'words' | 'lines') => 
                      setOptions(prev => ({ ...prev, diffMode: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="chars">字符级对比</SelectItem>
                      <SelectItem value="words">词级对比</SelectItem>
                      <SelectItem value="lines">行级对比</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">忽略选项</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="ignoreCase"
                      checked={options.ignoreCase}
                      onCheckedChange={(checked) => setOptions(prev => ({ ...prev, ignoreCase: checked }))}
                    />
                    <Label htmlFor="ignoreCase">忽略大小写</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="ignoreWhitespace"
                      checked={options.ignoreWhitespace}
                      onCheckedChange={(checked) => setOptions(prev => ({ ...prev, ignoreWhitespace: checked }))}
                    />
                    <Label htmlFor="ignoreWhitespace">忽略空格</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="ignoreEmptyLines"
                      checked={options.ignoreEmptyLines}
                      onCheckedChange={(checked) => setOptions(prev => ({ ...prev, ignoreEmptyLines: checked }))}
                    />
                    <Label htmlFor="ignoreEmptyLines">忽略空行</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="highlightMoved"
                      checked={options.highlightMoved}
                      onCheckedChange={(checked) => setOptions(prev => ({ ...prev, highlightMoved: checked }))}
                    />
                    <Label htmlFor="highlightMoved">突出显示移动</Label>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">显示选项</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="showLineNumbers"
                      checked={options.showLineNumbers}
                      onCheckedChange={(checked) => setOptions(prev => ({ ...prev, showLineNumbers: checked }))}
                    />
                    <Label htmlFor="showLineNumbers">显示行号</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="compactView"
                      checked={options.compactView}
                      onCheckedChange={(checked) => setOptions(prev => ({ ...prev, compactView: checked }))}
                    />
                    <Label htmlFor="compactView">紧凑视图</Label>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="contextLines">上下文行数</Label>
                  <Input
                    id="contextLines"
                    type="number"
                    min="0"
                    max="10"
                    value={options.contextLines}
                    onChange={(e) => setOptions(prev => ({ ...prev, contextLines: parseInt(e.target.value) || 0 }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                功能特点
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 多版本文本管理</li>
                <li>• 字符/词/行级对比</li>
                <li>• 智能差异检测</li>
                <li>• 可视化对比结果</li>
                <li>• 详细统计分析</li>
                <li>• 灵活的显示选项</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium">使用技巧</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 可以创建多个版本进行对比</li>
                <li>• 使用紧凑视图只查看变化部分</li>
                <li>• 调整上下文行数控制显示范围</li>
                <li>• 导出对比报告保存结果</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}