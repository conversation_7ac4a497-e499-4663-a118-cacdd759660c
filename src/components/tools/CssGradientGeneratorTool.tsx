'use client';

import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { 
  Palette, 
  Copy, 
  Download,
  Plus,
  Minus,
  RotateCw,
  Move,
  Zap,
  Code,
  Eye,
  Layers,
  Settings,
  Trash2
} from 'lucide-react';

interface ColorStop {
  id: string;
  color: string;
  position: number;
  opacity: number;
}

interface GradientConfig {
  type: 'linear' | 'radial' | 'conic';
  angle: number;
  shape: 'circle' | 'ellipse';
  position: { x: number; y: number };
  colorStops: ColorStop[];
  repeat: boolean;
}

interface GradientPreset {
  name: string;
  description: string;
  config: GradientConfig;
}

const CssGradientGeneratorTool: React.FC = () => {
  const [gradient, setGradient] = useState<GradientConfig>({
    type: 'linear',
    angle: 90,
    shape: 'circle',
    position: { x: 50, y: 50 },
    colorStops: [
      { id: '1', color: '#ff6b6b', position: 0, opacity: 1 },
      { id: '2', color: '#4ecdc4', position: 100, opacity: 1 }
    ],
    repeat: false
  });

  const [selectedStop, setSelectedStop] = useState<string>('1');
  const [previewMode, setPreviewMode] = useState<'full' | 'element'>('element');
  const [exportFormat, setExportFormat] = useState<'css' | 'scss' | 'tailwind'>('css');
  const [customSize, setCustomSize] = useState({ width: 300, height: 200 });
  const [showGrid, setShowGrid] = useState(false);
  const gradientRef = useRef<HTMLDivElement>(null);

  // Predefined gradient presets
  const presets: GradientPreset[] = [
    {
      name: 'Sunset',
      description: 'Warm orange to pink gradient',
      config: {
        type: 'linear',
        angle: 45,
        shape: 'circle',
        position: { x: 50, y: 50 },
        colorStops: [
          { id: '1', color: '#ff9a9e', position: 0, opacity: 1 },
          { id: '2', color: '#fecfef', position: 50, opacity: 1 },
          { id: '3', color: '#fecfef', position: 100, opacity: 1 }
        ],
        repeat: false
      }
    },
    {
      name: 'Ocean',
      description: 'Deep blue to light blue',
      config: {
        type: 'linear',
        angle: 180,
        shape: 'circle',
        position: { x: 50, y: 50 },
        colorStops: [
          { id: '1', color: '#667eea', position: 0, opacity: 1 },
          { id: '2', color: '#764ba2', position: 100, opacity: 1 }
        ],
        repeat: false
      }
    },
    {
      name: 'Rainbow',
      description: 'Full spectrum radial gradient',
      config: {
        type: 'radial',
        angle: 0,
        shape: 'circle',
        position: { x: 50, y: 50 },
        colorStops: [
          { id: '1', color: '#ff0000', position: 0, opacity: 1 },
          { id: '2', color: '#ff9900', position: 16.66, opacity: 1 },
          { id: '3', color: '#ffff00', position: 33.33, opacity: 1 },
          { id: '4', color: '#00ff00', position: 50, opacity: 1 },
          { id: '5', color: '#0099ff', position: 66.66, opacity: 1 },
          { id: '6', color: '#6600ff', position: 83.33, opacity: 1 },
          { id: '7', color: '#ff0099', position: 100, opacity: 1 }
        ],
        repeat: false
      }
    },
    {
      name: 'Metallic',
      description: 'Chrome-like metallic effect',
      config: {
        type: 'linear',
        angle: 90,
        shape: 'circle',
        position: { x: 50, y: 50 },
        colorStops: [
          { id: '1', color: '#eee', position: 0, opacity: 1 },
          { id: '2', color: '#999', position: 50, opacity: 1 },
          { id: '3', color: '#777', position: 51, opacity: 1 },
          { id: '4', color: '#0a0a0a', position: 100, opacity: 1 }
        ],
        repeat: false
      }
    },
    {
      name: 'Neon',
      description: 'Bright neon glow effect',
      config: {
        type: 'radial',
        angle: 0,
        shape: 'circle',
        position: { x: 50, y: 50 },
        colorStops: [
          { id: '1', color: '#00ffff', position: 0, opacity: 1 },
          { id: '2', color: '#ff00ff', position: 50, opacity: 0.8 },
          { id: '3', color: '#000000', position: 100, opacity: 1 }
        ],
        repeat: false
      }
    }
  ];

  // Generate CSS gradient string
  const generateGradientCSS = useCallback((config: GradientConfig): string => {
    const { type, angle, shape, position, colorStops, repeat } = config;
    
    const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);
    const stopsString = sortedStops
      .map(stop => {
        const alpha = stop.opacity < 1 ? `${Math.round(stop.opacity * 255).toString(16).padStart(2, '0')}` : '';
        const color = stop.opacity < 1 ? `${stop.color}${alpha}` : stop.color;
        return `${color} ${stop.position}%`;
      })
      .join(', ');

    const prefix = repeat ? 'repeating-' : '';

    switch (type) {
      case 'linear':
        return `${prefix}linear-gradient(${angle}deg, ${stopsString})`;
      case 'radial':
        const shapeStr = shape === 'circle' ? 'circle' : 'ellipse';
        const positionStr = `at ${position.x}% ${position.y}%`;
        return `${prefix}radial-gradient(${shapeStr} ${positionStr}, ${stopsString})`;
      case 'conic':
        return `${prefix}conic-gradient(from ${angle}deg at ${position.x}% ${position.y}%, ${stopsString})`;
      default:
        return `linear-gradient(${angle}deg, ${stopsString})`;
    }
  }, []);

  const currentGradientCSS = useMemo(() => generateGradientCSS(gradient), [gradient, generateGradientCSS]);

  // Add new color stop
  const addColorStop = useCallback(() => {
    const newId = Date.now().toString();
    const newPosition = gradient.colorStops.length > 0 
      ? Math.min(100, Math.max(...gradient.colorStops.map(s => s.position)) + 20)
      : 50;
    
    const newStop: ColorStop = {
      id: newId,
      color: '#ff6b6b',
      position: newPosition,
      opacity: 1
    };

    setGradient(prev => ({
      ...prev,
      colorStops: [...prev.colorStops, newStop].sort((a, b) => a.position - b.position)
    }));
    setSelectedStop(newId);
  }, [gradient.colorStops]);

  // Remove color stop
  const removeColorStop = useCallback((id: string) => {
    if (gradient.colorStops.length <= 2) return;
    
    setGradient(prev => ({
      ...prev,
      colorStops: prev.colorStops.filter(stop => stop.id !== id)
    }));
    
    if (selectedStop === id) {
      setSelectedStop(gradient.colorStops.find(s => s.id !== id)?.id || '');
    }
  }, [gradient.colorStops, selectedStop]);

  // Update color stop
  const updateColorStop = useCallback((id: string, updates: Partial<ColorStop>) => {
    setGradient(prev => ({
      ...prev,
      colorStops: prev.colorStops.map(stop => 
        stop.id === id ? { ...stop, ...updates } : stop
      )
    }));
  }, []);

  // Apply preset
  const applyPreset = useCallback((preset: GradientPreset) => {
    setGradient(preset.config);
    setSelectedStop(preset.config.colorStops[0]?.id || '');
  }, []);

  // Generate random gradient
  const generateRandomGradient = useCallback(() => {
    const types: GradientConfig['type'][] = ['linear', 'radial', 'conic'];
    const randomType = types[Math.floor(Math.random() * types.length)];
    
    const colors = [
      '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
      '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43',
      '#8395a7', '#6c5ce7', '#fd79a8', '#fdcb6e', '#6c5ce7'
    ];
    
    const numStops = 2 + Math.floor(Math.random() * 3);
    const colorStops: ColorStop[] = [];
    
    for (let i = 0; i < numStops; i++) {
      const randomColor = colors[Math.floor(Math.random() * colors.length)];
      const position = (i / (numStops - 1)) * 100;
      
      colorStops.push({
        id: (i + 1).toString(),
        color: randomColor,
        position: position,
        opacity: 0.8 + Math.random() * 0.2
      });
    }

    const newGradient: GradientConfig = {
      type: randomType,
      angle: Math.floor(Math.random() * 360),
      shape: Math.random() > 0.5 ? 'circle' : 'ellipse',
      position: {
        x: 30 + Math.random() * 40,
        y: 30 + Math.random() * 40
      },
      colorStops,
      repeat: Math.random() > 0.8
    };

    setGradient(newGradient);
    setSelectedStop(colorStops[0]?.id || '');
  }, []);

  // Copy functions
  const copyCSS = useCallback(() => {
    let cssCode = '';
    
    switch (exportFormat) {
      case 'css':
        cssCode = `.gradient {
  background: ${currentGradientCSS};
}`;
        break;
      case 'scss':
        cssCode = `$gradient: ${currentGradientCSS};

.gradient {
  background: $gradient;
}`;
        break;
      case 'tailwind':
        // Simplified Tailwind approximation
        cssCode = `<!-- Add to tailwind.config.js -->
{
  backgroundImage: {
    'custom-gradient': '${currentGradientCSS}'
  }
}

<!-- Usage -->
<div class="bg-custom-gradient">
  Content here
</div>`;
        break;
    }
    
    navigator.clipboard.writeText(cssCode);
  }, [currentGradientCSS, exportFormat]);

  const downloadGradient = useCallback(() => {
    // Create a canvas to generate gradient image
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 600;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return;

    // Create gradient
    let grad;
    if (gradient.type === 'linear') {
      const radians = (gradient.angle - 90) * (Math.PI / 180);
      const x1 = canvas.width / 2 + Math.cos(radians) * canvas.width / 2;
      const y1 = canvas.height / 2 + Math.sin(radians) * canvas.height / 2;
      const x2 = canvas.width / 2 - Math.cos(radians) * canvas.width / 2;
      const y2 = canvas.height / 2 - Math.sin(radians) * canvas.height / 2;
      grad = ctx.createLinearGradient(x1, y1, x2, y2);
    } else {
      const centerX = (gradient.position.x / 100) * canvas.width;
      const centerY = (gradient.position.y / 100) * canvas.height;
      const radius = Math.max(canvas.width, canvas.height) / 2;
      grad = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
    }

    // Add color stops
    const sortedStops = [...gradient.colorStops].sort((a, b) => a.position - b.position);
    sortedStops.forEach(stop => {
      const alpha = Math.round(stop.opacity * 255).toString(16).padStart(2, '0');
      const color = `${stop.color}${alpha}`;
      grad.addColorStop(stop.position / 100, color);
    });

    ctx.fillStyle = grad;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Download
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'gradient.png';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    });
  }, [gradient]);

  const selectedStopData = gradient.colorStops.find(stop => stop.id === selectedStop);

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 to-orange-100 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-rose-500 to-orange-600 rounded-xl shadow-lg">
              <Palette className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-rose-600 to-orange-600 bg-clip-text text-transparent">
              CSS Gradient Generator
            </h1>
          </div>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Create stunning CSS gradients with our visual editor. Support for linear, radial, and conic gradients
            with real-time preview and professional export options.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Controls Panel */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Gradient Controls
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Gradient Type */}
              <div className="space-y-2">
                <Label>Gradient Type</Label>
                <Select 
                  value={gradient.type} 
                  onValueChange={(value: GradientConfig['type']) => 
                    setGradient(prev => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="linear">Linear</SelectItem>
                    <SelectItem value="radial">Radial</SelectItem>
                    <SelectItem value="conic">Conic</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Angle for Linear and Conic */}
              {(gradient.type === 'linear' || gradient.type === 'conic') && (
                <div className="space-y-2">
                  <Label>Angle: {gradient.angle}°</Label>
                  <Slider
                    value={[gradient.angle]}
                    onValueChange={([value]) => 
                      setGradient(prev => ({ ...prev, angle: value }))
                    }
                    min={0}
                    max={360}
                    step={1}
                    className="w-full"
                  />
                </div>
              )}

              {/* Shape for Radial */}
              {gradient.type === 'radial' && (
                <div className="space-y-2">
                  <Label>Shape</Label>
                  <Select 
                    value={gradient.shape} 
                    onValueChange={(value: 'circle' | 'ellipse') => 
                      setGradient(prev => ({ ...prev, shape: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="circle">Circle</SelectItem>
                      <SelectItem value="ellipse">Ellipse</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Position for Radial and Conic */}
              {(gradient.type === 'radial' || gradient.type === 'conic') && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Position X: {gradient.position.x}%</Label>
                    <Slider
                      value={[gradient.position.x]}
                      onValueChange={([value]) => 
                        setGradient(prev => ({ 
                          ...prev, 
                          position: { ...prev.position, x: value }
                        }))
                      }
                      min={0}
                      max={100}
                      step={1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Position Y: {gradient.position.y}%</Label>
                    <Slider
                      value={[gradient.position.y]}
                      onValueChange={([value]) => 
                        setGradient(prev => ({ 
                          ...prev, 
                          position: { ...prev.position, y: value }
                        }))
                      }
                      min={0}
                      max={100}
                      step={1}
                    />
                  </div>
                </div>
              )}

              {/* Repeat */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="repeat"
                  checked={gradient.repeat}
                  onCheckedChange={(checked) => 
                    setGradient(prev => ({ ...prev, repeat: checked }))
                  }
                />
                <Label htmlFor="repeat">Repeating Gradient</Label>
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                <Button onClick={generateRandomGradient} className="w-full" variant="outline">
                  <RotateCw className="w-4 h-4 mr-2" />
                  Random Gradient
                </Button>
                <div className="grid grid-cols-2 gap-2">
                  <Button onClick={copyCSS} size="sm">
                    <Copy className="w-4 h-4 mr-2" />
                    Copy CSS
                  </Button>
                  <Button onClick={downloadGradient} size="sm" variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Preview and Color Stops */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  Preview
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Label htmlFor="preview-mode" className="text-sm">Mode:</Label>
                  <Select value={previewMode} onValueChange={(value: 'full' | 'element') => setPreviewMode(value)}>
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="element">Element</SelectItem>
                      <SelectItem value="full">Full</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Preview */}
              <div className="relative">
                <div 
                  ref={gradientRef}
                  className={`border-2 border-gray-200 rounded-lg ${showGrid ? 'bg-checkerboard' : ''}`}
                  style={{
                    background: currentGradientCSS,
                    height: previewMode === 'full' ? '400px' : `${customSize.height}px`,
                    width: previewMode === 'full' ? '100%' : `${customSize.width}px`,
                    margin: previewMode === 'element' ? '0 auto' : '0',
                    backgroundSize: showGrid ? '20px 20px, 20px 20px, 10px 10px, 10px 10px' : undefined,
                    backgroundPosition: showGrid ? '0 0, 10px 10px, 0 0, 10px 10px' : undefined,
                    backgroundImage: showGrid 
                      ? `${currentGradientCSS}, linear-gradient(45deg, #eee 25%, transparent 25%), linear-gradient(-45deg, #eee 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #eee 75%), linear-gradient(-45deg, transparent 75%, #eee 75%)`
                      : currentGradientCSS
                  }}
                >
                  {previewMode === 'element' && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="bg-white/80 backdrop-blur-sm px-4 py-2 rounded-lg shadow-lg">
                        <p className="text-sm font-medium text-gray-800">Sample Content</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Size Controls for Element Mode */}
                {previewMode === 'element' && (
                  <div className="mt-4 grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm">Width: {customSize.width}px</Label>
                      <Slider
                        value={[customSize.width]}
                        onValueChange={([value]) => setCustomSize(prev => ({ ...prev, width: value }))}
                        min={200}
                        max={600}
                        step={10}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm">Height: {customSize.height}px</Label>
                      <Slider
                        value={[customSize.height]}
                        onValueChange={([value]) => setCustomSize(prev => ({ ...prev, height: value }))}
                        min={100}
                        max={400}
                        step={10}
                      />
                    </div>
                  </div>
                )}

                <div className="mt-4 flex items-center space-x-2">
                  <Switch
                    id="show-grid"
                    checked={showGrid}
                    onCheckedChange={setShowGrid}
                  />
                  <Label htmlFor="show-grid" className="text-sm">Show transparency grid</Label>
                </div>
              </div>

              {/* Color Stops */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-semibold">Color Stops</Label>
                  <Button onClick={addColorStop} size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Stop
                  </Button>
                </div>

                <div className="space-y-2">
                  {gradient.colorStops
                    .sort((a, b) => a.position - b.position)
                    .map((stop) => (
                    <div 
                      key={stop.id} 
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedStop === stop.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedStop(stop.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div 
                            className="w-8 h-8 rounded border-2 border-white shadow-sm"
                            style={{ backgroundColor: stop.color, opacity: stop.opacity }}
                          />
                          <div>
                            <div className="text-sm font-medium">{stop.color}</div>
                            <div className="text-xs text-gray-500">{stop.position}% • {Math.round(stop.opacity * 100)}%</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {selectedStop === stop.id && (
                            <Badge variant="outline" className="text-xs">Selected</Badge>
                          )}
                          {gradient.colorStops.length > 2 && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                removeColorStop(stop.id);
                              }}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Selected Stop Controls */}
                {selectedStopData && (
                  <div className="p-4 bg-gray-50 rounded-lg space-y-4">
                    <Label className="text-sm font-medium text-gray-600">Edit Selected Stop</Label>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm">Color</Label>
                        <div className="flex gap-2">
                          <Input
                            type="color"
                            value={selectedStopData.color}
                            onChange={(e) => updateColorStop(selectedStop, { color: e.target.value })}
                            className="w-12 h-8 p-1 border-2"
                          />
                          <Input
                            value={selectedStopData.color}
                            onChange={(e) => updateColorStop(selectedStop, { color: e.target.value })}
                            className="flex-1 text-xs"
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <Label className="text-sm">Position: {selectedStopData.position}%</Label>
                        <Slider
                          value={[selectedStopData.position]}
                          onValueChange={([value]) => updateColorStop(selectedStop, { position: value })}
                          min={0}
                          max={100}
                          step={0.1}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm">Opacity: {Math.round(selectedStopData.opacity * 100)}%</Label>
                      <Slider
                        value={[selectedStopData.opacity]}
                        onValueChange={([value]) => updateColorStop(selectedStop, { opacity: value })}
                        min={0}
                        max={1}
                        step={0.01}
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs for Additional Features */}
        <Tabs defaultValue="presets" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="presets" className="flex items-center gap-2">
              <Layers className="w-4 h-4" />
              Presets
            </TabsTrigger>
            <TabsTrigger value="code" className="flex items-center gap-2">
              <Code className="w-4 h-4" />
              Export
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              Advanced
            </TabsTrigger>
          </TabsList>

          <TabsContent value="presets" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Gradient Presets</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {presets.map((preset, index) => (
                    <div
                      key={index}
                      className="relative cursor-pointer group border-2 border-gray-200 rounded-lg overflow-hidden hover:border-gray-400 transition-colors"
                      onClick={() => applyPreset(preset)}
                    >
                      <div 
                        className="h-24 w-full"
                        style={{ background: generateGradientCSS(preset.config) }}
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity text-white text-center">
                          <div className="font-semibold">{preset.name}</div>
                          <div className="text-sm opacity-80">{preset.description}</div>
                        </div>
                      </div>
                      <div className="p-2 bg-white">
                        <div className="font-medium text-sm">{preset.name}</div>
                        <div className="text-xs text-gray-500">{preset.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="code" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Export Code</CardTitle>
                  <Select value={exportFormat} onValueChange={(value: 'css' | 'scss' | 'tailwind') => setExportFormat(value)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="css">CSS</SelectItem>
                      <SelectItem value="scss">SCSS</SelectItem>
                      <SelectItem value="tailwind">Tailwind</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Generated CSS:</Label>
                  <pre className="mt-2 p-4 bg-gray-900 text-gray-100 rounded-lg overflow-x-auto text-sm">
                    {exportFormat === 'css' && `.gradient {
  background: ${currentGradientCSS};
}`}
                    {exportFormat === 'scss' && `$gradient: ${currentGradientCSS};

.gradient {
  background: $gradient;
}`}
                    {exportFormat === 'tailwind' && `/* Add to tailwind.config.js */
{
  backgroundImage: {
    'custom-gradient': '${currentGradientCSS}'
  }
}

/* Usage */
<div class="bg-custom-gradient">
  Content here
</div>`}
                  </pre>
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={copyCSS} className="flex-1">
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Code
                  </Button>
                  <Button onClick={downloadGradient} variant="outline" className="flex-1">
                    <Download className="w-4 h-4 mr-2" />
                    Download PNG
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <Label className="text-base font-semibold">Gradient Information</Label>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Type:</span>
                        <span className="font-medium capitalize">{gradient.type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Color Stops:</span>
                        <span className="font-medium">{gradient.colorStops.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Repeating:</span>
                        <span className="font-medium">{gradient.repeat ? 'Yes' : 'No'}</span>
                      </div>
                      {gradient.type !== 'linear' && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Position:</span>
                          <span className="font-medium">{gradient.position.x}%, {gradient.position.y}%</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <Label className="text-base font-semibold">Browser Support</Label>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Chrome/Edge:</span>
                        <Badge className="bg-green-100 text-green-800 text-xs">Full</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Firefox:</span>
                        <Badge className="bg-green-100 text-green-800 text-xs">Full</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Safari:</span>
                        <Badge className="bg-green-100 text-green-800 text-xs">Full</Badge>
                      </div>
                      {gradient.type === 'conic' && (
                        <div className="text-xs text-orange-600 mt-2">
                          * Conic gradients require modern browsers
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <Label className="text-base font-semibold">Performance Tips</Label>
                  <ul className="mt-2 space-y-1 text-sm text-gray-600">
                    <li>• Use fewer color stops for better performance</li>
                    <li>• Avoid too many repeating gradients on the same page</li>
                    <li>• Consider using CSS custom properties for dynamic gradients</li>
                    <li>• Test gradient complexity on mobile devices</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CssGradientGeneratorTool;