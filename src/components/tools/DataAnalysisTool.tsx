'use client'

import { useState, useEffect, useCallback } from 'react'
import { BarChart3, TrendingUp, Calculator, FileSpreadsheet, Info, Download } from 'lucide-react'

interface DataStats {
  count: number
  sum: number
  mean: number
  median: number
  mode: number[]
  min: number
  max: number
  range: number
  variance: number
  stdDev: number
  q1: number
  q3: number
  iqr: number
}

interface FrequencyItem {
  value: number
  frequency: number
  percentage: number
}

export default function DataAnalysisTool() {
  const [input, setInput] = useState('')
  const [data, setData] = useState<number[]>([])
  const [stats, setStats] = useState<DataStats | null>(null)
  const [frequency, setFrequency] = useState<FrequencyItem[]>([])
  const [error, setError] = useState('')
  const [delimiter, setDelimiter] = useState<'comma' | 'space' | 'newline'>('comma')
  const [precision, setPrecision] = useState(2)

  // 解析输入数据
  const parseData = useCallback((text: string): number[] => {
    if (!text.trim()) return []
    
    let separator: RegExp
    switch (delimiter) {
      case 'comma':
        separator = /[,，]/
        break
      case 'space':
        separator = /\s+/
        break
      case 'newline':
        separator = /[\n\r]+/
        break
    }
    
    const values = text
      .split(separator)
      .map(s => s.trim())
      .filter(s => s !== '')
      .map(s => parseFloat(s))
      .filter(n => !isNaN(n))
    
    return values
  }, [delimiter])

  // 计算统计数据
  const calculateStats = (numbers: number[]): DataStats | null => {
    if (numbers.length === 0) return null
    
    const sorted = [...numbers].sort((a, b) => a - b)
    const n = numbers.length
    
    // 基本统计
    const sum = numbers.reduce((a, b) => a + b, 0)
    const mean = sum / n
    const min = sorted[0]
    const max = sorted[n - 1]
    const range = max - min
    
    // 中位数
    const median = n % 2 === 0
      ? (sorted[n / 2 - 1] + sorted[n / 2]) / 2
      : sorted[Math.floor(n / 2)]
    
    // 众数
    const frequencyMap = new Map<number, number>()
    numbers.forEach(num => {
      frequencyMap.set(num, (frequencyMap.get(num) || 0) + 1)
    })
    const frequencies = Array.from(frequencyMap.values())
    const maxFreq = Math.max(...frequencies)
    const mode = Array.from(frequencyMap.entries())
      .filter(([_, freq]) => freq === maxFreq)
      .map(([value]) => value)
    
    // 方差和标准差
    const variance = numbers.reduce((acc, num) => acc + Math.pow(num - mean, 2), 0) / n
    const stdDev = Math.sqrt(variance)
    
    // 四分位数
    const q1Index = Math.floor(n * 0.25)
    const q3Index = Math.floor(n * 0.75)
    const q1 = sorted[q1Index]
    const q3 = sorted[q3Index]
    const iqr = q3 - q1
    
    return {
      count: n,
      sum,
      mean,
      median,
      mode,
      min,
      max,
      range,
      variance,
      stdDev,
      q1,
      q3,
      iqr,
    }
  }

  // 计算频率分布
  const calculateFrequency = (numbers: number[]): FrequencyItem[] => {
    if (numbers.length === 0) return []
    
    const frequencyMap = new Map<number, number>()
    numbers.forEach(num => {
      frequencyMap.set(num, (frequencyMap.get(num) || 0) + 1)
    })
    
    const total = numbers.length
    const items: FrequencyItem[] = Array.from(frequencyMap.entries())
      .map(([value, frequency]) => ({
        value,
        frequency,
        percentage: (frequency / total) * 100,
      }))
      .sort((a, b) => b.frequency - a.frequency)
    
    return items
  }

  // 处理输入变化
  useEffect(() => {
    try {
      setError('')
      const numbers = parseData(input)
      setData(numbers)
      
      if (numbers.length > 0) {
        const calculatedStats = calculateStats(numbers)
        setStats(calculatedStats)
        setFrequency(calculateFrequency(numbers))
      } else {
        setStats(null)
        setFrequency([])
      }
    } catch (err) {
      setError('数据解析错误，请检查输入格式')
      setData([])
      setStats(null)
      setFrequency([])
    }
  }, [input, parseData])

  // 格式化数字
  const formatNumber = (num: number): string => {
    return num.toFixed(precision)
  }

  // 导出结果
  const exportResults = () => {
    if (!stats) return
    
    const report = `数据统计分析报告
生成时间：${new Date().toLocaleString('zh-CN')}

基本统计信息：
- 数据个数：${stats.count}
- 总和：${formatNumber(stats.sum)}
- 平均值：${formatNumber(stats.mean)}
- 中位数：${formatNumber(stats.median)}
- 众数：${stats.mode.map(formatNumber).join(', ')}
- 最小值：${formatNumber(stats.min)}
- 最大值：${formatNumber(stats.max)}
- 极差：${formatNumber(stats.range)}
- 方差：${formatNumber(stats.variance)}
- 标准差：${formatNumber(stats.stdDev)}
- 第一四分位数：${formatNumber(stats.q1)}
- 第三四分位数：${formatNumber(stats.q3)}
- 四分位距：${formatNumber(stats.iqr)}

频率分布：
${frequency.map(item => 
  `值：${formatNumber(item.value)} | 频数：${item.frequency} | 百分比：${item.percentage.toFixed(1)}%`
).join('\n')}

原始数据：
${data.join(', ')}
`
    
    const blob = new Blob([report], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `数据分析报告_${new Date().getTime()}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }

  // 生成示例数据
  const generateSampleData = () => {
    const samples = []
    for (let i = 0; i < 30; i++) {
      samples.push(Math.floor(Math.random() * 100) + 1)
    }
    setInput(samples.join(', '))
  }

  // 清空数据
  const clearAll = () => {
    setInput('')
    setData([])
    setStats(null)
    setFrequency([])
    setError('')
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">数据统计分析器</h2>
        <p className="text-gray-600">
          输入数值数据，自动计算各种统计指标和频率分布
        </p>
      </div>

      {/* 工具栏 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">分隔符：</span>
              <select
                value={delimiter}
                onChange={(e) => setDelimiter(e.target.value as any)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="comma">逗号</option>
                <option value="space">空格</option>
                <option value="newline">换行</option>
              </select>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">小数位数：</span>
              <input
                type="number"
                min="0"
                max="10"
                value={precision}
                onChange={(e) => setPrecision(parseInt(e.target.value) || 2)}
                className="w-16 px-2 py-1 border border-gray-300 rounded-md text-sm"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={generateSampleData}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              生成示例
            </button>
            <button
              onClick={clearAll}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
            >
              清空
            </button>
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* 输入区域 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <div className="flex items-center mb-3">
          <FileSpreadsheet className="w-5 h-5 mr-2" />
          <h3 className="font-medium">数据输入</h3>
          {data.length > 0 && (
            <span className="ml-auto text-sm text-gray-500">
              已输入 {data.length} 个数据
            </span>
          )}
        </div>
        <textarea
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder={
            delimiter === 'comma' 
              ? '输入数值数据，用逗号分隔，例如：1, 2, 3, 4, 5'
              : delimiter === 'space'
              ? '输入数值数据，用空格分隔，例如：1 2 3 4 5'
              : '输入数值数据，每行一个数值'
          }
          className="w-full h-32 p-3 font-mono text-sm border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* 统计结果 */}
      {stats && (
        <>
          {/* 基本统计 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="flex items-center mb-4">
              <Calculator className="w-5 h-5 mr-2" />
              <h3 className="font-medium">统计结果</h3>
              <button
                onClick={exportResults}
                className="ml-auto p-2 hover:bg-gray-100 rounded"
                title="导出报告"
              >
                <Download className="w-4 h-4" />
              </button>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">数据个数</div>
                <div className="text-xl font-semibold">{stats.count}</div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">总和</div>
                <div className="text-xl font-semibold">{formatNumber(stats.sum)}</div>
              </div>
              
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">平均值</div>
                <div className="text-xl font-semibold text-blue-600">
                  {formatNumber(stats.mean)}
                </div>
              </div>
              
              <div className="bg-green-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">中位数</div>
                <div className="text-xl font-semibold text-green-600">
                  {formatNumber(stats.median)}
                </div>
              </div>
              
              <div className="bg-purple-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">众数</div>
                <div className="text-xl font-semibold text-purple-600">
                  {stats.mode.length > 3 
                    ? `${stats.mode.slice(0, 3).map(formatNumber).join(', ')}...`
                    : stats.mode.map(formatNumber).join(', ')
                  }
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">最小值</div>
                <div className="text-xl font-semibold">{formatNumber(stats.min)}</div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">最大值</div>
                <div className="text-xl font-semibold">{formatNumber(stats.max)}</div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">极差</div>
                <div className="text-xl font-semibold">{formatNumber(stats.range)}</div>
              </div>
              
              <div className="bg-orange-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">标准差</div>
                <div className="text-xl font-semibold text-orange-600">
                  {formatNumber(stats.stdDev)}
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">方差</div>
                <div className="text-xl font-semibold">{formatNumber(stats.variance)}</div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">Q1 (25%)</div>
                <div className="text-xl font-semibold">{formatNumber(stats.q1)}</div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm text-gray-600">Q3 (75%)</div>
                <div className="text-xl font-semibold">{formatNumber(stats.q3)}</div>
              </div>
            </div>
          </div>

          {/* 频率分布 */}
          {frequency.length > 0 && frequency.length <= 20 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
              <div className="flex items-center mb-4">
                <BarChart3 className="w-5 h-5 mr-2" />
                <h3 className="font-medium">频率分布</h3>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">数值</th>
                      <th className="text-left py-2">频数</th>
                      <th className="text-left py-2">百分比</th>
                      <th className="text-left py-2">分布图</th>
                    </tr>
                  </thead>
                  <tbody>
                    {frequency.slice(0, 10).map((item, index) => (
                      <tr key={index} className="border-b">
                        <td className="py-2">{formatNumber(item.value)}</td>
                        <td className="py-2">{item.frequency}</td>
                        <td className="py-2">{item.percentage.toFixed(1)}%</td>
                        <td className="py-2">
                          <div className="w-full bg-gray-200 rounded-full h-4">
                            <div
                              className="bg-blue-600 h-4 rounded-full"
                              style={{ width: `${item.percentage}%` }}
                            />
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {frequency.length > 10 && (
                  <p className="text-sm text-gray-500 mt-2">
                    仅显示前10个最频繁的值
                  </p>
                )}
              </div>
            </div>
          )}
        </>
      )}

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持多种数据输入格式：逗号、空格或换行分隔</li>
              <li>自动过滤非数值数据，确保计算准确性</li>
              <li>提供完整的描述性统计指标</li>
              <li>可导出详细的分析报告</li>
              <li>适用于数据分析、统计学习等场景</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
