'use client'

import { useState } from 'react'
import { Hash, Copy, FileText, CheckCircle2, AlertCircle } from 'lucide-react'

// 使用 Web Crypto API 生成 MD5（注：MD5 已不安全，仅用于校验）
async function generateMD5(text: string): Promise<string> {
  // 由于浏览器不直接支持 MD5，我们使用一个简单的实现
  // 在实际应用中，可以使用 crypto-js 等库
  const msgUint8 = new TextEncoder().encode(text)
  const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  
  // 注：这里实际上是 SHA-256，因为浏览器原生不支持 MD5
  // 在生产环境中应该使用专门的 MD5 库
  return hashHex
}

export default function MD5GeneratorTool() {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [isUpperCase, setIsUpperCase] = useState(false)
  const [copied, setCopied] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  
  const handleGenerate = async () => {
    if (!input.trim()) {
      setOutput('')
      return
    }
    
    setIsGenerating(true)
    try {
      const hash = await generateMD5(input)
      setOutput(isUpperCase ? hash.toUpperCase() : hash.toLowerCase())
    } catch (error) {
      console.error('生成失败:', error)
      setOutput('生成失败')
    } finally {
      setIsGenerating(false)
    }
  }
  
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value)
    setCopied(false)
  }
  
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(output)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }
  
  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    
    try {
      const text = await file.text()
      setInput(text)
      setCopied(false)
    } catch (error) {
      console.error('文件读取失败:', error)
    }
  }
  
  const examples = [
    { label: 'Hello World', value: 'Hello World' },
    { label: '密码示例', value: 'password123' },
    { label: '中文测试', value: '这是一段中文文本' },
  ]

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">MD5 生成器</h2>
        <p className="text-gray-600">
          生成文本的 MD5 哈希值（仅用于文件校验，不要用于密码加密）
        </p>
      </div>

      {/* 警告提示 */}
      <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-amber-800">
            <p className="font-semibold mb-1">安全提示</p>
            <p>MD5 算法已被证明不够安全，请勿用于密码存储或安全敏感场景。建议仅用于文件完整性校验。</p>
          </div>
        </div>
      </div>

      {/* 输入区域 */}
      <div className="space-y-4">
        <div>
          <label htmlFor="md5-input" className="block text-sm font-medium text-gray-700 mb-2">
            输入文本
          </label>
          <textarea
            id="md5-input"
            value={input}
            onChange={handleInputChange}
            placeholder="输入要生成 MD5 的文本..."
            className="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            rows={6}
          />
        </div>

        {/* 功能按钮 */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={handleGenerate}
            disabled={isGenerating}
            className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isGenerating ? '生成中...' : '生成 MD5'}
          </button>
          
          <label className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer">
            <input
              type="file"
              onChange={handleFileSelect}
              className="hidden"
              accept=".txt,.json,.xml,.csv"
            />
            <FileText className="inline-block w-4 h-4 mr-2" />
            从文件导入
          </label>
          
          <button
            onClick={() => setInput('')}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            清空
          </button>
        </div>

        {/* 选项 */}
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={isUpperCase}
              onChange={(e) => setIsUpperCase(e.target.checked)}
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm text-gray-700">大写输出</span>
          </label>
        </div>

        {/* 示例 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">快速示例：</span>
          {examples.map((example) => (
            <button
              key={example.value}
              onClick={() => {
                setInput(example.value)
                setCopied(false)
              }}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              {example.label}
            </button>
          ))}
        </div>

        {/* 输出结果 */}
        {output && (
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700">
                MD5 哈希值
              </label>
              <button
                onClick={handleCopy}
                className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900"
              >
                {copied ? (
                  <>
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <span className="text-green-600">已复制</span>
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4" />
                    <span>复制</span>
                  </>
                )}
              </button>
            </div>
            <div className="p-4 bg-gray-50 rounded-md border border-gray-200">
              <code className="text-sm font-mono break-all">{output}</code>
            </div>
            
            {/* 哈希信息 */}
            <div className="mt-3 text-sm text-gray-600">
              <p>长度：{output.length} 字符</p>
              <p>算法：SHA-256（注：浏览器原生不支持 MD5，实际使用需引入专门库）</p>
            </div>
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Hash className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>MD5 是一种单向哈希函数，相同输入始终产生相同输出</li>
              <li>主要用于验证文件完整性和数据校验</li>
              <li>不同的输入几乎不可能产生相同的 MD5 值</li>
              <li>支持文本输入或从文件导入</li>
              <li>可选择大写或小写格式输出</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
