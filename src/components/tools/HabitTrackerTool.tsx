'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Calendar, CheckCircle2, Circle, Plus, Trash2, TrendingUp, Target, Award, BarChart3 } from 'lucide-react';
import { useTranslations } from '@/hooks/useTranslations';

interface Habit {
  id: string;
  name: string;
  description: string;
  frequency: 'daily' | 'weekly' | 'monthly';
  target: number;
  color: string;
  createdAt: string;
  completions: Record<string, boolean>;
}

interface HabitStats {
  currentStreak: number;
  longestStreak: number;
  completionRate: number;
  totalCompletions: number;
}

export default function HabitTrackerTool() {
  const t = useTranslations();
  const [habits, setHabits] = useState<Habit[]>([]);
  const [newHabit, setNewHabit] = useState({
    name: '',
    description: '',
    frequency: 'daily' as const,
    target: 1,
    color: '#3B82F6'
  });
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [view, setView] = useState<'today' | 'week' | 'month'>('today');

  // 从 localStorage 加载习惯数据
  useEffect(() => {
    const savedHabits = localStorage.getItem('habitTracker');
    if (savedHabits) {
      setHabits(JSON.parse(savedHabits));
    }
  }, []);

  // 保存习惯数据到 localStorage
  useEffect(() => {
    if (habits.length > 0) {
      localStorage.setItem('habitTracker', JSON.stringify(habits));
    }
  }, [habits]);

  // 添加新习惯
  const addHabit = () => {
    if (!newHabit.name.trim()) return;

    const habit: Habit = {
      id: Date.now().toString(),
      name: newHabit.name,
      description: newHabit.description,
      frequency: newHabit.frequency,
      target: newHabit.target,
      color: newHabit.color,
      createdAt: new Date().toISOString(),
      completions: {}
    };

    setHabits([...habits, habit]);
    setNewHabit({
      name: '',
      description: '',
      frequency: 'daily',
      target: 1,
      color: '#3B82F6'
    });
  };

  // 删除习惯
  const deleteHabit = (id: string) => {
    setHabits(habits.filter(h => h.id !== id));
  };

  // 切换习惯完成状态
  const toggleHabitCompletion = (habitId: string, date: string) => {
    setHabits(habits.map(habit => {
      if (habit.id === habitId) {
        const completions = { ...habit.completions };
        if (completions[date]) {
          delete completions[date];
        } else {
          completions[date] = true;
        }
        return { ...habit, completions };
      }
      return habit;
    }));
  };

  // 计算习惯统计数据
  const calculateHabitStats = (habit: Habit): HabitStats => {
    const dates = Object.keys(habit.completions).sort();
    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;
    let lastDate: Date | null = null;

    // 计算连续天数
    dates.forEach(dateStr => {
      const date = new Date(dateStr);
      if (lastDate) {
        const dayDiff = (date.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24);
        if (dayDiff === 1) {
          tempStreak++;
        } else {
          longestStreak = Math.max(longestStreak, tempStreak);
          tempStreak = 1;
        }
      } else {
        tempStreak = 1;
      }
      lastDate = date;
    });

    longestStreak = Math.max(longestStreak, tempStreak);

    // 计算当前连续天数
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    let checkDate = new Date(today);
    currentStreak = 0;

    while (true) {
      const dateStr = checkDate.toISOString().split('T')[0];
      if (habit.completions[dateStr]) {
        currentStreak++;
        checkDate.setDate(checkDate.getDate() - 1);
      } else {
        break;
      }
    }

    // 计算完成率
    const startDate = new Date(habit.createdAt);
    const daysSinceCreation = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    const completionRate = (dates.length / daysSinceCreation) * 100;

    return {
      currentStreak,
      longestStreak,
      completionRate: Math.min(100, completionRate),
      totalCompletions: dates.length
    };
  };

  // 获取日期范围内的日期列表
  const getDateRange = () => {
    const dates: string[] = [];
    const today = new Date();
    
    if (view === 'today') {
      dates.push(today.toISOString().split('T')[0]);
    } else if (view === 'week') {
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        dates.push(date.toISOString().split('T')[0]);
      }
    } else if (view === 'month') {
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        dates.push(date.toISOString().split('T')[0]);
      }
    }
    
    return dates;
  };

  // 格式化日期显示
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    };
    return date.toLocaleDateString(undefined, options);
  };

  // 获取习惯颜色的预设选项
  const colorOptions = [
    { value: '#3B82F6', label: '蓝色' },
    { value: '#10B981', label: '绿色' },
    { value: '#F59E0B', label: '黄色' },
    { value: '#EF4444', label: '红色' },
    { value: '#8B5CF6', label: '紫色' },
    { value: '#EC4899', label: '粉色' },
    { value: '#6B7280', label: '灰色' }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{t('tools.habitTracker.title')}</CardTitle>
          <CardDescription>{t('tools.habitTracker.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tracker" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="tracker">习惯追踪</TabsTrigger>
              <TabsTrigger value="statistics">统计分析</TabsTrigger>
              <TabsTrigger value="manage">管理习惯</TabsTrigger>
            </TabsList>

            <TabsContent value="tracker" className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <Select value={view} onValueChange={(v: any) => setView(v)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">今天</SelectItem>
                      <SelectItem value="week">本周</SelectItem>
                      <SelectItem value="month">本月</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {habits.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>还没有添加任何习惯</p>
                  <p className="text-sm mt-2">点击&ldquo;管理习惯&rdquo;标签页添加你的第一个习惯</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {view === 'today' ? (
                    // 今日视图
                    <div className="space-y-3">
                      {habits.map(habit => {
                        const stats = calculateHabitStats(habit);
                        const today = new Date().toISOString().split('T')[0];
                        const isCompleted = habit.completions[today];

                        return (
                          <Card key={habit.id} className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <button
                                  onClick={() => toggleHabitCompletion(habit.id, today)}
                                  className="transition-all"
                                >
                                  {isCompleted ? (
                                    <CheckCircle2 
                                      className="h-8 w-8" 
                                      style={{ color: habit.color }}
                                    />
                                  ) : (
                                    <Circle 
                                      className="h-8 w-8 text-muted-foreground hover:opacity-70" 
                                    />
                                  )}
                                </button>
                                <div>
                                  <h3 className="font-medium">{habit.name}</h3>
                                  {habit.description && (
                                    <p className="text-sm text-muted-foreground">{habit.description}</p>
                                  )}
                                  <div className="flex items-center gap-4 mt-1">
                                    <Badge variant="secondary" className="text-xs">
                                      🔥 {stats.currentStreak} 天连续
                                    </Badge>
                                    <span className="text-xs text-muted-foreground">
                                      完成率: {stats.completionRate.toFixed(0)}%
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </Card>
                        );
                      })}
                    </div>
                  ) : (
                    // 周/月视图
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr>
                            <th className="text-left p-2">习惯</th>
                            {getDateRange().map(date => (
                              <th key={date} className="text-center p-1 text-xs">
                                <div>{formatDate(date).split(' ')[0]}</div>
                                <div>{formatDate(date).split(' ')[2]}</div>
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {habits.map(habit => (
                            <tr key={habit.id}>
                              <td className="p-2">
                                <div className="flex items-center gap-2">
                                  <div 
                                    className="w-3 h-3 rounded-full" 
                                    style={{ backgroundColor: habit.color }}
                                  />
                                  <span className="font-medium">{habit.name}</span>
                                </div>
                              </td>
                              {getDateRange().map(date => (
                                <td key={date} className="text-center p-1">
                                  <button
                                    onClick={() => toggleHabitCompletion(habit.id, date)}
                                    className="w-8 h-8 rounded-full transition-all hover:opacity-70"
                                    style={{
                                      backgroundColor: habit.completions[date] 
                                        ? habit.color 
                                        : 'transparent',
                                      border: `2px solid ${habit.completions[date] 
                                        ? habit.color 
                                        : '#e5e7eb'}`
                                    }}
                                  />
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="statistics" className="space-y-4">
              {habits.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>还没有统计数据</p>
                  <p className="text-sm mt-2">添加习惯并开始追踪后查看统计</p>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2">
                  {habits.map(habit => {
                    const stats = calculateHabitStats(habit);
                    return (
                      <Card key={habit.id}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <div 
                                className="w-4 h-4 rounded-full" 
                                style={{ backgroundColor: habit.color }}
                              />
                              {habit.name}
                            </CardTitle>
                            <Award className="h-5 w-5 text-muted-foreground" />
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-muted-foreground">当前连续</p>
                              <p className="text-2xl font-bold">{stats.currentStreak} 天</p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">最长连续</p>
                              <p className="text-2xl font-bold">{stats.longestStreak} 天</p>
                            </div>
                          </div>
                          
                          <div>
                            <div className="flex justify-between text-sm mb-2">
                              <span>完成率</span>
                              <span>{stats.completionRate.toFixed(0)}%</span>
                            </div>
                            <Progress value={stats.completionRate} className="h-2" />
                          </div>
                          
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">总完成次数</span>
                            <span className="font-medium">{stats.totalCompletions}</span>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </TabsContent>

            <TabsContent value="manage" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">添加新习惯</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="habit-name">习惯名称</Label>
                      <Input
                        id="habit-name"
                        placeholder="例如：每天运动30分钟"
                        value={newHabit.name}
                        onChange={(e) => setNewHabit({ ...newHabit, name: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="habit-frequency">频率</Label>
                      <Select 
                        value={newHabit.frequency} 
                        onValueChange={(v: any) => setNewHabit({ ...newHabit, frequency: v })}
                      >
                        <SelectTrigger id="habit-frequency">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">每天</SelectItem>
                          <SelectItem value="weekly">每周</SelectItem>
                          <SelectItem value="monthly">每月</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="habit-description">描述（可选）</Label>
                    <Input
                      id="habit-description"
                      placeholder="添加一些细节或目标"
                      value={newHabit.description}
                      onChange={(e) => setNewHabit({ ...newHabit, description: e.target.value })}
                    />
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="habit-target">目标次数</Label>
                      <Input
                        id="habit-target"
                        type="number"
                        min="1"
                        value={newHabit.target}
                        onChange={(e) => setNewHabit({ ...newHabit, target: parseInt(e.target.value) || 1 })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="habit-color">颜色</Label>
                      <Select 
                        value={newHabit.color} 
                        onValueChange={(v) => setNewHabit({ ...newHabit, color: v })}
                      >
                        <SelectTrigger id="habit-color">
                          <SelectValue>
                            <div className="flex items-center gap-2">
                              <div 
                                className="w-4 h-4 rounded-full" 
                                style={{ backgroundColor: newHabit.color }}
                              />
                              <span>
                                {colorOptions.find(c => c.value === newHabit.color)?.label}
                              </span>
                            </div>
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {colorOptions.map(color => (
                            <SelectItem key={color.value} value={color.value}>
                              <div className="flex items-center gap-2">
                                <div 
                                  className="w-4 h-4 rounded-full" 
                                  style={{ backgroundColor: color.value }}
                                />
                                <span>{color.label}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <Button onClick={addHabit} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    添加习惯
                  </Button>
                </CardContent>
              </Card>

              {habits.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">管理现有习惯</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {habits.map(habit => (
                        <div 
                          key={habit.id} 
                          className="flex items-center justify-between p-3 rounded-lg border"
                        >
                          <div className="flex items-center gap-3">
                            <div 
                              className="w-4 h-4 rounded-full" 
                              style={{ backgroundColor: habit.color }}
                            />
                            <div>
                              <p className="font-medium">{habit.name}</p>
                              {habit.description && (
                                <p className="text-sm text-muted-foreground">{habit.description}</p>
                              )}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteHabit(habit.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
