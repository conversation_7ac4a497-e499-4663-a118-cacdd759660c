'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Wifi, Shield, Zap, Copy, RotateCcw, AlertTriangle, CheckCircle2, XCircle } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

interface PortScanResult {
  port: number
  status: 'open' | 'closed' | 'filtered' | 'timeout'
  service?: string
  protocol: 'tcp' | 'udp'
  responseTime?: number
}

interface ScanProgress {
  current: number
  total: number
  percentage: number
}

export default function PortScannerTool() {
  const [activeTab, setActiveTab] = useState('single')
  
  // 单端口扫描
  const [singleHost, setSingleHost] = useState('')
  const [singlePort, setSinglePort] = useState('')
  const [singleProtocol, setSingleProtocol] = useState<'tcp' | 'udp'>('tcp')
  const [singleResult, setSingleResult] = useState<PortScanResult | null>(null)
  const [singleScanning, setSingleScanning] = useState(false)
  
  // 多端口扫描
  const [multiHost, setMultiHost] = useState('')
  const [portRange, setPortRange] = useState('')
  const [multiProtocol, setMultiProtocol] = useState<'tcp' | 'udp'>('tcp')
  const [multiResults, setMultiResults] = useState<PortScanResult[]>([])
  const [multiScanning, setMultiScanning] = useState(false)
  const [scanProgress, setScanProgress] = useState<ScanProgress>({ current: 0, total: 0, percentage: 0 })
  
  // 预设扫描
  const [presetHost, setPresetHost] = useState('')
  const [presetType, setPresetType] = useState('common')
  const [presetResults, setPresetResults] = useState<PortScanResult[]>([])
  const [presetScanning, setPresetScanning] = useState(false)

  // 常用端口预设
  const portPresets = {
    common: {
      name: '常用端口',
      ports: [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 1433, 3306, 3389, 5432, 6379, 27017]
    },
    web: {
      name: 'Web服务',
      ports: [80, 443, 8000, 8080, 8443, 8888, 9000, 9090, 3000, 5000]
    },
    database: {
      name: '数据库',
      ports: [1433, 1521, 3306, 5432, 6379, 27017, 5984, 9042, 7000, 7001]
    },
    mail: {
      name: '邮件服务',
      ports: [25, 110, 143, 465, 587, 993, 995]
    },
    system: {
      name: '系统服务',
      ports: [21, 22, 23, 53, 135, 139, 445, 3389, 5900, 5985]
    }
  }

  // 常见服务端口映射
  const serviceMap: { [key: number]: string } = {
    21: 'FTP',
    22: 'SSH',
    23: 'Telnet',
    25: 'SMTP',
    53: 'DNS',
    80: 'HTTP',
    110: 'POP3',
    143: 'IMAP',
    443: 'HTTPS',
    993: 'IMAPS',
    995: 'POP3S',
    1433: 'SQL Server',
    3306: 'MySQL',
    3389: 'RDP',
    5432: 'PostgreSQL',
    6379: 'Redis',
    8080: 'HTTP Proxy',
    27017: 'MongoDB'
  }

  // 模拟端口扫描（实际应用中需要后端支持）
  const simulatePortScan = async (host: string, port: number, protocol: 'tcp' | 'udp'): Promise<PortScanResult> => {
    // 模拟扫描延迟
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500))
    
    // 验证主机格式
    if (!isValidHost(host)) {
      throw new Error('无效的主机地址')
    }
    
    // 模拟扫描结果（实际应用中会进行真实的网络连接测试）
    const statuses: ('open' | 'closed' | 'filtered' | 'timeout')[] = ['open', 'closed', 'filtered']
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
    
    // 常用端口更可能开放
    const isCommonPort = Object.keys(serviceMap).includes(port.toString())
    const status = isCommonPort && Math.random() > 0.3 ? 'open' : randomStatus
    
    return {
      port,
      status,
      service: status === 'open' ? serviceMap[port] : undefined,
      protocol,
      responseTime: Math.random() * 100 + 10
    }
  }

  // 验证主机地址
  const isValidHost = (host: string): boolean => {
    // IP地址正则
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    // 域名正则
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/
    
    return ipRegex.test(host) || domainRegex.test(host)
  }

  // 验证端口号
  const isValidPort = (port: number): boolean => {
    return port >= 1 && port <= 65535
  }

  // 解析端口范围
  const parsePortRange = (range: string): number[] => {
    const ports: number[] = []
    const parts = range.split(',')
    
    for (const part of parts) {
      const trimmed = part.trim()
      if (trimmed.includes('-')) {
        const [start, end] = trimmed.split('-').map(p => parseInt(p.trim()))
        if (isValidPort(start) && isValidPort(end) && start <= end) {
          for (let i = start; i <= end; i++) {
            ports.push(i)
          }
        }
      } else {
        const port = parseInt(trimmed)
        if (isValidPort(port)) {
          ports.push(port)
        }
      }
    }
    
    return Array.from(new Set(ports)).sort((a, b) => a - b)
  }

  // 单端口扫描
  const scanSinglePort = async () => {
    if (!singleHost || !singlePort) {
      toast({
        title: "输入错误",
        description: "请输入主机地址和端口号",
        variant: "destructive"
      })
      return
    }

    const port = parseInt(singlePort)
    if (!isValidPort(port)) {
      toast({
        title: "端口无效",
        description: "端口号必须在1-65535之间",
        variant: "destructive"
      })
      return
    }

    setSingleScanning(true)
    setSingleResult(null)

    try {
      const result = await simulatePortScan(singleHost, port, singleProtocol)
      setSingleResult(result)
      
      toast({
        title: "扫描完成",
        description: `端口 ${port} ${result.status === 'open' ? '开放' : '关闭/过滤'}`,
      })
    } catch (error) {
      toast({
        title: "扫描失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      })
    } finally {
      setSingleScanning(false)
    }
  }

  // 多端口扫描
  const scanMultiplePorts = async () => {
    if (!multiHost || !portRange) {
      toast({
        title: "输入错误",
        description: "请输入主机地址和端口范围",
        variant: "destructive"
      })
      return
    }

    const ports = parsePortRange(portRange)
    if (ports.length === 0) {
      toast({
        title: "端口范围无效",
        description: "请输入有效的端口范围，如：80,443 或 1-1000",
        variant: "destructive"
      })
      return
    }

    if (ports.length > 100) {
      toast({
        title: "端口数量过多",
        description: "为了性能考虑，单次扫描端口数量不能超过100个",
        variant: "destructive"
      })
      return
    }

    setMultiScanning(true)
    setMultiResults([])
    setScanProgress({ current: 0, total: ports.length, percentage: 0 })

    try {
      const results: PortScanResult[] = []
      for (let i = 0; i < ports.length; i++) {
        const result = await simulatePortScan(multiHost, ports[i], multiProtocol)
        results.push(result)
        setMultiResults([...results])
        
        const progress = {
          current: i + 1,
          total: ports.length,
          percentage: Math.round(((i + 1) / ports.length) * 100)
        }
        setScanProgress(progress)
      }
      
      const openPorts = results.filter(r => r.status === 'open').length
      toast({
        title: "扫描完成",
        description: `共扫描 ${ports.length} 个端口，发现 ${openPorts} 个开放端口`,
      })
    } catch (error) {
      toast({
        title: "扫描失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      })
    } finally {
      setMultiScanning(false)
    }
  }

  // 预设扫描
  const scanPresetPorts = async () => {
    if (!presetHost) {
      toast({
        title: "输入错误",
        description: "请输入主机地址",
        variant: "destructive"
      })
      return
    }

    const preset = portPresets[presetType as keyof typeof portPresets]
    setPresetScanning(true)
    setPresetResults([])

    try {
      const results: PortScanResult[] = []
      for (let i = 0; i < preset.ports.length; i++) {
        const result = await simulatePortScan(presetHost, preset.ports[i], 'tcp')
        results.push(result)
        setPresetResults([...results])
      }
      
      const openPorts = results.filter(r => r.status === 'open').length
      toast({
        title: "扫描完成",
        description: `${preset.name}扫描完成，发现 ${openPorts} 个开放端口`,
      })
    } catch (error) {
      toast({
        title: "扫描失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      })
    } finally {
      setPresetScanning(false)
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case 'closed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'filtered':
        return <Shield className="h-4 w-4 text-yellow-500" />
      case 'timeout':
        return <AlertTriangle className="h-4 w-4 text-gray-500" />
      default:
        return null
    }
  }

  // 获取状态样式
  const getStatusBadge = (status: string) => {
    const variants = {
      open: 'bg-green-100 text-green-800 border-green-200',
      closed: 'bg-red-100 text-red-800 border-red-200',
      filtered: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      timeout: 'bg-gray-100 text-gray-800 border-gray-200'
    }
    
    const labels = {
      open: '开放',
      closed: '关闭',
      filtered: '过滤',
      timeout: '超时'
    }
    
    return (
      <Badge className={variants[status as keyof typeof variants]}>
        {getStatusIcon(status)}
        <span className="ml-1">{labels[status as keyof typeof labels]}</span>
      </Badge>
    )
  }

  // 复制结果
  const copyResults = (results: PortScanResult[], host: string) => {
    const openPorts = results.filter(r => r.status === 'open')
    const text = `端口扫描结果 - ${host}\n` +
      `开放端口 (${openPorts.length}个):\n` +
      openPorts.map(r => `${r.port}/${r.protocol} ${r.service ? `(${r.service})` : ''} - ${r.responseTime?.toFixed(1)}ms`).join('\n')
    
    navigator.clipboard.writeText(text)
    toast({
      title: "已复制",
      description: "扫描结果已复制到剪贴板",
    })
  }

  // 清空结果
  const clearAll = () => {
    setSingleHost('')
    setSinglePort('')
    setMultiHost('')
    setPortRange('')
    setPresetHost('')
    setSingleResult(null)
    setMultiResults([])
    setPresetResults([])
    setScanProgress({ current: 0, total: 0, percentage: 0 })
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Wifi className="h-6 w-6" />
              <CardTitle>端口扫描器</CardTitle>
            </div>
            <Button variant="outline" size="sm" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-1" />
              清空
            </Button>
          </div>
          <CardDescription>
            扫描主机端口开放状态，支持单端口、多端口和预设扫描模式
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="single">单端口扫描</TabsTrigger>
              <TabsTrigger value="multiple">多端口扫描</TabsTrigger>
              <TabsTrigger value="preset">预设扫描</TabsTrigger>
            </TabsList>

            <TabsContent value="single" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="singleHost">目标主机</Label>
                    <Input
                      id="singleHost"
                      value={singleHost}
                      onChange={(e) => setSingleHost(e.target.value)}
                      placeholder="example.com 或 ***********"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="singlePort">端口号</Label>
                      <Input
                        id="singlePort"
                        type="number"
                        value={singlePort}
                        onChange={(e) => setSinglePort(e.target.value)}
                        placeholder="80"
                        min="1"
                        max="65535"
                      />
                    </div>
                    <div>
                      <Label>协议类型</Label>
                      <Select value={singleProtocol} onValueChange={(value: 'tcp' | 'udp') => setSingleProtocol(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="tcp">TCP</SelectItem>
                          <SelectItem value="udp">UDP</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Button 
                    onClick={scanSinglePort} 
                    disabled={singleScanning}
                    className="w-full"
                  >
                    {singleScanning ? (
                      <>
                        <Zap className="h-4 w-4 mr-2 animate-spin" />
                        扫描中...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        开始扫描
                      </>
                    )}
                  </Button>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">扫描说明</h4>
                  <div className="text-sm text-gray-600 space-y-2">
                    <p>• <strong>开放</strong>：端口接受连接</p>
                    <p>• <strong>关闭</strong>：端口不接受连接</p>
                    <p>• <strong>过滤</strong>：被防火墙过滤</p>
                    <p>• <strong>超时</strong>：连接超时</p>
                  </div>
                </div>
              </div>

              {singleResult && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">扫描结果</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div>
                          <div className="font-medium text-lg">
                            {singleHost}:{singleResult.port}
                          </div>
                          <div className="text-sm text-gray-600">
                            {singleResult.protocol.toUpperCase()} · {singleResult.responseTime?.toFixed(1)}ms
                          </div>
                        </div>
                        {singleResult.service && (
                          <Badge variant="outline">{singleResult.service}</Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(singleResult.status)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="multiple" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="multiHost">目标主机</Label>
                    <Input
                      id="multiHost"
                      value={multiHost}
                      onChange={(e) => setMultiHost(e.target.value)}
                      placeholder="example.com 或 ***********"
                    />
                  </div>

                  <div>
                    <Label htmlFor="portRange">端口范围</Label>
                    <Input
                      id="portRange"
                      value={portRange}
                      onChange={(e) => setPortRange(e.target.value)}
                      placeholder="80,443 或 1-100 或 80,443,8080-8090"
                    />
                  </div>

                  <div>
                    <Label>协议类型</Label>
                    <Select value={multiProtocol} onValueChange={(value: 'tcp' | 'udp') => setMultiProtocol(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="tcp">TCP</SelectItem>
                        <SelectItem value="udp">UDP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button 
                    onClick={scanMultiplePorts} 
                    disabled={multiScanning}
                    className="w-full"
                  >
                    {multiScanning ? (
                      <>
                        <Zap className="h-4 w-4 mr-2 animate-spin" />
                        扫描中 ({scanProgress.current}/{scanProgress.total})
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        开始扫描
                      </>
                    )}
                  </Button>

                  {multiScanning && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>扫描进度</span>
                        <span>{scanProgress.percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                          style={{ width: `${scanProgress.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">端口范围格式</h4>
                  <div className="text-sm text-gray-600 space-y-2">
                    <p>• <code>80</code> - 单个端口</p>
                    <p>• <code>80,443</code> - 多个端口</p>
                    <p>• <code>1-100</code> - 端口范围</p>
                    <p>• <code>80,443,8080-8090</code> - 混合格式</p>
                    <p className="text-yellow-600">* 单次扫描最多100个端口</p>
                  </div>
                </div>
              </div>

              {multiResults.length > 0 && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">
                        扫描结果 ({multiResults.filter(r => r.status === 'open').length}/{multiResults.length})
                      </CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResults(multiResults, multiHost)}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制结果
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {multiResults.map((result, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center space-x-4">
                            <div>
                              <div className="font-medium">
                                端口 {result.port}
                              </div>
                              <div className="text-sm text-gray-600">
                                {result.protocol.toUpperCase()} · {result.responseTime?.toFixed(1)}ms
                              </div>
                            </div>
                            {result.service && (
                              <Badge variant="outline">{result.service}</Badge>
                            )}
                          </div>
                          {getStatusBadge(result.status)}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="preset" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="presetHost">目标主机</Label>
                    <Input
                      id="presetHost"
                      value={presetHost}
                      onChange={(e) => setPresetHost(e.target.value)}
                      placeholder="example.com 或 ***********"
                    />
                  </div>

                  <div>
                    <Label>扫描类型</Label>
                    <Select value={presetType} onValueChange={setPresetType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(portPresets).map(([key, preset]) => (
                          <SelectItem key={key} value={key}>
                            {preset.name} ({preset.ports.length}个端口)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button 
                    onClick={scanPresetPorts} 
                    disabled={presetScanning}
                    className="w-full"
                  >
                    {presetScanning ? (
                      <>
                        <Zap className="h-4 w-4 mr-2 animate-spin" />
                        扫描中...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        开始扫描
                      </>
                    )}
                  </Button>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">
                    {portPresets[presetType as keyof typeof portPresets].name}端口列表
                  </h4>
                  <div className="text-sm text-gray-600 max-h-48 overflow-y-auto">
                    <div className="grid grid-cols-3 gap-2">
                      {portPresets[presetType as keyof typeof portPresets].ports.map(port => (
                        <div key={port} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <span className="font-mono">{port}</span>
                          {serviceMap[port] && (
                            <span className="text-xs text-gray-500">{serviceMap[port]}</span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {presetResults.length > 0 && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">
                        扫描结果 ({presetResults.filter(r => r.status === 'open').length}/{presetResults.length})
                      </CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyResults(presetResults, presetHost)}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制结果
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {presetResults.map((result, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center space-x-4">
                            <div>
                              <div className="font-medium">
                                端口 {result.port}
                              </div>
                              <div className="text-sm text-gray-600">
                                {result.protocol.toUpperCase()} · {result.responseTime?.toFixed(1)}ms
                              </div>
                            </div>
                            {result.service && (
                              <Badge variant="outline">{result.service}</Badge>
                            )}
                          </div>
                          {getStatusBadge(result.status)}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
