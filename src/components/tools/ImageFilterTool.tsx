'use client'

import React, { useState, useRef, useEffect } from 'react'
import NextImage from 'next/image'
import { Upload, Download, RefreshCw, Image as ImageIcon, Sliders, Palette } from 'lucide-react'

interface FilterSettings {
  brightness: number
  contrast: number
  saturation: number
  grayscale: number
  sepia: number
  blur: number
  hueRotate: number
  invert: number
  opacity: number
}

interface PresetFilter {
  name: string
  settings: Partial<FilterSettings>
}

export default function ImageFilterTool() {
  const [originalImage, setOriginalImage] = useState<string | null>(null)
  const [processedImage, setProcessedImage] = useState<string | null>(null)
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 })
  const [isProcessing, setIsProcessing] = useState(false)
  
  const [filterSettings, setFilterSettings] = useState<FilterSettings>({
    brightness: 100,
    contrast: 100,
    saturation: 100,
    grayscale: 0,
    sepia: 0,
    blur: 0,
    hueRotate: 0,
    invert: 0,
    opacity: 100
  })
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const previewCanvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 预设滤镜
  const presetFilters: PresetFilter[] = [
    {
      name: '原图',
      settings: {
        brightness: 100,
        contrast: 100,
        saturation: 100,
        grayscale: 0,
        sepia: 0,
        blur: 0,
        hueRotate: 0,
        invert: 0,
        opacity: 100
      }
    },
    {
      name: '黑白',
      settings: { grayscale: 100 }
    },
    {
      name: '怀旧',
      settings: { sepia: 100 }
    },
    {
      name: '明亮',
      settings: { brightness: 130, contrast: 110 }
    },
    {
      name: '暗调',
      settings: { brightness: 70, contrast: 90 }
    },
    {
      name: '高对比度',
      settings: { contrast: 150, saturation: 120 }
    },
    {
      name: '褪色',
      settings: { saturation: 50, contrast: 80 }
    },
    {
      name: '冷色调',
      settings: { hueRotate: 180, saturation: 80 }
    },
    {
      name: '暖色调',
      settings: { hueRotate: 30, saturation: 120, brightness: 110 }
    },
    {
      name: '反色',
      settings: { invert: 100 }
    },
    {
      name: '模糊',
      settings: { blur: 5 }
    },
    {
      name: '梦幻',
      settings: { blur: 2, brightness: 110, saturation: 130 }
    }
  ]

  // 处理图片上传
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      const img = new Image()
      img.onload = () => {
        setImageSize({ width: img.width, height: img.height })
        setOriginalImage(event.target?.result as string)
        resetFilters()
        setProcessedImage(null)
      }
      img.src = event.target?.result as string
    }
    reader.readAsDataURL(file)
  }

  // 生成CSS滤镜字符串
  const generateFilterString = (settings: FilterSettings): string => {
    const filters = []
    
    if (settings.brightness !== 100) {
      filters.push(`brightness(${settings.brightness}%)`)
    }
    if (settings.contrast !== 100) {
      filters.push(`contrast(${settings.contrast}%)`)
    }
    if (settings.saturation !== 100) {
      filters.push(`saturate(${settings.saturation}%)`)
    }
    if (settings.grayscale > 0) {
      filters.push(`grayscale(${settings.grayscale}%)`)
    }
    if (settings.sepia > 0) {
      filters.push(`sepia(${settings.sepia}%)`)
    }
    if (settings.blur > 0) {
      filters.push(`blur(${settings.blur}px)`)
    }
    if (settings.hueRotate !== 0) {
      filters.push(`hue-rotate(${settings.hueRotate}deg)`)
    }
    if (settings.invert > 0) {
      filters.push(`invert(${settings.invert}%)`)
    }
    if (settings.opacity !== 100) {
      filters.push(`opacity(${settings.opacity}%)`)
    }
    
    return filters.join(' ')
  }

  // 更新预览
  useEffect(() => {
    if (!originalImage || !previewCanvasRef.current) return
    
    const canvas = previewCanvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const img = new Image()
    img.onload = () => {
      // 设置预览画布大小（限制最大尺寸）
      const maxSize = 600
      const scale = Math.min(maxSize / img.width, maxSize / img.height, 1)
      
      canvas.width = img.width * scale
      canvas.height = img.height * scale
      
      // 应用滤镜
      ctx.filter = generateFilterString(filterSettings)
      
      // 绘制图片
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
    }
    img.src = originalImage
  }, [originalImage, filterSettings])

  // 更新单个滤镜设置
  const updateFilter = (key: keyof FilterSettings, value: number) => {
    setFilterSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // 应用预设滤镜
  const applyPreset = (preset: PresetFilter) => {
    setFilterSettings({
      brightness: preset.settings.brightness ?? 100,
      contrast: preset.settings.contrast ?? 100,
      saturation: preset.settings.saturation ?? 100,
      grayscale: preset.settings.grayscale ?? 0,
      sepia: preset.settings.sepia ?? 0,
      blur: preset.settings.blur ?? 0,
      hueRotate: preset.settings.hueRotate ?? 0,
      invert: preset.settings.invert ?? 0,
      opacity: preset.settings.opacity ?? 100
    })
  }

  // 重置滤镜
  const resetFilters = () => {
    setFilterSettings({
      brightness: 100,
      contrast: 100,
      saturation: 100,
      grayscale: 0,
      sepia: 0,
      blur: 0,
      hueRotate: 0,
      invert: 0,
      opacity: 100
    })
  }

  // 应用滤镜并生成结果
  const applyFilters = () => {
    if (!originalImage || !canvasRef.current) return
    
    setIsProcessing(true)
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const img = new Image()
    img.onload = () => {
      // 设置画布为原始尺寸
      canvas.width = img.width
      canvas.height = img.height
      
      // 应用滤镜
      ctx.filter = generateFilterString(filterSettings)
      
      // 绘制图片
      ctx.drawImage(img, 0, 0)
      
      // 导出结果
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob)
          setProcessedImage(url)
        }
        setIsProcessing(false)
      }, 'image/png')
    }
    img.src = originalImage
  }

  // 下载图片
  const downloadImage = () => {
    if (!processedImage) return
    
    const link = document.createElement('a')
    link.href = processedImage
    link.download = `filtered_${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 重置
  const reset = () => {
    setOriginalImage(null)
    setProcessedImage(null)
    setImageSize({ width: 0, height: 0 })
    resetFilters()
    if (fileInputRef.current) fileInputRef.current.value = ''
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片滤镜效果工具</h2>
        <p className="text-gray-600">
          为您的图片添加各种滤镜效果，支持实时预览和自定义调整
        </p>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* 控制面板 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 预设滤镜 */}
          <div className="bg-white rounded-lg border border-gray-300 p-4">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Palette className="w-4 h-4" />
              预设滤镜
            </h3>
            <div className="grid grid-cols-3 gap-2">
              {presetFilters.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => applyPreset(preset)}
                  disabled={!originalImage}
                  className="px-3 py-2 text-sm bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-50 disabled:text-gray-400"
                >
                  {preset.name}
                </button>
              ))}
            </div>
          </div>

          {/* 自定义调整 */}
          <div className="bg-white rounded-lg border border-gray-300 p-4">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Sliders className="w-4 h-4" />
              自定义调整
            </h3>
            <div className="space-y-4">
              {/* 亮度 */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>亮度</span>
                  <span>{filterSettings.brightness}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="200"
                  value={filterSettings.brightness}
                  onChange={(e) => updateFilter('brightness', Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>

              {/* 对比度 */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>对比度</span>
                  <span>{filterSettings.contrast}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="200"
                  value={filterSettings.contrast}
                  onChange={(e) => updateFilter('contrast', Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>

              {/* 饱和度 */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>饱和度</span>
                  <span>{filterSettings.saturation}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="200"
                  value={filterSettings.saturation}
                  onChange={(e) => updateFilter('saturation', Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>

              {/* 灰度 */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>灰度</span>
                  <span>{filterSettings.grayscale}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={filterSettings.grayscale}
                  onChange={(e) => updateFilter('grayscale', Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>

              {/* 褐色 */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>褐色</span>
                  <span>{filterSettings.sepia}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={filterSettings.sepia}
                  onChange={(e) => updateFilter('sepia', Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>

              {/* 模糊 */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>模糊</span>
                  <span>{filterSettings.blur}px</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="20"
                  value={filterSettings.blur}
                  onChange={(e) => updateFilter('blur', Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>

              {/* 色相旋转 */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>色相旋转</span>
                  <span>{filterSettings.hueRotate}°</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="360"
                  value={filterSettings.hueRotate}
                  onChange={(e) => updateFilter('hueRotate', Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>

              {/* 反色 */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>反色</span>
                  <span>{filterSettings.invert}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={filterSettings.invert}
                  onChange={(e) => updateFilter('invert', Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>

              {/* 不透明度 */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>不透明度</span>
                  <span>{filterSettings.opacity}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={filterSettings.opacity}
                  onChange={(e) => updateFilter('opacity', Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-2">
            <button
              onClick={applyFilters}
              disabled={!originalImage || isProcessing}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              应用滤镜
            </button>
            {processedImage && (
              <button
                onClick={downloadImage}
                className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              >
                <Download className="w-4 h-4 inline mr-2" />
                下载结果
              </button>
            )}
            <button
              onClick={resetFilters}
              disabled={!originalImage}
              className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              重置滤镜
            </button>
            <button
              onClick={reset}
              className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
            >
              <RefreshCw className="w-4 h-4 inline mr-2" />
              重新开始
            </button>
          </div>

          {/* 图片信息 */}
          {originalImage && (
            <div className="bg-white rounded-lg border border-gray-300 p-4">
              <h3 className="font-medium mb-2">图片信息</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p>尺寸: {imageSize.width} × {imageSize.height} 像素</p>
                <p>当前滤镜: {generateFilterString(filterSettings) || '无'}</p>
              </div>
            </div>
          )}
        </div>

        {/* 预览区域 */}
        <div className="lg:col-span-2">
          {!originalImage ? (
            <div className="bg-white rounded-lg border-2 border-dashed border-gray-300 p-12">
              <div className="text-center">
                <ImageIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">上传图片开始编辑</p>
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleImageUpload}
                  accept="image/*"
                  className="hidden"
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  <Upload className="w-4 h-4 inline mr-2" />
                  选择图片
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 实时预览 */}
              <div className="bg-white rounded-lg border border-gray-300 p-4">
                <h3 className="font-medium mb-4">实时预览</h3>
                <div className="bg-gray-100 rounded-lg p-4 flex items-center justify-center min-h-[400px]">
                  <canvas
                    ref={previewCanvasRef}
                    className="max-w-full max-h-[600px]"
                  />
                </div>
              </div>

              {/* 处理结果 */}
              {processedImage && (
                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <h3 className="font-medium mb-4">处理结果</h3>
                  <div className="bg-gray-100 rounded-lg p-4 flex items-center justify-center">
                    <NextImage
                      src={processedImage}
                      alt="Processed"
                      width={600}
                      height={400}
                      className="max-w-full max-h-[600px] object-contain"
                      unoptimized
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Canvas 用于处理图片 */}
      <canvas ref={canvasRef} className="hidden" />

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Sliders className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>选择预设滤镜快速应用效果，或使用滑块自定义调整</li>
              <li>支持亮度、对比度、饱和度、色相等多种参数调节</li>
              <li>实时预览滤镜效果，所见即所得</li>
              <li>可以组合多种滤镜效果创造独特风格</li>
              <li>处理后的图片保持原始分辨率</li>
              <li>支持常见图片格式（JPG、PNG、GIF等）</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
