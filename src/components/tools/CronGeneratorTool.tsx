'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select } from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, RefreshCw, Clock, Calendar, Info } from 'lucide-react'

interface CronExpression {
  seconds: string
  minutes: string
  hours: string
  dayOfMonth: string
  month: string
  dayOfWeek: string
  year: string
}

const CronGeneratorTool: React.FC = () => {
  const [expression, setExpression] = useState<CronExpression>({
    seconds: '0',
    minutes: '0',
    hours: '0',
    dayOfMonth: '*',
    month: '*',
    dayOfWeek: '*',
    year: '*'
  })
  
  const [customExpression, setCustomExpression] = useState('')
  const [cronString, setCronString] = useState('')
  const [description, setDescription] = useState('')
  const [nextRuns, setNextRuns] = useState<string[]>([])

  // 预设的常用 Cron 表达式
  const presets = [
    { name: '每分钟', cron: '0 * * * * *', desc: '每分钟执行一次' },
    { name: '每5分钟', cron: '0 */5 * * * *', desc: '每5分钟执行一次' },
    { name: '每15分钟', cron: '0 */15 * * * *', desc: '每15分钟执行一次' },
    { name: '每30分钟', cron: '0 */30 * * * *', desc: '每30分钟执行一次' },
    { name: '每小时', cron: '0 0 * * * *', desc: '每小时整点执行' },
    { name: '每天午夜', cron: '0 0 0 * * *', desc: '每天午夜12点执行' },
    { name: '每天上午9点', cron: '0 0 9 * * *', desc: '每天上午9点执行' },
    { name: '工作日上午9点', cron: '0 0 9 * * 1-5', desc: '周一到周五上午9点执行' },
    { name: '每周一上午9点', cron: '0 0 9 * * 1', desc: '每周一上午9点执行' },
    { name: '每月1号午夜', cron: '0 0 0 1 * *', desc: '每月1号午夜执行' },
  ]

  // 时间选项
  const minuteOptions = Array.from({ length: 60 }, (_, i) => ({ value: i.toString(), label: i.toString().padStart(2, '0') }))
  const hourOptions = Array.from({ length: 24 }, (_, i) => ({ value: i.toString(), label: i.toString().padStart(2, '0') }))
  const dayOptions = Array.from({ length: 31 }, (_, i) => ({ value: (i + 1).toString(), label: (i + 1).toString() }))
  const monthOptions = [
    { value: '1', label: '一月' },
    { value: '2', label: '二月' },
    { value: '3', label: '三月' },
    { value: '4', label: '四月' },
    { value: '5', label: '五月' },
    { value: '6', label: '六月' },
    { value: '7', label: '七月' },
    { value: '8', label: '八月' },
    { value: '9', label: '九月' },
    { value: '10', label: '十月' },
    { value: '11', label: '十一月' },
    { value: '12', label: '十二月' },
  ]
  const weekdayOptions = [
    { value: '0', label: '周日' },
    { value: '1', label: '周一' },
    { value: '2', label: '周二' },
    { value: '3', label: '周三' },
    { value: '4', label: '周四' },
    { value: '5', label: '周五' },
    { value: '6', label: '周六' },
  ]

  // 生成 Cron 表达式字符串
  useEffect(() => {
    const cron = `${expression.seconds} ${expression.minutes} ${expression.hours} ${expression.dayOfMonth} ${expression.month} ${expression.dayOfWeek} ${expression.year}`.trim()
    setCronString(cron)
    setCustomExpression(cron)
    generateDescription(cron)
    generateNextRuns(cron)
  }, [expression])

  // 生成描述
  const generateDescription = (cron: string) => {
    const parts = cron.split(' ')
    if (parts.length < 6) return

    const [sec, min, hour, day, month, weekday] = parts
    let desc = '执行时间：'

    // 简单的描述生成逻辑
    if (sec === '0' && min === '0' && hour === '0' && day === '*' && month === '*' && weekday === '*') {
      desc = '每天午夜执行'
    } else if (sec === '0' && min === '0' && hour !== '*' && day === '*' && month === '*' && weekday === '*') {
      desc = `每天${hour}点执行`
    } else if (sec === '0' && min !== '*' && hour !== '*' && day === '*' && month === '*' && weekday === '*') {
      desc = `每天${hour}:${min.padStart(2, '0')}执行`
    } else if (min.includes('*/')) {
      const interval = min.split('*/')[1]
      desc = `每${interval}分钟执行一次`
    } else if (hour.includes('*/')) {
      const interval = hour.split('*/')[1]
      desc = `每${interval}小时执行一次`
    } else {
      desc = '自定义执行规则'
    }

    setDescription(desc)
  }

  // 生成接下来的执行时间（简化版本）
  const generateNextRuns = (cron: string) => {
    // 这里是简化版本，实际应该使用专门的 cron 解析库
    const runs: string[] = []
    const now = new Date()
    
    for (let i = 0; i < 5; i++) {
      const nextTime = new Date(now.getTime() + (i + 1) * 60 * 1000)
      runs.push(nextTime.toLocaleString('zh-CN'))
    }
    
    setNextRuns(runs)
  }

  // 应用预设表达式
  const applyPreset = (preset: typeof presets[0]) => {
    setCustomExpression(preset.cron)
    const parts = preset.cron.split(' ')
    if (parts.length >= 6) {
      setExpression({
        seconds: parts[0] || '0',
        minutes: parts[1] || '0',
        hours: parts[2] || '0',
        dayOfMonth: parts[3] || '*',
        month: parts[4] || '*',
        dayOfWeek: parts[5] || '*',
        year: parts[6] || '*'
      })
    }
  }

  // 解析自定义表达式
  const parseCustomExpression = () => {
    const parts = customExpression.trim().split(/\s+/)
    if (parts.length >= 6) {
      setExpression({
        seconds: parts[0] || '0',
        minutes: parts[1] || '0',
        hours: parts[2] || '0',
        dayOfMonth: parts[3] || '*',
        month: parts[4] || '*',
        dayOfWeek: parts[5] || '*',
        year: parts[6] || '*'
      })
    }
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // 重置表达式
  const resetExpression = () => {
    setExpression({
      seconds: '0',
      minutes: '0',
      hours: '0',
      dayOfMonth: '*',
      month: '*',
      dayOfWeek: '*',
      year: '*'
    })
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Clock className="w-8 h-8" />
          Cron 表达式生成器
        </h1>
        <p className="text-muted-foreground">
          生成和解析 Cron 表达式，设置定时任务执行规则
        </p>
      </div>

      <Tabs defaultValue="builder" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="builder">可视化构建</TabsTrigger>
          <TabsTrigger value="presets">常用预设</TabsTrigger>
          <TabsTrigger value="custom">自定义输入</TabsTrigger>
        </TabsList>

        <TabsContent value="builder" className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Cron 表达式构建器</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
              <div className="space-y-2">
                <Label>秒 (0-59)</Label>
                <Input
                  value={expression.seconds}
                  onChange={(e) => setExpression(prev => ({ ...prev, seconds: e.target.value }))}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2">
                <Label>分 (0-59)</Label>
                <Input
                  value={expression.minutes}
                  onChange={(e) => setExpression(prev => ({ ...prev, minutes: e.target.value }))}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2">
                <Label>时 (0-23)</Label>
                <Input
                  value={expression.hours}
                  onChange={(e) => setExpression(prev => ({ ...prev, hours: e.target.value }))}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2">
                <Label>日 (1-31)</Label>
                <Input
                  value={expression.dayOfMonth}
                  onChange={(e) => setExpression(prev => ({ ...prev, dayOfMonth: e.target.value }))}
                  placeholder="*"
                />
              </div>
              <div className="space-y-2">
                <Label>月 (1-12)</Label>
                <Input
                  value={expression.month}
                  onChange={(e) => setExpression(prev => ({ ...prev, month: e.target.value }))}
                  placeholder="*"
                />
              </div>
              <div className="space-y-2">
                <Label>周 (0-7)</Label>
                <Input
                  value={expression.dayOfWeek}
                  onChange={(e) => setExpression(prev => ({ ...prev, dayOfWeek: e.target.value }))}
                  placeholder="*"
                />
              </div>
              <div className="space-y-2">
                <Label>年 (可选)</Label>
                <Input
                  value={expression.year}
                  onChange={(e) => setExpression(prev => ({ ...prev, year: e.target.value }))}
                  placeholder="*"
                />
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                <Info className="w-4 h-4" />
                Cron 表达式格式说明
              </div>
              <div className="text-xs space-y-1">
                <p>• * 表示所有值</p>
                <p>• */n 表示每n个单位</p>
                <p>• a-b 表示从a到b的范围</p>
                <p>• a,b,c 表示特定的几个值</p>
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button variant="outline" onClick={resetExpression} className="flex items-center gap-2">
                <RefreshCw className="w-4 h-4" />
                重置
              </Button>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="presets" className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">常用 Cron 表达式</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {presets.map((preset, index) => (
                <div
                  key={index}
                  className="p-4 border rounded-lg cursor-pointer hover:bg-muted transition-colors"
                  onClick={() => applyPreset(preset)}
                >
                  <div className="font-medium">{preset.name}</div>
                  <div className="text-sm text-muted-foreground font-mono bg-muted px-2 py-1 rounded mt-1">
                    {preset.cron}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">{preset.desc}</div>
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="custom" className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">自定义 Cron 表达式</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>输入 Cron 表达式</Label>
                <div className="flex gap-2">
                  <Input
                    value={customExpression}
                    onChange={(e) => setCustomExpression(e.target.value)}
                    placeholder="0 0 9 * * 1-5"
                    className="font-mono"
                  />
                  <Button onClick={parseCustomExpression}>解析</Button>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                格式：秒 分 时 日 月 周 [年]
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 结果显示 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            生成的表达式
          </h3>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Cron 表达式</Label>
              <div className="flex gap-2">
                <Input
                  value={cronString}
                  readOnly
                  className="font-mono bg-muted"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(cronString)}
                  className="flex items-center gap-2"
                >
                  <Copy className="w-4 h-4" />
                  复制
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>执行规则描述</Label>
              <div className="p-3 bg-muted rounded-lg text-sm">
                {description}
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">接下来的执行时间</h3>
          <div className="space-y-2">
            {nextRuns.map((time, index) => (
              <div key={index} className="p-2 bg-muted rounded text-sm font-mono">
                {time}
              </div>
            ))}
          </div>
          <div className="text-xs text-muted-foreground mt-2">
            * 以上时间为示例，实际执行时间请以系统为准
          </div>
        </Card>
      </div>

      {/* 语法说明 */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Cron 表达式语法说明</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h4 className="font-medium">字段说明</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>秒</span>
                <span className="text-muted-foreground">0-59</span>
              </div>
              <div className="flex justify-between">
                <span>分</span>
                <span className="text-muted-foreground">0-59</span>
              </div>
              <div className="flex justify-between">
                <span>时</span>
                <span className="text-muted-foreground">0-23</span>
              </div>
              <div className="flex justify-between">
                <span>日</span>
                <span className="text-muted-foreground">1-31</span>
              </div>
              <div className="flex justify-between">
                <span>月</span>
                <span className="text-muted-foreground">1-12</span>
              </div>
              <div className="flex justify-between">
                <span>周</span>
                <span className="text-muted-foreground">0-7 (0和7都是周日)</span>
              </div>
              <div className="flex justify-between">
                <span>年</span>
                <span className="text-muted-foreground">1970-2099 (可选)</span>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <h4 className="font-medium">特殊字符</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="font-mono">*</span>
                <span className="text-muted-foreground">任意值</span>
              </div>
              <div className="flex justify-between">
                <span className="font-mono">?</span>
                <span className="text-muted-foreground">不指定值（用于日和周）</span>
              </div>
              <div className="flex justify-between">
                <span className="font-mono">-</span>
                <span className="text-muted-foreground">范围（如 1-5）</span>
              </div>
              <div className="flex justify-between">
                <span className="font-mono">,</span>
                <span className="text-muted-foreground">枚举（如 1,3,5）</span>
              </div>
              <div className="flex justify-between">
                <span className="font-mono">/</span>
                <span className="text-muted-foreground">间隔（如 */5）</span>
              </div>
              <div className="flex justify-between">
                <span className="font-mono">L</span>
                <span className="text-muted-foreground">最后（月的最后一天）</span>
              </div>
              <div className="flex justify-between">
                <span className="font-mono">W</span>
                <span className="text-muted-foreground">工作日</span>
              </div>
              <div className="flex justify-between">
                <span className="font-mono">#</span>
                <span className="text-muted-foreground">第几个周几（如 2#1表示第一个周二）</span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default CronGeneratorTool
