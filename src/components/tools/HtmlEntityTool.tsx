'use client'

import { useState, useEffect } from 'react'
import { Code, Copy, Check, FileText, AlertCircle, Info } from 'lucide-react'

interface EntityMap {
  [key: string]: string
}

// HTML实体映射表
const htmlEntities: EntityMap = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#39;',
  '¡': '&iexcl;',
  '¢': '&cent;',
  '£': '&pound;',
  '¤': '&curren;',
  '¥': '&yen;',
  '¦': '&brvbar;',
  '§': '&sect;',
  '¨': '&uml;',
  '©': '&copy;',
  'ª': '&ordf;',
  '«': '&laquo;',
  '¬': '&not;',
  '­': '&shy;',
  '®': '&reg;',
  '¯': '&macr;',
  '°': '&deg;',
  '±': '&plusmn;',
  '²': '&sup2;',
  '³': '&sup3;',
  '´': '&acute;',
  'µ': '&micro;',
  '¶': '&para;',
  '·': '&middot;',
  '¸': '&cedil;',
  '¹': '&sup1;',
  'º': '&ordm;',
  '»': '&raquo;',
  '¼': '&frac14;',
  '½': '&frac12;',
  '¾': '&frac34;',
  '¿': '&iquest;',
  'À': '&Agrave;',
  'Á': '&Aacute;',
  'Â': '&Acirc;',
  'Ã': '&Atilde;',
  'Ä': '&Auml;',
  'Å': '&Aring;',
  'Æ': '&AElig;',
  'Ç': '&Ccedil;',
  'È': '&Egrave;',
  'É': '&Eacute;',
  'Ê': '&Ecirc;',
  'Ë': '&Euml;',
  'Ì': '&Igrave;',
  'Í': '&Iacute;',
  'Î': '&Icirc;',
  'Ï': '&Iuml;',
  'Ð': '&ETH;',
  'Ñ': '&Ntilde;',
  'Ò': '&Ograve;',
  'Ó': '&Oacute;',
  'Ô': '&Ocirc;',
  'Õ': '&Otilde;',
  'Ö': '&Ouml;',
  '×': '&times;',
  'Ø': '&Oslash;',
  'Ù': '&Ugrave;',
  'Ú': '&Uacute;',
  'Û': '&Ucirc;',
  'Ü': '&Uuml;',
  'Ý': '&Yacute;',
  'Þ': '&THORN;',
  'ß': '&szlig;',
  'à': '&agrave;',
  'á': '&aacute;',
  'â': '&acirc;',
  'ã': '&atilde;',
  'ä': '&auml;',
  'å': '&aring;',
  'æ': '&aelig;',
  'ç': '&ccedil;',
  'è': '&egrave;',
  'é': '&eacute;',
  'ê': '&ecirc;',
  'ë': '&euml;',
  'ì': '&igrave;',
  'í': '&iacute;',
  'î': '&icirc;',
  'ï': '&iuml;',
  'ð': '&eth;',
  'ñ': '&ntilde;',
  'ò': '&ograve;',
  'ó': '&oacute;',
  'ô': '&ocirc;',
  'õ': '&otilde;',
  'ö': '&ouml;',
  '÷': '&divide;',
  'ø': '&oslash;',
  'ù': '&ugrave;',
  'ú': '&uacute;',
  'û': '&ucirc;',
  'ü': '&uuml;',
  'ý': '&yacute;',
  'þ': '&thorn;',
  'ÿ': '&yuml;',
  '–': '&ndash;',
  '—': '&mdash;',
  '\u2018': '&lsquo;',  // '
  '\u2019': '&rsquo;',  // '
  '‚': '&sbquo;',
  '\u201C': '&ldquo;',  // "
  '\u201D': '&rdquo;',  // "
  '„': '&bdquo;',
  '†': '&dagger;',
  '‡': '&Dagger;',
  '•': '&bull;',
  '…': '&hellip;',
  '‰': '&permil;',
  '′': '&prime;',
  '″': '&Prime;',
  '‹': '&lsaquo;',
  '›': '&rsaquo;',
  '‾': '&oline;',
  '⁄': '&frasl;',
  '€': '&euro;',
  'ℑ': '&image;',
  '℘': '&weierp;',
  'ℜ': '&real;',
  '™': '&trade;',
  'ℵ': '&alefsym;',
  '←': '&larr;',
  '↑': '&uarr;',
  '→': '&rarr;',
  '↓': '&darr;',
  '↔': '&harr;',
  '↵': '&crarr;',
  '⇐': '&lArr;',
  '⇑': '&uArr;',
  '⇒': '&rArr;',
  '⇓': '&dArr;',
  '⇔': '&hArr;',
  '∀': '&forall;',
  '∂': '&part;',
  '∃': '&exist;',
  '∅': '&empty;',
  '∇': '&nabla;',
  '∈': '&isin;',
  '∉': '&notin;',
  '∋': '&ni;',
  '∏': '&prod;',
  '∑': '&sum;',
  '−': '&minus;',
  '∗': '&lowast;',
  '√': '&radic;',
  '∝': '&prop;',
  '∞': '&infin;',
  '∠': '&ang;',
  '∧': '&and;',
  '∨': '&or;',
  '∩': '&cap;',
  '∪': '&cup;',
  '∫': '&int;',
  '∴': '&there4;',
  '∼': '&sim;',
  '≅': '&cong;',
  '≈': '&asymp;',
  '≠': '&ne;',
  '≡': '&equiv;',
  '≤': '&le;',
  '≥': '&ge;',
  '⊂': '&sub;',
  '⊃': '&sup;',
  '⊄': '&nsub;',
  '⊆': '&sube;',
  '⊇': '&supe;',
  '⊕': '&oplus;',
  '⊗': '&otimes;',
  '⊥': '&perp;',
  '⋅': '&sdot;',
  '⌈': '&lceil;',
  '⌉': '&rceil;',
  '⌊': '&lfloor;',
  '⌋': '&rfloor;',
  '〈': '&lang;',
  '〉': '&rang;',
  '◊': '&loz;',
  '♠': '&spades;',
  '♣': '&clubs;',
  '♥': '&hearts;',
  '♦': '&diams;',
}

// 创建反向映射表
const reverseHtmlEntities: EntityMap = Object.entries(htmlEntities).reduce((acc, [char, entity]) => {
  acc[entity] = char
  return acc
}, {} as EntityMap)

export default function HtmlEntityTool() {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [mode, setMode] = useState<'encode' | 'decode'>('encode')
  const [encodeMode, setEncodeMode] = useState<'all' | 'special'>('special')
  const [copied, setCopied] = useState(false)
  const [stats, setStats] = useState({
    charCount: 0,
    entityCount: 0,
    specialCharCount: 0,
  })

  // HTML实体编码
  const encodeHtmlEntities = (text: string, mode: 'all' | 'special'): string => {
    if (mode === 'all') {
      // 编码所有非ASCII字符
      return text.split('').map(char => {
        const code = char.charCodeAt(0)
        if (code > 127) {
          return `&#${code};`
        }
        return htmlEntities[char] || char
      }).join('')
    } else {
      // 只编码特殊字符
      return text.split('').map(char => htmlEntities[char] || char).join('')
    }
  }

  // HTML实体解码
  const decodeHtmlEntities = (text: string): string => {
    // 解码命名实体
    let decoded = text
    Object.entries(reverseHtmlEntities).forEach(([entity, char]) => {
      decoded = decoded.replace(new RegExp(entity, 'g'), char)
    })
    
    // 解码数字实体（十进制）
    decoded = decoded.replace(/&#(\d+);/g, (match, code) => {
      return String.fromCharCode(parseInt(code, 10))
    })
    
    // 解码数字实体（十六进制）
    decoded = decoded.replace(/&#x([0-9a-fA-F]+);/g, (match, code) => {
      return String.fromCharCode(parseInt(code, 16))
    })
    
    return decoded
  }

  // 统计信息
  const calculateStats = (text: string) => {
    const charCount = text.length
    const entityMatches = text.match(/&[#\w]+;/g) || []
    const entityCount = entityMatches.length
    const specialChars = text.split('').filter(char => htmlEntities[char])
    const specialCharCount = specialChars.length

    setStats({
      charCount,
      entityCount,
      specialCharCount,
    })
  }

  // 处理输入变化
  useEffect(() => {
    if (!input) {
      setOutput('')
      setStats({ charCount: 0, entityCount: 0, specialCharCount: 0 })
      return
    }

    try {
      if (mode === 'encode') {
        const encoded = encodeHtmlEntities(input, encodeMode)
        setOutput(encoded)
        calculateStats(input)
      } else {
        const decoded = decodeHtmlEntities(input)
        setOutput(decoded)
        calculateStats(input)
      }
    } catch (error) {
      setOutput('处理错误：无效的输入')
    }
  }, [input, mode, encodeMode])

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(output)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 加载示例
  const loadExample = () => {
    if (mode === 'encode') {
      setInput('Hello <World>! 特殊字符 & 符号 © 2024 → ♥')
    } else {
      setInput('Hello &lt;World&gt;! 特殊字符 &amp; 符号 &copy; 2024 &rarr; &hearts;')
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">HTML 实体编码/解码</h2>
        <p className="text-gray-600">
          在线转换 HTML 实体，支持命名实体和数字实体的编码解码
        </p>
      </div>

      {/* 模式选择 */}
      <div className="mb-6 flex flex-wrap gap-4">
        <div className="flex rounded-lg border border-gray-200 overflow-hidden">
          <button
            onClick={() => setMode('encode')}
            className={`px-4 py-2 font-medium transition-colors ${
              mode === 'encode'
                ? 'bg-blue-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            编码
          </button>
          <button
            onClick={() => setMode('decode')}
            className={`px-4 py-2 font-medium transition-colors ${
              mode === 'decode'
                ? 'bg-blue-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            解码
          </button>
        </div>

        {mode === 'encode' && (
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">编码模式：</span>
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                value="special"
                checked={encodeMode === 'special'}
                onChange={(e) => setEncodeMode('special')}
                className="text-blue-500"
              />
              <span className="text-sm">仅特殊字符</span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                value="all"
                checked={encodeMode === 'all'}
                onChange={(e) => setEncodeMode('all')}
                className="text-blue-500"
              />
              <span className="text-sm">所有非ASCII字符</span>
            </label>
          </div>
        )}
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="font-medium flex items-center gap-2">
              <FileText className="w-4 h-4" />
              {mode === 'encode' ? '原始文本' : 'HTML 实体'}
            </label>
            <button
              onClick={loadExample}
              className="text-sm text-blue-500 hover:text-blue-600"
            >
              加载示例
            </button>
          </div>
          <textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={mode === 'encode' ? '输入要编码的文本...' : '输入要解码的HTML实体...'}
            className="w-full h-64 p-4 font-mono text-sm border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          
          {/* 统计信息 */}
          <div className="mt-2 flex flex-wrap gap-4 text-sm text-gray-600">
            <span>字符数：{stats.charCount}</span>
            {mode === 'encode' ? (
              <span>特殊字符：{stats.specialCharCount}</span>
            ) : (
              <span>实体数：{stats.entityCount}</span>
            )}
          </div>
        </div>

        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="font-medium flex items-center gap-2">
              <Code className="w-4 h-4" />
              {mode === 'encode' ? 'HTML 实体' : '解码文本'}
            </label>
            <button
              onClick={copyToClipboard}
              disabled={!output}
              className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              {copied ? (
                <>
                  <Check className="w-4 h-4" />
                  已复制
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4" />
                  复制
                </>
              )}
            </button>
          </div>
          <textarea
            value={output}
            readOnly
            placeholder="转换结果将显示在这里..."
            className="w-full h-64 p-4 font-mono text-sm bg-gray-50 border border-gray-300 rounded-lg resize-none"
          />
        </div>
      </div>

      {/* 常用实体参考 */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium mb-3 flex items-center gap-2">
          <AlertCircle className="w-4 h-4" />
          常用 HTML 实体
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
          <div className="font-mono">
            <span className="text-gray-600">&lt;</span> → &amp;lt;
          </div>
          <div className="font-mono">
            <span className="text-gray-600">&gt;</span> → &amp;gt;
          </div>
          <div className="font-mono">
            <span className="text-gray-600">&amp;</span> → &amp;amp;
          </div>
          <div className="font-mono">
            <span className="text-gray-600">&quot;</span> → &amp;quot;
          </div>
          <div className="font-mono">
            <span className="text-gray-600">&apos;</span> → &amp;#39;
          </div>
          <div className="font-mono">
            <span className="text-gray-600">©</span> → &amp;copy;
          </div>
          <div className="font-mono">
            <span className="text-gray-600">®</span> → &amp;reg;
          </div>
          <div className="font-mono">
            <span className="text-gray-600">™</span> → &amp;trade;
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>编码模式：将特殊字符转换为 HTML 实体</li>
              <li>解码模式：将 HTML 实体转换回原始字符</li>
              <li>支持命名实体（如 &amp;copy;）和数字实体（如 &amp;#169; 或 &amp;#xA9;）</li>
              <li>『仅特殊字符』模式只编码 HTML 特殊字符</li>
              <li>『所有非ASCII字符』模式会编码所有 Unicode 字符</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
