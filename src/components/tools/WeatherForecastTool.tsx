'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Cloud, 
  CloudRain, 
  CloudSnow, 
  Sun, 
  CloudDrizzle,
  Wind,
  Droplets,
  Thermometer,
  Eye,
  Gauge,
  MapPin,
  Calendar,
  AlertCircle,
  Loader2,
  CloudLightning,
  CloudFog
} from 'lucide-react'
import { useTranslations } from '@/hooks/useTranslations'

interface WeatherData {
  location: {
    name: string
    country: string
    lat: number
    lon: number
    localtime: string
  }
  current: {
    temp: number
    feelsLike: number
    humidity: number
    windSpeed: number
    windDir: string
    pressure: number
    visibility: number
    uv: number
    condition: string
    icon: string
  }
  forecast: DayForecast[]
}

interface DayForecast {
  date: string
  maxTemp: number
  minTemp: number
  avgTemp: number
  condition: string
  icon: string
  chanceOfRain: number
  humidity: number
  windSpeed: number
}

interface SavedLocation {
  id: string
  name: string
  country: string
  lat: number
  lon: number
}

// 模拟天气数据（实际应用中应该调用天气API）
const mockWeatherData: { [key: string]: WeatherData } = {
  '北京': {
    location: {
      name: '北京',
      country: '中国',
      lat: 39.9042,
      lon: 116.4074,
      localtime: new Date().toLocaleString('zh-CN')
    },
    current: {
      temp: 25,
      feelsLike: 27,
      humidity: 65,
      windSpeed: 12,
      windDir: '东北',
      pressure: 1013,
      visibility: 10,
      uv: 6,
      condition: '多云',
      icon: 'partly-cloudy'
    },
    forecast: [
      {
        date: new Date().toISOString().split('T')[0],
        maxTemp: 28,
        minTemp: 18,
        avgTemp: 23,
        condition: '多云',
        icon: 'partly-cloudy',
        chanceOfRain: 20,
        humidity: 60,
        windSpeed: 10
      },
      {
        date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
        maxTemp: 26,
        minTemp: 17,
        avgTemp: 22,
        condition: '小雨',
        icon: 'light-rain',
        chanceOfRain: 70,
        humidity: 75,
        windSpeed: 15
      },
      {
        date: new Date(Date.now() + 172800000).toISOString().split('T')[0],
        maxTemp: 24,
        minTemp: 16,
        avgTemp: 20,
        condition: '晴',
        icon: 'sunny',
        chanceOfRain: 10,
        humidity: 55,
        windSpeed: 8
      }
    ]
  },
  'Beijing': {
    location: {
      name: 'Beijing',
      country: 'China',
      lat: 39.9042,
      lon: 116.4074,
      localtime: new Date().toLocaleString('en-US')
    },
    current: {
      temp: 25,
      feelsLike: 27,
      humidity: 65,
      windSpeed: 12,
      windDir: 'NE',
      pressure: 1013,
      visibility: 10,
      uv: 6,
      condition: 'Partly Cloudy',
      icon: 'partly-cloudy'
    },
    forecast: [
      {
        date: new Date().toISOString().split('T')[0],
        maxTemp: 28,
        minTemp: 18,
        avgTemp: 23,
        condition: 'Partly Cloudy',
        icon: 'partly-cloudy',
        chanceOfRain: 20,
        humidity: 60,
        windSpeed: 10
      },
      {
        date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
        maxTemp: 26,
        minTemp: 17,
        avgTemp: 22,
        condition: 'Light Rain',
        icon: 'light-rain',
        chanceOfRain: 70,
        humidity: 75,
        windSpeed: 15
      },
      {
        date: new Date(Date.now() + 172800000).toISOString().split('T')[0],
        maxTemp: 24,
        minTemp: 16,
        avgTemp: 20,
        condition: 'Sunny',
        icon: 'sunny',
        chanceOfRain: 10,
        humidity: 55,
        windSpeed: 8
      }
    ]
  }
}

export default function WeatherForecastTool() {
  const t = useTranslations()
  const [location, setLocation] = useState('')
  const [unit, setUnit] = useState<'celsius' | 'fahrenheit'>('celsius')
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null)
  const [savedLocations, setSavedLocations] = useState<SavedLocation[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('current')

  // 从本地存储加载保存的位置
  useEffect(() => {
    const saved = localStorage.getItem('weatherSavedLocations')
    if (saved) {
      setSavedLocations(JSON.parse(saved))
    }
  }, [])

  // 获取天气图标
  const getWeatherIcon = (iconType: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      'sunny': <Sun className="w-12 h-12 text-yellow-500" />,
      'partly-cloudy': <Cloud className="w-12 h-12 text-gray-500" />,
      'cloudy': <Cloud className="w-12 h-12 text-gray-600" />,
      'light-rain': <CloudDrizzle className="w-12 h-12 text-blue-400" />,
      'rain': <CloudRain className="w-12 h-12 text-blue-500" />,
      'heavy-rain': <CloudRain className="w-12 h-12 text-blue-600" />,
      'snow': <CloudSnow className="w-12 h-12 text-blue-300" />,
      'thunderstorm': <CloudLightning className="w-12 h-12 text-purple-500" />,
      'fog': <CloudFog className="w-12 h-12 text-gray-400" />
    }
    return iconMap[iconType] || <Cloud className="w-12 h-12 text-gray-500" />
  }

  // 温度转换
  const convertTemp = (temp: number, toUnit: 'celsius' | 'fahrenheit' = unit) => {
    if (toUnit === 'fahrenheit') {
      return Math.round((temp * 9/5) + 32)
    }
    return Math.round(temp)
  }

  // 获取温度单位符号
  const getTempUnit = () => unit === 'celsius' ? '°C' : '°F'

  // 搜索天气
  const searchWeather = async () => {
    if (!location.trim()) {
      setError(t('weather.errors.enterLocation'))
      return
    }

    setLoading(true)
    setError('')

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟获取天气数据
      const data = mockWeatherData[location] || mockWeatherData['北京']
      setWeatherData({
        ...data,
        location: {
          ...data.location,
          name: location
        }
      })
    } catch (err) {
      setError(t('weather.errors.fetchFailed'))
    } finally {
      setLoading(false)
    }
  }

  // 保存位置
  const saveLocation = () => {
    if (!weatherData) return

    const newLocation: SavedLocation = {
      id: Date.now().toString(),
      name: weatherData.location.name,
      country: weatherData.location.country,
      lat: weatherData.location.lat,
      lon: weatherData.location.lon
    }

    const updated = [...savedLocations, newLocation]
    setSavedLocations(updated)
    localStorage.setItem('weatherSavedLocations', JSON.stringify(updated))
  }

  // 删除保存的位置
  const deleteLocation = (id: string) => {
    const updated = savedLocations.filter(loc => loc.id !== id)
    setSavedLocations(updated)
    localStorage.setItem('weatherSavedLocations', JSON.stringify(updated))
  }

  // 加载保存的位置天气
  const loadSavedLocation = (loc: SavedLocation) => {
    setLocation(loc.name)
    searchWeather()
  }

  // 获取当前位置天气
  const getCurrentLocation = () => {
    if ('geolocation' in navigator) {
      setLoading(true)
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          // 这里应该使用反向地理编码API获取位置名称
          setLocation(t('weather.currentLocation'))
          await searchWeather()
        },
        (error) => {
          setError(t('weather.errors.locationFailed'))
          setLoading(false)
        }
      )
    } else {
      setError(t('weather.errors.locationNotSupported'))
    }
  }

  return (
    <div className="w-full max-w-4xl space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>{t('tools.weather-forecast.title')}</CardTitle>
          <CardDescription>
            {t('tools.weather-forecast.description')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 搜索栏 */}
          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                placeholder={t('weather.searchPlaceholder')}
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && searchWeather()}
                className="flex-1"
              />
              <Button onClick={searchWeather} disabled={loading}>
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  t('weather.search')
                )}
              </Button>
              <Button variant="outline" onClick={getCurrentLocation}>
                <MapPin className="w-4 h-4" />
              </Button>
            </div>

            {/* 单位选择 */}
            <div className="flex items-center gap-4">
              <Label>{t('weather.unit')}</Label>
              <Select value={unit} onValueChange={(value: 'celsius' | 'fahrenheit') => setUnit(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="celsius">{t('weather.celsius')}</SelectItem>
                  <SelectItem value="fahrenheit">{t('weather.fahrenheit')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 天气信息 */}
          {weatherData && (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="current">{t('weather.current')}</TabsTrigger>
                <TabsTrigger value="forecast">{t('weather.forecast')}</TabsTrigger>
                <TabsTrigger value="saved">{t('weather.saved')}</TabsTrigger>
              </TabsList>

              {/* 当前天气 */}
              <TabsContent value="current" className="space-y-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      {/* 位置信息 */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-2xl font-bold">{weatherData.location.name}</h3>
                          <p className="text-muted-foreground">{weatherData.location.country}</p>
                          <p className="text-sm text-muted-foreground">{weatherData.location.localtime}</p>
                        </div>
                        <Button variant="outline" size="sm" onClick={saveLocation}>
                          {t('weather.saveLocation')}
                        </Button>
                      </div>

                      {/* 主要天气信息 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          {getWeatherIcon(weatherData.current.icon)}
                          <div>
                            <p className="text-4xl font-bold">
                              {convertTemp(weatherData.current.temp)}{getTempUnit()}
                            </p>
                            <p className="text-lg text-muted-foreground">{weatherData.current.condition}</p>
                            <p className="text-sm text-muted-foreground">
                              {t('weather.feelsLike')}: {convertTemp(weatherData.current.feelsLike)}{getTempUnit()}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* 详细信息网格 */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="flex items-center gap-2">
                          <Wind className="w-5 h-5 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">{t('weather.wind')}</p>
                            <p className="font-medium">{weatherData.current.windSpeed} km/h {weatherData.current.windDir}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Droplets className="w-5 h-5 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">{t('weather.humidity')}</p>
                            <p className="font-medium">{weatherData.current.humidity}%</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Gauge className="w-5 h-5 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">{t('weather.pressure')}</p>
                            <p className="font-medium">{weatherData.current.pressure} hPa</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Eye className="w-5 h-5 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">{t('weather.visibility')}</p>
                            <p className="font-medium">{weatherData.current.visibility} km</p>
                          </div>
                        </div>
                      </div>

                      {/* UV指数 */}
                      <div className="p-4 bg-muted rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-muted-foreground">{t('weather.uvIndex')}</p>
                            <p className="text-2xl font-bold">{weatherData.current.uv}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">
                              {weatherData.current.uv <= 2 && t('weather.uvLow')}
                              {weatherData.current.uv > 2 && weatherData.current.uv <= 5 && t('weather.uvModerate')}
                              {weatherData.current.uv > 5 && weatherData.current.uv <= 7 && t('weather.uvHigh')}
                              {weatherData.current.uv > 7 && weatherData.current.uv <= 10 && t('weather.uvVeryHigh')}
                              {weatherData.current.uv > 10 && t('weather.uvExtreme')}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 天气预报 */}
              <TabsContent value="forecast" className="space-y-4">
                {weatherData.forecast.map((day, index) => (
                  <Card key={index}>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          {getWeatherIcon(day.icon)}
                          <div>
                            <p className="font-medium">
                              {new Date(day.date).toLocaleDateString(undefined, { 
                                weekday: 'long', 
                                month: 'short', 
                                day: 'numeric' 
                              })}
                            </p>
                            <p className="text-sm text-muted-foreground">{day.condition}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-medium">
                            {convertTemp(day.maxTemp)}° / {convertTemp(day.minTemp)}°
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {t('weather.avgTemp')}: {convertTemp(day.avgTemp)}°
                          </p>
                        </div>
                      </div>
                      <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <CloudRain className="w-4 h-4 text-muted-foreground" />
                          <span>{day.chanceOfRain}%</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Droplets className="w-4 h-4 text-muted-foreground" />
                          <span>{day.humidity}%</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Wind className="w-4 h-4 text-muted-foreground" />
                          <span>{day.windSpeed} km/h</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              {/* 保存的位置 */}
              <TabsContent value="saved" className="space-y-4">
                {savedLocations.length === 0 ? (
                  <Card>
                    <CardContent className="pt-6 text-center text-muted-foreground">
                      {t('weather.noSavedLocations')}
                    </CardContent>
                  </Card>
                ) : (
                  savedLocations.map((loc) => (
                    <Card key={loc.id} className="cursor-pointer hover:bg-muted/50" onClick={() => loadSavedLocation(loc)}>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <MapPin className="w-5 h-5 text-muted-foreground" />
                            <div>
                              <p className="font-medium">{loc.name}</p>
                              <p className="text-sm text-muted-foreground">{loc.country}</p>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteLocation(loc.id)
                            }}
                          >
                            {t('common.delete')}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </TabsContent>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
