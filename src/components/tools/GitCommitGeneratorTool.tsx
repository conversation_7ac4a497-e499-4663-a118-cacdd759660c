'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, GitBranch, GitCommit, RefreshCw, Sparkles, FileText, Zap, History } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CommitTemplate {
  type: string
  scope?: string
  subject: string
  body?: string
  footer?: string
  breaking?: boolean
  closes?: string[]
}

const COMMIT_TYPES = [
  { value: 'feat', label: 'feat', description: '新功能' },
  { value: 'fix', label: 'fix', description: '修复 bug' },
  { value: 'docs', label: 'docs', description: '文档更新' },
  { value: 'style', label: 'style', description: '代码格式修改' },
  { value: 'refactor', label: 'refactor', description: '重构代码' },
  { value: 'perf', label: 'perf', description: '性能优化' },
  { value: 'test', label: 'test', description: '测试相关' },
  { value: 'chore', label: 'chore', description: '构建过程或辅助工具' },
  { value: 'ci', label: 'ci', description: 'CI/CD 相关' },
  { value: 'build', label: 'build', description: '构建系统' },
  { value: 'revert', label: 'revert', description: '回滚提交' },
]

const SAMPLE_COMMITS = [
  {
    type: 'feat',
    scope: 'auth',
    subject: '添加用户登录功能',
    body: '实现了用户登录验证逻辑，包括:\n- 用户名密码验证\n- JWT token 生成\n- 登录状态持久化',
    footer: '',
    breaking: false,
    closes: ['#123']
  },
  {
    type: 'fix',
    scope: 'api',
    subject: '修复用户信息获取异常',
    body: '修复了当用户 token 过期时获取用户信息失败的问题',
    footer: '',
    breaking: false,
    closes: ['#456']
  },
  {
    type: 'docs',
    scope: '',
    subject: '更新 README 文档',
    body: '添加了项目安装和使用说明',
    footer: '',
    breaking: false,
    closes: []
  },
  {
    type: 'refactor',
    scope: 'utils',
    subject: '重构日期处理工具类',
    body: '将日期处理逻辑从多个文件中抽取到统一的工具类中',
    footer: '',
    breaking: false,
    closes: []
  },
  {
    type: 'perf',
    scope: 'database',
    subject: '优化数据库查询性能',
    body: '添加了数据库索引，优化了慢查询',
    footer: '',
    breaking: false,
    closes: []
  }
]

const EMOJI_MAP: Record<string, string> = {
  feat: '✨',
  fix: '🐛',
  docs: '📚',
  style: '💄',
  refactor: '♻️',
  perf: '⚡',
  test: '✅',
  chore: '🔧',
  ci: '👷',
  build: '📦',
  revert: '⏪'
}

export default function GitCommitGeneratorTool() {
  const [template, setTemplate] = useState<CommitTemplate>({
    type: 'feat',
    scope: '',
    subject: '',
    body: '',
    footer: '',
    breaking: false,
    closes: []
  })
  const [commitMessage, setCommitMessage] = useState('')
  const [includeEmoji, setIncludeEmoji] = useState(false)
  const [useConventional, setUseConventional] = useState(true)
  const [activeTab, setActiveTab] = useState('builder')
  const [issueInput, setIssueInput] = useState('')
  const [history, setHistory] = useState<string[]>([])
  const { toast } = useToast()

  // 生成提交信息
  const generateCommitMessage = useCallback(() => {
    if (!template.subject.trim()) {
      toast({
        title: '请输入提交主题',
        description: '提交主题不能为空',
        variant: 'destructive',
      })
      return
    }

    let message = ''
    
    if (useConventional) {
      // Conventional Commits 格式
      const emoji = includeEmoji ? EMOJI_MAP[template.type] + ' ' : ''
      const scope = template.scope ? `(${template.scope})` : ''
      const breaking = template.breaking ? '!' : ''
      
      message = `${emoji}${template.type}${scope}${breaking}: ${template.subject}`
      
      if (template.body) {
        message += `\n\n${template.body}`
      }
      
      if (template.footer) {
        message += `\n\n${template.footer}`
      }
      
      if (template.breaking && template.footer) {
        message += `\n\nBREAKING CHANGE: ${template.footer}`
      }
      
      if (template.closes && template.closes.length > 0) {
        const closeLines = template.closes.map(issue => `Closes ${issue}`).join('\n')
        message += `\n\n${closeLines}`
      }
    } else {
      // 简单格式
      const emoji = includeEmoji ? EMOJI_MAP[template.type] + ' ' : ''
      message = `${emoji}${template.subject}`
      
      if (template.body) {
        message += `\n\n${template.body}`
      }
    }
    
    setCommitMessage(message)
    
    // 添加到历史记录
    setHistory(prev => [message, ...prev.slice(0, 9)]) // 保留最近10条
  }, [template, includeEmoji, useConventional, toast])

  // 复制提交信息
  const copyCommitMessage = useCallback(() => {
    if (!commitMessage) {
      toast({
        title: '请先生成提交信息',
        description: '点击生成按钮创建提交信息',
        variant: 'destructive',
      })
      return
    }

    navigator.clipboard.writeText(commitMessage).then(() => {
      toast({
        title: '复制成功',
        description: '提交信息已复制到剪贴板',
      })
    }).catch(() => {
      toast({
        title: '复制失败',
        description: '请手动选择并复制',
        variant: 'destructive',
      })
    })
  }, [commitMessage, toast])

  // 重置表单
  const resetForm = useCallback(() => {
    setTemplate({
      type: 'feat',
      scope: '',
      subject: '',
      body: '',
      footer: '',
      breaking: false,
      closes: []
    })
    setCommitMessage('')
    setIssueInput('')
  }, [])

  // 使用示例
  const useSample = useCallback((sample: CommitTemplate) => {
    setTemplate(sample)
    setActiveTab('builder')
  }, [])

  // 添加关闭的 issue
  const addIssue = useCallback(() => {
    if (!issueInput.trim()) return
    
    const issueNumber = issueInput.trim().startsWith('#') ? issueInput.trim() : `#${issueInput.trim()}`
    
    setTemplate(prev => ({
      ...prev,
      closes: [...(prev.closes || []), issueNumber]
    }))
    setIssueInput('')
  }, [issueInput])

  // 删除 issue
  const removeIssue = useCallback((index: number) => {
    setTemplate(prev => ({
      ...prev,
      closes: prev.closes?.filter((_, i) => i !== index) || []
    }))
  }, [])

  // 从历史记录中使用
  const useFromHistory = useCallback((message: string) => {
    setCommitMessage(message)
    setActiveTab('preview')
  }, [])

  return (
    <div className="w-full max-w-4xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitCommit className="h-5 w-5" />
            Git 提交信息生成器
          </CardTitle>
          <CardDescription>
            基于 Conventional Commits 规范生成标准化的 Git 提交信息
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="builder">构建器</TabsTrigger>
              <TabsTrigger value="preview">预览</TabsTrigger>
              <TabsTrigger value="samples">示例</TabsTrigger>
              <TabsTrigger value="history">历史</TabsTrigger>
            </TabsList>

            <TabsContent value="builder" className="space-y-6">
              {/* 基本设置 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>提交类型</Label>
                  <Select value={template.type} onValueChange={(value) => setTemplate(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {COMMIT_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            {includeEmoji && <span>{EMOJI_MAP[type.value]}</span>}
                            <span className="font-medium">{type.label}</span>
                            <span className="text-sm text-muted-foreground">- {type.description}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="scope">作用域 (可选)</Label>
                  <Input
                    id="scope"
                    value={template.scope}
                    onChange={(e) => setTemplate(prev => ({ ...prev, scope: e.target.value }))}
                    placeholder="如: auth, api, ui"
                  />
                </div>
              </div>

              {/* 提交主题 */}
              <div className="space-y-2">
                <Label htmlFor="subject">提交主题 *</Label>
                <Input
                  id="subject"
                  value={template.subject}
                  onChange={(e) => setTemplate(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="简短描述本次提交的内容"
                  className="text-base"
                />
              </div>

              {/* 提交正文 */}
              <div className="space-y-2">
                <Label htmlFor="body">提交正文 (可选)</Label>
                <Textarea
                  id="body"
                  value={template.body}
                  onChange={(e) => setTemplate(prev => ({ ...prev, body: e.target.value }))}
                  placeholder="详细描述本次提交的内容、原因和影响"
                  className="min-h-[100px]"
                />
              </div>

              {/* 页脚 */}
              <div className="space-y-2">
                <Label htmlFor="footer">页脚 (可选)</Label>
                <Textarea
                  id="footer"
                  value={template.footer}
                  onChange={(e) => setTemplate(prev => ({ ...prev, footer: e.target.value }))}
                  placeholder="其他信息，如重大变更说明"
                  className="min-h-[60px]"
                />
              </div>

              {/* 关闭的 Issue */}
              <div className="space-y-2">
                <Label>关闭的 Issue</Label>
                <div className="flex gap-2">
                  <Input
                    value={issueInput}
                    onChange={(e) => setIssueInput(e.target.value)}
                    placeholder="Issue 编号，如: 123"
                    onKeyPress={(e) => e.key === 'Enter' && addIssue()}
                  />
                  <Button onClick={addIssue} variant="outline">
                    添加
                  </Button>
                </div>
                {template.closes && template.closes.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {template.closes.map((issue, index) => (
                      <div key={index} className="flex items-center gap-1 bg-secondary px-2 py-1 rounded">
                        <span className="text-sm">{issue}</span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeIssue(index)}
                          className="h-4 w-4 p-0"
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 选项 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>选项</Label>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="conventional"
                      checked={useConventional}
                      onCheckedChange={setUseConventional}
                    />
                    <Label htmlFor="conventional">使用 Conventional Commits</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="emoji"
                      checked={includeEmoji}
                      onCheckedChange={setIncludeEmoji}
                    />
                    <Label htmlFor="emoji">包含 Emoji</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="breaking"
                      checked={template.breaking}
                      onCheckedChange={(checked) => setTemplate(prev => ({ ...prev, breaking: checked }))}
                    />
                    <Label htmlFor="breaking">重大变更</Label>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-2">
                <Button onClick={generateCommitMessage} className="flex-1">
                  <Zap className="h-4 w-4 mr-2" />
                  生成提交信息
                </Button>
                <Button onClick={resetForm} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重置
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              <div className="flex justify-between items-center">
                <Label>生成的提交信息</Label>
                <Button onClick={copyCommitMessage} variant="outline" size="sm">
                  <Copy className="h-4 w-4 mr-2" />
                  复制
                </Button>
              </div>
              <Card>
                <CardContent className="p-4">
                  <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-lg overflow-x-auto">
                    {commitMessage || '点击"生成提交信息"按钮来创建提交信息'}
                  </pre>
                </CardContent>
              </Card>
              
              {commitMessage && (
                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <GitBranch className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-600">使用方法</span>
                    </div>
                    <div className="text-sm text-blue-600 space-y-1">
                      <p>1. 复制上方的提交信息</p>
                      <p>2. 在终端中运行: <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">git commit -m &quot;粘贴提交信息&quot;</code></p>
                      <p>3. 或者使用 <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">git commit</code> 然后在编辑器中粘贴</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="samples" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Sparkles className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">提交信息示例</h3>
                </div>
                {SAMPLE_COMMITS.map((sample, index) => (
                  <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {includeEmoji && <span>{EMOJI_MAP[sample.type]}</span>}
                          <span className="font-medium text-blue-600">{sample.type}</span>
                          {sample.scope && <span className="text-sm text-muted-foreground">({sample.scope})</span>}
                        </div>
                        <Button
                          onClick={() => {
                            setTemplate(sample)
                            setActiveTab('builder')
                          }}
                          variant="outline"
                          size="sm"
                        >
                          使用此示例
                        </Button>
                      </div>
                      <div className="text-sm">
                        <p className="font-medium">{sample.subject}</p>
                        {sample.body && <p className="text-muted-foreground mt-1">{sample.body}</p>}
                        {sample.closes && sample.closes.length > 0 && (
                          <p className="text-green-600 mt-1">关闭: {sample.closes.join(', ')}</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <History className="h-5 w-5" />
                <h3 className="text-lg font-semibold">历史记录</h3>
              </div>
              {history.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center text-muted-foreground">
                    <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>暂无历史记录</p>
                    <p className="text-sm mt-2">生成的提交信息会自动保存到这里</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-2">
                  {history.map((message, index) => (
                    <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <pre className="whitespace-pre-wrap text-sm flex-1 mr-4">
                            {message}
                          </pre>
                          <Button
                            onClick={() => {
                              setCommitMessage(message)
                              setActiveTab('preview')
                            }}
                            variant="outline"
                            size="sm"
                          >
                            使用
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 使用提示 */}
      <Card className="bg-muted/50">
        <CardHeader>
          <CardTitle className="text-lg">使用提示</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
            <li>遵循 Conventional Commits 规范，让提交历史更清晰</li>
            <li>使用合适的提交类型，便于自动化工具处理</li>
            <li>提交主题应简洁明了，不超过 50 个字符</li>
            <li>提交正文用于详细说明变更的内容和原因</li>
            <li>重大变更应在页脚中说明影响和迁移方法</li>
            <li>可以关联相关的 Issue 编号</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
