'use client'

import React, { useState, useC<PERSON>back, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Copy, 
  Download, 
  Variable, 
  Plus, 
  Minus, 
  RotateCcw, 
  Play,
  Settings,
  Palette,
  Type,
  Ruler,
  Zap,
  Eye
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CssVariable {
  id: string
  name: string
  value: string
  type: 'color' | 'size' | 'font' | 'shadow' | 'custom'
  category: string
  description?: string
}

interface VariableCategory {
  name: string
  icon: React.ReactNode
  color: string
}

interface VariableTemplate {
  name: string
  description: string
  variables: Omit<CssVariable, 'id'>[]
}

const CssVariableGeneratorTool: React.FC = () => {
  const { toast } = useToast()

  const [variables, setVariables] = useState<CssVariable[]>([
    {
      id: '1',
      name: '--primary-color',
      value: '#3b82f6',
      type: 'color',
      category: 'colors',
      description: 'Primary brand color'
    },
    {
      id: '2',
      name: '--secondary-color',
      value: '#10b981',
      type: 'color',
      category: 'colors',
      description: 'Secondary accent color'
    },
    {
      id: '3',
      name: '--font-size-base',
      value: '16px',
      type: 'size',
      category: 'typography',
      description: 'Base font size'
    },
    {
      id: '4',
      name: '--spacing-unit',
      value: '8px',
      type: 'size',
      category: 'spacing',
      description: 'Base spacing unit'
    }
  ])

  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [previewMode, setPreviewMode] = useState<'css' | 'scss' | 'preview'>('css')
  const [includeFallbacks, setIncludeFallbacks] = useState(false)
  const [useScope, setUseScope] = useState(':root')

  const categories: VariableCategory[] = [
    { name: 'colors', icon: <Palette className="h-4 w-4" />, color: 'bg-red-100 text-red-700' },
    { name: 'typography', icon: <Type className="h-4 w-4" />, color: 'bg-blue-100 text-blue-700' },
    { name: 'spacing', icon: <Ruler className="h-4 w-4" />, color: 'bg-green-100 text-green-700' },
    { name: 'shadows', icon: <Settings className="h-4 w-4" />, color: 'bg-purple-100 text-purple-700' },
    { name: 'other', icon: <Variable className="h-4 w-4" />, color: 'bg-gray-100 text-gray-700' }
  ]

  const templates: VariableTemplate[] = [
    {
      name: '基础颜色系统',
      description: '现代Web应用的基础颜色变量',
      variables: [
        { name: '--color-primary', value: '#3b82f6', type: 'color', category: 'colors', description: '主色调' },
        { name: '--color-primary-dark', value: '#1d4ed8', type: 'color', category: 'colors', description: '主色调深色' },
        { name: '--color-primary-light', value: '#93c5fd', type: 'color', category: 'colors', description: '主色调浅色' },
        { name: '--color-secondary', value: '#10b981', type: 'color', category: 'colors', description: '次要颜色' },
        { name: '--color-success', value: '#22c55e', type: 'color', category: 'colors', description: '成功色' },
        { name: '--color-warning', value: '#f59e0b', type: 'color', category: 'colors', description: '警告色' },
        { name: '--color-error', value: '#ef4444', type: 'color', category: 'colors', description: '错误色' },
        { name: '--color-gray-50', value: '#f9fafb', type: 'color', category: 'colors', description: '灰色-最浅' },
        { name: '--color-gray-500', value: '#6b7280', type: 'color', category: 'colors', description: '灰色-中等' },
        { name: '--color-gray-900', value: '#111827', type: 'color', category: 'colors', description: '灰色-最深' }
      ]
    },
    {
      name: '字体排版系统',
      description: '完整的字体和排版变量系统',
      variables: [
        { name: '--font-family-sans', value: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif', type: 'font', category: 'typography', description: '无衬线字体' },
        { name: '--font-family-serif', value: 'Georgia, serif', type: 'font', category: 'typography', description: '衬线字体' },
        { name: '--font-family-mono', value: 'Monaco, "Courier New", monospace', type: 'font', category: 'typography', description: '等宽字体' },
        { name: '--font-size-xs', value: '12px', type: 'size', category: 'typography', description: '超小字号' },
        { name: '--font-size-sm', value: '14px', type: 'size', category: 'typography', description: '小字号' },
        { name: '--font-size-base', value: '16px', type: 'size', category: 'typography', description: '基础字号' },
        { name: '--font-size-lg', value: '18px', type: 'size', category: 'typography', description: '大字号' },
        { name: '--font-size-xl', value: '20px', type: 'size', category: 'typography', description: '超大字号' },
        { name: '--font-size-2xl', value: '24px', type: 'size', category: 'typography', description: '2倍大字号' },
        { name: '--line-height-tight', value: '1.25', type: 'size', category: 'typography', description: '紧密行高' },
        { name: '--line-height-normal', value: '1.5', type: 'size', category: 'typography', description: '正常行高' },
        { name: '--line-height-relaxed', value: '1.75', type: 'size', category: 'typography', description: '宽松行高' }
      ]
    },
    {
      name: '间距系统',
      description: '8px网格的间距变量系统',
      variables: [
        { name: '--spacing-0', value: '0', type: 'size', category: 'spacing', description: '无间距' },
        { name: '--spacing-1', value: '4px', type: 'size', category: 'spacing', description: '1单位间距' },
        { name: '--spacing-2', value: '8px', type: 'size', category: 'spacing', description: '2单位间距' },
        { name: '--spacing-3', value: '12px', type: 'size', category: 'spacing', description: '3单位间距' },
        { name: '--spacing-4', value: '16px', type: 'size', category: 'spacing', description: '4单位间距' },
        { name: '--spacing-5', value: '20px', type: 'size', category: 'spacing', description: '5单位间距' },
        { name: '--spacing-6', value: '24px', type: 'size', category: 'spacing', description: '6单位间距' },
        { name: '--spacing-8', value: '32px', type: 'size', category: 'spacing', description: '8单位间距' },
        { name: '--spacing-10', value: '40px', type: 'size', category: 'spacing', description: '10单位间距' },
        { name: '--spacing-12', value: '48px', type: 'size', category: 'spacing', description: '12单位间距' },
        { name: '--spacing-16', value: '64px', type: 'size', category: 'spacing', description: '16单位间距' },
        { name: '--spacing-20', value: '80px', type: 'size', category: 'spacing', description: '20单位间距' }
      ]
    },
    {
      name: '阴影和效果',
      description: '阴影和视觉效果变量',
      variables: [
        { name: '--shadow-sm', value: '0 1px 2px 0 rgba(0,0,0,0.05)', type: 'shadow', category: 'shadows', description: '小阴影' },
        { name: '--shadow-md', value: '0 4px 6px -1px rgba(0,0,0,0.1)', type: 'shadow', category: 'shadows', description: '中等阴影' },
        { name: '--shadow-lg', value: '0 10px 15px -3px rgba(0,0,0,0.1)', type: 'shadow', category: 'shadows', description: '大阴影' },
        { name: '--shadow-xl', value: '0 20px 25px -5px rgba(0,0,0,0.1)', type: 'shadow', category: 'shadows', description: '超大阴影' },
        { name: '--shadow-inner', value: 'inset 0 2px 4px 0 rgba(0,0,0,0.06)', type: 'shadow', category: 'shadows', description: '内阴影' },
        { name: '--border-radius-sm', value: '4px', type: 'size', category: 'shadows', description: '小圆角' },
        { name: '--border-radius-md', value: '8px', type: 'size', category: 'shadows', description: '中等圆角' },
        { name: '--border-radius-lg', value: '12px', type: 'size', category: 'shadows', description: '大圆角' },
        { name: '--border-radius-full', value: '9999px', type: 'size', category: 'shadows', description: '完全圆角' }
      ]
    }
  ]

  const addVariable = useCallback(() => {
    const newVariable: CssVariable = {
      id: String(Date.now()),
      name: '--new-variable',
      value: '#000000',
      type: 'custom',
      category: 'other',
      description: ''
    }
    setVariables(prev => [...prev, newVariable])
  }, [])

  const updateVariable = useCallback((id: string, updates: Partial<CssVariable>) => {
    setVariables(prev => prev.map(variable => 
      variable.id === id ? { ...variable, ...updates } : variable
    ))
  }, [])

  const removeVariable = useCallback((id: string) => {
    setVariables(prev => prev.filter(variable => variable.id !== id))
  }, [])

  const applyTemplate = useCallback((template: VariableTemplate) => {
    const newVariables = template.variables.map((variable, index) => ({
      ...variable,
      id: `template-${Date.now()}-${index}`
    }))
    setVariables(newVariables)
    toast({
      title: '模板已应用',
      description: `已成功应用"${template.name}"模板，包含${newVariables.length}个变量`
    })
  }, [toast])

  const resetVariables = useCallback(() => {
    setVariables([
      {
        id: '1',
        name: '--primary-color',
        value: '#3b82f6',
        type: 'color',
        category: 'colors',
        description: 'Primary brand color'
      },
      {
        id: '2',
        name: '--secondary-color',
        value: '#10b981',
        type: 'color',
        category: 'colors',
        description: 'Secondary accent color'
      },
      {
        id: '3',
        name: '--font-size-base',
        value: '16px',
        type: 'size',
        category: 'typography',
        description: 'Base font size'
      },
      {
        id: '4',
        name: '--spacing-unit',
        value: '8px',
        type: 'size',
        category: 'spacing',
        description: 'Base spacing unit'
      }
    ])
  }, [])

  const filteredVariables = useMemo(() => {
    return selectedCategory === 'all' 
      ? variables 
      : variables.filter(variable => variable.category === selectedCategory)
  }, [variables, selectedCategory])

  const generateCSS = useCallback(() => {
    let css = `${useScope} {\n`
    
    variables.forEach(variable => {
      if (variable.description) {
        css += `  /* ${variable.description} */\n`
      }
      css += `  ${variable.name}: ${variable.value};\n`
      if (variables.indexOf(variable) < variables.length - 1) {
        css += '\n'
      }
    })
    
    css += '}\n'

    if (includeFallbacks) {
      css += '\n/* 使用示例 (带回退值) */\n'
      css += '.example {\n'
      variables.slice(0, 3).forEach(variable => {
        css += `  ${variable.type === 'color' ? 'color' : 'font-size'}: var(${variable.name}, ${variable.value});\n`
      })
      css += '}\n'
    }

    return css
  }, [variables, useScope, includeFallbacks])

  const generateSCSS = useCallback(() => {
    let scss = '// SCSS 变量定义\n'
    
    const groupedVariables = variables.reduce((acc, variable) => {
      if (!acc[variable.category]) {
        acc[variable.category] = []
      }
      acc[variable.category].push(variable)
      return acc
    }, {} as Record<string, CssVariable[]>)

    Object.entries(groupedVariables).forEach(([category, vars]) => {
      scss += `\n// ${category.charAt(0).toUpperCase() + category.slice(1)}\n`
      vars.forEach(variable => {
        const scssVarName = variable.name.replace('--', '$').replace(/-([a-z])/g, (match, letter) => letter.toUpperCase())
        if (variable.description) {
          scss += `// ${variable.description}\n`
        }
        scss += `${scssVarName}: ${variable.value};\n`
      })
    })

    scss += '\n// CSS 自定义属性映射\n'
    scss += `${useScope} {\n`
    variables.forEach(variable => {
      const scssVarName = variable.name.replace('--', '$').replace(/-([a-z])/g, (match, letter) => letter.toUpperCase())
      scss += `  ${variable.name}: #{${scssVarName}};\n`
    })
    scss += '}\n'

    if (includeFallbacks) {
      scss += '\n// Mixin 示例\n'
      scss += '@mixin apply-theme-colors($primary: var(--primary-color), $secondary: var(--secondary-color)) {\n'
      scss += '  color: $primary;\n'
      scss += '  background-color: $secondary;\n'
      scss += '}\n'
    }

    return scss
  }, [variables, useScope, includeFallbacks])

  const copyCSS = useCallback(() => {
    const css = previewMode === 'scss' ? generateSCSS() : generateCSS()
    navigator.clipboard.writeText(css)
    toast({
      title: '已复制',
      description: `${previewMode === 'scss' ? 'SCSS' : 'CSS'}代码已复制到剪贴板`
    })
  }, [generateCSS, generateSCSS, previewMode, toast])

  const downloadFile = useCallback(() => {
    const css = previewMode === 'scss' ? generateSCSS() : generateCSS()
    const filename = previewMode === 'scss' ? 'variables.scss' : 'variables.css'
    
    const blob = new Blob([css], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: '下载完成',
      description: `${filename} 文件已下载`
    })
  }, [generateCSS, generateSCSS, previewMode, toast])

  const getVariablePreview = useCallback((variable: CssVariable) => {
    switch (variable.type) {
      case 'color':
        return (
          <div 
            className="w-6 h-6 rounded border border-gray-300"
            style={{ backgroundColor: variable.value }}
          />
        )
      case 'size':
        return (
          <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {variable.value}
          </div>
        )
      case 'shadow':
        return (
          <div 
            className="w-8 h-6 bg-white rounded border"
            style={{ boxShadow: variable.value }}
          />
        )
      case 'font':
        return (
          <div 
            className="text-xs text-gray-700"
            style={{ fontFamily: variable.value }}
          >
            Aa
          </div>
        )
      default:
        return (
          <div className="text-xs text-gray-400">
            {variable.value.substring(0, 10)}...
          </div>
        )
    }
  }, [])

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Variable className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">CSS 变量生成器</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          创建和管理CSS自定义属性(变量)，支持分类管理、模板导入、SCSS导出
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 配置面板 */}
        <div className="lg:col-span-1 space-y-6">
          <Tabs defaultValue="variables" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="variables">变量</TabsTrigger>
              <TabsTrigger value="templates">模板</TabsTrigger>
            </TabsList>

            <TabsContent value="variables" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      变量管理
                    </div>
                    <Button size="sm" onClick={addVariable}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 分类过滤 */}
                  <div>
                    <Label>分类筛选</Label>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        {categories.map(category => (
                          <SelectItem key={category.name} value={category.name}>
                            <div className="flex items-center gap-2">
                              {category.icon}
                              {category.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 变量列表 */}
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredVariables.map(variable => (
                      <div key={variable.id} className="p-3 border rounded-lg space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getVariablePreview(variable)}
                            <Badge className={categories.find(c => c.name === variable.category)?.color}>
                              {variable.category}
                            </Badge>
                          </div>
                          <Button 
                            size="sm" 
                            variant="destructive"
                            onClick={() => removeVariable(variable.id)}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <div className="space-y-2">
                          <div>
                            <Label className="text-xs">变量名</Label>
                            <Input
                              value={variable.name}
                              onChange={(e) => updateVariable(variable.id, { name: e.target.value })}
                              placeholder="--variable-name"
                            />
                          </div>
                          
                          <div>
                            <Label className="text-xs">值</Label>
                            <div className="flex gap-2">
                              <Input
                                value={variable.value}
                                onChange={(e) => updateVariable(variable.id, { value: e.target.value })}
                                placeholder="变量值"
                                className="flex-1"
                              />
                              {variable.type === 'color' && (
                                <Input
                                  type="color"
                                  value={variable.value}
                                  onChange={(e) => updateVariable(variable.id, { value: e.target.value })}
                                  className="w-12 p-1"
                                />
                              )}
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <Label className="text-xs">类型</Label>
                              <Select 
                                value={variable.type}
                                onValueChange={(value: CssVariable['type']) => updateVariable(variable.id, { type: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="color">颜色</SelectItem>
                                  <SelectItem value="size">尺寸</SelectItem>
                                  <SelectItem value="font">字体</SelectItem>
                                  <SelectItem value="shadow">阴影</SelectItem>
                                  <SelectItem value="custom">自定义</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            
                            <div>
                              <Label className="text-xs">分类</Label>
                              <Select 
                                value={variable.category}
                                onValueChange={(value) => updateVariable(variable.id, { category: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {categories.map(category => (
                                    <SelectItem key={category.name} value={category.name}>
                                      {category.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div>
                            <Label className="text-xs">描述 (可选)</Label>
                            <Input
                              value={variable.description || ''}
                              onChange={(e) => updateVariable(variable.id, { description: e.target.value })}
                              placeholder="变量用途描述"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="templates" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    预设模板
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {templates.map((template, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="font-medium">{template.name}</h4>
                          <p className="text-sm text-gray-500">{template.description}</p>
                        </div>
                        <Button 
                          size="sm" 
                          onClick={() => applyTemplate(template)}
                        >
                          应用
                        </Button>
                      </div>
                      <Badge variant="secondary">
                        {template.variables.length} 个变量
                      </Badge>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-sm">
                <Settings className="h-4 w-4" />
                导出设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>选择器作用域</Label>
                <Input
                  value={useScope}
                  onChange={(e) => setUseScope(e.target.value)}
                  placeholder=":root"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>包含使用示例</Label>
                <Switch 
                  checked={includeFallbacks}
                  onCheckedChange={setIncludeFallbacks}
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex gap-2">
            <Button variant="outline" onClick={resetVariables} className="flex-1">
              <RotateCcw className="h-4 w-4 mr-2" />
              重置
            </Button>
            <Button onClick={downloadFile} className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              下载
            </Button>
          </div>
        </div>

        {/* 预览和代码区域 */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Play className="h-5 w-5" />
                  代码生成
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant={previewMode === 'css' ? 'default' : 'outline'}
                    onClick={() => setPreviewMode('css')}
                  >
                    CSS
                  </Button>
                  <Button
                    size="sm"
                    variant={previewMode === 'scss' ? 'default' : 'outline'}
                    onClick={() => setPreviewMode('scss')}
                  >
                    SCSS
                  </Button>
                  <Button
                    size="sm"
                    variant={previewMode === 'preview' ? 'default' : 'outline'}
                    onClick={() => setPreviewMode('preview')}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    预览
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {previewMode === 'preview' ? (
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium mb-3">变量预览</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {variables.filter(v => v.type === 'color').slice(0, 6).map(variable => (
                        <div key={variable.id} className="flex items-center gap-3">
                          <div 
                            className="w-8 h-8 rounded border"
                            style={{ backgroundColor: variable.value }}
                          />
                          <div>
                            <div className="font-mono text-sm">{variable.name}</div>
                            <div className="text-xs text-gray-500">{variable.value}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg space-y-3">
                    <h4 className="font-medium">使用示例</h4>
                    <div 
                      className="p-4 rounded"
                      style={{
                        backgroundColor: variables.find(v => v.name.includes('primary'))?.value || '#3b82f6',
                        color: 'white'
                      }}
                    >
                      <h5 className="font-bold">主要内容区域</h5>
                      <p>使用 --primary-color 变量作为背景色</p>
                    </div>
                    <div 
                      className="p-4 rounded border"
                      style={{
                        fontSize: variables.find(v => v.name.includes('font-size'))?.value || '16px',
                        fontFamily: variables.find(v => v.type === 'font')?.value || 'sans-serif'
                      }}
                    >
                      这段文字使用了自定义的字体大小和字体族变量
                    </div>
                  </div>
                </div>
              ) : (
                <div className="relative">
                  <Textarea
                    value={previewMode === 'scss' ? generateSCSS() : generateCSS()}
                    readOnly
                    className="min-h-[500px] font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={copyCSS}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 变量统计 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Variable className="h-5 w-5" />
                变量统计
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="font-semibold text-blue-600">{variables.length}</div>
                  <div className="text-gray-600">总变量数</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="font-semibold text-red-600">
                    {variables.filter(v => v.type === 'color').length}
                  </div>
                  <div className="text-gray-600">颜色变量</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="font-semibold text-green-600">
                    {variables.filter(v => v.type === 'size').length}
                  </div>
                  <div className="text-gray-600">尺寸变量</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="font-semibold text-purple-600">
                    {variables.filter(v => v.category === 'typography').length}
                  </div>
                  <div className="text-gray-600">字体变量</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default CssVariableGeneratorTool