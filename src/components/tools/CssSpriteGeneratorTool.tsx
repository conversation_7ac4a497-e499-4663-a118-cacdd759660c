'use client'

import React, { useState, useRef, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Upload, 
  Download, 
  Trash2, 
  Grid, 
  Settings, 
  Copy, 
  Eye,
  FileImage,
  Code,
  Palette,
  Move,
  RotateCcw,
  Info
} from 'lucide-react'
import JSZip from 'jszip'

/**
 * 图片信息接口
 */
interface ImageInfo {
  id: string
  name: string
  file: File
  width: number
  height: number
  x: number
  y: number
  canvas?: HTMLCanvasElement
}

/**
 * 精灵图配置接口
 */
interface SpriteConfig {
  layout: 'horizontal' | 'vertical' | 'grid' | 'optimal'
  spacing: number
  backgroundColor: string
  format: 'png' | 'jpg' | 'webp'
  quality: number
  maxWidth: number
  maxHeight: number
  gridColumns: number
}

/**
 * CSS样式配置接口
 */
interface CssConfig {
  className: string
  useDataAttributes: boolean
  includeHover: boolean
  includeActive: boolean
  outputFormat: 'css' | 'scss' | 'less'
  includeRetina: boolean
}

/**
 * CSS精灵图生成器工具组件
 */
const CssSpriteGeneratorTool: React.FC = () => {
  // 状态管理
  const [images, setImages] = useState<ImageInfo[]>([])
  const [spriteConfig, setSpriteConfig] = useState<SpriteConfig>({
    layout: 'optimal',
    spacing: 2,
    backgroundColor: 'transparent',
    format: 'png',
    quality: 90,
    maxWidth: 2048,
    maxHeight: 2048,
    gridColumns: 4
  })
  const [cssConfig, setCssConfig] = useState<CssConfig>({
    className: 'sprite',
    useDataAttributes: false,
    includeHover: false,
    includeActive: false,
    outputFormat: 'css',
    includeRetina: false
  })
  const [spriteCanvas, setSpriteCanvas] = useState<HTMLCanvasElement | null>(null)
  const [generatedCss, setGeneratedCss] = useState<string>('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [previewMode, setPreviewMode] = useState<'sprite' | 'preview'>('sprite')

  // 引用
  const fileInputRef = useRef<HTMLInputElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  /**
   * 处理文件上传
   */
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const img = new Image()
          img.onload = () => {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            if (!ctx) return

            canvas.width = img.width
            canvas.height = img.height
            ctx.drawImage(img, 0, 0)

            const imageInfo: ImageInfo = {
              id: Math.random().toString(36).substr(2, 9),
              name: file.name.replace(/\.[^/.]+$/, ''),
              file,
              width: img.width,
              height: img.height,
              x: 0,
              y: 0,
              canvas
            }

            setImages(prev => [...prev, imageInfo])
          }
          img.src = e.target?.result as string
        }
        reader.readAsDataURL(file)
      }
    })

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  /**
   * 删除图片
   */
  const removeImage = useCallback((id: string) => {
    setImages(prev => prev.filter(img => img.id !== id))
  }, [])

  /**
   * 清空所有图片
   */
  const clearAllImages = useCallback(() => {
    setImages([])
    setSpriteCanvas(null)
    setGeneratedCss('')
  }, [])

  /**
   * 计算最优布局
   */
  const calculateOptimalLayout = useCallback((images: ImageInfo[]): ImageInfo[] => {
    if (images.length === 0) return []

    const { spacing, maxWidth } = spriteConfig

    // 简单的贪心算法
    const layout = [...images].sort((a, b) => (b.width * b.height) - (a.width * a.height))
    let currentX = 0
    let currentY = 0
    let rowHeight = 0

    for (let i = 0; i < layout.length; i++) {
      const img = layout[i]

      // 检查是否需要换行
      if (currentX + img.width > maxWidth && currentX > 0) {
        currentX = 0
        currentY += rowHeight + spacing
        rowHeight = 0
      }

      img.x = currentX
      img.y = currentY
      
      currentX += img.width + spacing
      rowHeight = Math.max(rowHeight, img.height)
    }

    return layout
  }, [spriteConfig])

  /**
   * 计算网格布局
   */
  const calculateGridLayout = useCallback((images: ImageInfo[]): ImageInfo[] => {
    const { spacing, gridColumns } = spriteConfig
    const layout = images.map(img => ({ ...img }))
    
    let currentX = 0
    let currentY = 0
    let maxRowHeight = 0
    
    for (let i = 0; i < layout.length; i++) {
      const img = layout[i]
      const col = i % gridColumns
      
      if (col === 0 && i > 0) {
        currentY += maxRowHeight + spacing
        currentX = 0
        maxRowHeight = 0
      }
      
      img.x = currentX
      img.y = currentY
      
      currentX += img.width + spacing
      maxRowHeight = Math.max(maxRowHeight, img.height)
    }
    
    return layout
  }, [spriteConfig])

  /**
   * 计算布局
   */
  const calculateLayout = useCallback((images: ImageInfo[]): ImageInfo[] => {
    const { layout, spacing } = spriteConfig

    switch (layout) {
      case 'horizontal':
        return images.map((img, index) => ({
          ...img,
          x: index === 0 ? 0 : images.slice(0, index).reduce((sum, prev) => sum + prev.width + spacing, 0),
          y: 0
        }))

      case 'vertical':
        return images.map((img, index) => ({
          ...img,
          x: 0,
          y: index === 0 ? 0 : images.slice(0, index).reduce((sum, prev) => sum + prev.height + spacing, 0)
        }))

      case 'grid':
        return calculateGridLayout(images)

      case 'optimal':
        return calculateOptimalLayout(images)

      default:
        return images
    }
  }, [spriteConfig, calculateGridLayout, calculateOptimalLayout])

  /**
   * 生成CSS代码
   */
  const generateCSS = useCallback((layoutImages: ImageInfo[], spriteWidth: number, spriteHeight: number): string => {
    const { className, useDataAttributes, includeHover, includeActive, outputFormat, includeRetina } = cssConfig
    
    let css = ''
    
    // 基础样式
    css += `/* CSS Sprite Generated by PoorLow Tools */\n`
    css += `.${className} {\n`
    css += `  background-image: url('sprite.${spriteConfig.format}');\n`
    css += `  background-repeat: no-repeat;\n`
    css += `  display: inline-block;\n`
    css += `}\n\n`
    
    // 各个图标的样式
    layoutImages.forEach(img => {
      const selector = useDataAttributes 
        ? `.${className}[data-icon="${img.name}"]`
        : `.${className}-${img.name}`
      
      css += `${selector} {\n`
      css += `  width: ${img.width}px;\n`
      css += `  height: ${img.height}px;\n`
      css += `  background-position: -${img.x}px -${img.y}px;\n`
      css += `}\n`
      
      // 悬停效果
      if (includeHover) {
        css += `${selector}:hover {\n`
        css += `  opacity: 0.8;\n`
        css += `}\n`
      }
      
      // 激活效果
      if (includeActive) {
        css += `${selector}:active {\n`
        css += `  opacity: 0.6;\n`
        css += `}\n`
      }
      
      css += `\n`
    })
    
    // Retina支持
    if (includeRetina) {
      css += `@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n`
      css += `  .${className} {\n`
      css += `    background-image: url('sprite@2x.${spriteConfig.format}');\n`
      css += `    background-size: ${spriteWidth}px ${spriteHeight}px;\n`
      css += `  }\n`
      css += `}\n`
    }
    
    return css
  }, [cssConfig, spriteConfig])

  /**
   * 生成HTML示例
   */
  const generateHTMLExample = useCallback((): string => {
    const { className, useDataAttributes } = cssConfig
    
    let html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Sprite 示例</title>
    <link rel="stylesheet" href="sprite.${cssConfig.outputFormat}">
</head>
<body>
    <h1>CSS Sprite 示例</h1>
    <div class="sprite-demo">
`

    images.forEach(img => {
      if (useDataAttributes) {
        html += `        <i class="${className}" data-icon="${img.name}" title="${img.name}"></i>\n`
      } else {
        html += `        <i class="${className}-${img.name}" title="${img.name}"></i>\n`
      }
    })

    html += `    </div>
</body>
</html>`

    return html
  }, [cssConfig, images])

  /**
   * 生成精灵图
   */
  const generateSprite = useCallback(async () => {
    if (images.length === 0) return

    setIsGenerating(true)

    try {
      const layoutImages = calculateLayout(images)
      const { backgroundColor } = spriteConfig

      // 计算画布尺寸
      const maxX = Math.max(...layoutImages.map(img => img.x + img.width))
      const maxY = Math.max(...layoutImages.map(img => img.y + img.height))

      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) return

      canvas.width = maxX
      canvas.height = maxY

      // 设置背景色
      if (backgroundColor !== 'transparent') {
        ctx.fillStyle = backgroundColor
        ctx.fillRect(0, 0, canvas.width, canvas.height)
      }

      // 绘制所有图片
      for (const img of layoutImages) {
        if (img.canvas) {
          ctx.drawImage(img.canvas, img.x, img.y)
        }
      }

      setSpriteCanvas(canvas)

      // 生成CSS
      const css = generateCSS(layoutImages, canvas.width, canvas.height)
      setGeneratedCss(css)

      // 更新预览画布
      if (canvasRef.current) {
        const previewCtx = canvasRef.current.getContext('2d')
        if (previewCtx) {
          canvasRef.current.width = Math.min(canvas.width, 800)
          canvasRef.current.height = Math.min(canvas.height, 600)
          
          const scale = Math.min(
            canvasRef.current.width / canvas.width,
            canvasRef.current.height / canvas.height
          )
          
          previewCtx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height)
          previewCtx.save()
          previewCtx.scale(scale, scale)
          previewCtx.drawImage(canvas, 0, 0)
          previewCtx.restore()
        }
      }

    } catch (error) {
      console.error('生成精灵图失败:', error)
    } finally {
      setIsGenerating(false)
    }
  }, [images, spriteConfig, calculateLayout, generateCSS])

  /**
   * 下载精灵图
   */
  const downloadSprite = useCallback(() => {
    if (!spriteCanvas) return

    const link = document.createElement('a')
    link.download = `sprite.${spriteConfig.format}`
    link.href = spriteCanvas.toDataURL(`image/${spriteConfig.format}`, spriteConfig.quality / 100)
    link.click()
  }, [spriteCanvas, spriteConfig])

  /**
   * 下载CSS文件
   */
  const downloadCSS = useCallback(() => {
    if (!generatedCss) return

    const blob = new Blob([generatedCss], { type: 'text/css' })
    const link = document.createElement('a')
    link.download = `sprite.${cssConfig.outputFormat}`
    link.href = URL.createObjectURL(blob)
    link.click()
    URL.revokeObjectURL(link.href)
  }, [generatedCss, cssConfig])

  /**
   * 下载完整包
   */
  const downloadPackage = useCallback(async () => {
    if (!spriteCanvas || !generatedCss) return

    const zip = new JSZip()
    
    // 添加精灵图
    const spriteBlob = await new Promise<Blob>((resolve) => {
      spriteCanvas.toBlob((blob) => {
        resolve(blob!)
      }, `image/${spriteConfig.format}`, spriteConfig.quality / 100)
    })
    zip.file(`sprite.${spriteConfig.format}`, spriteBlob)
    
    // 添加CSS文件
    zip.file(`sprite.${cssConfig.outputFormat}`, generatedCss)
    
    // 添加HTML示例
    const htmlExample = generateHTMLExample()
    zip.file('example.html', htmlExample)
    
    // 生成并下载ZIP
    const zipBlob = await zip.generateAsync({ type: 'blob' })
    const link = document.createElement('a')
    link.download = 'css-sprite-package.zip'
    link.href = URL.createObjectURL(zipBlob)
    link.click()
    URL.revokeObjectURL(link.href)
  }, [spriteCanvas, generatedCss, spriteConfig, cssConfig, generateHTMLExample])

  /**
   * 复制CSS到剪贴板
   */
  const copyCssToClipboard = useCallback(async () => {
    if (!generatedCss) return
    
    try {
      await navigator.clipboard.writeText(generatedCss)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }, [generatedCss])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Grid className="h-6 w-6" />
            CSS精灵图生成器
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="upload">上传图片</TabsTrigger>
              <TabsTrigger value="config">配置设置</TabsTrigger>
              <TabsTrigger value="preview">预览结果</TabsTrigger>
              <TabsTrigger value="export">导出下载</TabsTrigger>
            </TabsList>

            {/* 上传图片 */}
            <TabsContent value="upload" className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <FileImage className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-lg font-medium mb-2">选择图片文件</p>
                <p className="text-gray-500 mb-4">支持 PNG、JPG、GIF、WebP 等格式</p>
                <Button onClick={() => fileInputRef.current?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  选择文件
                </Button>
              </div>

              {images.length > 0 && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">已上传图片 ({images.length})</h3>
                    <Button variant="outline" size="sm" onClick={clearAllImages}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      清空全部
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {images.map((img) => (
                      <div key={img.id} className="relative border rounded-lg p-2">
                        <div className="aspect-square bg-gray-100 rounded mb-2 flex items-center justify-center">
                          {img.canvas && (
                            <img
                              src={img.canvas.toDataURL()}
                              alt={img.name}
                              className="max-w-full max-h-full object-contain"
                            />
                          )}
                        </div>
                        <p className="text-sm font-medium truncate">{img.name}</p>
                        <p className="text-xs text-gray-500">{img.width} × {img.height}</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute top-1 right-1"
                          onClick={() => removeImage(img.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </TabsContent>

            {/* 配置设置 */}
            <TabsContent value="config" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 精灵图配置 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      精灵图配置
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="layout">布局方式</Label>
                      <Select
                        value={spriteConfig.layout}
                        onValueChange={(value: any) => setSpriteConfig(prev => ({ ...prev, layout: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="optimal">智能优化</SelectItem>
                          <SelectItem value="horizontal">水平排列</SelectItem>
                          <SelectItem value="vertical">垂直排列</SelectItem>
                          <SelectItem value="grid">网格布局</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="spacing">图片间距 (px)</Label>
                      <Input
                        id="spacing"
                        type="number"
                        min="0"
                        max="50"
                        value={spriteConfig.spacing}
                        onChange={(e) => setSpriteConfig(prev => ({ ...prev, spacing: parseInt(e.target.value) || 0 }))}
                      />
                    </div>

                    {spriteConfig.layout === 'grid' && (
                      <div>
                        <Label htmlFor="gridColumns">网格列数</Label>
                        <Input
                          id="gridColumns"
                          type="number"
                          min="1"
                          max="10"
                          value={spriteConfig.gridColumns}
                          onChange={(e) => setSpriteConfig(prev => ({ ...prev, gridColumns: parseInt(e.target.value) || 4 }))}
                        />
                      </div>
                    )}

                    <div>
                      <Label htmlFor="backgroundColor">背景颜色</Label>
                      <div className="flex gap-2">
                        <Input
                          id="backgroundColor"
                          value={spriteConfig.backgroundColor}
                          onChange={(e) => setSpriteConfig(prev => ({ ...prev, backgroundColor: e.target.value }))}
                          placeholder="transparent 或 #ffffff"
                        />
                        <input
                          type="color"
                          value={spriteConfig.backgroundColor === 'transparent' ? '#ffffff' : spriteConfig.backgroundColor}
                          onChange={(e) => setSpriteConfig(prev => ({ ...prev, backgroundColor: e.target.value }))}
                          className="w-12 h-10 border rounded"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="format">输出格式</Label>
                      <Select
                        value={spriteConfig.format}
                        onValueChange={(value: any) => setSpriteConfig(prev => ({ ...prev, format: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="png">PNG</SelectItem>
                          <SelectItem value="jpg">JPG</SelectItem>
                          <SelectItem value="webp">WebP</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {spriteConfig.format !== 'png' && (
                      <div>
                        <Label htmlFor="quality">图片质量 (%)</Label>
                        <Input
                          id="quality"
                          type="number"
                          min="1"
                          max="100"
                          value={spriteConfig.quality}
                          onChange={(e) => setSpriteConfig(prev => ({ ...prev, quality: parseInt(e.target.value) || 90 }))}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* CSS配置 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Code className="h-5 w-5" />
                      CSS配置
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="className">CSS类名</Label>
                      <Input
                        id="className"
                        value={cssConfig.className}
                        onChange={(e) => setCssConfig(prev => ({ ...prev, className: e.target.value }))}
                        placeholder="sprite"
                      />
                    </div>

                    <div>
                      <Label htmlFor="outputFormat">输出格式</Label>
                      <Select
                        value={cssConfig.outputFormat}
                        onValueChange={(value: any) => setCssConfig(prev => ({ ...prev, outputFormat: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="css">CSS</SelectItem>
                          <SelectItem value="scss">SCSS</SelectItem>
                          <SelectItem value="less">Less</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="useDataAttributes"
                          checked={cssConfig.useDataAttributes}
                          onChange={(e) => setCssConfig(prev => ({ ...prev, useDataAttributes: e.target.checked }))}
                        />
                        <Label htmlFor="useDataAttributes">使用 data 属性</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="includeHover"
                          checked={cssConfig.includeHover}
                          onChange={(e) => setCssConfig(prev => ({ ...prev, includeHover: e.target.checked }))}
                        />
                        <Label htmlFor="includeHover">包含悬停效果</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="includeActive"
                          checked={cssConfig.includeActive}
                          onChange={(e) => setCssConfig(prev => ({ ...prev, includeActive: e.target.checked }))}
                        />
                        <Label htmlFor="includeActive">包含激活效果</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="includeRetina"
                          checked={cssConfig.includeRetina}
                          onChange={(e) => setCssConfig(prev => ({ ...prev, includeRetina: e.target.checked }))}
                        />
                        <Label htmlFor="includeRetina">Retina 支持</Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex justify-center">
                <Button
                  onClick={generateSprite}
                  disabled={images.length === 0 || isGenerating}
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <RotateCcw className="h-4 w-4 mr-2 animate-spin" />
                      生成中...
                    </>
                  ) : (
                    <>
                      <Palette className="h-4 w-4 mr-2" />
                      生成精灵图
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            {/* 预览结果 */}
            <TabsContent value="preview" className="space-y-4">
              {spriteCanvas ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <h3 className="text-lg font-medium">预览结果</h3>
                    <div className="flex gap-2">
                      <Button
                        variant={previewMode === 'sprite' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setPreviewMode('sprite')}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        精灵图
                      </Button>
                      <Button
                        variant={previewMode === 'preview' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setPreviewMode('preview')}
                      >
                        <Code className="h-4 w-4 mr-2" />
                        CSS代码
                      </Button>
                    </div>
                  </div>

                  {previewMode === 'sprite' && (
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <canvas
                        ref={canvasRef}
                        className="max-w-full border rounded"
                        style={{ 
                          background: spriteConfig.backgroundColor === 'transparent' 
                            ? 'url("data:image/svg+xml,%3csvg width=\'20\' height=\'20\' xmlns=\'http://www.w3.org/2000/svg\'%3e%3cg fill=\'%23f0f0f0\'%3e%3crect width=\'10\' height=\'10\'/%3e%3crect x=\'10\' y=\'10\' width=\'10\' height=\'10\'/%3e%3c/g%3e%3c/svg%3e")'
                            : spriteConfig.backgroundColor
                        }}
                      />
                      <div className="mt-2 text-sm text-gray-600">
                        尺寸: {spriteCanvas.width} × {spriteCanvas.height} px
                      </div>
                    </div>
                  )}

                  {previewMode === 'preview' && generatedCss && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">生成的CSS代码</h4>
                          <Button variant="outline" size="sm" onClick={copyCssToClipboard}>
                            <Copy className="h-4 w-4 mr-2" />
                            复制代码
                          </Button>
                        </div>
                        
                        <Textarea
                          value={generatedCss}
                          readOnly
                          className="h-64 font-mono text-sm"
                          placeholder="生成的CSS代码将显示在这里..."
                        />
                      </div>
                    )}
                  </div>
                ) : (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      请先上传图片并生成精灵图，然后查看预览结果。
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>

              {/* 导出下载 */}
              <TabsContent value="export" className="space-y-4">
                {spriteCanvas && generatedCss ? (
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">下载选项</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4 text-center">
                          <FileImage className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                          <h4 className="font-medium mb-2">精灵图</h4>
                          <p className="text-sm text-gray-600 mb-4">
                            下载生成的精灵图文件
                          </p>
                          <Button onClick={downloadSprite} className="w-full">
                            <Download className="h-4 w-4 mr-2" />
                            下载图片
                          </Button>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4 text-center">
                          <Code className="h-8 w-8 mx-auto mb-2 text-green-500" />
                          <h4 className="font-medium mb-2">CSS文件</h4>
                          <p className="text-sm text-gray-600 mb-4">
                            下载生成的CSS样式文件
                          </p>
                          <Button onClick={downloadCSS} className="w-full">
                            <Download className="h-4 w-4 mr-2" />
                            下载CSS
                          </Button>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-4 text-center">
                          <Grid className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                          <h4 className="font-medium mb-2">完整包</h4>
                          <p className="text-sm text-gray-600 mb-4">
                            下载包含图片、CSS和示例的ZIP包
                          </p>
                          <Button onClick={downloadPackage} className="w-full">
                            <Download className="h-4 w-4 mr-2" />
                            下载ZIP包
                          </Button>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="border rounded-lg p-4 bg-gray-50">
                      <h4 className="font-medium mb-2">使用说明</h4>
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>1. 将精灵图文件上传到您的服务器</p>
                        <p>2. 在HTML中引入CSS文件</p>
                        <p>3. 使用对应的CSS类名显示图标</p>
                        <p>4. 参考示例HTML文件了解具体用法</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      请先生成精灵图后再进行下载操作。
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    )
  }

  export default CssSpriteGeneratorTool
