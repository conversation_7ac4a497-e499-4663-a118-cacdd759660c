'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  Download, 
  FileImage, 
  Hash, 
  Calendar, 
  Type, 
  RotateCcw,
  Eye,
  EyeOff,
  Copy,
  Check,
  X,
  Settings,
  Shuffle,
  ArrowUpDown
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import JSZip from 'jszip'

/**
 * 图片批量重命名工具组件
 * 提供智能批量重命名功能，支持多种命名规则和预览
 */
export default function ImageBatchRenamerTool() {
  const { toast } = useToast()
  
  // 文件状态
  const [files, setFiles] = useState<File[]>([])
  const [previewVisible, setPreviewVisible] = useState(true)
  
  // 重命名配置
  const [namingPattern, setNamingPattern] = useState('custom')
  const [customPattern, setCustomPattern] = useState('IMG_{index}')
  const [startIndex, setStartIndex] = useState(1)
  const [indexPadding, setIndexPadding] = useState(3)
  const [prefix, setPrefix] = useState('')
  const [suffix, setSuffix] = useState('')
  const [dateFormat, setDateFormat] = useState('YYYY-MM-DD')
  const [caseStyle, setCaseStyle] = useState('original')
  const [separator, setSeparator] = useState('_')
  
  // 高级选项
  const [removeSpaces, setRemoveSpaces] = useState(true)
  const [removeSpecialChars, setRemoveSpecialChars] = useState(true)
  const [preserveExtension, setPreserveExtension] = useState(true)
  const [conflictResolution, setConflictResolution] = useState('append_number')
  
  // 预览状态
  const [renamedFiles, setRenamedFiles] = useState<Array<{
    original: File
    newName: string
    hasConflict: boolean
  }>>([])
  
  // 复制状态
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null)

  /**
   * 处理文件上传
   */
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = Array.from(event.target.files || [])
    const imageFiles = uploadedFiles.filter(file => file.type.startsWith('image/'))
    
    if (imageFiles.length !== uploadedFiles.length) {
      toast({
        title: "文件过滤",
        description: `已过滤掉 ${uploadedFiles.length - imageFiles.length} 个非图片文件`,
        variant: "default"
      })
    }
    
    setFiles(imageFiles)
    toast({
      title: "文件上传成功",
      description: `已上传 ${imageFiles.length} 个图片文件`,
      variant: "default"
    })
  }, [toast])

  /**
   * 拖拽处理
   */
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    const droppedFiles = Array.from(event.dataTransfer.files)
    const imageFiles = droppedFiles.filter(file => file.type.startsWith('image/'))
    
    setFiles(prev => [...prev, ...imageFiles])
    toast({
      title: "文件添加成功",
      description: `已添加 ${imageFiles.length} 个图片文件`,
      variant: "default"
    })
  }, [toast])

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
  }, [])

  /**
   * 格式化文件名
   */
  const formatFileName = useCallback((file: File, index: number): string => {
    const fileExtension = file.name.split('.').pop() || ''
    const baseName = file.name.replace(/\.[^/.]+$/, '')
    const currentDate = new Date()
    
    let newName = ''
    
    // 根据命名模式生成名称
    switch (namingPattern) {
      case 'custom':
        newName = customPattern
          .replace(/{index}/g, (startIndex + index).toString().padStart(indexPadding, '0'))
          .replace(/{original}/g, baseName)
          .replace(/{date}/g, formatDate(currentDate, dateFormat))
          .replace(/{time}/g, formatTime(currentDate))
          .replace(/{size}/g, formatFileSize(file.size))
          .replace(/{random}/g, Math.random().toString(36).substr(2, 6))
        break
      
      case 'sequential':
        newName = `${prefix}${(startIndex + index).toString().padStart(indexPadding, '0')}${suffix}`
        break
      
      case 'date_based':
        newName = `${prefix}${formatDate(currentDate, dateFormat)}${separator}${(index + 1).toString().padStart(indexPadding, '0')}${suffix}`
        break
      
      case 'original_clean':
        newName = cleanFileName(baseName)
        break
      
      case 'hash_based':
        newName = `${prefix}${generateHash(file.name + file.size)}${suffix}`
        break
      
      default:
        newName = baseName
    }
    
    // 应用大小写样式
    newName = applyCaseStyle(newName, caseStyle)
    
    // 清理文件名
    if (removeSpaces) {
      newName = newName.replace(/\s+/g, separator)
    }
    
    if (removeSpecialChars) {
      newName = newName.replace(/[^\w\-_.]/g, '')
    }
    
    // 添加扩展名
    if (preserveExtension && fileExtension) {
      newName += `.${fileExtension}`
    }
    
    return newName
  }, [
    namingPattern, customPattern, startIndex, indexPadding, prefix, suffix,
    dateFormat, caseStyle, separator, removeSpaces, removeSpecialChars, preserveExtension
  ])

  /**
   * 格式化日期
   */
  const formatDate = (date: Date, format: string): string => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    
    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
  }

  /**
   * 格式化时间
   */
  const formatTime = (date: Date): string => {
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    
    return `${hours}${minutes}${seconds}`
  }

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes}B`
    if (bytes < 1024 * 1024) return `${Math.round(bytes / 1024)}KB`
    return `${Math.round(bytes / (1024 * 1024))}MB`
  }

  /**
   * 清理文件名
   */
  const cleanFileName = (name: string): string => {
    return name
      .replace(/[^\w\s\-_.]/g, '')
      .replace(/\s+/g, separator)
      .trim()
  }

  /**
   * 应用大小写样式
   */
  const applyCaseStyle = (text: string, style: string): string => {
    switch (style) {
      case 'lowercase':
        return text.toLowerCase()
      case 'uppercase':
        return text.toUpperCase()
      case 'capitalize':
        return text.replace(/\b\w/g, l => l.toUpperCase())
      case 'camelCase':
        return text.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '')
      default:
        return text
    }
  }

  /**
   * 生成哈希
   */
  const generateHash = (input: string): string => {
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString(36).substr(0, 8)
  }

  /**
   * 检测命名冲突
   */
  const detectConflicts = useCallback((renamedList: Array<{ original: File; newName: string; hasConflict: boolean }>) => {
    const nameCount = new Map<string, number>()
    
    return renamedList.map(item => {
      const count = nameCount.get(item.newName) || 0
      nameCount.set(item.newName, count + 1)
      
      return {
        ...item,
        hasConflict: count > 0
      }
    })
  }, [])

  /**
   * 解决命名冲突
   */
  const resolveConflicts = useCallback((renamedList: Array<{ original: File; newName: string; hasConflict: boolean }>) => {
    const nameCount = new Map<string, number>()
    
    return renamedList.map(item => {
      if (!item.hasConflict) return item
      
      const baseName = item.newName.replace(/\.[^/.]+$/, '')
      const extension = item.newName.split('.').pop()
      const count = nameCount.get(baseName) || 0
      nameCount.set(baseName, count + 1)
      
      let resolvedName = item.newName
      
      switch (conflictResolution) {
        case 'append_number':
          resolvedName = `${baseName}_${count + 1}${extension ? `.${extension}` : ''}`
          break
        case 'prepend_number':
          resolvedName = `${count + 1}_${item.newName}`
          break
        case 'timestamp':
          resolvedName = `${baseName}_${Date.now()}${extension ? `.${extension}` : ''}`
          break
      }
      
      return {
        ...item,
        newName: resolvedName,
        hasConflict: false
      }
    })
  }, [conflictResolution])

  /**
   * 生成预览
   */
  const generatePreview = useCallback(() => {
    if (files.length === 0) return
    
    const renamed = files.map((file, index) => ({
      original: file,
      newName: formatFileName(file, index),
      hasConflict: false
    }))
    
    const withConflicts = detectConflicts(renamed)
    const resolved = resolveConflicts(withConflicts)
    
    setRenamedFiles(resolved)
  }, [files, formatFileName, detectConflicts, resolveConflicts])

  // 自动生成预览
  React.useEffect(() => {
    generatePreview()
  }, [generatePreview])

  /**
   * 复制文件名
   */
  const copyFileName = useCallback(async (fileName: string, index: number) => {
    try {
      await navigator.clipboard.writeText(fileName)
      setCopiedIndex(index)
      setTimeout(() => setCopiedIndex(null), 2000)
      
      toast({
        title: "复制成功",
        description: "文件名已复制到剪贴板",
        variant: "default"
      })
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive"
      })
    }
  }, [toast])

  /**
   * 下载重命名文件
   */
  const downloadRenamedFiles = useCallback(async () => {
    if (renamedFiles.length === 0) return
    
    try {
      const zip = new JSZip()
      
      for (const item of renamedFiles) {
        const arrayBuffer = await item.original.arrayBuffer()
        zip.file(item.newName, arrayBuffer)
      }
      
      const content = await zip.generateAsync({ type: 'blob' })
      const url = URL.createObjectURL(content)
      const link = document.createElement('a')
      link.href = url
      link.download = 'renamed_images.zip'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: "下载成功",
        description: "重命名后的文件已打包下载",
        variant: "default"
      })
    } catch (error) {
      toast({
        title: "下载失败",
        description: "文件打包过程中出现错误",
        variant: "destructive"
      })
    }
  }, [renamedFiles, toast])

  /**
   * 清空文件
   */
  const clearFiles = useCallback(() => {
    setFiles([])
    setRenamedFiles([])
  }, [])

  /**
   * 随机排序文件
   */
  const shuffleFiles = useCallback(() => {
    const shuffled = [...files].sort(() => Math.random() - 0.5)
    setFiles(shuffled)
  }, [files])

  /**
   * 按名称排序文件
   */
  const sortFilesByName = useCallback(() => {
    const sorted = [...files].sort((a, b) => a.name.localeCompare(b.name))
    setFiles(sorted)
  }, [files])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileImage className="h-6 w-6" />
            图片批量重命名工具
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="upload" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="upload">文件上传</TabsTrigger>
              <TabsTrigger value="settings">重命名设置</TabsTrigger>
              <TabsTrigger value="preview">预览与下载</TabsTrigger>
            </TabsList>

            {/* 文件上传 */}
            <TabsContent value="upload" className="space-y-4">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-lg font-medium mb-2">拖拽图片文件到此处</p>
                <p className="text-gray-500 mb-4">或点击下方按钮选择文件</p>
                <Input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="file-upload"
                />
                <Label htmlFor="file-upload">
                  <Button variant="outline" className="cursor-pointer">
                    选择图片文件
                  </Button>
                </Label>
              </div>

              {files.length > 0 && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{files.length} 个文件</Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={shuffleFiles}
                        className="flex items-center gap-1"
                      >
                        <Shuffle className="h-4 w-4" />
                        随机排序
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={sortFilesByName}
                        className="flex items-center gap-1"
                      >
                        <ArrowUpDown className="h-4 w-4" />
                        按名称排序
                      </Button>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearFiles}
                      className="flex items-center gap-1"
                    >
                      <X className="h-4 w-4" />
                      清空
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                    {files.map((file, index) => (
                      <div key={index} className="border rounded-lg p-3 space-y-2">
                        <div className="font-medium truncate" title={file.name}>
                          {file.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatFileSize(file.size)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </TabsContent>

            {/* 重命名设置 */}
            <TabsContent value="settings" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 命名模式 */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">命名模式</Label>
                  <Select value={namingPattern} onValueChange={setNamingPattern}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="custom">自定义模式</SelectItem>
                      <SelectItem value="sequential">序号命名</SelectItem>
                      <SelectItem value="date_based">日期命名</SelectItem>
                      <SelectItem value="original_clean">原名清理</SelectItem>
                      <SelectItem value="hash_based">哈希命名</SelectItem>
                    </SelectContent>
                  </Select>

                  {namingPattern === 'custom' && (
                    <div className="space-y-2">
                      <Label>自定义模式</Label>
                      <Input
                        value={customPattern}
                        onChange={(e) => setCustomPattern(e.target.value)}
                        placeholder="IMG_{index}"
                      />
                      <div className="text-sm text-gray-500">
                        可用变量：{'{index}'}, {'{original}'}, {'{date}'}, {'{time}'}, {'{size}'}, {'{random}'}
                      </div>
                    </div>
                  )}
                </div>

                {/* 基础设置 */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">基础设置</Label>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>起始序号</Label>
                      <Input
                        type="number"
                        value={startIndex}
                        onChange={(e) => setStartIndex(parseInt(e.target.value) || 1)}
                        min="0"
                      />
                    </div>
                    <div>
                      <Label>序号位数</Label>
                      <Input
                        type="number"
                        value={indexPadding}
                        onChange={(e) => setIndexPadding(parseInt(e.target.value) || 1)}
                        min="1"
                        max="10"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>前缀</Label>
                      <Input
                        value={prefix}
                        onChange={(e) => setPrefix(e.target.value)}
                        placeholder="可选前缀"
                      />
                    </div>
                    <div>
                      <Label>后缀</Label>
                      <Input
                        value={suffix}
                        onChange={(e) => setSuffix(e.target.value)}
                        placeholder="可选后缀"
                      />
                    </div>
                  </div>
                </div>

                {/* 格式设置 */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">格式设置</Label>
                  
                  <div>
                    <Label>日期格式</Label>
                    <Select value={dateFormat} onValueChange={setDateFormat}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                        <SelectItem value="YYYYMMDD">YYYYMMDD</SelectItem>
                        <SelectItem value="DD-MM-YYYY">DD-MM-YYYY</SelectItem>
                        <SelectItem value="MM-DD-YYYY">MM-DD-YYYY</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>大小写样式</Label>
                    <Select value={caseStyle} onValueChange={setCaseStyle}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="original">保持原样</SelectItem>
                        <SelectItem value="lowercase">小写</SelectItem>
                        <SelectItem value="uppercase">大写</SelectItem>
                        <SelectItem value="capitalize">首字母大写</SelectItem>
                        <SelectItem value="camelCase">驼峰命名</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>分隔符</Label>
                    <Select value={separator} onValueChange={setSeparator}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_">下划线 (_)</SelectItem>
                        <SelectItem value="-">连字符 (-)</SelectItem>
                        <SelectItem value=".">点号 (.)</SelectItem>
                        <SelectItem value="">无分隔符</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* 高级选项 */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">高级选项</Label>
                  
                  <div className="space-y-3">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={removeSpaces}
                        onChange={(e) => setRemoveSpaces(e.target.checked)}
                        className="rounded"
                      />
                      <span>移除空格</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={removeSpecialChars}
                        onChange={(e) => setRemoveSpecialChars(e.target.checked)}
                        className="rounded"
                      />
                      <span>移除特殊字符</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={preserveExtension}
                        onChange={(e) => setPreserveExtension(e.target.checked)}
                        className="rounded"
                      />
                      <span>保留文件扩展名</span>
                    </label>
                  </div>

                  <div>
                    <Label>冲突解决方式</Label>
                    <Select value={conflictResolution} onValueChange={setConflictResolution}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="append_number">追加数字</SelectItem>
                        <SelectItem value="prepend_number">前置数字</SelectItem>
                        <SelectItem value="timestamp">添加时间戳</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* 预览与下载 */}
            <TabsContent value="preview" className="space-y-4">
              {renamedFiles.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{renamedFiles.length} 个文件</Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPreviewVisible(!previewVisible)}
                        className="flex items-center gap-1"
                      >
                        {previewVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        {previewVisible ? '隐藏预览' : '显示预览'}
                      </Button>
                    </div>
                    <Button
                      onClick={downloadRenamedFiles}
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      下载重命名文件
                    </Button>
                  </div>

                  {previewVisible && (
                    <div className="border rounded-lg overflow-hidden">
                      <div className="bg-gray-50 px-4 py-2 grid grid-cols-3 gap-4 font-medium text-sm">
                        <div>原文件名</div>
                        <div>新文件名</div>
                        <div>操作</div>
                      </div>
                      <div className="max-h-96 overflow-y-auto">
                        {renamedFiles.map((item, index) => (
                          <div key={index} className="px-4 py-3 border-t grid grid-cols-3 gap-4 items-center">
                            <div className="truncate text-sm" title={item.original.name}>
                              {item.original.name}
                            </div>
                            <div className="flex items-center gap-2">
                              <span 
                                className={`truncate text-sm ${item.hasConflict ? 'text-red-600' : 'text-green-600'}`}
                                title={item.newName}
                              >
                                {item.newName}
                              </span>
                              {item.hasConflict && (
                                <Badge variant="destructive" className="text-xs">
                                  冲突
                                </Badge>
                              )}
                            </div>
                            <div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyFileName(item.newName, index)}
                                className="flex items-center gap-1"
                              >
                                {copiedIndex === index ? (
                                  <Check className="h-3 w-3" />
                                ) : (
                                  <Copy className="h-3 w-3" />
                                )}
                                复制
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <FileImage className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>请先上传图片文件</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
