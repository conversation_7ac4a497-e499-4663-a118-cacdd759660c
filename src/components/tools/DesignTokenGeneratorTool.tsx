'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  Copy, 
  Download, 
  Palette, 
  Plus, 
  Minus, 
  RotateCcw, 
  Play,
  Settings,
  Eye,
  Code,
  FileCode,
  Layers,
  Grid,
  Type,
  Ruler,
  Zap,
  Package,
  Upload,
  CheckCircle
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface DesignToken {
  id: string
  name: string
  value: string
  type: 'color' | 'typography' | 'spacing' | 'size' | 'shadow' | 'border' | 'opacity' | 'z-index' | 'timing' | 'breakpoint'
  category: string
  description?: string
  alias?: string
  deprecated?: boolean
}

interface TokenCategory {
  name: string
  icon: React.ReactNode
  color: string
  description: string
}

interface ExportFormat {
  name: string
  extension: string
  description: string
}

const DesignTokenGeneratorTool: React.FC = () => {
  const { toast } = useToast()

  const [tokens, setTokens] = useState<DesignToken[]>([
    {
      id: '1',
      name: 'color.primary.500',
      value: '#3b82f6',
      type: 'color',
      category: 'brand',
      description: 'Primary brand color'
    },
    {
      id: '2',
      name: 'color.gray.100',
      value: '#f3f4f6',
      type: 'color',
      category: 'neutral',
      description: 'Light gray background'
    },
    {
      id: '3',
      name: 'font.size.base',
      value: '16px',
      type: 'typography',
      category: 'text',
      description: 'Base font size'
    },
    {
      id: '4',
      name: 'spacing.md',
      value: '16px',
      type: 'spacing',
      category: 'layout',
      description: 'Medium spacing'
    }
  ])

  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [exportFormat, setExportFormat] = useState<string>('css')
  const [includeDocumentation, setIncludeDocumentation] = useState(true)
  const [useAliases, setUseAliases] = useState(false)
  const [namingConvention, setNamingConvention] = useState<'kebab' | 'camel' | 'snake' | 'dot'>('kebab')

  const categories: TokenCategory[] = [
    { name: 'brand', icon: <Palette className="h-4 w-4" />, color: 'bg-blue-100 text-blue-700', description: '品牌色彩' },
    { name: 'neutral', icon: <Grid className="h-4 w-4" />, color: 'bg-gray-100 text-gray-700', description: '中性色' },
    { name: 'semantic', icon: <CheckCircle className="h-4 w-4" />, color: 'bg-green-100 text-green-700', description: '语义色' },
    { name: 'text', icon: <Type className="h-4 w-4" />, color: 'bg-purple-100 text-purple-700', description: '文字' },
    { name: 'layout', icon: <Ruler className="h-4 w-4" />, color: 'bg-orange-100 text-orange-700', description: '布局' },
    { name: 'interaction', icon: <Zap className="h-4 w-4" />, color: 'bg-yellow-100 text-yellow-700', description: '交互' },
    { name: 'elevation', icon: <Layers className="h-4 w-4" />, color: 'bg-indigo-100 text-indigo-700', description: '高度' }
  ]

  const tokenTypes = [
    { value: 'color', label: '颜色' },
    { value: 'typography', label: '字体' },
    { value: 'spacing', label: '间距' },
    { value: 'size', label: '尺寸' },
    { value: 'shadow', label: '阴影' },
    { value: 'border', label: '边框' },
    { value: 'opacity', label: '透明度' },
    { value: 'z-index', label: 'Z层级' },
    { value: 'timing', label: '时间' },
    { value: 'breakpoint', label: '断点' }
  ]

  const exportFormats: ExportFormat[] = [
    { name: 'CSS Variables', extension: 'css', description: 'CSS自定义属性格式' },
    { name: 'SCSS Variables', extension: 'scss', description: 'SCSS变量格式' },
    { name: 'JavaScript', extension: 'js', description: 'JavaScript对象格式' },
    { name: 'TypeScript', extension: 'ts', description: 'TypeScript定义格式' },
    { name: 'JSON', extension: 'json', description: 'JSON数据格式' },
    { name: 'Design Tokens', extension: 'json', description: 'W3C Design Tokens格式' },
    { name: 'Figma Tokens', extension: 'json', description: 'Figma Tokens插件格式' },
    { name: 'Style Dictionary', extension: 'json', description: 'Style Dictionary格式' }
  ]

  const namingConventions = [
    { value: 'kebab', label: 'kebab-case', example: 'color-primary-500' },
    { value: 'camel', label: 'camelCase', example: 'colorPrimary500' },
    { value: 'snake', label: 'snake_case', example: 'color_primary_500' },
    { value: 'dot', label: 'dot.notation', example: 'color.primary.500' }
  ]

  const predefinedTokenSets = [
    {
      name: '基础色彩系统',
      description: '完整的品牌色彩令牌',
      tokens: [
        { name: 'color.primary.50', value: '#eff6ff', type: 'color', category: 'brand', description: '主色调-最浅' },
        { name: 'color.primary.100', value: '#dbeafe', type: 'color', category: 'brand', description: '主色调-浅' },
        { name: 'color.primary.500', value: '#3b82f6', type: 'color', category: 'brand', description: '主色调-标准' },
        { name: 'color.primary.900', value: '#1e3a8a', type: 'color', category: 'brand', description: '主色调-深' },
        { name: 'color.gray.50', value: '#f9fafb', type: 'color', category: 'neutral', description: '灰色-最浅' },
        { name: 'color.gray.500', value: '#6b7280', type: 'color', category: 'neutral', description: '灰色-中等' },
        { name: 'color.gray.900', value: '#111827', type: 'color', category: 'neutral', description: '灰色-最深' },
        { name: 'color.success.500', value: '#10b981', type: 'color', category: 'semantic', description: '成功色' },
        { name: 'color.warning.500', value: '#f59e0b', type: 'color', category: 'semantic', description: '警告色' },
        { name: 'color.error.500', value: '#ef4444', type: 'color', category: 'semantic', description: '错误色' }
      ]
    },
    {
      name: '字体排版系统',
      description: '完整的字体和排版令牌',
      tokens: [
        { name: 'font.family.sans', value: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif', type: 'typography', category: 'text', description: '无衬线字体' },
        { name: 'font.family.serif', value: 'Georgia, "Times New Roman", Times, serif', type: 'typography', category: 'text', description: '衬线字体' },
        { name: 'font.family.mono', value: 'Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace', type: 'typography', category: 'text', description: '等宽字体' },
        { name: 'font.size.xs', value: '12px', type: 'typography', category: 'text', description: '超小字号' },
        { name: 'font.size.sm', value: '14px', type: 'typography', category: 'text', description: '小字号' },
        { name: 'font.size.base', value: '16px', type: 'typography', category: 'text', description: '基准字号' },
        { name: 'font.size.lg', value: '18px', type: 'typography', category: 'text', description: '大字号' },
        { name: 'font.size.xl', value: '20px', type: 'typography', category: 'text', description: '超大字号' },
        { name: 'font.weight.normal', value: '400', type: 'typography', category: 'text', description: '正常字重' },
        { name: 'font.weight.medium', value: '500', type: 'typography', category: 'text', description: '中等字重' },
        { name: 'font.weight.semibold', value: '600', type: 'typography', category: 'text', description: '半粗字重' },
        { name: 'font.weight.bold', value: '700', type: 'typography', category: 'text', description: '粗体字重' }
      ]
    },
    {
      name: '间距尺寸系统',
      description: '8px网格的间距和尺寸令牌',
      tokens: [
        { name: 'spacing.xs', value: '4px', type: 'spacing', category: 'layout', description: '超小间距' },
        { name: 'spacing.sm', value: '8px', type: 'spacing', category: 'layout', description: '小间距' },
        { name: 'spacing.md', value: '16px', type: 'spacing', category: 'layout', description: '中等间距' },
        { name: 'spacing.lg', value: '24px', type: 'spacing', category: 'layout', description: '大间距' },
        { name: 'spacing.xl', value: '32px', type: 'spacing', category: 'layout', description: '超大间距' },
        { name: 'spacing.2xl', value: '48px', type: 'spacing', category: 'layout', description: '2倍大间距' },
        { name: 'size.sm', value: '24px', type: 'size', category: 'layout', description: '小尺寸' },
        { name: 'size.md', value: '32px', type: 'size', category: 'layout', description: '中等尺寸' },
        { name: 'size.lg', value: '48px', type: 'size', category: 'layout', description: '大尺寸' },
        { name: 'border.radius.sm', value: '4px', type: 'border', category: 'layout', description: '小圆角' },
        { name: 'border.radius.md', value: '8px', type: 'border', category: 'layout', description: '中等圆角' },
        { name: 'border.radius.lg', value: '12px', type: 'border', category: 'layout', description: '大圆角' }
      ]
    },
    {
      name: '阴影和效果',
      description: '阴影、透明度和交互效果令牌',
      tokens: [
        { name: 'shadow.sm', value: '0 1px 2px 0 rgba(0, 0, 0, 0.05)', type: 'shadow', category: 'elevation', description: '小阴影' },
        { name: 'shadow.md', value: '0 4px 6px -1px rgba(0, 0, 0, 0.1)', type: 'shadow', category: 'elevation', description: '中等阴影' },
        { name: 'shadow.lg', value: '0 10px 15px -3px rgba(0, 0, 0, 0.1)', type: 'shadow', category: 'elevation', description: '大阴影' },
        { name: 'shadow.xl', value: '0 20px 25px -5px rgba(0, 0, 0, 0.1)', type: 'shadow', category: 'elevation', description: '超大阴影' },
        { name: 'opacity.disabled', value: '0.5', type: 'opacity', category: 'interaction', description: '禁用透明度' },
        { name: 'opacity.hover', value: '0.8', type: 'opacity', category: 'interaction', description: '悬停透明度' },
        { name: 'z-index.dropdown', value: '1000', type: 'z-index', category: 'elevation', description: '下拉菜单层级' },
        { name: 'z-index.modal', value: '1050', type: 'z-index', category: 'elevation', description: '弹窗层级' },
        { name: 'timing.fast', value: '150ms', type: 'timing', category: 'interaction', description: '快速动画' },
        { name: 'timing.normal', value: '250ms', type: 'timing', category: 'interaction', description: '正常动画' },
        { name: 'timing.slow', value: '350ms', type: 'timing', category: 'interaction', description: '慢速动画' }
      ]
    }
  ]

  const addToken = useCallback(() => {
    const newToken: DesignToken = {
      id: String(Date.now()),
      name: 'new.token',
      value: '#000000',
      type: 'color',
      category: 'brand',
      description: ''
    }
    setTokens(prev => [...prev, newToken])
  }, [])

  const updateToken = useCallback((id: string, updates: Partial<DesignToken>) => {
    setTokens(prev => prev.map(token => 
      token.id === id ? { ...token, ...updates } : token
    ))
  }, [])

  const removeToken = useCallback((id: string) => {
    setTokens(prev => prev.filter(token => token.id !== id))
  }, [])

  const applyTokenSet = useCallback((tokenSet: any) => {
    const newTokens = tokenSet.tokens.map((token: any, index: number) => ({
      ...token,
      id: `preset-${Date.now()}-${index}`
    }))
    setTokens(newTokens)
    toast({
      title: '令牌集已应用',
      description: `已成功应用"${tokenSet.name}"，包含${newTokens.length}个令牌`
    })
  }, [toast])

  const resetTokens = useCallback(() => {
    setTokens([
      {
        id: '1',
        name: 'color.primary.500',
        value: '#3b82f6',
        type: 'color',
        category: 'brand',
        description: 'Primary brand color'
      },
      {
        id: '2',
        name: 'color.gray.100',
        value: '#f3f4f6',
        type: 'color',
        category: 'neutral',
        description: 'Light gray background'
      },
      {
        id: '3',
        name: 'font.size.base',
        value: '16px',
        type: 'typography',
        category: 'text',
        description: 'Base font size'
      },
      {
        id: '4',
        name: 'spacing.md',
        value: '16px',
        type: 'spacing',
        category: 'layout',
        description: 'Medium spacing'
      }
    ])
  }, [])

  const filteredTokens = useMemo(() => {
    return tokens.filter(token => {
      const categoryMatch = selectedCategory === 'all' || token.category === selectedCategory
      const typeMatch = selectedType === 'all' || token.type === selectedType
      return categoryMatch && typeMatch
    })
  }, [tokens, selectedCategory, selectedType])

  const convertNaming = useCallback((name: string, convention: string) => {
    switch (convention) {
      case 'kebab':
        return name.replace(/[._]/g, '-').replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '')
      case 'camel':
        return name.replace(/[-._](.)/g, (_, char) => char.toUpperCase()).replace(/^[A-Z]/, char => char.toLowerCase())
      case 'snake':
        return name.replace(/[-._]/g, '_').replace(/([A-Z])/g, '_$1').toLowerCase().replace(/^_/, '')
      case 'dot':
        return name.replace(/[-_]/g, '.').replace(/([A-Z])/g, '.$1').toLowerCase().replace(/^\./, '')
      default:
        return name
    }
  }, [])

  const generateExport = useCallback(() => {
    const processedTokens = tokens.map(token => ({
      ...token,
      name: convertNaming(token.name, namingConvention)
    }))

    let output = ''

    switch (exportFormat) {
      case 'css':
        output = ':root {\n'
        if (includeDocumentation) {
          output += '  /* Design Tokens Generated by PoorLow Tools */\n\n'
        }
        processedTokens.forEach(token => {
          if (includeDocumentation && token.description) {
            output += `  /* ${token.description} */\n`
          }
          output += `  --${token.name}: ${token.value};\n`
          if (includeDocumentation) output += '\n'
        })
        output += '}'
        break

      case 'scss':
        if (includeDocumentation) {
          output += '// Design Tokens Generated by PoorLow Tools\n\n'
        }
        
        const groupedTokens = processedTokens.reduce((acc, token) => {
          if (!acc[token.category]) acc[token.category] = []
          acc[token.category].push(token)
          return acc
        }, {} as Record<string, DesignToken[]>)

        Object.entries(groupedTokens).forEach(([category, categoryTokens]) => {
          if (includeDocumentation) {
            output += `// ${category.charAt(0).toUpperCase() + category.slice(1)} Tokens\n`
          }
          categoryTokens.forEach(token => {
            if (includeDocumentation && token.description) {
              output += `// ${token.description}\n`
            }
            output += `$${token.name}: ${token.value};\n`
          })
          output += '\n'
        })
        break

      case 'js':
        if (includeDocumentation) {
          output += '/**\n * Design Tokens Generated by PoorLow Tools\n */\n\n'
        }
        output += 'export const tokens = {\n'
        processedTokens.forEach((token, index) => {
          if (includeDocumentation && token.description) {
            output += `  /** ${token.description} */\n`
          }
          output += `  '${token.name}': '${token.value}'${index < processedTokens.length - 1 ? ',' : ''}\n`
        })
        output += '}\n\nexport default tokens'
        break

      case 'ts':
        if (includeDocumentation) {
          output += '/**\n * Design Tokens Generated by PoorLow Tools\n */\n\n'
        }
        output += 'export interface DesignTokens {\n'
        processedTokens.forEach(token => {
          if (includeDocumentation && token.description) {
            output += `  /** ${token.description} */\n`
          }
          output += `  '${token.name}': string\n`
        })
        output += '}\n\nexport const tokens: DesignTokens = {\n'
        processedTokens.forEach((token, index) => {
          output += `  '${token.name}': '${token.value}'${index < processedTokens.length - 1 ? ',' : ''}\n`
        })
        output += '}\n\nexport default tokens'
        break

      case 'json':
        const jsonTokens = processedTokens.reduce((acc, token) => {
          acc[token.name] = {
            value: token.value,
            type: token.type,
            category: token.category,
            ...(token.description && { description: token.description }),
            ...(token.alias && { alias: token.alias }),
            ...(token.deprecated && { deprecated: token.deprecated })
          }
          return acc
        }, {} as Record<string, any>)
        output = JSON.stringify(jsonTokens, null, 2)
        break

      case 'design-tokens':
        const designTokensFormat = processedTokens.reduce((acc, token) => {
          const path = token.name.split('.')
          let current = acc
          
          for (let i = 0; i < path.length - 1; i++) {
            if (!current[path[i]]) current[path[i]] = {}
            current = current[path[i]]
          }
          
          current[path[path.length - 1]] = {
            $value: token.value,
            $type: token.type,
            ...(token.description && { $description: token.description })
          }
          
          return acc
        }, {} as Record<string, any>)
        output = JSON.stringify(designTokensFormat, null, 2)
        break

      case 'figma-tokens':
        const figmaTokens = processedTokens.reduce((acc, token) => {
          if (!acc[token.category]) acc[token.category] = {}
          const tokenName = token.name.replace(`${token.category}.`, '')
          acc[token.category][tokenName] = {
            value: token.value,
            type: token.type,
            ...(token.description && { description: token.description })
          }
          return acc
        }, {} as Record<string, any>)
        output = JSON.stringify(figmaTokens, null, 2)
        break

      case 'style-dictionary':
        const styleDictionaryTokens = processedTokens.reduce((acc, token) => {
          const path = token.name.split('.')
          let current = acc
          
          for (let i = 0; i < path.length - 1; i++) {
            if (!current[path[i]]) current[path[i]] = {}
            current = current[path[i]]
          }
          
          current[path[path.length - 1]] = {
            value: token.value,
            type: token.type,
            category: token.category,
            ...(token.description && { comment: token.description })
          }
          
          return acc
        }, {} as Record<string, any>)
        output = JSON.stringify(styleDictionaryTokens, null, 2)
        break
    }

    return output
  }, [tokens, exportFormat, includeDocumentation, namingConvention, convertNaming])

  const copyToClipboard = useCallback(() => {
    const output = generateExport()
    navigator.clipboard.writeText(output)
    toast({
      title: '已复制',
      description: '设计令牌代码已复制到剪贴板'
    })
  }, [generateExport, toast])

  const downloadFile = useCallback(() => {
    const output = generateExport()
    const format = exportFormats.find(f => f.name.toLowerCase().includes(exportFormat))
    const filename = `design-tokens.${format?.extension || 'txt'}`
    
    const blob = new Blob([output], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: '下载完成',
      description: `${filename} 文件已下载`
    })
  }, [generateExport, exportFormat, exportFormats, toast])

  const getTokenPreview = useCallback((token: DesignToken) => {
    switch (token.type) {
      case 'color':
        return (
          <div 
            className="w-6 h-6 rounded border border-gray-300"
            style={{ backgroundColor: token.value }}
          />
        )
      case 'typography':
        if (token.name.includes('family')) {
          return (
            <div 
              className="text-sm text-gray-700 px-2 py-1 bg-gray-100 rounded"
              style={{ fontFamily: token.value }}
            >
              Aa
            </div>
          )
        } else if (token.name.includes('size')) {
          return (
            <div 
              className="text-gray-700 leading-none"
              style={{ fontSize: token.value }}
            >
              T
            </div>
          )
        } else {
          return (
            <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {token.value}
            </div>
          )
        }
      case 'spacing':
      case 'size':
        return (
          <div className="flex items-center">
            <div 
              className="bg-blue-200 h-4"
              style={{ width: token.value }}
            />
            <span className="text-xs text-gray-500 ml-2">{token.value}</span>
          </div>
        )
      case 'shadow':
        return (
          <div 
            className="w-8 h-6 bg-white rounded border"
            style={{ boxShadow: token.value }}
          />
        )
      default:
        return (
          <div className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
            {token.value}
          </div>
        )
    }
  }, [])

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Package className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">设计令牌生成器</h1>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          创建和管理设计系统令牌，支持多种导出格式和命名规范
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 侧边栏配置 */}
        <div className="lg:col-span-1 space-y-6">
          <Tabs defaultValue="tokens" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="tokens">令牌</TabsTrigger>
              <TabsTrigger value="presets">预设</TabsTrigger>
            </TabsList>

            <TabsContent value="tokens" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      令牌管理
                    </div>
                    <Button size="sm" onClick={addToken}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 筛选选项 */}
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm">分类筛选</Label>
                      <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部分类</SelectItem>
                          {categories.map(category => (
                            <SelectItem key={category.name} value={category.name}>
                              <div className="flex items-center gap-2">
                                {category.icon}
                                {category.description}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm">类型筛选</Label>
                      <Select value={selectedType} onValueChange={setSelectedType}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部类型</SelectItem>
                          {tokenTypes.map(type => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* 令牌列表 */}
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredTokens.map(token => (
                      <div key={token.id} className="p-3 border rounded-lg space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getTokenPreview(token)}
                            <Badge className={categories.find(c => c.name === token.category)?.color}>
                              {token.category}
                            </Badge>
                          </div>
                          <Button 
                            size="sm" 
                            variant="destructive"
                            onClick={() => removeToken(token.id)}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <div className="space-y-2">
                          <div>
                            <Label className="text-xs">令牌名称</Label>
                            <Input
                              value={token.name}
                              onChange={(e) => updateToken(token.id, { name: e.target.value })}
                              placeholder="token.name"
                              className="font-mono text-sm"
                            />
                          </div>
                          
                          <div>
                            <Label className="text-xs">值</Label>
                            <div className="flex gap-2">
                              <Input
                                value={token.value}
                                onChange={(e) => updateToken(token.id, { value: e.target.value })}
                                placeholder="令牌值"
                                className="flex-1"
                              />
                              {token.type === 'color' && (
                                <Input
                                  type="color"
                                  value={token.value}
                                  onChange={(e) => updateToken(token.id, { value: e.target.value })}
                                  className="w-12 p-1"
                                />
                              )}
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <Label className="text-xs">类型</Label>
                              <Select 
                                value={token.type}
                                onValueChange={(value: DesignToken['type']) => updateToken(token.id, { type: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {tokenTypes.map(type => (
                                    <SelectItem key={type.value} value={type.value}>
                                      {type.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            
                            <div>
                              <Label className="text-xs">分类</Label>
                              <Select 
                                value={token.category}
                                onValueChange={(value) => updateToken(token.id, { category: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {categories.map(category => (
                                    <SelectItem key={category.name} value={category.name}>
                                      {category.description}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div>
                            <Label className="text-xs">描述 (可选)</Label>
                            <Input
                              value={token.description || ''}
                              onChange={(e) => updateToken(token.id, { description: e.target.value })}
                              placeholder="令牌用途描述"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="presets" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    预设令牌集
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {predefinedTokenSets.map((tokenSet, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="font-medium">{tokenSet.name}</h4>
                          <p className="text-sm text-gray-500">{tokenSet.description}</p>
                        </div>
                        <Button 
                          size="sm" 
                          onClick={() => applyTokenSet(tokenSet)}
                        >
                          应用
                        </Button>
                      </div>
                      <Badge variant="secondary">
                        {tokenSet.tokens.length} 个令牌
                      </Badge>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* 导出设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-sm">
                <FileCode className="h-4 w-4" />
                导出设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>导出格式</Label>
                <Select value={exportFormat} onValueChange={setExportFormat}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {exportFormats.map(format => (
                      <SelectItem key={format.name} value={format.name.toLowerCase().replace(' ', '-')}>
                        <div>
                          <div className="font-medium">{format.name}</div>
                          <div className="text-xs text-gray-500">{format.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>命名规范</Label>
                <Select value={namingConvention} onValueChange={(value: any) => setNamingConvention(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {namingConventions.map(convention => (
                      <SelectItem key={convention.value} value={convention.value}>
                        <div>
                          <div className="font-medium">{convention.label}</div>
                          <div className="text-xs font-mono text-gray-500">{convention.example}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label>包含文档注释</Label>
                  <Switch 
                    checked={includeDocumentation}
                    onCheckedChange={setIncludeDocumentation}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label>使用别名</Label>
                  <Switch 
                    checked={useAliases}
                    onCheckedChange={setUseAliases}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex gap-2">
            <Button variant="outline" onClick={resetTokens} className="flex-1">
              <RotateCcw className="h-4 w-4 mr-2" />
              重置
            </Button>
            <Button onClick={downloadFile} className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              下载
            </Button>
          </div>
        </div>

        {/* 主内容区域 */}
        <div className="lg:col-span-3 space-y-6">
          {/* 代码预览 */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  代码预览
                </CardTitle>
                <div className="flex gap-2">
                  <Button onClick={copyToClipboard} size="sm" variant="outline">
                    <Copy className="h-4 w-4 mr-1" />
                    复制
                  </Button>
                  <Button onClick={downloadFile} size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    下载
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="relative">
                <Textarea
                  value={generateExport()}
                  readOnly
                  className="min-h-[400px] font-mono text-sm"
                />
                <Button
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={copyToClipboard}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 令牌预览 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                令牌预览
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* 颜色令牌预览 */}
                {filteredTokens.filter(t => t.type === 'color').length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3 flex items-center gap-2">
                      <Palette className="h-4 w-4" />
                      颜色令牌
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {filteredTokens.filter(t => t.type === 'color').map(token => (
                        <div key={token.id} className="text-center">
                          <div 
                            className="w-full h-16 rounded-lg border border-gray-200 mb-2"
                            style={{ backgroundColor: token.value }}
                          />
                          <div className="font-mono text-xs font-medium">{token.name}</div>
                          <div className="text-xs text-gray-500">{token.value}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 字体令牌预览 */}
                {filteredTokens.filter(t => t.type === 'typography').length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3 flex items-center gap-2">
                      <Type className="h-4 w-4" />
                      字体令牌
                    </h4>
                    <div className="space-y-3">
                      {filteredTokens.filter(t => t.type === 'typography').map(token => (
                        <div key={token.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <div className="font-mono text-sm font-medium">{token.name}</div>
                            <div className="text-xs text-gray-500">{token.description}</div>
                          </div>
                          <div className="text-right">
                            <div 
                              className="text-lg leading-none mb-1"
                              style={token.name.includes('family') ? { fontFamily: token.value } : 
                                     token.name.includes('size') ? { fontSize: token.value } :
                                     token.name.includes('weight') ? { fontWeight: token.value } : {}}
                            >
                              {token.name.includes('family') ? 'Typography Sample' : 
                               token.name.includes('size') ? 'Size' : 
                               token.name.includes('weight') ? 'Weight' : 'Text'}
                            </div>
                            <div className="text-xs text-gray-500">{token.value}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 间距令牌预览 */}
                {filteredTokens.filter(t => t.type === 'spacing' || t.type === 'size').length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3 flex items-center gap-2">
                      <Ruler className="h-4 w-4" />
                      间距/尺寸令牌
                    </h4>
                    <div className="space-y-2">
                      {filteredTokens.filter(t => t.type === 'spacing' || t.type === 'size').map(token => (
                        <div key={token.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <div className="font-mono text-sm font-medium">{token.name}</div>
                            <div className="text-xs text-gray-500">{token.description}</div>
                          </div>
                          <div className="flex items-center gap-3">
                            <div 
                              className="bg-blue-200 h-6 border border-blue-300"
                              style={{ width: token.value }}
                            />
                            <span className="text-sm font-mono">{token.value}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 令牌统计 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                令牌统计
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="font-semibold text-blue-600">{tokens.length}</div>
                  <div className="text-gray-600">总令牌数</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="font-semibold text-red-600">
                    {tokens.filter(t => t.type === 'color').length}
                  </div>
                  <div className="text-gray-600">颜色令牌</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="font-semibold text-green-600">
                    {tokens.filter(t => t.type === 'typography').length}
                  </div>
                  <div className="text-gray-600">字体令牌</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="font-semibold text-purple-600">
                    {tokens.filter(t => t.type === 'spacing').length}
                  </div>
                  <div className="text-gray-600">间距令牌</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default DesignTokenGeneratorTool