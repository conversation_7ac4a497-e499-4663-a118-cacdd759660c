'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Copy, Calculator, Eye, Settings, TrendingUp, Monitor, Smartphone, Tablet, RotateCcw, Info } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface ClampConfig {
  minValue: number
  minUnit: string
  maxValue: number
  maxUnit: string
  minViewport: number
  maxViewport: number
  property: string
}

interface DevicePreview {
  name: string
  width: number
  icon: any
  color: string
}

const devicePresets: DevicePreview[] = [
  { name: '手机', width: 375, icon: Smartphone, color: 'text-blue-500' },
  { name: '平板', width: 768, icon: Tablet, color: 'text-green-500' },
  { name: '桌面', width: 1200, icon: Monitor, color: 'text-purple-500' },
  { name: '大屏', width: 1920, icon: Monitor, color: 'text-orange-500' }
]

const cssProperties = [
  { value: 'font-size', label: '字体大小', unit: 'rem' },
  { value: 'width', label: '宽度', unit: 'px' },
  { value: 'height', label: '高度', unit: 'px' },
  { value: 'padding', label: '内边距', unit: 'rem' },
  { value: 'margin', label: '外边距', unit: 'rem' },
  { value: 'gap', label: '间距', unit: 'rem' },
  { value: 'border-radius', label: '圆角', unit: 'px' },
  { value: 'line-height', label: '行高', unit: '' },
  { value: 'letter-spacing', label: '字间距', unit: 'em' },
  { value: 'top', label: '顶部位置', unit: 'px' },
  { value: 'left', label: '左侧位置', unit: 'px' },
  { value: 'right', label: '右侧位置', unit: 'px' },
  { value: 'bottom', label: '底部位置', unit: 'px' }
]

const units = ['px', 'rem', 'em', '%', 'vw', 'vh', 'vmin', 'vmax']

const commonPresets = [
  {
    name: '响应式字体',
    description: '从16px到24px的字体大小',
    config: {
      minValue: 1,
      minUnit: 'rem',
      maxValue: 1.5,
      maxUnit: 'rem',
      minViewport: 320,
      maxViewport: 1200,
      property: 'font-size'
    }
  },
  {
    name: '响应式容器',
    description: '从320px到1200px的容器宽度',
    config: {
      minValue: 320,
      minUnit: 'px',
      maxValue: 1200,
      maxUnit: 'px',
      minViewport: 320,
      maxViewport: 1920,
      property: 'width'
    }
  },
  {
    name: '响应式间距',
    description: '从0.5rem到2rem的间距',
    config: {
      minValue: 0.5,
      minUnit: 'rem',
      maxValue: 2,
      maxUnit: 'rem',
      minViewport: 320,
      maxViewport: 1200,
      property: 'padding'
    }
  },
  {
    name: '响应式行高',
    description: '从1.4到1.6的行高',
    config: {
      minValue: 1.4,
      minUnit: '',
      maxValue: 1.6,
      maxUnit: '',
      minViewport: 320,
      maxViewport: 1200,
      property: 'line-height'
    }
  }
]

export default function CssClampCalculatorTool() {
  const [config, setConfig] = useState<ClampConfig>({
    minValue: 1,
    minUnit: 'rem',
    maxValue: 1.5,
    maxUnit: 'rem',
    minViewport: 320,
    maxViewport: 1200,
    property: 'font-size'
  })

  const [activeTab, setActiveTab] = useState('calculator')
  const [previewViewport, setPreviewViewport] = useState(768)

  const updateConfig = useCallback((updates: Partial<ClampConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }))
  }, [])

  // 计算clamp函数的中间值（viewportwidth部分）
  const calculateClampFunction = useMemo(() => {
    const { minValue, minUnit, maxValue, maxUnit, minViewport, maxViewport } = config

    // 确保单位一致性
    if (minUnit !== maxUnit && minUnit !== '' && maxUnit !== '') {
      return null // 单位不一致时无法计算
    }

    const unit = minUnit || maxUnit
    const viewportDiff = maxViewport - minViewport
    const valueDiff = maxValue - minValue

    if (viewportDiff === 0) {
      return null
    }

    // 计算斜率和截距
    const slope = (valueDiff / viewportDiff) * 100 // 转换为vw
    const intercept = minValue - (slope * minViewport) / 100

    const preferredValue = intercept >= 0 
      ? `${intercept.toFixed(4)}${unit} + ${slope.toFixed(4)}vw`
      : `${slope.toFixed(4)}vw - ${Math.abs(intercept).toFixed(4)}${unit}`

    return {
      clampFunction: `clamp(${minValue}${unit}, ${preferredValue}, ${maxValue}${unit})`,
      slope,
      intercept,
      preferredValue
    }
  }, [config])

  // 计算当前视口下的实际值
  const getCurrentValue = useCallback((viewport: number) => {
    if (!calculateClampFunction) return null

    const { minValue, maxValue, minViewport, maxViewport } = config
    
    if (viewport <= minViewport) return minValue
    if (viewport >= maxViewport) return maxValue

    const progress = (viewport - minViewport) / (maxViewport - minViewport)
    return minValue + (maxValue - minValue) * progress
  }, [config, calculateClampFunction])

  const copyClampFunction = useCallback(() => {
    if (calculateClampFunction) {
      navigator.clipboard.writeText(calculateClampFunction.clampFunction)
      toast.success('Clamp函数已复制到剪贴板')
    }
  }, [calculateClampFunction])

  const copyCSSRule = useCallback(() => {
    if (calculateClampFunction) {
      const cssRule = `.element {\n  ${config.property}: ${calculateClampFunction.clampFunction};\n}`
      navigator.clipboard.writeText(cssRule)
      toast.success('CSS规则已复制到剪贴板')
    }
  }, [calculateClampFunction, config.property])

  const applyPreset = useCallback((preset: typeof commonPresets[0]) => {
    setConfig(preset.config)
    toast.success(`已应用预设: ${preset.name}`)
  }, [])

  const resetConfig = useCallback(() => {
    setConfig({
      minValue: 1,
      minUnit: 'rem',
      maxValue: 1.5,
      maxUnit: 'rem',
      minViewport: 320,
      maxViewport: 1200,
      property: 'font-size'
    })
    toast.success('已重置所有设置')
  }, [])

  // 生成视口宽度对应值的图表数据
  const chartData = useMemo(() => {
    const points = []
    const step = (config.maxViewport - config.minViewport) / 50
    for (let vw = config.minViewport; vw <= config.maxViewport; vw += step) {
      const value = getCurrentValue(vw)
      if (value !== null) {
        points.push({ viewport: vw, value })
      }
    }
    return points
  }, [config, getCurrentValue])

  const currentValue = getCurrentValue(previewViewport)

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          CSS Clamp计算器
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          响应式CSS值计算器，生成fluid typography和responsive spacing的完美clamp()函数
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="calculator" className="flex items-center gap-2">
            <Calculator className="w-4 h-4" />
            计算器
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="w-4 h-4" />
            预览
          </TabsTrigger>
          <TabsTrigger value="presets" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            预设
          </TabsTrigger>
        </TabsList>

        <TabsContent value="calculator" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 输入配置 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    配置参数
                  </CardTitle>
                  <Button variant="outline" size="sm" onClick={resetConfig}>
                    <RotateCcw className="h-4 w-4 mr-1" />
                    重置
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* CSS属性选择 */}
                <div className="space-y-2">
                  <Label>CSS属性</Label>
                  <Select 
                    value={config.property} 
                    onValueChange={(value) => updateConfig({ property: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {cssProperties.map(prop => (
                        <SelectItem key={prop.value} value={prop.value}>
                          {prop.label} ({prop.value})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                {/* 最小值设置 */}
                <div className="space-y-4">
                  <Label className="text-base font-semibold">最小值 (小屏幕)</Label>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="col-span-2 space-y-2">
                      <Label>数值</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={config.minValue}
                        onChange={(e) => updateConfig({ minValue: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>单位</Label>
                      <Select 
                        value={config.minUnit} 
                        onValueChange={(value) => updateConfig({ minUnit: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {units.map(unit => (
                            <SelectItem key={unit} value={unit}>
                              {unit || '无单位'}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>最小视口宽度: {config.minViewport}px</Label>
                    <Slider
                      value={[config.minViewport]}
                      onValueChange={([value]) => updateConfig({ minViewport: value })}
                      min={200}
                      max={800}
                      step={10}
                    />
                  </div>
                </div>

                <Separator />

                {/* 最大值设置 */}
                <div className="space-y-4">
                  <Label className="text-base font-semibold">最大值 (大屏幕)</Label>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="col-span-2 space-y-2">
                      <Label>数值</Label>
                      <Input
                        type="number"
                        step="0.1"
                        value={config.maxValue}
                        onChange={(e) => updateConfig({ maxValue: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>单位</Label>
                      <Select 
                        value={config.maxUnit} 
                        onValueChange={(value) => updateConfig({ maxUnit: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {units.map(unit => (
                            <SelectItem key={unit} value={unit}>
                              {unit || '无单位'}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>最大视口宽度: {config.maxViewport}px</Label>
                    <Slider
                      value={[config.maxViewport]}
                      onValueChange={([value]) => updateConfig({ maxViewport: value })}
                      min={800}
                      max={2000}
                      step={10}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 结果展示 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  计算结果
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {calculateClampFunction ? (
                  <>
                    {/* Clamp函数 */}
                    <div className="space-y-3">
                      <Label className="text-base font-semibold">Clamp函数</Label>
                      <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
                        <code className="text-sm break-all font-mono">
                          {calculateClampFunction.clampFunction}
                        </code>
                      </div>
                      <Button onClick={copyClampFunction} size="sm" className="w-full">
                        <Copy className="h-4 w-4 mr-2" />
                        复制Clamp函数
                      </Button>
                    </div>

                    <Separator />

                    {/* CSS规则 */}
                    <div className="space-y-3">
                      <Label className="text-base font-semibold">CSS规则</Label>
                      <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
                        <code className="text-sm break-all font-mono">
                          .element {'{'}
                          <br />
                          &nbsp;&nbsp;{config.property}: {calculateClampFunction.clampFunction};
                          <br />
                          {'}'}
                        </code>
                      </div>
                      <Button onClick={copyCSSRule} size="sm" className="w-full">
                        <Copy className="h-4 w-4 mr-2" />
                        复制CSS规则
                      </Button>
                    </div>

                    <Separator />

                    {/* 数学解释 */}
                    <div className="space-y-3">
                      <Label className="text-base font-semibold flex items-center gap-2">
                        <Info className="h-4 w-4" />
                        计算详情
                      </Label>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">斜率 (slope):</span>
                          <Badge variant="outline">
                            {calculateClampFunction.slope.toFixed(4)}vw
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">截距 (intercept):</span>
                          <Badge variant="outline">
                            {calculateClampFunction.intercept.toFixed(4)}{config.minUnit}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">中间值:</span>
                          <Badge variant="outline">
                            {calculateClampFunction.preferredValue}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Calculator className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p>请检查配置参数</p>
                    <p className="text-sm mt-1">确保最小值和最大值使用相同单位</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 设备预览 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  设备预览
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 视口宽度控制 */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <Label>当前视口: {previewViewport}px</Label>
                    {currentValue && (
                      <Badge variant="secondary">
                        {currentValue.toFixed(2)}{config.minUnit}
                      </Badge>
                    )}
                  </div>
                  <Slider
                    value={[previewViewport]}
                    onValueChange={([value]) => setPreviewViewport(value)}
                    min={200}
                    max={2000}
                    step={10}
                  />
                </div>

                {/* 设备预设 */}
                <div className="grid grid-cols-2 gap-2">
                  {devicePresets.map((device) => (
                    <Button
                      key={device.name}
                      variant={previewViewport === device.width ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPreviewViewport(device.width)}
                      className="flex items-center gap-2"
                    >
                      <device.icon className={`h-4 w-4 ${device.color}`} />
                      <span>{device.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {device.width}px
                      </Badge>
                    </Button>
                  ))}
                </div>

                {/* 视觉预览 */}
                {config.property === 'font-size' && currentValue && (
                  <div className="p-6 bg-gray-50 dark:bg-gray-800 rounded-lg text-center">
                    <p 
                      className="text-gray-900 dark:text-gray-100"
                      style={{ fontSize: `${currentValue}${config.minUnit}` }}
                    >
                      响应式文字大小预览
                    </p>
                    <p className="text-sm text-gray-500 mt-2">
                      当前大小: {currentValue.toFixed(2)}{config.minUnit}
                    </p>
                  </div>
                )}

                {config.property === 'width' && currentValue && (
                  <div className="p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div 
                      className="bg-blue-500 h-12 rounded transition-all duration-300"
                      style={{ width: `${Math.min(currentValue, 100)}%` }}
                    />
                    <p className="text-sm text-gray-500 mt-2 text-center">
                      宽度: {currentValue.toFixed(2)}{config.minUnit}
                    </p>
                  </div>
                )}

                {config.property.startsWith('padding') && currentValue && (
                  <div className="p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div 
                      className="bg-blue-500 h-16 w-32 mx-auto rounded flex items-center justify-center text-white text-sm transition-all duration-300"
                      style={{ padding: `${currentValue}${config.minUnit}` }}
                    >
                      内容区域
                    </div>
                    <p className="text-sm text-gray-500 mt-2 text-center">
                      内边距: {currentValue.toFixed(2)}{config.minUnit}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 响应曲线图 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  响应曲线
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* 简化的图表 */}
                  <div className="h-48 bg-gray-50 dark:bg-gray-800 rounded-lg p-4 relative overflow-hidden">
                    <svg className="w-full h-full" viewBox="0 0 400 160">
                      {/* 网格线 */}
                      <defs>
                        <pattern id="grid" width="40" height="32" patternUnits="userSpaceOnUse">
                          <path d="M 40 0 L 0 0 0 32" fill="none" stroke="#e5e5e5" strokeWidth="1"/>
                        </pattern>
                      </defs>
                      <rect width="400" height="160" fill="url(#grid)" />
                      
                      {/* 数据线 */}
                      {chartData.length > 1 && (
                        <polyline
                          fill="none"
                          stroke="#3b82f6"
                          strokeWidth="2"
                          points={chartData.map((point, index) => {
                            const x = (index / (chartData.length - 1)) * 380 + 10
                            const maxVal = Math.max(...chartData.map(p => p.value))
                            const minVal = Math.min(...chartData.map(p => p.value))
                            const range = maxVal - minVal || 1
                            const y = 140 - ((point.value - minVal) / range) * 120
                            return `${x},${y}`
                          }).join(' ')}
                        />
                      )}
                      
                      {/* 当前值指示器 */}
                      {currentValue && (
                        <circle
                          cx={(previewViewport - config.minViewport) / (config.maxViewport - config.minViewport) * 380 + 10}
                          cy={140 - ((currentValue - config.minValue) / (config.maxValue - config.minValue)) * 120}
                          r="4"
                          fill="#ef4444"
                          stroke="#ffffff"
                          strokeWidth="2"
                        />
                      )}
                      
                      {/* 轴标签 */}
                      <text x="10" y="155" fontSize="10" fill="#6b7280">{config.minViewport}px</text>
                      <text x="370" y="155" fontSize="10" fill="#6b7280">{config.maxViewport}px</text>
                      <text x="5" y="15" fontSize="10" fill="#6b7280">{config.maxValue}{config.maxUnit}</text>
                      <text x="5" y="145" fontSize="10" fill="#6b7280">{config.minValue}{config.minUnit}</text>
                    </svg>
                  </div>
                  
                  {/* 图例 */}
                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-0.5 bg-blue-500"></div>
                      <span className="text-gray-600">响应曲线</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-gray-600">当前值</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="presets" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                常用预设
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {commonPresets.map((preset, index) => (
                  <div
                    key={index}
                    className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-gray-300 dark:hover:border-gray-600 cursor-pointer transition-colors"
                    onClick={() => applyPreset(preset)}
                  >
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">
                          {preset.name}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {preset.description}
                        </p>
                      </div>
                      
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-600">属性:</span>
                          <Badge variant="outline">{preset.config.property}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">范围:</span>
                          <Badge variant="outline">
                            {preset.config.minValue}{preset.config.minUnit} → {preset.config.maxValue}{preset.config.maxUnit}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">视口:</span>
                          <Badge variant="outline">
                            {preset.config.minViewport}px → {preset.config.maxViewport}px
                          </Badge>
                        </div>
                      </div>
                      
                      <Button size="sm" className="w-full">
                        应用预设
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 自定义预设说明 */}
          <Card>
            <CardHeader>
              <CardTitle>使用指南</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 text-sm">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                    1. 选择CSS属性
                  </h4>
                  <p className="text-gray-600">
                    选择需要响应式调整的CSS属性，如font-size、padding、width等。
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                    2. 设置数值范围
                  </h4>
                  <p className="text-gray-600">
                    定义最小值和最大值，以及对应的视口宽度范围。
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                    3. 预览效果
                  </h4>
                  <p className="text-gray-600">
                    使用预览面板查看不同屏幕尺寸下的实际效果。
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                    4. 复制代码
                  </h4>
                  <p className="text-gray-600">
                    生成的clamp()函数可以直接在CSS中使用，实现完美的响应式设计。
                  </p>
                </div>
              </div>
              
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  <strong>提示:</strong> clamp()函数在现代浏览器中有良好支持，可以替代复杂的媒体查询实现流畅的响应式效果。
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}