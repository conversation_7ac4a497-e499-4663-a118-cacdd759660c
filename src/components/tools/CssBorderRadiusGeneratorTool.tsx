'use client'

import React, { useState, useCallback, useMemo, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Copy, Download, RotateCcw, Eye, Palette, Settings, Grid, Layers, Square, Target } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface BorderRadiusConfig {
  topLeft: number
  topRight: number
  bottomRight: number
  bottomLeft: number
  unit: 'px' | '%' | 'rem' | 'em'
  syncCorners: boolean
}

interface PresetRadius {
  name: string
  description: string
  config: Omit<BorderRadiusConfig, 'unit' | 'syncCorners'>
}

const presetRadiuses: PresetRadius[] = [
  {
    name: '无圆角',
    description: '完全直角',
    config: { topLeft: 0, topRight: 0, bottomRight: 0, bottomLeft: 0 }
  },
  {
    name: '轻微圆角',
    description: '微妙的圆角效果',
    config: { topLeft: 4, topRight: 4, bottomRight: 4, bottomLeft: 4 }
  },
  {
    name: '标准圆角',
    description: '常见的卡片圆角',
    config: { topLeft: 8, topRight: 8, bottomRight: 8, bottomLeft: 8 }
  },
  {
    name: '大圆角',
    description: '明显的圆角效果',
    config: { topLeft: 16, topRight: 16, bottomRight: 16, bottomLeft: 16 }
  },
  {
    name: '超大圆角',
    description: '非常明显的圆角',
    config: { topLeft: 24, topRight: 24, bottomRight: 24, bottomLeft: 24 }
  },
  {
    name: '圆形',
    description: '完全圆形（50%）',
    config: { topLeft: 50, topRight: 50, bottomRight: 50, bottomLeft: 50 }
  },
  {
    name: '胶囊形',
    description: '左右圆形，上下直角',
    config: { topLeft: 50, topRight: 50, bottomRight: 50, bottomLeft: 50 }
  },
  {
    name: '标签形状',
    description: '右侧圆角',
    config: { topLeft: 0, topRight: 24, bottomRight: 24, bottomLeft: 0 }
  },
  {
    name: '对话框',
    description: '左上角尖锐，其他圆角',
    config: { topLeft: 0, topRight: 16, bottomRight: 16, bottomLeft: 16 }
  },
  {
    name: '不规则',
    description: '各个角度不同',
    config: { topLeft: 8, topRight: 24, bottomRight: 4, bottomLeft: 16 }
  },
  {
    name: '水滴形',
    description: '顶部圆角，底部尖锐',
    config: { topLeft: 32, topRight: 32, bottomRight: 4, bottomLeft: 4 }
  },
  {
    name: '半圆形',
    description: '顶部半圆',
    config: { topLeft: 100, topRight: 100, bottomRight: 0, bottomLeft: 0 }
  }
]

export default function CssBorderRadiusGeneratorTool() {
  const [radiusConfig, setRadiusConfig] = useState<BorderRadiusConfig>({
    topLeft: 8,
    topRight: 8,
    bottomRight: 8,
    bottomLeft: 8,
    unit: 'px',
    syncCorners: true
  })
  
  const [backgroundColor, setBackgroundColor] = useState('#f0f0f0')
  const [elementColor, setElementColor] = useState('#ffffff')
  const [elementSize, setElementSize] = useState({ width: 200, height: 150 })
  const [showGrid, setShowGrid] = useState(false)
  const [showGuides, setShowGuides] = useState(false)
  const [exportFormat, setExportFormat] = useState<'css' | 'tailwind' | 'scss' | 'js'>('css')
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const generateRadiusCSS = useCallback((config: BorderRadiusConfig) => {
    const { topLeft, topRight, bottomRight, bottomLeft, unit } = config
    
    if (topLeft === topRight && topRight === bottomRight && bottomRight === bottomLeft) {
      return `${topLeft}${unit}`
    }
    
    return `${topLeft}${unit} ${topRight}${unit} ${bottomRight}${unit} ${bottomLeft}${unit}`
  }, [])

  const radiusCSS = useMemo(() => generateRadiusCSS(radiusConfig), [radiusConfig, generateRadiusCSS])

  const updateRadius = useCallback((corner: keyof Pick<BorderRadiusConfig, 'topLeft' | 'topRight' | 'bottomRight' | 'bottomLeft'>, value: number) => {
    setRadiusConfig(prev => {
      if (prev.syncCorners) {
        return {
          ...prev,
          topLeft: value,
          topRight: value,
          bottomRight: value,
          bottomLeft: value
        }
      } else {
        return {
          ...prev,
          [corner]: value
        }
      }
    })
  }, [])

  const updateUnit = useCallback((unit: BorderRadiusConfig['unit']) => {
    setRadiusConfig(prev => ({ ...prev, unit }))
  }, [])

  const toggleSync = useCallback(() => {
    setRadiusConfig(prev => ({ ...prev, syncCorners: !prev.syncCorners }))
  }, [])

  const applyPreset = useCallback((preset: PresetRadius) => {
    setRadiusConfig(prev => ({
      ...prev,
      ...preset.config
    }))
    toast.success(`已应用预设: ${preset.name}`)
  }, [])

  const resetRadius = useCallback(() => {
    setRadiusConfig({
      topLeft: 8,
      topRight: 8,
      bottomRight: 8,
      bottomLeft: 8,
      unit: 'px',
      syncCorners: true
    })
    toast.success('已重置圆角设置')
  }, [])

  const generateRandomRadius = useCallback(() => {
    const sync = Math.random() > 0.5
    const baseValue = Math.floor(Math.random() * 50)
    
    if (sync) {
      setRadiusConfig(prev => ({
        ...prev,
        topLeft: baseValue,
        topRight: baseValue,
        bottomRight: baseValue,
        bottomLeft: baseValue,
        syncCorners: true
      }))
    } else {
      setRadiusConfig(prev => ({
        ...prev,
        topLeft: Math.floor(Math.random() * 50),
        topRight: Math.floor(Math.random() * 50),
        bottomRight: Math.floor(Math.random() * 50),
        bottomLeft: Math.floor(Math.random() * 50),
        syncCorners: false
      }))
    }
    toast.success('已生成随机圆角')
  }, [])

  const copyCSS = useCallback(() => {
    let cssCode = ''
    
    switch (exportFormat) {
      case 'css':
        cssCode = `.rounded-element {\n  border-radius: ${radiusCSS};\n}`
        break
      case 'scss':
        cssCode = `$border-radius: ${radiusCSS};\n\n.rounded-element {\n  border-radius: $border-radius;\n}`
        break
      case 'tailwind':
        // 简化的Tailwind转换
        const { topLeft, topRight, bottomRight, bottomLeft, unit } = radiusConfig
        if (topLeft === topRight && topRight === bottomRight && bottomRight === bottomLeft) {
          const standardValues = { 0: 'rounded-none', 2: 'rounded-sm', 4: 'rounded', 6: 'rounded-md', 8: 'rounded-lg', 12: 'rounded-xl', 16: 'rounded-2xl', 24: 'rounded-3xl' }
          const tailwindClass = standardValues[topLeft as keyof typeof standardValues] || `rounded-[${topLeft}${unit}]`
          cssCode = `<div class="${tailwindClass}">\n  Content here\n</div>`
        } else {
          cssCode = `<div class="rounded-[${topLeft}${unit}_${topRight}${unit}_${bottomRight}${unit}_${bottomLeft}${unit}]">\n  Content here\n</div>`
        }
        break
      case 'js':
        cssCode = `const borderRadiusStyle = {\n  borderRadius: '${radiusCSS}'\n};\n\n// React usage\n<div style={borderRadiusStyle}>\n  Content here\n</div>`
        break
    }
    
    navigator.clipboard.writeText(cssCode)
    toast.success('代码已复制到剪贴板')
  }, [radiusCSS, exportFormat, radiusConfig])

  const downloadCSS = useCallback(() => {
    const cssContent = `.rounded-element {\n  border-radius: ${radiusCSS};\n}`
    const blob = new Blob([cssContent], { type: 'text/css' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'border-radius.css'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('CSS文件已下载')
  }, [radiusCSS])

  const exportPNG = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = 400
    canvas.height = 300

    // 绘制背景
    ctx.fillStyle = backgroundColor
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 绘制网格
    if (showGrid) {
      ctx.strokeStyle = 'rgba(0,0,0,0.1)'
      ctx.lineWidth = 1
      for (let x = 0; x < canvas.width; x += 20) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, canvas.height)
        ctx.stroke()
      }
      for (let y = 0; y < canvas.height; y += 20) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(canvas.width, y)
        ctx.stroke()
      }
    }

    // 绘制圆角元素
    const x = (canvas.width - elementSize.width) / 2
    const y = (canvas.height - elementSize.height) / 2
    const { topLeft, topRight, bottomRight, bottomLeft, unit } = radiusConfig
    
    // 转换单位为像素
    const pixelUnit = unit === '%' ? Math.min(elementSize.width, elementSize.height) / 100 : 1
    const tl = topLeft * pixelUnit
    const tr = topRight * pixelUnit
    const br = bottomRight * pixelUnit
    const bl = bottomLeft * pixelUnit

    ctx.fillStyle = elementColor
    ctx.beginPath()
    ctx.moveTo(x + tl, y)
    ctx.arcTo(x + elementSize.width, y, x + elementSize.width, y + elementSize.height, tr)
    ctx.arcTo(x + elementSize.width, y + elementSize.height, x, y + elementSize.height, br)
    ctx.arcTo(x, y + elementSize.height, x, y, bl)
    ctx.arcTo(x, y, x + elementSize.width, y, tl)
    ctx.closePath()
    ctx.fill()

    // 导出
    canvas.toBlob(blob => {
      if (blob) {
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'border-radius-preview.png'
        a.click()
        URL.revokeObjectURL(url)
        toast.success('预览图已导出')
      }
    })
  }, [backgroundColor, elementColor, elementSize, radiusConfig, showGrid])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          CSS圆角生成器
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          可视化设计border-radius效果，支持独立角度控制、预设库和实时预览
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 预览区域 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                实时预览
              </CardTitle>
              <div className="flex items-center gap-2">
                <Switch
                  id="show-grid"
                  checked={showGrid}
                  onCheckedChange={setShowGrid}
                />
                <Label htmlFor="show-grid" className="text-sm">网格</Label>
                <Switch
                  id="show-guides"
                  checked={showGuides}
                  onCheckedChange={setShowGuides}
                />
                <Label htmlFor="show-guides" className="text-sm">辅助线</Label>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 环境设置 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>背景色</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="w-12 h-8 p-0 border-0"
                  />
                  <Input
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="flex-1 text-sm"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label>元素色</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="color"
                    value={elementColor}
                    onChange={(e) => setElementColor(e.target.value)}
                    className="w-12 h-8 p-0 border-0"
                  />
                  <Input
                    value={elementColor}
                    onChange={(e) => setElementColor(e.target.value)}
                    className="flex-1 text-sm"
                  />
                </div>
              </div>
            </div>

            {/* 尺寸设置 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>宽度: {elementSize.width}px</Label>
                <Slider
                  value={[elementSize.width]}
                  onValueChange={([value]) => setElementSize(prev => ({ ...prev, width: value }))}
                  min={100}
                  max={300}
                  step={10}
                />
              </div>
              <div className="space-y-2">
                <Label>高度: {elementSize.height}px</Label>
                <Slider
                  value={[elementSize.height]}
                  onValueChange={([value]) => setElementSize(prev => ({ ...prev, height: value }))}
                  min={80}
                  max={250}
                  step={10}
                />
              </div>
            </div>

            {/* 预览容器 */}
            <div 
              className="relative w-full h-64 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 overflow-hidden flex items-center justify-center"
              style={{ 
                backgroundColor,
                backgroundImage: showGrid ? 
                  'radial-gradient(circle, rgba(0,0,0,0.1) 1px, transparent 1px)' : 'none',
                backgroundSize: showGrid ? '20px 20px' : 'auto'
              }}
            >
              <div
                className="relative transition-all duration-300"
                style={{
                  width: `${elementSize.width}px`,
                  height: `${elementSize.height}px`,
                  backgroundColor: elementColor,
                  borderRadius: radiusCSS,
                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                }}
              >
                {/* 角度指示器 */}
                {showGuides && (
                  <>
                    <div className="absolute -top-2 -left-2 w-4 h-4 border-t-2 border-l-2 border-blue-500 rounded-tl-lg" />
                    <div className="absolute -top-2 -right-2 w-4 h-4 border-t-2 border-r-2 border-green-500 rounded-tr-lg" />
                    <div className="absolute -bottom-2 -right-2 w-4 h-4 border-b-2 border-r-2 border-orange-500 rounded-br-lg" />
                    <div className="absolute -bottom-2 -left-2 w-4 h-4 border-b-2 border-l-2 border-purple-500 rounded-bl-lg" />
                  </>
                )}
              </div>
            </div>

            {/* 当前CSS */}
            <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
              <Label className="text-sm font-medium">当前CSS:</Label>
              <code className="block mt-1 text-sm font-mono break-all">
                border-radius: {radiusCSS};
              </code>
            </div>
          </CardContent>
        </Card>

        {/* 控制面板 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                圆角设置
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={generateRandomRadius}>
                  <Grid className="h-4 w-4 mr-1" />
                  随机
                </Button>
                <Button variant="outline" size="sm" onClick={resetRadius}>
                  <RotateCcw className="h-4 w-4 mr-1" />
                  重置
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 单位和同步设置 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Label>单位:</Label>
                <Select value={radiusConfig.unit} onValueChange={updateUnit}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="px">px</SelectItem>
                    <SelectItem value="%">%</SelectItem>
                    <SelectItem value="rem">rem</SelectItem>
                    <SelectItem value="em">em</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  id="sync-corners"
                  checked={radiusConfig.syncCorners}
                  onCheckedChange={toggleSync}
                />
                <Label htmlFor="sync-corners" className="text-sm">同步角度</Label>
              </div>
            </div>

            {/* 角度控制 */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {/* 左上角 */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 border-t-2 border-l-2 border-blue-500 rounded-tl-lg" />
                    <Label className="text-sm">左上角</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[radiusConfig.topLeft]}
                      onValueChange={([value]) => updateRadius('topLeft', value)}
                      min={0}
                      max={radiusConfig.unit === '%' ? 50 : 100}
                      step={radiusConfig.unit === '%' ? 1 : 1}
                      className="flex-1"
                    />
                    <Input
                      type="number"
                      value={radiusConfig.topLeft}
                      onChange={(e) => updateRadius('topLeft', parseInt(e.target.value) || 0)}
                      className="w-16 text-xs"
                    />
                    <span className="text-xs text-gray-500">{radiusConfig.unit}</span>
                  </div>
                </div>

                {/* 右上角 */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 border-t-2 border-r-2 border-green-500 rounded-tr-lg" />
                    <Label className="text-sm">右上角</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[radiusConfig.topRight]}
                      onValueChange={([value]) => updateRadius('topRight', value)}
                      min={0}
                      max={radiusConfig.unit === '%' ? 50 : 100}
                      step={1}
                      className="flex-1"
                      disabled={radiusConfig.syncCorners}
                    />
                    <Input
                      type="number"
                      value={radiusConfig.topRight}
                      onChange={(e) => updateRadius('topRight', parseInt(e.target.value) || 0)}
                      className="w-16 text-xs"
                      disabled={radiusConfig.syncCorners}
                    />
                    <span className="text-xs text-gray-500">{radiusConfig.unit}</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {/* 左下角 */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 border-b-2 border-l-2 border-purple-500 rounded-bl-lg" />
                    <Label className="text-sm">左下角</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[radiusConfig.bottomLeft]}
                      onValueChange={([value]) => updateRadius('bottomLeft', value)}
                      min={0}
                      max={radiusConfig.unit === '%' ? 50 : 100}
                      step={1}
                      className="flex-1"
                      disabled={radiusConfig.syncCorners}
                    />
                    <Input
                      type="number"
                      value={radiusConfig.bottomLeft}
                      onChange={(e) => updateRadius('bottomLeft', parseInt(e.target.value) || 0)}
                      className="w-16 text-xs"
                      disabled={radiusConfig.syncCorners}
                    />
                    <span className="text-xs text-gray-500">{radiusConfig.unit}</span>
                  </div>
                </div>

                {/* 右下角 */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 border-b-2 border-r-2 border-orange-500 rounded-br-lg" />
                    <Label className="text-sm">右下角</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[radiusConfig.bottomRight]}
                      onValueChange={([value]) => updateRadius('bottomRight', value)}
                      min={0}
                      max={radiusConfig.unit === '%' ? 50 : 100}
                      step={1}
                      className="flex-1"
                      disabled={radiusConfig.syncCorners}
                    />
                    <Input
                      type="number"
                      value={radiusConfig.bottomRight}
                      onChange={(e) => updateRadius('bottomRight', parseInt(e.target.value) || 0)}
                      className="w-16 text-xs"
                      disabled={radiusConfig.syncCorners}
                    />
                    <span className="text-xs text-gray-500">{radiusConfig.unit}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 可视化角度调节器 */}
            <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Label className="text-sm font-medium mb-3 block">可视化调节</Label>
              <div className="relative w-32 h-24 mx-auto border border-gray-300 rounded" style={{ borderRadius: radiusCSS }}>
                <div className="absolute -top-2 -left-2">
                  <button
                    className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-lg cursor-pointer hover:scale-110 transition-transform"
                    onClick={() => updateRadius('topLeft', Math.min((radiusConfig.topLeft + 5), radiusConfig.unit === '%' ? 50 : 100))}
                  />
                </div>
                <div className="absolute -top-2 -right-2">
                  <button
                    className="w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-lg cursor-pointer hover:scale-110 transition-transform"
                    onClick={() => updateRadius('topRight', Math.min((radiusConfig.topRight + 5), radiusConfig.unit === '%' ? 50 : 100))}
                    disabled={radiusConfig.syncCorners}
                  />
                </div>
                <div className="absolute -bottom-2 -right-2">
                  <button
                    className="w-4 h-4 bg-orange-500 rounded-full border-2 border-white shadow-lg cursor-pointer hover:scale-110 transition-transform"
                    onClick={() => updateRadius('bottomRight', Math.min((radiusConfig.bottomRight + 5), radiusConfig.unit === '%' ? 50 : 100))}
                    disabled={radiusConfig.syncCorners}
                  />
                </div>
                <div className="absolute -bottom-2 -left-2">
                  <button
                    className="w-4 h-4 bg-purple-500 rounded-full border-2 border-white shadow-lg cursor-pointer hover:scale-110 transition-transform"
                    onClick={() => updateRadius('bottomLeft', Math.min((radiusConfig.bottomLeft + 5), radiusConfig.unit === '%' ? 50 : 100))}
                    disabled={radiusConfig.syncCorners}
                  />
                </div>
              </div>
              <p className="text-xs text-gray-500 text-center mt-2">点击角落调节圆角</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 预设和导出 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 预设库 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              圆角预设
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-3">
              {presetRadiuses.map((preset, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-3 flex flex-col items-center gap-2"
                  onClick={() => applyPreset(preset)}
                >
                  <div 
                    className="w-12 h-8 bg-blue-100 border border-blue-300"
                    style={{
                      borderRadius: `${preset.config.topLeft}px ${preset.config.topRight}px ${preset.config.bottomRight}px ${preset.config.bottomLeft}px`
                    }}
                  />
                  <div className="text-center">
                    <div className="font-medium text-xs">{preset.name}</div>
                    <div className="text-xs text-gray-500">{preset.description}</div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 代码导出 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              代码导出
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <Label>格式:</Label>
              <Select value={exportFormat} onValueChange={setExportFormat}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="css">CSS</SelectItem>
                  <SelectItem value="tailwind">Tailwind</SelectItem>
                  <SelectItem value="scss">SCSS</SelectItem>
                  <SelectItem value="js">JavaScript</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs defaultValue="code" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="code">代码</TabsTrigger>
                <TabsTrigger value="info">信息</TabsTrigger>
              </TabsList>
              
              <TabsContent value="code" className="space-y-3">
                <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
                  <code className="text-sm break-all">
                    {exportFormat === 'css' && `.rounded-element {\n  border-radius: ${radiusCSS};\n}`}
                    {exportFormat === 'scss' && `$border-radius: ${radiusCSS};\n\n.rounded-element {\n  border-radius: $border-radius;\n}`}
                    {exportFormat === 'tailwind' && 
                      (radiusConfig.topLeft === radiusConfig.topRight && 
                       radiusConfig.topRight === radiusConfig.bottomRight && 
                       radiusConfig.bottomRight === radiusConfig.bottomLeft
                        ? `<div class="rounded-[${radiusConfig.topLeft}${radiusConfig.unit}]">`
                        : `<div class="rounded-[${radiusCSS}]">`
                      )
                    }
                    {exportFormat === 'js' && `const style = {\n  borderRadius: '${radiusCSS}'\n};`}
                  </code>
                </div>
                <div className="flex gap-2">
                  <Button onClick={copyCSS} className="flex-1">
                    <Copy className="h-4 w-4 mr-2" />
                    复制代码
                  </Button>
                  <Button onClick={downloadCSS} variant="outline" className="flex-1">
                    <Download className="h-4 w-4 mr-2" />
                    下载CSS
                  </Button>
                </div>
              </TabsContent>
              
              <TabsContent value="info" className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">单位类型:</span>
                    <Badge variant="outline">{radiusConfig.unit}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">同步角度:</span>
                    <Badge variant={radiusConfig.syncCorners ? "default" : "outline"}>
                      {radiusConfig.syncCorners ? '是' : '否'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">最大圆角:</span>
                    <Badge variant="outline">
                      {Math.max(radiusConfig.topLeft, radiusConfig.topRight, radiusConfig.bottomRight, radiusConfig.bottomLeft)}{radiusConfig.unit}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">均匀圆角:</span>
                    <Badge variant={
                      radiusConfig.topLeft === radiusConfig.topRight && 
                      radiusConfig.topRight === radiusConfig.bottomRight && 
                      radiusConfig.bottomRight === radiusConfig.bottomLeft ? "default" : "outline"
                    }>
                      {radiusConfig.topLeft === radiusConfig.topRight && 
                       radiusConfig.topRight === radiusConfig.bottomRight && 
                       radiusConfig.bottomRight === radiusConfig.bottomLeft ? '是' : '否'}
                    </Badge>
                  </div>
                </div>
                
                <Separator />
                
                <Button onClick={exportPNG} variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  导出预览图(PNG)
                </Button>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* 隐藏的canvas用于导出 */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  )
}