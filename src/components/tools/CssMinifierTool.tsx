'use client'

import { useState } from 'react'
import { FileCode, Copy, Check, Download, Upload, Minimize2, Settings, Info, AlertCircle } from 'lucide-react'

interface MinifierOptions {
  removeComments: boolean
  removeWhitespace: boolean
  removeEmptyRules: boolean
  mergeSelectors: boolean
  mergeProperties: boolean
  removeUnits: boolean
  convertColors: boolean
  removeQuotes: boolean
  removeSemicolons: boolean
}

export default function CssMinifierTool() {
  const [inputCss, setInputCss] = useState('')
  const [outputCss, setOutputCss] = useState('')
  const [isMinifying, setIsMinifying] = useState(false)
  const [copied, setCopied] = useState(false)
  const [showOptions, setShowOptions] = useState(false)
  const [options, setOptions] = useState<MinifierOptions>({
    removeComments: true,
    removeWhitespace: true,
    removeEmptyRules: true,
    mergeSelectors: false,
    mergeProperties: false,
    removeUnits: true,
    convertColors: true,
    removeQuotes: true,
    removeSemicolons: true,
  })

  // 计算压缩率
  const calculateCompression = () => {
    if (!inputCss || !outputCss) return 0
    const originalSize = new Blob([inputCss]).size
    const minifiedSize = new Blob([outputCss]).size
    return Math.round((1 - minifiedSize / originalSize) * 100)
  }

  // CSS 压缩函数
  const minifyCss = () => {
    if (!inputCss.trim()) return

    setIsMinifying(true)
    
    setTimeout(() => {
      try {
        let result = inputCss

        // 1. 删除注释
        if (options.removeComments) {
          // 删除多行注释
          result = result.replace(/\/\*[\s\S]*?\*\//g, '')
          // 删除单行注释（CSS 不支持 // 注释，但有些预处理器支持）
          result = result.replace(/\/\/.*$/gm, '')
        }

        // 2. 删除空规则
        if (options.removeEmptyRules) {
          result = result.replace(/[^{}]+{\s*}/g, '')
        }

        // 3. 删除不必要的空白
        if (options.removeWhitespace) {
          // 删除多余的空格
          result = result.replace(/\s+/g, ' ')
          // 删除选择器和 { 之间的空格
          result = result.replace(/\s*{\s*/g, '{')
          // 删除 } 前后的空格
          result = result.replace(/\s*}\s*/g, '}')
          // 删除属性冒号后的空格
          result = result.replace(/:\s+/g, ':')
          // 删除分号后的空格
          result = result.replace(/;\s+/g, ';')
          // 删除逗号后的空格
          result = result.replace(/,\s+/g, ',')
          // 删除 > + ~ 等选择器符号周围的空格
          result = result.replace(/\s*([>+~])\s*/g, '$1')
          // 删除开头和结尾的空白
          result = result.trim()
        }

        // 4. 删除最后一个分号
        if (options.removeSemicolons) {
          result = result.replace(/;}/g, '}')
        }

        // 5. 删除零单位
        if (options.removeUnits) {
          // 0px -> 0, 0em -> 0, etc.
          result = result.replace(/:\s*0(px|em|rem|%|vh|vw|vmin|vmax|ex|ch|cm|mm|in|pt|pc)/gi, ':0')
          // 删除小数点前的 0 (0.5 -> .5)
          result = result.replace(/:\s*0\.(\d+)/g, ':.$1')
        }

        // 6. 转换颜色值
        if (options.convertColors) {
          // 转换 rgb 到十六进制
          result = result.replace(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/g, (match, r, g, b) => {
            const hex = '#' + [r, g, b].map(x => {
              const hex = parseInt(x).toString(16)
              return hex.length === 1 ? '0' + hex : hex
            }).join('')
            return hex
          })

          // 缩短十六进制颜色 (#ffffff -> #fff)
          result = result.replace(/#([0-9a-f])\1([0-9a-f])\2([0-9a-f])\3/gi, '#$1$2$3')

          // 常见颜色名称转换
          const colorMap: { [key: string]: string } = {
            '#f00': 'red',
            '#ff0000': 'red',
            '#000': 'black',
            '#000000': 'black',
            '#fff': 'white',
            '#ffffff': 'white',
          }
          
          Object.entries(colorMap).forEach(([hex, name]) => {
            if (name.length < hex.length) {
              result = result.replace(new RegExp(hex, 'gi'), name)
            }
          })
        }

        // 7. 删除引号（在安全的情况下）
        if (options.removeQuotes) {
          // 删除 url() 中不必要的引号
          result = result.replace(/url\(["']([^"']+)["']\)/g, 'url($1)')
          // 删除字体名称中不必要的引号（单个单词）
          result = result.replace(/font-family:\s*["'](\w+)["']/g, 'font-family:$1')
        }

        // 8. 合并相同选择器（简单实现）
        if (options.mergeSelectors) {
          const rules: { [selector: string]: string[] } = {}
          const ruleRegex = /([^{]+){([^}]+)}/g
          let match

          while ((match = ruleRegex.exec(result)) !== null) {
            const selector = match[1].trim()
            const properties = match[2].trim()
            
            if (!rules[selector]) {
              rules[selector] = []
            }
            rules[selector].push(properties)
          }

          // 重建 CSS
          result = Object.entries(rules)
            .map(([selector, props]) => `${selector}{${props.join(';')}${props.length > 0 ? ';' : ''}}`)
            .join('')
        }

        // 9. 合并相同属性（保留最后一个）
        if (options.mergeProperties) {
          result = result.replace(/([^{]+){([^}]+)}/g, (match, selector, properties) => {
            const propMap = new Map<string, string>()
            const props = properties.split(';').filter((p: string) => p.trim())
            
            props.forEach((prop: string) => {
              const [key, value] = prop.split(':').map((s: string) => s.trim())
              if (key && value) {
                propMap.set(key, value)
              }
            })
            
            const mergedProps = Array.from(propMap.entries())
              .map(([k, v]) => `${k}:${v}`)
              .join(';')
            
            return `${selector.trim()}{${mergedProps}${mergedProps ? ';' : ''}}`
          })
        }

        setOutputCss(result)
      } catch (error) {
        setOutputCss(`/* 压缩失败：${error instanceof Error ? error.message : '未知错误'} */`)
      } finally {
        setIsMinifying(false)
      }
    }, 300)
  }

  // 复制到剪贴板
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(outputCss)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  // 下载压缩后的 CSS
  const downloadCss = () => {
    const blob = new Blob([outputCss], { type: 'text/css' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'style.min.css'
    a.click()
    URL.revokeObjectURL(url)
  }

  // 上传 CSS 文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputCss(content)
    }
    reader.readAsText(file)
  }

  // 加载示例
  const loadExample = () => {
    setInputCss(`/* Header Styles */
.header {
  background-color: #ffffff;
  padding: 20px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  font-size: 24px;
  font-weight: bold;
  color: #333333;
  text-decoration: none;
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-menu li {
  margin-left: 30px;
}

.nav-menu a {
  color: #666666;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: #0066cc;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 10px 20px;
  background-color: #0066cc;
  color: #ffffff;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.btn:hover {
  background-color: #0052a3;
}

/* Empty rule that should be removed */
.empty-rule {
  
}

/* Media Query */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .header .container {
    padding: 0 15px;
  }
}`)
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">CSS 压缩工具</h2>
        <p className="text-gray-600">
          压缩和优化 CSS 代码，减小文件体积，提高网页加载速度
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">原始 CSS</label>
            <div className="flex gap-2">
              <label className="text-sm text-blue-500 hover:text-blue-600 cursor-pointer">
                <input
                  type="file"
                  accept=".css"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <span className="flex items-center gap-1">
                  <Upload className="w-4 h-4" />
                  上传文件
                </span>
              </label>
              <button
                onClick={loadExample}
                className="text-sm text-blue-500 hover:text-blue-600"
              >
                加载示例
              </button>
            </div>
          </div>
          <textarea
            value={inputCss}
            onChange={(e) => setInputCss(e.target.value)}
            placeholder="请输入要压缩的 CSS 代码..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div className="mt-2 text-sm text-gray-500">
            大小：{new Blob([inputCss]).size} 字节
          </div>
        </div>

        {/* 输出区域 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">压缩后的 CSS</label>
            <div className="flex gap-2">
              {outputCss && (
                <>
                  <button
                    onClick={downloadCss}
                    className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                  >
                    <Download className="w-4 h-4" />
                    下载
                  </button>
                  <button
                    onClick={copyToClipboard}
                    className="text-sm text-blue-500 hover:text-blue-600 flex items-center gap-1"
                  >
                    {copied ? (
                      <>
                        <Check className="w-4 h-4" />
                        已复制
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4" />
                        复制
                      </>
                    )}
                  </button>
                </>
              )}
            </div>
          </div>
          <textarea
            value={outputCss}
            readOnly
            placeholder="压缩后的 CSS 将显示在这里..."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none bg-gray-50"
          />
          <div className="mt-2 flex justify-between text-sm text-gray-500">
            <span>大小：{new Blob([outputCss]).size} 字节</span>
            {outputCss && (
              <span className="text-green-600 font-medium">
                压缩率：{calculateCompression()}%
              </span>
            )}
          </div>
        </div>
      </div>

      {/* 压缩选项 */}
      <div className="mt-6 border-t pt-6">
        <button
          onClick={() => setShowOptions(!showOptions)}
          className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
        >
          <Settings className="w-4 h-4" />
          压缩选项
          <span className="text-gray-400">{showOptions ? '▼' : '▶'}</span>
        </button>

        {showOptions && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.removeComments}
                onChange={(e) => setOptions({ ...options, removeComments: e.target.checked })}
                className="rounded text-blue-500 focus:ring-blue-500"
              />
              <span className="text-sm">删除注释</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.removeWhitespace}
                onChange={(e) => setOptions({ ...options, removeWhitespace: e.target.checked })}
                className="rounded text-blue-500 focus:ring-blue-500"
              />
              <span className="text-sm">删除空白字符</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.removeEmptyRules}
                onChange={(e) => setOptions({ ...options, removeEmptyRules: e.target.checked })}
                className="rounded text-blue-500 focus:ring-blue-500"
              />
              <span className="text-sm">删除空规则</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.removeUnits}
                onChange={(e) => setOptions({ ...options, removeUnits: e.target.checked })}
                className="rounded text-blue-500 focus:ring-blue-500"
              />
              <span className="text-sm">删除零值单位</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.convertColors}
                onChange={(e) => setOptions({ ...options, convertColors: e.target.checked })}
                className="rounded text-blue-500 focus:ring-blue-500"
              />
              <span className="text-sm">优化颜色值</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.removeQuotes}
                onChange={(e) => setOptions({ ...options, removeQuotes: e.target.checked })}
                className="rounded text-blue-500 focus:ring-blue-500"
              />
              <span className="text-sm">删除不必要的引号</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.removeSemicolons}
                onChange={(e) => setOptions({ ...options, removeSemicolons: e.target.checked })}
                className="rounded text-blue-500 focus:ring-blue-500"
              />
              <span className="text-sm">删除最后的分号</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.mergeSelectors}
                onChange={(e) => setOptions({ ...options, mergeSelectors: e.target.checked })}
                className="rounded text-blue-500 focus:ring-blue-500"
              />
              <span className="text-sm">合并相同选择器</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={options.mergeProperties}
                onChange={(e) => setOptions({ ...options, mergeProperties: e.target.checked })}
                className="rounded text-blue-500 focus:ring-blue-500"
              />
              <span className="text-sm">合并重复属性</span>
            </label>
          </div>
        )}
      </div>

      {/* 压缩按钮 */}
      <button
        onClick={minifyCss}
        disabled={!inputCss.trim() || isMinifying}
        className="mt-6 w-full py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
      >
        <Minimize2 className="w-5 h-5" />
        {isMinifying ? '压缩中...' : '压缩 CSS'}
      </button>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持标准 CSS 语法的压缩和优化</li>
              <li>自动删除注释、空白和不必要的字符</li>
              <li>智能优化颜色值和单位</li>
              <li>可选择性地合并相同的选择器和属性</li>
              <li>保持 CSS 功能不变的前提下减小文件体积</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 优化建议 */}
      <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-yellow-900">
            <h3 className="font-semibold mb-1">优化建议</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>压缩前建议保留原始文件备份</li>
              <li>某些高级选项可能影响特定的 CSS 功能</li>
              <li>建议在生产环境使用压缩后的文件</li>
              <li>配合 Gzip 压缩可获得更好的效果</li>
              <li>对于大型项目，建议使用专业的构建工具</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 压缩技巧 */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">压缩技巧</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
          <div>
            <strong>删除注释：</strong>
            <span className="text-gray-600">移除所有 CSS 注释</span>
          </div>
          <div>
            <strong>删除空白：</strong>
            <span className="text-gray-600">移除不必要的空格和换行</span>
          </div>
          <div>
            <strong>优化颜色：</strong>
            <span className="text-gray-600">#ffffff → #fff, rgb(255,0,0) → red</span>
          </div>
          <div>
            <strong>删除零单位：</strong>
            <span className="text-gray-600">0px → 0, 0.5em → .5em</span>
          </div>
          <div>
            <strong>合并选择器：</strong>
            <span className="text-gray-600">相同选择器的规则合并</span>
          </div>
          <div>
            <strong>删除引号：</strong>
            <span className="text-gray-600">url(&quot;img.png&quot;) → url(img.png)</span>
          </div>
        </div>
      </div>
    </div>
  )
}
