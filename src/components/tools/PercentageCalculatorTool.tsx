'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Percent, Calculator } from 'lucide-react';
import { ToolLayout } from '@/components/layout/ToolLayout';

type CalculationType = 'basic' | 'increase' | 'decrease' | 'difference' | 'ratio';

export default function PercentageCalculatorTool() {
  const [calculationType, setCalculationType] = useState<CalculationType>('basic');
  
  // 基础百分比计算
  const [basicValue, setBasicValue] = useState('');
  const [basicPercent, setBasicPercent] = useState('');
  const [basicResult, setBasicResult] = useState<string>('');
  
  // 增减百分比
  const [originalValue, setOriginalValue] = useState('');
  const [changePercent, setChangePercent] = useState('');
  const [changeResult, setChangeResult] = useState<string>('');
  
  // 百分比差异
  const [value1, setValue1] = useState('');
  const [value2, setValue2] = useState('');
  const [differenceResult, setDifferenceResult] = useState<string>('');
  
  // 比例计算
  const [ratioValue1, setRatioValue1] = useState('');
  const [ratioValue2, setRatioValue2] = useState('');
  const [ratioResult, setRatioResult] = useState<string>('');

  // 基础百分比计算
  const calculateBasicPercentage = () => {
    const value = parseFloat(basicValue);
    const percent = parseFloat(basicPercent);
    
    if (isNaN(value) || isNaN(percent)) {
      setBasicResult('请输入有效的数字');
      return;
    }
    
    const result = (value * percent) / 100;
    setBasicResult(`${basicValue} 的 ${basicPercent}% = ${result.toFixed(2)}`);
  };

  // 增减百分比计算
  const calculateChange = (type: 'increase' | 'decrease') => {
    const value = parseFloat(originalValue);
    const percent = parseFloat(changePercent);
    
    if (isNaN(value) || isNaN(percent)) {
      setChangeResult('请输入有效的数字');
      return;
    }
    
    const change = (value * percent) / 100;
    const result = type === 'increase' ? value + change : value - change;
    const changeText = type === 'increase' ? '增加' : '减少';
    
    setChangeResult(
      `${originalValue} ${changeText} ${changePercent}% = ${result.toFixed(2)}\n` +
      `变化量：${type === 'increase' ? '+' : '-'}${Math.abs(change).toFixed(2)}`
    );
  };

  // 百分比差异计算
  const calculateDifference = () => {
    const val1 = parseFloat(value1);
    const val2 = parseFloat(value2);
    
    if (isNaN(val1) || isNaN(val2)) {
      setDifferenceResult('请输入有效的数字');
      return;
    }
    
    if (val1 === 0) {
      setDifferenceResult('第一个值不能为 0');
      return;
    }
    
    const difference = val2 - val1;
    const percentDifference = (difference / val1) * 100;
    
    setDifferenceResult(
      `从 ${value1} 到 ${value2} 的变化：\n` +
      `绝对差异：${difference.toFixed(2)}\n` +
      `百分比变化：${percentDifference > 0 ? '+' : ''}${percentDifference.toFixed(2)}%\n` +
      `${val2 > val1 ? '增长' : '减少'} ${Math.abs(percentDifference).toFixed(2)}%`
    );
  };

  // 比例计算
  const calculateRatio = () => {
    const val1 = parseFloat(ratioValue1);
    const val2 = parseFloat(ratioValue2);
    
    if (isNaN(val1) || isNaN(val2)) {
      setRatioResult('请输入有效的数字');
      return;
    }
    
    if (val2 === 0) {
      setRatioResult('第二个值不能为 0');
      return;
    }
    
    const percentage = (val1 / val2) * 100;
    
    setRatioResult(
      `${ratioValue1} 占 ${ratioValue2} 的比例：\n` +
      `${percentage.toFixed(2)}%\n` +
      `比值：${(val1 / val2).toFixed(4)}`
    );
  };

  const clearAll = () => {
    setBasicValue('');
    setBasicPercent('');
    setBasicResult('');
    setOriginalValue('');
    setChangePercent('');
    setChangeResult('');
    setValue1('');
    setValue2('');
    setDifferenceResult('');
    setRatioValue1('');
    setRatioValue2('');
    setRatioResult('');
  };

  return (
    <ToolLayout
      title="百分比计算器"
      description="计算百分比、增减率、比例等各种百分比相关运算"
    >
      <div className="space-y-6">
        {/* 计算类型选择 */}
        <Card className="p-6">
          <Label className="text-base mb-4 block">选择计算类型</Label>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
            <Button
              variant={calculationType === 'basic' ? 'default' : 'outline'}
              onClick={() => setCalculationType('basic')}
              size="sm"
            >
              基础百分比
            </Button>
            <Button
              variant={calculationType === 'increase' ? 'default' : 'outline'}
              onClick={() => setCalculationType('increase')}
              size="sm"
            >
              增加百分比
            </Button>
            <Button
              variant={calculationType === 'decrease' ? 'default' : 'outline'}
              onClick={() => setCalculationType('decrease')}
              size="sm"
            >
              减少百分比
            </Button>
            <Button
              variant={calculationType === 'difference' ? 'default' : 'outline'}
              onClick={() => setCalculationType('difference')}
              size="sm"
            >
              百分比差异
            </Button>
            <Button
              variant={calculationType === 'ratio' ? 'default' : 'outline'}
              onClick={() => setCalculationType('ratio')}
              size="sm"
            >
              比例计算
            </Button>
          </div>
        </Card>

        {/* 基础百分比计算 */}
        {calculationType === 'basic' && (
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Percent className="w-5 h-5" />
              <h3 className="text-lg font-medium">基础百分比计算</h3>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="basic-value">数值</Label>
                  <Input
                    id="basic-value"
                    type="number"
                    value={basicValue}
                    onChange={(e) => setBasicValue(e.target.value)}
                    placeholder="输入数值"
                  />
                </div>
                <div>
                  <Label htmlFor="basic-percent">百分比 (%)</Label>
                  <Input
                    id="basic-percent"
                    type="number"
                    value={basicPercent}
                    onChange={(e) => setBasicPercent(e.target.value)}
                    placeholder="输入百分比"
                  />
                </div>
              </div>
              <Button onClick={calculateBasicPercentage} className="w-full">
                <Calculator className="w-4 h-4 mr-2" />
                计算
              </Button>
              {basicResult && (
                <div className="p-4 bg-muted rounded-md">
                  <p className="text-lg font-medium">{basicResult}</p>
                </div>
              )}
            </div>
          </Card>
        )}

        {/* 增加百分比 */}
        {calculationType === 'increase' && (
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Percent className="w-5 h-5" />
              <h3 className="text-lg font-medium">增加百分比计算</h3>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="increase-value">原始值</Label>
                  <Input
                    id="increase-value"
                    type="number"
                    value={originalValue}
                    onChange={(e) => setOriginalValue(e.target.value)}
                    placeholder="输入原始值"
                  />
                </div>
                <div>
                  <Label htmlFor="increase-percent">增加百分比 (%)</Label>
                  <Input
                    id="increase-percent"
                    type="number"
                    value={changePercent}
                    onChange={(e) => setChangePercent(e.target.value)}
                    placeholder="输入增加的百分比"
                  />
                </div>
              </div>
              <Button onClick={() => calculateChange('increase')} className="w-full">
                <Calculator className="w-4 h-4 mr-2" />
                计算增加后的值
              </Button>
              {changeResult && (
                <div className="p-4 bg-muted rounded-md">
                  <pre className="text-sm whitespace-pre-wrap">{changeResult}</pre>
                </div>
              )}
            </div>
          </Card>
        )}

        {/* 减少百分比 */}
        {calculationType === 'decrease' && (
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Percent className="w-5 h-5" />
              <h3 className="text-lg font-medium">减少百分比计算</h3>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="decrease-value">原始值</Label>
                  <Input
                    id="decrease-value"
                    type="number"
                    value={originalValue}
                    onChange={(e) => setOriginalValue(e.target.value)}
                    placeholder="输入原始值"
                  />
                </div>
                <div>
                  <Label htmlFor="decrease-percent">减少百分比 (%)</Label>
                  <Input
                    id="decrease-percent"
                    type="number"
                    value={changePercent}
                    onChange={(e) => setChangePercent(e.target.value)}
                    placeholder="输入减少的百分比"
                  />
                </div>
              </div>
              <Button onClick={() => calculateChange('decrease')} className="w-full">
                <Calculator className="w-4 h-4 mr-2" />
                计算减少后的值
              </Button>
              {changeResult && (
                <div className="p-4 bg-muted rounded-md">
                  <pre className="text-sm whitespace-pre-wrap">{changeResult}</pre>
                </div>
              )}
            </div>
          </Card>
        )}

        {/* 百分比差异 */}
        {calculationType === 'difference' && (
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Percent className="w-5 h-5" />
              <h3 className="text-lg font-medium">百分比差异计算</h3>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="diff-value1">起始值</Label>
                  <Input
                    id="diff-value1"
                    type="number"
                    value={value1}
                    onChange={(e) => setValue1(e.target.value)}
                    placeholder="输入起始值"
                  />
                </div>
                <div>
                  <Label htmlFor="diff-value2">结束值</Label>
                  <Input
                    id="diff-value2"
                    type="number"
                    value={value2}
                    onChange={(e) => setValue2(e.target.value)}
                    placeholder="输入结束值"
                  />
                </div>
              </div>
              <Button onClick={calculateDifference} className="w-full">
                <Calculator className="w-4 h-4 mr-2" />
                计算百分比变化
              </Button>
              {differenceResult && (
                <div className="p-4 bg-muted rounded-md">
                  <pre className="text-sm whitespace-pre-wrap">{differenceResult}</pre>
                </div>
              )}
            </div>
          </Card>
        )}

        {/* 比例计算 */}
        {calculationType === 'ratio' && (
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Percent className="w-5 h-5" />
              <h3 className="text-lg font-medium">比例计算</h3>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="ratio-value1">部分值</Label>
                  <Input
                    id="ratio-value1"
                    type="number"
                    value={ratioValue1}
                    onChange={(e) => setRatioValue1(e.target.value)}
                    placeholder="输入部分值"
                  />
                </div>
                <div>
                  <Label htmlFor="ratio-value2">总值</Label>
                  <Input
                    id="ratio-value2"
                    type="number"
                    value={ratioValue2}
                    onChange={(e) => setRatioValue2(e.target.value)}
                    placeholder="输入总值"
                  />
                </div>
              </div>
              <Button onClick={calculateRatio} className="w-full">
                <Calculator className="w-4 h-4 mr-2" />
                计算比例
              </Button>
              {ratioResult && (
                <div className="p-4 bg-muted rounded-md">
                  <pre className="text-sm whitespace-pre-wrap">{ratioResult}</pre>
                </div>
              )}
            </div>
          </Card>
        )}

        {/* 清空按钮 */}
        <div className="flex justify-center">
          <Button onClick={clearAll} variant="outline">
            清空所有
          </Button>
        </div>

        {/* 使用说明 */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">使用说明</h3>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>• <strong>基础百分比</strong>：计算一个数的百分之几是多少</p>
            <p>• <strong>增加百分比</strong>：计算增加一定百分比后的结果</p>
            <p>• <strong>减少百分比</strong>：计算减少一定百分比后的结果</p>
            <p>• <strong>百分比差异</strong>：计算两个数之间的百分比变化</p>
            <p>• <strong>比例计算</strong>：计算一个数占另一个数的百分比</p>
          </div>
        </Card>
      </div>
    </ToolLayout>
  );
}
