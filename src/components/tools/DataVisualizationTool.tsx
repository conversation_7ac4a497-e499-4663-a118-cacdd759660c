'use client'

import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Download, Upload, Settings, Info } from 'lucide-react'

interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string
  }[]
}

interface ChartConfig {
  type: 'bar' | 'line' | 'pie' | 'doughnut'
  title: string
  width: number
  height: number
  showLegend: boolean
  showGrid: boolean
}

export default function DataVisualizationTool() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [csvData, setCsvData] = useState('')
  const [chartData, setChartData] = useState<ChartData | null>(null)
  const [config, setConfig] = useState<ChartConfig>({
    type: 'bar',
    title: '数据可视化图表',
    width: 800,
    height: 400,
    showLegend: true,
    showGrid: true,
  })
  const [error, setError] = useState('')

  // 颜色方案
  const colorSchemes = useMemo(() => ({
    default: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#14B8A6', '#F97316'],
    pastel: ['#93C5FD', '#86EFAC', '#FDE047', '#FCA5A5', '#C4B5FD', '#FBCFE8', '#5EEAD4', '#FDBA74'],
    dark: ['#1E40AF', '#047857', '#B45309', '#991B1B', '#5B21B6', '#BE185D', '#0F766E', '#C2410C'],
  }), [])

  // 解析CSV数据
  const parseCSV = useCallback((text: string): ChartData | null => {
    try {
      const lines = text.trim().split('\n')
      if (lines.length < 2) {
        throw new Error('CSV数据至少需要包含标题行和一行数据')
      }

      const headers = lines[0].split(',').map(h => h.trim())
      const labels: string[] = []
      const datasets: { [key: string]: number[] } = {}

      // 初始化数据集
      headers.slice(1).forEach(header => {
        datasets[header] = []
      })

      // 解析数据行
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim())
        if (values.length !== headers.length) continue

        labels.push(values[0])
        for (let j = 1; j < values.length; j++) {
          const num = parseFloat(values[j])
          if (!isNaN(num)) {
            datasets[headers[j]].push(num)
          }
        }
      }

      // 转换为图表数据格式
      const chartDatasets = Object.entries(datasets).map(([label, data], index) => ({
        label,
        data,
        backgroundColor: config.type === 'line' 
          ? 'transparent' 
          : colorSchemes.default[index % colorSchemes.default.length],
        borderColor: colorSchemes.default[index % colorSchemes.default.length],
      }))

      return {
        labels,
        datasets: chartDatasets,
      }
    } catch (err) {
      throw new Error(`CSV解析错误: ${err instanceof Error ? err.message : '未知错误'}`)
    }
  }, [config.type, colorSchemes])

  // 绘制图表
  const drawChart = useCallback(() => {
    if (!canvasRef.current || !chartData) return

    const ctx = canvasRef.current.getContext('2d')
    if (!ctx) return

    // 清空画布
    ctx.clearRect(0, 0, config.width, config.height)

    // 设置画布尺寸
    canvasRef.current.width = config.width
    canvasRef.current.height = config.height

    // 计算绘图区域
    const padding = 60
    const chartWidth = config.width - padding * 2
    const chartHeight = config.height - padding * 2

    // 绘制标题
    ctx.font = 'bold 20px sans-serif'
    ctx.fillStyle = '#1F2937'
    ctx.textAlign = 'center'
    ctx.fillText(config.title, config.width / 2, 30)

    // 内联绘图函数以避免依赖问题
    const drawBarChart = (
      ctx: CanvasRenderingContext2D,
      data: ChartData,
      padding: number,
      width: number,
      height: number
    ) => {
      const barWidth = width / (data.labels.length * data.datasets.length + data.labels.length + 1)
      const maxValue = Math.max(...data.datasets.flatMap(d => d.data))
      const scale = height / maxValue

      // 绘制网格线
      if (config.showGrid) {
        ctx.strokeStyle = '#E5E7EB'
        ctx.lineWidth = 1
        for (let i = 0; i <= 5; i++) {
          const y = padding + height - (height / 5) * i
          ctx.beginPath()
          ctx.moveTo(padding, y)
          ctx.lineTo(padding + width, y)
          ctx.stroke()
        }
      }

      // 绘制柱子
      data.labels.forEach((label, labelIndex) => {
        data.datasets.forEach((dataset, datasetIndex) => {
          const value = dataset.data[labelIndex] || 0
          const x = padding + barWidth + labelIndex * (barWidth * (data.datasets.length + 1)) + datasetIndex * barWidth
          const barHeight = value * scale
          const y = padding + height - barHeight

          ctx.fillStyle = (typeof dataset.backgroundColor === 'string' ? dataset.backgroundColor : dataset.backgroundColor?.[0]) || '#3B82F6'
          ctx.fillRect(x, y, barWidth * 0.8, barHeight)
        })
      })

      // 绘制标签
      ctx.fillStyle = '#374151'
      ctx.font = '12px sans-serif'
      ctx.textAlign = 'center'
      data.labels.forEach((label, index) => {
        const x = padding + barWidth + index * (barWidth * (data.datasets.length + 1)) + (barWidth * data.datasets.length) / 2
        ctx.fillText(label, x, padding + height + 20)
      })
    }

    const drawLineChart = (
      ctx: CanvasRenderingContext2D,
      data: ChartData,
      padding: number,
      width: number,
      height: number
    ) => {
      const pointSpacing = width / (data.labels.length - 1)
      const maxValue = Math.max(...data.datasets.flatMap(d => d.data))
      const scale = height / maxValue

      // 绘制网格线
      if (config.showGrid) {
        ctx.strokeStyle = '#E5E7EB'
        ctx.lineWidth = 1
        for (let i = 0; i <= 5; i++) {
          const y = padding + height - (height / 5) * i
          ctx.beginPath()
          ctx.moveTo(padding, y)
          ctx.lineTo(padding + width, y)
          ctx.stroke()
        }
      }

      // 绘制每条线
      data.datasets.forEach((dataset) => {
        ctx.strokeStyle = dataset.borderColor || '#3B82F6'
        ctx.lineWidth = 2
        ctx.beginPath()

        dataset.data.forEach((value, index) => {
          const x = padding + index * pointSpacing
          const y = padding + height - value * scale

          if (index === 0) {
            ctx.moveTo(x, y)
          } else {
            ctx.lineTo(x, y)
          }
        })
        ctx.stroke()

        // 绘制数据点
        ctx.fillStyle = dataset.borderColor || '#3B82F6'
        dataset.data.forEach((value, index) => {
          const x = padding + index * pointSpacing
          const y = padding + height - value * scale
          ctx.beginPath()
          ctx.arc(x, y, 4, 0, Math.PI * 2)
          ctx.fill()
        })
      })

      // 绘制标签
      ctx.fillStyle = '#374151'
      ctx.font = '12px sans-serif'
      ctx.textAlign = 'center'
      data.labels.forEach((label, index) => {
        const x = padding + index * pointSpacing
        ctx.fillText(label, x, padding + height + 20)
      })
    }

    const drawPieChart = (
      ctx: CanvasRenderingContext2D,
      data: ChartData,
      padding: number,
      width: number,
      height: number,
      isDoughnut: boolean
    ) => {
      if (data.datasets.length === 0 || data.datasets[0].data.length === 0) return

      const centerX = padding + width / 2
      const centerY = padding + height / 2
      const radius = Math.min(width, height) / 2 - 20
      const innerRadius = isDoughnut ? radius * 0.5 : 0

      const values = data.datasets[0].data
      const total = values.reduce((sum, val) => sum + val, 0)
      let currentAngle = -Math.PI / 2

      values.forEach((value, index) => {
        const sliceAngle = (value / total) * Math.PI * 2
        
        // 绘制扇形
        ctx.beginPath()
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
        if (isDoughnut) {
          ctx.arc(centerX, centerY, innerRadius, currentAngle + sliceAngle, currentAngle, true)
        } else {
          ctx.lineTo(centerX, centerY)
        }
        ctx.closePath()
        
        ctx.fillStyle = colorSchemes.default[index % colorSchemes.default.length]
        ctx.fill()

        // 绘制百分比标签
        const labelAngle = currentAngle + sliceAngle / 2
        const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7)
        const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7)
        
        ctx.fillStyle = '#FFFFFF'
        ctx.font = 'bold 14px sans-serif'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        const percentage = ((value / total) * 100).toFixed(1)
        ctx.fillText(`${percentage}%`, labelX, labelY)

        currentAngle += sliceAngle
      })
    }

    const drawLegend = (
      ctx: CanvasRenderingContext2D,
      data: ChartData,
      x: number,
      y: number
    ) => {
      ctx.font = '12px sans-serif'
      const items = config.type === 'pie' || config.type === 'doughnut' 
        ? data.labels 
        : data.datasets.map(d => d.label)

      items.forEach((item, index) => {
        const itemY = y + index * 25
        
        // 绘制色块
        ctx.fillStyle = config.type === 'pie' || config.type === 'doughnut'
          ? colorSchemes.default[index % colorSchemes.default.length]
          : data.datasets[index]?.borderColor || '#3B82F6'
        ctx.fillRect(x - 120, itemY - 8, 16, 16)
        
        // 绘制文字
        ctx.fillStyle = '#374151'
        ctx.textAlign = 'left'
        ctx.fillText(item, x - 95, itemY + 4)
      })
    }

    // 根据图表类型绘制
    switch (config.type) {
      case 'bar':
        drawBarChart(ctx, chartData, padding, chartWidth, chartHeight)
        break
      case 'line':
        drawLineChart(ctx, chartData, padding, chartWidth, chartHeight)
        break
      case 'pie':
      case 'doughnut':
        drawPieChart(ctx, chartData, padding, chartWidth, chartHeight, config.type === 'doughnut')
        break
    }

    // 绘制图例
    if (config.showLegend && chartData.datasets.length > 0) {
      drawLegend(ctx, chartData, config.width - padding, padding + 50)
    }
  }, [chartData, config, colorSchemes])


  // 更新图表
  useEffect(() => {
    if (csvData) {
      try {
        setError('')
        const parsed = parseCSV(csvData)
        setChartData(parsed)
      } catch (err) {
        setError(err instanceof Error ? err.message : '解析数据时出错')
        setChartData(null)
      }
    }
  }, [csvData, config.type, parseCSV])

  useEffect(() => {
    drawChart()
  }, [drawChart])

  // 下载图表
  const downloadChart = () => {
    if (!canvasRef.current) return
    
    const link = document.createElement('a')
    link.download = `chart_${new Date().getTime()}.png`
    link.href = canvasRef.current.toDataURL()
    link.click()
  }

  // 加载示例数据
  const loadSampleData = () => {
    const sampleData = `月份,销售额,利润,成本
1月,12000,3000,9000
2月,15000,4500,10500
3月,18000,6000,12000
4月,16000,5000,11000
5月,20000,7000,13000
6月,22000,8000,14000`
    setCsvData(sampleData)
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">数据可视化工具</h2>
        <p className="text-gray-600">
          将CSV数据转换为美观的图表，支持柱状图、折线图、饼图等多种类型
        </p>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* 左侧：数据输入和配置 */}
        <div className="lg:col-span-1 space-y-4">
          {/* 数据输入 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Upload className="w-4 h-4" />
              数据输入
            </h3>
            <textarea
              value={csvData}
              onChange={(e) => setCsvData(e.target.value)}
              placeholder="粘贴CSV格式的数据，第一行为标题行..."
              className="w-full h-48 p-3 font-mono text-sm border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={loadSampleData}
              className="mt-2 w-full px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm"
            >
              加载示例数据
            </button>
          </div>

          {/* 图表配置 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3 flex items-center gap-2">
              <Settings className="w-4 h-4" />
              图表配置
            </h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium mb-1">图表类型</label>
                <select
                  value={config.type}
                  onChange={(e) => setConfig({ ...config, type: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="bar">柱状图</option>
                  <option value="line">折线图</option>
                  <option value="pie">饼图</option>
                  <option value="doughnut">环形图</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">图表标题</label>
                <input
                  type="text"
                  value={config.title}
                  onChange={(e) => setConfig({ ...config, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium mb-1">宽度</label>
                  <input
                    type="number"
                    value={config.width}
                    onChange={(e) => setConfig({ ...config, width: parseInt(e.target.value) || 800 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">高度</label>
                  <input
                    type="number"
                    value={config.height}
                    onChange={(e) => setConfig({ ...config, height: parseInt(e.target.value) || 400 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={config.showLegend}
                    onChange={(e) => setConfig({ ...config, showLegend: e.target.checked })}
                    className="rounded"
                  />
                  <span className="text-sm">显示图例</span>
                </label>
                
                {(config.type === 'bar' || config.type === 'line') && (
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={config.showGrid}
                      onChange={(e) => setConfig({ ...config, showGrid: e.target.checked })}
                      className="rounded"
                    />
                    <span className="text-sm">显示网格线</span>
                  </label>
                )}
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}
        </div>

        {/* 右侧：图表预览 */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium flex items-center gap-2">
                {config.type === 'bar' && <BarChart className="w-4 h-4" />}
                {config.type === 'line' && <LineChart className="w-4 h-4" />}
                {(config.type === 'pie' || config.type === 'doughnut') && <PieChart className="w-4 h-4" />}
                图表预览
              </h3>
              {chartData && (
                <button
                  onClick={downloadChart}
                  className="p-2 hover:bg-gray-100 rounded-md"
                  title="下载图表"
                >
                  <Download className="w-4 h-4" />
                </button>
              )}
            </div>

            <div className="overflow-auto">
              <canvas
                ref={canvasRef}
                className="border border-gray-200 rounded"
                style={{ maxWidth: '100%', height: 'auto' }}
              />
            </div>

            {!chartData && (
              <div className="flex items-center justify-center h-96 text-gray-400">
                <div className="text-center">
                  <BarChart className="w-12 h-12 mx-auto mb-2" />
                  <p>输入数据后将在此显示图表</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持CSV格式数据输入，第一行为标题，第一列为标签</li>
              <li>可选择柱状图、折线图、饼图、环形图等多种图表类型</li>
              <li>支持自定义图表标题、尺寸和显示选项</li>
              <li>点击下载按钮可将图表保存为PNG图片</li>
              <li>饼图和环形图仅使用第一个数据系列</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
