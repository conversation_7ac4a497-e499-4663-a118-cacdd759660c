'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Bell, BellOff, Plus, Trash2, Clock, Calendar, Volume2, VolumeX } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Alarm {
  id: string
  name: string
  time: string
  days: number[] // 0 = 周日, 1 = 周一, ..., 6 = 周六
  enabled: boolean
  sound: string
  snooze: boolean
  snoozeDuration: number // 分钟
  message: string
}

const DAYS = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
const SOUNDS = [
  { id: 'default', name: '默认铃声' },
  { id: 'gentle', name: '轻柔唤醒' },
  { id: 'energetic', name: '活力铃声' },
  { id: 'classic', name: '经典闹钟' },
  { id: 'nature', name: '自然之声' },
]

export default function AlarmClockTool() {
  const [alarms, setAlarms] = useState<Alarm[]>([])
  const [newAlarmTime, setNewAlarmTime] = useState('')
  const [newAlarmName, setNewAlarmName] = useState('')
  const [selectedDays, setSelectedDays] = useState<number[]>([])
  const [selectedSound, setSelectedSound] = useState('default')
  const [enableSnooze, setEnableSnooze] = useState(true)
  const [snoozeDuration, setSnoozeDuration] = useState(5)
  const [alarmMessage, setAlarmMessage] = useState('')
  const [currentTime, setCurrentTime] = useState(new Date())
  const [activeAlarms, setActiveAlarms] = useState<Set<string>>(new Set())
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const { toast } = useToast()

  // 更新当前时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // 从 localStorage 加载闹钟
  useEffect(() => {
    const savedAlarms = localStorage.getItem('alarmClocks')
    if (savedAlarms) {
      setAlarms(JSON.parse(savedAlarms))
    }
    
    // 初始化音频
    audioRef.current = new Audio('/sounds/notification.mp3')
    audioRef.current.loop = true
  }, [])

  // 保存闹钟到 localStorage
  useEffect(() => {
    if (alarms.length > 0) {
      localStorage.setItem('alarmClocks', JSON.stringify(alarms))
    }
  }, [alarms])

  const snoozeAlarm = useCallback((alarm: Alarm) => {
    // 停止当前闹钟声音
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }
    
    // 创建贪睡闹钟
    const snoozeTime = new Date()
    snoozeTime.setMinutes(snoozeTime.getMinutes() + alarm.snoozeDuration)
    
    const snoozeAlarmData: Alarm = {
      ...alarm,
      id: `${alarm.id}-snooze-${Date.now()}`,
      name: `${alarm.name} (贪睡)`,
      time: `${snoozeTime.getHours().toString().padStart(2, '0')}:${snoozeTime.getMinutes().toString().padStart(2, '0')}`,
      days: [snoozeTime.getDay()], // 只在今天响
      snooze: false, // 贪睡闹钟不再支持贪睡
    }
    
    setAlarms(prev => [...prev, snoozeAlarmData])
    
    toast({
      title: '贪睡模式',
      description: `闹钟将在 ${alarm.snoozeDuration} 分钟后再次响起`,
    })
  }, [toast])

  const triggerAlarm = useCallback((alarm: Alarm) => {
    // 播放声音
    if (audioRef.current) {
      audioRef.current.play().catch(e => console.error('播放音频失败:', e))
    }
    
    // 显示通知
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`闹钟: ${alarm.name || '未命名闹钟'}`, {
        body: alarm.message || '时间到了！',
        icon: '/icons/icon-192x192.png',
        requireInteraction: true,
      })
    }
    
    // 显示 Toast
    toast({
      title: `闹钟: ${alarm.name || '未命名闹钟'}`,
      description: alarm.message || '时间到了！',
      action: alarm.snooze ? (
        <Button
          size="sm"
          onClick={() => snoozeAlarm(alarm)}
        >
          贪睡 {alarm.snoozeDuration} 分钟
        </Button>
      ) : undefined,
    })
  }, [toast, snoozeAlarm])

  // 检查闹钟
  useEffect(() => {
    const checkAlarms = () => {
      const now = new Date()
      const currentHour = now.getHours()
      const currentMinute = now.getMinutes()
      const currentDay = now.getDay()
      const currentTimeStr = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`
      
      alarms.forEach(alarm => {
        if (!alarm.enabled) return
        
        // 检查是否已经响过
        const alarmKey = `${alarm.id}-${currentTimeStr}`
        if (activeAlarms.has(alarmKey)) return
        
        // 检查时间是否匹配
        if (alarm.time !== currentTimeStr) return
        
        // 检查日期是否匹配
        if (alarm.days.length > 0 && !alarm.days.includes(currentDay)) return
        
        // 触发闹钟
        triggerAlarm(alarm)
        setActiveAlarms(prev => new Set(prev).add(alarmKey))
      })
    }
    
    const interval = setInterval(checkAlarms, 1000)
    return () => clearInterval(interval)
  }, [alarms, activeAlarms, triggerAlarm])

  const createAlarm = () => {
    if (!newAlarmTime) {
      toast({
        title: '请设置闹钟时间',
        variant: 'destructive',
      })
      return
    }
    
    const alarm: Alarm = {
      id: Date.now().toString(),
      name: newAlarmName || '闹钟',
      time: newAlarmTime,
      days: selectedDays,
      enabled: true,
      sound: selectedSound,
      snooze: enableSnooze,
      snoozeDuration: snoozeDuration,
      message: alarmMessage,
    }
    
    setAlarms([...alarms, alarm])
    
    // 重置表单
    setNewAlarmTime('')
    setNewAlarmName('')
    setSelectedDays([])
    setSelectedSound('default')
    setEnableSnooze(true)
    setSnoozeDuration(5)
    setAlarmMessage('')
    
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
    
    toast({
      title: '闹钟已创建',
      description: `${alarm.name} 已设置为 ${alarm.time}`,
    })
  }

  const toggleAlarm = (id: string) => {
    setAlarms(alarms.map(alarm => 
      alarm.id === id ? { ...alarm, enabled: !alarm.enabled } : alarm
    ))
  }

  const deleteAlarm = (id: string) => {
    setAlarms(alarms.filter(alarm => alarm.id !== id))
    
    // 如果正在响铃，停止声音
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
    }
  }

  const toggleDay = (day: number) => {
    if (selectedDays.includes(day)) {
      setSelectedDays(selectedDays.filter(d => d !== day))
    } else {
      setSelectedDays([...selectedDays, day])
    }
  }

  const formatAlarmDays = (days: number[]) => {
    if (days.length === 0) return '一次性'
    if (days.length === 7) return '每天'
    if (days.length === 5 && !days.includes(0) && !days.includes(6)) return '工作日'
    if (days.length === 2 && days.includes(0) && days.includes(6)) return '周末'
    
    return days.map(d => DAYS[d]).join(', ')
  }

  const getNextAlarmTime = (alarm: Alarm) => {
    const now = new Date()
    const [hours, minutes] = alarm.time.split(':').map(Number)
    
    // 如果是一次性闹钟
    if (alarm.days.length === 0) {
      const alarmTime = new Date()
      alarmTime.setHours(hours, minutes, 0, 0)
      
      if (alarmTime <= now) {
        alarmTime.setDate(alarmTime.getDate() + 1)
      }
      
      return alarmTime
    }
    
    // 如果是重复闹钟
    for (let i = 0; i < 7; i++) {
      const checkDate = new Date()
      checkDate.setDate(checkDate.getDate() + i)
      checkDate.setHours(hours, minutes, 0, 0)
      
      if (checkDate > now && alarm.days.includes(checkDate.getDay())) {
        return checkDate
      }
    }
    
    // 如果没找到，返回下周的第一个匹配日期
    const nextWeek = new Date()
    nextWeek.setDate(nextWeek.getDate() + 7)
    nextWeek.setHours(hours, minutes, 0, 0)
    
    while (!alarm.days.includes(nextWeek.getDay())) {
      nextWeek.setDate(nextWeek.getDate() + 1)
    }
    
    return nextWeek
  }

  const formatTimeUntilAlarm = (alarm: Alarm) => {
    if (!alarm.enabled) return '已禁用'
    
    const nextTime = getNextAlarmTime(alarm)
    const diff = nextTime.getTime() - new Date().getTime()
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days}天${hours % 24}小时后`
    }
    
    return `${hours}小时${minutes}分钟后`
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">闹钟提醒器</h2>
          <div className="text-3xl font-mono">
            {currentTime.toLocaleTimeString('zh-CN')}
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="alarm-time">闹钟时间</Label>
              <Input
                id="alarm-time"
                type="time"
                value={newAlarmTime}
                onChange={(e) => setNewAlarmTime(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="alarm-name">闹钟名称</Label>
              <Input
                id="alarm-name"
                value={newAlarmName}
                onChange={(e) => setNewAlarmName(e.target.value)}
                placeholder="例如：起床、吃药、会议"
              />
            </div>
          </div>
          
          <div>
            <Label>重复</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {DAYS.map((day, index) => (
                <Button
                  key={index}
                  size="sm"
                  variant={selectedDays.includes(index) ? 'default' : 'outline'}
                  onClick={() => toggleDay(index)}
                >
                  {day}
                </Button>
              ))}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="alarm-sound">铃声</Label>
              <Select value={selectedSound} onValueChange={setSelectedSound}>
                <SelectTrigger id="alarm-sound">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SOUNDS.map((sound) => (
                    <SelectItem key={sound.id} value={sound.id}>
                      {sound.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="snooze">贪睡功能</Label>
                <Switch
                  id="snooze"
                  checked={enableSnooze}
                  onCheckedChange={setEnableSnooze}
                />
              </div>
              
              {enableSnooze && (
                <Select value={snoozeDuration.toString()} onValueChange={(v) => setSnoozeDuration(Number(v))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 分钟</SelectItem>
                    <SelectItem value="10">10 分钟</SelectItem>
                    <SelectItem value="15">15 分钟</SelectItem>
                    <SelectItem value="20">20 分钟</SelectItem>
                    <SelectItem value="30">30 分钟</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
          
          <div>
            <Label htmlFor="alarm-message">提醒消息（可选）</Label>
            <Input
              id="alarm-message"
              value={alarmMessage}
              onChange={(e) => setAlarmMessage(e.target.value)}
              placeholder="例如：记得带文件、吃早餐"
            />
          </div>
          
          <Button onClick={createAlarm} className="w-full">
            <Plus className="w-4 h-4 mr-2" />
            创建闹钟
          </Button>
        </div>
      </Card>
      
      {alarms.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">我的闹钟</h3>
          <div className="space-y-4">
            {alarms.map((alarm) => (
              <div
                key={alarm.id}
                className={`p-4 rounded-lg border ${
                  alarm.enabled
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                    : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-semibold text-lg">{alarm.name}</h4>
                      <span className="text-2xl font-mono">{alarm.time}</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {formatAlarmDays(alarm.days)} · {formatTimeUntilAlarm(alarm)}
                    </p>
                    {alarm.message && (
                      <p className="text-sm mt-2">{alarm.message}</p>
                    )}
                    <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        {alarm.enabled ? (
                          <Bell className="w-3 h-3" />
                        ) : (
                          <BellOff className="w-3 h-3" />
                        )}
                        {alarm.enabled ? '已启用' : '已禁用'}
                      </span>
                      {alarm.snooze && (
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          贪睡 {alarm.snoozeDuration} 分钟
                        </span>
                      )}
                      <span className="flex items-center gap-1">
                        <Volume2 className="w-3 h-3" />
                        {SOUNDS.find(s => s.id === alarm.sound)?.name}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={alarm.enabled}
                      onCheckedChange={() => toggleAlarm(alarm.id)}
                    />
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => deleteAlarm(alarm.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
      
      {alarms.length === 0 && (
        <Card className="p-12 text-center">
          <Bell className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">
            还没有设置闹钟，创建一个吧！
          </p>
        </Card>
      )}
    </div>
  )
}
