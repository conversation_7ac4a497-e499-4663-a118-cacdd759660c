'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { 
  DollarSign, 
  Plus, 
  Trash2, 
  Calendar,
  TrendingUp,
  TrendingDown,
  PieChart,
  Download,
  Upload,
  Edit2,
  Check,
  X
} from 'lucide-react'

interface Transaction {
  id: string
  type: 'income' | 'expense'
  category: string
  amount: number
  description: string
  date: string
  tags: string[]
}

interface Category {
  id: string
  name: string
  type: 'income' | 'expense'
  color: string
}

const defaultCategories: Category[] = [
  // 支出类别
  { id: 'food', name: '餐饮', type: 'expense', color: '#FF6B6B' },
  { id: 'transport', name: '交通', type: 'expense', color: '#4ECDC4' },
  { id: 'shopping', name: '购物', type: 'expense', color: '#45B7D1' },
  { id: 'entertainment', name: '娱乐', type: 'expense', color: '#F7DC6F' },
  { id: 'bills', name: '账单', type: 'expense', color: '#BB8FCE' },
  { id: 'health', name: '医疗', type: 'expense', color: '#85C1E2' },
  { id: 'education', name: '教育', type: 'expense', color: '#F8C471' },
  { id: 'other-expense', name: '其他支出', type: 'expense', color: '#AAB7B8' },
  // 收入类别
  { id: 'salary', name: '工资', type: 'income', color: '#58D68D' },
  { id: 'bonus', name: '奖金', type: 'income', color: '#52BE80' },
  { id: 'investment', name: '投资', type: 'income', color: '#45B39D' },
  { id: 'freelance', name: '兼职', type: 'income', color: '#48C9B0' },
  { id: 'other-income', name: '其他收入', type: 'income', color: '#76D7C4' },
]

export default function ExpenseTrackerTool() {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [categories] = useState<Category[]>(defaultCategories)
  const [activeTab, setActiveTab] = useState('add')
  const [editingId, setEditingId] = useState<string | null>(null)
  const { toast } = useToast()

  // 表单状态
  const [formData, setFormData] = useState({
    type: 'expense' as 'income' | 'expense',
    category: '',
    amount: '',
    description: '',
    date: new Date().toISOString().split('T')[0],
    tags: ''
  })

  // 筛选状态
  const [filterType, setFilterType] = useState<'all' | 'income' | 'expense'>('all')
  const [filterMonth, setFilterMonth] = useState(new Date().toISOString().slice(0, 7))
  const [filterCategory, setFilterCategory] = useState<string>('all')

  // 从 localStorage 加载数据
  useEffect(() => {
    const savedTransactions = localStorage.getItem('expense-tracker-transactions')
    if (savedTransactions) {
      setTransactions(JSON.parse(savedTransactions))
    }
  }, [])

  // 保存数据到 localStorage
  useEffect(() => {
    localStorage.setItem('expense-tracker-transactions', JSON.stringify(transactions))
  }, [transactions])

  // 计算统计数据
  const statistics = useMemo(() => {
    const filtered = transactions.filter(t => {
      const transactionMonth = t.date.slice(0, 7)
      return transactionMonth === filterMonth
    })

    const income = filtered
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)

    const expense = filtered
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)

    const balance = income - expense

    // 按类别统计
    const byCategory = filtered.reduce((acc, t) => {
      if (!acc[t.category]) {
        acc[t.category] = { amount: 0, count: 0, type: t.type }
      }
      acc[t.category].amount += t.amount
      acc[t.category].count += 1
      return acc
    }, {} as Record<string, { amount: number; count: number; type: string }>)

    return { income, expense, balance, byCategory }
  }, [transactions, filterMonth])

  // 筛选后的交易列表
  const filteredTransactions = useMemo(() => {
    return transactions.filter(t => {
      const matchType = filterType === 'all' || t.type === filterType
      const matchMonth = t.date.slice(0, 7) === filterMonth
      const matchCategory = filterCategory === 'all' || t.category === filterCategory
      return matchType && matchMonth && matchCategory
    }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }, [transactions, filterType, filterMonth, filterCategory])

  // 添加或更新交易
  const handleSubmit = () => {
    if (!formData.category || !formData.amount || !formData.description) {
      toast({
        title: '请填写完整信息',
        description: '类别、金额和描述为必填项',
        variant: 'destructive'
      })
      return
    }

    const amount = parseFloat(formData.amount)
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: '金额无效',
        description: '请输入有效的金额',
        variant: 'destructive'
      })
      return
    }

    const transaction: Transaction = {
      id: editingId || Math.random().toString(36).substr(2, 9),
      type: formData.type,
      category: formData.category,
      amount,
      description: formData.description,
      date: formData.date,
      tags: formData.tags.split(',').map(t => t.trim()).filter(t => t)
    }

    if (editingId) {
      setTransactions(prev => prev.map(t => t.id === editingId ? transaction : t))
      setEditingId(null)
      toast({
        title: '更新成功',
        description: '交易记录已更新',
      })
    } else {
      setTransactions(prev => [...prev, transaction])
      toast({
        title: '添加成功',
        description: '交易记录已添加',
      })
    }

    // 重置表单
    setFormData({
      type: 'expense',
      category: '',
      amount: '',
      description: '',
      date: new Date().toISOString().split('T')[0],
      tags: ''
    })
  }

  // 删除交易
  const deleteTransaction = (id: string) => {
    setTransactions(prev => prev.filter(t => t.id !== id))
    toast({
      title: '删除成功',
      description: '交易记录已删除',
    })
  }

  // 编辑交易
  const editTransaction = (transaction: Transaction) => {
    setFormData({
      type: transaction.type,
      category: transaction.category,
      amount: transaction.amount.toString(),
      description: transaction.description,
      date: transaction.date,
      tags: transaction.tags.join(', ')
    })
    setEditingId(transaction.id)
    setActiveTab('add')
  }

  // 导出数据
  const exportData = () => {
    const dataStr = JSON.stringify(transactions, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    const exportFileDefaultName = `expense-tracker-${new Date().toISOString().split('T')[0]}.json`

    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  // 导入数据
  const importData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        if (Array.isArray(data)) {
          setTransactions(data)
          toast({
            title: '导入成功',
            description: `已导入 ${data.length} 条记录`,
          })
        }
      } catch (error) {
        toast({
          title: '导入失败',
          description: '文件格式不正确',
          variant: 'destructive'
        })
      }
    }
    reader.readAsText(file)
  }

  // 获取类别名称
  const getCategoryName = (categoryId: string) => {
    return categories.find(c => c.id === categoryId)?.name || categoryId
  }

  // 获取类别颜色
  const getCategoryColor = (categoryId: string) => {
    return categories.find(c => c.id === categoryId)?.color || '#666'
  }

  return (
    <Card className="w-full max-w-6xl">
      <CardHeader>
        <CardTitle>记账本</CardTitle>
        <CardDescription>
          记录和管理您的收支，掌握财务状况
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="add">记账</TabsTrigger>
            <TabsTrigger value="list">明细</TabsTrigger>
            <TabsTrigger value="stats">统计</TabsTrigger>
            <TabsTrigger value="manage">管理</TabsTrigger>
          </TabsList>

          {/* 记账标签页 */}
          <TabsContent value="add" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">类型</Label>
                <Select value={formData.type} onValueChange={(v) => setFormData({...formData, type: v as 'income' | 'expense', category: ''})}>
                  <SelectTrigger id="type">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="expense">支出</SelectItem>
                    <SelectItem value="income">收入</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="category">类别</Label>
                <Select value={formData.category} onValueChange={(v) => setFormData({...formData, category: v})}>
                  <SelectTrigger id="category">
                    <SelectValue placeholder="选择类别" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories
                      .filter(c => c.type === formData.type)
                      .map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          <span className="flex items-center gap-2">
                            <span 
                              className="w-3 h-3 rounded-full" 
                              style={{ backgroundColor: category.color }}
                            />
                            {category.name}
                          </span>
                        </SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="amount">金额</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.amount}
                onChange={(e) => setFormData({...formData, amount: e.target.value})}
              />
            </div>

            <div>
              <Label htmlFor="description">描述</Label>
              <Input
                id="description"
                placeholder="输入描述"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />
            </div>

            <div>
              <Label htmlFor="date">日期</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({...formData, date: e.target.value})}
              />
            </div>

            <div>
              <Label htmlFor="tags">标签（用逗号分隔）</Label>
              <Input
                id="tags"
                placeholder="标签1, 标签2"
                value={formData.tags}
                onChange={(e) => setFormData({...formData, tags: e.target.value})}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={handleSubmit} className="flex-1">
                {editingId ? (
                  <>
                    <Check className="w-4 h-4 mr-2" />
                    更新
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    添加
                  </>
                )}
              </Button>
              {editingId && (
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setEditingId(null)
                    setFormData({
                      type: 'expense',
                      category: '',
                      amount: '',
                      description: '',
                      date: new Date().toISOString().split('T')[0],
                      tags: ''
                    })
                  }}
                >
                  <X className="w-4 h-4 mr-2" />
                  取消
                </Button>
              )}
            </div>
          </TabsContent>

          {/* 明细标签页 */}
          <TabsContent value="list" className="space-y-4">
            {/* 筛选器 */}
            <div className="grid grid-cols-3 gap-4">
              <Select value={filterType} onValueChange={(v) => setFilterType(v as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="income">收入</SelectItem>
                  <SelectItem value="expense">支出</SelectItem>
                </SelectContent>
              </Select>

              <Input
                type="month"
                value={filterMonth}
                onChange={(e) => setFilterMonth(e.target.value)}
              />

              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类别</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 交易列表 */}
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredTransactions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  暂无交易记录
                </div>
              ) : (
                filteredTransactions.map(transaction => (
                  <div
                    key={transaction.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="w-10 h-10 rounded-full flex items-center justify-center"
                        style={{ backgroundColor: getCategoryColor(transaction.category) + '20' }}
                      >
                        {transaction.type === 'income' ? (
                          <TrendingUp className="w-5 h-5" style={{ color: getCategoryColor(transaction.category) }} />
                        ) : (
                          <TrendingDown className="w-5 h-5" style={{ color: getCategoryColor(transaction.category) }} />
                        )}
                      </div>
                      <div>
                        <div className="font-medium">{transaction.description}</div>
                        <div className="text-sm text-muted-foreground">
                          {getCategoryName(transaction.category)} · {transaction.date}
                          {transaction.tags.length > 0 && (
                            <span className="ml-2">
                              {transaction.tags.map(tag => (
                                <span key={tag} className="inline-block px-2 py-0.5 text-xs bg-muted rounded-full mr-1">
                                  {tag}
                                </span>
                              ))}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`font-semibold ${transaction.type === 'income' ? 'text-green-600' : 'text-red-600'}`}>
                        {transaction.type === 'income' ? '+' : '-'}¥{transaction.amount.toFixed(2)}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => editTransaction(transaction)}
                      >
                        <Edit2 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteTransaction(transaction.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </TabsContent>

          {/* 统计标签页 */}
          <TabsContent value="stats" className="space-y-4">
            <div className="flex items-center justify-center">
              <Input
                type="month"
                value={filterMonth}
                onChange={(e) => setFilterMonth(e.target.value)}
                className="w-48"
              />
            </div>

            {/* 总览卡片 */}
            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">总收入</p>
                      <p className="text-2xl font-bold text-green-600">
                        ¥{statistics.income.toFixed(2)}
                      </p>
                    </div>
                    <TrendingUp className="w-8 h-8 text-green-600 opacity-20" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">总支出</p>
                      <p className="text-2xl font-bold text-red-600">
                        ¥{statistics.expense.toFixed(2)}
                      </p>
                    </div>
                    <TrendingDown className="w-8 h-8 text-red-600 opacity-20" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">结余</p>
                      <p className={`text-2xl font-bold ${statistics.balance >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
                        ¥{statistics.balance.toFixed(2)}
                      </p>
                    </div>
                    <DollarSign className="w-8 h-8 text-blue-600 opacity-20" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 分类统计 */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                分类统计
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                {/* 支出分类 */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">支出分类</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {Object.entries(statistics.byCategory)
                      .filter(([_, data]) => data.type === 'expense')
                      .sort((a, b) => b[1].amount - a[1].amount)
                      .map(([categoryId, data]) => {
                        const category = categories.find(c => c.id === categoryId)
                        const percentage = statistics.expense > 0 
                          ? (data.amount / statistics.expense * 100).toFixed(1)
                          : '0'
                        return (
                          <div key={categoryId} className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span className="flex items-center gap-2">
                                <span 
                                  className="w-3 h-3 rounded-full" 
                                  style={{ backgroundColor: category?.color }}
                                />
                                {category?.name}
                              </span>
                              <span>¥{data.amount.toFixed(2)} ({percentage}%)</span>
                            </div>
                            <div className="w-full bg-muted rounded-full h-2">
                              <div 
                                className="h-2 rounded-full transition-all"
                                style={{ 
                                  width: `${percentage}%`,
                                  backgroundColor: category?.color
                                }}
                              />
                            </div>
                          </div>
                        )
                      })
                    }
                  </CardContent>
                </Card>

                {/* 收入分类 */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">收入分类</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {Object.entries(statistics.byCategory)
                      .filter(([_, data]) => data.type === 'income')
                      .sort((a, b) => b[1].amount - a[1].amount)
                      .map(([categoryId, data]) => {
                        const category = categories.find(c => c.id === categoryId)
                        const percentage = statistics.income > 0 
                          ? (data.amount / statistics.income * 100).toFixed(1)
                          : '0'
                        return (
                          <div key={categoryId} className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span className="flex items-center gap-2">
                                <span 
                                  className="w-3 h-3 rounded-full" 
                                  style={{ backgroundColor: category?.color }}
                                />
                                {category?.name}
                              </span>
                              <span>¥{data.amount.toFixed(2)} ({percentage}%)</span>
                            </div>
                            <div className="w-full bg-muted rounded-full h-2">
                              <div 
                                className="h-2 rounded-full transition-all"
                                style={{ 
                                  width: `${percentage}%`,
                                  backgroundColor: category?.color
                                }}
                              />
                            </div>
                          </div>
                        )
                      })
                    }
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* 管理标签页 */}
          <TabsContent value="manage" className="space-y-4">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-2">数据管理</h3>
                <div className="flex gap-2">
                  <Button onClick={exportData} variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    导出数据
                  </Button>
                  <Button variant="outline" asChild>
                    <label className="cursor-pointer">
                      <Upload className="w-4 h-4 mr-2" />
                      导入数据
                      <input
                        type="file"
                        accept=".json"
                        className="hidden"
                        onChange={importData}
                      />
                    </label>
                  </Button>
                </div>
              </div>

              <div className="rounded-lg bg-muted p-4 space-y-2">
                <h4 className="font-medium">使用说明</h4>
                <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                  <li>记录每笔收入和支出，养成良好的记账习惯</li>
                  <li>使用标签对交易进行更细致的分类</li>
                  <li>定期查看统计数据，了解消费习惯</li>
                  <li>导出数据进行备份，避免数据丢失</li>
                  <li>所有数据保存在本地浏览器中</li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
