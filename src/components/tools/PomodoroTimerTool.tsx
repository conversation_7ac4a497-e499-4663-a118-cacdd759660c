'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Timer, Play, Pause, RotateCcw, <PERSON><PERSON><PERSON>, Bell, BellOff, Target } from 'lucide-react'

interface TimerSettings {
  workMinutes: number
  shortBreakMinutes: number
  longBreakMinutes: number
  sessionsUntilLongBreak: number
  autoStartBreaks: boolean
  autoStartPomodoros: boolean
  soundEnabled: boolean
}

type TimerMode = 'work' | 'shortBreak' | 'longBreak'

export default function PomodoroTimerTool() {
  const [settings, setSettings] = useState<TimerSettings>({
    workMinutes: 25,
    shortBreakMinutes: 5,
    longBreakMinutes: 15,
    sessionsUntilLongBreak: 4,
    autoStartBreaks: false,
    autoStartPomodoros: false,
    soundEnabled: true,
  })

  const [mode, setMode] = useState<TimerMode>('work')
  const [secondsLeft, setSecondsLeft] = useState(settings.workMinutes * 60)
  const [isActive, setIsActive] = useState(false)
  const [completedSessions, setCompletedSessions] = useState(0)
  const [showSettings, setShowSettings] = useState(false)

  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  // 初始化音频
  useEffect(() => {
    audioRef.current = new Audio('/sounds/notification.mp3')
    audioRef.current.volume = 0.5
  }, [])

  // 获取当前模式的分钟数
  const getModeMinutes = useCallback((currentMode: TimerMode): number => {
    switch (currentMode) {
      case 'work':
        return settings.workMinutes
      case 'shortBreak':
        return settings.shortBreakMinutes
      case 'longBreak':
        return settings.longBreakMinutes
    }
  }, [settings.workMinutes, settings.shortBreakMinutes, settings.longBreakMinutes])

  // 计时器逻辑
  useEffect(() => {
    if (isActive && secondsLeft > 0) {
      intervalRef.current = setInterval(() => {
        setSecondsLeft((seconds) => seconds - 1)
      }, 1000)
    } else if (secondsLeft === 0) {
      // 计时结束
      if (settings.soundEnabled && audioRef.current) {
        audioRef.current.play().catch(() => {
          // 忽略自动播放错误
        })
      }

      if (mode === 'work') {
        const newCompletedSessions = completedSessions + 1
        setCompletedSessions(newCompletedSessions)

        // 判断是长休息还是短休息
        if (newCompletedSessions % settings.sessionsUntilLongBreak === 0) {
          setMode('longBreak')
          setSecondsLeft(settings.longBreakMinutes * 60)
          if (settings.autoStartBreaks) {
            setIsActive(true)
          } else {
            setIsActive(false)
          }
        } else {
          setMode('shortBreak')
          setSecondsLeft(settings.shortBreakMinutes * 60)
          if (settings.autoStartBreaks) {
            setIsActive(true)
          } else {
            setIsActive(false)
          }
        }
      } else {
        // 休息结束，回到工作模式
        setMode('work')
        setSecondsLeft(settings.workMinutes * 60)
        if (settings.autoStartPomodoros) {
          setIsActive(true)
        } else {
          setIsActive(false)
        }
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isActive, secondsLeft, mode, completedSessions, settings, getModeMinutes])

  // 切换模式时更新时间
  useEffect(() => {
    setSecondsLeft(getModeMinutes(mode) * 60)
    setIsActive(false)
  }, [mode, settings, getModeMinutes])

  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 计算进度百分比
  const getProgress = (): number => {
    const totalSeconds = getModeMinutes(mode) * 60
    return ((totalSeconds - secondsLeft) / totalSeconds) * 100
  }

  // 控制函数
  const toggleTimer = () => {
    setIsActive(!isActive)
  }

  const resetTimer = () => {
    setIsActive(false)
    setSecondsLeft(getModeMinutes(mode) * 60)
  }

  const switchMode = (newMode: TimerMode) => {
    setMode(newMode)
    setIsActive(false)
  }

  // 获取模式显示信息
  const getModeInfo = () => {
    switch (mode) {
      case 'work':
        return { name: '专注时间', color: 'bg-red-500', textColor: 'text-red-600' }
      case 'shortBreak':
        return { name: '短休息', color: 'bg-green-500', textColor: 'text-green-600' }
      case 'longBreak':
        return { name: '长休息', color: 'bg-blue-500', textColor: 'text-blue-600' }
    }
  }

  const modeInfo = getModeInfo()

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">番茄钟计时器</h2>
        <p className="text-gray-600">
          使用番茄工作法提高专注力和工作效率
        </p>
      </div>

      {/* 主计时器 */}
      <div className="bg-gray-50 rounded-2xl p-8 text-center mb-6">
        {/* 模式选择 */}
        <div className="flex justify-center space-x-2 mb-8">
          <button
            onClick={() => switchMode('work')}
            className={`px-4 py-2 rounded-md transition-colors ${
              mode === 'work'
                ? 'bg-red-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100'
            }`}
          >
            专注
          </button>
          <button
            onClick={() => switchMode('shortBreak')}
            className={`px-4 py-2 rounded-md transition-colors ${
              mode === 'shortBreak'
                ? 'bg-green-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100'
            }`}
          >
            短休息
          </button>
          <button
            onClick={() => switchMode('longBreak')}
            className={`px-4 py-2 rounded-md transition-colors ${
              mode === 'longBreak'
                ? 'bg-blue-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100'
            }`}
          >
            长休息
          </button>
        </div>

        {/* 计时器显示 */}
        <div className="relative w-64 h-64 mx-auto mb-8">
          {/* 进度环 */}
          <svg className="absolute inset-0 w-full h-full transform -rotate-90">
            <circle
              cx="128"
              cy="128"
              r="120"
              stroke="#e5e7eb"
              strokeWidth="8"
              fill="none"
            />
            <circle
              cx="128"
              cy="128"
              r="120"
              stroke="currentColor"
              strokeWidth="8"
              fill="none"
              strokeDasharray={`${2 * Math.PI * 120}`}
              strokeDashoffset={`${2 * Math.PI * 120 * (1 - getProgress() / 100)}`}
              className={`transition-all duration-1000 ${modeInfo.textColor}`}
            />
          </svg>
          
          {/* 时间显示 */}
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <Timer className={`w-8 h-8 mb-2 ${modeInfo.textColor}`} />
            <div className="text-5xl font-bold font-mono">{formatTime(secondsLeft)}</div>
            <div className={`text-sm mt-2 ${modeInfo.textColor}`}>{modeInfo.name}</div>
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="flex justify-center space-x-4 mb-6">
          <button
            onClick={toggleTimer}
            className={`px-8 py-3 rounded-lg text-white font-medium transition-colors ${
              isActive
                ? 'bg-gray-600 hover:bg-gray-700'
                : `${modeInfo.color} hover:opacity-90`
            }`}
          >
            {isActive ? (
              <>
                <Pause className="inline-block w-5 h-5 mr-2" />
                暂停
              </>
            ) : (
              <>
                <Play className="inline-block w-5 h-5 mr-2" />
                开始
              </>
            )}
          </button>
          <button
            onClick={resetTimer}
            className="px-6 py-3 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <RotateCcw className="inline-block w-5 h-5 mr-2" />
            重置
          </button>
        </div>

        {/* 番茄统计 */}
        <div className="flex items-center justify-center space-x-2 text-gray-600">
          <Target className="w-5 h-5" />
          <span>今日已完成 {completedSessions} 个番茄</span>
        </div>
      </div>

      {/* 设置面板 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="flex items-center justify-between w-full mb-4"
        >
          <h3 className="flex items-center text-lg font-medium">
            <Settings className="w-5 h-5 mr-2" />
            计时器设置
          </h3>
          <span className="text-gray-400">{showSettings ? '收起' : '展开'}</span>
        </button>

        {showSettings && (
          <div className="space-y-4">
            {/* 时间设置 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  专注时间（分钟）
                </label>
                <input
                  type="number"
                  min="1"
                  max="60"
                  value={settings.workMinutes}
                  onChange={(e) =>
                    setSettings({ ...settings, workMinutes: parseInt(e.target.value) || 25 })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  短休息（分钟）
                </label>
                <input
                  type="number"
                  min="1"
                  max="30"
                  value={settings.shortBreakMinutes}
                  onChange={(e) =>
                    setSettings({ ...settings, shortBreakMinutes: parseInt(e.target.value) || 5 })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  长休息（分钟）
                </label>
                <input
                  type="number"
                  min="1"
                  max="60"
                  value={settings.longBreakMinutes}
                  onChange={(e) =>
                    setSettings({ ...settings, longBreakMinutes: parseInt(e.target.value) || 15 })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>

            {/* 其他设置 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                长休息间隔（番茄数）
              </label>
              <input
                type="number"
                min="2"
                max="10"
                value={settings.sessionsUntilLongBreak}
                onChange={(e) =>
                  setSettings({ ...settings, sessionsUntilLongBreak: parseInt(e.target.value) || 4 })
                }
                className="w-32 px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* 开关设置 */}
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.autoStartBreaks}
                  onChange={(e) =>
                    setSettings({ ...settings, autoStartBreaks: e.target.checked })
                  }
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">自动开始休息</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.autoStartPomodoros}
                  onChange={(e) =>
                    setSettings({ ...settings, autoStartPomodoros: e.target.checked })
                  }
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">自动开始下一个番茄</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.soundEnabled}
                  onChange={(e) =>
                    setSettings({ ...settings, soundEnabled: e.target.checked })
                  }
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700 flex items-center">
                  {settings.soundEnabled ? (
                    <Bell className="w-4 h-4 mr-1" />
                  ) : (
                    <BellOff className="w-4 h-4 mr-1" />
                  )}
                  提示音
                </span>
              </label>
            </div>
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Timer className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">番茄工作法</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>选择一个待完成的任务</li>
              <li>设置计时器至 25 分钟（一个番茄时间）</li>
              <li>专注工作，直到计时器响起</li>
              <li>短暂休息 5 分钟</li>
              <li>每 4 个番茄时间，休息 15-30 分钟</li>
            </ul>
            <p className="mt-2">
              注：为保证提示音正常播放，请确保浏览器允许音频自动播放
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
