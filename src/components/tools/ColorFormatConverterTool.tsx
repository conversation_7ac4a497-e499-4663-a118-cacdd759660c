'use client';

import React, { useState, useMemo, use<PERSON><PERSON>back, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { 
  Copy, 
  RefreshCw, 
  Trash2, 
  Download, 
  Upload, 
  Palette, 
  Eye, 
  Shuffle, 
  Save,
  History,
  BookOpen,
  Lightbulb,
  Calculator,
  Pipette
} from 'lucide-react';

interface ColorData {
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
  hsv: { h: number; s: number; v: number };
  cmyk: { c: number; m: number; y: number; k: number };
  lab: { l: number; a: number; b: number };
  xyz: { x: number; y: number; z: number };
  name?: string;
}

interface ColorScheme {
  name: string;
  colors: ColorData[];
  description: string;
}

interface ColorHistory {
  color: ColorData;
  timestamp: string;
}

const COLOR_SPACES = [
  { id: 'hex', name: 'HEX', example: '#FF5733' },
  { id: 'rgb', name: 'RGB', example: 'rgb(255, 87, 51)' },
  { id: 'hsl', name: 'HSL', example: 'hsl(12, 100%, 60%)' },
  { id: 'hsv', name: 'HSV', example: 'hsv(12, 80%, 100%)' },
  { id: 'cmyk', name: 'CMYK', example: 'cmyk(0%, 66%, 80%, 0%)' },
  { id: 'lab', name: 'LAB', example: 'lab(62, 53, 54)' },
  { id: 'xyz', name: 'XYZ', example: 'xyz(41, 28, 8)' }
];

const PREDEFINED_COLORS = [
  { name: '红色', hex: '#FF0000' },
  { name: '绿色', hex: '#00FF00' },
  { name: '蓝色', hex: '#0000FF' },
  { name: '黄色', hex: '#FFFF00' },
  { name: '品红', hex: '#FF00FF' },
  { name: '青色', hex: '#00FFFF' },
  { name: '黑色', hex: '#000000' },
  { name: '白色', hex: '#FFFFFF' },
  { name: '灰色', hex: '#808080' },
  { name: '橙色', hex: '#FFA500' },
  { name: '紫色', hex: '#800080' },
  { name: '棕色', hex: '#A52A2A' },
  { name: '粉色', hex: '#FFC0CB' },
  { name: '海军蓝', hex: '#000080' },
  { name: '金色', hex: '#FFD700' },
  { name: '银色', hex: '#C0C0C0' }
];

export default function ColorFormatConverterTool() {
  const [inputColor, setInputColor] = useState('#3B82F6');
  const [inputFormat, setInputFormat] = useState('hex');
  const [colorData, setColorData] = useState<ColorData | null>(null);
  const [history, setHistory] = useState<ColorHistory[]>([]);
  const [savedPalettes, setSavedPalettes] = useState<ColorScheme[]>([]);
  const [selectedScheme, setSelectedScheme] = useState<ColorScheme | null>(null);
  const [error, setError] = useState('');
  const [copiedFormat, setCopiedFormat] = useState('');

  // 颜色转换函数
  const hexToRgb = useCallback((hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }, []);

  const rgbToHex = useCallback((r: number, g: number, b: number): string => {
    return "#" + [r, g, b].map(x => {
      const hex = Math.round(x).toString(16);
      return hex.length === 1 ? "0" + hex : hex;
    }).join("");
  }, []);

  const rgbToHsl = useCallback((r: number, g: number, b: number): { h: number; s: number; l: number } => {
    r /= 255;
    g /= 255;
    b /= 255;
    
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    };
  }, []);

  const rgbToHsv = useCallback((r: number, g: number, b: number): { h: number; s: number; v: number } => {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0;
    const v = max;
    const d = max - min;
    const s = max === 0 ? 0 : d / max;

    if (max !== min) {
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      v: Math.round(v * 100)
    };
  }, []);

  const rgbToCmyk = useCallback((r: number, g: number, b: number): { c: number; m: number; y: number; k: number } => {
    r /= 255;
    g /= 255;
    b /= 255;

    const k = 1 - Math.max(r, Math.max(g, b));
    const c = k === 1 ? 0 : (1 - r - k) / (1 - k);
    const m = k === 1 ? 0 : (1 - g - k) / (1 - k);
    const y = k === 1 ? 0 : (1 - b - k) / (1 - k);

    return {
      c: Math.round(c * 100),
      m: Math.round(m * 100),
      y: Math.round(y * 100),
      k: Math.round(k * 100)
    };
  }, []);

  const rgbToLab = useCallback((r: number, g: number, b: number): { l: number; a: number; b: number } => {
    // 简化的 RGB 到 LAB 转换
    let rNorm = r / 255;
    let gNorm = g / 255;
    let bNorm = b / 255;

    // Gamma correction
    rNorm = rNorm > 0.04045 ? Math.pow((rNorm + 0.055) / 1.055, 2.4) : rNorm / 12.92;
    gNorm = gNorm > 0.04045 ? Math.pow((gNorm + 0.055) / 1.055, 2.4) : gNorm / 12.92;
    bNorm = bNorm > 0.04045 ? Math.pow((bNorm + 0.055) / 1.055, 2.4) : bNorm / 12.92;

    // Convert to XYZ (D65 illuminant)
    let x = (rNorm * 0.4124 + gNorm * 0.3576 + bNorm * 0.1805) / 0.95047;
    let y = (rNorm * 0.2126 + gNorm * 0.7152 + bNorm * 0.0722) / 1.00000;
    let z = (rNorm * 0.0193 + gNorm * 0.1192 + bNorm * 0.9505) / 1.08883;

    // Convert XYZ to LAB
    x = x > 0.008856 ? Math.pow(x, 1/3) : (7.787 * x + 16/116);
    y = y > 0.008856 ? Math.pow(y, 1/3) : (7.787 * y + 16/116);
    z = z > 0.008856 ? Math.pow(z, 1/3) : (7.787 * z + 16/116);

    return {
      l: Math.round(116 * y - 16),
      a: Math.round(500 * (x - y)),
      b: Math.round(200 * (y - z))
    };
  }, []);

  const rgbToXyz = useCallback((r: number, g: number, b: number): { x: number; y: number; z: number } => {
    let rNorm = r / 255;
    let gNorm = g / 255;
    let bNorm = b / 255;

    // Gamma correction
    rNorm = rNorm > 0.04045 ? Math.pow((rNorm + 0.055) / 1.055, 2.4) : rNorm / 12.92;
    gNorm = gNorm > 0.04045 ? Math.pow((gNorm + 0.055) / 1.055, 2.4) : gNorm / 12.92;
    bNorm = bNorm > 0.04045 ? Math.pow((bNorm + 0.055) / 1.055, 2.4) : bNorm / 12.92;

    return {
      x: Math.round((rNorm * 0.4124 + gNorm * 0.3576 + bNorm * 0.1805) * 100),
      y: Math.round((rNorm * 0.2126 + gNorm * 0.7152 + bNorm * 0.0722) * 100),
      z: Math.round((rNorm * 0.0193 + gNorm * 0.1192 + bNorm * 0.9505) * 100)
    };
  }, []);

  const parseColorInput = useCallback((input: string, format: string): ColorData | null => {
    try {
      let rgb: { r: number; g: number; b: number } | null = null;

      switch (format) {
        case 'hex': {
          rgb = hexToRgb(input);
          break;
        }
        case 'rgb': {
          const rgbMatch = input.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
          if (rgbMatch) {
            rgb = {
              r: parseInt(rgbMatch[1]),
              g: parseInt(rgbMatch[2]),
              b: parseInt(rgbMatch[3])
            };
          }
          break;
        }
        case 'hsl': {
          const hslMatch = input.match(/hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)/);
          if (hslMatch) {
            const h = parseInt(hslMatch[1]) / 360;
            const s = parseInt(hslMatch[2]) / 100;
            const l = parseInt(hslMatch[3]) / 100;
            
            // HSL to RGB conversion
            const c = (1 - Math.abs(2 * l - 1)) * s;
            const x = c * (1 - Math.abs((h * 6) % 2 - 1));
            const m = l - c / 2;
            
            let r = 0, g = 0, b = 0;
            const hueSegment = Math.floor(h * 6);
            
            switch (hueSegment) {
              case 0: [r, g, b] = [c, x, 0]; break;
              case 1: [r, g, b] = [x, c, 0]; break;
              case 2: [r, g, b] = [0, c, x]; break;
              case 3: [r, g, b] = [0, x, c]; break;
              case 4: [r, g, b] = [x, 0, c]; break;
              case 5: [r, g, b] = [c, 0, x]; break;
            }
            
            rgb = {
              r: Math.round((r + m) * 255),
              g: Math.round((g + m) * 255),
              b: Math.round((b + m) * 255)
            };
          }
          break;
        }
      }

      if (!rgb) return null;

      const hex = rgbToHex(rgb.r, rgb.g, rgb.b);
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      const hsv = rgbToHsv(rgb.r, rgb.g, rgb.b);
      const cmyk = rgbToCmyk(rgb.r, rgb.g, rgb.b);
      const lab = rgbToLab(rgb.r, rgb.g, rgb.b);
      const xyz = rgbToXyz(rgb.r, rgb.g, rgb.b);

      return {
        hex,
        rgb,
        hsl,
        hsv,
        cmyk,
        lab,
        xyz
      };
    } catch (err) {
      return null;
    }
  }, [hexToRgb, rgbToHex, rgbToHsl, rgbToHsv, rgbToCmyk, rgbToLab, rgbToXyz]);

  const generateColorScheme = useCallback((baseColor: ColorData, type: string): ColorData[] => {
    const { h, s, l } = baseColor.hsl;
    const schemes: ColorData[] = [];

    switch (type) {
      case 'monochromatic': {
        // 单色方案 - 不同明度
        for (let i = 0; i < 5; i++) {
          const newL = Math.max(10, Math.min(90, l + (i - 2) * 20));
          const tempColor = parseColorInput(`hsl(${h}, ${s}%, ${newL}%)`, 'hsl');
          if (tempColor) schemes.push(tempColor);
        }
        break;
      }
      case 'analogous': {
        // 类似色方案 - 相邻色相
        for (let i = 0; i < 5; i++) {
          const newH = (h + (i - 2) * 30 + 360) % 360;
          const tempColor = parseColorInput(`hsl(${newH}, ${s}%, ${l}%)`, 'hsl');
          if (tempColor) schemes.push(tempColor);
        }
        break;
      }
      case 'complementary': {
        // 互补色方案
        schemes.push(baseColor);
        const complementH = (h + 180) % 360;
        const tempColor = parseColorInput(`hsl(${complementH}, ${s}%, ${l}%)`, 'hsl');
        if (tempColor) schemes.push(tempColor);
        break;
      }
      case 'triadic': {
        // 三等分方案
        for (let i = 0; i < 3; i++) {
          const newH = (h + i * 120) % 360;
          const tempColor = parseColorInput(`hsl(${newH}, ${s}%, ${l}%)`, 'hsl');
          if (tempColor) schemes.push(tempColor);
        }
        break;
      }
      case 'tetradic': {
        // 四等分方案
        for (let i = 0; i < 4; i++) {
          const newH = (h + i * 90) % 360;
          const tempColor = parseColorInput(`hsl(${newH}, ${s}%, ${l}%)`, 'hsl');
          if (tempColor) schemes.push(tempColor);
        }
        break;
      }
      default:
        schemes.push(baseColor);
    }

    return schemes;
  }, [parseColorInput]);

  const formatColorOutput = useCallback((color: ColorData) => {
    return {
      hex: color.hex.toUpperCase(),
      rgb: `rgb(${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b})`,
      rgba: `rgba(${color.rgb.r}, ${color.rgb.g}, ${color.rgb.b}, 1)`,
      hsl: `hsl(${color.hsl.h}, ${color.hsl.s}%, ${color.hsl.l}%)`,
      hsla: `hsla(${color.hsl.h}, ${color.hsl.s}%, ${color.hsl.l}%, 1)`,
      hsv: `hsv(${color.hsv.h}, ${color.hsv.s}%, ${color.hsv.v}%)`,
      cmyk: `cmyk(${color.cmyk.c}%, ${color.cmyk.m}%, ${color.cmyk.y}%, ${color.cmyk.k}%)`,
      lab: `lab(${color.lab.l}, ${color.lab.a}, ${color.lab.b})`,
      xyz: `xyz(${color.xyz.x}, ${color.xyz.y}, ${color.xyz.z})`,
      css: `--color: ${color.hex.toUpperCase()};`,
      scss: `$color: ${color.hex.toUpperCase()};`,
      tailwind: `bg-[${color.hex.toUpperCase()}]`
    };
  }, []);

  useEffect(() => {
    const parsed = parseColorInput(inputColor, inputFormat);
    if (parsed) {
      setColorData(parsed);
      setError('');
    } else {
      setError('无效的颜色格式');
    }
  }, [inputColor, inputFormat, parseColorInput]);

  const handleCopy = async (text: string, format: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedFormat(format);
      setTimeout(() => setCopiedFormat(''), 2000);
    } catch (err) {
      setError('复制失败');
    }
  };

  const addToHistory = () => {
    if (!colorData) return;
    
    const newEntry: ColorHistory = {
      color: colorData,
      timestamp: new Date().toLocaleString()
    };
    
    setHistory(prev => [newEntry, ...prev.slice(0, 19)]);
  };

  const generateRandomColor = () => {
    const randomHex = '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
    setInputColor(randomHex);
    setInputFormat('hex');
  };

  const exportPalette = () => {
    if (!selectedScheme) return;
    
    const paletteData = {
      name: selectedScheme.name,
      colors: selectedScheme.colors.map(color => formatColorOutput(color)),
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(paletteData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `color-palette-${selectedScheme.name.toLowerCase().replace(/\s+/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const colorFormats = useMemo(() => {
    if (!colorData) return null;
    return formatColorOutput(colorData);
  }, [colorData, formatColorOutput]);

  const colorSchemes = useMemo(() => {
    if (!colorData) return [];
    
    return [
      { name: '单色', type: 'monochromatic', colors: generateColorScheme(colorData, 'monochromatic') },
      { name: '类似色', type: 'analogous', colors: generateColorScheme(colorData, 'analogous') },
      { name: '互补色', type: 'complementary', colors: generateColorScheme(colorData, 'complementary') },
      { name: '三等分', type: 'triadic', colors: generateColorScheme(colorData, 'triadic') },
      { name: '四等分', type: 'tetradic', colors: generateColorScheme(colorData, 'tetradic') }
    ];
  }, [colorData, generateColorScheme]);

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>颜色格式转换器</CardTitle>
          <CardDescription>
            专业的颜色格式转换工具，支持HEX、RGB、HSL、HSV、CMYK、LAB、XYZ等多种格式互转，
            提供配色方案生成、颜色历史记录和调色板管理功能。
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* 输入区域 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg">颜色输入</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <div className="text-sm text-red-500 bg-red-50 p-2 rounded">
                {error}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="inputFormat">输入格式</Label>
                <Select value={inputFormat} onValueChange={setInputFormat}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {COLOR_SPACES.map(space => (
                      <SelectItem key={space.id} value={space.id}>
                        {space.name} - {space.example}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="inputColor">颜色值</Label>
                <div className="flex gap-2">
                  <Input
                    id="inputColor"
                    value={inputColor}
                    onChange={(e) => setInputColor(e.target.value)}
                    placeholder="输入颜色值..."
                  />
                  {inputFormat === 'hex' && (
                    <input
                      type="color"
                      value={colorData?.hex || '#000000'}
                      onChange={(e) => setInputColor(e.target.value)}
                      className="w-12 h-10 border rounded cursor-pointer"
                    />
                  )}
                </div>
              </div>
            </div>

            <div className="flex gap-2 flex-wrap">
              <Button variant="outline" onClick={generateRandomColor}>
                <Shuffle className="h-4 w-4 mr-1" />
                随机颜色
              </Button>
              <Button variant="outline" onClick={addToHistory} disabled={!colorData}>
                <Save className="h-4 w-4 mr-1" />
                保存到历史
              </Button>
            </div>

            {/* 颜色预览 */}
            {colorData && (
              <div className="space-y-4">
                <div 
                  className="w-full h-32 rounded border-2 border-gray-300 flex items-center justify-center text-white font-bold text-lg shadow-md"
                  style={{ backgroundColor: colorData.hex }}
                >
                  {colorData.hex}
                </div>

                {/* 快速选择预设颜色 */}
                <div className="space-y-2">
                  <Label>快速选择：</Label>
                  <div className="grid grid-cols-8 gap-2">
                    {PREDEFINED_COLORS.map((color, index) => (
                      <button
                        key={index}
                        className="w-8 h-8 rounded border-2 border-gray-300 cursor-pointer hover:scale-110 transition-transform"
                        style={{ backgroundColor: color.hex }}
                        title={color.name}
                        onClick={() => {
                          setInputColor(color.hex);
                          setInputFormat('hex');
                        }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 历史记录 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg flex items-center gap-2">
                <History className="h-5 w-5" />
                历史记录
              </CardTitle>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setHistory([])}
                disabled={history.length === 0}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-[300px] overflow-y-auto">
              {history.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  暂无历史记录
                </div>
              ) : (
                history.map((entry, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-2 p-2 border rounded cursor-pointer hover:bg-gray-50"
                    onClick={() => {
                      setInputColor(entry.color.hex);
                      setInputFormat('hex');
                    }}
                  >
                    <div
                      className="w-6 h-6 rounded border"
                      style={{ backgroundColor: entry.color.hex }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-mono">{entry.color.hex}</div>
                      <div className="text-xs text-muted-foreground">{entry.timestamp}</div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 转换结果 */}
      {colorFormats && (
        <Tabs defaultValue="formats" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="formats">格式转换</TabsTrigger>
            <TabsTrigger value="schemes">配色方案</TabsTrigger>
            <TabsTrigger value="analysis">颜色分析</TabsTrigger>
          </TabsList>

          <TabsContent value="formats" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">所有格式</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(colorFormats).map(([format, value]) => (
                    <div key={format} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex-1">
                        <div className="font-medium text-sm uppercase">{format}</div>
                        <div className="font-mono text-sm text-muted-foreground">{value}</div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopy(value, format)}
                      >
                        {copiedFormat === format ? (
                          <Eye className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schemes" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">配色方案</CardTitle>
                  {selectedScheme && (
                    <Button variant="outline" size="sm" onClick={exportPalette}>
                      <Download className="h-4 w-4 mr-1" />
                      导出调色板
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {colorSchemes.map((scheme, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label className="font-medium">{scheme.name}</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedScheme(scheme)}
                      >
                        选择方案
                      </Button>
                    </div>
                    <div className="flex gap-2">
                      {scheme.colors.map((color, colorIndex) => (
                        <div
                          key={colorIndex}
                          className="flex-1 h-16 rounded border cursor-pointer hover:scale-105 transition-transform"
                          style={{ backgroundColor: color.hex }}
                          title={color.hex}
                          onClick={() => {
                            setInputColor(color.hex);
                            setInputFormat('hex');
                          }}
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">颜色分析</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {colorData && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <h4 className="font-medium">基础信息</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>亮度:</span>
                          <Badge variant="outline">{colorData.hsl.l}%</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>饱和度:</span>
                          <Badge variant="outline">{colorData.hsl.s}%</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>色相:</span>
                          <Badge variant="outline">{colorData.hsl.h}°</Badge>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium">感知特征</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>是否为暖色:</span>
                          <Badge variant={colorData.hsl.h >= 0 && colorData.hsl.h <= 120 ? "default" : "secondary"}>
                            {colorData.hsl.h >= 0 && colorData.hsl.h <= 120 ? '是' : '否'}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>对比度等级:</span>
                          <Badge variant={colorData.hsl.l > 50 ? "outline" : "default"}>
                            {colorData.hsl.l > 50 ? '浅色' : '深色'}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>饱和度等级:</span>
                          <Badge variant={colorData.hsl.s > 50 ? "default" : "secondary"}>
                            {colorData.hsl.s > 75 ? '高饱和' : colorData.hsl.s > 25 ? '中饱和' : '低饱和'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            使用说明
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                功能特点
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• 支持7种主要颜色格式互转</li>
                <li>• 智能配色方案生成</li>
                <li>• 颜色历史记录管理</li>
                <li>• 调色板导出功能</li>
                <li>• 颜色分析和特征识别</li>
                <li>• CSS/SCSS/Tailwind代码生成</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Calculator className="h-4 w-4" />
                支持格式
              </h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• <span className="font-mono">HEX</span>: #FF5733</li>
                <li>• <span className="font-mono">RGB</span>: rgb(255, 87, 51)</li>
                <li>• <span className="font-mono">HSL</span>: hsl(12, 100%, 60%)</li>
                <li>• <span className="font-mono">HSV</span>: hsv(12, 80%, 100%)</li>
                <li>• <span className="font-mono">CMYK</span>: cmyk(0%, 66%, 80%, 0%)</li>
                <li>• <span className="font-mono">LAB</span>: lab(62, 53, 54)</li>
                <li>• <span className="font-mono">XYZ</span>: xyz(41, 28, 8)</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}