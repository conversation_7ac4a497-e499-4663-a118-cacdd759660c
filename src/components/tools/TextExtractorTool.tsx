'use client'

import React, { useState, useCallback } from 'react'
import { Search, Copy, Check, FileText, Download, Upload, Filter, Hash, Link, Mail, Phone, CreditCard, Calendar, Globe } from 'lucide-react'

interface ExtractOption {
  id: string
  label: string
  icon: React.ElementType
  pattern: RegExp
  description: string
  example: string
}

const extractOptions: ExtractOption[] = [
  {
    id: 'email',
    label: '邮箱地址',
    icon: Mail,
    pattern: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
    description: '提取所有邮箱地址',
    example: '<EMAIL>'
  },
  {
    id: 'url',
    label: '网址链接',
    icon: Link,
    pattern: /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g,
    description: '提取所有 HTTP/HTTPS 链接',
    example: 'https://example.com'
  },
  {
    id: 'phone',
    label: '电话号码',
    icon: Phone,
    pattern: /(\+?86)?1[3-9]\d{9}|(\d{3,4}-)?\d{7,8}/g,
    description: '提取中国大陆手机号和座机号',
    example: '13812345678, 010-12345678'
  },
  {
    id: 'ip',
    label: 'IP地址',
    icon: Globe,
    pattern: /\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b/g,
    description: '提取 IPv4 地址',
    example: '***********'
  },
  {
    id: 'number',
    label: '数字',
    icon: Hash,
    pattern: /-?\d+\.?\d*/g,
    description: '提取所有数字（包括小数）',
    example: '123, -45.67'
  },
  {
    id: 'chinese',
    label: '中文字符',
    icon: FileText,
    pattern: /[\u4e00-\u9fa5]+/g,
    description: '提取所有中文字符',
    example: '你好世界'
  },
  {
    id: 'english',
    label: '英文单词',
    icon: FileText,
    pattern: /[a-zA-Z]+/g,
    description: '提取所有英文单词',
    example: 'Hello World'
  },
  {
    id: 'date',
    label: '日期',
    icon: Calendar,
    pattern: /\d{4}[-\/年]\d{1,2}[-\/月]\d{1,2}[日]?|\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4}/g,
    description: '提取常见日期格式',
    example: '2024-01-01, 2024年1月1日'
  },
  {
    id: 'time',
    label: '时间',
    icon: Calendar,
    pattern: /\d{1,2}:\d{2}(:\d{2})?(\s*[AaPp][Mm])?/g,
    description: '提取时间格式',
    example: '12:30, 15:45:30'
  },
  {
    id: 'hashtag',
    label: '话题标签',
    icon: Hash,
    pattern: /#[^\s#]+/g,
    description: '提取 # 开头的话题标签',
    example: '#技术分享 #前端开发'
  },
  {
    id: 'mention',
    label: '@提及',
    icon: Mail,
    pattern: /@[^\s@]+/g,
    description: '提取 @ 开头的用户提及',
    example: '@username @某某'
  },
  {
    id: 'creditcard',
    label: '银行卡号',
    icon: CreditCard,
    pattern: /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g,
    description: '提取16位银行卡号格式',
    example: '1234 5678 9012 3456'
  }
]

export default function TextExtractorTool() {
  const [inputText, setInputText] = useState('')
  const [selectedOptions, setSelectedOptions] = useState<string[]>(['email', 'url'])
  const [extractedResults, setExtractedResults] = useState<Record<string, string[]>>({})
  const [customPattern, setCustomPattern] = useState('')
  const [customFlags, setCustomFlags] = useState('g')
  const [useCustom, setUseCustom] = useState(false)
  const [copied, setCopied] = useState<string | null>(null)
  
  // 执行提取
  const performExtraction = useCallback(() => {
    if (!inputText.trim()) {
      setExtractedResults({})
      return
    }
    
    const results: Record<string, string[]> = {}
    
    if (useCustom && customPattern) {
      try {
        const regex = new RegExp(customPattern, customFlags)
        const matches = inputText.match(regex) || []
        const uniqueMatches = Array.from(new Set(matches))
        if (uniqueMatches.length > 0) {
          results['custom'] = uniqueMatches
        }
      } catch (err) {
        console.error('自定义正则表达式错误:', err)
      }
    } else {
      selectedOptions.forEach(optionId => {
        const option = extractOptions.find(opt => opt.id === optionId)
        if (option) {
          const matches = inputText.match(option.pattern) || []
          const uniqueMatches = Array.from(new Set(matches))
          if (uniqueMatches.length > 0) {
            results[optionId] = uniqueMatches
          }
        }
      })
    }
    
    setExtractedResults(results)
  }, [inputText, selectedOptions, useCustom, customPattern, customFlags])
  
  // 监听变化
  React.useEffect(() => {
    performExtraction()
  }, [performExtraction])
  
  // 切换选项
  const toggleOption = (optionId: string) => {
    setSelectedOptions(prev => 
      prev.includes(optionId)
        ? prev.filter(id => id !== optionId)
        : [...prev, optionId]
    )
  }
  
  // 复制结果
  const copyResults = async (optionId: string) => {
    const results = extractedResults[optionId]
    if (!results) return
    
    try {
      await navigator.clipboard.writeText(results.join('\n'))
      setCopied(optionId)
      setTimeout(() => setCopied(null), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }
  
  // 复制所有结果
  const copyAllResults = async () => {
    const allResults = Object.entries(extractedResults)
      .map(([key, values]) => {
        const option = extractOptions.find(opt => opt.id === key)
        const label = key === 'custom' ? '自定义匹配' : option?.label || key
        return `【${label}】\n${values.join('\n')}`
      })
      .join('\n\n')
    
    try {
      await navigator.clipboard.writeText(allResults)
      setCopied('all')
      setTimeout(() => setCopied(null), 2000)
    } catch (err) {
      console.error('复制失败:', err)
    }
  }
  
  // 下载结果
  const downloadResults = () => {
    const allResults = Object.entries(extractedResults)
      .map(([key, values]) => {
        const option = extractOptions.find(opt => opt.id === key)
        const label = key === 'custom' ? '自定义匹配' : option?.label || key
        return `【${label}】\n${values.join('\n')}`
      })
      .join('\n\n')
    
    const blob = new Blob([allResults], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `extracted_${Date.now()}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  
  // 上传文件
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return
    
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputText(content)
    }
    reader.readAsText(file)
  }
  
  // 统计信息
  const totalMatches = Object.values(extractedResults).reduce((sum, matches) => sum + matches.length, 0)
  
  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">文本提取工具</h2>
        <p className="text-gray-600">
          从文本中智能提取邮箱、网址、电话号码等特定内容
        </p>
      </div>
      
      {/* 提取选项 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-medium">提取选项</h3>
          <button
            onClick={() => setUseCustom(!useCustom)}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              useCustom
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            自定义正则
          </button>
        </div>
        
        {useCustom ? (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">正则表达式</label>
              <input
                type="text"
                value={customPattern}
                onChange={(e) => setCustomPattern(e.target.value)}
                placeholder="输入正则表达式，如: \d+"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">匹配标志</label>
              <input
                type="text"
                value={customFlags}
                onChange={(e) => setCustomFlags(e.target.value)}
                placeholder="如: g, gi, gm"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {extractOptions.map(option => {
              const Icon = option.icon
              const isSelected = selectedOptions.includes(option.id)
              return (
                <button
                  key={option.id}
                  onClick={() => toggleOption(option.id)}
                  className={`p-3 rounded-lg border transition-all ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  title={option.description}
                >
                  <Icon className="w-5 h-5 mx-auto mb-1" />
                  <div className="text-sm font-medium">{option.label}</div>
                  <div className="text-xs text-gray-500 mt-1">{option.example}</div>
                </button>
              )
            })}
          </div>
        )}
      </div>
      
      {/* 主要内容区 */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* 输入区 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="font-medium">输入文本</label>
            <label className="text-sm text-gray-600 hover:text-gray-800 cursor-pointer">
              <input
                type="file"
                onChange={handleFileUpload}
                className="hidden"
                accept=".txt"
              />
              <Upload className="w-4 h-4 inline mr-1" />
              上传文件
            </label>
          </div>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="粘贴或输入包含邮箱、网址、电话等信息的文本..."
            className="w-full h-96 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
          />
          <div className="mt-2 text-sm text-gray-500">
            字符数: {inputText.length}
          </div>
        </div>
        
        {/* 结果区 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="font-medium">
              提取结果 {totalMatches > 0 && `(${totalMatches}项)`}
            </label>
            {totalMatches > 0 && (
              <div className="flex gap-2">
                <button
                  onClick={copyAllResults}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  {copied === 'all' ? (
                    <><Check className="w-4 h-4 inline mr-1 text-green-500" />已复制</>
                  ) : (
                    <><Copy className="w-4 h-4 inline mr-1" />复制全部</>
                  )}
                </button>
                <button
                  onClick={downloadResults}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  <Download className="w-4 h-4 inline mr-1" />
                  下载
                </button>
              </div>
            )}
          </div>
          
          <div className="h-96 overflow-y-auto border border-gray-300 rounded-md p-3 bg-gray-50">
            {Object.keys(extractedResults).length === 0 ? (
              <div className="text-gray-500 text-center py-8">
                <Filter className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                <p>暂无提取结果</p>
                <p className="text-sm mt-1">请输入文本并选择提取选项</p>
              </div>
            ) : (
              <div className="space-y-4">
                {Object.entries(extractedResults).map(([key, values]) => {
                  const option = extractOptions.find(opt => opt.id === key)
                  const Icon = option?.icon || Filter
                  const label = key === 'custom' ? '自定义匹配' : option?.label || key
                  
                  return (
                    <div key={key} className="bg-white rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <Icon className="w-4 h-4 mr-2 text-gray-600" />
                          <span className="font-medium">{label}</span>
                          <span className="ml-2 text-sm text-gray-500">({values.length}项)</span>
                        </div>
                        <button
                          onClick={() => copyResults(key)}
                          className="text-sm text-gray-600 hover:text-gray-800"
                        >
                          {copied === key ? (
                            <Check className="w-4 h-4 text-green-500" />
                          ) : (
                            <Copy className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                      <div className="space-y-1">
                        {values.map((value, index) => (
                          <div
                            key={index}
                            className="px-2 py-1 bg-gray-100 rounded text-sm font-mono break-all"
                          >
                            {value}
                          </div>
                        ))}
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Search className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>选择要提取的内容类型，或使用自定义正则表达式</li>
              <li>输入或粘贴包含目标内容的文本</li>
              <li>工具会自动提取并去重，结果实时显示</li>
              <li>支持提取邮箱、网址、电话、IP地址、日期时间等多种格式</li>
              <li>可以同时选择多个提取选项</li>
              <li>提取结果可以分别复制或整体下载</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
