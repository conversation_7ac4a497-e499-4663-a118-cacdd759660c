'use client'

import React, { useState, useEffect } from 'react'
import { Calendar, Clock, RotateCcw, Download, Info, ChevronRight, ChevronLeft } from 'lucide-react'
import { format, getISOWeek, getWeek, startOfWeek, endOfWeek, startOfYear, endOfYear, addWeeks, subWeeks, parseISO } from 'date-fns'
import { zhCN, enUS, ja } from 'date-fns/locale'

interface WeekStandard {
  id: string
  name: {
    zh: string
    en: string
    ja: string
  }
  description: {
    zh: string
    en: string
    ja: string
  }
}

interface WeekInfo {
  weekNumber: number
  year: number
  startDate: Date
  endDate: Date
  totalWeeks: number
  standard: string
}

interface HistoryRecord {
  id: string
  date: Date
  weekInfo: WeekInfo
  timestamp: Date
}

const weekStandards: WeekStandard[] = [
  {
    id: 'iso',
    name: {
      zh: 'ISO 8601标准',
      en: 'ISO 8601 Standard',
      ja: 'ISO 8601標準'
    },
    description: {
      zh: '国际标准，周一为一周第一天，第一周包含1月4日',
      en: 'International standard, Monday is first day, first week contains January 4th',
      ja: '国際標準、月曜日が週の最初の日、第1週は1月4日を含む'
    }
  },
  {
    id: 'us',
    name: {
      zh: '美国标准',
      en: 'US Standard',
      ja: '米国標準'
    },
    description: {
      zh: '周日为一周第一天，第一周从1月1日开始',
      en: 'Sunday is first day, first week starts from January 1st',
      ja: '日曜日が週の最初の日、第1週は1月1日から開始'
    }
  }
]

export default function WeekNumberCalculatorTool() {
  const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'))
  const [standard, setStandard] = useState<string>('iso')
  const [currentWeekInfo, setCurrentWeekInfo] = useState<WeekInfo | null>(null)
  const [yearOverview, setYearOverview] = useState<WeekInfo[]>([])
  const [history, setHistory] = useState<HistoryRecord[]>([])
  const [showYearOverview, setShowYearOverview] = useState(false)
  const [locale, setLocale] = useState<'zh' | 'en' | 'ja'>('zh')

  const dateLocales = {
    zh: zhCN,
    en: enUS,
    ja: ja
  }

  // 计算周数信息
  const calculateWeekInfo = (date: Date, std: string): WeekInfo => {
    let weekNumber: number
    let startDate: Date
    let endDate: Date
    let totalWeeks: number

    if (std === 'iso') {
      weekNumber = getISOWeek(date)
      startDate = startOfWeek(date, { weekStartsOn: 1 }) // 周一开始
      endDate = endOfWeek(date, { weekStartsOn: 1 })
      
      // 计算该年总周数
      const yearEnd = endOfYear(date)
      totalWeeks = getISOWeek(yearEnd)
    } else {
      // 美国标准
      weekNumber = getWeek(date, { weekStartsOn: 0 }) // 周日开始
      startDate = startOfWeek(date, { weekStartsOn: 0 })
      endDate = endOfWeek(date, { weekStartsOn: 0 })
      
      // 计算该年总周数
      const yearEnd = endOfYear(date)
      totalWeeks = getWeek(yearEnd, { weekStartsOn: 0 })
    }

    return {
      weekNumber,
      year: date.getFullYear(),
      startDate,
      endDate,
      totalWeeks,
      standard: std
    }
  }

  // 生成年度周数概览
  const generateYearOverview = (year: number, std: string): WeekInfo[] => {
    const overview: WeekInfo[] = []
    const yearStart = startOfYear(new Date(year, 0, 1))
    
    let currentDate = yearStart
    let weekNum = 1
    
    while (currentDate.getFullYear() === year) {
      const weekInfo = calculateWeekInfo(currentDate, std)
      if (weekInfo.weekNumber >= weekNum) {
        overview.push(weekInfo)
        weekNum = weekInfo.weekNumber + 1
      }
      currentDate = addWeeks(currentDate, 1)
      
      // 防止无限循环
      if (overview.length > 60) break
    }
    
    return overview
  }

  // 更新计算结果
  useEffect(() => {
    try {
      const date = parseISO(selectedDate)
      if (isNaN(date.getTime())) return

      const weekInfo = calculateWeekInfo(date, standard)
      setCurrentWeekInfo(weekInfo)

      // 生成年度概览
      const overview = generateYearOverview(date.getFullYear(), standard)
      setYearOverview(overview)
    } catch (error) {
      console.error('日期计算错误:', error)
    }
  }, [selectedDate, standard])

  // 添加到历史记录
  const addToHistory = () => {
    if (!currentWeekInfo) return

    const record: HistoryRecord = {
      id: Date.now().toString(),
      date: parseISO(selectedDate),
      weekInfo: currentWeekInfo,
      timestamp: new Date()
    }

    setHistory(prev => [record, ...prev.slice(0, 9)]) // 保持最多10条记录
  }

  // 设置为今天
  const setToday = () => {
    setSelectedDate(format(new Date(), 'yyyy-MM-dd'))
  }

  // 上一周/下一周
  const navigateWeek = (direction: 'prev' | 'next') => {
    const date = parseISO(selectedDate)
    const newDate = direction === 'prev' 
      ? subWeeks(date, 1)
      : addWeeks(date, 1)
    setSelectedDate(format(newDate, 'yyyy-MM-dd'))
  }

  // 导出功能
  const exportData = () => {
    if (!currentWeekInfo) return

    const data = {
      date: selectedDate,
      weekInfo: currentWeekInfo,
      yearOverview: yearOverview,
      exportTime: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `week-number-${selectedDate}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const standardInfo = weekStandards.find(s => s.id === standard)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        {/* 标题区域 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-500 rounded-full mb-4">
            <Calendar className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">周数计算器</h1>
          <p className="text-gray-600">精确计算年度周数，支持多种国际标准</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 主计算面板 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 输入设置 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <Calendar className="w-5 h-5 mr-2 text-blue-500" />
                日期设置
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    选择日期
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="date"
                      value={selectedDate}
                      onChange={(e) => setSelectedDate(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <button
                      onClick={setToday}
                      className="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                      title="今天"
                    >
                      <Clock className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    计算标准
                  </label>
                  <select
                    value={standard}
                    onChange={(e) => setStandard(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {weekStandards.map(std => (
                      <option key={std.id} value={std.id}>
                        {std.name[locale]}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* 周导航 */}
              <div className="flex items-center justify-center mt-4 space-x-4">
                <button
                  onClick={() => navigateWeek('prev')}
                  className="flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <ChevronLeft className="w-4 h-4 mr-1" />
                  上一周
                </button>
                <button
                  onClick={() => navigateWeek('next')}
                  className="flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  下一周
                  <ChevronRight className="w-4 h-4 ml-1" />
                </button>
              </div>

              {/* 标准说明 */}
              {standardInfo && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-start">
                    <Info className="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">
                        {standardInfo.name[locale]}
                      </p>
                      <p className="text-sm text-blue-600 mt-1">
                        {standardInfo.description[locale]}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 计算结果 */}
            {currentWeekInfo && (
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">计算结果</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="text-center p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white">
                    <div className="text-3xl font-bold mb-2">
                      第 {currentWeekInfo.weekNumber} 周
                    </div>
                    <div className="text-sm opacity-90">
                      {currentWeekInfo.year}年 • 共{currentWeekInfo.totalWeeks}周
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600">周开始日期：</span>
                      <span className="font-medium">
                        {format(currentWeekInfo.startDate, 'yyyy年MM月dd日 (EEEE)', {
                          locale: dateLocales[locale]
                        })}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600">周结束日期：</span>
                      <span className="font-medium">
                        {format(currentWeekInfo.endDate, 'yyyy年MM月dd日 (EEEE)', {
                          locale: dateLocales[locale]
                        })}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600">计算标准：</span>
                      <span className="font-medium">{standardInfo?.name[locale]}</span>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3 mt-6">
                  <button
                    onClick={addToHistory}
                    className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    添加到历史
                  </button>
                  <button
                    onClick={exportData}
                    className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    导出
                  </button>
                </div>
              </div>
            )}

            {/* 年度概览 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800">年度周数概览</h3>
                <button
                  onClick={() => setShowYearOverview(!showYearOverview)}
                  className="px-3 py-1 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors text-sm"
                >
                  {showYearOverview ? '隐藏' : '显示'}概览
                </button>
              </div>

              {showYearOverview && currentWeekInfo && (
                <div className="space-y-4">
                  <div className="text-sm text-gray-600 mb-3">
                    {currentWeekInfo.year}年共有 {currentWeekInfo.totalWeeks} 周
                  </div>
                  
                  <div className="max-h-96 overflow-y-auto">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {yearOverview.slice(0, 20).map((week, index) => (
                        <div
                          key={index}
                          className={`p-3 rounded-lg border ${
                            week.weekNumber === currentWeekInfo.weekNumber
                              ? 'bg-blue-50 border-blue-200'
                              : 'bg-gray-50 border-gray-200'
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <span className="font-medium">第{week.weekNumber}周</span>
                            <span className="text-sm text-gray-500">
                              {format(week.startDate, 'MM/dd')} - {format(week.endDate, 'MM/dd')}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                    {yearOverview.length > 20 && (
                      <div className="text-center mt-3 text-sm text-gray-500">
                        还有 {yearOverview.length - 20} 周...
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 历史记录 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800">历史记录</h3>
                {history.length > 0 && (
                  <button
                    onClick={() => setHistory([])}
                    className="text-sm text-gray-500 hover:text-red-500 transition-colors"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </button>
                )}
              </div>

              {history.length === 0 ? (
                <p className="text-gray-500 text-center py-4">暂无历史记录</p>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {history.map((record) => (
                    <div
                      key={record.id}
                      className="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => setSelectedDate(format(record.date, 'yyyy-MM-dd'))}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium text-blue-600">
                            第{record.weekInfo.weekNumber}周
                          </div>
                          <div className="text-sm text-gray-600">
                            {format(record.date, 'yyyy-MM-dd')}
                          </div>
                        </div>
                        <div className="text-xs text-gray-400">
                          {format(record.timestamp, 'HH:mm')}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* 快速信息 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">今日信息</h3>
              
              {(() => {
                const today = new Date()
                const todayWeekInfo = calculateWeekInfo(today, standard)
                
                return (
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                      <span className="text-gray-600">今天是：</span>
                      <span className="font-medium">
                        {format(today, 'MM-dd (EEEE)', { locale: dateLocales[locale] })}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                      <span className="text-gray-600">本周是：</span>
                      <span className="font-medium">第{todayWeekInfo.weekNumber}周</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                      <span className="text-gray-600">本年共：</span>
                      <span className="font-medium">{todayWeekInfo.totalWeeks}周</span>
                    </div>
                  </div>
                )
              })()}
            </div>

            {/* 使用说明 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">使用说明</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>选择日期和计算标准查看周数信息</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>支持ISO 8601和美国两种常用标准</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>查看完整年度周数概览</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>支持历史记录和数据导出</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
