'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { FileText, Download, Upload, RefreshCw, AlertCircle, CheckCircle2, File, Image, FileCode, FileSpreadsheet, FileType, FileMusic, FileVideo, Archive } from 'lucide-react'

// 支持的文件格式分类
const FILE_CATEGORIES = {
  text: {
    label: '文本文件',
    icon: FileText,
    formats: [
      { value: 'txt', label: 'TXT', description: '纯文本文件' },
      { value: 'md', label: 'Markdown', description: 'Markdown文档' },
      { value: 'html', label: 'HTML', description: 'HTML网页' },
      { value: 'rtf', label: 'RTF', description: '富文本格式' },
      { value: 'csv', label: 'CSV', description: '逗号分隔值' },
      { value: 'json', label: 'JSON', description: 'JSON数据' },
      { value: 'xml', label: 'XML', description: 'XML文档' },
      { value: 'yaml', label: 'YAML', description: 'YAML配置' },
    ]
  },
  image: {
    label: '图像文件',
    icon: Image,
    formats: [
      { value: 'jpg', label: 'JPG', description: 'JPEG图像' },
      { value: 'png', label: 'PNG', description: 'PNG图像' },
      { value: 'gif', label: 'GIF', description: 'GIF动图' },
      { value: 'bmp', label: 'BMP', description: '位图文件' },
      { value: 'webp', label: 'WebP', description: 'WebP图像' },
      { value: 'svg', label: 'SVG', description: '矢量图形' },
      { value: 'ico', label: 'ICO', description: '图标文件' },
      { value: 'tiff', label: 'TIFF', description: 'TIFF图像' },
    ]
  },
  document: {
    label: '文档文件',
    icon: FileSpreadsheet,
    formats: [
      { value: 'pdf', label: 'PDF', description: 'PDF文档' },
      { value: 'docx', label: 'DOCX', description: 'Word文档' },
      { value: 'xlsx', label: 'XLSX', description: 'Excel表格' },
      { value: 'pptx', label: 'PPTX', description: 'PowerPoint演示' },
      { value: 'odt', label: 'ODT', description: 'OpenDocument文本' },
      { value: 'ods', label: 'ODS', description: 'OpenDocument表格' },
      { value: 'odp', label: 'ODP', description: 'OpenDocument演示' },
    ]
  },
  code: {
    label: '代码文件',
    icon: FileCode,
    formats: [
      { value: 'js', label: 'JavaScript', description: 'JavaScript代码' },
      { value: 'ts', label: 'TypeScript', description: 'TypeScript代码' },
      { value: 'py', label: 'Python', description: 'Python代码' },
      { value: 'java', label: 'Java', description: 'Java代码' },
      { value: 'cpp', label: 'C++', description: 'C++代码' },
      { value: 'c', label: 'C', description: 'C代码' },
      { value: 'go', label: 'Go', description: 'Go代码' },
      { value: 'rs', label: 'Rust', description: 'Rust代码' },
    ]
  },
  audio: {
    label: '音频文件',
    icon: FileMusic,
    formats: [
      { value: 'mp3', label: 'MP3', description: 'MP3音频' },
      { value: 'wav', label: 'WAV', description: 'WAV音频' },
      { value: 'flac', label: 'FLAC', description: 'FLAC无损音频' },
      { value: 'aac', label: 'AAC', description: 'AAC音频' },
      { value: 'ogg', label: 'OGG', description: 'OGG音频' },
      { value: 'm4a', label: 'M4A', description: 'M4A音频' },
    ]
  },
  video: {
    label: '视频文件',
    icon: FileVideo,
    formats: [
      { value: 'mp4', label: 'MP4', description: 'MP4视频' },
      { value: 'avi', label: 'AVI', description: 'AVI视频' },
      { value: 'mkv', label: 'MKV', description: 'MKV视频' },
      { value: 'mov', label: 'MOV', description: 'MOV视频' },
      { value: 'wmv', label: 'WMV', description: 'WMV视频' },
      { value: 'flv', label: 'FLV', description: 'FLV视频' },
      { value: 'webm', label: 'WebM', description: 'WebM视频' },
    ]
  },
  archive: {
    label: '压缩文件',
    icon: Archive,
    formats: [
      { value: 'zip', label: 'ZIP', description: 'ZIP压缩包' },
      { value: 'rar', label: 'RAR', description: 'RAR压缩包' },
      { value: '7z', label: '7Z', description: '7Z压缩包' },
      { value: 'tar', label: 'TAR', description: 'TAR归档' },
      { value: 'gz', label: 'GZ', description: 'GZ压缩' },
      { value: 'bz2', label: 'BZ2', description: 'BZ2压缩' },
    ]
  }
}

// 转换支持矩阵
const CONVERSION_MATRIX: { [key: string]: string[] } = {
  // 文本格式转换
  'txt': ['md', 'html', 'rtf', 'csv', 'json', 'xml', 'yaml'],
  'md': ['txt', 'html', 'rtf', 'pdf'],
  'html': ['txt', 'md', 'rtf', 'pdf'],
  'csv': ['txt', 'json', 'xml', 'xlsx'],
  'json': ['txt', 'csv', 'xml', 'yaml'],
  'xml': ['txt', 'json', 'csv', 'yaml'],
  'yaml': ['txt', 'json', 'xml'],
  
  // 图像格式转换
  'jpg': ['png', 'gif', 'bmp', 'webp', 'ico', 'tiff'],
  'png': ['jpg', 'gif', 'bmp', 'webp', 'ico', 'tiff'],
  'gif': ['jpg', 'png', 'bmp', 'webp', 'ico'],
  'bmp': ['jpg', 'png', 'gif', 'webp', 'ico', 'tiff'],
  'webp': ['jpg', 'png', 'gif', 'bmp', 'ico'],
  'ico': ['jpg', 'png', 'gif', 'bmp', 'webp'],
  'tiff': ['jpg', 'png', 'bmp', 'webp'],
  
  // 音频格式转换
  'mp3': ['wav', 'flac', 'aac', 'ogg', 'm4a'],
  'wav': ['mp3', 'flac', 'aac', 'ogg', 'm4a'],
  'flac': ['mp3', 'wav', 'aac', 'ogg', 'm4a'],
  'aac': ['mp3', 'wav', 'flac', 'ogg', 'm4a'],
  'ogg': ['mp3', 'wav', 'flac', 'aac', 'm4a'],
  'm4a': ['mp3', 'wav', 'flac', 'aac', 'ogg'],
  
  // 视频格式转换
  'mp4': ['avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'],
  'avi': ['mp4', 'mkv', 'mov', 'wmv', 'flv'],
  'mkv': ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
  'mov': ['mp4', 'avi', 'mkv', 'wmv', 'flv'],
  'wmv': ['mp4', 'avi', 'mkv', 'mov', 'flv'],
  'flv': ['mp4', 'avi', 'mkv', 'mov', 'wmv'],
  'webm': ['mp4', 'mkv'],
  
  // 压缩格式转换
  'zip': ['rar', '7z', 'tar', 'gz'],
  'rar': ['zip', '7z', 'tar'],
  '7z': ['zip', 'rar', 'tar'],
  'tar': ['zip', 'rar', '7z', 'gz', 'bz2'],
  'gz': ['zip', 'tar', 'bz2'],
  'bz2': ['zip', 'tar', 'gz'],
}

interface ConversionTask {
  id: string
  fileName: string
  fromFormat: string
  toFormat: string
  status: 'pending' | 'converting' | 'completed' | 'error'
  progress: number
  errorMessage?: string
  downloadUrl?: string
  fileSize?: number
}

export default function FileFormatConverterTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [fromFormat, setFromFormat] = useState<string>('')
  const [toFormat, setToFormat] = useState<string>('')
  const [activeCategory, setActiveCategory] = useState<string>('text')
  const [conversionTasks, setConversionTasks] = useState<ConversionTask[]>([])
  const [dragOver, setDragOver] = useState(false)

  // 文件拖放处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  // 文件选择处理
  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file)
    const extension = file.name.split('.').pop()?.toLowerCase()
    if (extension) {
      setFromFormat(extension)
      // 自动选择对应的分类
      for (const [category, config] of Object.entries(FILE_CATEGORIES)) {
        if (config.formats.some(format => format.value === extension)) {
          setActiveCategory(category)
          break
        }
      }
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  // 获取可转换的格式
  const getAvailableFormats = useCallback((sourceFormat: string) => {
    return CONVERSION_MATRIX[sourceFormat] || []
  }, [])

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }, [])

  // 模拟转换处理
  const handleConvert = useCallback(() => {
    if (!selectedFile || !fromFormat || !toFormat) return

    const taskId = Date.now().toString()
    const newTask: ConversionTask = {
      id: taskId,
      fileName: selectedFile.name,
      fromFormat,
      toFormat,
      status: 'pending',
      progress: 0,
      fileSize: selectedFile.size,
    }

    setConversionTasks(prev => [...prev, newTask])

    // 模拟转换过程
    setTimeout(() => {
      setConversionTasks(prev => prev.map(task => 
        task.id === taskId ? { ...task, status: 'converting' } : task
      ))
      
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setConversionTasks(prev => prev.map(task => {
          if (task.id === taskId && task.status === 'converting') {
            const newProgress = Math.min(task.progress + Math.random() * 20, 100)
            if (newProgress >= 100) {
              clearInterval(progressInterval)
              return {
                ...task,
                status: 'completed',
                progress: 100,
                downloadUrl: URL.createObjectURL(selectedFile), // 模拟下载链接
              }
            }
            return { ...task, progress: newProgress }
          }
          return task
        }))
      }, 500)
    }, 1000)
  }, [selectedFile, fromFormat, toFormat])

  // 删除任务
  const handleDeleteTask = useCallback((taskId: string) => {
    setConversionTasks(prev => prev.filter(task => task.id !== taskId))
  }, [])

  // 下载文件
  const handleDownload = useCallback((task: ConversionTask) => {
    if (task.downloadUrl) {
      const link = document.createElement('a')
      link.href = task.downloadUrl
      link.download = task.fileName.replace(/\.[^/.]+$/, `.${task.toFormat}`)
      link.click()
    }
  }, [])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileType className="h-5 w-5" />
            文件格式转换器
          </CardTitle>
          <CardDescription>
            支持多种文件格式之间的转换，包括文本、图像、音频、视频等格式
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 文件上传区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragOver ? 'border-primary bg-primary/10' : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-lg font-medium">
                拖放文件到此处或点击选择文件
              </p>
              <p className="text-sm text-muted-foreground">
                支持多种文件格式，单个文件最大100MB
              </p>
            </div>
            <Input
              type="file"
              className="hidden"
              id="file-input"
              onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
            />
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => document.getElementById('file-input')?.click()}
            >
              选择文件
            </Button>
          </div>

          {/* 选中的文件信息 */}
          {selectedFile && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <File className="h-8 w-8 text-blue-500" />
                    <div>
                      <h3 className="font-medium">{selectedFile.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {formatFileSize(selectedFile.size)}
                      </p>
                    </div>
                  </div>
                  <Badge variant="outline">{fromFormat.toUpperCase()}</Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 格式选择 */}
          <Tabs value={activeCategory} onValueChange={setActiveCategory}>
            <TabsList className="grid w-full grid-cols-3 lg:grid-cols-7">
              {Object.entries(FILE_CATEGORIES).map(([key, category]) => {
                const Icon = category.icon
                return (
                  <TabsTrigger key={key} value={key} className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{category.label}</span>
                  </TabsTrigger>
                )
              })}
            </TabsList>

            {Object.entries(FILE_CATEGORIES).map(([key, category]) => (
              <TabsContent key={key} value={key} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 源格式 */}
                  <div>
                    <Label htmlFor="from-format">源格式</Label>
                    <Select value={fromFormat} onValueChange={setFromFormat}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择源格式" />
                      </SelectTrigger>
                      <SelectContent>
                        {category.formats.map((format) => (
                          <SelectItem key={format.value} value={format.value}>
                            <div className="flex items-center justify-between w-full">
                              <span>{format.label}</span>
                              <span className="text-xs text-muted-foreground ml-2">
                                {format.description}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 目标格式 */}
                  <div>
                    <Label htmlFor="to-format">目标格式</Label>
                    <Select value={toFormat} onValueChange={setToFormat}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择目标格式" />
                      </SelectTrigger>
                      <SelectContent>
                      {fromFormat && getAvailableFormats(fromFormat).map((format: string) => {
                        const formatInfo = category.formats.find(f => f.value === format)
                        return (
                          <SelectItem key={format} value={format}>
                            <div className="flex items-center justify-between w-full">
                              <span>{formatInfo?.label || format.toUpperCase()}</span>
                              <span className="text-xs text-muted-foreground ml-2">
                                {formatInfo?.description || ''}
                              </span>
                            </div>
                          </SelectItem>
                        )
                      })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* 转换按钮 */}
                <Button
                  onClick={handleConvert}
                  disabled={!selectedFile || !fromFormat || !toFormat}
                  className="w-full"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  开始转换
                </Button>
              </TabsContent>
            ))}
          </Tabs>

          {/* 转换任务列表 */}
          {conversionTasks.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">转换任务</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {conversionTasks.map((task) => (
                    <div key={task.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {task.status === 'completed' ? (
                            <CheckCircle2 className="h-5 w-5 text-green-500" />
                          ) : task.status === 'error' ? (
                            <AlertCircle className="h-5 w-5 text-red-500" />
                          ) : (
                            <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
                          )}
                          <span className="font-medium">{task.fileName}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{task.fromFormat.toUpperCase()}</Badge>
                          <span>→</span>
                          <Badge variant="outline">{task.toFormat.toUpperCase()}</Badge>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {task.status === 'converting' && (
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${task.progress}%` }}
                            />
                          </div>
                        )}
                        {task.status === 'completed' && (
                          <Button
                            size="sm"
                            onClick={() => handleDownload(task)}
                          >
                            <Download className="w-4 h-4 mr-1" />
                            下载
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDeleteTask(task.id)}
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 支持的格式说明 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(FILE_CATEGORIES).map(([key, category]) => {
              const Icon = category.icon
              return (
                <Card key={key}>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Icon className="h-5 w-5" />
                      <h3 className="font-medium">{category.label}</h3>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {category.formats.map((format) => (
                        <Badge key={format.value} variant="secondary" className="text-xs">
                          {format.label}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* 使用说明 */}
          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">使用说明</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• 选择或拖放要转换的文件</li>
              <li>• 选择源格式和目标格式</li>
              <li>• 点击开始转换，等待处理完成</li>
              <li>• 转换完成后可下载转换后的文件</li>
              <li>• 支持批量转换和多种文件格式</li>
              <li>• 所有转换都在本地进行，保护您的隐私</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
