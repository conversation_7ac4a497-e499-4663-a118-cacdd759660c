'use client';

import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, Download, RefreshCw, Image as ImageIcon, Trash2, Info, Edit, Save, X } from 'lucide-react';

interface ExifData {
  [key: string]: any;
}

interface ImageFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  url: string;
  originalExif: ExifData;
  editedExif: ExifData;
  hasChanges: boolean;
}

export default function ImageMetadataEditorTool() {
  const [images, setImages] = useState<ImageFile[]>([]);
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * 处理文件上传
   */
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    if (imageFiles.length === 0) {
      alert('请选择图片文件');
      return;
    }

    setProcessing(true);

    for (const file of imageFiles) {
      try {
        const url = URL.createObjectURL(file);
        const exifData = await extractExifData(file);
        
        const imageFile: ImageFile = {
          id: Math.random().toString(36).substr(2, 9),
          file,
          name: file.name,
          size: file.size,
          type: file.type,
          url,
          originalExif: exifData,
          editedExif: { ...exifData },
          hasChanges: false
        };

        setImages(prev => [...prev, imageFile]);
      } catch (error) {
        console.error('处理文件失败:', error);
      }
    }

    setProcessing(false);
  };

  /**
   * 提取EXIF数据（模拟实现）
   */
  const extractExifData = async (file: File): Promise<ExifData> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        // 模拟EXIF数据
        const mockExifData: ExifData = {
          'File Name': file.name,
          'File Size': formatFileSize(file.size),
          'File Type': file.type,
          'Image Width': img.naturalWidth,
          'Image Height': img.naturalHeight,
          'Color Space': 'sRGB',
          'Bits Per Sample': '8 8 8',
          'Compression': 'JPEG',
          'Orientation': 'Horizontal (normal)',
          'X Resolution': '72 dpi',
          'Y Resolution': '72 dpi',
          'Resolution Unit': 'inches',
          'Software': 'Unknown',
          'Date/Time': new Date().toISOString().replace('T', ' ').split('.')[0],
          'Artist': '',
          'Copyright': '',
          'Image Description': '',
          'Make': '',
          'Model': '',
          'ISO Speed': '',
          'Exposure Time': '',
          'F Number': '',
          'Focal Length': '',
          'Flash': '',
          'White Balance': '',
          'GPS Latitude': '',
          'GPS Longitude': '',
          'GPS Altitude': '',
          'Keywords': '',
          'Subject': '',
          'Title': '',
          'Comment': ''
        };
        
        URL.revokeObjectURL(img.src);
        resolve(mockExifData);
      };
      
      img.onerror = () => {
        resolve({});
      };
      
      img.src = URL.createObjectURL(file);
    });
  };

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * 获取选中的图片
   */
  const selectedImage = images.find(img => img.id === selectedImageId);

  /**
   * 开始编辑字段
   */
  const startEditing = (field: string, value: any) => {
    setEditingField(field);
    setEditValue(String(value || ''));
  };

  /**
   * 保存编辑
   */
  const saveEdit = () => {
    if (!selectedImageId || !editingField) return;

    setImages(prev => prev.map(img => {
      if (img.id === selectedImageId) {
        const newEditedExif = { ...img.editedExif };
        newEditedExif[editingField] = editValue;
        
        return {
          ...img,
          editedExif: newEditedExif,
          hasChanges: JSON.stringify(newEditedExif) !== JSON.stringify(img.originalExif)
        };
      }
      return img;
    }));

    setEditingField(null);
    setEditValue('');
  };

  /**
   * 取消编辑
   */
  const cancelEdit = () => {
    setEditingField(null);
    setEditValue('');
  };

  /**
   * 删除字段
   */
  const deleteField = (field: string) => {
    if (!selectedImageId) return;

    setImages(prev => prev.map(img => {
      if (img.id === selectedImageId) {
        const newEditedExif = { ...img.editedExif };
        delete newEditedExif[field];
        
        return {
          ...img,
          editedExif: newEditedExif,
          hasChanges: JSON.stringify(newEditedExif) !== JSON.stringify(img.originalExif)
        };
      }
      return img;
    }));
  };

  /**
   * 添加新字段
   */
  const addNewField = () => {
    const fieldName = prompt('请输入字段名称:');
    if (!fieldName || !selectedImageId) return;

    const fieldValue = prompt('请输入字段值:');
    if (fieldValue === null) return;

    setImages(prev => prev.map(img => {
      if (img.id === selectedImageId) {
        const newEditedExif = { ...img.editedExif };
        newEditedExif[fieldName] = fieldValue;
        
        return {
          ...img,
          editedExif: newEditedExif,
          hasChanges: JSON.stringify(newEditedExif) !== JSON.stringify(img.originalExif)
        };
      }
      return img;
    }));
  };

  /**
   * 重置更改
   */
  const resetChanges = () => {
    if (!selectedImageId) return;

    setImages(prev => prev.map(img => {
      if (img.id === selectedImageId) {
        return {
          ...img,
          editedExif: { ...img.originalExif },
          hasChanges: false
        };
      }
      return img;
    }));
  };

  /**
   * 导出元数据
   */
  const exportMetadata = (format: 'json' | 'csv' | 'txt') => {
    if (!selectedImage) return;

    let content = '';
    let filename = '';
    let mimeType = '';

    switch (format) {
      case 'json':
        content = JSON.stringify(selectedImage.editedExif, null, 2);
        filename = `${selectedImage.name}_metadata.json`;
        mimeType = 'application/json';
        break;
      
      case 'csv':
        const csvRows = Object.entries(selectedImage.editedExif).map(([key, value]) => {
          const escapedKey = key.replace(/"/g, '""');
          const escapedValue = String(value).replace(/"/g, '""');
          return `"${escapedKey}","${escapedValue}"`;
        });
        content = 'Field,Value\n' + csvRows.join('\n');
        filename = `${selectedImage.name}_metadata.csv`;
        mimeType = 'text/csv';
        break;
      
      case 'txt':
        content = Object.entries(selectedImage.editedExif)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n');
        filename = `${selectedImage.name}_metadata.txt`;
        mimeType = 'text/plain';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
  };

  /**
   * 删除图片
   */
  const removeImage = (imageId: string) => {
    setImages(prev => {
      const image = prev.find(img => img.id === imageId);
      if (image) {
        URL.revokeObjectURL(image.url);
      }
      return prev.filter(img => img.id !== imageId);
    });
    
    if (selectedImageId === imageId) {
      setSelectedImageId(null);
    }
  };

  /**
   * 清空所有图片
   */
  const clearAll = () => {
    images.forEach(image => {
      URL.revokeObjectURL(image.url);
    });
    setImages([]);
    setSelectedImageId(null);
  };

  /**
   * 获取字段分类
   */
  const getFieldCategories = (exifData: ExifData) => {
    const categories = {
      basic: ['File Name', 'File Size', 'File Type', 'Image Width', 'Image Height'],
      camera: ['Make', 'Model', 'ISO Speed', 'Exposure Time', 'F Number', 'Focal Length', 'Flash', 'White Balance'],
      location: ['GPS Latitude', 'GPS Longitude', 'GPS Altitude'],
      description: ['Title', 'Subject', 'Image Description', 'Keywords', 'Artist', 'Copyright', 'Comment'],
      technical: ['Color Space', 'Bits Per Sample', 'Compression', 'Orientation', 'X Resolution', 'Y Resolution', 'Resolution Unit', 'Software', 'Date/Time']
    };

    const result: { [key: string]: Array<[string, any]> } = {};
    const allFields = Object.entries(exifData);
    
    // 分类已知字段
    Object.entries(categories).forEach(([category, fields]) => {
      result[category] = allFields.filter(([key]) => fields.includes(key));
    });

    // 其他字段
    const knownFields = Object.values(categories).flat();
    result.other = allFields.filter(([key]) => !knownFields.includes(key));

    return result;
  };

  const categoryNames = {
    basic: '基本信息',
    camera: '相机参数',
    location: '位置信息',
    description: '描述信息',
    technical: '技术参数',
    other: '其他信息'
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      {/* 工具说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            图片元数据编辑器
          </CardTitle>
          <CardDescription>
            查看、编辑和管理图片的EXIF元数据信息，包括相机参数、位置信息、描述信息等。
            支持批量处理和多种格式导出。
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 文件上传 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">上传图片</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button
              onClick={() => fileInputRef.current?.click()}
              disabled={processing}
              className="flex-1"
            >
              {processing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  选择图片文件
                </>
              )}
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleFileUpload}
              className="hidden"
            />
            
            {images.length > 0 && (
              <Button variant="outline" onClick={clearAll}>
                <Trash2 className="h-4 w-4 mr-2" />
                清空全部
              </Button>
            )}
          </div>
          
          <p className="text-sm text-muted-foreground">
            支持JPEG、PNG、WebP、GIF、BMP等格式。支持多选批量处理。
          </p>
        </CardContent>
      </Card>

      {/* 图片列表和编辑器 */}
      {images.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* 图片列表 */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="text-lg">图片列表 ({images.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {images.map((image) => (
                  <div
                    key={image.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedImageId === image.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedImageId(image.id)}
                  >
                    <div className="flex items-center gap-3">
                      <img
                        src={image.url}
                        alt={image.name}
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{image.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(image.size)}
                        </p>
                        {image.hasChanges && (
                          <Badge variant="secondary" className="text-xs">
                            已修改
                          </Badge>
                        )}
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeImage(image.id);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 元数据编辑器 */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">
                  {selectedImage ? `元数据编辑 - ${selectedImage.name}` : '请选择图片'}
                </CardTitle>
                {selectedImage && (
                  <div className="flex gap-2">
                    {selectedImage.hasChanges && (
                      <Button size="sm" variant="outline" onClick={resetChanges}>
                        <RefreshCw className="h-4 w-4 mr-1" />
                        重置
                      </Button>
                    )}
                    <Button size="sm" variant="outline" onClick={addNewField}>
                      添加字段
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {selectedImage ? (
                <Tabs defaultValue="edit" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="edit">编辑</TabsTrigger>
                    <TabsTrigger value="preview">预览</TabsTrigger>
                    <TabsTrigger value="export">导出</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="edit" className="space-y-4">
                    <div className="max-h-96 overflow-y-auto space-y-4">
                      {Object.entries(getFieldCategories(selectedImage.editedExif)).map(([category, fields]) => {
                        if (fields.length === 0) return null;
                        
                        return (
                          <div key={category} className="space-y-2">
                            <h4 className="font-medium text-sm text-muted-foreground">
                              {categoryNames[category as keyof typeof categoryNames]}
                            </h4>
                            <div className="space-y-2">
                              {fields.map(([key, value]) => (
                                <div key={key} className="flex items-center gap-2 p-2 border rounded">
                                  <div className="flex-1 min-w-0">
                                    <Label className="text-xs font-medium">{key}</Label>
                                    {editingField === key ? (
                                      <div className="flex gap-2 mt-1">
                                        {key === 'Image Description' || key === 'Comment' || key === 'Keywords' ? (
                                          <Textarea
                                            value={editValue}
                                            onChange={(e) => setEditValue(e.target.value)}
                                            className="text-sm"
                                            rows={2}
                                          />
                                        ) : (
                                          <Input
                                            value={editValue}
                                            onChange={(e) => setEditValue(e.target.value)}
                                            className="text-sm"
                                          />
                                        )}
                                        <Button size="sm" onClick={saveEdit}>
                                          <Save className="h-3 w-3" />
                                        </Button>
                                        <Button size="sm" variant="outline" onClick={cancelEdit}>
                                          <X className="h-3 w-3" />
                                        </Button>
                                      </div>
                                    ) : (
                                      <p className="text-sm text-muted-foreground truncate">
                                        {String(value) || '(空)'}
                                      </p>
                                    )}
                                  </div>
                                  {editingField !== key && (
                                    <div className="flex gap-1">
                                      <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => startEditing(key, value)}
                                      >
                                        <Edit className="h-3 w-3" />
                                      </Button>
                                      {!['File Name', 'File Size', 'File Type', 'Image Width', 'Image Height'].includes(key) && (
                                        <Button
                                          size="sm"
                                          variant="ghost"
                                          onClick={() => deleteField(key)}
                                        >
                                          <Trash2 className="h-3 w-3" />
                                        </Button>
                                      )}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="preview" className="space-y-4">
                    <div className="flex items-center gap-4">
                      <img
                        src={selectedImage.url}
                        alt={selectedImage.name}
                        className="w-32 h-32 object-cover rounded-lg"
                      />
                      <div className="space-y-2">
                        <h3 className="font-medium">{selectedImage.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {selectedImage.editedExif['Image Width']} × {selectedImage.editedExif['Image Height']}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(selectedImage.size)}
                        </p>
                        {selectedImage.hasChanges && (
                          <Badge variant="secondary">已修改</Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="max-h-64 overflow-y-auto">
                      <pre className="text-xs bg-gray-50 p-3 rounded">
                        {JSON.stringify(selectedImage.editedExif, null, 2)}
                      </pre>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="export" className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">导出格式</h4>
                        <div className="flex gap-2">
                          <Button onClick={() => exportMetadata('json')}>
                            <Download className="h-4 w-4 mr-2" />
                            JSON
                          </Button>
                          <Button onClick={() => exportMetadata('csv')}>
                            <Download className="h-4 w-4 mr-2" />
                            CSV
                          </Button>
                          <Button onClick={() => exportMetadata('txt')}>
                            <Download className="h-4 w-4 mr-2" />
                            TXT
                          </Button>
                        </div>
                      </div>
                      
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                          注意：此工具仅用于查看和编辑元数据信息，不会修改原始图片文件。
                          如需将修改后的元数据写入图片文件，请使用专业的图片处理软件。
                        </AlertDescription>
                      </Alert>
                    </div>
                  </TabsContent>
                </Tabs>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  请从左侧列表中选择一张图片来查看和编辑其元数据
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">使用说明</CardTitle>
        </CardHeader>
        <CardContent className="prose prose-sm max-w-none">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">功能特点：</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li><strong>查看元数据</strong>：显示图片的完整EXIF信息，包括相机参数、位置信息等</li>
                <li><strong>编辑字段</strong>：可以修改描述、关键词、版权等可编辑字段</li>
                <li><strong>分类显示</strong>：按基本信息、相机参数、位置信息等分类组织</li>
                <li><strong>批量处理</strong>：支持同时处理多张图片</li>
                <li><strong>多格式导出</strong>：支持JSON、CSV、TXT格式导出</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">使用技巧：</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>点击编辑按钮可以修改字段值，支持多行文本编辑</li>
                <li>可以添加自定义字段来存储额外信息</li>
                <li>修改后的字段会显示&ldquo;已修改&rdquo;标记</li>
                <li>使用重置按钮可以恢复到原始状态</li>
                <li>导出功能可以保存元数据信息用于备份或分析</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">注意事项：</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>此工具仅用于查看和编辑元数据，不会修改原始图片文件</li>
                <li>某些字段（如文件大小、图片尺寸）为只读，无法修改</li>
                <li>GPS位置信息需要原图片包含位置数据才能显示</li>
                <li>不同相机品牌的EXIF字段可能有所差异</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
