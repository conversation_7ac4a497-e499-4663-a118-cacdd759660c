'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Co<PERSON>, Check, Palette, <PERSON>pet<PERSON>, RefreshCw, Grid } from 'lucide-react'

// 声明 EyeDropper API 类型
interface EyeDropperResult {
  sRGBHex: string
}

interface EyeDropper {
  open(): Promise<EyeDropperResult>
}

declare global {
  interface Window {
    EyeDropper?: {
      new(): EyeDropper
    }
  }
}

interface ColorFormat {
  hex: string
  rgb: string
  hsl: string
  hsv: string
}

interface ColorHistory {
  color: string
  timestamp: number
}

interface ColorPalette {
  name: string
  colors: string[]
}

export default function ColorPickerTool() {
  const [color, setColor] = useState('#3B82F6')
  const [formats, setFormats] = useState<ColorFormat>({
    hex: '#3B82F6',
    rgb: 'rgb(59, 130, 246)',
    hsl: 'hsl(217, 91%, 60%)',
    hsv: 'hsv(217, 76%, 96%)',
  })
  const [copied, setCopied] = useState<string>('')
  const [history, setHistory] = useState<ColorHistory[]>([])
  const [showPalettes, setShowPalettes] = useState(true)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isPickingFromScreen, setIsPickingFromScreen] = useState(false)

  // 预设调色板
  const palettes: ColorPalette[] = [
    {
      name: '基础颜色',
      colors: ['#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'],
    },
    {
      name: '材料设计',
      colors: ['#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5', '#2196F3', '#03A9F4', '#00BCD4'],
    },
    {
      name: 'Tailwind CSS',
      colors: ['#EF4444', '#F59E0B', '#10B981', '#3B82F6', '#6366F1', '#8B5CF6', '#EC4899', '#14B8A6'],
    },
    {
      name: '柔和色调',
      colors: ['#FFB6C1', '#FFA07A', '#FFD700', '#98FB98', '#87CEEB', '#DDA0DD', '#F0E68C', '#B0C4DE'],
    },
    {
      name: '深色调',
      colors: ['#8B0000', '#006400', '#00008B', '#4B0082', '#2F4F4F', '#800000', '#483D8B', '#556B2F'],
    },
  ]

  // HEX 转 RGB
  const hexToRgb = (hex: string): { r: number; g: number; b: number } => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : { r: 0, g: 0, b: 0 }
  }

  // RGB 转 HEX
  const rgbToHex = (r: number, g: number, b: number): string => {
    return '#' + [r, g, b].map(x => x.toString(16).padStart(2, '0')).join('').toUpperCase()
  }

  // RGB 转 HSL
  const rgbToHsl = (r: number, g: number, b: number): { h: number; s: number; l: number } => {
    r /= 255
    g /= 255
    b /= 255

    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h = 0
    let s = 0
    const l = (max + min) / 2

    if (max !== min) {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)

      switch (max) {
        case r:
          h = ((g - b) / d + (g < b ? 6 : 0)) / 6
          break
        case g:
          h = ((b - r) / d + 2) / 6
          break
        case b:
          h = ((r - g) / d + 4) / 6
          break
      }
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100),
    }
  }

  // RGB 转 HSV
  const rgbToHsv = (r: number, g: number, b: number): { h: number; s: number; v: number } => {
    r /= 255
    g /= 255
    b /= 255

    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    const d = max - min
    let h = 0
    const s = max === 0 ? 0 : d / max
    const v = max

    if (max !== min) {
      switch (max) {
        case r:
          h = ((g - b) / d + (g < b ? 6 : 0)) / 6
          break
        case g:
          h = ((b - r) / d + 2) / 6
          break
        case b:
          h = ((r - g) / d + 4) / 6
          break
      }
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      v: Math.round(v * 100),
    }
  }

  // 更新所有格式
  const updateFormats = useCallback((hexColor: string) => {
    const rgb = hexToRgb(hexColor)
    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
    const hsv = rgbToHsv(rgb.r, rgb.g, rgb.b)

    setFormats({
      hex: hexColor.toUpperCase(),
      rgb: `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`,
      hsl: `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`,
      hsv: `hsv(${hsv.h}, ${hsv.s}%, ${hsv.v}%)`,
    })
  }, [])

  // 处理颜色变化
  const handleColorChange = (newColor: string) => {
    setColor(newColor)
    updateFormats(newColor)
    
    // 添加到历史记录
    const newHistory: ColorHistory = {
      color: newColor,
      timestamp: Date.now(),
    }
    setHistory([newHistory, ...history.filter(h => h.color !== newColor).slice(0, 19)])
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string, format: string) => {
    if (typeof window !== 'undefined' && navigator.clipboard) {
      navigator.clipboard.writeText(text).then(() => {
        setCopied(format)
        setTimeout(() => setCopied(''), 2000)
      }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        setCopied(format)
        setTimeout(() => setCopied(''), 2000)
      })
    }
  }

  // 从 RGB 输入解析颜色
  const parseRgbInput = (input: string) => {
    const match = input.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
    if (match) {
      const r = parseInt(match[1])
      const g = parseInt(match[2])
      const b = parseInt(match[3])
      if (r <= 255 && g <= 255 && b <= 255) {
        const hex = rgbToHex(r, g, b)
        handleColorChange(hex)
      }
    }
  }

  // 生成随机颜色
  const generateRandomColor = () => {
    const randomHex = '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')
    handleColorChange(randomHex)
  }

  // 初始化
  useEffect(() => {
    updateFormats(color)
  }, [color, updateFormats])

  // 绘制颜色轮
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const centerX = canvas.width / 2
    const centerY = canvas.height / 2
    const radius = Math.min(centerX, centerY) - 10

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 绘制色相环
    for (let angle = 0; angle < 360; angle++) {
      const startAngle = (angle - 1) * Math.PI / 180
      const endAngle = angle * Math.PI / 180

      ctx.beginPath()
      ctx.moveTo(centerX, centerY)
      ctx.arc(centerX, centerY, radius, startAngle, endAngle)
      ctx.closePath()

      const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius)
      gradient.addColorStop(0, 'white')
      gradient.addColorStop(0.7, `hsl(${angle}, 100%, 50%)`)
      gradient.addColorStop(1, `hsl(${angle}, 100%, 50%)`)

      ctx.fillStyle = gradient
      ctx.fill()
    }

    // 中心圆
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius * 0.3, 0, 2 * Math.PI)
    ctx.fillStyle = color
    ctx.fill()
    ctx.strokeStyle = '#000'
    ctx.lineWidth = 2
    ctx.stroke()
  }, [color])

  // 处理画布点击
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const imageData = ctx.getImageData(x, y, 1, 1)
    const pixel = imageData.data

    const hex = rgbToHex(pixel[0], pixel[1], pixel[2])
    handleColorChange(hex)
  }

  // 屏幕取色（仅在支持的浏览器中）
  const pickFromScreen = async () => {
    if (typeof window !== 'undefined' && 'EyeDropper' in window) {
      try {
        setIsPickingFromScreen(true)
        const eyeDropper = new window.EyeDropper!()
        const result = await eyeDropper.open()
        handleColorChange(result.sRGBHex)
      } catch (err) {
        console.error('取色失败:', err)
      } finally {
        setIsPickingFromScreen(false)
      }
    } else {
      if (typeof window !== 'undefined') {
        alert('您的浏览器不支持屏幕取色功能')
      }
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">颜色选择器</h2>
        <p className="text-gray-600">
          选择颜色并获取多种格式的颜色代码
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧控制面板 */}
        <div className="space-y-4">
          {/* 颜色输入 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3">颜色选择</h3>
            
            <div className="space-y-3">
              {/* 颜色选择器 */}
              <div className="flex items-center gap-3">
                <input
                  type="color"
                  value={color}
                  onChange={(e) => handleColorChange(e.target.value)}
                  className="w-20 h-20 rounded cursor-pointer"
                />
                <div className="flex-1">
                  <div
                    className="w-full h-20 rounded-md border-2 border-gray-300"
                    style={{ backgroundColor: color }}
                  />
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-2">
                <button
                  onClick={generateRandomColor}
                  className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  随机颜色
                </button>
                {typeof window !== 'undefined' && 'EyeDropper' in window && (
                  <button
                    onClick={pickFromScreen}
                    disabled={isPickingFromScreen}
                    className="flex-1 px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center justify-center disabled:bg-gray-400"
                  >
                    <Pipette className="w-4 h-4 mr-2" />
                    屏幕取色
                  </button>
                )}
              </div>

              {/* HEX 输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  HEX 值
                </label>
                <input
                  type="text"
                  value={formats.hex}
                  onChange={(e) => {
                    const value = e.target.value
                    if (/^#[0-9A-F]{6}$/i.test(value)) {
                      handleColorChange(value)
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md font-mono"
                  placeholder="#000000"
                />
              </div>
            </div>
          </div>

          {/* 颜色格式 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3">颜色格式</h3>
            
            <div className="space-y-2">
              {Object.entries(formats).map(([format, value]) => (
                <div key={format} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div>
                    <div className="text-xs text-gray-500 uppercase">{format}</div>
                    <div className="font-mono text-sm">{value}</div>
                  </div>
                  <button
                    onClick={() => copyToClipboard(value, format)}
                    className="p-2 hover:bg-gray-200 rounded"
                  >
                    {copied === format ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <Copy className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* 历史记录 */}
          {history.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h3 className="font-medium mb-3">最近使用</h3>
              <div className="grid grid-cols-8 gap-2">
                {history.slice(0, 16).map((item, index) => (
                  <button
                    key={index}
                    onClick={() => handleColorChange(item.color)}
                    className="w-10 h-10 rounded border-2 border-gray-300 hover:border-gray-400"
                    style={{ backgroundColor: item.color }}
                    title={item.color}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 中间颜色轮 */}
        <div className="flex flex-col items-center">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3 text-center">颜色轮</h3>
            <canvas
              ref={canvasRef}
              width={300}
              height={300}
              onClick={handleCanvasClick}
              className="cursor-crosshair"
            />
          </div>
        </div>

        {/* 右侧调色板 */}
        <div className="space-y-4">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <button
              onClick={() => setShowPalettes(!showPalettes)}
              className="flex items-center justify-between w-full mb-3"
            >
              <h3 className="font-medium flex items-center">
                <Palette className="w-5 h-5 mr-2" />
                预设调色板
              </h3>
              <span className="text-gray-400 text-sm">{showPalettes ? '收起' : '展开'}</span>
            </button>

            {showPalettes && (
              <div className="space-y-4">
                {palettes.map((palette) => (
                  <div key={palette.name}>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">{palette.name}</h4>
                    <div className="grid grid-cols-4 gap-2">
                      {palette.colors.map((paletteColor) => (
                        <button
                          key={paletteColor}
                          onClick={() => handleColorChange(paletteColor)}
                          className="w-full h-10 rounded border-2 border-gray-300 hover:border-gray-400 transition-colors"
                          style={{ backgroundColor: paletteColor }}
                          title={paletteColor}
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 颜色理论 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-medium mb-3 flex items-center">
              <Grid className="w-5 h-5 mr-2" />
              配色方案
            </h3>
            
            <div className="space-y-3">
              {/* 互补色 */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">互补色</h4>
                <div className="flex gap-2">
                  <div
                    className="flex-1 h-10 rounded border-2 border-gray-300"
                    style={{ backgroundColor: color }}
                  />
                  <div
                    className="flex-1 h-10 rounded border-2 border-gray-300"
                    style={{
                      backgroundColor: (() => {
                        const rgb = hexToRgb(color)
                        const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
                        const complementHue = (hsl.h + 180) % 360
                        return `hsl(${complementHue}, ${hsl.s}%, ${hsl.l}%)`
                      })(),
                    }}
                  />
                </div>
              </div>

              {/* 类似色 */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">类似色</h4>
                <div className="flex gap-2">
                  {[-30, 0, 30].map((offset) => {
                    const rgb = hexToRgb(color)
                    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
                    const hue = (hsl.h + offset + 360) % 360
                    return (
                      <div
                        key={offset}
                        className="flex-1 h-10 rounded border-2 border-gray-300"
                        style={{
                          backgroundColor: `hsl(${hue}, ${hsl.s}%, ${hsl.l}%)`,
                        }}
                      />
                    )
                  })}
                </div>
              </div>

              {/* 三角配色 */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">三角配色</h4>
                <div className="flex gap-2">
                  {[0, 120, 240].map((offset) => {
                    const rgb = hexToRgb(color)
                    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
                    const hue = (hsl.h + offset) % 360
                    return (
                      <div
                        key={offset}
                        className="flex-1 h-10 rounded border-2 border-gray-300"
                        style={{
                          backgroundColor: `hsl(${hue}, ${hsl.s}%, ${hsl.l}%)`,
                        }}
                      />
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Palette className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持多种颜色格式：HEX、RGB、HSL、HSV</li>
              <li>点击颜色轮可以快速选择颜色</li>
              <li>支持屏幕取色功能（需要浏览器支持）</li>
              <li>提供多种预设调色板和配色方案</li>
              <li>自动保存最近使用的颜色历史</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
