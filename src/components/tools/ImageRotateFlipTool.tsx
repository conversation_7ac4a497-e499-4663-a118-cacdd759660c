'use client'

import React, { useState, useRef, useEffect } from 'react'
import NextImage from 'next/image'
import { Upload, Download, RotateCw, RotateCcw, FlipHorizontal, FlipVertical, RefreshCw, Image as ImageIcon } from 'lucide-react'

interface ImageTransform {
  rotation: number
  flipHorizontal: boolean
  flipVertical: boolean
}

export default function ImageRotateFlipTool() {
  const [originalImage, setOriginalImage] = useState<string | null>(null)
  const [processedImage, setProcessedImage] = useState<string | null>(null)
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 })
  const [transform, setTransform] = useState<ImageTransform>({
    rotation: 0,
    flipHorizontal: false,
    flipVertical: false
  })
  const [isProcessing, setIsProcessing] = useState(false)
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const previewCanvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 预设的旋转角度
  const rotationPresets = [
    { label: '0°', value: 0 },
    { label: '90°', value: 90 },
    { label: '180°', value: 180 },
    { label: '270°', value: 270 },
  ]

  // 处理图片上传
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      const img = new Image()
      img.onload = () => {
        setImageSize({ width: img.width, height: img.height })
        setOriginalImage(event.target?.result as string)
        setTransform({
          rotation: 0,
          flipHorizontal: false,
          flipVertical: false
        })
        setProcessedImage(null)
      }
      img.src = event.target?.result as string
    }
    reader.readAsDataURL(file)
  }

  // 更新预览
  useEffect(() => {
    if (!originalImage || !previewCanvasRef.current) return
    
    const canvas = previewCanvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const img = new Image()
    img.onload = () => {
      // 计算旋转后的画布大小
      const rad = (transform.rotation * Math.PI) / 180
      const cos = Math.abs(Math.cos(rad))
      const sin = Math.abs(Math.sin(rad))
      
      const newWidth = img.width * cos + img.height * sin
      const newHeight = img.width * sin + img.height * cos
      
      // 设置预览画布大小（限制最大尺寸）
      const maxSize = 500
      const scale = Math.min(maxSize / newWidth, maxSize / newHeight, 1)
      
      canvas.width = newWidth * scale
      canvas.height = newHeight * scale
      
      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // 应用变换
      ctx.save()
      ctx.translate(canvas.width / 2, canvas.height / 2)
      
      // 应用翻转
      ctx.scale(
        transform.flipHorizontal ? -1 : 1,
        transform.flipVertical ? -1 : 1
      )
      
      // 应用旋转
      ctx.rotate(rad)
      
      // 绘制图片
      ctx.drawImage(
        img,
        -img.width * scale / 2,
        -img.height * scale / 2,
        img.width * scale,
        img.height * scale
      )
      
      ctx.restore()
    }
    img.src = originalImage
  }, [originalImage, transform])

  // 旋转图片
  const rotateImage = (angle: number) => {
    setTransform(prev => ({
      ...prev,
      rotation: (prev.rotation + angle) % 360
    }))
  }

  // 设置固定角度
  const setRotation = (angle: number) => {
    setTransform(prev => ({
      ...prev,
      rotation: angle
    }))
  }

  // 水平翻转
  const flipHorizontal = () => {
    setTransform(prev => ({
      ...prev,
      flipHorizontal: !prev.flipHorizontal
    }))
  }

  // 垂直翻转
  const flipVertical = () => {
    setTransform(prev => ({
      ...prev,
      flipVertical: !prev.flipVertical
    }))
  }

  // 应用变换
  const applyTransform = () => {
    if (!originalImage || !canvasRef.current) return
    
    setIsProcessing(true)
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const img = new Image()
    img.onload = () => {
      // 计算旋转后的画布大小
      const rad = (transform.rotation * Math.PI) / 180
      const cos = Math.abs(Math.cos(rad))
      const sin = Math.abs(Math.sin(rad))
      
      canvas.width = img.width * cos + img.height * sin
      canvas.height = img.width * sin + img.height * cos
      
      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // 应用变换
      ctx.save()
      ctx.translate(canvas.width / 2, canvas.height / 2)
      
      // 应用翻转
      ctx.scale(
        transform.flipHorizontal ? -1 : 1,
        transform.flipVertical ? -1 : 1
      )
      
      // 应用旋转
      ctx.rotate(rad)
      
      // 绘制图片
      ctx.drawImage(
        img,
        -img.width / 2,
        -img.height / 2,
        img.width,
        img.height
      )
      
      ctx.restore()
      
      // 导出结果
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob)
          setProcessedImage(url)
        }
        setIsProcessing(false)
      }, 'image/png')
    }
    img.src = originalImage
  }

  // 下载图片
  const downloadImage = () => {
    if (!processedImage) return
    
    const link = document.createElement('a')
    link.href = processedImage
    link.download = `transformed_${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 重置
  const reset = () => {
    setOriginalImage(null)
    setProcessedImage(null)
    setImageSize({ width: 0, height: 0 })
    setTransform({
      rotation: 0,
      flipHorizontal: false,
      flipVertical: false
    })
    if (fileInputRef.current) fileInputRef.current.value = ''
  }

  // 重置变换
  const resetTransform = () => {
    setTransform({
      rotation: 0,
      flipHorizontal: false,
      flipVertical: false
    })
    setProcessedImage(null)
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片旋转翻转工具</h2>
        <p className="text-gray-600">
          轻松旋转和翻转您的图片，支持任意角度旋转和水平/垂直翻转
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {/* 控制面板 */}
        <div className="md:col-span-1 space-y-4">
          <div className="bg-white rounded-lg border border-gray-300 p-4">
            <h3 className="font-medium mb-4">变换控制</h3>
            
            {/* 旋转控制 */}
            <div className="mb-6">
              <label className="block text-sm font-medium mb-3">旋转角度</label>
              
              {/* 快速旋转按钮 */}
              <div className="grid grid-cols-2 gap-2 mb-3">
                <button
                  onClick={() => rotateImage(-90)}
                  disabled={!originalImage}
                  className="px-3 py-2 bg-gray-200 rounded-md hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 flex items-center justify-center gap-2"
                >
                  <RotateCcw className="w-4 h-4" />
                  逆时针 90°
                </button>
                <button
                  onClick={() => rotateImage(90)}
                  disabled={!originalImage}
                  className="px-3 py-2 bg-gray-200 rounded-md hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 flex items-center justify-center gap-2"
                >
                  <RotateCw className="w-4 h-4" />
                  顺时针 90°
                </button>
              </div>
              
              {/* 预设角度 */}
              <div className="grid grid-cols-4 gap-1 mb-3">
                {rotationPresets.map(preset => (
                  <button
                    key={preset.value}
                    onClick={() => setRotation(preset.value)}
                    disabled={!originalImage}
                    className={`px-2 py-1 text-sm rounded ${
                      transform.rotation === preset.value
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 hover:bg-gray-200'
                    } disabled:bg-gray-50 disabled:text-gray-400`}
                  >
                    {preset.label}
                  </button>
                ))}
              </div>
              
              {/* 自定义角度滑块 */}
              <div>
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>自定义角度</span>
                  <span>{transform.rotation}°</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="359"
                  value={transform.rotation}
                  onChange={(e) => setRotation(Number(e.target.value))}
                  disabled={!originalImage}
                  className="w-full"
                />
              </div>
            </div>

            {/* 翻转控制 */}
            <div className="mb-6">
              <label className="block text-sm font-medium mb-3">翻转选项</label>
              <div className="space-y-2">
                <button
                  onClick={flipHorizontal}
                  disabled={!originalImage}
                  className={`w-full px-3 py-2 rounded-md flex items-center justify-center gap-2 ${
                    transform.flipHorizontal
                      ? 'bg-blue-500 text-white hover:bg-blue-600'
                      : 'bg-gray-200 hover:bg-gray-300'
                  } disabled:bg-gray-100 disabled:text-gray-400`}
                >
                  <FlipHorizontal className="w-4 h-4" />
                  水平翻转
                </button>
                <button
                  onClick={flipVertical}
                  disabled={!originalImage}
                  className={`w-full px-3 py-2 rounded-md flex items-center justify-center gap-2 ${
                    transform.flipVertical
                      ? 'bg-blue-500 text-white hover:bg-blue-600'
                      : 'bg-gray-200 hover:bg-gray-300'
                  } disabled:bg-gray-100 disabled:text-gray-400`}
                >
                  <FlipVertical className="w-4 h-4" />
                  垂直翻转
                </button>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-2">
              <button
                onClick={applyTransform}
                disabled={!originalImage || isProcessing}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                应用变换
              </button>
              {processedImage && (
                <button
                  onClick={downloadImage}
                  className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                >
                  <Download className="w-4 h-4 inline mr-2" />
                  下载结果
                </button>
              )}
              <button
                onClick={resetTransform}
                disabled={!originalImage}
                className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                重置变换
              </button>
              <button
                onClick={reset}
                className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
              >
                <RefreshCw className="w-4 h-4 inline mr-2" />
                重新开始
              </button>
            </div>
          </div>

          {/* 图片信息 */}
          {originalImage && (
            <div className="bg-white rounded-lg border border-gray-300 p-4">
              <h3 className="font-medium mb-2">图片信息</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p>原始尺寸: {imageSize.width} × {imageSize.height} 像素</p>
                <p>当前旋转: {transform.rotation}°</p>
                <p>水平翻转: {transform.flipHorizontal ? '是' : '否'}</p>
                <p>垂直翻转: {transform.flipVertical ? '是' : '否'}</p>
              </div>
            </div>
          )}
        </div>

        {/* 预览区域 */}
        <div className="md:col-span-2">
          {!originalImage ? (
            <div className="bg-white rounded-lg border-2 border-dashed border-gray-300 p-12">
              <div className="text-center">
                <ImageIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">上传图片开始编辑</p>
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleImageUpload}
                  accept="image/*"
                  className="hidden"
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  <Upload className="w-4 h-4 inline mr-2" />
                  选择图片
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 预览 */}
              <div className="bg-white rounded-lg border border-gray-300 p-4">
                <h3 className="font-medium mb-4">实时预览</h3>
                <div className="bg-gray-100 rounded-lg p-4 flex items-center justify-center min-h-[300px]">
                  <canvas
                    ref={previewCanvasRef}
                    className="max-w-full max-h-[500px]"
                  />
                </div>
              </div>

              {/* 处理结果 */}
              {processedImage && (
                <div className="bg-white rounded-lg border border-gray-300 p-4">
                  <h3 className="font-medium mb-4">处理结果</h3>
                  <div className="bg-gray-100 rounded-lg p-4 flex items-center justify-center">
                    <NextImage
                      src={processedImage}
                      alt="Processed"
                      width={500}
                      height={400}
                      className="max-w-full max-h-[500px] object-contain"
                      unoptimized
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Canvas 用于处理图片 */}
      <canvas ref={canvasRef} className="hidden" />

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <RotateCw className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用说明</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>支持任意角度旋转（0-359度）</li>
              <li>快速旋转按钮可以90度递增旋转</li>
              <li>支持水平和垂直翻转，可同时应用</li>
              <li>实时预览变换效果</li>
              <li>处理后的图片保持原始质量</li>
              <li>支持常见图片格式（JPG、PNG、GIF等）</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
