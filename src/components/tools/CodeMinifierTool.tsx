'use client'

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Download, RefreshCw, Zap, FileCode, Gauge, AlertCircle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CompressionResult {
  code: string
  originalSize: number
  compressedSize: number
  savings: number
  compressionRatio: number
}

const CODE_TYPES = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'css', label: 'CSS' },
  { value: 'html', label: 'HTML' },
  { value: 'json', label: 'JSON' },
]

const COMPRESSION_LEVELS = [
  { value: 'basic', label: '基础压缩', description: '移除空白和注释' },
  { value: 'aggressive', label: '激进压缩', description: '变量名压缩' },
  { value: 'extreme', label: '极限压缩', description: '最大化压缩' },
]

export default function CodeMinifierTool() {
  const [inputCode, setInputCode] = useState('')
  const [outputCode, setOutputCode] = useState('')
  const [codeType, setCodeType] = useState('javascript')
  const [compressionLevel, setCompressionLevel] = useState('basic')
  const [isCompressing, setIsCompressing] = useState(false)
  const [result, setResult] = useState<CompressionResult | null>(null)
  const [activeTab, setActiveTab] = useState('compress')
  const { toast } = useToast()

  // JavaScript 压缩
  const minifyJavaScript = useCallback((code: string, level: string) => {
    let minified = code
    
    // 移除注释
    minified = minified.replace(/\/\*[\s\S]*?\*\//g, '')
    minified = minified.replace(/\/\/.*$/gm, '')
    
    // 移除多余空白
    minified = minified.replace(/\s+/g, ' ')
    minified = minified.replace(/;\s*}/g, '}')
    minified = minified.replace(/{\s+/g, '{')
    minified = minified.replace(/}\s+/g, '}')
    minified = minified.replace(/,\s+/g, ',')
    minified = minified.replace(/:\s+/g, ':')
    minified = minified.replace(/;\s+/g, ';')
    
    if (level === 'aggressive' || level === 'extreme') {
      // 简单的变量名压缩
      const vars = new Map<string, string>()
      let counter = 0
      
      minified = minified.replace(/\b(var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g, (match, type, varName) => {
        if (!vars.has(varName)) {
          vars.set(varName, `_${counter++}`)
        }
        return `${type} ${vars.get(varName)}`
      })
      
      // 替换变量引用
      vars.forEach((shortName, originalName) => {
        const regex = new RegExp(`\\b${originalName}\\b`, 'g')
        minified = minified.replace(regex, shortName)
      })
    }
    
    if (level === 'extreme') {
      // 移除所有不必要的空白
      minified = minified.replace(/\s*([{}();,:])\s*/g, '$1')
      minified = minified.replace(/\s*=\s*/g, '=')
      minified = minified.replace(/\s*\+\s*/g, '+')
      minified = minified.replace(/\s*-\s*/g, '-')
      minified = minified.replace(/\s*\*\s*/g, '*')
      minified = minified.replace(/\s*\/\s*/g, '/')
    }
    
    return minified.trim()
  }, [])

  // CSS 压缩
  const minifyCSS = useCallback((code: string, level: string) => {
    let minified = code
    
    // 移除注释
    minified = minified.replace(/\/\*[\s\S]*?\*\//g, '')
    
    // 移除多余空白
    minified = minified.replace(/\s+/g, ' ')
    minified = minified.replace(/;\s*}/g, '}')
    minified = minified.replace(/{\s+/g, '{')
    minified = minified.replace(/}\s+/g, '}')
    minified = minified.replace(/,\s+/g, ',')
    minified = minified.replace(/:\s+/g, ':')
    minified = minified.replace(/;\s+/g, ';')
    
    if (level === 'aggressive' || level === 'extreme') {
      // 压缩颜色值
      minified = minified.replace(/#([0-9a-f])\1([0-9a-f])\2([0-9a-f])\3/gi, '#$1$2$3')
      
      // 移除零值单位
      minified = minified.replace(/\b0+(px|em|rem|%|vh|vw|pt|pc|in|cm|mm|ex|ch|vmin|vmax)/g, '0')
      
      // 压缩小数
      minified = minified.replace(/0\.([0-9]+)/g, '.$1')
    }
    
    if (level === 'extreme') {
      // 移除所有不必要的空白
      minified = minified.replace(/\s*([{}();,:])\s*/g, '$1')
      minified = minified.replace(/\s*>\s*/g, '>')
      minified = minified.replace(/\s*\+\s*/g, '+')
      minified = minified.replace(/\s*~\s*/g, '~')
    }
    
    return minified.trim()
  }, [])

  // HTML 压缩
  const minifyHTML = useCallback((code: string, level: string) => {
    let minified = code
    
    // 移除注释（保留条件注释）
    minified = minified.replace(/<!--(?!\[if)[\s\S]*?-->/g, '')
    
    // 移除多余空白
    minified = minified.replace(/\s+/g, ' ')
    minified = minified.replace(/>\s+</g, '><')
    
    if (level === 'aggressive' || level === 'extreme') {
      // 移除可选的结束标签
      minified = minified.replace(/<\/(?:p|li|dt|dd|option|thead|tbody|tfoot|tr|td|th)>/gi, '')
      
      // 移除可选属性引号
      minified = minified.replace(/=(['"]?)([a-zA-Z0-9-_]+)\1/g, '=$2')
    }
    
    if (level === 'extreme') {
      // 移除标签间的空白
      minified = minified.replace(/>\s+</g, '><')
      minified = minified.replace(/\s+/g, ' ')
    }
    
    return minified.trim()
  }, [])

  // JSON 压缩
  const minifyJSON = useCallback((code: string) => {
    try {
      const parsed = JSON.parse(code)
      return JSON.stringify(parsed)
    } catch (error) {
      throw new Error('JSON格式错误')
    }
  }, [])

  // 执行压缩
  const handleMinify = useCallback(() => {
    if (!inputCode.trim()) {
      toast({
        title: '请输入代码',
        description: '请在输入框中输入需要压缩的代码',
        variant: 'destructive',
      })
      return
    }

    setIsCompressing(true)
    
    try {
      let minified = ''
      
      switch (codeType) {
        case 'javascript':
          minified = minifyJavaScript(inputCode, compressionLevel)
          break
        case 'css':
          minified = minifyCSS(inputCode, compressionLevel)
          break
        case 'html':
          minified = minifyHTML(inputCode, compressionLevel)
          break
        case 'json':
          minified = minifyJSON(inputCode)
          break
        default:
          throw new Error('不支持的代码类型')
      }
      
      const originalSize = new Blob([inputCode]).size
      const compressedSize = new Blob([minified]).size
      const savings = originalSize - compressedSize
      const compressionRatio = ((savings / originalSize) * 100)
      
      setOutputCode(minified)
      setResult({
        code: minified,
        originalSize,
        compressedSize,
        savings,
        compressionRatio
      })
      
      toast({
        title: '压缩成功',
        description: `节省了 ${savings} 字节 (${compressionRatio.toFixed(1)}%)`,
      })
    } catch (error) {
      toast({
        title: '压缩失败',
        description: error instanceof Error ? error.message : '代码格式错误',
        variant: 'destructive',
      })
    } finally {
      setIsCompressing(false)
    }
  }, [inputCode, codeType, compressionLevel, minifyJavaScript, minifyCSS, minifyHTML, minifyJSON, toast])

  // 复制结果
  const copyResult = useCallback(() => {
    if (!outputCode) {
      toast({
        title: '请先压缩代码',
        description: '请先执行压缩操作',
        variant: 'destructive',
      })
      return
    }

    navigator.clipboard.writeText(outputCode).then(() => {
      toast({
        title: '复制成功',
        description: '压缩后的代码已复制到剪贴板',
      })
    }).catch(() => {
      toast({
        title: '复制失败',
        description: '请手动选择并复制',
        variant: 'destructive',
      })
    })
  }, [outputCode, toast])

  // 下载结果
  const downloadResult = useCallback(() => {
    if (!outputCode) {
      toast({
        title: '请先压缩代码',
        description: '请先执行压缩操作',
        variant: 'destructive',
      })
      return
    }

    const extensions = {
      javascript: 'js',
      css: 'css',
      html: 'html',
      json: 'json'
    }
    
    const blob = new Blob([outputCode], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `minified.${extensions[codeType as keyof typeof extensions]}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: '下载成功',
      description: '压缩后的代码已下载',
    })
  }, [outputCode, codeType, toast])

  // 清空输入
  const clearInput = useCallback(() => {
    setInputCode('')
    setOutputCode('')
    setResult(null)
  }, [])

  // 格式化字节大小
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileCode className="h-5 w-5" />
            代码压缩器
          </CardTitle>
          <CardDescription>
            压缩 JavaScript、CSS、HTML、JSON 代码，减小文件体积
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="compress">压缩代码</TabsTrigger>
              <TabsTrigger value="settings">压缩设置</TabsTrigger>
            </TabsList>

            <TabsContent value="compress" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 输入区域 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="input" className="text-base font-medium">输入代码</Label>
                    <div className="flex items-center gap-2">
                      <Select value={codeType} onValueChange={setCodeType}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {CODE_TYPES.map(type => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <Textarea
                    id="input"
                    value={inputCode}
                    onChange={(e) => setInputCode(e.target.value)}
                    placeholder="在此输入或粘贴需要压缩的代码..."
                    className="min-h-[400px] font-mono text-sm"
                  />
                  
                  <div className="flex gap-2">
                    <Button onClick={handleMinify} disabled={isCompressing} className="flex-1">
                      {isCompressing ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          压缩中...
                        </>
                      ) : (
                        <>
                          <Zap className="h-4 w-4 mr-2" />
                          压缩代码
                        </>
                      )}
                    </Button>
                    <Button onClick={clearInput} variant="outline">
                      清空
                    </Button>
                  </div>
                </div>

                {/* 输出区域 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="output" className="text-base font-medium">压缩结果</Label>
                    <div className="flex gap-2">
                      <Button onClick={copyResult} variant="outline" size="sm">
                        <Copy className="h-4 w-4 mr-2" />
                        复制
                      </Button>
                      <Button onClick={downloadResult} variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        下载
                      </Button>
                    </div>
                  </div>
                  
                  <Textarea
                    id="output"
                    value={outputCode}
                    readOnly
                    placeholder="压缩后的代码将在此显示..."
                    className="min-h-[400px] font-mono text-sm"
                  />
                  
                  {result && (
                    <Card className="bg-muted/50">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Gauge className="h-4 w-4 text-green-600" />
                          <span className="font-medium">压缩统计</span>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">原始大小: </span>
                            <span className="font-mono">{formatBytes(result.originalSize)}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">压缩后: </span>
                            <span className="font-mono">{formatBytes(result.compressedSize)}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">节省: </span>
                            <span className="font-mono text-green-600">{formatBytes(result.savings)}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">压缩率: </span>
                            <span className="font-mono text-green-600">{result.compressionRatio.toFixed(1)}%</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <div className="max-w-md space-y-4">
                <div className="space-y-2">
                  <Label>代码类型</Label>
                  <Select value={codeType} onValueChange={setCodeType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CODE_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>压缩级别</Label>
                  <Select value={compressionLevel} onValueChange={setCompressionLevel}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {COMPRESSION_LEVELS.map(level => (
                        <SelectItem key={level.value} value={level.value}>
                          <div className="flex flex-col">
                            <span>{level.label}</span>
                            <span className="text-xs text-muted-foreground">{level.description}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Card className="bg-blue-50 dark:bg-blue-900/20">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
                      <div className="text-sm text-blue-600">
                        <p className="font-medium mb-1">压缩说明</p>
                        <ul className="text-xs space-y-1">
                          <li>• 基础压缩：移除空白字符和注释</li>
                          <li>• 激进压缩：变量名短化和优化</li>
                          <li>• 极限压缩：最大化压缩，可能影响可读性</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card className="bg-muted/50">
        <CardHeader>
          <CardTitle className="text-lg">使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm text-muted-foreground">
            <div>
              <h4 className="font-medium text-foreground mb-2">支持的代码类型</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li><strong>JavaScript</strong>：变量压缩、注释移除、空白优化</li>
                <li><strong>CSS</strong>：颜色压缩、单位优化、样式合并</li>
                <li><strong>HTML</strong>：标签优化、属性压缩、空白移除</li>
                <li><strong>JSON</strong>：格式化压缩、空白移除</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-foreground mb-2">压缩特性</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>智能识别代码类型并应用对应压缩策略</li>
                <li>支持多种压缩级别，满足不同需求</li>
                <li>实时显示压缩统计和节省空间</li>
                <li>支持复制和下载压缩结果</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
