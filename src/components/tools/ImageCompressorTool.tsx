'use client'

import { useState, useRef, useCallback } from 'react'
import Image from 'next/image'
import { Image as ImageIcon, Upload, Download, Settings, AlertCircle, CheckCircle2 } from 'lucide-react'

interface CompressedImage {
  file: File
  originalSize: number
  compressedSize: number
  preview: string
}

export default function ImageCompressorTool() {
  const [images, setImages] = useState<CompressedImage[]>([])
  const [quality, setQuality] = useState(0.8)
  const [maxWidth, setMaxWidth] = useState(1920)
  const [maxHeight, setMaxHeight] = useState(1080)
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true)
  const [isCompressing, setIsCompressing] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const compressImage = useCallback(async (file: File): Promise<CompressedImage> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        const img = document.createElement('img')
        img.onload = () => {
          const canvas = document.createElement('canvas')
          let width = img.width
          let height = img.height

          // 计算新尺寸
          if (width > maxWidth || height > maxHeight) {
            if (maintainAspectRatio) {
              const aspectRatio = width / height
              if (width > maxWidth) {
                width = maxWidth
                height = width / aspectRatio
              }
              if (height > maxHeight) {
                height = maxHeight
                width = height * aspectRatio
              }
            } else {
              width = Math.min(width, maxWidth)
              height = Math.min(height, maxHeight)
            }
          }

          canvas.width = width
          canvas.height = height

          const ctx = canvas.getContext('2d')
          if (!ctx) {
            reject(new Error('无法获取 canvas context'))
            return
          }

          // 绘制图像
          ctx.drawImage(img, 0, 0, width, height)

          // 转换为 Blob
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('压缩失败'))
                return
              }

              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now(),
              })

              resolve({
                file: compressedFile,
                originalSize: file.size,
                compressedSize: blob.size,
                preview: canvas.toDataURL('image/jpeg', quality),
              })
            },
            'image/jpeg',
            quality
          )
        }

        img.onerror = () => {
          reject(new Error('图片加载失败'))
        }

        img.src = e.target?.result as string
      }

      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }

      reader.readAsDataURL(file)
    })
  }, [maxWidth, maxHeight, maintainAspectRatio, quality])

  const handleFileSelect = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    setIsCompressing(true)
    
    try {
      const compressedImages = await Promise.all(
        files.map(file => compressImage(file))
      )
      setImages(prev => [...prev, ...compressedImages])
    } catch (error) {
      console.error('压缩失败:', error)
    } finally {
      setIsCompressing(false)
    }

    // 清空 input 以允许重复选择相同文件
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [compressImage])

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()

    const files = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    )

    if (files.length > 0) {
      const input = fileInputRef.current
      if (input) {
        const dataTransfer = new DataTransfer()
        files.forEach(file => dataTransfer.items.add(file))
        input.files = dataTransfer.files
        
        const event = new Event('change', { bubbles: true })
        input.dispatchEvent(event)
      }
    }
  }, [])

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const downloadImage = (image: CompressedImage) => {
    const url = URL.createObjectURL(image.file)
    const a = document.createElement('a')
    a.href = url
    a.download = `compressed_${image.file.name}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const downloadAll = () => {
    images.forEach(image => downloadImage(image))
  }

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index))
  }

  const clearAll = () => {
    setImages([])
  }

  const totalOriginalSize = images.reduce((sum, img) => sum + img.originalSize, 0)
  const totalCompressedSize = images.reduce((sum, img) => sum + img.compressedSize, 0)
  const totalSaved = totalOriginalSize - totalCompressedSize
  const savingPercentage = totalOriginalSize > 0 
    ? Math.round((totalSaved / totalOriginalSize) * 100) 
    : 0

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">图片压缩工具</h2>
        <p className="text-gray-600">
          批量压缩图片，支持调整尺寸和质量，大幅减小文件体积
        </p>
      </div>

      {/* 设置面板 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="flex items-center text-sm font-medium text-gray-700 mb-4">
          <Settings className="w-4 h-4 mr-2" />
          压缩设置
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label htmlFor="quality" className="block text-sm text-gray-600 mb-1">
              质量: {Math.round(quality * 100)}%
            </label>
            <input
              id="quality"
              type="range"
              min="0.1"
              max="1"
              step="0.1"
              value={quality}
              onChange={(e) => setQuality(Number(e.target.value))}
              className="w-full"
            />
          </div>
          
          <div>
            <label htmlFor="maxWidth" className="block text-sm text-gray-600 mb-1">
              最大宽度 (px)
            </label>
            <input
              id="maxWidth"
              type="number"
              value={maxWidth}
              onChange={(e) => setMaxWidth(Number(e.target.value))}
              className="w-full px-3 py-1 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label htmlFor="maxHeight" className="block text-sm text-gray-600 mb-1">
              最大高度 (px)
            </label>
            <input
              id="maxHeight"
              type="number"
              value={maxHeight}
              onChange={(e) => setMaxHeight(Number(e.target.value))}
              className="w-full px-3 py-1 border border-gray-300 rounded-md"
            />
          </div>
          
          <div className="flex items-end">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={maintainAspectRatio}
                onChange={(e) => setMaintainAspectRatio(e.target.checked)}
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">保持宽高比</span>
            </label>
          </div>
        </div>
      </div>

      {/* 上传区域 */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
      >
        <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p className="text-gray-600 mb-2">拖拽图片到这里，或点击选择</p>
        <p className="text-sm text-gray-500 mb-4">支持 JPG、PNG、GIF、WebP 等格式</p>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={isCompressing}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {isCompressing ? '压缩中...' : '选择图片'}
        </button>
      </div>

      {/* 压缩结果 */}
      {images.length > 0 && (
        <div className="mt-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">压缩结果</h3>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                共节省 {formatFileSize(totalSaved)} ({savingPercentage}%)
              </div>
              <button
                onClick={downloadAll}
                className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                下载全部
              </button>
              <button
                onClick={clearAll}
                className="px-4 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                清空全部
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {images.map((image, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <div className="aspect-video bg-gray-100 relative">
                  <Image
                    src={image.preview}
                    alt={`压缩后的图片 ${index + 1}`}
                    fill
                    className="object-contain"
                    unoptimized
                  />
                </div>
                <div className="p-4">
                  <p className="text-sm font-medium truncate mb-2">{image.file.name}</p>
                  <div className="text-xs text-gray-600 space-y-1">
                    <p>原始大小: {formatFileSize(image.originalSize)}</p>
                    <p>压缩后: {formatFileSize(image.compressedSize)}</p>
                    <p className="text-green-600">
                      节省: {formatFileSize(image.originalSize - image.compressedSize)} 
                      ({Math.round(((image.originalSize - image.compressedSize) / image.originalSize) * 100)}%)
                    </p>
                  </div>
                  <div className="mt-3 flex space-x-2">
                    <button
                      onClick={() => downloadImage(image)}
                      className="flex-1 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      下载
                    </button>
                    <button
                      onClick={() => removeImage(index)}
                      className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 使用提示 */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
          <div className="text-sm text-blue-900">
            <h3 className="font-semibold mb-1">使用提示</h3>
            <ul className="list-disc list-inside space-y-1">
              <li>质量设置越低，文件越小，但图片质量会下降</li>
              <li>建议照片使用 70-80% 质量，截图使用 80-90% 质量</li>
              <li>调整尺寸可以大幅减小文件体积</li>
              <li>所有处理都在浏览器本地完成，不会上传到服务器</li>
              <li>压缩后的图片格式统一为 JPEG</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
