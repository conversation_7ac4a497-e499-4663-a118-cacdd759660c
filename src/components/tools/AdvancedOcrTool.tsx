'use client'

import React, { useState, use<PERSON><PERSON>back, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Upload, Download, FileText, Image as ImageIcon, Copy, Eye, Settings, Languages, Zap, Search, Filter, RotateCw, Maximize, Type, FileImage, BookOpen } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface OcrResult {
  id: string
  text: string
  confidence: number
  language: string
  words: Array<{
    text: string
    confidence: number
    bbox: [number, number, number, number]
  }>
  lines: Array<{
    text: string
    confidence: number
    bbox: [number, number, number, number]
  }>
  blocks: Array<{
    text: string
    confidence: number
    bbox: [number, number, number, number]
  }>
}

interface ProcessedFile {
  id: string
  file: File
  preview: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress: number
  result?: OcrResult
  error?: string
}

interface OcrSettings {
  language: string
  psm: number // Page Segmentation Mode
  oem: number // OCR Engine Mode
  whitelist: string
  blacklist: string
  dpi: number
  preprocessImage: boolean
  enhanceContrast: boolean
  removeNoise: boolean
  correctSkew: boolean
}

const SUPPORTED_LANGUAGES = [
  { code: 'eng', name: '英语 (English)', flag: '🇺🇸' },
  { code: 'chi_sim', name: '简体中文', flag: '🇨🇳' },
  { code: 'chi_tra', name: '繁体中文', flag: '🇹🇼' },
  { code: 'jpn', name: '日语 (Japanese)', flag: '🇯🇵' },
  { code: 'kor', name: '韩语 (Korean)', flag: '🇰🇷' },
  { code: 'fra', name: '法语 (French)', flag: '🇫🇷' },
  { code: 'deu', name: '德语 (German)', flag: '🇩🇪' },
  { code: 'spa', name: '西班牙语 (Spanish)', flag: '🇪🇸' },
  { code: 'rus', name: '俄语 (Russian)', flag: '🇷🇺' },
  { code: 'ara', name: '阿拉伯语 (Arabic)', flag: '🇸🇦' },
]

const PSM_OPTIONS = [
  { value: 0, label: '0 - 仅方向和脚本检测 (OSD)' },
  { value: 1, label: '1 - 自动页面分割 (OSD)' },
  { value: 2, label: '2 - 自动页面分割，无OSD或OCR' },
  { value: 3, label: '3 - 全自动页面分割，无OSD (默认)' },
  { value: 4, label: '4 - 假设单列可变大小文本' },
  { value: 5, label: '5 - 假设单个统一文本块' },
  { value: 6, label: '6 - 假设单个统一文本行' },
  { value: 7, label: '7 - 将图像视为单个文本词' },
  { value: 8, label: '8 - 将图像视为单个字符' },
  { value: 9, label: '9 - 将图像视为圆圈中的单个词' },
  { value: 10, label: '10 - 将图像视为单个字符' },
  { value: 11, label: '11 - 稀疏文本' },
  { value: 12, label: '12 - 稀疏文本 (OSD)' },
  { value: 13, label: '13 - 原始行，将图像视为单个文本行' },
]

export default function AdvancedOcrTool() {
  const [files, setFiles] = useState<ProcessedFile[]>([])
  const [settings, setSettings] = useState<OcrSettings>({
    language: 'eng',
    psm: 3,
    oem: 3,
    whitelist: '',
    blacklist: '',
    dpi: 300,
    preprocessImage: true,
    enhanceContrast: true,
    removeNoise: true,
    correctSkew: true
  })
  const [isProcessing, setIsProcessing] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [selectedResult, setSelectedResult] = useState<OcrResult | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const { toast } = useToast()

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    const droppedFiles = Array.from(e.dataTransfer.files)
    handleFiles(droppedFiles)
  }, [])

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files)
      handleFiles(selectedFiles)
    }
  }

  const handleFiles = (fileList: File[]) => {
    const validFiles = fileList.filter(file => {
      return file.type.startsWith('image/') || file.type === 'application/pdf'
    })

    if (validFiles.length !== fileList.length) {
      toast({
        title: '部分文件不支持',
        description: '仅支持图片文件和PDF文档',
        variant: 'destructive'
      })
    }

    const newFiles: ProcessedFile[] = validFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      preview: URL.createObjectURL(file),
      status: 'pending',
      progress: 0
    }))

    setFiles(prev => [...prev, ...newFiles])
  }

  const removeFile = (id: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === id)
      if (file?.preview) {
        URL.revokeObjectURL(file.preview)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  const clearAll = () => {
    files.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview)
      }
    })
    setFiles([])
    setSelectedResult(null)
  }

  // 模拟OCR处理（实际应用中需要集成真实的OCR服务）
  const simulateOcr = async (file: ProcessedFile): Promise<OcrResult> => {
    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000))
    
    // 模拟OCR结果
    const mockTexts = [
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      'The quick brown fox jumps over the lazy dog.',
      '这是一段中文文本，用于测试OCR识别效果。',
      'Hello World! This is a test document.',
      '人工智能技术正在快速发展，改变着我们的生活方式。'
    ]
    
    const randomText = mockTexts[Math.floor(Math.random() * mockTexts.length)]
    const words = randomText.split(' ')
    
    return {
      id: file.id,
      text: randomText,
      confidence: 85 + Math.random() * 10,
      language: settings.language,
      words: words.map((word, index) => ({
        text: word,
        confidence: 80 + Math.random() * 15,
        bbox: [index * 50, 10, (index + 1) * 50, 30]
      })),
      lines: [{
        text: randomText,
        confidence: 88 + Math.random() * 8,
        bbox: [0, 10, 500, 30]
      }],
      blocks: [{
        text: randomText,
        confidence: 90 + Math.random() * 8,
        bbox: [0, 0, 500, 50]
      }]
    }
  }

  const processFile = async (file: ProcessedFile) => {
    setFiles(prev => prev.map(f => 
      f.id === file.id 
        ? { ...f, status: 'processing' as const, progress: 0 }
        : f
    ))

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(f => 
          f.id === file.id && f.progress < 90
            ? { ...f, progress: f.progress + 10 }
            : f
        ))
      }, 200)

      const result = await simulateOcr(file)

      clearInterval(progressInterval)

      setFiles(prev => prev.map(f => 
        f.id === file.id 
          ? { 
              ...f, 
              status: 'completed' as const, 
              progress: 100,
              result
            }
          : f
      ))

    } catch (error) {
      setFiles(prev => prev.map(f => 
        f.id === file.id 
          ? { 
              ...f, 
              status: 'error' as const, 
              progress: 0,
              error: error instanceof Error ? error.message : 'OCR处理失败'
            }
          : f
      ))
    }
  }

  const processAll = async () => {
    setIsProcessing(true)
    const pendingFiles = files.filter(f => f.status === 'pending')
    
    for (const file of pendingFiles) {
      await processFile(file)
    }
    
    setIsProcessing(false)
    toast({
      title: 'OCR处理完成',
      description: `成功处理 ${pendingFiles.length} 个文件`
    })
  }

  const copyText = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: '已复制',
      description: '文本已复制到剪贴板'
    })
  }

  const exportResults = () => {
    const completedFiles = files.filter(f => f.status === 'completed' && f.result)
    const results = completedFiles.map(f => ({
      filename: f.file.name,
      text: f.result!.text,
      confidence: f.result!.confidence,
      language: f.result!.language,
      timestamp: new Date().toISOString()
    }))

    const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `ocr-results-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const drawBoundingBoxes = (result: OcrResult, imageUrl: string) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const img = new Image()
    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0)

      // 绘制边界框
      ctx.strokeStyle = '#ff0000'
      ctx.lineWidth = 2
      
      result.words.forEach(word => {
        const [x, y, x2, y2] = word.bbox
        ctx.strokeRect(x, y, x2 - x, y2 - y)
        
        // 显示置信度
        ctx.fillStyle = '#ff0000'
        ctx.font = '12px Arial'
        ctx.fillText(`${word.confidence.toFixed(1)}%`, x, y - 5)
      })
    }
    img.src = imageUrl
  }

  const filteredFiles = files.filter(file => {
    if (!searchQuery) return true
    const query = searchQuery.toLowerCase()
    return file.file.name.toLowerCase().includes(query) ||
           (file.result?.text.toLowerCase().includes(query))
  })

  const getLanguageName = (code: string) => {
    return SUPPORTED_LANGUAGES.find(lang => lang.code === code)?.name || code
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">增强版OCR文字识别</h1>
        <p className="text-muted-foreground">
          高精度图片文字识别工具，支持多语言、批量处理和智能预处理
        </p>
      </div>

      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="upload">文件上传</TabsTrigger>
          <TabsTrigger value="results">识别结果</TabsTrigger>
          <TabsTrigger value="settings">高级设置</TabsTrigger>
          <TabsTrigger value="analysis">结果分析</TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-6">
          {/* 文件上传区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                上传文件
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive 
                    ? 'border-primary bg-primary/5' 
                    : 'border-muted-foreground/25 hover:border-primary/50'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <FileImage className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">
                  拖拽文件到此处或点击选择
                </p>
                <p className="text-sm text-muted-foreground mb-4">
                  支持图片文件 (JPG, PNG, GIF, BMP, TIFF) 和 PDF 文档
                </p>
                <Input
                  type="file"
                  multiple
                  accept="image/*,.pdf"
                  onChange={handleFileInput}
                  className="hidden"
                  id="file-upload"
                />
                <Label htmlFor="file-upload">
                  <Button variant="outline" className="cursor-pointer">
                    选择文件
                  </Button>
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* 文件列表 */}
          {files.length > 0 && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    文件列表 ({files.length})
                  </CardTitle>
                  <div className="flex gap-2">
                    <Button
                      onClick={processAll}
                      disabled={isProcessing || files.every(f => f.status !== 'pending')}
                      className="flex items-center gap-2"
                    >
                      <Zap className="h-4 w-4" />
                      {isProcessing ? '处理中...' : '全部识别'}
                    </Button>
                    <Button
                      onClick={clearAll}
                      variant="outline"
                      size="sm"
                    >
                      清空
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {files.map((file) => (
                    <div key={file.id} className="border rounded-lg p-4">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                          <img
                            src={file.preview}
                            alt={file.file.name}
                            className="w-16 h-16 object-cover rounded border"
                          />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium truncate">{file.file.name}</h4>
                            <Badge variant={
                              file.status === 'completed' ? 'default' :
                              file.status === 'error' ? 'destructive' :
                              file.status === 'processing' ? 'secondary' : 'outline'
                            }>
                              {file.status === 'pending' && '待处理'}
                              {file.status === 'processing' && '处理中'}
                              {file.status === 'completed' && '已完成'}
                              {file.status === 'error' && '失败'}
                            </Badge>
                          </div>
                          
                          {file.status === 'processing' && (
                            <Progress value={file.progress} className="mb-2" />
                          )}

                          {file.status === 'error' && (
                            <p className="text-sm text-destructive mb-2">{file.error}</p>
                          )}

                          {file.result && (
                            <div className="text-sm text-muted-foreground mb-2">
                              <span>识别语言: {getLanguageName(file.result.language)}</span>
                              <span className="mx-2">•</span>
                              <span>置信度: {file.result.confidence.toFixed(1)}%</span>
                            </div>
                          )}

                          <div className="flex gap-2">
                            {file.status === 'pending' && (
                              <Button
                                onClick={() => processFile(file)}
                                size="sm"
                                variant="outline"
                              >
                                识别
                              </Button>
                            )}
                            {file.result && (
                              <>
                                <Button
                                  onClick={() => setSelectedResult(file.result!)}
                                  size="sm"
                                  variant="outline"
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  查看
                                </Button>
                                <Button
                                  onClick={() => copyText(file.result!.text)}
                                  size="sm"
                                  variant="outline"
                                >
                                  <Copy className="h-3 w-3 mr-1" />
                                  复制
                                </Button>
                              </>
                            )}
                            <Button
                              onClick={() => removeFile(file.id)}
                              size="sm"
                              variant="ghost"
                            >
                              删除
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="flex-1">
              <Input
                placeholder="搜索文件名或识别内容..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <Button
              onClick={exportResults}
              disabled={!files.some(f => f.status === 'completed')}
              variant="outline"
            >
              <Download className="h-4 w-4 mr-2" />
              导出结果
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredFiles.filter(f => f.result).map((file) => (
              <Card key={file.id}>
                <CardHeader>
                  <CardTitle className="text-sm truncate">{file.file.name}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-sm">
                    <div className="flex items-center gap-2 mb-2">
                      <Languages className="h-4 w-4" />
                      <span>{getLanguageName(file.result!.language)}</span>
                      <Badge variant="secondary">
                        {file.result!.confidence.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                  
                  <Textarea
                    value={file.result!.text}
                    readOnly
                    className="min-h-[100px] resize-none"
                  />
                  
                  <div className="flex gap-2">
                    <Button
                      onClick={() => copyText(file.result!.text)}
                      size="sm"
                      variant="outline"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      复制
                    </Button>
                    <Button
                      onClick={() => setSelectedResult(file.result!)}
                      size="sm"
                      variant="outline"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      详细分析
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                OCR 设置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>识别语言</Label>
                    <Select
                      value={settings.language}
                      onValueChange={(value) => setSettings(prev => ({ ...prev, language: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {SUPPORTED_LANGUAGES.map((lang) => (
                          <SelectItem key={lang.code} value={lang.code}>
                            <span className="flex items-center gap-2">
                              <span>{lang.flag}</span>
                              <span>{lang.name}</span>
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>页面分割模式 (PSM)</Label>
                    <Select
                      value={settings.psm.toString()}
                      onValueChange={(value) => setSettings(prev => ({ ...prev, psm: parseInt(value) }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {PSM_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>DPI 设置</Label>
                    <Input
                      type="number"
                      value={settings.dpi}
                      onChange={(e) => setSettings(prev => ({ ...prev, dpi: parseInt(e.target.value) || 300 }))}
                      min="72"
                      max="600"
                    />
                    <p className="text-sm text-muted-foreground">
                      推荐值：300 (高质量) 或 150 (快速处理)
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>字符白名单</Label>
                    <Input
                      value={settings.whitelist}
                      onChange={(e) => setSettings(prev => ({ ...prev, whitelist: e.target.value }))}
                      placeholder="例如：0123456789"
                    />
                    <p className="text-sm text-muted-foreground">
                      仅识别指定字符，留空表示不限制
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>字符黑名单</Label>
                    <Input
                      value={settings.blacklist}
                      onChange={(e) => setSettings(prev => ({ ...prev, blacklist: e.target.value }))}
                      placeholder="例如：!@#$%"
                    />
                    <p className="text-sm text-muted-foreground">
                      排除指定字符
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">图像预处理</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center justify-between">
                    <Label>启用预处理</Label>
                    <Switch
                      checked={settings.preprocessImage}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, preprocessImage: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>增强对比度</Label>
                    <Switch
                      checked={settings.enhanceContrast}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enhanceContrast: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>去除噪声</Label>
                    <Switch
                      checked={settings.removeNoise}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, removeNoise: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>校正倾斜</Label>
                    <Switch
                      checked={settings.correctSkew}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, correctSkew: checked }))}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-6">
          {selectedResult ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>识别结果详情</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>完整文本</Label>
                    <Textarea
                      value={selectedResult.text}
                      readOnly
                      className="min-h-[200px]"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>整体置信度</Label>
                      <div className="text-2xl font-bold text-green-600">
                        {selectedResult.confidence.toFixed(1)}%
                      </div>
                    </div>
                    <div>
                      <Label>识别语言</Label>
                      <div className="text-lg">
                        {getLanguageName(selectedResult.language)}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>统计信息</Label>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="font-medium">单词数</div>
                        <div>{selectedResult.words.length}</div>
                      </div>
                      <div>
                        <div className="font-medium">行数</div>
                        <div>{selectedResult.lines.length}</div>
                      </div>
                      <div>
                        <div className="font-medium">块数</div>
                        <div>{selectedResult.blocks.length}</div>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      onClick={() => copyText(selectedResult.text)}
                      variant="outline"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      复制文本
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>可视化分析</CardTitle>
                </CardHeader>
                <CardContent>
                  <canvas
                    ref={canvasRef}
                    className="max-w-full border rounded"
                    style={{ maxHeight: '400px' }}
                  />
                  <p className="text-sm text-muted-foreground mt-2">
                    红色框显示识别的单词边界，数字表示置信度
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">选择识别结果进行分析</p>
                <p className="text-muted-foreground">
                  从识别结果页面选择一个文件来查看详细分析
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
