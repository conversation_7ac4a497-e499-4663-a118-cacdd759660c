'use client'

import { useState, useEffect, useRef } from 'react'
import { Sparkles } from 'lucide-react'
import { cn } from '@/lib/utils'
import { QuickAccessBar } from './QuickAccessBar'
import { SearchSection } from './SearchSection'
import { Tool } from '@/types/tool'
import { tools } from '@/lib/tools/registry'

interface HeroSectionProps {
  recentTools?: Tool[]
  favoriteTools?: Tool[]
  popularTools?: Tool[]
}

export function HeroSection({ 
  recentTools = [],
  favoriteTools = [],
  popularTools = []
}: HeroSectionProps) {
  
  // 快捷搜索关键词 - 更实用的功能类别
  const quickSearchTags = [
    '格式化',
    '压缩',
    '加密',
    '转换',
    '计算器',
    '二维码'
  ]

  // 动态数字效果
  const [toolCount, setToolCount] = useState(0)
  const actualToolCount = tools.length // 获取实际工具数量
  
  useEffect(() => {
    const interval = setInterval(() => {
      setToolCount(prev => {
        if (prev >= actualToolCount) return actualToolCount
        return prev + 2
      })
    }, 30)
    return () => clearInterval(interval)
  }, [actualToolCount])

  // 创建对 SearchSection 的引用
  const searchSectionRef = useRef<any>(null)

  // 处理热门标签点击
  const handleTagClick = (tag: string) => {
    // 跳转到搜索页面
    window.location.href = `/search?q=${encodeURIComponent(tag)}`
  }

  return (
    <>
      <section className="relative">
        {/* 动态渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600 opacity-90" />
        {/* CSS网格图案背景 */}
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '40px 40px',
            maskImage: 'linear-gradient(180deg, white, rgba(255,255,255,0))'
          }}
        />
        
        {/* 装饰性元素 */}
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob" />
        <div className="absolute top-40 right-10 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000" />
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000" />
        
        {/* 主要内容 */}
        <div className="relative z-10 px-4 py-24 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="text-center">
          {/* 标题 */}
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="w-8 h-8 text-yellow-300 animate-pulse" />
            <h1 className="text-5xl md:text-6xl font-bold text-white">
              <span className="text-yellow-300">{toolCount}+</span> 实用在线工具
            </h1>
            <Sparkles className="w-8 h-8 text-yellow-300 animate-pulse" />
          </div>
          
          <p className="text-xl md:text-2xl text-gray-100 mb-12">
            即用即走，完全免费，无需注册
          </p>

          {/* 智能下拉搜索框 */}
          <div className="max-w-3xl mx-auto mb-8">
            <SearchSection 
              placeholder="搜索工具名称、功能或关键词..."
              className="hero-search-section"
            />
          </div>

          {/* 快捷搜索标签 */}
          <div className="flex flex-wrap items-center justify-center gap-3">
            <span className="text-gray-200">快捷搜索：</span>
            {quickSearchTags.map((tag: string) => (
              <button
                key={tag}
                onClick={() => handleTagClick(tag)}
                className={cn(
                  "px-4 py-2 text-sm",
                  "bg-white/20 backdrop-blur-sm text-white",
                  "rounded-full border border-white/30",
                  "hover:bg-white/30 hover:scale-105",
                  "transition-all duration-200"
                )}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      </div>

        {/* 底部波浪装饰 */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg viewBox="0 0 1440 120" className="w-full h-20 fill-white dark:fill-gray-900">
            <path d="M0,64 C240,96 480,32 720,48 C960,64 1200,96 1440,64 L1440,120 L0,120 Z" />
          </svg>
        </div>
      </section>

      {/* 快速访问栏 */}
      {(recentTools.length > 0 || favoriteTools.length > 0 || popularTools.length > 0) && (
        <QuickAccessBar
          recentTools={recentTools}
          favoriteTools={favoriteTools}
          popularTools={popularTools}
        />
      )}
    </>
  )
}
