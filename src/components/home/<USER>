'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
  count: number;
  description: string;
}

const categories: Category[] = [
  {
    id: 'text',
    name: '文本处理',
    icon: '📝',
    color: 'from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20',
    count: 15,
    description: '文本编辑、转换、分析工具'
  },
  {
    id: 'dev',
    name: '开发工具',
    icon: '💻',
    color: 'from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20',
    count: 20,
    description: '代码格式化、编解码、生成工具'
  },
  {
    id: 'image',
    name: '图像处理',
    icon: '🎨',
    color: 'from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20',
    count: 10,
    description: '图片编辑、转换、优化工具'
  },
  {
    id: 'security',
    name: '加密安全',
    icon: '🔐',
    color: 'from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20',
    count: 10,
    description: '加密、解密、哈希生成工具'
  },
  {
    id: 'data',
    name: '数据分析',
    icon: '📊',
    color: 'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20',
    count: 10,
    description: '数据处理、分析、可视化工具'
  },
  {
    id: 'file',
    name: '文件转换',
    icon: '🔄',
    color: 'from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20',
    count: 10,
    description: '文件格式转换、处理工具'
  },
  {
    id: 'calc',
    name: '计算工具',
    icon: '🧮',
    color: 'from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20',
    count: 10,
    description: '各类计算器和换算工具'
  },
  {
    id: 'network',
    name: '网络工具',
    icon: '🌐',
    color: 'from-cyan-50 to-cyan-100 dark:from-cyan-900/20 dark:to-cyan-800/20',
    count: 5,
    description: '网络检测、查询工具'
  }
];

export function CategorySection() {
  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold">探索工具分类</h2>
          <Link 
            href="/tools" 
            className="text-sm text-muted-foreground hover:text-primary flex items-center gap-1 transition-colors"
          >
            查看全部
            <ArrowRight className="h-4 w-4" />
          </Link>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {categories.map((category) => (
            <Link key={category.id} href={`/tools/category/${category.id}`}>
              <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer group">
                <CardContent className="p-6">
                  <div className={cn(
                    "w-16 h-16 rounded-lg flex items-center justify-center mb-4 bg-gradient-to-br transition-all",
                    category.color,
                    "group-hover:scale-110"
                  )}>
                    <span className="text-3xl">{category.icon}</span>
                  </div>
                  
                  <h3 className="font-semibold text-lg mb-1">{category.name}</h3>
                  <p className="text-sm text-muted-foreground mb-2">{category.count} 个工具</p>
                  <p className="text-xs text-muted-foreground line-clamp-2">
                    {category.description}
                  </p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
