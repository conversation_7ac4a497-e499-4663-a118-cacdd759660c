'use client'

import { useState } from 'react'
import { ChevronRight, ChevronDown } from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { toolCategories, categoryOrder } from '@/lib/constants/categories'
import { Tool } from '@/types/tool'
import { cn } from '@/lib/utils'

interface CategoryShowcaseSectionProps {
  tools: Tool[]
  onCategoryClick: (category: string) => void
}

export function CategoryShowcaseSection({ 
  tools, 
  onCategoryClick 
}: CategoryShowcaseSectionProps) {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([])

  // 计算每个分类的工具数量
  const categoryToolCounts = tools.reduce((acc, tool) => {
    acc[tool.category] = (acc[tool.category] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    )
  }

  return (
    <section className="py-12 px-4 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            工具分类
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            探索 {Object.keys(toolCategories).length} 个分类下的 {tools.length} 个实用工具
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {categoryOrder.map((categoryKey) => {
            const category = toolCategories[categoryKey]
            if (!category) return null

            const toolCount = categoryToolCounts[categoryKey] || 0
            const isExpanded = expandedCategories.includes(categoryKey)
            const Icon = category.icon
            const categoryTools = tools.filter(tool => tool.category === categoryKey)

            return (
              <Card 
                key={categoryKey}
                className="overflow-hidden hover:shadow-lg transition-all duration-200"
              >
                <div
                  className="p-6 cursor-pointer"
                  onClick={() => onCategoryClick(categoryKey)}
                >
                  <div className="flex items-start gap-4">
                    <div 
                      className="w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0"
                      style={{ 
                        backgroundColor: category.color + '20',
                        color: category.color 
                      }}
                    >
                      <Icon className="w-6 h-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100 mb-1">
                        {category.name.zh}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {category.description.zh}
                      </p>
                      <div className="flex items-center justify-between">
                        <span 
                          className="text-sm font-medium"
                          style={{ color: category.color }}
                        >
                          {toolCount} 个工具
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-auto p-1"
                          onClick={(e) => {
                            e.stopPropagation()
                            toggleCategory(categoryKey)
                          }}
                        >
                          {isExpanded ? (
                            <ChevronDown className="w-4 h-4" />
                          ) : (
                            <ChevronRight className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 展开的工具列表 */}
                {isExpanded && categoryTools.length > 0 && (
                  <div className="border-t dark:border-gray-700 px-6 py-4 bg-gray-50 dark:bg-gray-800/50">
                    <div className="space-y-2">
                      {categoryTools.slice(0, 5).map((tool) => {
                        const ToolIcon = tool.icon
                        return (
                          <div
                            key={tool.id}
                            className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 cursor-pointer"
                            onClick={() => window.location.href = tool.path}
                          >
                            <ToolIcon className="w-4 h-4 opacity-50" />
                            <span>{tool.name.zh}</span>
                            {tool.isNew && (
                              <span className="text-xs bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 px-1.5 py-0.5 rounded">
                                新
                              </span>
                            )}
                          </div>
                        )
                      })}
                      {categoryTools.length > 5 && (
                        <Button
                          variant="link"
                          size="sm"
                          className="h-auto p-0 text-sm"
                          style={{ color: category.color }}
                          onClick={() => onCategoryClick(categoryKey)}
                        >
                          查看全部 {categoryTools.length} 个工具 →
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}
