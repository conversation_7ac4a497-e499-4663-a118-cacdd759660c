'use client'

import { useState } from 'react'
import { Clock, Star, TrendingUp } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Tool } from '@/types/tool'
import { ToolCard } from './ToolCard'

interface QuickAccessBarProps {
  recentTools: Tool[]
  favoriteTools: Tool[]
  popularTools: Tool[]
}

type TabType = 'recent' | 'favorites' | 'popular'

export function QuickAccessBar({ 
  recentTools, 
  favoriteTools, 
  popularTools 
}: QuickAccessBarProps) {
  const [activeTab, setActiveTab] = useState<TabType>('recent')

  const tabs = [
    { id: 'recent' as TabType, label: '最近使用', icon: Clock, tools: recentTools },
    { id: 'favorites' as TabType, label: '我的收藏', icon: Star, tools: favoriteTools },
    { id: 'popular' as TabType, label: '热门工具', icon: TrendingUp, tools: popularTools },
  ]

  const activeTabData = tabs.find(tab => tab.id === activeTab)
  const displayTools = activeTabData?.tools.slice(0, 6) || []

  if (recentTools.length === 0 && favoriteTools.length === 0) {
    return null // 如果没有数据，不显示这个组件
  }

  return (
    <section className="py-8 px-4 bg-gray-50 dark:bg-gray-800/50">
      <div className="max-w-7xl mx-auto">
        {/* 标签切换 */}
        <div className="flex items-center justify-center gap-2 mb-6">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id
            const hasTools = tab.tools.length > 0

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                disabled={!hasTools}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                  isActive
                    ? "bg-blue-500 text-white shadow-md"
                    : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                )}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
                {hasTools && (
                  <span className="text-xs opacity-70">({tab.tools.length})</span>
                )}
              </button>
            )
          })}
        </div>

        {/* 工具展示 */}
        {displayTools.length > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {displayTools.map((tool) => (
              <ToolCard
                key={tool.id}
                tool={tool}
                variant="compact"
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {activeTab === 'recent' && '暂无最近使用的工具'}
            {activeTab === 'favorites' && '暂无收藏的工具'}
            {activeTab === 'popular' && '暂无热门工具'}
          </div>
        )}
      </div>
    </section>
  )
}
