'use client';

import { useState, useEffect, useRef } from 'react';
import { Search, X, Clock, TrendingUp, Command } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Tool } from '@/types/tool';
import { useHomeStore } from '@/stores/homeStore';
import { useSearch } from '@/hooks/useSearch';
import { tools } from '@/lib/tools/registry';

interface SearchSectionProps {
  className?: string;
  autoFocus?: boolean;
  placeholder?: string;
}

export function SearchSection({ 
  className, 
  autoFocus = false,
  placeholder = "搜索工具..."
}: SearchSectionProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [keyboardActivated, setKeyboardActivated] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // 检测操作系统
  const [isMac, setIsMac] = useState(false);
  
  useEffect(() => {
    setIsMac(navigator.platform.toUpperCase().indexOf('MAC') >= 0);
  }, []);
  
  const { searchHistory, addSearchHistory } = useHomeStore();

  // 使用新的搜索 Hook
  const {
    query,
    results,
    suggestions,
    isSearching,
    search,
    clearSearch
  } = useSearch({
    tools,
    debounceDelay: 300,
    maxSuggestions: 8
  });

  // 限制显示的结果数量
  const displayResults = results.slice(0, 8);

  // 点击外部关闭
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 键盘导航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < results.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > -1 ? prev - 1 : -1);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0 && results[selectedIndex]) {
        handleSelectTool(results[selectedIndex]);
      } else if (query.trim()) {
        handleSearch();
      }
    } else if (e.key === 'Escape') {
      setIsOpen(false);
      inputRef.current?.blur();
    }
  };

  const handleSelectTool = (tool: Tool) => {
    addSearchHistory(query);
    router.push(`/tools/${tool.id}`);
    setIsOpen(false);
    clearSearch();
  };

  const handleSearch = () => {
    if (query.trim()) {
      addSearchHistory(query);
      router.push(`/search?q=${encodeURIComponent(query)}`);
      setIsOpen(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    search(suggestion);
    inputRef.current?.focus();
  };

  // 快捷键支持
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        
        // 添加视觉反馈
        setKeyboardActivated(true);
        
        // 聚焦搜索框
        inputRef.current?.focus();
        setIsOpen(true);
        
        // 滚动到搜索框位置
        searchRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
        
        // 500ms后移除动画效果
        setTimeout(() => {
          setKeyboardActivated(false);
        }, 500);
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => document.removeEventListener('keydown', handleGlobalKeyDown);
  }, []);

  return (
    <div ref={searchRef} className={cn("relative w-full max-w-2xl mx-auto", className)}>
      {/* 搜索输入框 */}
      <motion.div 
        className="relative"
        animate={keyboardActivated ? { scale: 1.05 } : { scale: 1 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => search(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          autoFocus={autoFocus}
          className={cn(
            "w-full h-14 pl-12 pr-12 text-lg rounded-2xl",
            "bg-background border-2 border-border",
            "focus:outline-none focus:border-primary focus:ring-4 focus:ring-primary/10",
            "transition-all duration-200",
            "placeholder:text-muted-foreground"
          )}
        />
        
        {/* 快捷键提示 */}
        <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-2">
          {query && (
            <button
              onClick={clearSearch}
              className="p-1 hover:bg-muted rounded-md transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          )}
          <kbd className="hidden sm:inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-muted rounded">
            {isMac ? (
              <>
                <Command className="h-3 w-3" />
                <span>+K</span>
              </>
            ) : (
              <span>Ctrl+K</span>
            )}
          </kbd>
        </div>
      </motion.div>

      {/* 搜索下拉面板 */}
      <AnimatePresence>
        {isOpen && (query || searchHistory.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "absolute top-full left-0 right-0 mt-2 z-[100]",
              "bg-popover border-2 border-border rounded-xl shadow-2xl",
              "overflow-hidden max-h-96 overflow-y-auto"
            )}
          >
            {/* 搜索结果 */}
            {displayResults.length > 0 && (
              <div className="p-2">
                <div className="text-xs font-medium text-muted-foreground px-3 py-2 flex items-center justify-between">
                  <span>搜索结果</span>
                  {isSearching && <span className="text-xs">搜索中...</span>}
                </div>
                <div className="space-y-1">
                  {displayResults.map((tool, index) => (
                    <button
                      key={tool.id}
                      onClick={() => handleSelectTool(tool)}
                      onMouseEnter={() => setSelectedIndex(index)}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-2.5 rounded-lg",
                        "text-left transition-colors",
                        selectedIndex === index
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-muted"
                      )}
                    >
                      <tool.icon className="h-5 w-5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">{tool.name.zh}</div>
                        <div className={cn(
                          "text-xs truncate",
                          selectedIndex === index
                            ? "text-primary-foreground/80"
                            : "text-muted-foreground"
                        )}>
                          {tool.description.zh}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* 搜索建议 */}
            {suggestions.length > 0 && displayResults.length === 0 && (
              <div className="p-2">
                <div className="text-xs font-medium text-muted-foreground px-3 py-2">
                  搜索建议
                </div>
                <div className="space-y-1">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-muted text-left transition-colors"
                    >
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                      <span>{suggestion}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* 搜索历史 */}
            {!query && searchHistory.length > 0 && (
              <div className="p-2">
                <div className="text-xs font-medium text-muted-foreground px-3 py-2">
                  搜索历史
                </div>
                <div className="space-y-1">
                  {searchHistory.slice(0, 5).map((item, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(item)}
                      className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-muted text-left transition-colors"
                    >
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{item}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* 查看全部结果 */}
            {query && displayResults.length > 0 && (
              <div className="border-t border-border p-2">
                <button
                  onClick={handleSearch}
                  className="w-full px-3 py-2 text-sm text-center text-primary hover:bg-muted rounded-lg transition-colors"
                >
                  查看全部 &ldquo;{query}&rdquo; 的搜索结果
                </button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
