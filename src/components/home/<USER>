'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Sparkles, TrendingUp, Clock, RefreshCw } from 'lucide-react';
import { Tool } from '@/types/tool';
import { ToolCard } from './ToolCard';
import { recommendationService } from '@/lib/services/recommendationService';
import { tools } from '@/lib/tools/registry';
import { useTranslations } from '@/hooks/useTranslations';
import { cn } from '@/lib/utils';

interface RecommendationSectionProps {
  className?: string;
  currentToolId?: string;
}

export function RecommendationSection({ 
  className,
  currentToolId 
}: RecommendationSectionProps) {
  const [recommendations, setRecommendations] = useState<Tool[]>([]);
  const [popularTools, setPopularTools] = useState<Tool[]>([]);
  const [recentTools, setRecentTools] = useState<Tool[]>([]);
  const [activeTab, setActiveTab] = useState<'recommended' | 'popular' | 'recent'>('recommended');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 加载推荐数据
  const loadRecommendations = useCallback(() => {
    const personalRecommendations = recommendationService.getRecommendations(tools, currentToolId);
    const popular = recommendationService.getPopularTools(tools, 6);
    const recent = recommendationService.getRecentTools(tools, 6);

    setRecommendations(personalRecommendations);
    setPopularTools(popular);
    setRecentTools(recent);
  }, [currentToolId]);

  useEffect(() => {
    loadRecommendations();
  }, [loadRecommendations]);

  // 刷新推荐
  const handleRefresh = async () => {
    setIsRefreshing(true);
    
    // 模拟刷新动画
    await new Promise(resolve => setTimeout(resolve, 500));
    
    loadRecommendations();
    setIsRefreshing(false);
  };

  // 记录工具点击
  const handleToolClick = (tool: Tool) => {
    recommendationService.recordAction({
      toolId: tool.id,
      action: 'view',
      timestamp: Date.now()
    });
  };

  const tabs = [
    {
      id: 'recommended' as const,
      label: '为你推荐',
      icon: Sparkles,
      tools: recommendations
    },
    {
      id: 'popular' as const,
      label: '热门工具',
      icon: TrendingUp,
      tools: popularTools
    },
    {
      id: 'recent' as const,
      label: '最近使用',
      icon: Clock,
      tools: recentTools
    }
  ];

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <section className={cn("py-12", className)}>
      <div className="container mx-auto px-4">
        {/* 标题栏 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-8">
            {/* 标签切换 */}
            <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 rounded-md transition-all",
                    "text-sm font-medium",
                    activeTab === tab.id
                      ? "bg-background text-foreground shadow-sm"
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* 刷新按钮 */}
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className={cn(
              "p-2 rounded-lg transition-all",
              "hover:bg-muted text-muted-foreground hover:text-foreground",
              isRefreshing && "animate-spin"
            )}
          >
            <RefreshCw className="h-5 w-5" />
          </button>
        </div>

        {/* 工具展示 */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTabData?.tools.length === 0 ? (
            <div className="text-center py-16 text-muted-foreground">
              <activeTabData.icon className="h-12 w-12 mx-auto mb-4 opacity-20" />
              <p className="text-lg">
                {activeTab === 'recent' 
                  ? '暂无使用记录'
                  : '暂无推荐数据，使用更多工具后将获得个性化推荐'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activeTabData?.tools.map((tool, index) => (
                <motion.div
                  key={tool.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <ToolCard
                    tool={tool}
                    onClick={() => handleToolClick(tool)}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </section>
  );
}
