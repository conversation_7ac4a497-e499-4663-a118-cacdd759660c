'use client'

import { useMemo } from 'react'
import { Tool } from '@/types/tool'
import { ToolCard } from './ToolCard'

interface ToolGridSectionProps {
  tools: Tool[]
  searchQuery: string
  selectedCategories: string[]
}

export function ToolGridSection({ 
  tools, 
  searchQuery, 
  selectedCategories 
}: ToolGridSectionProps) {
  // 过滤工具
  const filteredTools = useMemo(() => {
    return tools.filter(tool => {
      // 搜索过滤
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        // 搜索中文名称和描述
        const matchesSearch = 
          tool.name.zh.toLowerCase().includes(query) ||
          tool.name.en.toLowerCase().includes(query) ||
          tool.description.zh.toLowerCase().includes(query) ||
          tool.description.en.toLowerCase().includes(query) ||
          tool.keywords.zh.some((k: string) => k.toLowerCase().includes(query)) ||
          tool.keywords.en.some((k: string) => k.toLowerCase().includes(query))
        
        if (!matchesSearch) return false
      }

      // 分类过滤
      if (selectedCategories.length > 0) {
        if (!selectedCategories.includes(tool.category)) {
          return false
        }
      }

      return true
    })
  }, [tools, searchQuery, selectedCategories])

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8 flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            所有工具
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            共 {filteredTools.length} 个工具
          </p>
        </div>

        {filteredTools.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">
              没有找到符合条件的工具
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredTools.map((tool) => (
              <ToolCard key={tool.id} tool={tool} />
            ))}
          </div>
        )}
      </div>
    </section>
  )
}
