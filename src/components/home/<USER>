'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tool } from '@/types/tool';
import { getIconByName } from '@/lib/utils/icons';
import { cn } from '@/lib/utils';

interface ToolCardProps {
  tool: Tool;
  searchQuery?: string;
  onClick?: () => void;
  locale?: string;
  variant?: 'default' | 'compact';
}

// 高亮搜索关键词
function highlightText(text: string, query?: string): React.ReactNode {
  if (!query || !text) return text;

  const parts = text.split(new RegExp(`(${query})`, 'gi'));
  return parts.map((part, index) => 
    part.toLowerCase() === query.toLowerCase() ? (
      <span key={index} className="bg-yellow-200 dark:bg-yellow-800 font-semibold">
        {part}
      </span>
    ) : (
      part
    )
  );
}

export function ToolCard({ tool, searchQuery, onClick, locale = 'zh', variant = 'default' }: ToolCardProps) {
  const Icon = typeof tool.icon === 'string' ? getIconByName(tool.icon) : tool.icon;
  const isNew = tool.isNew;
  
  // 获取当前语言的名称和描述
  const name = typeof tool.name === 'string' ? tool.name : tool.name[locale as keyof typeof tool.name] || tool.name.zh;
  const description = typeof tool.description === 'string' ? tool.description : tool.description[locale as keyof typeof tool.description] || tool.description.zh;

  if (variant === 'compact') {
    return (
      <Link href={`/tools/${tool.id}`} onClick={onClick}>
        <Card className="h-full transition-all duration-200 hover:shadow-md hover:scale-105 cursor-pointer group">
          <CardContent className="p-4">
            <div className="flex flex-col items-center text-center space-y-2">
              <div className={cn(
                "p-2 rounded-lg transition-colors",
                "bg-gradient-to-br from-blue-50 to-blue-100",
                "dark:from-blue-900/20 dark:to-blue-800/20",
                "group-hover:from-blue-100 group-hover:to-blue-200",
                "dark:group-hover:from-blue-800/30 dark:group-hover:to-blue-700/30"
              )}>
                <Icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              
              <h3 className="font-medium text-sm line-clamp-1">
                {name}
              </h3>
              
              {isNew && (
                <Badge variant="default" className="bg-green-500 hover:bg-green-600 text-xs">
                  新
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </Link>
    );
  }

  return (
    <Link href={`/tools/${tool.id}`} onClick={onClick}>
      <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer group">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className={cn(
              "p-3 rounded-lg transition-colors",
              "bg-gradient-to-br from-blue-50 to-blue-100",
              "dark:from-blue-900/20 dark:to-blue-800/20",
              "group-hover:from-blue-100 group-hover:to-blue-200",
              "dark:group-hover:from-blue-800/30 dark:group-hover:to-blue-700/30"
            )}>
              <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            {isNew && (
              <Badge variant="default" className="bg-green-500 hover:bg-green-600">
                新
              </Badge>
            )}
          </div>
          
          <h3 className="font-semibold text-lg mb-2 line-clamp-1">
            {highlightText(name, searchQuery)}
          </h3>
          
          <p className="text-sm text-muted-foreground line-clamp-2 mb-4">
            {highlightText(description, searchQuery)}
          </p>
          
          <div className="flex items-center justify-between">
            <Badge variant="secondary" className="text-xs">
              {tool.category}
            </Badge>
            <span className="text-xs text-muted-foreground">
              点击使用 →
            </span>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
