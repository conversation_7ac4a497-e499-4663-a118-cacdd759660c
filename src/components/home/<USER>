'use client'

import Link from 'next/link'
import { Clock, Star, TrendingUp } from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tool } from '@/types/tool'
import { toolCategories } from '@/lib/constants/categories'
import { cn } from '@/lib/utils'

interface QuickAccessSectionProps {
  recentTools: Tool[]
  favoriteTools: Tool[]
  popularTools: Tool[]
}

export function QuickAccessSection({ 
  recentTools, 
  favoriteTools, 
  popularTools 
}: QuickAccessSectionProps) {
  
  // 模拟使用次数数据（实际项目中应该从API获取）
  const getToolUsage = (toolId: string): number => {
    const usageMap: Record<string, number> = {
      'base64': 15234,
      'json-formatter': 12876,
      'md5-generator': 10543,
      'qr-code-generator': 9876,
      'image-compressor': 8765,
      'word-count': 7654,
      'url-encoder': 6543,
      'password-generator': 5432,
      'color-picker': 4321,
      'timestamp-converter': 3210
    }
    return usageMap[toolId] || Math.floor(Math.random() * 1000) + 500
  }

  const renderToolCard = (tool: Tool, index: number, section: 'recent' | 'favorite' | 'popular') => {
    const Icon = tool.icon
    const category = toolCategories[tool.category]
    const usage = getToolUsage(tool.id)
    
    return (
      <Link
        key={tool.id}
        href={tool.path}
        className="block"
      >
        <Card className={cn(
          "p-4 hover:shadow-md transition-all duration-200 hover:scale-105 group relative overflow-hidden",
          section === 'popular' && index < 3 && "border-2",
          section === 'popular' && index === 0 && "border-yellow-500/50 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20",
          section === 'popular' && index === 1 && "border-gray-400/50 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20",
          section === 'popular' && index === 2 && "border-orange-500/50 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20"
        )}>
          {/* 排名标识 - 仅热门工具显示 */}
          {section === 'popular' && index < 3 && (
            <div className="absolute top-2 right-2 z-10">
              <div className={cn(
                "w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold",
                index === 0 && "bg-yellow-500 text-white",
                index === 1 && "bg-gray-400 text-white", 
                index === 2 && "bg-orange-600 text-white"
              )}>
                {index + 1}
              </div>
            </div>
          )}
          
          <div className="flex items-center gap-3">
            <div 
              className={cn(
                "w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200",
                section === 'popular' && index < 3 ? "group-hover:scale-110" : ""
              )}
              style={{ 
                backgroundColor: category?.color + '20',
                color: category?.color 
              }}
            >
              <Icon className="w-5 h-5" />
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">
                {tool.name.zh}
              </h4>
              <div className="flex items-center justify-between">
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {category?.name.zh}
                </p>
                {section === 'popular' && (
                  <span className="text-xs text-gray-400 flex items-center gap-1">
                    <TrendingUp className="w-3 h-3" />
                    {usage.toLocaleString()}
                  </span>
                )}
              </div>
              {/* 工具描述 - 仅热门工具前3名显示 */}
              {section === 'popular' && index < 3 && tool.description && (
                <p className="text-xs text-gray-600 dark:text-gray-300 mt-1 line-clamp-1">
                  {tool.description.zh}
                </p>
              )}
            </div>
            {(section === 'recent' || section === 'favorite') && index < 3 && (
              <span className="text-xs font-bold text-gray-400">
                #{index + 1}
              </span>
            )}
          </div>
        </Card>
      </Link>
    )
  }

  const sections = [
    {
      title: '最近使用',
      icon: Clock,
      tools: recentTools,
      emptyMessage: '暂无最近使用的工具',
      color: '#3B82F6', // 蓝色
      type: 'recent' as const
    },
    {
      title: '我的收藏', 
      icon: Star,
      tools: favoriteTools,
      emptyMessage: '暂无收藏的工具',
      color: '#F59E0B', // 橙色
      type: 'favorite' as const
    },
    {
      title: '热门工具',
      icon: TrendingUp,
      tools: popularTools,
      emptyMessage: '暂无热门工具',
      color: '#10B981', // 绿色
      type: 'popular' as const
    },
  ]

  return (
    <section className="py-12 px-4 bg-muted/30">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {sections.map((section) => {
            const SectionIcon = section.icon
            
            return (
              <div key={section.title}>
                <div className="flex items-center gap-2 mb-4">
                  <SectionIcon 
                    className="w-5 h-5" 
                    style={{ color: section.color }}
                  />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {section.title}
                  </h3>
                  {section.type === 'popular' && section.tools.length > 0 && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      (前{Math.min(section.tools.length, 5)}名)
                    </span>
                  )}
                </div>
                
                <div className="space-y-3">
                  {section.tools.length > 0 ? (
                    <>
                      {section.tools.slice(0, 5).map((tool, index) => 
                        renderToolCard(tool, index, section.type)
                      )}
                      {section.tools.length > 5 && (
                        <Button 
                          variant="ghost" 
                          className="w-full text-sm hover:bg-muted"
                          style={{ color: section.color }}
                        >
                          查看更多 ({section.tools.length - 5}+)
                        </Button>
                      )}
                    </>
                  ) : (
                    <Card className="p-8 text-center border-dashed">
                      <SectionIcon 
                        className="w-8 h-8 mx-auto mb-2 opacity-50"
                        style={{ color: section.color }}
                      />
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {section.emptyMessage}
                      </p>
                    </Card>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
