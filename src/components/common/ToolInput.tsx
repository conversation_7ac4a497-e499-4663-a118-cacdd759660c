'use client'

import React, { forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { Copy, Upload, X } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'

interface ToolInputProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  helperText?: string
  showClear?: boolean
  showCopy?: boolean
  onClear?: () => void
  onCopy?: () => void
}

/**
 * 工具输入组件
 * 提供统一的输入框样式和功能
 */
export const ToolInput = forwardRef<HTMLTextAreaElement, ToolInputProps>(
  ({ 
    label, 
    error, 
    helperText, 
    showClear = true,
    showCopy = false,
    onClear,
    onCopy,
    className,
    ...props 
  }, ref) => {
    const { t } = useTranslation({ namespace: 'common' })
    const hasValue = props.value && props.value.toString().length > 0

    const handleClear = () => {
      onClear?.()
    }

    const handleCopy = async () => {
      if (props.value) {
        try {
          await navigator.clipboard.writeText(props.value.toString())
          onCopy?.()
        } catch (err) {
          console.error('复制失败', err)
        }
      }
    }

    return (
      <div className="space-y-2">
        {label && (
          <label className="text-sm font-medium">
            {label}
          </label>
        )}
        
        <div className="relative">
          <textarea
            ref={ref}
            className={cn(
              'w-full min-h-[120px] p-3 pr-12 rounded-lg border bg-background resize-y',
              'focus:outline-none focus:ring-2 focus:ring-primary',
              error && 'border-destructive focus:ring-destructive',
              className
            )}
            {...props}
          />
          
          {/* 操作按钮 */}
          {hasValue && (
            <div className="absolute right-2 top-2 flex gap-1">
              {showCopy && (
                <button
                  type="button"
                  onClick={handleCopy}
                  className="p-1.5 hover:bg-accent rounded transition-colors"
                  title={t('copy') || '复制'}
                >
                  <Copy className="w-4 h-4" />
                </button>
              )}
              
              {showClear && (
                <button
                  type="button"
                  onClick={handleClear}
                  className="p-1.5 hover:bg-accent rounded transition-colors"
                  title={t('clear') || '清空'}
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          )}
        </div>
        
        {(error || helperText) && (
          <p className={cn(
            'text-sm',
            error ? 'text-destructive' : 'text-muted-foreground'
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    )
  }
)

ToolInput.displayName = 'ToolInput'

/**
 * 文件上传输入组件
 */
export function ToolFileInput({
  label,
  accept,
  multiple = false,
  onChange,
  className
}: {
  label?: string
  accept?: string
  multiple?: boolean
  onChange?: (files: FileList | null) => void
  className?: string
}) {
  const { t } = useTranslation({ namespace: 'common' })
  const inputRef = React.useRef<HTMLInputElement>(null)

  const handleClick = () => {
    inputRef.current?.click()
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.files)
  }

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <label className="text-sm font-medium">
          {label}
        </label>
      )}
      
      <div
        onClick={handleClick}
        className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:border-primary transition-colors"
      >
        <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
        <p className="text-sm font-medium">
          {t('upload_file') || '点击或拖拽文件到此处'}
        </p>
        <p className="text-xs text-muted-foreground mt-1">
          {accept ? `支持格式: ${accept}` : '支持所有文件格式'}
        </p>
      </div>
      
      <input
        ref={inputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleChange}
        className="hidden"
      />
    </div>
  )
}

/**
 * 工具输出组件
 * 用于展示工具处理结果
 */
export function ToolOutput({
  value,
  label,
  showCopy = true,
  height = 'auto',
  className
}: {
  value: string
  label?: string
  showCopy?: boolean
  height?: 'auto' | 'sm' | 'md' | 'lg'
  className?: string
}) {
  const { t } = useTranslation({ namespace: 'common' })
  const [copied, setCopied] = React.useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(value)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败', err)
    }
  }

  const heightClasses = {
    auto: 'min-h-[120px]',
    sm: 'h-[120px]',
    md: 'h-[200px]',
    lg: 'h-[300px]'
  }

  return (
    <div className="space-y-2">
      {label && (
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">
            {label}
          </label>
          {showCopy && value && (
            <button
              onClick={handleCopy}
              className="text-sm text-primary hover:underline flex items-center gap-1"
            >
              <Copy className="w-3 h-3" />
              {copied ? (t('copied') || '已复制') : (t('copy') || '复制')}
            </button>
          )}
        </div>
      )}
      
      <div
        className={cn(
          'w-full p-3 rounded-lg border bg-muted/50 overflow-auto',
          heightClasses[height],
          className
        )}
      >
        <pre className="font-mono text-sm whitespace-pre-wrap break-all">
          {value || (
            <span className="text-muted-foreground">
              {t('no_output') || '暂无输出'}
            </span>
          )}
        </pre>
      </div>
    </div>
  )
}
