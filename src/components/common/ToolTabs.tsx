'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface Tab {
  id: string
  label: string
  icon?: React.ReactNode
  disabled?: boolean
}

interface ToolTabsProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  className?: string
  variant?: 'default' | 'pills' | 'underline'
}

/**
 * 工具标签页组件
 * 用于在不同功能或视图之间切换
 */
export function ToolTabs({
  tabs,
  activeTab,
  onTabChange,
  className,
  variant = 'default'
}: ToolTabsProps) {
  const variants = {
    default: {
      container: 'border-b',
      tab: 'px-4 py-2 -mb-px border-b-2 transition-colors',
      active: 'border-primary text-primary',
      inactive: 'border-transparent hover:text-primary hover:border-gray-300'
    },
    pills: {
      container: 'bg-muted p-1 rounded-lg',
      tab: 'px-4 py-2 rounded-md transition-all',
      active: 'bg-background text-primary shadow-sm',
      inactive: 'hover:bg-background/50'
    },
    underline: {
      container: '',
      tab: 'px-4 py-2 relative transition-colors',
      active: 'text-primary after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-primary',
      inactive: 'hover:text-primary'
    }
  }

  const styles = variants[variant]

  return (
    <div className={cn('flex items-center gap-1', styles.container, className)}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => !tab.disabled && onTabChange(tab.id)}
          disabled={tab.disabled}
          className={cn(
            'inline-flex items-center gap-2 font-medium text-sm',
            'focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            styles.tab,
            activeTab === tab.id ? styles.active : styles.inactive
          )}
        >
          {tab.icon}
          {tab.label}
        </button>
      ))}
    </div>
  )
}

interface TabPanelProps {
  children: React.ReactNode
  tabId: string
  activeTab: string
  className?: string
}

/**
 * 标签页内容面板
 */
export function TabPanel({
  children,
  tabId,
  activeTab,
  className
}: TabPanelProps) {
  if (tabId !== activeTab) return null

  return (
    <div
      role="tabpanel"
      className={cn('animate-in fade-in-0 slide-in-from-bottom-1', className)}
    >
      {children}
    </div>
  )
}

/**
 * 垂直标签页组件
 */
export function VerticalTabs({
  tabs,
  activeTab,
  onTabChange,
  className
}: ToolTabsProps) {
  return (
    <div className={cn('flex flex-col gap-1', className)}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => !tab.disabled && onTabChange(tab.id)}
          disabled={tab.disabled}
          className={cn(
            'flex items-center gap-3 w-full px-4 py-2 text-left rounded-lg transition-colors',
            'focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            activeTab === tab.id
              ? 'bg-primary text-primary-foreground'
              : 'hover:bg-accent'
          )}
        >
          {tab.icon}
          <span className="font-medium text-sm">{tab.label}</span>
        </button>
      ))}
    </div>
  )
}

/**
 * 标签页容器组件
 * 包含标签和内容区域
 */
export function TabsContainer({
  tabs,
  defaultTab,
  children,
  className,
  variant = 'default',
  orientation = 'horizontal'
}: {
  tabs: Tab[]
  defaultTab?: string
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'pills' | 'underline'
  orientation?: 'horizontal' | 'vertical'
}) {
  const [activeTab, setActiveTab] = React.useState(
    defaultTab || tabs[0]?.id || ''
  )

  if (orientation === 'vertical') {
    return (
      <div className={cn('flex gap-6', className)}>
        <VerticalTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
        <div className="flex-1">{children}</div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      <ToolTabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        variant={variant}
      />
      <div>{children}</div>
    </div>
  )
}

/**
 * 简单的标签切换组件
 * 用于小型切换场景
 */
export function ToggleTabs({
  options,
  value,
  onChange,
  className
}: {
  options: Array<{ value: string; label: string }>
  value: string
  onChange: (value: string) => void
  className?: string
}) {
  return (
    <div className={cn('inline-flex bg-muted p-1 rounded-lg', className)}>
      {options.map((option) => (
        <button
          key={option.value}
          onClick={() => onChange(option.value)}
          className={cn(
            'px-3 py-1.5 text-sm font-medium rounded-md transition-all',
            value === option.value
              ? 'bg-background text-primary shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          )}
        >
          {option.label}
        </button>
      ))}
    </div>
  )
}
