'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { 
  Play, 
  RotateCcw, 
  Download, 
  Upload, 
  Settings,
  Copy,
  Trash2,
  Save
} from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'

interface ToolActionsProps {
  children?: React.ReactNode
  className?: string
  position?: 'left' | 'center' | 'right' | 'between'
}

/**
 * 工具操作按钮容器
 * 提供统一的按钮布局和样式
 */
export function ToolActions({ 
  children, 
  className,
  position = 'center'
}: ToolActionsProps) {
  const positionClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between'
  }

  return (
    <div className={cn(
      'flex items-center gap-3',
      positionClasses[position],
      className
    )}>
      {children}
    </div>
  )
}

interface ActionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  icon?: React.ReactNode
  loading?: boolean
}

/**
 * 统一的操作按钮组件
 */
export function ActionButton({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  loading = false,
  disabled,
  className,
  ...props
}: ActionButtonProps) {
  const { t } = useTranslation({ namespace: 'common' })

  const variants = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
  }

  const sizes = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4',
    lg: 'h-12 px-6 text-lg'
  }

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center gap-2 rounded-md font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary',
        'disabled:pointer-events-none disabled:opacity-50',
        variants[variant],
        sizes[size],
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
      ) : icon}
      {children}
    </button>
  )
}

/**
 * 预设的常用操作按钮
 */
export function ExecuteButton(props: Omit<ActionButtonProps, 'icon'>) {
  const { t } = useTranslation({ namespace: 'common' })
  return (
    <ActionButton icon={<Play className="w-4 h-4" />} {...props}>
      {props.children || t('execute') || '执行'}
    </ActionButton>
  )
}

export function ResetButton(props: Omit<ActionButtonProps, 'icon' | 'variant'>) {
  const { t } = useTranslation({ namespace: 'common' })
  return (
    <ActionButton 
      icon={<RotateCcw className="w-4 h-4" />} 
      variant="outline"
      {...props}
    >
      {props.children || t('reset') || '重置'}
    </ActionButton>
  )
}

export function DownloadButton(props: Omit<ActionButtonProps, 'icon'>) {
  const { t } = useTranslation({ namespace: 'common' })
  return (
    <ActionButton icon={<Download className="w-4 h-4" />} {...props}>
      {props.children || t('download') || '下载'}
    </ActionButton>
  )
}

export function UploadButton(props: Omit<ActionButtonProps, 'icon'>) {
  const { t } = useTranslation({ namespace: 'common' })
  return (
    <ActionButton icon={<Upload className="w-4 h-4" />} {...props}>
      {props.children || t('upload') || '上传'}
    </ActionButton>
  )
}

export function CopyButton(props: Omit<ActionButtonProps, 'icon'>) {
  const { t } = useTranslation({ namespace: 'common' })
  const [copied, setCopied] = React.useState(false)

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    props.onClick?.(e)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <ActionButton 
      icon={<Copy className="w-4 h-4" />} 
      onClick={handleClick}
      {...props}
    >
      {copied ? (t('copied') || '已复制') : (props.children || t('copy') || '复制')}
    </ActionButton>
  )
}

export function DeleteButton(props: Omit<ActionButtonProps, 'icon' | 'variant'>) {
  const { t } = useTranslation({ namespace: 'common' })
  return (
    <ActionButton 
      icon={<Trash2 className="w-4 h-4" />} 
      variant="destructive"
      {...props}
    >
      {props.children || t('delete') || '删除'}
    </ActionButton>
  )
}

export function SaveButton(props: Omit<ActionButtonProps, 'icon'>) {
  const { t } = useTranslation({ namespace: 'common' })
  return (
    <ActionButton icon={<Save className="w-4 h-4" />} {...props}>
      {props.children || t('save') || '保存'}
    </ActionButton>
  )
}

export function SettingsButton(props: Omit<ActionButtonProps, 'icon'>) {
  const { t } = useTranslation({ namespace: 'common' })
  return (
    <ActionButton 
      icon={<Settings className="w-4 h-4" />} 
      variant="ghost"
      {...props}
    >
      {props.children || t('settings') || '设置'}
    </ActionButton>
  )
}

/**
 * 快捷操作栏
 * 用于工具顶部或底部的快捷操作
 */
export function QuickActions({
  onExecute,
  onReset,
  onCopy,
  executing = false,
  canCopy = false,
  className
}: {
  onExecute?: () => void
  onReset?: () => void
  onCopy?: () => void
  executing?: boolean
  canCopy?: boolean
  className?: string
}) {
  return (
    <ToolActions position="between" className={className}>
      <div className="flex gap-2">
        <ExecuteButton onClick={onExecute} loading={executing} />
        <ResetButton onClick={onReset} disabled={executing} />
      </div>
      {canCopy && (
        <CopyButton onClick={onCopy} variant="outline" size="sm" />
      )}
    </ToolActions>
  )
}
