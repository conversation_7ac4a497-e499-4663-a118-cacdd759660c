'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface ToolContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

/**
 * 工具容器组件
 * 为工具提供统一的容器样式和布局
 */
export function ToolContainer({
  children,
  className,
  maxWidth = 'xl',
  padding = 'md'
}: ToolContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  }

  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }

  return (
    <div
      className={cn(
        'w-full mx-auto',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
    >
      {children}
    </div>
  )
}

/**
 * 工具卡片容器
 * 提供卡片样式的容器
 */
export function ToolCard({
  children,
  className,
  hoverable = false
}: {
  children: React.ReactNode
  className?: string
  hoverable?: boolean
}) {
  return (
    <div
      className={cn(
        'bg-card rounded-lg border shadow-sm',
        hoverable && 'transition-all duration-200 hover:shadow-md hover:border-primary/50',
        className
      )}
    >
      {children}
    </div>
  )
}

/**
 * 工具网格容器
 * 用于展示多个工具项的网格布局
 */
export function ToolGrid({
  children,
  columns = 'auto',
  gap = 'md',
  className
}: {
  children: React.ReactNode
  columns?: 'auto' | 1 | 2 | 3 | 4 | 6
  gap?: 'sm' | 'md' | 'lg'
  className?: string
}) {
  const columnClasses = {
    auto: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    6: 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-6'
  }

  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-4',
    lg: 'gap-6'
  }

  return (
    <div
      className={cn(
        'grid',
        columnClasses[columns],
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  )
}

/**
 * 工具分组容器
 * 用于将相关内容分组展示
 */
export function ToolSection({
  title,
  description,
  children,
  className,
  action
}: {
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
  action?: React.ReactNode
}) {
  return (
    <section className={cn('space-y-4', className)}>
      {(title || description || action) && (
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1">
            {title && (
              <h2 className="text-2xl font-semibold">{title}</h2>
            )}
            {description && (
              <p className="mt-1 text-muted-foreground">{description}</p>
            )}
          </div>
          {action && (
            <div className="flex-shrink-0">{action}</div>
          )}
        </div>
      )}
      {children}
    </section>
  )
}

/**
 * 工具内容包装器
 * 提供内容的基础间距和样式
 */
export function ToolContent({
  children,
  className
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn('space-y-6', className)}>
      {children}
    </div>
  )
}
