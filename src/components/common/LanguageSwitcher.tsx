'use client'

import { useState, useRef, useEffect } from 'react'
import { useParams, useRouter, usePathname } from 'next/navigation'
import { ChevronDown, Globe } from 'lucide-react'
import { locales, type Locale } from '@/i18n/config'

const languageNames: Record<Locale, string> = {
  zh: '中文',
  en: 'English',
  ja: '日本語'
}

const languageIcons: Record<Locale, string> = {
  zh: '🇨🇳',
  en: '🇺🇸',
  ja: '🇯🇵'
}

export function LanguageSwitcher() {
  const params = useParams()
  const router = useRouter()
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  
  const currentLocale = (params.locale as Locale) || 'zh'

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // 获取切换语言后的路径
  const getLocalizedPath = (newLocale: Locale) => {
    // 替换路径中的语言部分
    const segments = pathname.split('/')
    segments[1] = newLocale
    return segments.join('/')
  }

  // 处理语言切换
  const handleLanguageChange = (newLocale: Locale) => {
    if (newLocale !== currentLocale) {
      // 设置 cookie
      document.cookie = `preferred-language=${newLocale};path=/;max-age=31536000`
      
      // 跳转到新语言路径
      const newPath = getLocalizedPath(newLocale)
      router.push(newPath)
    }
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        aria-label="选择语言"
        aria-expanded={isOpen}
      >
        <Globe className="h-4 w-4" />
        <span className="hidden sm:inline">{languageNames[currentLocale]}</span>
        <span className="inline sm:hidden">{languageIcons[currentLocale]}</span>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1" role="menu" aria-orientation="vertical">
            {locales.map((locale) => (
              <button
                key={locale}
                onClick={() => handleLanguageChange(locale)}
                className={`flex items-center gap-3 w-full px-4 py-2 text-sm text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                  locale === currentLocale
                    ? 'text-primary font-medium bg-gray-50 dark:bg-gray-700'
                    : 'text-gray-700 dark:text-gray-200'
                }`}
                role="menuitem"
              >
                <span className="text-lg">{languageIcons[locale]}</span>
                <span>{languageNames[locale]}</span>
                {locale === currentLocale && (
                  <span className="ml-auto text-primary">✓</span>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
