'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { ArrowLeft, Share2, Bookmark, Info } from 'lucide-react'
import Link from 'next/link'
import { useTranslation } from '@/hooks/useTranslation'

interface ToolHeaderProps {
  title: string
  description?: string
  icon?: React.ReactNode
  category?: string
  locale: string
  backUrl?: string
  showActions?: boolean
  className?: string
  breadcrumbs?: Array<{
    label: string
    href?: string
  }>
}

/**
 * 工具头部组件
 * 显示工具标题、描述、面包屑导航和操作按钮
 */
export function ToolHeader({
  title,
  description,
  icon,
  category,
  locale,
  backUrl,
  showActions = true,
  className,
  breadcrumbs
}: ToolHeaderProps) {
  const { t } = useTranslation({ namespace: 'common' })

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text: description,
          url: window.location.href
        })
      } catch (err) {
        console.log('分享失败', err)
      }
    } else {
      // 降级处理：复制链接
      navigator.clipboard.writeText(window.location.href)
      // TODO: 显示提示
    }
  }

  const handleBookmark = () => {
    // TODO: 实现收藏功能
    console.log('收藏工具')
  }

  return (
    <header className={cn('space-y-4', className)}>
      {/* 面包屑导航 */}
      {breadcrumbs && breadcrumbs.length > 0 && (
        <nav aria-label="Breadcrumb">
          <ol className="flex items-center gap-2 text-sm text-muted-foreground">
            {breadcrumbs.map((crumb, index) => (
              <li key={index} className="flex items-center gap-2">
                {index > 0 && <span>/</span>}
                {crumb.href ? (
                  <Link
                    href={crumb.href}
                    className="hover:text-foreground transition-colors"
                  >
                    {crumb.label}
                  </Link>
                ) : (
                  <span className="text-foreground">{crumb.label}</span>
                )}
              </li>
            ))}
          </ol>
        </nav>
      )}

      <div className="flex items-start justify-between gap-4">
        <div className="flex-1 space-y-2">
          {/* 标题行 */}
          <div className="flex items-center gap-3">
            {backUrl && (
              <Link
                href={backUrl}
                className="p-2 hover:bg-accent rounded-lg transition-colors"
                aria-label={t('back') || '返回'}
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
            )}
            
            {icon && (
              <div className="p-2 bg-primary/10 rounded-lg">
                {icon}
              </div>
            )}
            
            <h1 className="text-2xl font-bold">{title}</h1>
            
            {category && (
              <span className="px-2 py-1 text-xs bg-muted rounded-md">
                {category}
              </span>
            )}
          </div>

          {/* 描述 */}
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}
        </div>

        {/* 操作按钮 */}
        {showActions && (
          <div className="flex items-center gap-2">
            <button
              onClick={handleShare}
              className="p-2 hover:bg-accent rounded-lg transition-colors"
              aria-label={t('share') || '分享'}
              title={t('share') || '分享'}
            >
              <Share2 className="w-5 h-5" />
            </button>
            
            <button
              onClick={handleBookmark}
              className="p-2 hover:bg-accent rounded-lg transition-colors"
              aria-label={t('bookmark') || '收藏'}
              title={t('bookmark') || '收藏'}
            >
              <Bookmark className="w-5 h-5" />
            </button>
            
            <button
              className="p-2 hover:bg-accent rounded-lg transition-colors"
              aria-label={t('info') || '信息'}
              title={t('info') || '信息'}
            >
              <Info className="w-5 h-5" />
            </button>
          </div>
        )}
      </div>
    </header>
  )
}

/**
 * 简化版工具头部
 */
export function ToolHeaderSimple({
  title,
  icon,
  className
}: {
  title: string
  icon?: React.ReactNode
  className?: string
}) {
  return (
    <div className={cn('flex items-center gap-3', className)}>
      {icon && (
        <div className="p-2 bg-primary/10 rounded-lg">
          {icon}
        </div>
      )}
      <h2 className="text-xl font-semibold">{title}</h2>
    </div>
  )
}

/**
 * 工具标签组件
 */
export function ToolBadge({
  children,
  variant = 'default',
  className
}: {
  children: React.ReactNode
  variant?: 'default' | 'primary' | 'secondary' | 'outline'
  className?: string
}) {
  const variants = {
    default: 'bg-muted text-muted-foreground',
    primary: 'bg-primary text-primary-foreground',
    secondary: 'bg-secondary text-secondary-foreground',
    outline: 'border border-input bg-transparent'
  }

  return (
    <span
      className={cn(
        'inline-flex items-center px-2 py-0.5 text-xs rounded-md',
        variants[variant],
        className
      )}
    >
      {children}
    </span>
  )
}
