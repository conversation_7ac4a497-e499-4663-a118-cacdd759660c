import { LucideIcon } from 'lucide-react';
import * as Icons from 'lucide-react';

// 根据字符串名称获取对应的图标组件
export function getIconByName(iconName: string): LucideIcon {
  // 如果没有提供图标名称，返回默认图标
  if (!iconName) {
    return Icons.Package;
  }

  // 尝试从 lucide-react 中获取对应的图标
  const icon = (Icons as any)[iconName];
  
  // 如果找到了对应的图标，返回它
  if (icon) {
    return icon;
  }

  // 如果没有找到，返回默认图标
  return Icons.Package;
}

// 获取分类对应的图标
export function getCategoryIcon(category: string): LucideIcon {
  const categoryIcons: Record<string, LucideIcon> = {
    '文本处理': Icons.FileText,
    '图像处理': Icons.Image,
    '开发工具': Icons.Code,
    '加密解密': Icons.Lock,
    '转换工具': Icons.RefreshCw,
    '计算器': Icons.Calculator,
    '生成器': Icons.Sparkles,
    '格式化': Icons.FileCode,
    '验证器': Icons.CheckCircle,
    '生产力工具': Icons.Zap,
    '生活助手': Icons.Heart,
    '网络工具': Icons.Globe,
    '其他工具': Icons.Package,
  };

  return categoryIcons[category] || Icons.Package;
}
