import Fuse from 'fuse.js';
import { Tool } from '@/types/tool';
import { tools } from '@/lib/tools/registry';

// Fuse.js 配置
const fuseOptions = {
  keys: [
    { name: 'name.zh', weight: 0.4 },
    { name: 'name.en', weight: 0.3 },
    { name: 'description.zh', weight: 0.2 },
    { name: 'description.en', weight: 0.1 },
    { name: 'keywords.zh', weight: 0.2 },
    { name: 'keywords.en', weight: 0.1 },
    { name: 'category', weight: 0.1 }
  ],
  threshold: 0.3,
  includeScore: true,
  includeMatches: true,
  minMatchCharLength: 2,
  shouldSort: true,
  findAllMatches: true,
  ignoreLocation: true,
  useExtendedSearch: true
};

// 创建搜索索引
const fuse = new Fuse(tools, fuseOptions);

// 搜索工具
export function searchTools(query: string): Tool[] {
  if (!query || query.trim() === '') {
    return [];
  }

  // 使用 Fuse.js 进行模糊搜索
  const results = fuse.search(query);
  
  // 返回搜索结果，按相关度排序
  return results.map(result => result.item);
}

// 获取搜索建议
export function getSearchSuggestions(query: string, limit: number = 5): string[] {
  if (!query || query.trim() === '') {
    return [];
  }

  const results = searchTools(query);
  const suggestions = new Set<string>();

  // 从搜索结果中提取建议
  results.slice(0, limit * 2).forEach(tool => {
    // 添加工具名称
    suggestions.add(tool.name.zh);
    if (tool.name.en && tool.name.en.toLowerCase().includes(query.toLowerCase())) {
      suggestions.add(tool.name.en);
    }
    
    // 添加匹配的关键词
    tool.keywords?.zh.forEach((keyword: string) => {
      if (keyword.toLowerCase().includes(query.toLowerCase())) {
        suggestions.add(keyword);
      }
    });
  });

  // 如果建议不够，添加分类名称
  if (suggestions.size < limit) {
    const categories = ['文本处理', '开发工具', '图像处理', '加密安全', '转换工具', '计算器', '生活助手', '网络工具'];
    categories.forEach(cat => {
      if (cat.includes(query)) {
        suggestions.add(cat);
      }
    });
  }

  return Array.from(suggestions).slice(0, limit);
}

// 按分类筛选工具
export function filterToolsByCategory(tools: Tool[], categories: string[]): Tool[] {
  if (!categories || categories.length === 0) {
    return tools;
  }
  
  return tools.filter(tool => categories.includes(tool.category));
}

// 获取热门工具
export function getPopularTools(limit: number = 6): Tool[] {
  // 暂时返回前几个工具作为热门工具
  // 未来可以基于实际使用数据
  return tools.slice(0, limit);
}

// 获取最近使用的工具
export function getRecentTools(toolIds: string[], limit: number = 5): Tool[] {
  return toolIds
    .slice(0, limit)
    .map(id => tools.find(tool => tool.id === id))
    .filter((tool): tool is Tool => tool !== undefined);
}

// 获取收藏的工具
export function getFavoriteTools(toolIds: string[]): Tool[] {
  return toolIds
    .map(id => tools.find(tool => tool.id === id))
    .filter((tool): tool is Tool => tool !== undefined);
}

// 高亮搜索匹配文本
export function highlightMatches(text: string, query: string): string {
  if (!query) return text;
  
  const regex = new RegExp(`(${query})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

// 拼音搜索支持（简单实现）
const pinyinMap: Record<string, string[]> = {
  '字': ['zi'],
  '数': ['shu'],
  '统': ['tong'],
  '计': ['ji'],
  '文': ['wen'],
  '本': ['ben'],
  '处': ['chu'],
  '理': ['li'],
  '图': ['tu'],
  '片': ['pian'],
  '压': ['ya'],
  '缩': ['suo'],
  '加': ['jia'],
  '密': ['mi'],
  '解': ['jie'],
  // ... 可以扩展更多
};

// 检查是否匹配拼音
export function matchPinyin(text: string, query: string): boolean {
  const lowerQuery = query.toLowerCase();
  
  // 检查每个字符的拼音
  for (const char of text) {
    const pinyins = pinyinMap[char];
    if (pinyins && pinyins.some(py => py.startsWith(lowerQuery))) {
      return true;
    }
  }
  
  return false;
}

// 综合搜索（包括拼音）
export function comprehensiveSearch(query: string): Tool[] {
  if (!query || query.trim() === '') {
    return [];
  }

  // 先进行常规搜索
  let results = searchTools(query);
  
  // 如果结果较少，尝试拼音搜索
  if (results.length < 5) {
    const pinyinResults = tools.filter(tool => 
      matchPinyin(tool.name.zh, query) || 
      matchPinyin(tool.description.zh, query)
    );
    
    // 合并结果，去重
    const resultIds = new Set(results.map(r => r.id));
    pinyinResults.forEach(tool => {
      if (!resultIds.has(tool.id)) {
        results.push(tool);
      }
    });
  }
  
  return results;
}
