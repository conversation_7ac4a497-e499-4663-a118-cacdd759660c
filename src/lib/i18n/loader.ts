import { type Locale } from '@/i18n/config'

// 翻译缓存
const translationCache = new Map<string, any>()

// 翻译文件映射
const translationModules: Record<string, () => Promise<any>> = {
  // Common translations
  'zh/common': () => import('../../../public/locales/zh/common.json'),
  'en/common': () => import('../../../public/locales/en/common.json'),
  'ja/common': () => import('../../../public/locales/ja/common.json'),
  
  // Navigation translations
  'zh/navigation': () => import('../../../public/locales/zh/navigation.json'),
  'en/navigation': () => import('../../../public/locales/en/navigation.json'),
  'ja/navigation': () => import('../../../public/locales/ja/navigation.json'),
  
  // Home translations
  'zh/home': () => import('../../../public/locales/zh/home.json'),
  'en/home': () => import('../../../public/locales/en/home.json'),
  'ja/home': () => import('../../../public/locales/ja/home.json'),
  
  // Tools common translations
  'zh/tools/_common': () => import('../../../public/locales/zh/tools/_common.json'),
  'en/tools/_common': () => import('../../../public/locales/en/tools/_common.json'),
  'ja/tools/_common': () => import('../../../public/locales/ja/tools/_common.json'),
  
  // Base64 tool translations
  'zh/tools/base64': () => import('../../../public/locales/zh/tools/base64.json'),
  'en/tools/base64': () => import('../../../public/locales/en/tools/base64.json'),
  'ja/tools/base64': () => import('../../../public/locales/ja/tools/base64.json'),
}

/**
 * 加载指定语言和命名空间的翻译
 * @param locale 语言代码
 * @param namespace 命名空间（文件名）
 * @returns 翻译内容
 */
export async function loadTranslations(
  locale: Locale,
  namespace: string = 'common'
): Promise<Record<string, any>> {
  const cacheKey = `${locale}/${namespace}`
  
  // 检查缓存
  if (translationCache.has(cacheKey)) {
    return translationCache.get(cacheKey)
  }

  try {
    // 获取对应的导入函数
    const importFn = translationModules[cacheKey]
    
    if (!importFn) {
      throw new Error(`No translation module found for ${cacheKey}`)
    }
    
    // 动态导入翻译文件
    const translationModule = await importFn()
    const translations = translationModule.default || translationModule
    
    // 缓存翻译内容
    translationCache.set(cacheKey, translations)
    
    return translations
  } catch (error) {
    console.error(`Failed to load translations for ${cacheKey}:`, error)
    
    // 尝试加载默认语言
    if (locale !== 'zh') {
      console.warn(`Falling back to default language for ${namespace}`)
      return loadTranslations('zh', namespace)
    }
    
    // 返回空对象避免错误
    return {}
  }
}

/**
 * 预加载多个命名空间的翻译
 * @param locale 语言代码
 * @param namespaces 命名空间数组
 */
export async function preloadTranslations(
  locale: Locale,
  namespaces: string[]
): Promise<void> {
  await Promise.all(
    namespaces.map(namespace => loadTranslations(locale, namespace))
  )
}

/**
 * 清除翻译缓存
 * @param locale 语言代码（可选）
 * @param namespace 命名空间（可选）
 */
export function clearTranslationCache(locale?: Locale, namespace?: string) {
  if (locale && namespace) {
    translationCache.delete(`${locale}/${namespace}`)
  } else if (locale) {
    // 清除指定语言的所有缓存
    const keys = Array.from(translationCache.keys())
    for (const key of keys) {
      if (key.startsWith(`${locale}/`)) {
        translationCache.delete(key)
      }
    }
  } else {
    // 清除所有缓存
    translationCache.clear()
  }
}

/**
 * 获取嵌套的翻译值
 * @param translations 翻译对象
 * @param key 键路径（支持点号分隔）
 * @returns 翻译值
 */
export function getNestedTranslation(
  translations: Record<string, any>,
  key: string
): string | undefined {
  const keys = key.split('.')
  let value: any = translations

  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k]
    } else {
      return undefined
    }
  }

  return typeof value === 'string' ? value : undefined
}

/**
 * 替换翻译中的占位符
 * @param translation 翻译文本
 * @param params 参数对象
 * @returns 替换后的文本
 */
export function replacePlaceholders(
  translation: string,
  params?: Record<string, string | number>
): string {
  if (!params) return translation

  return translation.replace(/{(\w+)}/g, (match, key) => {
    return params[key]?.toString() || match
  })
}

// 开发环境下支持热更新
if (process.env.NODE_ENV === 'development') {
  // 监听文件变化时清除缓存
  if (typeof window !== 'undefined') {
    ;(window as any).__clearTranslationCache = clearTranslationCache
  }
}
