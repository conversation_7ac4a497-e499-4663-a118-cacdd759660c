import { Tool, ToolCategory } from '@/types/tool'

// 用户行为记录
interface UserAction {
  toolId: string
  action: 'view' | 'use' | 'favorite' | 'search'
  timestamp: number
  duration?: number // 使用时长（毫秒）
}

// 工具使用统计
interface ToolStats {
  viewCount: number
  useCount: number
  favoriteCount: number
  lastUsed?: number
  totalDuration: number
  averageRating: number
}

// 推荐算法配置
interface RecommendationConfig {
  maxRecommendations: number
  recencyWeight: number
  popularityWeight: number
  personalWeight: number
  categoryWeight: number
}

const DEFAULT_CONFIG: RecommendationConfig = {
  maxRecommendations: 10,
  recencyWeight: 0.3,
  popularityWeight: 0.2,
  personalWeight: 0.4,
  categoryWeight: 0.1
}

export class RecommendationService {
  private userActions: UserAction[] = []
  private toolStats: Map<string, ToolStats> = new Map()
  private config: RecommendationConfig
  private cache: Map<string, { data: any, timestamp: number }> = new Map()
  private cacheExpiry = 5 * 60 * 1000 // 5分钟缓存

  constructor(config: Partial<RecommendationConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.loadFromStorage()
  }

  // 缓存获取方法
  private getCached<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data
    }
    return null
  }

  // 缓存设置方法
  private setCache<T>(key: string, data: T): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  // 记录用户行为
  recordAction(action: UserAction) {
    this.userActions.push(action)
    this.updateToolStats(action)
    this.saveToStorage()
    
    // 限制历史记录大小
    if (this.userActions.length > 1000) {
      this.userActions = this.userActions.slice(-800)
    }
  }

  // 更新工具统计
  private updateToolStats(action: UserAction) {
    const stats = this.toolStats.get(action.toolId) || {
      viewCount: 0,
      useCount: 0,
      favoriteCount: 0,
      totalDuration: 0,
      averageRating: 0
    }

    switch (action.action) {
      case 'view':
        stats.viewCount++
        break
      case 'use':
        stats.useCount++
        stats.lastUsed = action.timestamp
        if (action.duration) {
          stats.totalDuration += action.duration
        }
        break
      case 'favorite':
        stats.favoriteCount++
        break
    }

    this.toolStats.set(action.toolId, stats)
  }

  // 获取个性化推荐
  getRecommendations(tools: Tool[], currentToolId?: string): Tool[] {
    const scores = new Map<string, number>()

    // 为每个工具计算分数
    tools.forEach(tool => {
      if (tool.id === currentToolId) return // 排除当前工具

      let score = 0

      // 1. 基于使用历史的个人偏好分数
      const stats = this.toolStats.get(tool.id)
      if (stats) {
        const personalScore = this.calculatePersonalScore(stats)
        score += personalScore * this.config.personalWeight
      }

      // 2. 基于流行度的分数
      const popularityScore = this.calculatePopularityScore(tool)
      score += popularityScore * this.config.popularityWeight

      // 3. 基于时间的分数（最近使用）
      const recencyScore = this.calculateRecencyScore(tool.id)
      score += recencyScore * this.config.recencyWeight

      // 4. 基于分类相关性的分数
      if (currentToolId) {
        const categoryScore = this.calculateCategoryScore(tool, currentToolId, tools)
        score += categoryScore * this.config.categoryWeight
      }

      scores.set(tool.id, score)
    })

    // 按分数排序并返回推荐
    return tools
      .filter(tool => scores.has(tool.id))
      .sort((a, b) => (scores.get(b.id) || 0) - (scores.get(a.id) || 0))
      .slice(0, this.config.maxRecommendations)
  }

  // 计算个人偏好分数
  private calculatePersonalScore(stats: ToolStats): number {
    const viewScore = Math.min(stats.viewCount / 10, 1) * 0.2
    const useScore = Math.min(stats.useCount / 5, 1) * 0.5
    const favoriteScore = stats.favoriteCount > 0 ? 0.3 : 0
    
    return viewScore + useScore + favoriteScore
  }

  // 计算流行度分数
  private calculatePopularityScore(tool: Tool): number {
    // 这里可以基于全局统计数据
    // 目前使用模拟数据
    const popularTools = ['json-formatter', 'base64', 'timestamp-converter', 'uuid-generator']
    return popularTools.includes(tool.id) ? 0.8 : 0.3
  }

  // 计算时间相关性分数
  private calculateRecencyScore(toolId: string): number {
    const stats = this.toolStats.get(toolId)
    if (!stats || !stats.lastUsed) return 0

    const daysSinceLastUse = (Date.now() - stats.lastUsed) / (1000 * 60 * 60 * 24)
    
    if (daysSinceLastUse < 1) return 1
    if (daysSinceLastUse < 7) return 0.8
    if (daysSinceLastUse < 30) return 0.5
    if (daysSinceLastUse < 90) return 0.3
    
    return 0.1
  }

  // 计算分类相关性分数
  private calculateCategoryScore(tool: Tool, currentToolId: string, allTools: Tool[]): number {
    const currentTool = allTools.find(t => t.id === currentToolId)
    if (!currentTool) return 0

    // 同一分类得分更高
    if (tool.category === currentTool.category) return 1

    // 相关分类得分次之
    const relatedCategories: Record<ToolCategory, ToolCategory[]> = {
      'text': ['formatter', 'developer'],
      'developer': ['formatter', 'text', 'generator'],
      'generator': ['developer', 'crypto'],
      'crypto': ['developer', 'generator'],
      'converter': ['image', 'formatter'],
      'image': ['converter', 'lifestyle'],
      'calculator': ['converter', 'productivity'],
      'productivity': ['calculator', 'lifestyle'],
      'lifestyle': ['productivity', 'calculator'],
      'formatter': ['developer', 'text'],
      'validator': ['developer'],
      'network': ['developer'],
      'other': []
    }

    const related = relatedCategories[currentTool.category] || []
    return related.includes(tool.category) ? 0.5 : 0.2
  }

  // 获取热门工具
  getPopularTools(tools: Tool[], limit: number = 6): Tool[] {
    const cacheKey = `popular_${limit}`
    const cached = this.getCached<Tool[]>(cacheKey)
    if (cached) return cached

    // 如果没有用户数据，返回默认的热门工具
    const hasUserData = Array.from(this.toolStats.values()).some(stats => 
      stats.useCount > 0 || stats.viewCount > 0
    )

    if (!hasUserData) {
      // 默认热门工具ID列表
      const defaultPopularIds = [
        'json-formatter', 'base64', 'qrcode-generator', 
        'timestamp-converter', 'uuid-generator', 'password-generator',
        'md5-generator', 'unit-converter', 'color-picker'
      ]
      
      const result = defaultPopularIds
        .map(id => tools.find(tool => tool.id === id))
        .filter(Boolean)
        .slice(0, limit) as Tool[]
      
      this.setCache(cacheKey, result)
      return result
    }

    const toolsWithStats = tools.map(tool => ({
      tool,
      stats: this.toolStats.get(tool.id) || {
        viewCount: 0,
        useCount: 0,
        favoriteCount: 0,
        totalDuration: 0,
        averageRating: 0
      }
    }))

    const result = toolsWithStats
      .sort((a, b) => {
        const scoreA = a.stats.useCount * 3 + a.stats.viewCount
        const scoreB = b.stats.useCount * 3 + b.stats.viewCount
        return scoreB - scoreA
      })
      .slice(0, limit)
      .map(item => item.tool)

    this.setCache(cacheKey, result)
    return result
  }

  // 获取最近使用的工具
  getRecentTools(tools: Tool[], limit: number = 6): Tool[] {
    const cacheKey = `recent_${limit}`
    const cached = this.getCached<Tool[]>(cacheKey)
    if (cached) return cached

    const recentActions = this.userActions
      .filter(action => action.action === 'use')
      .reverse()

    const recentToolIds = new Set<string>()
    const recentTools: Tool[] = []

    for (const action of recentActions) {
      if (recentToolIds.has(action.toolId)) continue
      
      const tool = tools.find(t => t.id === action.toolId)
      if (tool) {
        recentToolIds.add(action.toolId)
        recentTools.push(tool)
        
        if (recentTools.length >= limit) break
      }
    }

    this.setCache(cacheKey, recentTools)
    return recentTools
  }

  // 存储到本地
  private saveToStorage() {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem('recommendation_actions', JSON.stringify(this.userActions))
      localStorage.setItem('recommendation_stats', JSON.stringify(Array.from(this.toolStats.entries())))
    } catch (error) {
      console.error('Failed to save recommendation data:', error)
    }
  }

  // 从本地加载
  private loadFromStorage() {
    if (typeof window === 'undefined') return

    try {
      const actionsData = localStorage.getItem('recommendation_actions')
      if (actionsData) {
        this.userActions = JSON.parse(actionsData)
      }

      const statsData = localStorage.getItem('recommendation_stats')
      if (statsData) {
        this.toolStats = new Map(JSON.parse(statsData))
      }
    } catch (error) {
      console.error('Failed to load recommendation data:', error)
    }
  }

  // 清除数据
  clearData() {
    this.userActions = []
    this.toolStats.clear()
    if (typeof window !== 'undefined') {
      localStorage.removeItem('recommendation_actions')
      localStorage.removeItem('recommendation_stats')
    }
  }
}

// 导出单例
export const recommendationService = new RecommendationService()
