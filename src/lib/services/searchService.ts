import Fuse, { IFuseOptions, FuseResult } from 'fuse.js'
import { Tool } from '@/types/tool'
import { pinyin } from 'pinyin-pro'

// Fuse.js 配置选项
const fuseOptions: IFuseOptions<Tool> = {
  keys: [
    { name: 'name.zh', weight: 0.4 },
    { name: 'name.en', weight: 0.3 },
    { name: 'description.zh', weight: 0.2 },
    { name: 'description.en', weight: 0.1 },
    { name: 'tags', weight: 0.1 },
    { name: 'category', weight: 0.1 }
  ],
  threshold: 0.3,
  includeScore: true,
  includeMatches: true,
  minMatchCharLength: 2,
  // 支持模糊匹配
  shouldSort: true,
  findAllMatches: false,
  location: 0,
  distance: 100,
  // 使用扩展搜索
  useExtendedSearch: true
}

export class SearchService {
  private fuse: Fuse<Tool>
  private tools: Tool[]
  private pinyinMap: Map<string, string[]> = new Map()

  constructor(tools: Tool[]) {
    this.tools = tools
    this.fuse = new Fuse(tools, fuseOptions)
    this.buildPinyinMap()
  }

  // 构建拼音映射
  private buildPinyinMap() {
    this.tools.forEach(tool => {
      // 为中文名称构建拼音
      const zhName = tool.name.zh
      const pinyinFull = pinyin(zhName, { type: 'array', toneType: 'none' }).join('')
      const pinyinFirst = pinyin(zhName, { type: 'array', toneType: 'none' })
        .map((p: string) => p[0])
        .join('')
      
      this.pinyinMap.set(tool.id, [pinyinFull, pinyinFirst])
    })
  }

  // 主搜索方法
  search(query: string): Tool[] {
    if (!query || query.trim() === '') {
      return this.tools
    }

    const trimmedQuery = query.trim().toLowerCase()
    
    // 1. 使用 Fuse.js 进行主搜索
    const fuseResults = this.fuse.search(trimmedQuery)
    
    // 2. 拼音搜索补充
    const pinyinResults = this.searchByPinyin(trimmedQuery)
    
    // 3. 合并结果并去重
    const resultMap = new Map<string, { tool: Tool; score: number }>()
    
    // 添加 Fuse.js 结果
    fuseResults.forEach(result => {
      resultMap.set(result.item.id, {
        tool: result.item,
        score: result.score || 0
      })
    })
    
    // 添加拼音搜索结果（如果不存在）
    pinyinResults.forEach(tool => {
      if (!resultMap.has(tool.id)) {
        resultMap.set(tool.id, {
          tool,
          score: 0.5 // 给拼音匹配一个固定分数
        })
      }
    })
    
    // 4. 按分数排序并返回
    return Array.from(resultMap.values())
      .sort((a, b) => a.score - b.score)
      .map(item => item.tool)
  }

  // 拼音搜索
  private searchByPinyin(query: string): Tool[] {
    const results: Tool[] = []
    
    this.tools.forEach(tool => {
      const pinyinVariants = this.pinyinMap.get(tool.id) || []
      
      // 检查是否匹配任何拼音变体
      const matches = pinyinVariants.some(variant => 
        variant.includes(query) || query.includes(variant)
      )
      
      if (matches) {
        results.push(tool)
      }
    })
    
    return results
  }

  // 获取搜索建议
  getSuggestions(query: string, limit: number = 5): string[] {
    if (!query || query.trim().length < 2) {
      return []
    }

    const results = this.search(query).slice(0, limit)
    return results.map(tool => tool.name.zh)
  }

  // 更新工具列表
  updateTools(tools: Tool[]) {
    this.tools = tools
    this.fuse = new Fuse(tools, fuseOptions)
    this.buildPinyinMap()
  }

  // 高亮搜索结果
  highlightResults(results: FuseResult<Tool>[]): Array<{
    tool: Tool
    highlights: Record<string, string>
  }> {
    return results.map(result => {
      const highlights: Record<string, string> = {}
      
      result.matches?.forEach((match: any) => {
        if (match.key && match.value) {
          let highlightedText = match.value
          
          // 按匹配索引倒序排序，避免索引偏移
          const indices = [...(match.indices || [])]
            .sort((a, b) => b[0] - a[0])
          
          indices.forEach(([start, end]) => {
            highlightedText = 
              highlightedText.slice(0, start) +
              '<mark>' +
              highlightedText.slice(start, end + 1) +
              '</mark>' +
              highlightedText.slice(end + 1)
          })
          
          highlights[match.key] = highlightedText
        }
      })
      
      return {
        tool: result.item,
        highlights
      }
    })
  }
}

// 导出单例
let searchServiceInstance: SearchService | null = null

export function getSearchService(tools: Tool[]): SearchService {
  if (!searchServiceInstance) {
    searchServiceInstance = new SearchService(tools)
  } else {
    searchServiceInstance.updateTools(tools)
  }
  return searchServiceInstance
}
