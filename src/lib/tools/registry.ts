import { Tool } from '@/types/tool'
import { Code, FileText, Code2, Hash, QrCode, Image, Calculator, Timer, BarChart, Globe, Fingerprint, Shield, Palette, Clock, Regex, FileDiff, Edit3, FileSpreadsheet, Link2, Heart, DollarSign, BarChart3, LineChart, FileCode, Send, ImageIcon, Lock, Database, Calendar, Volume2, Binary, CheckSquare, BookOpen, Table, Type, Search, ArrowUpDown, Filter, FileCheck, FileType, FileSearch, Droplets, RotateCw, Sliders, Grid, Images, FileImage, Camera, Sparkles, GitCommit, Package, Key, Percent, TrendingUp, Apple, Wifi, CloudSun, Book, FileDown, Receipt, Target, Film, TimerIcon, Bell, Gift, Plane, FileText as FileTextIcon, List, Highlighter, Languages, MessageSquare, Eye, Layers, Square, Variable, StretchHorizontal, Maximize, Minimize, Video, Layout, Scissors } from 'lucide-react'
import dynamic from 'next/dynamic'

// 动态导入工具组件
const Base64Tool = dynamic(() => import('@/components/tools/wrappers/Base64ToolWrapper'))
const WordCountTool = dynamic(() => import('@/components/tools/WordCountTool'))
const JsonFormatterTool = dynamic(() => import('@/components/tools/JsonFormatterTool'))
const MD5GeneratorTool = dynamic(() => import('@/components/tools/MD5GeneratorTool'))
const QRCodeGeneratorTool = dynamic(() => import('@/components/tools/QRCodeGeneratorTool'))
const ImageCompressorTool = dynamic(() => import('@/components/tools/ImageCompressorTool'))
const UnitConverterTool = dynamic(() => import('@/components/tools/UnitConverterTool'))
const PomodoroTimerTool = dynamic(() => import('@/components/tools/PomodoroTimerTool'))
const ChartGeneratorTool = dynamic(() => import('@/components/tools/ChartGeneratorTool'))
const IPLookupTool = dynamic(() => import('@/components/tools/IPLookupTool'))
const UUIDGeneratorTool = dynamic(() => import('@/components/tools/UUIDGeneratorTool'))
const PasswordGeneratorTool = dynamic(() => import('@/components/tools/PasswordGeneratorTool'))
const ColorPickerTool = dynamic(() => import('@/components/tools/ColorPickerTool'))
const TimestampConverterTool = dynamic(() => import('@/components/tools/TimestampConverterTool'))
const RegexTesterTool = dynamic(() => import('@/components/tools/RegexTesterTool'))
const TextDiffTool = dynamic(() => import('@/components/tools/TextDiffTool'))
const MarkdownEditorTool = dynamic(() => import('@/components/tools/MarkdownEditorTool'))
const CsvToJsonTool = dynamic(() => import('@/components/tools/CsvToJsonTool'))
const UrlEncoderTool = dynamic(() => import('@/components/tools/UrlEncoderTool'))
const BmiCalculatorTool = dynamic(() => import('@/components/tools/BmiCalculatorTool'))
const CurrencyConverterTool = dynamic(() => import('@/components/tools/CurrencyConverterTool'))
const DataAnalysisTool = dynamic(() => import('@/components/tools/DataAnalysisTool'))
const DomainLookupTool = dynamic(() => import('@/components/tools/DomainLookupTool'))
const DataVisualizationTool = dynamic(() => import('@/components/tools/DataVisualizationTool'))
const HtmlEntityTool = dynamic(() => import('@/components/tools/HtmlEntityTool'))
const HttpRequestTool = dynamic(() => import('@/components/tools/HttpRequestTool'))
const ImageConverterTool = dynamic(() => import('@/components/tools/ImageConverterTool'))
const TextEncryptTool = dynamic(() => import('@/components/tools/TextEncryptTool'))
const SqlFormatterTool = dynamic(() => import('@/components/tools/SqlFormatterTool'))
const DateCalculatorTool = dynamic(() => import('@/components/tools/DateCalculatorTool'))
const TextToSpeechTool = dynamic(() => import('@/components/tools/TextToSpeechTool'))
const BaseConverterTool = dynamic(() => import('@/components/tools/BaseConverterTool'))
const TodoListTool = dynamic(() => import('@/components/tools/TodoListTool'))
const SHAGeneratorTool = dynamic(() => import('@/components/tools/SHAGeneratorTool'))
const NotebookTool = dynamic(() => import('@/components/tools/NotebookTool'))
const SpreadsheetTool = dynamic(() => import('@/components/tools/SpreadsheetTool'))
const JsObfuscatorTool = dynamic(() => import('@/components/tools/JsObfuscatorTool'))
const CssMinifierTool = dynamic(() => import('@/components/tools/CssMinifierTool'))
const TextCaseTool = dynamic(() => import('@/components/tools/TextCaseTool'))
const TextReplaceTool = dynamic(() => import('@/components/tools/TextReplaceTool'))
const TextStatisticsTool = dynamic(() => import('@/components/tools/TextStatisticsTool'))
const LoanCalculatorTool = dynamic(() => import('@/components/tools/LoanCalculatorTool'))
const TextSortTool = dynamic(() => import('@/components/tools/TextSortTool'))
const TextDeduplicatorTool = dynamic(() => import('@/components/tools/TextDeduplicatorTool'))
const TextSplitMergeTool = dynamic(() => import('@/components/tools/TextSplitMergeTool'))
const FileHashTool = dynamic(() => import('@/components/tools/FileHashTool'))
const TextEncodingTool = dynamic(() => import('@/components/tools/TextEncodingTool'))
const TextExtractorTool = dynamic(() => import('@/components/tools/TextExtractorTool'))
const TextTemplateTool = dynamic(() => import('@/components/tools/TextTemplateTool'))
const ImageWatermarkTool = dynamic(() => import('@/components/tools/ImageWatermarkTool'))
const ImageCropperTool = dynamic(() => import('@/components/tools/ImageCropperTool'))
const ImageRotateFlipTool = dynamic(() => import('@/components/tools/ImageRotateFlipTool'))
const ImageFilterTool = dynamic(() => import('@/components/tools/ImageFilterTool'))
const ImageStitchTool = dynamic(() => import('@/components/tools/ImageStitchTool'))
const BatchImageConverterTool = dynamic(() => import('@/components/tools/BatchImageConverterTool'))
const ImageToBase64Tool = dynamic(() => import('@/components/tools/ImageToBase64Tool'))
const ImageExifViewerTool = dynamic(() => import('@/components/tools/ImageExifViewerTool'))
const CssGeneratorTool = dynamic(() => import('@/components/tools/CssGeneratorTool'))
const HtmlPreviewerTool = dynamic(() => import('@/components/tools/HtmlPreviewerTool'))
const CodeBeautifierTool = dynamic(() => import('@/components/tools/CodeBeautifierTool'))
const ApiTesterTool = dynamic(() => import('@/components/tools/ApiTesterTool'))
const JsModuleGeneratorTool = dynamic(() => import('@/components/tools/JsModuleGeneratorTool'))
const GitCommitGeneratorTool = dynamic(() => import('@/components/tools/GitCommitGeneratorTool'))
const CodeMinifierTool = dynamic(() => import('@/components/tools/CodeMinifierTool'))
const CodeSnippetManagerTool = dynamic(() => import('@/components/tools/CodeSnippetManagerTool'))
const CronGeneratorTool = dynamic(() => import('@/components/tools/CronGeneratorTool'))
const JwtDecoderTool = dynamic(() => import('@/components/tools/JwtDecoderTool'))
const PackageManagerTool = dynamic(() => import('@/components/tools/PackageManagerTool'))
const AESEncryptTool = dynamic(() => import('@/components/tools/AESEncryptTool'))
const RSAKeyGeneratorTool = dynamic(() => import('@/components/tools/RSAKeyGeneratorTool'))
const HMACGeneratorTool = dynamic(() => import('@/components/tools/HMACGeneratorTool'))
const PasswordStrengthTool = dynamic(() => import('@/components/tools/PasswordStrengthTool'))
const DigitalSignatureVerifierTool = dynamic(() => import('@/components/tools/DigitalSignatureVerifierTool'))
const SSLCertificateAnalyzerTool = dynamic(() => import('@/components/tools/SSLCertificateAnalyzerTool'))
const FileEncryptorTool = dynamic(() => import('@/components/tools/FileEncryptorTool'))
const PercentageCalculatorTool = dynamic(() => import('@/components/tools/PercentageCalculatorTool'))
const StatisticsCalculatorTool = dynamic(() => import('@/components/tools/StatisticsCalculatorTool'))
const GeometryCalculatorTool = dynamic(() => import('@/components/tools/GeometryCalculatorTool'))
const ScientificCalculatorTool = dynamic(() => import('@/components/tools/ScientificCalculatorTool'))
const CompoundInterestCalculatorTool = dynamic(() => import('@/components/tools/CompoundInterestCalculatorTool'))
const TimeCalculatorTool = dynamic(() => import('@/components/tools/TimeCalculatorTool'))
const FitnessCalculatorTool = dynamic(() => import('@/components/tools/FitnessCalculatorTool'))
const NutritionCalculatorTool = dynamic(() => import('@/components/tools/NutritionCalculatorTool'))
const PortScannerTool = dynamic(() => import('@/components/tools/PortScannerTool'))
const DNSLookupTool = dynamic(() => import('@/components/tools/DNSLookupTool'))
const WhoisLookupTool = dynamic(() => import('@/components/tools/WhoisLookupTool'))
const FileSizeCalculatorTool = dynamic(() => import('@/components/tools/FileSizeCalculatorTool'))
const WorldClockTool = dynamic(() => import('@/components/tools/WorldClockTool'))
const FileFormatConverterTool = dynamic(() => import('@/components/tools/FileFormatConverterTool'))
const FileBatchRenameTool = dynamic(() => import('@/components/tools/FileBatchRenameTool'))
const PasswordManagerTool = dynamic(() => import('@/components/tools/PasswordManagerTool'))
const WeatherForecastTool = dynamic(() => import('@/components/tools/WeatherForecastTool'))
const EbookReaderTool = dynamic(() => import('@/components/tools/EbookReaderTool'))
const PdfConverterTool = dynamic(() => import('@/components/tools/PdfConverterTool'))
const ExpenseTrackerTool = dynamic(() => import('@/components/tools/ExpenseTrackerTool'))
const HabitTrackerTool = dynamic(() => import('@/components/tools/HabitTrackerTool'))
const DocumentConverterTool = dynamic(() => import('@/components/tools/DocumentConverterTool'))
const MediaConverterTool = dynamic(() => import('@/components/tools/MediaConverterTool'))
const OCRTool = dynamic(() => import('@/components/tools/OCRTool'))
const CountdownTimerTool = dynamic(() => import('@/components/tools/CountdownTimerTool'))
const AlarmClockTool = dynamic(() => import('@/components/tools/AlarmClockTool'))
const BirthdayReminderTool = dynamic(() => import('@/components/tools/BirthdayReminderTool'))
const TravelPlannerTool = dynamic(() => import('@/components/tools/TravelPlannerTool'))
const SlugifyTool = dynamic(() => import('@/components/tools/SlugifyTool'))
const LoremIpsumTool = dynamic(() => import('@/components/tools/LoremIpsumTool'))
const YamlJsonConverterTool = dynamic(() => import('@/components/tools/YamlJsonConverterTool'))
const HtmlToTextTool = dynamic(() => import('@/components/tools/HtmlToTextTool'))
const TextToTableTool = dynamic(() => import('@/components/tools/TextToTableTool'))
const TextToListTool = dynamic(() => import('@/components/tools/TextToListTool'))
const TextHighlighterTool = dynamic(() => import('@/components/tools/TextHighlighterTool'))
const TextSummarizerTool = dynamic(() => import('@/components/tools/TextSummarizerTool'))
const SpellCheckerTool = dynamic(() => import('@/components/tools/SpellCheckerTool'))
const TextTranslatorTool = dynamic(() => import('@/components/tools/TextTranslatorTool'))
const TextToSpeechEnhancedTool = dynamic(() => import('@/components/tools/TextToSpeechEnhancedTool'))
const GrammarCheckerTool = dynamic(() => import('@/components/tools/GrammarCheckerTool'))
const TextFormatterTool = dynamic(() => import('@/components/tools/TextFormatterTool'))
const TextAnnotatorTool = dynamic(() => import('@/components/tools/TextAnnotatorTool'))
const TextVersionDiffTool = dynamic(() => import('@/components/tools/TextVersionDiffTool'))
const ColorFormatConverterTool = dynamic(() => import('@/components/tools/ColorFormatConverterTool'))
const ColorPaletteGeneratorTool = dynamic(() => import('@/components/tools/ColorPaletteGeneratorTool'))
const ColorContrastCheckerTool = dynamic(() => import('@/components/tools/ColorContrastCheckerTool'))
const CssGradientGeneratorTool = dynamic(() => import('@/components/tools/CssGradientGeneratorTool'))
const CssShadowGeneratorTool = dynamic(() => import('@/components/tools/CssShadowGeneratorTool'))
const CssBorderRadiusGeneratorTool = dynamic(() => import('@/components/tools/CssBorderRadiusGeneratorTool'))
const FaviconGeneratorTool = dynamic(() => import('@/components/tools/FaviconGeneratorTool'))
const CssClampCalculatorTool = dynamic(() => import('@/components/tools/CssClampCalculatorTool'))
const TailwindCheatSheetTool = dynamic(() => import('@/components/tools/TailwindCheatSheetTool'))
const CssAnimationGeneratorTool = dynamic(() => import('@/components/tools/CssAnimationGeneratorTool'))
const CssGridGeneratorTool = dynamic(() => import('@/components/tools/CssGridGeneratorTool'))
const FlexboxGeneratorTool = dynamic(() => import('@/components/tools/FlexboxGeneratorTool'))
const CssVariableGeneratorTool = dynamic(() => import('@/components/tools/CssVariableGeneratorTool'))
const FontPairingTool = dynamic(() => import('@/components/tools/FontPairingTool'))
const DesignTokenGeneratorTool = dynamic(() => import('@/components/tools/DesignTokenGeneratorTool'))
const ImageResizerTool = dynamic(() => import('@/components/tools/ImageResizerTool'))
const SvgOptimizerTool = dynamic(() => import('@/components/tools/SvgOptimizerTool'))
const GifFrameExtractorTool = dynamic(() => import('@/components/tools/GifFrameExtractorTool'))
const VideoTrimmerTool = dynamic(() => import('@/components/tools/VideoTrimmerTool'))
const ImageCollageTool = dynamic(() => import('@/components/tools/ImageCollageTool'))
const AudioConverterTool = dynamic(() => import('@/components/tools/AudioConverterTool'))
const TimeDifferenceCalculatorTool = dynamic(() => import('@/components/tools/TimeDifferenceCalculatorTool'))
const TimezoneConverterTool = dynamic(() => import('@/components/tools/TimezoneConverterTool'))
const WeekNumberCalculatorTool = dynamic(() => import('@/components/tools/WeekNumberCalculatorTool'))
const DateArithmeticTool = dynamic(() => import('@/components/tools/DateArithmeticTool'))
const BusinessDayCalculatorTool = dynamic(() => import('@/components/tools/BusinessDayCalculatorTool'))
const ImageBlurTool = dynamic(() => import('@/components/tools/ImageBlurTool'))
const ImageMosaicTool = dynamic(() => import('@/components/tools/ImageMosaicTool'))
const CssSpriteGeneratorTool = dynamic(() => import('@/components/tools/CssSpriteGeneratorTool'))
const ImageBatchRenamerTool = dynamic(() => import('@/components/tools/ImageBatchRenamerTool'))
const WebpConverterTool = dynamic(() => import('@/components/tools/WebpConverterTool'))
const ImageMetadataEditorTool = dynamic(() => import('@/components/tools/ImageMetadataEditorTool'))
const ImageColorExtractorTool = dynamic(() => import('@/components/tools/ImageColorExtractorTool'))
const AdvancedOcrTool = dynamic(() => import('@/components/tools/AdvancedOcrTool'))
const GifMakerTool = dynamic(() => import('@/components/tools/GifMakerTool'))
const CalendarGeneratorTool = dynamic(() => import('@/components/tools/CalendarGeneratorTool'))

export const tools: Tool[] = [
  {
    id: 'week-number-calculator',
    name: {
      zh: '周数计算器',
      en: 'Week Number Calculator',
      ja: '週数計算機',
    },
    description: {
      zh: '精确计算年度周数，支持ISO 8601和美国两种国际标准，提供完整年度概览和历史记录管理',
      en: 'Precisely calculate annual week numbers with ISO 8601 and US standards, provides complete yearly overview and history management',
      ja: 'ISO 8601と米国標準で年度週数を正確に計算、完全な年次概要と履歴管理を提供',
    },
    category: 'calculator',
    icon: Calendar,
    path: '/tools/week-number-calculator',
    keywords: {
      zh: ['周数计算', '周数', '年度周数', 'ISO 8601', '美国标准', '时间计算', '日历计算', '工作周'],
      en: ['week number', 'week calculation', 'annual weeks', 'ISO 8601', 'US standard', 'time calculation', 'calendar calculation', 'work week'],
      ja: ['週数計算', '週数', '年度週数', 'ISO 8601', '米国標準', '時間計算', 'カレンダー計算', '作業週'],
    },
    component: WeekNumberCalculatorTool,
    isNew: true,
  },
  {
    id: 'base64',
    name: {
      zh: 'Base64 编解码',
      en: 'Base64 Encode/Decode',
      ja: 'Base64エンコード/デコード',
    },
    description: {
      zh: '在线 Base64 编码和解码工具',
      en: 'Online Base64 encoding and decoding tool',
      ja: 'オンラインBase64エンコード・デコードツール',
    },
    category: 'crypto',
    icon: Code,
    path: '/tools/base64',
    keywords: {
      zh: ['base64', '编码', '解码', '加密', '解密'],
      en: ['base64', 'encode', 'decode', 'encrypt', 'decrypt'],
      ja: ['base64', 'エンコード', 'デコード', '暗号化', '復号化'],
    },
    component: Base64Tool,
  },
  {
    id: 'port-scanner',
    name: {
      zh: '端口扫描器',
      en: 'Port Scanner',
      ja: 'ポートスキャナー',
    },
    description: {
      zh: '扫描主机端口开放状态，支持单端口、多端口和预设扫描模式',
      en: 'Scan host port status with single port, multi-port and preset scanning modes',
      ja: 'ホストポートの開放状態をスキャン、単一ポート、複数ポート、プリセットスキャンモードに対応',
    },
    category: 'network',
    icon: Wifi,
    path: '/tools/port-scanner',
    keywords: {
      zh: ['端口扫描', '网络扫描', '端口', '网络安全', '服务发现', 'TCP', 'UDP', 'nmap'],
      en: ['port scanner', 'network scan', 'port', 'network security', 'service discovery', 'TCP', 'UDP', 'nmap'],
      ja: ['ポートスキャン', 'ネットワークスキャン', 'ポート', 'ネットワークセキュリティ', 'サービス発見', 'TCP', 'UDP', 'nmap'],
    },
    component: PortScannerTool,
    isNew: true,
  },
  {
    id: 'dns-lookup',
    name: {
      zh: 'DNS查询工具',
      en: 'DNS Lookup Tool',
      ja: 'DNS検索ツール',
    },
    description: {
      zh: '查询域名的DNS记录信息，支持A、AAAA、CNAME、MX、TXT等多种记录类型',
      en: 'Query domain DNS records with support for A, AAAA, CNAME, MX, TXT and other record types',
      ja: 'ドメインのDNSレコード情報を検索、A、AAAA、CNAME、MX、TXTなど多種のレコードタイプに対応',
    },
    category: 'network',
    icon: Globe,
    path: '/tools/dns-lookup',
    keywords: {
      zh: ['DNS', '域名解析', '域名查询', 'DNS记录', 'A记录', 'CNAME', 'MX', 'TXT', '解析'],
      en: ['DNS', 'domain resolution', 'domain lookup', 'DNS records', 'A record', 'CNAME', 'MX', 'TXT', 'resolution'],
      ja: ['DNS', 'ドメイン解決', 'ドメイン検索', 'DNSレコード', 'Aレコード', 'CNAME', 'MX', 'TXT', '解決'],
    },
    component: DNSLookupTool,
    isNew: true,
  },
  {
    id: 'whois-lookup',
    name: {
      zh: 'WHOIS 查询工具',
      en: 'WHOIS Lookup Tool',
      ja: 'WHOIS検索ツール',
    },
    description: {
      zh: '查询域名的注册信息和WHOIS数据，包括注册商、联系人等详细信息',
      en: 'Query domain registration information and WHOIS data including registrar and contact details',
      ja: 'ドメイン登録情報とWHOISデータを検索、レジストラと連絡先詳細を含む',
    },
    category: 'network',
    icon: Globe,
    path: '/tools/whois-lookup',
    keywords: {
      zh: ['WHOIS', '域名查询', '域名注册', '注册信息', '域名信息', '注册商', '联系人'],
      en: ['WHOIS', 'domain lookup', 'domain registration', 'registration info', 'domain info', 'registrar', 'contact'],
      ja: ['WHOIS', 'ドメイン検索', 'ドメイン登録', '登録情報', 'ドメイン情報', 'レジストラ', '連絡先'],
    },
    component: WhoisLookupTool,
    isNew: true,
  },
  {
    id: 'json-formatter',
    name: {
      zh: 'JSON 格式化',
      en: 'JSON Formatter',
      ja: 'JSONフォーマッター',
    },
    description: {
      zh: '在线格式化、压缩、验证 JSON 数据',
      en: 'Format, minify and validate JSON data online',
      ja: 'JSONデータをオンラインでフォーマット、圧縮、検証',
    },
    category: 'developer',
    icon: Code2,
    path: '/tools/json-formatter',
    keywords: {
      zh: ['json', '格式化', '压缩', '验证', 'json格式化'],
      en: ['json', 'formatter', 'minify', 'validate', 'json formatter'],
      ja: ['json', 'フォーマット', '圧縮', '検証', 'jsonフォーマッター'],
    },
    component: JsonFormatterTool,
    isNew: true,
  },
  {
    id: 'md5-generator',
    name: {
      zh: 'MD5 生成器',
      en: 'MD5 Generator',
      ja: 'MD5ジェネレーター',
    },
    description: {
      zh: '生成文本的 MD5 哈希值（仅用于文件校验）',
      en: 'Generate MD5 hash for text (for file verification only)',
      ja: 'テキストのMD5ハッシュ値を生成（ファイル検証用）',
    },
    category: 'crypto',
    icon: Hash,
    path: '/tools/md5-generator',
    keywords: {
      zh: ['md5', '哈希', '散列', '校验', '摘要'],
      en: ['md5', 'hash', 'checksum', 'digest', 'verification'],
      ja: ['md5', 'ハッシュ', 'チェックサム', 'ダイジェスト', '検証'],
    },
    component: MD5GeneratorTool,
    isNew: true,
  },
  {
    id: 'qrcode-generator',
    name: {
      zh: '二维码生成器',
      en: 'QR Code Generator',
      ja: 'QRコードジェネレーター',
    },
    description: {
      zh: '快速生成各种内容的二维码，支持自定义样式',
      en: 'Generate QR codes with custom styles',
      ja: 'カスタムスタイルでQRコードを生成',
    },
    category: 'generator',
    icon: QrCode,
    path: '/tools/qrcode-generator',
    keywords: {
      zh: ['二维码', 'qr', 'qrcode', '扫码', '条码'],
      en: ['qr code', 'qrcode', 'barcode', 'scan', 'generator'],
      ja: ['QRコード', 'バーコード', 'スキャン', 'ジェネレーター'],
    },
    component: QRCodeGeneratorTool,
    isNew: true,
  },
  {
    id: 'image-compressor',
    name: {
      zh: '图片压缩',
      en: 'Image Compressor',
      ja: '画像圧縮',
    },
    description: {
      zh: '批量压缩图片，支持调整尺寸和质量',
      en: 'Batch compress images with size and quality adjustment',
      ja: 'サイズと品質調整で画像を一括圧縮',
    },
    category: 'image',
    icon: Image,
    path: '/tools/image-compressor',
    keywords: {
      zh: ['图片压缩', '图像压缩', '压缩图片', '批量压缩', '减小体积'],
      en: ['image compress', 'photo compress', 'batch compress', 'reduce size'],
      ja: ['画像圧縮', '写真圧縮', '一括圧縮', 'サイズ削減'],
    },
    component: ImageCompressorTool,
    isNew: true,
  },
  {
    id: 'unit-converter',
    name: {
      zh: '单位换算器',
      en: 'Unit Converter',
      ja: '単位変換器',
    },
    description: {
      zh: '支持长度、重量、温度等多种单位的快速换算',
      en: 'Convert between various units of length, weight, temperature, and more',
      ja: '長さ、重量、温度など様々な単位を変換',
    },
    category: 'calculator',
    icon: Calculator,
    path: '/tools/unit-converter',
    keywords: {
      zh: ['单位换算', '单位转换', '长度', '重量', '温度', '换算器'],
      en: ['unit converter', 'conversion', 'length', 'weight', 'temperature'],
      ja: ['単位変換', '変換', '長さ', '重量', '温度'],
    },
    component: UnitConverterTool,
    isNew: true,
  },
  {
    id: 'pomodoro-timer',
    name: {
      zh: '番茄钟计时器',
      en: 'Pomodoro Timer',
      ja: 'ポモドーロタイマー',
    },
    description: {
      zh: '使用番茄工作法提高专注力和工作效率',
      en: 'Boost focus and productivity with the Pomodoro Technique',
      ja: 'ポモドーロテクニックで集中力と生産性を向上',
    },
    category: 'productivity',
    icon: Timer,
    path: '/tools/pomodoro-timer',
    keywords: {
      zh: ['番茄钟', '番茄工作法', '计时器', '专注', '时间管理'],
      en: ['pomodoro', 'timer', 'focus', 'productivity', 'time management'],
      ja: ['ポモドーロ', 'タイマー', '集中', '生産性', '時間管理'],
    },
    component: PomodoroTimerTool,
    isNew: true,
  },
  {
    id: 'chart-generator',
    name: {
      zh: '数据图表生成器',
      en: 'Chart Generator',
      ja: 'チャートジェネレーター',
    },
    description: {
      zh: '快速创建柱状图、折线图、饼图等数据可视化图表',
      en: 'Create bar charts, line charts, pie charts and more',
      ja: '棒グラフ、折れ線グラフ、円グラフなどを作成',
    },
    category: 'generator',
    icon: BarChart,
    path: '/tools/chart-generator',
    keywords: {
      zh: ['图表', '数据可视化', '柱状图', '折线图', '饼图', 'svg'],
      en: ['chart', 'data visualization', 'bar chart', 'line chart', 'pie chart', 'svg'],
      ja: ['チャート', 'データ可視化', '棒グラフ', '折れ線グラフ', '円グラフ', 'svg'],
    },
    component: ChartGeneratorTool,
    isNew: true,
  },
  {
    id: 'ip-lookup',
    name: {
      zh: 'IP地址查询',
      en: 'IP Lookup',
      ja: 'IPアドレス検索',
    },
    description: {
      zh: '查询IP地址的地理位置、运营商等详细信息',
      en: 'Look up IP address location, ISP and other details',
      ja: 'IPアドレスの地理的位置、ISPなどの詳細を検索',
    },
    category: 'developer',
    icon: Globe,
    path: '/tools/ip-lookup',
    keywords: {
      zh: ['ip', 'ip地址', '地理位置', '运营商', 'isp', '网络'],
      en: ['ip', 'ip address', 'location', 'isp', 'network', 'lookup'],
      ja: ['ip', 'ipアドレス', '位置', 'isp', 'ネットワーク', '検索'],
    },
    component: IPLookupTool,
    isNew: true,
  },
  {
    id: 'uuid-generator',
    name: {
      zh: 'UUID 生成器',
      en: 'UUID Generator',
      ja: 'UUIDジェネレーター',
    },
    description: {
      zh: '生成通用唯一识别码（UUID），支持多个版本和格式',
      en: 'Generate universally unique identifiers (UUID) with multiple versions',
      ja: 'UUID（汎用一意識別子）を生成、複数バージョン対応',
    },
    category: 'generator',
    icon: Fingerprint,
    path: '/tools/uuid-generator',
    keywords: {
      zh: ['uuid', 'guid', '唯一标识', '随机', 'v4', 'v1'],
      en: ['uuid', 'guid', 'unique identifier', 'random', 'v4', 'v1'],
      ja: ['uuid', 'guid', '一意識別子', 'ランダム', 'v4', 'v1'],
    },
    component: UUIDGeneratorTool,
    isNew: true,
  },
  {
    id: 'password-generator',
    name: {
      zh: '密码生成器',
      en: 'Password Generator',
      ja: 'パスワードジェネレーター',
    },
    description: {
      zh: '生成安全、随机的强密码，保护您的账户安全',
      en: 'Generate secure, random strong passwords to protect your accounts',
      ja: '安全でランダムな強力なパスワードを生成',
    },
    category: 'crypto',
    icon: Shield,
    path: '/tools/password-generator',
    keywords: {
      zh: ['密码', '密码生成', '随机密码', '强密码', '安全'],
      en: ['password', 'password generator', 'random password', 'strong password', 'security'],
      ja: ['パスワード', 'パスワード生成', 'ランダムパスワード', '強力なパスワード', 'セキュリティ'],
    },
    component: PasswordGeneratorTool,
    isNew: true,
  },
  {
    id: 'color-picker',
    name: {
      zh: '颜色选择器',
      en: 'Color Picker',
      ja: 'カラーピッカー',
    },
    description: {
      zh: '选择颜色并获取多种格式的颜色代码',
      en: 'Pick colors and get color codes in multiple formats',
      ja: '色を選択して複数フォーマットのカラーコードを取得',
    },
    category: 'developer',
    icon: Palette,
    path: '/tools/color-picker',
    keywords: {
      zh: ['颜色', '色彩', 'hex', 'rgb', 'hsl', '调色板', '配色'],
      en: ['color', 'picker', 'hex', 'rgb', 'hsl', 'palette', 'scheme'],
      ja: ['カラー', 'ピッカー', 'hex', 'rgb', 'hsl', 'パレット', '配色'],
    },
    component: ColorPickerTool,
    isNew: true,
  },
  {
    id: 'timestamp-converter',
    name: {
      zh: '时间戳转换器',
      en: 'Timestamp Converter',
      ja: 'タイムスタンプ変換器',
    },
    description: {
      zh: 'Unix 时间戳与日期时间格式相互转换',
      en: 'Convert between Unix timestamp and date/time formats',
      ja: 'Unixタイムスタンプと日時形式の相互変換',
    },
    category: 'developer',
    icon: Clock,
    path: '/tools/timestamp-converter',
    keywords: {
      zh: ['时间戳', 'unix', '时间', '日期', '转换', 'timestamp'],
      en: ['timestamp', 'unix', 'time', 'date', 'converter', 'epoch'],
      ja: ['タイムスタンプ', 'unix', '時間', '日付', '変換', 'エポック'],
    },
    component: TimestampConverterTool,
    isNew: true,
  },
  {
    id: 'regex-tester',
    name: {
      zh: '正则表达式测试器',
      en: 'Regex Tester',
      ja: '正規表現テスター',
    },
    description: {
      zh: '在线测试和调试正则表达式，支持实时匹配和高亮显示',
      en: 'Test and debug regular expressions online with real-time matching',
      ja: 'リアルタイムマッチングで正規表現をオンラインでテスト',
    },
    category: 'developer',
    icon: Regex,
    path: '/tools/regex-tester',
    keywords: {
      zh: ['正则', '正则表达式', 'regex', '匹配', '测试', '调试'],
      en: ['regex', 'regular expression', 'pattern', 'match', 'test', 'debug'],
      ja: ['正規表現', 'regex', 'パターン', 'マッチ', 'テスト', 'デバッグ'],
    },
    component: RegexTesterTool,
    isNew: true,
  },
  {
    id: 'text-diff',
    name: {
      zh: '文本对比工具',
      en: 'Text Diff Tool',
      ja: 'テキスト比較ツール',
    },
    description: {
      zh: '对比两段文本的差异，支持行级别对比和多种选项',
      en: 'Compare differences between two texts with line-level comparison',
      ja: '2つのテキストの差分を比較、行レベルの比較をサポート',
    },
    category: 'text',
    icon: FileDiff,
    path: '/tools/text-diff',
    keywords: {
      zh: ['文本对比', '差异', 'diff', '比较', '文本比较', '对比工具'],
      en: ['text diff', 'compare', 'difference', 'comparison', 'text comparison'],
      ja: ['テキスト比較', '差分', 'diff', '比較', 'テキスト差分'],
    },
    component: TextDiffTool,
    isNew: true,
  },
  {
    id: 'markdown-editor',
    name: {
      zh: 'Markdown 编辑器',
      en: 'Markdown Editor',
      ja: 'Markdownエディター',
    },
    description: {
      zh: '实时预览的 Markdown 编辑器，支持常用语法和快捷操作',
      en: 'Real-time preview Markdown editor with common syntax support',
      ja: 'リアルタイムプレビュー付きMarkdownエディター',
    },
    category: 'text',
    icon: Edit3,
    path: '/tools/markdown-editor',
    keywords: {
      zh: ['markdown', 'md', '编辑器', '预览', '文档', '写作'],
      en: ['markdown', 'md', 'editor', 'preview', 'document', 'writing'],
      ja: ['markdown', 'md', 'エディター', 'プレビュー', 'ドキュメント', '執筆'],
    },
    component: MarkdownEditorTool,
    isNew: true,
  },
  {
    id: 'csv-to-json',
    name: {
      zh: 'CSV/JSON 转换器',
      en: 'CSV/JSON Converter',
      ja: 'CSV/JSONコンバーター',
    },
    description: {
      zh: '在 CSV 和 JSON 格式之间相互转换，支持自定义选项',
      en: 'Convert between CSV and JSON formats with custom options',
      ja: 'CSVとJSON形式の相互変換、カスタムオプション対応',
    },
    category: 'converter',
    icon: FileSpreadsheet,
    path: '/tools/csv-to-json',
    keywords: {
      zh: ['csv', 'json', '转换', '表格', '数据', 'excel'],
      en: ['csv', 'json', 'convert', 'table', 'data', 'excel'],
      ja: ['csv', 'json', '変換', 'テーブル', 'データ', 'excel'],
    },
    component: CsvToJsonTool,
    isNew: true,
  },
  {
    id: 'url-encoder',
    name: {
      zh: 'URL 编码/解码',
      en: 'URL Encoder/Decoder',
      ja: 'URLエンコーダー/デコーダー',
    },
    description: {
      zh: '在线 URL 编码和解码工具，支持完整 URL 和组件编码',
      en: 'Online URL encoding and decoding tool with full URL and component support',
      ja: 'オンラインURLエンコード・デコードツール',
    },
    category: 'developer',
    icon: Link2,
    path: '/tools/url-encoder',
    keywords: {
      zh: ['url', '编码', '解码', 'encode', 'decode', 'uri'],
      en: ['url', 'encode', 'decode', 'uri', 'percent', 'encoding'],
      ja: ['url', 'エンコード', 'デコード', 'uri', 'パーセント', 'エンコーディング'],
    },
    component: UrlEncoderTool,
    isNew: true,
  },
  {
    id: 'bmi-calculator',
    name: {
      zh: 'BMI 计算器',
      en: 'BMI Calculator',
      ja: 'BMI計算機',
    },
    description: {
      zh: '计算您的身体质量指数（BMI），了解您的健康体重范围',
      en: 'Calculate your Body Mass Index (BMI) and understand your healthy weight range',
      ja: 'BMI（ボディマス指数）を計算し、健康的な体重範囲を理解',
    },
    category: 'lifestyle',
    icon: Heart,
    path: '/tools/bmi-calculator',
    keywords: {
      zh: ['bmi', '体重', '身高', '健康', '体质指数', '肥胖'],
      en: ['bmi', 'weight', 'height', 'health', 'body mass index', 'obesity'],
      ja: ['bmi', '体重', '身長', '健康', 'ボディマス指数', '肥満'],
    },
    component: BmiCalculatorTool,
    isNew: true,
  },
  {
    id: 'currency-converter',
    name: {
      zh: '汇率换算器',
      en: 'Currency Converter',
      ja: '為替レート計算機',
    },
    description: {
      zh: '实时汇率转换，支持全球主要货币',
      en: 'Real-time currency conversion with major global currencies',
      ja: 'リアルタイム為替レート変換、主要通貨対応',
    },
    category: 'converter',
    icon: DollarSign,
    path: '/tools/currency-converter',
    keywords: {
      zh: ['汇率', '货币', '外汇', '换算', '美元', '人民币'],
      en: ['exchange rate', 'currency', 'forex', 'converter', 'dollar', 'yuan'],
      ja: ['為替レート', '通貨', '外国為替', '変換', 'ドル', '元'],
    },
    component: CurrencyConverterTool,
    isNew: true,
  },
  {
    id: 'data-analysis',
    name: {
      zh: '数据统计分析器',
      en: 'Data Analysis Tool',
      ja: 'データ統計分析ツール',
    },
    description: {
      zh: '输入数值数据，自动计算各种统计指标和频率分布',
      en: 'Analyze numerical data with various statistical indicators',
      ja: '数値データを入力して、各種統計指標と頻度分布を自動計算',
    },
    category: 'calculator',
    icon: BarChart3,
    path: '/tools/data-analysis',
    keywords: {
      zh: ['数据分析', '统计', '平均值', '中位数', '标准差', '方差'],
      en: ['data analysis', 'statistics', 'mean', 'median', 'standard deviation', 'variance'],
      ja: ['データ分析', '統計', '平均値', '中央値', '標準偏差', '分散'],
    },
    component: DataAnalysisTool,
    isNew: true,
  },
  {
    id: 'domain-lookup',
    name: {
      zh: '域名查询工具',
      en: 'Domain Lookup Tool',
      ja: 'ドメイン検索ツール',
    },
    description: {
      zh: '查询域名的注册信息、DNS记录和WHOIS数据',
      en: 'Lookup domain registration, DNS records and WHOIS data',
      ja: 'ドメイン登録情報、DNSレコード、WHOISデータを検索',
    },
    category: 'developer',
    icon: Globe,
    path: '/tools/domain-lookup',
    keywords: {
      zh: ['域名', 'domain', 'whois', 'dns', '域名查询', '注册信息'],
      en: ['domain', 'whois', 'dns', 'lookup', 'registration', 'nameserver'],
      ja: ['ドメイン', 'whois', 'dns', '検索', '登録情報', 'ネームサーバー'],
    },
    component: DomainLookupTool,
    isNew: true,
  },
  {
    id: 'data-visualization',
    name: {
      zh: '数据可视化工具',
      en: 'Data Visualization Tool',
      ja: 'データ可視化ツール',
    },
    description: {
      zh: '将CSV数据转换为美观的图表，支持柱状图、折线图、饼图等多种类型',
      en: 'Convert CSV data to beautiful charts including bar, line, pie charts',
      ja: 'CSVデータを美しいチャートに変換、棒グラフ、折れ線グラフ、円グラフなど対応',
    },
    category: 'other',
    icon: LineChart,
    path: '/tools/data-visualization',
    keywords: {
      zh: ['数据可视化', '图表', '柱状图', '折线图', '饼图', 'CSV'],
      en: ['data visualization', 'chart', 'bar chart', 'line chart', 'pie chart', 'CSV'],
      ja: ['データ可視化', 'チャート', '棒グラフ', '折れ線グラフ', '円グラフ', 'CSV'],
    },
    component: DataVisualizationTool,
    isNew: true,
  },
  {
    id: 'html-entity',
    name: {
      zh: 'HTML 实体编码/解码',
      en: 'HTML Entity Encoder/Decoder',
      ja: 'HTMLエンティティエンコーダー/デコーダー',
    },
    description: {
      zh: '在线转换 HTML 实体，支持命名实体和数字实体的编码解码',
      en: 'Online HTML entity conversion with named and numeric entity support',
      ja: 'オンラインHTMLエンティティ変換、名前付きと数値エンティティ対応',
    },
    category: 'text',
    icon: FileCode,
    path: '/tools/html-entity',
    keywords: {
      zh: ['html', 'entity', '实体', '编码', '解码', '转义'],
      en: ['html', 'entity', 'encode', 'decode', 'escape', 'unescape'],
      ja: ['html', 'エンティティ', 'エンコード', 'デコード', 'エスケープ'],
    },
    component: HtmlEntityTool,
    isNew: true,
  },
  {
    id: 'http-request',
    name: {
      zh: 'HTTP 请求测试器',
      en: 'HTTP Request Tester',
      ja: 'HTTPリクエストテスター',
    },
    description: {
      zh: '在线测试 HTTP 请求，支持多种请求方法和自定义请求头',
      en: 'Test HTTP requests online with various methods and custom headers',
      ja: 'オンラインHTTPリクエストテスト、各種メソッドとカスタムヘッダー対応',
    },
    category: 'network',
    icon: Send,
    path: '/tools/http-request',
    keywords: {
      zh: ['http', 'api', '请求', '测试', 'rest', 'ajax'],
      en: ['http', 'api', 'request', 'test', 'rest', 'ajax'],
      ja: ['http', 'api', 'リクエスト', 'テスト', 'rest', 'ajax'],
    },
    component: HttpRequestTool,
    isNew: true,
  },
  {
    id: 'image-converter',
    name: {
      zh: '图片格式转换',
      en: 'Image Format Converter',
      ja: '画像フォーマット変換',
    },
    description: {
      zh: '批量转换图片格式，支持 JPEG、PNG、WebP 等多种格式',
      en: 'Batch convert image formats including JPEG, PNG, WebP and more',
      ja: '画像フォーマットを一括変換、JPEG、PNG、WebPなど対応',
    },
    category: 'image',
    icon: ImageIcon,
    path: '/tools/image-converter',
    keywords: {
      zh: ['图片转换', '格式转换', 'jpeg', 'png', 'webp', '批量转换'],
      en: ['image converter', 'format conversion', 'jpeg', 'png', 'webp', 'batch convert'],
      ja: ['画像変換', 'フォーマット変換', 'jpeg', 'png', 'webp', '一括変換'],
    },
    component: ImageConverterTool,
    isNew: true,
  },
  {
    id: 'text-encrypt',
    name: {
      zh: '文本加密/解密',
      en: 'Text Encryption/Decryption',
      ja: 'テキスト暗号化/復号化',
    },
    description: {
      zh: '使用多种加密算法保护您的文本信息',
      en: 'Protect your text with various encryption algorithms',
      ja: '様々な暗号化アルゴリズムでテキストを保護',
    },
    category: 'text',
    icon: Lock,
    path: '/tools/text-encrypt',
    keywords: {
      zh: ['加密', '解密', 'AES', 'DES', 'Base64', '摩斯密码', '凯撒密码'],
      en: ['encrypt', 'decrypt', 'AES', 'DES', 'Base64', 'morse', 'caesar'],
      ja: ['暗号化', '復号化', 'AES', 'DES', 'Base64', 'モールス', 'シーザー'],
    },
    component: TextEncryptTool,
    isNew: true,
  },
  {
    id: 'sql-formatter',
    name: {
      zh: 'SQL 格式化工具',
      en: 'SQL Formatter',
      ja: 'SQLフォーマッター',
    },
    description: {
      zh: '格式化、美化和压缩 SQL 查询语句',
      en: 'Format, beautify and minify SQL queries',
      ja: 'SQLクエリのフォーマット、美化、圧縮',
    },
    category: 'developer',
    icon: Database,
    path: '/tools/sql-formatter',
    keywords: {
      zh: ['sql', '格式化', '美化', '压缩', '查询', '数据库'],
      en: ['sql', 'formatter', 'beautify', 'minify', 'query', 'database'],
      ja: ['sql', 'フォーマット', '美化', '圧縮', 'クエリ', 'データベース'],
    },
    component: SqlFormatterTool,
    isNew: true,
  },
  {
    id: 'date-calculator',
    name: {
      zh: '日期计算器',
      en: 'Date Calculator',
      ja: '日付計算機',
    },
    description: {
      zh: '日期加减、间隔计算、节假日查询等多功能日期工具',
      en: 'Date arithmetic, interval calculation, holiday query and more',
      ja: '日付計算、期間計算、休日検索など多機能日付ツール',
    },
    category: 'lifestyle',
    icon: Calendar,
    path: '/tools/date-calculator',
    keywords: {
      zh: ['日期', '计算', '间隔', '工作日', '星座', '农历'],
      en: ['date', 'calculator', 'interval', 'workdays', 'zodiac', 'calendar'],
      ja: ['日付', '計算', '期間', '営業日', '星座', 'カレンダー'],
    },
    component: DateCalculatorTool,
    isNew: true,
  },
  {
    id: 'text-to-speech',
    name: {
      zh: '文本转语音',
      en: 'Text to Speech',
      ja: 'テキスト読み上げ',
    },
    description: {
      zh: '将文本转换为自然流畅的语音，支持多种语言和声音',
      en: 'Convert text to natural speech with multiple languages and voices',
      ja: 'テキストを自然な音声に変換、複数の言語と音声に対応',
    },
    category: 'text',
    icon: Volume2,
    path: '/tools/text-to-speech',
    keywords: {
      zh: ['语音', '朗读', 'TTS', '语音合成', '文字转语音'],
      en: ['speech', 'voice', 'TTS', 'text to speech', 'audio'],
      ja: ['音声', '読み上げ', 'TTS', 'テキスト読み上げ', 'オーディオ'],
    },
    component: TextToSpeechTool,
    isNew: true,
  },
  {
    id: 'base-converter',
    name: {
      zh: '进制转换器',
      en: 'Base Converter',
      ja: '進数変換器',
    },
    description: {
      zh: '在二进制、八进制、十进制和十六进制之间自由转换',
      en: 'Convert between binary, octal, decimal and hexadecimal',
      ja: '2進数、8進数、10進数、16進数の相互変換',
    },
    category: 'calculator',
    icon: Binary,
    path: '/tools/base-converter',
    keywords: {
      zh: ['进制', '二进制', '八进制', '十进制', '十六进制', '转换'],
      en: ['base', 'binary', 'octal', 'decimal', 'hexadecimal', 'converter'],
      ja: ['進数', '2進数', '8進数', '10進数', '16進数', '変換'],
    },
    component: BaseConverterTool,
    isNew: true,
  },
  {
    id: 'todo-list',
    name: {
      zh: '任务清单管理器',
      en: 'Todo List Manager',
      ja: 'タスクリストマネージャー',
    },
    description: {
      zh: '高效管理您的待办事项，支持优先级、分类和截止日期',
      en: 'Efficiently manage your tasks with priority, categories and due dates',
      ja: 'タスクを効率的に管理、優先度、カテゴリ、期限をサポート',
    },
    category: 'productivity',
    icon: CheckSquare,
    path: '/tools/todo-list',
    keywords: {
      zh: ['任务', '待办', 'todo', '清单', '管理', 'GTD'],
      en: ['task', 'todo', 'list', 'manager', 'GTD', 'productivity'],
      ja: ['タスク', 'TODO', 'リスト', '管理', 'GTD', '生産性'],
    },
    component: TodoListTool,
    isNew: true,
  },
  {
    id: 'sha-generator',
    name: {
      zh: 'SHA 哈希生成器',
      en: 'SHA Hash Generator',
      ja: 'SHAハッシュジェネレーター',
    },
    description: {
      zh: '生成文本或文件的 SHA 哈希值，支持多种 SHA 算法',
      en: 'Generate SHA hash for text or files with multiple SHA algorithms',
      ja: 'テキストやファイルのSHAハッシュ値を生成、複数のSHAアルゴリズム対応',
    },
    category: 'crypto',
    icon: Shield,
    path: '/tools/sha-generator',
    keywords: {
      zh: ['sha', 'sha256', 'sha512', '哈希', '加密', '校验'],
      en: ['sha', 'sha256', 'sha512', 'hash', 'checksum', 'crypto'],
      ja: ['sha', 'sha256', 'sha512', 'ハッシュ', '暗号化', 'チェックサム'],
    },
    component: SHAGeneratorTool,
    isNew: true,
  },
  {
    id: 'notebook',
    name: {
      zh: '笔记本',
      en: 'Notebook',
      ja: 'ノートブック',
    },
    description: {
      zh: '创建、管理和搜索您的个人笔记，支持分类和标签',
      en: 'Create, manage and search your personal notes with categories and tags',
      ja: '個人ノートを作成、管理、検索、カテゴリとタグ対応',
    },
    category: 'productivity',
    icon: BookOpen,
    path: '/tools/notebook',
    keywords: {
      zh: ['笔记', '笔记本', '记事本', '备忘录', '知识管理'],
      en: ['note', 'notebook', 'memo', 'knowledge', 'management'],
      ja: ['ノート', 'メモ', '備忘録', '知識管理', 'ナレッジ'],
    },
    component: NotebookTool,
    isNew: true,
  },
  {
    id: 'spreadsheet',
    name: {
      zh: '数据表格编辑器',
      en: 'Spreadsheet Editor',
      ja: 'スプレッドシートエディター',
    },
    description: {
      zh: '在线电子表格编辑器，支持公式计算、数据导入导出',
      en: 'Online spreadsheet editor with formula calculation and data import/export',
      ja: 'オンライン表計算エディター、数式計算とデータインポート/エクスポート対応',
    },
    category: 'other',
    icon: Table,
    path: '/tools/spreadsheet',
    keywords: {
      zh: ['表格', 'excel', '电子表格', '公式', 'csv', '数据处理'],
      en: ['spreadsheet', 'excel', 'table', 'formula', 'csv', 'data'],
      ja: ['表計算', 'エクセル', 'スプレッドシート', '数式', 'csv', 'データ'],
    },
    component: SpreadsheetTool,
    isNew: true,
  },
  {
    id: 'js-obfuscator',
    name: {
      zh: 'JavaScript 混淆器',
      en: 'JavaScript Obfuscator',
      ja: 'JavaScript難読化ツール',
    },
    description: {
      zh: '混淆 JavaScript 代码，保护源代码不被轻易理解和逆向工程',
      en: 'Obfuscate JavaScript code to protect from reverse engineering',
      ja: 'JavaScriptコードを難読化し、リバースエンジニアリングから保護',
    },
    category: 'developer',
    icon: Shield,
    path: '/tools/js-obfuscator',
    keywords: {
      zh: ['js', 'javascript', '混淆', '加密', '保护', '源码'],
      en: ['js', 'javascript', 'obfuscate', 'protect', 'minify', 'security'],
      ja: ['js', 'javascript', '難読化', '保護', 'セキュリティ', 'ソースコード'],
    },
    component: JsObfuscatorTool,
    isNew: true,
  },
  {
    id: 'css-minifier',
    name: {
      zh: 'CSS 压缩工具',
      en: 'CSS Minifier',
      ja: 'CSS圧縮ツール',
    },
    description: {
      zh: '压缩和优化 CSS 代码，减小文件体积，提高网页加载速度',
      en: 'Minify and optimize CSS code to reduce file size and improve loading speed',
      ja: 'CSSコードを圧縮・最適化し、ファイルサイズを削減して読み込み速度を向上',
    },
    category: 'developer',
    icon: FileCode,
    path: '/tools/css-minifier',
    keywords: {
      zh: ['css', '压缩', '优化', '最小化', 'minify', '性能'],
      en: ['css', 'minify', 'compress', 'optimize', 'minimize', 'performance'],
      ja: ['css', '圧縮', '最適化', 'ミニファイ', 'パフォーマンス'],
    },
    component: CssMinifierTool,
    isNew: true,
  },
  {
    id: 'text-case',
    name: {
      zh: '文本大小写转换',
      en: 'Text Case Converter',
      ja: 'テキスト大文字小文字変換',
    },
    description: {
      zh: '转换文本的大小写格式，支持多种编程命名规范和文本样式',
      en: 'Convert text case with support for various naming conventions and styles',
      ja: 'テキストの大文字小文字を変換、各種命名規則とスタイルに対応',
    },
    category: 'text',
    icon: Type,
    path: '/tools/text-case',
    keywords: {
      zh: ['大小写', '驼峰', '蛇形', '标题', '转换', 'camelCase', 'snake_case'],
      en: ['case', 'camel', 'snake', 'title', 'convert', 'uppercase', 'lowercase'],
      ja: ['大文字', '小文字', 'キャメルケース', 'スネークケース', '変換'],
    },
    component: TextCaseTool,
    isNew: true,
  },
  {
    id: 'text-replace',
    name: {
      zh: '文本查找替换',
      en: 'Text Find & Replace',
      ja: 'テキスト検索置換',
    },
    description: {
      zh: '强大的文本查找和替换工具，支持正则表达式和多种匹配选项',
      en: 'Powerful text find and replace tool with regex support',
      ja: '正規表現対応の強力なテキスト検索置換ツール',
    },
    category: 'text',
    icon: Search,
    path: '/tools/text-replace',
    keywords: {
      zh: ['查找', '替换', '搜索', '正则', 'regex', '批量替换'],
      en: ['find', 'replace', 'search', 'regex', 'batch', 'text'],
      ja: ['検索', '置換', '正規表現', 'regex', 'バッチ', 'テキスト'],
    },
    component: TextReplaceTool,
    isNew: true,
  },
  {
    id: 'text-statistics',
    name: {
      zh: '文本统计分析',
      en: 'Text Statistics Analyzer',
      ja: 'テキスト統計分析',
    },
    description: {
      zh: '全面分析文本的各项统计指标，包括字数、词频、阅读时间等',
      en: 'Comprehensive text analysis including word count, frequency, reading time',
      ja: 'テキストの統計指標を包括的に分析、単語数、頻度、読書時間など',
    },
    category: 'text',
    icon: FileText,
    path: '/tools/text-statistics',
    keywords: {
      zh: ['文本统计', '字数', '词频', '分析', '阅读时间', '词汇多样性'],
      en: ['text statistics', 'word count', 'frequency', 'analysis', 'reading time'],
      ja: ['テキスト統計', '文字数', '頻度', '分析', '読書時間', '語彙多様性'],
    },
    component: TextStatisticsTool,
    isNew: true,
  },
  {
    id: 'loan-calculator',
    name: {
      zh: '贷款计算器',
      en: 'Loan Calculator',
      ja: 'ローン計算機',
    },
    description: {
      zh: '计算房贷、车贷等各类贷款的月供、利息和还款计划',
      en: 'Calculate monthly payments, interest and repayment plans for loans',
      ja: '住宅ローン、自動車ローンなどの月々の支払い、利息、返済計画を計算',
    },
    category: 'lifestyle',
    icon: Calculator,
    path: '/tools/loan-calculator',
    keywords: {
      zh: ['贷款', '房贷', '车贷', '月供', '利息', '还款计划'],
      en: ['loan', 'mortgage', 'car loan', 'monthly payment', 'interest'],
      ja: ['ローン', '住宅ローン', '自動車ローン', '月々の支払い', '利息'],
    },
    component: LoanCalculatorTool,
    isNew: true,
  },
  {
    id: 'text-sort',
    name: {
      zh: '文本排序工具',
      en: 'Text Sort Tool',
      ja: 'テキストソートツール',
    },
    description: {
      zh: '对文本行进行排序，支持字母、数字、长度、日期等多种排序方式',
      en: 'Sort text lines with support for alphabetical, numerical, length, and date sorting',
      ja: 'テキスト行をソート、アルファベット、数値、長さ、日付など多様なソート方式対応',
    },
    category: 'text',
    icon: ArrowUpDown,
    path: '/tools/text-sort',
    keywords: {
      zh: ['排序', '文本排序', '升序', '降序', '随机', '去重'],
      en: ['sort', 'text sort', 'ascending', 'descending', 'random', 'unique'],
      ja: ['ソート', 'テキストソート', '昇順', '降順', 'ランダム', '重複削除'],
    },
    component: TextSortTool,
    isNew: true,
  },
  {
    id: 'text-deduplicator',
    name: {
      zh: '文本去重工具',
      en: 'Text Deduplicator',
      ja: 'テキスト重複削除ツール',
    },
    description: {
      zh: '快速去除文本中的重复项，支持精确匹配和模糊匹配',
      en: 'Remove duplicate text items with exact and fuzzy matching support',
      ja: 'テキストの重複項目を削除、完全一致とファジーマッチング対応',
    },
    category: 'text',
    icon: Filter,
    path: '/tools/text-deduplicator',
    keywords: {
      zh: ['去重', '重复', '去除重复', '模糊匹配', '文本处理'],
      en: ['deduplicate', 'duplicate', 'remove duplicates', 'fuzzy match', 'text'],
      ja: ['重複削除', '重複', 'ファジーマッチ', 'テキスト処理'],
    },
    component: TextDeduplicatorTool,
    isNew: true,
  },
  {
    id: 'text-split-merge',
    name: {
      zh: '文本分割合并',
      en: 'Text Split & Merge',
      ja: 'テキスト分割・結合',
    },
    description: {
      zh: '灵活地分割文本为多行，或将多行文本合并为一行',
      en: 'Flexibly split text into lines or merge multiple lines into one',
      ja: 'テキストを柔軟に分割、または複数行を1行に結合',
    },
    category: 'text',
    icon: FileText,
    path: '/tools/text-split-merge',
    keywords: {
      zh: ['分割', '合并', '拆分', '连接', 'CSV', '数组', 'SQL'],
      en: ['split', 'merge', 'join', 'divide', 'CSV', 'array', 'SQL'],
      ja: ['分割', '結合', '分離', '連結', 'CSV', '配列', 'SQL'],
    },
    component: TextSplitMergeTool,
    isNew: true,
  },
  {
    id: 'file-hash',
    name: {
      zh: '文件哈希校验',
      en: 'File Hash Checker',
      ja: 'ファイルハッシュチェッカー',
    },
    description: {
      zh: '计算文件的 MD5、SHA1、SHA256、SHA512 哈希值，验证文件完整性',
      en: 'Calculate MD5, SHA1, SHA256, SHA512 hash values to verify file integrity',
      ja: 'ファイルのMD5、SHA1、SHA256、SHA512ハッシュ値を計算し、完全性を検証',
    },
    category: 'converter',
    icon: FileCheck,
    path: '/tools/file-hash',
    keywords: {
      zh: ['文件哈希', '校验', 'MD5', 'SHA', '完整性', '验证'],
      en: ['file hash', 'checksum', 'MD5', 'SHA', 'integrity', 'verify'],
      ja: ['ファイルハッシュ', 'チェックサム', 'MD5', 'SHA', '完全性', '検証'],
    },
    component: FileHashTool,
    isNew: true,
  },
  {
    id: 'text-encoding',
    name: {
      zh: '文本编码转换',
      en: 'Text Encoding Converter',
      ja: 'テキストエンコーディング変換',
    },
    description: {
      zh: '支持多种编码格式的相互转换，包括 Base64、URL、Unicode、十六进制等',
      en: 'Convert between various encoding formats including Base64, URL, Unicode, Hex',
      ja: '様々なエンコーディング形式の相互変換、Base64、URL、Unicode、16進数など',
    },
    category: 'text',
    icon: FileType,
    path: '/tools/text-encoding',
    keywords: {
      zh: ['编码', '解码', 'Base64', 'URL', 'Unicode', '十六进制', '二进制', '摩斯密码'],
      en: ['encode', 'decode', 'Base64', 'URL', 'Unicode', 'hex', 'binary', 'morse'],
      ja: ['エンコード', 'デコード', 'Base64', 'URL', 'Unicode', '16進数', '2進数', 'モールス'],
    },
    component: TextEncodingTool,
    isNew: true,
  },
  {
    id: 'text-extractor',
    name: {
      zh: '文本提取工具',
      en: 'Text Extractor',
      ja: 'テキスト抽出ツール',
    },
    description: {
      zh: '从文本中智能提取邮箱、网址、电话号码等特定内容',
      en: 'Extract emails, URLs, phone numbers and more from text',
      ja: 'テキストからメール、URL、電話番号などを抽出',
    },
    category: 'text',
    icon: FileSearch,
    path: '/tools/text-extractor',
    keywords: {
      zh: ['提取', '邮箱', '网址', '电话', 'IP地址', '正则', '匹配'],
      en: ['extract', 'email', 'url', 'phone', 'IP address', 'regex', 'match'],
      ja: ['抽出', 'メール', 'URL', '電話', 'IPアドレス', '正規表現', 'マッチ'],
    },
    component: TextExtractorTool,
    isNew: true,
  },
  {
    id: 'text-template',
    name: {
      zh: '文本模板工具',
      en: 'Text Template Tool',
      ja: 'テキストテンプレートツール',
    },
    description: {
      zh: '创建和管理文本模板，使用变量快速生成个性化内容',
      en: 'Create and manage text templates with variables for personalized content',
      ja: 'テキストテンプレートを作成・管理、変数でパーソナライズされたコンテンツを生成',
    },
    category: 'text',
    icon: FileText,
    path: '/tools/text-template',
    keywords: {
      zh: ['模板', '变量', '邮件', '文档', '生成', '个性化'],
      en: ['template', 'variable', 'email', 'document', 'generate', 'personalize'],
      ja: ['テンプレート', '変数', 'メール', 'ドキュメント', '生成', 'パーソナライズ'],
    },
    component: TextTemplateTool,
    isNew: true,
  },
  {
    id: 'image-watermark',
    name: {
      zh: '图片水印工具',
      en: 'Image Watermark Tool',
      ja: '画像透かしツール',
    },
    description: {
      zh: '为图片添加文字或图片水印，保护您的版权',
      en: 'Add text or image watermarks to protect your copyright',
      ja: '画像にテキストまたは画像の透かしを追加して著作権を保護',
    },
    category: 'image',
    icon: Droplets,
    path: '/tools/image-watermark',
    keywords: {
      zh: ['水印', '图片水印', '版权保护', '文字水印', '图片保护'],
      en: ['watermark', 'image watermark', 'copyright', 'text watermark', 'protection'],
      ja: ['透かし', '画像透かし', '著作権', 'テキスト透かし', '保護'],
    },
    component: ImageWatermarkTool,
    isNew: true,
  },
  {
    id: 'image-cropper',
    name: {
      zh: '图片裁剪工具',
      en: 'Image Cropper',
      ja: '画像クロップツール',
    },
    description: {
      zh: '自由裁剪图片，支持多种宽高比和旋转功能',
      en: 'Crop images freely with various aspect ratios and rotation',
      ja: '画像を自由にクロップ、様々なアスペクト比と回転機能対応',
    },
    category: 'image',
    icon: Image,
    path: '/tools/image-cropper',
    keywords: {
      zh: ['裁剪', '图片裁剪', '剪切', '旋转', '缩放', '宽高比'],
      en: ['crop', 'image crop', 'cut', 'rotate', 'zoom', 'aspect ratio'],
      ja: ['クロップ', '画像クロップ', 'カット', '回転', 'ズーム', 'アスペクト比'],
    },
    component: ImageCropperTool,
    isNew: true,
  },
  {
    id: 'image-rotate-flip',
    name: {
      zh: '图片旋转翻转',
      en: 'Image Rotate & Flip',
      ja: '画像回転・反転',
    },
    description: {
      zh: '旋转和翻转图片，支持任意角度旋转和水平/垂直翻转',
      en: 'Rotate and flip images with any angle and horizontal/vertical flip',
      ja: '画像を任意の角度で回転、水平・垂直反転',
    },
    category: 'image',
    icon: RotateCw,
    path: '/tools/image-rotate-flip',
    keywords: {
      zh: ['旋转', '翻转', '图片旋转', '镜像', '变换', '角度'],
      en: ['rotate', 'flip', 'image rotate', 'mirror', 'transform', 'angle'],
      ja: ['回転', '反転', '画像回転', 'ミラー', '変換', '角度'],
    },
    component: ImageRotateFlipTool,
    isNew: true,
  },
  {
    id: 'image-filter',
    name: {
      zh: '图片滤镜效果',
      en: 'Image Filter Effects',
      ja: '画像フィルター効果',
    },
    description: {
      zh: '为图片添加各种滤镜效果，支持实时预览和自定义调整',
      en: 'Add various filter effects to images with real-time preview',
      ja: '画像に様々なフィルター効果を追加、リアルタイムプレビュー対応',
    },
    category: 'image',
    icon: Sliders,
    path: '/tools/image-filter',
    keywords: {
      zh: ['滤镜', '图片滤镜', '特效', '亮度', '对比度', '饱和度', '模糊'],
      en: ['filter', 'image filter', 'effects', 'brightness', 'contrast', 'saturation', 'blur'],
      ja: ['フィルター', '画像フィルター', 'エフェクト', '明度', 'コントラスト', '彩度', 'ぼかし'],
    },
    component: ImageFilterTool,
    isNew: true,
  },
  {
    id: 'image-stitch',
    name: {
      zh: '图片拼接工具',
      en: 'Image Stitcher',
      ja: '画像結合ツール',
    },
    description: {
      zh: '将多张图片拼接成一张，支持水平、垂直和网格布局',
      en: 'Stitch multiple images into one with horizontal, vertical and grid layouts',
      ja: '複数の画像を1枚に結合、水平・垂直・グリッドレイアウト対応',
    },
    category: 'image',
    icon: Grid,
    path: '/tools/image-stitch',
    keywords: {
      zh: ['拼接', '图片拼接', '合并', '拼图', '组合', '网格'],
      en: ['stitch', 'image stitch', 'merge', 'collage', 'combine', 'grid'],
      ja: ['結合', '画像結合', 'マージ', 'コラージュ', '組み合わせ', 'グリッド'],
    },
    component: ImageStitchTool,
    isNew: true,
  },
  {
    id: 'batch-image-converter',
    name: {
      zh: '图片格式批量转换',
      en: 'Batch Image Converter',
      ja: '画像一括変換',
    },
    description: {
      zh: '批量转换图片格式，支持调整尺寸和质量，一次处理多张图片',
      en: 'Batch convert image formats with size and quality adjustment',
      ja: '画像フォーマットを一括変換、サイズと品質調整対応',
    },
    category: 'image',
    icon: Images,
    path: '/tools/batch-image-converter',
    keywords: {
      zh: ['批量转换', '图片批量', '格式转换', '批量处理', '图片转换', '多张图片'],
      en: ['batch convert', 'bulk image', 'format conversion', 'batch process', 'multiple images'],
      ja: ['一括変換', '画像一括', 'フォーマット変換', 'バッチ処理', '複数画像'],
    },
    component: BatchImageConverterTool,
    isNew: true,
  },
  {
    id: 'image-to-base64',
    name: {
      zh: '图片 Base64 转换',
      en: 'Image to Base64',
      ja: '画像 Base64 変換',
    },
    description: {
      zh: '图片与 Base64 编码相互转换，支持多种图片格式',
      en: 'Convert between images and Base64 encoding, supports various formats',
      ja: '画像とBase64エンコーディングの相互変換、様々なフォーマット対応',
    },
    category: 'image',
    icon: FileImage,
    path: '/tools/image-to-base64',
    keywords: {
      zh: ['图片', 'base64', '编码', '解码', '转换', 'data url'],
      en: ['image', 'base64', 'encode', 'decode', 'convert', 'data url'],
      ja: ['画像', 'base64', 'エンコード', 'デコード', '変換', 'data url'],
    },
    component: ImageToBase64Tool,
    isNew: true,
  },
  {
    id: 'image-exif-viewer',
    name: {
      zh: '图片 EXIF 信息查看器',
      en: 'Image EXIF Viewer',
      ja: '画像 EXIF ビューアー',
    },
    description: {
      zh: '查看照片的 EXIF 元数据，包括相机型号、拍摄参数、GPS 位置等信息',
      en: 'View photo EXIF metadata including camera model, shooting parameters, GPS location',
      ja: '写真のEXIFメタデータを表示、カメラモデル、撮影パラメータ、GPS位置など',
    },
    category: 'image',
    icon: Camera,
    path: '/tools/image-exif-viewer',
    keywords: {
      zh: ['exif', '元数据', '照片信息', '相机', 'gps', '拍摄参数'],
      en: ['exif', 'metadata', 'photo info', 'camera', 'gps', 'shooting parameters'],
      ja: ['exif', 'メタデータ', '写真情報', 'カメラ', 'gps', '撮影パラメータ'],
    },
    component: ImageExifViewerTool,
    isNew: true,
  },
  {
    id: 'css-generator',
    name: {
      zh: 'CSS 生成器',
      en: 'CSS Generator',
      ja: 'CSSジェネレーター',
    },
    description: {
      zh: '可视化生成 CSS 样式代码，支持盒模型、背景、文本、布局、效果等属性',
      en: 'Visually generate CSS style code with support for box model, background, text, layout, effects',
      ja: 'ボックスモデル、背景、テキスト、レイアウト、エフェクトなどのCSSスタイルコードを視覚的に生成',
    },
    category: 'developer',
    icon: Palette,
    path: '/tools/css-generator',
    keywords: {
      zh: ['css', '样式', '生成器', '盒模型', '布局', 'flexbox', 'grid'],
      en: ['css', 'style', 'generator', 'box model', 'layout', 'flexbox', 'grid'],
      ja: ['css', 'スタイル', 'ジェネレーター', 'ボックスモデル', 'レイアウト', 'flexbox', 'grid'],
    },
    component: CssGeneratorTool,
    isNew: true,
  },
  {
    id: 'html-previewer',
    name: {
      zh: 'HTML 预览器',
      en: 'HTML Previewer',
      ja: 'HTMLプレビューアー',
    },
    description: {
      zh: '实时编辑和预览 HTML、CSS、JavaScript 代码，支持即时渲染和代码高亮',
      en: 'Real-time edit and preview HTML, CSS, JavaScript code with instant rendering',
      ja: 'HTML、CSS、JavaScriptコードをリアルタイムで編集・プレビュー、即時レンダリング対応',
    },
    category: 'developer',
    icon: Code2,
    path: '/tools/html-previewer',
    keywords: {
      zh: ['html', '预览', 'css', 'javascript', '编辑器', '实时', '代码'],
      en: ['html', 'preview', 'css', 'javascript', 'editor', 'real-time', 'code'],
      ja: ['html', 'プレビュー', 'css', 'javascript', 'エディター', 'リアルタイム', 'コード'],
    },
    component: HtmlPreviewerTool,
    isNew: true,
  },
  {
    id: 'code-beautifier',
    name: {
      zh: '代码美化工具',
      en: 'Code Beautifier',
      ja: 'コード整形ツール',
    },
    description: {
      zh: '格式化和美化各种编程语言的代码，支持 JavaScript、CSS、HTML、JSON、SQL 等',
      en: 'Format and beautify code for various programming languages including JavaScript, CSS, HTML, JSON, SQL',
      ja: 'JavaScript、CSS、HTML、JSON、SQLなど様々なプログラミング言語のコードをフォーマット・整形',
    },
    category: 'developer',
    icon: Sparkles,
    path: '/tools/code-beautifier',
    keywords: {
      zh: ['代码美化', '格式化', 'prettier', 'beautify', 'javascript', 'css', 'html', 'json', 'sql'],
      en: ['code beautifier', 'formatter', 'prettier', 'beautify', 'javascript', 'css', 'html', 'json', 'sql'],
      ja: ['コード整形', 'フォーマット', 'prettier', 'beautify', 'javascript', 'css', 'html', 'json', 'sql'],
    },
    component: CodeBeautifierTool,
    isNew: true,
  },
  {
    id: 'api-tester',
    name: {
      zh: 'API 测试工具',
      en: 'API Tester',
      ja: 'APIテスター',
    },
    description: {
      zh: '在线测试 REST API 接口，支持各种 HTTP 方法和自定义请求头',
      en: 'Test REST API endpoints online with various HTTP methods and custom headers',
      ja: 'REST APIエンドポイントをオンラインでテスト、各種HTTPメソッドとカスタムヘッダー対応',
    },
    category: 'developer',
    icon: Send,
    path: '/tools/api-tester',
    keywords: {
      zh: ['api', '接口测试', 'rest', 'http', '请求', 'postman', 'curl'],
      en: ['api', 'rest', 'http', 'request', 'test', 'postman', 'curl'],
      ja: ['api', 'rest', 'http', 'リクエスト', 'テスト', 'postman', 'curl'],
    },
    component: ApiTesterTool,
    isNew: true,
  },
  {
    id: 'js-module-generator',
    name: {
      zh: 'JS 模块生成器',
      en: 'JS Module Generator',
      ja: 'JSモジュールジェネレーター',
    },
    description: {
      zh: '快速生成标准的 JavaScript 模块模板，支持 CommonJS、ES Module 和 UMD 格式',
      en: 'Generate standard JavaScript module templates with CommonJS, ES Module, and UMD support',
      ja: 'CommonJS、ES Module、UMD形式に対応した標準的なJavaScriptモジュールテンプレートを生成',
    },
    category: 'developer',
    icon: Code,
    path: '/tools/js-module-generator',
    keywords: {
      zh: ['js', 'javascript', '模块', '生成器', 'npm', 'package', '脚手架'],
      en: ['js', 'javascript', 'module', 'generator', 'npm', 'package', 'scaffold'],
      ja: ['js', 'javascript', 'モジュール', 'ジェネレーター', 'npm', 'パッケージ', 'スキャフォールド'],
    },
    component: JsModuleGeneratorTool,
    isNew: true,
  },
  {
    id: 'git-commit-generator',
    name: {
      zh: 'Git 提交信息生成器',
      en: 'Git Commit Generator',
      ja: 'Git コミットジェネレーター',
    },
    description: {
      zh: '基于 Conventional Commits 规范生成标准化的 Git 提交信息',
      en: 'Generate standardized Git commit messages based on Conventional Commits',
      ja: 'Conventional Commitsに基づいて標準化されたGitコミットメッセージを生成',
    },
    category: 'developer',
    icon: GitCommit,
    path: '/tools/git-commit-generator',
    keywords: {
      zh: ['git', 'commit', '提交', '版本控制', 'conventional commits', '规范'],
      en: ['git', 'commit', 'version control', 'conventional commits', 'standard'],
      ja: ['git', 'コミット', 'バージョン管理', 'conventional commits', '標準'],
    },
    component: GitCommitGeneratorTool,
    isNew: true,
  },
  {
    id: 'code-minifier',
    name: {
      zh: '代码压缩器',
      en: 'Code Minifier',
      ja: 'コード圧縮ツール',
    },
    description: {
      zh: '压缩 JavaScript、CSS、HTML、JSON 代码，减小文件体积',
      en: 'Minify JavaScript, CSS, HTML, JSON code to reduce file size',
      ja: 'JavaScript、CSS、HTML、JSONコードを圧縮してファイルサイズを縮小',
    },
    category: 'developer',
    icon: FileCode,
    path: '/tools/code-minifier',
    keywords: {
      zh: ['代码压缩', '代码优化', 'minify', 'javascript', 'css', 'html', 'json'],
      en: ['code minifier', 'code optimization', 'minify', 'javascript', 'css', 'html', 'json'],
      ja: ['コード圧縮', 'コード最適化', 'minify', 'javascript', 'css', 'html', 'json'],
    },
    component: CodeMinifierTool,
    isNew: true,
  },
  {
    id: 'code-snippet-manager',
    name: {
      zh: '代码片段管理器',
      en: 'Code Snippet Manager',
      ja: 'コードスニペット管理ツール',
    },
    description: {
      zh: '管理和组织您的代码片段，支持多种编程语言和标签分类',
      en: 'Manage and organize your code snippets with multi-language support and tag categorization',
      ja: '複数のプログラミング言語とタグ分类をサポートしたコードスニペットの管理・整理',
    },
    category: 'developer',
    icon: Code,
    path: '/tools/code-snippet-manager',
    keywords: {
      zh: ['代码片段', '代码管理', '片段收藏', '代码整理', '开发工具', '编程助手'],
      en: ['code snippet', 'code management', 'snippet collection', 'code organization', 'development tools', 'programming assistant'],
      ja: ['コードスニペット', 'コード管理', 'スニペット収集', 'コード整理', '開発ツール', 'プログラミングアシスタント'],
    },
    component: CodeSnippetManagerTool,
    isNew: true,
  },
  {
    id: 'cron-generator',
    name: {
      zh: 'Cron 表达式生成器',
      en: 'Cron Expression Generator',
      ja: 'Cron表現ジェネレーター',
    },
    description: {
      zh: '生成和解析 Cron 表达式，设置定时任务执行规则',
      en: 'Generate and parse Cron expressions for scheduled task rules',
      ja: 'Cron表現を生成・解析し、スケジュールタスクのルールを設定',
    },
    category: 'developer',
    icon: Clock,
    path: '/tools/cron-generator',
    keywords: {
      zh: ['cron', '定时任务', '表达式', '调度', '计划任务', '定时器'],
      en: ['cron', 'scheduled task', 'expression', 'scheduler', 'timer', 'automation'],
      ja: ['cron', 'スケジュールタスク', '表現', 'スケジューラー', 'タイマー', '自動化'],
    },
    component: CronGeneratorTool,
    isNew: true,
  },
  {
    id: 'jwt-decoder',
    name: {
      zh: 'JWT 解析器',
      en: 'JWT Decoder',
      ja: 'JWTデコーダー',
    },
    description: {
      zh: '解析和验证 JSON Web Tokens，查看 Header、Payload 和签名信息',
      en: 'Parse and validate JSON Web Tokens, view Header, Payload and signature information',
      ja: 'JSON Web Tokensを解析・検証し、Header、Payload、署名情報を表示',
    },
    category: 'developer',
    icon: Shield,
    path: '/tools/jwt-decoder',
    keywords: {
      zh: ['jwt', 'json web token', '解析', '验证', '令牌', '身份验证'],
      en: ['jwt', 'json web token', 'decode', 'verify', 'token', 'authentication'],
      ja: ['jwt', 'json web token', 'デコード', '検証', 'トークン', '認証'],
    },
    component: JwtDecoderTool,
    isNew: true,
  },
  {
    id: 'package-manager',
    name: {
      zh: '包管理器助手',
      en: 'Package Manager Helper',
      ja: 'パッケージマネージャーヘルパー',
    },
    description: {
      zh: '快速生成不同包管理器的命令，支持 npm、yarn、pnpm、bun',
      en: 'Generate commands for different package managers including npm, yarn, pnpm, bun',
      ja: 'npm、yarn、pnpm、bunなど異なるパッケージマネージャーのコマンドを生成',
    },
    category: 'developer',
    icon: Package,
    path: '/tools/package-manager',
    keywords: {
      zh: ['包管理', 'npm', 'yarn', 'pnpm', 'bun', '依赖管理', '命令生成'],
      en: ['package manager', 'npm', 'yarn', 'pnpm', 'bun', 'dependency management', 'command generation'],
      ja: ['パッケージマネージャー', 'npm', 'yarn', 'pnpm', 'bun', '依存関係管理', 'コマンド生成'],
    },
    component: PackageManagerTool,
    isNew: true,
  },
  {
    id: 'aes-encrypt',
    name: {
      zh: 'AES 加密解密',
      en: 'AES Encryption/Decryption',
      ja: 'AES暗号化/復号化',
    },
    description: {
      zh: '使用 AES 算法进行数据加密和解密，支持多种加密模式和填充方式',
      en: 'Encrypt and decrypt data using AES algorithm with multiple modes and padding options',
      ja: '複数のモードとパディングオプションでAESアルゴリズムを使用してデータを暗号化・復号化',
    },
    category: 'crypto',
    icon: Shield,
    path: '/tools/aes-encrypt',
    keywords: {
      zh: ['aes', '加密', '解密', '对称加密', '安全', '密钥', 'cbc', 'ecb'],
      en: ['aes', 'encrypt', 'decrypt', 'symmetric encryption', 'security', 'key', 'cbc', 'ecb'],
      ja: ['aes', '暗号化', '復号化', '対称暗号化', 'セキュリティ', '鍵', 'cbc', 'ecb'],
    },
    component: AESEncryptTool,
    isNew: true,
  },
  {
    id: 'rsa-key-generator',
    name: {
      zh: 'RSA 密钥生成器',
      en: 'RSA Key Generator',
      ja: 'RSA鍵ジェネレーター',
    },
    description: {
      zh: '生成 RSA 公钥和私钥对，用于加密、解密和数字签名',
      en: 'Generate RSA public and private key pairs for encryption, decryption and digital signatures',
      ja: 'RSA公開鍵と秘密鍵のペアを生成、暗号化・復号化・デジタル署名に使用',
    },
    category: 'crypto',
    icon: Key,
    path: '/tools/rsa-key-generator',
    keywords: {
      zh: ['rsa', '密钥生成', '公钥', '私钥', '非对称加密', '数字签名', 'pem'],
      en: ['rsa', 'key generator', 'public key', 'private key', 'asymmetric encryption', 'digital signature', 'pem'],
      ja: ['rsa', '鍵生成', '公開鍵', '秘密鍵', '非対称暗号化', 'デジタル署名', 'pem'],
    },
    component: RSAKeyGeneratorTool,
    isNew: true,
  },
  {
    id: 'hmac-generator',
    name: {
      zh: 'HMAC 生成器',
      en: 'HMAC Generator',
      ja: 'HMACジェネレーター',
    },
    description: {
      zh: '生成基于哈希的消息认证码（HMAC），用于验证数据完整性和真实性',
      en: 'Generate Hash-based Message Authentication Code (HMAC) for data integrity and authenticity',
      ja: 'ハッシュベースメッセージ認証コード（HMAC）を生成し、データの完全性と真正性を検証',
    },
    category: 'crypto',
    icon: Hash,
    path: '/tools/hmac-generator',
    keywords: {
      zh: ['hmac', '消息认证码', '哈希', '完整性', '签名', 'sha256', 'api签名'],
      en: ['hmac', 'message authentication code', 'hash', 'integrity', 'signature', 'sha256', 'api signature'],
      ja: ['hmac', 'メッセージ認証コード', 'ハッシュ', '完全性', '署名', 'sha256', 'api署名'],
    },
    component: HMACGeneratorTool,
    isNew: true,
  },
  {
    id: 'password-strength',
    name: {
      zh: '密码强度检测器',
      en: 'Password Strength Checker',
      ja: 'パスワード強度チェッカー',
    },
    description: {
      zh: '检测密码强度，提供安全性评估和改进建议，生成强密码',
      en: 'Check password strength with security assessment and improvement suggestions',
      ja: 'パスワード強度をチェックし、セキュリティ評価と改善提案を提供',
    },
    category: 'crypto',
    icon: Shield,
    path: '/tools/password-strength',
    keywords: {
      zh: ['密码强度', '密码检测', '安全', '密码评估', '强密码', '密码分析'],
      en: ['password strength', 'password checker', 'security', 'password assessment', 'strong password', 'password analysis'],
      ja: ['パスワード強度', 'パスワードチェック', 'セキュリティ', 'パスワード評価', '強いパスワード', 'パスワード分析'],
    },
    component: PasswordStrengthTool,
    isNew: true,
  },
  {
    id: 'digital-signature-verifier',
    name: {
      zh: '数字签名验证器',
      en: 'Digital Signature Verifier',
      ja: 'デジタル署名検証ツール',
    },
    description: {
      zh: '生成、签名和验证数字签名，确保消息完整性和身份认证',
      en: 'Generate, sign and verify digital signatures for message integrity and authentication',
      ja: 'デジタル署名の生成、署名、検証を行い、メッセージの完全性と認証を確保',
    },
    category: 'crypto',
    icon: Shield,
    path: '/tools/digital-signature-verifier',
    keywords: {
      zh: ['数字签名', '签名验证', 'RSA', 'ECDSA', 'Ed25519', '公钥', '私钥', '身份认证'],
      en: ['digital signature', 'signature verification', 'RSA', 'ECDSA', 'Ed25519', 'public key', 'private key', 'authentication'],
      ja: ['デジタル署名', '署名検証', 'RSA', 'ECDSA', 'Ed25519', '公開鍵', '秘密鍵', '認証'],
    },
    component: DigitalSignatureVerifierTool,
    isNew: true,
  },
  {
    id: 'ssl-certificate-analyzer',
    name: {
      zh: 'SSL/TLS 证书解析器',
      en: 'SSL/TLS Certificate Analyzer',
      ja: 'SSL/TLS証明書アナライザー',
    },
    description: {
      zh: '分析 SSL/TLS 证书信息，检查证书有效性和安全性',
      en: 'Analyze SSL/TLS certificate information, check validity and security',
      ja: 'SSL/TLS証明書情報を分析し、有効性とセキュリティをチェック',
    },
    category: 'network',
    icon: Shield,
    path: '/tools/ssl-certificate-analyzer',
    keywords: {
      zh: ['ssl', 'tls', '证书', 'https', '安全', '加密', '证书链', 'CA'],
      en: ['ssl', 'tls', 'certificate', 'https', 'security', 'encryption', 'certificate chain', 'CA'],
      ja: ['ssl', 'tls', '証明書', 'https', 'セキュリティ', '暗号化', '証明書チェーン', 'CA'],
    },
    component: SSLCertificateAnalyzerTool,
    isNew: true,
  },
  {
    id: 'file-encryptor',
    name: {
      zh: '文件加密解密',
      en: 'File Encryptor/Decryptor',
      ja: 'ファイル暗号化/復号化',
    },
    description: {
      zh: '使用 AES 加密算法安全地加密和解密文件内容',
      en: 'Securely encrypt and decrypt file contents using AES encryption',
      ja: 'AES暗号化を使用してファイルの内容を安全に暗号化・復号化',
    },
    category: 'crypto',
    icon: Lock,
    path: '/tools/file-encryptor',
    keywords: {
      zh: ['文件加密', '文件解密', 'AES', '加密', '解密', '文件保护', '安全'],
      en: ['file encryption', 'file decryption', 'AES', 'encrypt', 'decrypt', 'file protection', 'security'],
      ja: ['ファイル暗号化', 'ファイル復号化', 'AES', '暗号化', '復号化', 'ファイル保護', 'セキュリティ'],
    },
    component: FileEncryptorTool,
    isNew: true,
  },
  {
    id: 'percentage-calculator',
    name: {
      zh: '百分比计算器',
      en: 'Percentage Calculator',
      ja: 'パーセンテージ計算機',
    },
    description: {
      zh: '计算百分比、增减率、比例等各种百分比相关运算',
      en: 'Calculate percentages, increase/decrease rates, ratios and more',
      ja: 'パーセンテージ、増減率、比率などの計算',
    },
    category: 'calculator',
    icon: Percent,
    path: '/tools/percentage-calculator',
    keywords: {
      zh: ['百分比', '百分比计算', '增长率', '减少率', '比例', '百分比差异'],
      en: ['percentage', 'percent', 'increase rate', 'decrease rate', 'ratio', 'percentage difference'],
      ja: ['パーセンテージ', 'パーセント', '増加率', '減少率', '比率', 'パーセンテージ差'],
    },
    component: PercentageCalculatorTool,
    isNew: true,
  },
  {
    id: 'statistics-calculator',
    name: {
      zh: '统计计算器',
      en: 'Statistics Calculator',
      ja: '統計計算機',
    },
    description: {
      zh: '计算数据集的各种统计指标，包括均值、中位数、标准差、偏度、峰度等',
      en: 'Calculate various statistical indicators including mean, median, standard deviation, skewness, kurtosis',
      ja: '平均値、中央値、標準偏差、歪度、尖度など様々な統計指標を計算',
    },
    category: 'other',
    icon: BarChart3,
    path: '/tools/statistics-calculator',
    keywords: {
      zh: ['统计', '统计计算', '均值', '中位数', '标准差', '方差', '偏度', '峰度', '四分位数'],
      en: ['statistics', 'statistical calculation', 'mean', 'median', 'standard deviation', 'variance', 'skewness', 'kurtosis', 'quartiles'],
      ja: ['統計', '統計計算', '平均値', '中央値', '標準偏差', '分散', '歪度', '尖度', '四分位数'],
    },
    component: StatisticsCalculatorTool,
    isNew: true,
  },
  {
    id: 'geometry-calculator',
    name: {
      zh: '几何计算器',
      en: 'Geometry Calculator',
      ja: '幾何学計算機',
    },
    description: {
      zh: '计算各种几何图形的面积、周长、体积和表面积，支持平面和立体图形',
      en: 'Calculate area, perimeter, volume and surface area of various geometric shapes',
      ja: '様々な幾何学図形の面积、周長、体積、表面積を計算',
    },
    category: 'calculator',
    icon: Calculator,
    path: '/tools/geometry-calculator',
    keywords: {
      zh: ['几何', '几何计算', '面积', '周长', '体积', '表面积', '平面图形', '立体图形', '数学'],
      en: ['geometry', 'geometric calculation', 'area', 'perimeter', 'volume', 'surface area', '2d shapes', '3d shapes', 'math'],
      ja: ['幾何学', '幾何計算', '面積', '周長', '体積', '表面積', '平面図形', '立体図形', '数学'],
    },
    component: GeometryCalculatorTool,
    isNew: true,
  },
  {
    id: 'scientific-calculator',
    name: {
      zh: '科学计算器',
      en: 'Scientific Calculator',
      ja: '科学計算機',
    },
    description: {
      zh: '功能强大的科学计算器，支持三角函数、对数、指数、内存等高级数学运算',
      en: 'Advanced scientific calculator with trigonometric, logarithmic, exponential functions and memory',
      ja: '三角関数、対数、指数、メモリなどの高度な数学演算対応の科学計算機',
    },
    category: 'calculator',
    icon: Calculator,
    path: '/tools/scientific-calculator',
    keywords: {
      zh: ['科学计算器', '三角函数', '对数', '指数', '内存', '数学', '计算', '函数'],
      en: ['scientific calculator', 'trigonometric', 'logarithm', 'exponential', 'memory', 'math', 'calculation', 'function'],
      ja: ['科学計算機', '三角関数', '対数', '指数', 'メモリ', '数学', '計算', '関数'],
    },
    component: ScientificCalculatorTool,
    isNew: true,
  },
  {
    id: 'compound-interest-calculator',
    name: {
      zh: '复利计算器',
      en: 'Compound Interest Calculator',
      ja: '複利計算機',
    },
    description: {
      zh: '计算复利投资收益，支持定期投入和多种复利频率，包含投资建议和年度明细',
      en: 'Calculate compound interest returns with regular contributions and various compounding frequencies',
      ja: '複利投資収益を計算、定期投入と様々な複利頻度に対応、投資アドバイスと年次詳細を含む',
    },
    category: 'calculator',
    icon: TrendingUp,
    path: '/tools/compound-interest-calculator',
    keywords: {
      zh: ['复利', '复利计算', '投资', '理财', '收益', '本金', '利息', '投资回报', '退休规划'],
      en: ['compound interest', 'investment', 'finance', 'returns', 'principal', 'interest', 'ROI', 'retirement planning'],
      ja: ['複利', '投資', '金融', '収益', '元本', '利息', 'ROI', '退職計画'],
    },
    component: CompoundInterestCalculatorTool,
    isNew: true,
  },
  {
    id: 'time-calculator',
    name: {
      zh: '时间计算器',
      en: 'Time Calculator',
      ja: '時間計算機',
    },
    description: {
      zh: '多功能时间计算工具，支持时间间隔、时间加减、工作日和时区转换',
      en: 'Multi-functional time calculation tool with interval, arithmetic, workday and timezone conversion',
      ja: '時間間隔、時間加減算、営業日、タイムゾーン変換対応の多機能時間計算ツール',
    },
    category: 'calculator',
    icon: Clock,
    path: '/tools/time-calculator',
    keywords: {
      zh: ['时间计算', '时间间隔', '时间加减', '工作日', '时区转换', '日期计算', '时间差'],
      en: ['time calculator', 'time interval', 'time arithmetic', 'workday', 'timezone conversion', 'date calculation', 'time difference'],
      ja: ['時間計算', '時間間隔', '時間加減算', '営業日', 'タイムゾーン変換', '日付計算', '時差'],
    },
    component: TimeCalculatorTool,
    isNew: true,
  },
  {
    id: 'fitness-calculator',
    name: {
      zh: '健身计算器',
      en: 'Fitness Calculator',
      ja: 'フィットネス計算機',
    },
    description: {
      zh: '全面的健身计算工具，包括基础代谢率、卡路里消耗、身体成分分析',
      en: 'Comprehensive fitness calculation tool with BMR, calorie burn, and body composition analysis',
      ja: 'BMR、カロリー消費、体組成分析を含む包括的なフィットネス計算ツール',
    },
    category: 'calculator',
    icon: Heart,
    path: '/tools/fitness-calculator',
    keywords: {
      zh: ['健身', '基础代谢率', 'BMR', '卡路里', '体脂率', '运动', '健康', '代谢'],
      en: ['fitness', 'BMR', 'basal metabolic rate', 'calories', 'body fat', 'exercise', 'health', 'metabolism'],
      ja: ['フィットネス', 'BMR', '基礎代謝率', 'カロリー', '体脂肪', '運動', '健康', '代謝'],
    },
    component: FitnessCalculatorTool,
    isNew: true,
  },
  {
    id: 'nutrition-calculator',
    name: {
      zh: '营养计算器',
      en: 'Nutrition Calculator',
      ja: '栄養計算機',
    },
    description: {
      zh: '计算营养需求、食物营养成分和营养素比例分布',
      en: 'Calculate nutrition requirements, food nutrition and macronutrient distribution',
      ja: '栄養需要量、食品栄養成分、栄養素比率分布を計算',
    },
    category: 'calculator',
    icon: Apple,
    path: '/tools/nutrition-calculator',
    keywords: {
      zh: ['营养', '营养计算', '食物营养', '营养素', '卡路里', '蛋白质', '脂肪', '碳水化合物'],
      en: ['nutrition', 'nutrition calculator', 'food nutrition', 'nutrients', 'calories', 'protein', 'fat', 'carbohydrates'],
      ja: ['栄養', '栄養計算', '食品栄養', '栄養素', 'カロリー', 'タンパク質', '脂肪', '炭水化合物'],
    },
    component: NutritionCalculatorTool,
    isNew: true,
  },
  {
    id: 'file-size-calculator',
    name: {
      zh: '文件大小计算器',
      en: 'File Size Calculator',
      ja: 'ファイルサイズ計算機',
    },
    description: {
      zh: '转换不同文件大小单位，支持二进制（1024）和十进制（1000）转换标准',
      en: 'Convert between different file size units with binary (1024) and decimal (1000) standards',
      ja: '異なるファイルサイズ単位を変換、バイナリ（1024）と十進制（1000）標準に対応',
    },
    category: 'converter',
    icon: Database,
    path: '/tools/file-size-calculator',
    keywords: {
      zh: ['文件大小', '文件大小计算', '单位转换', '字节', 'KB', 'MB', 'GB', 'TB', '存储空间'],
      en: ['file size', 'file size calculator', 'unit conversion', 'bytes', 'KB', 'MB', 'GB', 'TB', 'storage'],
      ja: ['ファイルサイズ', 'ファイルサイズ計算', '単位変換', 'バイト', 'KB', 'MB', 'GB', 'TB', 'ストレージ'],
    },
    component: FileSizeCalculatorTool,
    isNew: true,
  },
  {
    id: 'world-clock',
    name: {
      zh: '世界时钟',
      en: 'World Clock',
      ja: '世界時計',
    },
    description: {
      zh: '查看全球不同时区的当前时间，支持添加和管理多个城市',
      en: 'View current time in different time zones around the world with city management',
      ja: '世界各地の異なるタイムゾーンの現在時刻を表示、都市管理機能付き',
    },
    category: 'lifestyle',
    icon: Globe,
    path: '/tools/world-clock',
    keywords: {
      zh: ['世界时钟', '时区', '时间', '全球时间', '时差', '城市时间', '国际时间'],
      en: ['world clock', 'timezone', 'time', 'global time', 'time difference', 'city time', 'international time'],
      ja: ['世界時計', 'タイムゾーン', '時間', 'グローバル時間', '時差', '都市時間', '国際時間'],
    },
    component: WorldClockTool,
    isNew: true,
  },
  {
    id: 'file-format-converter',
    name: {
      zh: '文件格式转换器',
      en: 'File Format Converter',
      ja: 'ファイル形式変換器',
    },
    description: {
      zh: '支持多种文件格式之间的转换，包括文本、图像、音频、视频等格式',
      en: 'Convert between various file formats including text, images, audio, video, and more',
      ja: 'テキスト、画像、音声、動画など、さまざまなファイル形式間での変換をサポート',
    },
    category: 'converter',
    icon: FileType,
    path: '/tools/file-format-converter',
    keywords: {
      zh: ['文件转换', '格式转换', '文件格式', '转换器', '文档转换', '图片转换', '音频转换', '视频转换'],
      en: ['file converter', 'format conversion', 'file format', 'converter', 'document conversion', 'image conversion', 'audio conversion', 'video conversion'],
      ja: ['ファイル変換', 'フォーマット変換', 'ファイル形式', '変換器', '文書変換', '画像変換', '音声変換', '動画変換'],
    },
    component: FileFormatConverterTool,
    isNew: true,
  },
  {
    id: 'file-batch-rename',
    name: {
      zh: '文件批量重命名',
      en: 'File Batch Rename',
      ja: 'ファイル一括リネーム',
    },
    description: {
      zh: '批量重命名多个文件，支持多种命名规则和预设模板',
      en: 'Batch rename multiple files with various naming rules and preset templates',
      ja: '複数のファイルを一括リネーム、様々な命名規則とプリセットテンプレート対応',
    },
    category: 'converter',
    icon: Edit3,
    path: '/tools/file-batch-rename',
    keywords: {
      zh: ['文件重命名', '批量重命名', '文件管理', '重命名规则', '文件批处理', '名称修改'],
      en: ['file rename', 'batch rename', 'file management', 'rename rules', 'file batch processing', 'name modification'],
      ja: ['ファイルリネーム', '一括リネーム', 'ファイル管理', 'リネーム規則', 'ファイル一括処理', '名前変更'],
    },
    component: FileBatchRenameTool,
    isNew: true,
  },
  {
    id: 'password-manager',
    name: {
      zh: '密码管理器',
      en: 'Password Manager',
      ja: 'パスワードマネージャー',
    },
    description: {
      zh: '安全地生成、存储和管理您的密码，支持密码强度分析和安全检查',
      en: 'Securely generate, store and manage your passwords with strength analysis and security audit',
      ja: 'パスワードを安全に生成・保存・管理、強度分析とセキュリティ監査機能付き',
    },
    category: 'productivity',
    icon: Lock,
    path: '/tools/password-manager',
    keywords: {
      zh: ['密码管理', '密码生成', '密码存储', '安全分析', '密码强度', '账号管理', '隐私保护'],
      en: ['password manager', 'password generator', 'password storage', 'security analysis', 'password strength', 'account management', 'privacy protection'],
      ja: ['パスワード管理', 'パスワード生成', 'パスワード保存', 'セキュリティ分析', 'パスワード強度', 'アカウント管理', 'プライバシー保護'],
    },
    component: PasswordManagerTool,
    isNew: true,
  },
  {
    id: 'weather-forecast',
    name: {
      zh: '天气预报',
      en: 'Weather Forecast',
      ja: '天気予報',
    },
    description: {
      zh: '查看全球各地的实时天气和未来几天的天气预报',
      en: 'Check real-time weather and forecasts for locations worldwide',
      ja: '世界中の場所のリアルタイム天気と予報を確認',
    },
    category: 'lifestyle',
    icon: CloudSun,
    path: '/tools/weather-forecast',
    keywords: {
      zh: ['天气', '天气预报', '气温', '降雨', '风速', '湿度', '天气查询', '气象'],
      en: ['weather', 'weather forecast', 'temperature', 'rain', 'wind', 'humidity', 'weather check', 'meteorology'],
      ja: ['天気', '天気予報', '気温', '降雨', '風速', '湿度', '天気確認', '気象'],
    },
    component: WeatherForecastTool,
    isNew: true,
  },
  {
    id: 'ebook-reader',
    name: {
      zh: '电子书阅读器',
      en: 'E-book Reader',
      ja: '電子書籍リーダー',
    },
    description: {
      zh: '在线阅读电子书，支持多种格式、书签、笔记和阅读进度管理',
      en: 'Read e-books online with support for multiple formats, bookmarks, notes and reading progress',
      ja: 'オンラインで電子書籍を読む、複数フォーマット、ブックマーク、メモ、読書進捗管理対応',
    },
    category: 'productivity',
    icon: Book,
    path: '/tools/ebook-reader',
    keywords: {
      zh: ['电子书', '阅读器', 'epub', 'pdf', '书签', '笔记', '阅读', '电子阅读'],
      en: ['ebook', 'reader', 'epub', 'pdf', 'bookmark', 'notes', 'reading', 'e-reader'],
      ja: ['電子書籍', 'リーダー', 'epub', 'pdf', 'ブックマーク', 'メモ', '読書', '電子リーダー'],
    },
    component: EbookReaderTool,
    isNew: true,
  },
  {
    id: 'pdf-converter',
    name: {
      zh: 'PDF 转换器',
      en: 'PDF Converter',
      ja: 'PDF変換器',
    },
    description: {
      zh: '在线转换 PDF 文件，支持多种格式互转',
      en: 'Convert PDF files online with support for multiple formats',
      ja: 'PDFファイルをオンラインで変換、複数フォーマット対応',
    },
    category: 'converter',
    icon: FileDown,
    path: '/tools/pdf-converter',
    keywords: {
      zh: ['pdf', '转换', 'word', 'excel', 'ppt', '图片', '文档转换', 'pdf转换'],
      en: ['pdf', 'convert', 'word', 'excel', 'ppt', 'image', 'document conversion', 'pdf converter'],
      ja: ['pdf', '変換', 'word', 'excel', 'ppt', '画像', 'ドキュメント変換', 'pdf変換'],
    },
    component: PdfConverterTool,
    isNew: true,
  },
  {
    id: 'expense-tracker',
    name: {
      zh: '记账本',
      en: 'Expense Tracker',
      ja: '家計簿',
    },
    description: {
      zh: '记录和管理您的收支，掌握财务状况',
      en: 'Track and manage your income and expenses, master your financial situation',
      ja: '収支を記録・管理し、財務状況を把握',
    },
    category: 'lifestyle',
    icon: Receipt,
    path: '/tools/expense-tracker',
    keywords: {
      zh: ['记账', '账本', '收支', '理财', '财务', '支出', '收入', '预算'],
      en: ['expense', 'tracker', 'budget', 'finance', 'income', 'spending', 'money', 'accounting'],
      ja: ['家計簿', '収支', '予算', '財務', '支出', '収入', '会計', 'お金'],
    },
    component: ExpenseTrackerTool,
    isNew: true,
  },
  {
    id: 'habit-tracker',
    name: {
      zh: '习惯追踪器',
      en: 'Habit Tracker',
      ja: '習慣トラッカー',
    },
    description: {
      zh: '追踪和培养良好习惯，可视化进度和统计分析',
      en: 'Track and build good habits with visual progress and statistics',
      ja: '良い習慣を追跡・育成、進捗の可視化と統計分析',
    },
    category: 'lifestyle',
    icon: Target,
    path: '/tools/habit-tracker',
    keywords: {
      zh: ['习惯', '追踪', '打卡', '目标', '坚持', '自律', '进度', '统计'],
      en: ['habit', 'tracker', 'goal', 'progress', 'streak', 'routine', 'discipline', 'statistics'],
      ja: ['習慣', 'トラッカー', '目標', '進捗', '継続', 'ルーティン', '自己管理', '統計'],
    },
    component: HabitTrackerTool,
    isNew: true,
  },
  {
    id: 'document-converter',
    name: {
      zh: '文档转换器',
      en: 'Document Converter',
      ja: 'ドキュメント変換器',
    },
    description: {
      zh: '在线转换各种文档格式，支持 Word、Excel、PowerPoint、PDF 等格式互转',
      en: 'Convert between various document formats including Word, Excel, PowerPoint, PDF and more',
      ja: 'Word、Excel、PowerPoint、PDFなど様々なドキュメント形式を相互変換',
    },
    category: 'converter',
    icon: FileText,
    path: '/tools/document-converter',
    keywords: {
      zh: ['文档转换', 'word', 'excel', 'ppt', 'pdf', 'doc', 'docx', 'xlsx', 'pptx', '格式转换', '文件转换'],
      en: ['document converter', 'word', 'excel', 'ppt', 'pdf', 'doc', 'docx', 'xlsx', 'pptx', 'format conversion', 'file conversion'],
      ja: ['ドキュメント変換', 'word', 'excel', 'ppt', 'pdf', 'doc', 'docx', 'xlsx', 'pptx', 'フォーマット変換', 'ファイル変換'],
    },
    component: DocumentConverterTool,
    isNew: true,
  },
  {
    id: 'media-converter',
    name: {
      zh: '音视频转换器',
      en: 'Media Converter',
      ja: 'メディアコンバーター',
    },
    description: {
      zh: '在线转换音频和视频文件格式，支持多种常见格式互转',
      en: 'Convert audio and video file formats online with support for various common formats',
      ja: 'オーディオとビデオファイル形式をオンラインで変換、様々な一般的なフォーマットに対応',
    },
    category: 'converter',
    icon: Film,
    path: '/tools/media-converter',
    keywords: {
      zh: ['音频转换', '视频转换', 'mp3', 'mp4', 'wav', 'avi', 'mov', 'flac', '格式转换', '媒体转换'],
      en: ['audio converter', 'video converter', 'mp3', 'mp4', 'wav', 'avi', 'mov', 'flac', 'format conversion', 'media conversion'],
      ja: ['オーディオ変換', 'ビデオ変換', 'mp3', 'mp4', 'wav', 'avi', 'mov', 'flac', 'フォーマット変換', 'メディア変換'],
    },
    component: MediaConverterTool,
    isNew: true,
  },
  {
    id: 'ocr',
    name: {
      zh: '文档扫描OCR',
      en: 'Document OCR Scanner',
      ja: 'ドキュメントOCRスキャナー',
    },
    description: {
      zh: '从图片和PDF中提取文字，支持多语言识别',
      en: 'Extract text from images and PDFs with multi-language support',
      ja: '画像とPDFからテキストを抽出、多言語認識対応',
    },
    category: 'converter',
    icon: FileText,
    path: '/tools/ocr',
    keywords: {
      zh: ['ocr', '文字识别', '图片转文字', 'pdf转文字', '扫描', '文档识别', '光学字符识别'],
      en: ['ocr', 'text recognition', 'image to text', 'pdf to text', 'scan', 'document recognition', 'optical character recognition'],
      ja: ['ocr', 'テキスト認識', '画像からテキスト', 'pdfからテキスト', 'スキャン', 'ドキュメント認識', '光学文字認識'],
    },
    component: OCRTool,
    isNew: true,
  },
  {
    id: 'countdown-timer',
    name: {
      zh: '倒计时器',
      en: 'Countdown Timer',
      ja: 'カウントダウンタイマー',
    },
    description: {
      zh: '创建多个倒计时器，支持事件提醒和声音通知',
      en: 'Create multiple countdown timers with event reminders and sound notifications',
      ja: '複数のカウントダウンタイマーを作成、イベントリマインダーと音声通知対応',
    },
    category: 'lifestyle',
    icon: TimerIcon,
    path: '/tools/countdown-timer',
    keywords: {
      zh: ['倒计时', '计时器', '提醒', '事件', '时间管理', '闹钟', '定时器'],
      en: ['countdown', 'timer', 'reminder', 'event', 'time management', 'alarm', 'stopwatch'],
      ja: ['カウントダウン', 'タイマー', 'リマインダー', 'イベント', '時間管理', 'アラーム', 'ストップウォッチ'],
    },
    component: CountdownTimerTool,
    isNew: true,
  },
  {
    id: 'alarm-clock',
    name: {
      zh: '闹钟提醒器',
      en: 'Alarm Clock',
      ja: 'アラームクロック',
    },
    description: {
      zh: '设置多个闹钟，支持重复提醒、贪睡功能和自定义铃声',
      en: 'Set multiple alarms with repeat reminders, snooze function and custom ringtones',
      ja: '複数のアラームを設定、繰り返しリマインダー、スヌーズ機能、カスタム着信音対応',
    },
    category: 'lifestyle',
    icon: Bell,
    path: '/tools/alarm-clock',
    keywords: {
      zh: ['闹钟', '提醒', '起床', '定时', '贪睡', '铃声', '时间管理'],
      en: ['alarm', 'clock', 'reminder', 'wake up', 'snooze', 'ringtone', 'time management'],
      ja: ['アラーム', '時計', 'リマインダー', '起床', 'スヌーズ', '着信音', '時間管理'],
    },
    component: AlarmClockTool,
    isNew: true,
  },
  {
    id: 'birthday-reminder',
    name: {
      zh: '生日提醒器',
      en: 'Birthday Reminder',
      ja: '誕生日リマインダー',
    },
    description: {
      zh: '管理亲友生日，自动提醒即将到来的生日，支持农历和星座显示',
      en: 'Manage birthdays with automatic reminders, lunar calendar and zodiac support',
      ja: '誕生日を管理、自動リマインダー、旧暦と星座表示対応',
    },
    category: 'lifestyle',
    icon: Gift,
    path: '/tools/birthday-reminder',
    keywords: {
      zh: ['生日', '提醒', '纪念日', '农历', '星座', '生肖', '年龄计算'],
      en: ['birthday', 'reminder', 'anniversary', 'lunar calendar', 'zodiac', 'age calculator'],
      ja: ['誕生日', 'リマインダー', '記念日', '旧暦', '星座', '干支', '年齢計算'],
    },
    component: BirthdayReminderTool,
    isNew: true,
  },
  {
    id: 'travel-planner',
    name: {
      zh: '旅行规划器',
      en: 'Travel Planner',
      ja: '旅行プランナー',
    },
    description: {
      zh: '制定详细的旅行计划，管理行程、预算和准备清单',
      en: 'Create detailed travel plans with itinerary, budget and checklist management',
      ja: '詳細な旅行計画を作成、行程、予算、準備リストを管理',
    },
    category: 'lifestyle',
    icon: Plane,
    path: '/tools/travel-planner',
    keywords: {
      zh: ['旅行', '规划', '行程', '预算', '清单', '旅游', '出行计划'],
      en: ['travel', 'planner', 'itinerary', 'budget', 'checklist', 'trip', 'vacation'],
      ja: ['旅行', 'プランナー', '行程', '予算', 'チェックリスト', '旅', '休暇'],
    },
    component: TravelPlannerTool,
    isNew: true,
  },
  {
    id: 'slugify',
    name: {
      zh: 'URL友好化工具',
      en: 'Slugify Tool',
      ja: 'URL最適化ツール',
    },
    description: {
      zh: '将任意文本转换为URL友好的字符串，支持多种分隔符和选项配置',
      en: 'Convert any text to URL-friendly strings with various separator and option configurations',
      ja: '任意のテキストをURL対応文字列に変換、様々な区切り文字とオプション設定に対応',
    },
    category: 'text',
    icon: Link2,
    path: '/tools/slugify',
    keywords: {
      zh: ['slugify', 'url', '友好化', '转换', '字符串', '链接', 'seo', '标识符'],
      en: ['slugify', 'url', 'friendly', 'convert', 'string', 'link', 'seo', 'identifier'],
      ja: ['slugify', 'url', 'フレンドリー', '変換', '文字列', 'リンク', 'seo', '識別子'],
    },
    component: SlugifyTool,
    isNew: true,
  },
  {
    id: 'lorem-ipsum',
    name: {
      zh: 'Lorem Ipsum 生成器',
      en: 'Lorem Ipsum Generator',
      ja: 'Lorem Ipsum ジェネレーター',
    },
    description: {
      zh: '生成占位文本，支持多种语言和格式，适用于设计和开发',
      en: 'Generate placeholder text with multiple languages and formats for design and development',
      ja: 'デザインと開発用のプレースホルダーテキストを生成、複数の言語とフォーマットに対応',
    },
    category: 'text',
    icon: FileTextIcon,
    path: '/tools/lorem-ipsum',
    keywords: {
      zh: ['lorem ipsum', '占位文本', '假文', '测试文本', '随机文本', '填充文本', '设计', '排版'],
      en: ['lorem ipsum', 'placeholder text', 'dummy text', 'test text', 'random text', 'filler text', 'design', 'typography'],
      ja: ['lorem ipsum', 'プレースホルダーテキスト', 'ダミーテキスト', 'テストテキスト', 'ランダムテキスト', 'フィラーテキスト', 'デザイン', 'タイポグラフィ'],
    },
    component: LoremIpsumTool,
    isNew: true,
  },
  {
    id: 'yaml-json-converter',
    name: {
      zh: 'YAML/JSON 转换器',
      en: 'YAML/JSON Converter',
      ja: 'YAML/JSON変換器',
    },
    description: {
      zh: '在YAML和JSON格式之间相互转换，支持复杂数据结构和文件上传',
      en: 'Convert between YAML and JSON formats with support for complex data structures and file upload',
      ja: 'YAMLとJSON形式の相互変換、複雑なデータ構造とファイルアップロードに対応',
    },
    category: 'converter',
    icon: Code2,
    path: '/tools/yaml-json-converter',
    keywords: {
      zh: ['yaml', 'json', '转换', '配置文件', '数据格式', 'yml', '解析'],
      en: ['yaml', 'json', 'convert', 'config file', 'data format', 'yml', 'parse'],
      ja: ['yaml', 'json', '変換', '設定ファイル', 'データ形式', 'yml', '解析'],
    },
    component: YamlJsonConverterTool,
    isNew: true,
  },
  {
    id: 'html-to-text',
    name: {
      zh: 'HTML转纯文本工具',
      en: 'HTML to Text Converter',
      ja: 'HTMLからテキストへ変換ツール',
    },
    description: {
      zh: '从HTML内容中提取纯文本，去除标签和样式，支持保留格式和Markdown输出',
      en: 'Extract plain text from HTML content, remove tags and styles, support format preservation and Markdown output',
      ja: 'HTMLコンテンツからプレーンテキストを抽出、タグとスタイルを削除、フォーマット保持とMarkdown出力に対応',
    },
    category: 'text',
    icon: FileText,
    path: '/tools/html-to-text',
    keywords: {
      zh: ['html', '转文本', '去标签', '提取文本', '网页内容', 'markdown', '清理html'],
      en: ['html', 'to text', 'strip tags', 'extract text', 'web content', 'markdown', 'clean html'],
      ja: ['html', 'テキスト変換', 'タグ削除', 'テキスト抽出', 'ウェブコンテンツ', 'markdown', 'htmlクリーン'],
    },
    component: HtmlToTextTool,
    isNew: true,
  },
  {
    id: 'text-to-table',
    name: {
      zh: '文本转表格',
      en: 'Text to Table Converter',
      ja: 'テキストからテーブルへ変換',
    },
    description: {
      zh: '将结构化文本转换为HTML表格、Markdown表格或CSV格式，支持多种分隔符',
      en: 'Convert structured text to HTML tables, Markdown tables or CSV format, supports multiple delimiters',
      ja: '構造化テキストをHTMLテーブル、Markdownテーブル、CSV形式に変換、複数の区切り文字に対応',
    },
    category: 'text',
    icon: Table,
    path: '/tools/text-to-table',
    keywords: {
      zh: ['文本', '表格', 'html', 'markdown', 'csv', '转换', '分隔符'],
      en: ['text', 'table', 'html', 'markdown', 'csv', 'convert', 'delimiter'],
      ja: ['テキスト', 'テーブル', 'html', 'markdown', 'csv', '変換', '区切り文字'],
    },
    component: TextToTableTool,
    isNew: true,
  },
  {
    id: 'text-to-list',
    name: {
      zh: '文本转列表',
      en: 'Text to List Converter',
      ja: 'テキストからリストへ変換',
    },
    description: {
      zh: '将文本转换为有序或无序列表，支持HTML、Markdown和纯文本格式',
      en: 'Convert text to ordered or unordered lists with HTML, Markdown and plain text output',
      ja: 'テキストを順序付きまたは順序なしリストに変換、HTML、Markdown、プレーンテキスト出力対応',
    },
    category: 'text',
    icon: List,
    path: '/tools/text-to-list',
    keywords: {
      zh: ['文本', '列表', 'html', 'markdown', '有序列表', '无序列表', '转换'],
      en: ['text', 'list', 'html', 'markdown', 'ordered list', 'unordered list', 'convert'],
      ja: ['テキスト', 'リスト', 'html', 'markdown', '順序付きリスト', '順序なしリスト', '変換'],
    },
    component: TextToListTool,
    isNew: true,
  },
  {
    id: 'text-highlighter',
    name: {
      zh: '文本高亮工具',
      en: 'Text Highlighter',
      ja: 'テキストハイライター',
    },
    description: {
      zh: '智能高亮文本中的关键词，支持正则表达式、大小写敏感、全词匹配等高级功能',
      en: 'Smart keyword highlighting with regex support, case sensitivity, whole word matching and more',
      ja: 'キーワードのスマートハイライト、正規表現、大文字小文字区別、全単語マッチング等対応',
    },
    category: 'text',
    icon: Highlighter,
    path: '/tools/text-highlighter',
    keywords: {
      zh: ['文本', '高亮', '关键词', '正则表达式', '标记', '搜索', '颜色'],
      en: ['text', 'highlight', 'keyword', 'regex', 'mark', 'search', 'color'],
      ja: ['テキスト', 'ハイライト', 'キーワード', '正規表現', 'マーク', '検索', '色'],
    },
    component: TextHighlighterTool,
    isNew: true,
  },
  {
    id: 'text-summarizer',
    name: {
      zh: '文本缩略器',
      en: 'Text Summarizer',
      ja: 'テキスト要約ツール',
    },
    description: {
      zh: '智能生成文本摘要，支持多种算法和自定义选项',
      en: 'Generate intelligent text summaries with multiple algorithms and custom options',
      ja: '複数のアルゴリズムとカスタムオプションでインテリジェントなテキスト要約を生成',
    },
    category: 'text',
    icon: Target,
    path: '/tools/text-summarizer',
    keywords: {
      zh: ['文本缩略', '摘要', '总结', '关键词', '文本分析', '智能摘要', '文本压缩'],
      en: ['text summarizer', 'summary', 'abstract', 'keywords', 'text analysis', 'smart summary', 'text compression'],
      ja: ['テキスト要約', '要約', '抄録', 'キーワード', 'テキスト分析', 'スマート要約', 'テキスト圧縮'],
    },
    component: TextSummarizerTool,
    isNew: true,
  },
  {
    id: 'spell-checker',
    name: {
      zh: '拼写检查器',
      en: 'Spell Checker',
      ja: 'スペルチェッカー',
    },
    description: {
      zh: '智能检查文本拼写错误，支持多语言和自定义词典',
      en: 'Intelligent spell checking with multi-language support and custom dictionary',
      ja: '多言語対応とカスタム辞書機能付きのインテリジェントスペルチェック',
    },
    category: 'text',
    icon: BookOpen,
    path: '/tools/spell-checker',
    keywords: {
      zh: ['拼写检查', '拼写', '语法', '错误检查', '词典', '文本校对', '英文检查'],
      en: ['spell check', 'spelling', 'grammar', 'error check', 'dictionary', 'text proofreading', 'english check'],
      ja: ['スペルチェック', 'スペル', '文法', 'エラーチェック', '辞書', 'テキスト校正', '英語チェック'],
    },
    component: SpellCheckerTool,
    isNew: true,
  },
  {
    id: 'grammar-checker',
    name: {
      zh: '语法检查器',
      en: 'Grammar Checker',
      ja: '文法チェッカー',
    },
    description: {
      zh: '智能检查文本中的语法、拼写、标点和风格问题，支持多语言检测和自定义规则配置',
      en: 'Intelligent grammar, spelling, punctuation and style checker with multi-language detection and customizable rules',
      ja: '多言語検出とカスタマイズ可能なルール機能付きのインテリジェント文法、スペル、句読点、スタイルチェッカー',
    },
    category: 'text',
    icon: CheckSquare,
    path: '/tools/grammar-checker',
    keywords: {
      zh: ['语法检查', '语法', '文法', '拼写', '标点', '风格', '错误检查', '文本校对', '写作助手'],
      en: ['grammar check', 'grammar', 'spelling', 'punctuation', 'style', 'error check', 'text proofreading', 'writing assistant'],
      ja: ['文法チェック', '文法', 'スペル', '句読点', 'スタイル', 'エラーチェック', 'テキスト校正', 'ライティングアシスタント'],
    },
    component: GrammarCheckerTool,
    isNew: true,
  },
  {
    id: 'text-formatter',
    name: {
      zh: '文本格式化器',
      en: 'Text Formatter',
      ja: 'テキストフォーマッター',
    },
    description: {
      zh: '智能文本格式化工具，支持多种格式化规则、自定义配置和批量处理，可处理空格、标点、大小写、列表等',
      en: 'Intelligent text formatting tool with multiple rules, custom configuration and batch processing for spaces, punctuation, case, lists and more',
      ja: 'スペース、句読点、大文字小文字、リストなどを処理する複数のルール、カスタム設定、バッチ処理機能付きのインテリジェントテキストフォーマットツール',
    },
    category: 'text',
    icon: Type,
    path: '/tools/text-formatter',
    keywords: {
      zh: ['文本格式化', '格式化', '文本整理', '格式规范', '文本美化', '批量处理', '自定义规则', '文本清理'],
      en: ['text formatting', 'formatting', 'text cleanup', 'format standardization', 'text beautify', 'batch processing', 'custom rules', 'text cleaning'],
      ja: ['テキストフォーマット', 'フォーマット', 'テキスト整理', '形式標準化', 'テキスト美化', 'バッチ処理', 'カスタムルール', 'テキストクリーニング'],
    },
    component: TextFormatterTool,
    isNew: true,
  },
  {
    id: 'text-annotator',
    name: {
      zh: '文本标注器',
      en: 'Text Annotator',
      ja: 'テキストアノテーター',
    },
    description: {
      zh: '强大的文本标注工具，支持多种注释类型、协作标注和导出功能，适用于文档审阅、学术研究、内容分析等场景',
      en: 'Powerful text annotation tool with multiple annotation types, collaborative features and export options for document review, academic research and content analysis',
      ja: '文書レビュー、学術研究、コンテンツ分析に対応した複数の注釈タイプ、コラボレーション機能、エクスポートオプション付きの強力なテキスト注釈ツール',
    },
    category: 'text',
    icon: MessageSquare,
    path: '/tools/text-annotator',
    keywords: {
      zh: ['文本标注', '标注', '批注', '文档审阅', '协作', '注释', '学术研究', '内容分析', '文档标记'],
      en: ['text annotation', 'annotation', 'markup', 'document review', 'collaboration', 'comments', 'academic research', 'content analysis', 'document marking'],
      ja: ['テキスト注釈', '注釈', 'マークアップ', '文書レビュー', 'コラボレーション', 'コメント', '学術研究', 'コンテンツ分析', '文書マーキング'],
    },
    component: TextAnnotatorTool,
    isNew: true,
  },
  {
    id: 'text-version-diff',
    name: {
      zh: '文本版本对比',
      en: 'Text Version Diff',
      ja: 'テキストバージョン比較',
    },
    description: {
      zh: '强大的多版本文本对比工具，支持字符级、词级、行级差异检测，提供详细的统计分析和可视化对比结果',
      en: 'Powerful multi-version text comparison tool with character, word and line-level diff detection, detailed statistics and visual comparison results',
      ja: '文字、単語、行レベルの差分検出、詳細な統計、視覚的な比較結果を提供する強力な複数バージョンテキスト比較ツール',
    },
    category: 'text',
    icon: FileDiff,
    path: '/tools/text-version-diff',
    keywords: {
      zh: ['文本对比', '版本对比', '差异检测', '版本管理', '文档比较', '变更追踪', '代码对比', '文本分析'],
      en: ['text comparison', 'version diff', 'difference detection', 'version control', 'document comparison', 'change tracking', 'code diff', 'text analysis'],
      ja: ['テキスト比較', 'バージョン差分', '差異検出', 'バージョン管理', '文書比較', '変更追跡', 'コード差分', 'テキスト分析'],
    },
    component: TextVersionDiffTool,
    isNew: true,
  },
  {
    id: 'text-translator',
    name: {
      zh: '文本翻译器',
      en: 'Text Translator',
      ja: 'テキスト翻訳ツール',
    },
    description: {
      zh: '多语言文本翻译工具，支持自动语言检测、历史记录和语音朗读',
      en: 'Multi-language text translation tool with auto-detection, history and speech synthesis',
      ja: '自動言語検出、履歴、音声合成機能付きの多言語テキスト翻訳ツール',
    },
    category: 'text',
    icon: Languages,
    path: '/tools/text-translator',
    keywords: {
      zh: ['翻译', '多语言', '语言检测', '翻译历史', '语音朗读', '文本翻译', '在线翻译'],
      en: ['translate', 'translation', 'multi-language', 'language detection', 'speech', 'text translation', 'online translate'],
      ja: ['翻訳', '多言語', '言語検出', '翻訳履歴', '音声', 'テキスト翻訳', 'オンライン翻訳'],
    },
    component: TextTranslatorTool,
    isNew: true,
  },
  {
    id: 'text-to-speech-enhanced',
    name: {
      zh: '增强版文本朗读器',
      en: 'Enhanced Text to Speech',
      ja: '拡張テキスト読み上げ',
    },
    description: {
      zh: '功能强大的文本转语音工具，支持多语言、语音控制、播放控制和语音设置',
      en: 'Advanced text-to-speech tool with multi-language support, voice control, playback control and voice settings',
      ja: '多言語対応、音声制御、再生制御、音声設定機能付きの高度なテキスト読み上げツール',
    },
    category: 'text',
    icon: Volume2,
    path: '/tools/text-to-speech-enhanced',
    keywords: {
      zh: ['语音', '朗读', 'TTS', '语音合成', '文字转语音', '增强版', '语音控制', '播放控制'],
      en: ['speech', 'voice', 'TTS', 'text to speech', 'audio', 'enhanced', 'voice control', 'playback control'],
      ja: ['音声', '読み上げ', 'TTS', 'テキスト読み上げ', 'オーディオ', '拡張', '音声制御', '再生制御'],
    },
    component: TextToSpeechEnhancedTool,
    isNew: true,
  },
  {
    id: 'color-format-converter',
    name: {
      zh: 'HEX/RGB转换器',
      en: 'Color Format Converter',
      ja: '色形式変換器',
    },
    description: {
      zh: '专业的颜色格式转换工具，支持HEX、RGB、HSL、HSV、CMYK、LAB、XYZ等多种格式互转，提供配色方案生成、颜色历史记录和调色板管理功能',
      en: 'Professional color format converter supporting HEX, RGB, HSL, HSV, CMYK, LAB, XYZ and more with color scheme generation, history and palette management',
      ja: 'HEX、RGB、HSL、HSV、CMYK、LAB、XYZなど多様な形式に対応した専門的な色形式変換ツール、配色生成、履歴、パレット管理機能付き',
    },
    category: 'developer',
    icon: Palette,
    path: '/tools/color-format-converter',
    keywords: {
      zh: ['颜色转换', 'hex', 'rgb', 'hsl', 'hsv', 'cmyk', 'lab', 'xyz', '配色方案', '调色板', '颜色分析'],
      en: ['color converter', 'hex', 'rgb', 'hsl', 'hsv', 'cmyk', 'lab', 'xyz', 'color scheme', 'palette', 'color analysis'],
      ja: ['色変換', 'hex', 'rgb', 'hsl', 'hsv', 'cmyk', 'lab', 'xyz', '配色', 'パレット', '色分析'],
    },
    component: ColorFormatConverterTool,
    isNew: true,
  },
  {
    id: 'color-palette-generator',
    name: {
      zh: '调色板生成器',
      en: 'Color Palette Generator',
      ja: 'カラーパレットジェネレーター',
    },
    description: {
      zh: '智能配色方案生成器，支持互补色、三角色、类比色、单色和四分色方案，提供色彩和谐理论、无障碍对比度检查和多格式导出功能',
      en: 'Intelligent color palette generator with complementary, triadic, analogous, monochromatic and tetradic schemes, color harmony theory, accessibility contrast checking and multi-format export',
      ja: '補色、三角配色、類似色、単色、四分配色スキームに対応した知能的カラーパレットジェネレーター、色彩調和理論、アクセシビリティコントラスト検査、多形式エクスポート機能付き',
    },
    category: 'developer',
    icon: Palette,
    path: '/tools/color-palette-generator',
    keywords: {
      zh: ['调色板', '配色方案', '颜色和谐', '互补色', '三角色', '类比色', '单色', '四分色', '色彩理论', '无障碍', '对比度'],
      en: ['color palette', 'color scheme', 'color harmony', 'complementary', 'triadic', 'analogous', 'monochromatic', 'tetradic', 'color theory', 'accessibility', 'contrast'],
      ja: ['カラーパレット', '配色スキーム', '色彩調和', '補色', '三角配色', '類似色', '単色', '四分配色', '色彩理論', 'アクセシビリティ', 'コントラスト'],
    },
    component: ColorPaletteGeneratorTool,
    isNew: true,
  },
  {
    id: 'color-contrast-checker',
    name: {
      zh: '对比度检查器',
      en: 'Color Contrast Checker',
      ja: 'カラーコントラストチェッカー',
    },
    description: {
      zh: 'WCAG标准无障碍对比度检查器，提供详细的可访问性分析、色盲模拟、批量检查和改进建议，确保设计符合Web无障碍标准',
      en: 'WCAG standard accessibility contrast checker with detailed accessibility analysis, color blindness simulation, bulk checking and improvement suggestions to ensure designs meet web accessibility standards',
      ja: 'WCAG標準アクセシビリティコントラストチェッカー、詳細なアクセシビリティ分析、色覚障害シミュレーション、一括チェック、改善提案を提供し、デザインがWebアクセシビリティ基準を満たすことを保証',
    },
    category: 'developer',
    icon: Eye,
    path: '/tools/color-contrast-checker',
    keywords: {
      zh: ['对比度', 'WCAG', '无障碍', '可访问性', '色盲', '视觉障碍', '网页标准', '辅助功能', '颜色检查', 'AA', 'AAA'],
      en: ['contrast', 'WCAG', 'accessibility', 'color blindness', 'visual impairment', 'web standards', 'compliance', 'color checker', 'AA', 'AAA'],
      ja: ['コントラスト', 'WCAG', 'アクセシビリティ', '色覚障害', '視覚障害', 'Web標準', 'コンプライアンス', '色チェック', 'AA', 'AAA'],
    },
    component: ColorContrastCheckerTool,
    isNew: true,
  },
  {
    id: 'css-gradient-generator',
    name: {
      zh: 'CSS渐变生成器',
      en: 'CSS Gradient Generator',
      ja: 'CSSグラデーションジェネレーター',
    },
    description: {
      zh: '可视化CSS渐变编辑器，支持线性、径向、圆锥渐变，提供实时预览、色彩管理、预设库和多格式导出功能',
      en: 'Visual CSS gradient editor with linear, radial, conic gradients, real-time preview, color management, preset library and multi-format export',
      ja: '線形、放射状、円錐グラデーション対応のビジュアルCSSグラデーションエディター、リアルタイムプレビュー、色管理、プリセットライブラリ、多形式エクスポート機能付き',
    },
    category: 'developer',
    icon: Palette,
    path: '/tools/css-gradient-generator',
    keywords: {
      zh: ['css', '渐变', '线性渐变', '径向渐变', '圆锥渐变', '可视化', '生成器', '预设', '导出'],
      en: ['css', 'gradient', 'linear gradient', 'radial gradient', 'conic gradient', 'visual', 'generator', 'presets', 'export'],
      ja: ['css', 'グラデーション', '線形グラデーション', '放射状グラデーション', '円錐グラデーション', 'ビジュアル', 'ジェネレーター', 'プリセット', 'エクスポート'],
    },
    component: CssGradientGeneratorTool,
    isNew: true,
  },
  {
    id: 'css-shadow-generator',
    name: {
      zh: 'CSS阴影生成器',
      en: 'CSS Shadow Generator',
      ja: 'CSSシャドウジェネレーター',
    },
    description: {
      zh: '强大的box-shadow可视化编辑器，支持多层阴影、实时预览、预设库和多格式代码导出',
      en: 'Powerful box-shadow visual editor with multi-layer shadows, real-time preview, preset library and multi-format code export',
      ja: '多層シャドウ、リアルタイムプレビュー、プリセットライブラリ、多形式コードエクスポート対応の強力なbox-shadowビジュアルエディター',
    },
    category: 'developer',
    icon: Layers,
    path: '/tools/css-shadow-generator',
    keywords: {
      zh: ['css', '阴影', 'box-shadow', '多层', '可视化', '生成器', '预设', '导出'],
      en: ['css', 'shadow', 'box-shadow', 'multi-layer', 'visual', 'generator', 'presets', 'export'],
      ja: ['css', 'シャドウ', 'box-shadow', '多層', 'ビジュアル', 'ジェネレーター', 'プリセット', 'エクスポート'],
    },
    component: CssShadowGeneratorTool,
    isNew: true,
  },
  {
    id: 'css-border-radius-generator',
    name: {
      zh: 'CSS圆角生成器',
      en: 'CSS Border Radius Generator',
      ja: 'CSS境界半径ジェネレーター',
    },
    description: {
      zh: '直观的border-radius可视化编辑器，支持独立角度控制、预设库、单位转换和实时预览',
      en: 'Intuitive border-radius visual editor with independent corner control, preset library, unit conversion and real-time preview',
      ja: '独立した角度制御、プリセットライブラリ、単位変換、リアルタイムプレビュー対応の直感的なborder-radiusビジュアルエディター',
    },
    category: 'developer',
    icon: Square,
    path: '/tools/css-border-radius-generator',
    keywords: {
      zh: ['css', '圆角', 'border-radius', '角度', '可视化', '生成器', '预设', '单位'],
      en: ['css', 'border-radius', 'corner', 'radius', 'visual', 'generator', 'presets', 'unit'],
      ja: ['css', 'border-radius', '角度', '半径', 'ビジュアル', 'ジェネレーター', 'プリセット', '単位'],
    },
    component: CssBorderRadiusGeneratorTool,
    isNew: true,
  },
  {
    id: 'favicon-generator',
    name: {
      zh: 'Favicon生成器',
      en: 'Favicon Generator',
      ja: 'Faviconジェネレーター',
    },
    description: {
      zh: '多尺寸图标生成器，支持文字、图片、渐变背景，一键生成完整的favicon文件套装',
      en: 'Multi-size icon generator with text, image, gradient backgrounds, one-click generation of complete favicon file set',
      ja: 'テキスト、画像、グラデーション背景対応の多サイズアイコンジェネレーター、ワンクリックで完全なfaviconファイルセットを生成',
    },
    category: 'developer',
    icon: ImageIcon,
    path: '/tools/favicon-generator',
    keywords: {
      zh: ['favicon', '图标', '网站图标', '多尺寸', 'ico', 'png', '生成器', '渐变'],
      en: ['favicon', 'icon', 'website icon', 'multi-size', 'ico', 'png', 'generator', 'gradient'],
      ja: ['favicon', 'アイコン', 'ウェブサイトアイコン', '多サイズ', 'ico', 'png', 'ジェネレーター', 'グラデーション'],
    },
    component: FaviconGeneratorTool,
    isNew: true,
  },
  {
    id: 'css-clamp-calculator',
    name: {
      zh: 'CSS Clamp计算器',
      en: 'CSS Clamp Calculator',
      ja: 'CSS Clamp計算ツール',
    },
    description: {
      zh: '响应式尺寸计算器，生成完美的clamp()函数实现流畅的响应式字体、间距和布局',
      en: 'Responsive size calculator generating perfect clamp() functions for smooth fluid typography, spacing and layouts',
      ja: 'スムーズな流体タイポグラフィ、スペーシング、レイアウトのための完璧なclamp()関数を生成するレスポンシブサイズ計算ツール',
    },
    category: 'developer',
    icon: Calculator,
    path: '/tools/css-clamp-calculator',
    keywords: {
      zh: ['css', 'clamp', '响应式', '字体', '间距', '流畅', '计算器', 'fluid'],
      en: ['css', 'clamp', 'responsive', 'typography', 'spacing', 'fluid', 'calculator', 'viewport'],
      ja: ['css', 'clamp', 'レスポンシブ', 'タイポグラフィ', 'スペーシング', '流体', '計算ツール', 'ビューポート'],
    },
    component: CssClampCalculatorTool,
    isNew: true,
  },
  {
    id: 'tailwind-cheatsheet',
    name: {
      zh: 'Tailwind CSS速查表',
      en: 'Tailwind CSS Cheat Sheet',
      ja: 'Tailwind CSSチートシート',
    },
    description: {
      zh: '完整的Tailwind CSS类名参考手册，快速查找、复制和学习常用样式类',
      en: 'Complete Tailwind CSS class reference guide with quick search, copy and learn common utility classes',
      ja: '完全なTailwind CSSクラス参照ガイド、一般的なユーティリティクラスの素早い検索、コピー、学習',
    },
    category: 'developer',
    icon: Book,
    path: '/tools/tailwind-cheatsheet',
    keywords: {
      zh: ['tailwind', 'css', '速查表', '类名', '参考', '样式', 'utility', '工具类'],
      en: ['tailwind', 'css', 'cheat sheet', 'classes', 'reference', 'styles', 'utility', 'framework'],
      ja: ['tailwind', 'css', 'チートシート', 'クラス', '参考', 'スタイル', 'ユーティリティ', 'フレームワーク'],
    },
    component: TailwindCheatSheetTool,
    isNew: true,
  },
  {
    id: 'css-animation-generator',
    name: {
      zh: 'CSS动画生成器',
      en: 'CSS Animation Generator',
      ja: 'CSSアニメーションジェネレーター',
    },
    description: {
      zh: '可视化关键帧动画编辑器，支持多重变换、时间轴控制、预设库和实时预览',
      en: 'Visual keyframe animation editor with multi-transform, timeline control, preset library and real-time preview',
      ja: 'マルチトランスフォーム、タイムライン制御、プリセットライブラリ、リアルタイムプレビュー対応のビジュアルキーフレームアニメーションエディター',
    },
    category: 'developer',
    icon: Sparkles,
    path: '/tools/css-animation-generator',
    keywords: {
      zh: ['css', '动画', '关键帧', 'keyframes', '变换', 'transform', '生成器', '预览'],
      en: ['css', 'animation', 'keyframes', 'transform', 'generator', 'preview', 'timeline', 'visual'],
      ja: ['css', 'アニメーション', 'キーフレーム', '変換', 'ジェネレーター', 'プレビュー', 'タイムライン', 'ビジュアル'],
    },
    component: CssAnimationGeneratorTool,
    isNew: true,
  },
  {
    id: 'css-grid-generator',
    name: {
      zh: 'CSS Grid生成器',
      en: 'CSS Grid Generator',
      ja: 'CSSグリッドジェネレーター',
    },
    description: {
      zh: '可视化CSS Grid布局编辑器，支持拖拽设计、模板库、响应式预览和多格式代码导出',
      en: 'Visual CSS Grid layout editor with drag-and-drop design, template library, responsive preview and multi-format code export',
      ja: 'ドラッグアンドドロップデザイン、テンプレートライブラリ、レスポンシブプレビュー、多形式コードエクスポート対応のビジュアルCSSグリッドレイアウトエディター',
    },
    category: 'developer',
    icon: Grid,
    path: '/tools/css-grid-generator',
    keywords: {
      zh: ['css', 'grid', '网格', '布局', 'layout', '生成器', '响应式', 'responsive'],
      en: ['css', 'grid', 'layout', 'generator', 'responsive', 'visual', 'drag', 'template'],
      ja: ['css', 'グリッド', 'レイアウト', 'ジェネレーター', 'レスポンシブ', 'ビジュアル', 'ドラッグ', 'テンプレート'],
    },
    component: CssGridGeneratorTool,
    isNew: true,
  },
  {
    id: 'flexbox-generator',
    name: {
      zh: 'Flexbox生成器',
      en: 'Flexbox Generator',
      ja: 'Flexboxジェネレーター',
    },
    description: {
      zh: '可视化CSS Flexbox布局编辑器，支持实时预览、模板库、响应式设计和多格式代码导出',
      en: 'Visual CSS Flexbox layout editor with real-time preview, template library, responsive design and multi-format code export',
      ja: 'リアルタイムプレビュー、テンプレートライブラリ、レスポンシブデザイン、多形式コードエクスポート対応のビジュアルCSSFlexboxレイアウトエディター',
    },
    category: 'developer',
    icon: StretchHorizontal,
    path: '/tools/flexbox-generator',
    keywords: {
      zh: ['flexbox', 'flex', '弹性布局', '布局', 'layout', '生成器', '响应式', 'css'],
      en: ['flexbox', 'flex', 'layout', 'generator', 'responsive', 'visual', 'css', 'design'],
      ja: ['flexbox', 'フレックス', 'レイアウト', 'ジェネレーター', 'レスポンシブ', 'ビジュアル', 'css', 'デザイン'],
    },
    component: FlexboxGeneratorTool,
    isNew: true,
  },
  {
    id: 'css-variable-generator',
    name: {
      zh: 'CSS变量生成器',
      en: 'CSS Variable Generator',
      ja: 'CSS変数ジェネレーター',
    },
    description: {
      zh: '创建和管理CSS自定义属性(变量)，支持分类管理、模板导入、SCSS导出和可视化预览',
      en: 'Create and manage CSS custom properties (variables) with category management, template import, SCSS export and visual preview',
      ja: 'CSS カスタムプロパティ（変数）の作成と管理、カテゴリ管理、テンプレートインポート、SCSS エクスポート、ビジュアルプレビューに対応',
    },
    category: 'developer',
    icon: Variable,
    path: '/tools/css-variable-generator',
    keywords: {
      zh: ['css', '变量', 'variable', '自定义属性', 'custom property', 'scss', '样式', '设计系统'],
      en: ['css', 'variable', 'custom property', 'scss', 'design system', 'style', 'generator', 'variables'],
      ja: ['css', '変数', 'カスタムプロパティ', 'scss', 'デザインシステム', 'スタイル', 'ジェネレーター'],
    },
    component: CssVariableGeneratorTool,
    isNew: true,
  },
  {
    id: 'image-resizer',
    name: {
      zh: '图片尺寸调整',
      en: 'Image Resizer',
      ja: '画像リサイザー',
    },
    description: {
      zh: '批量调整图片大小，支持多种调整模式、格式转换、质量控制和预览对比',
      en: 'Batch resize images with multiple resize modes, format conversion, quality control and preview comparison',
      ja: '複数のリサイズモード、フォーマット変換、品質制御、プレビュー比較対応のバッチ画像リサイザー',
    },
    category: 'image',
    icon: Maximize,
    path: '/tools/image-resizer',
    keywords: {
      zh: ['图片', '调整', '尺寸', '缩放', '压缩', '批量', '格式转换', 'resize'],
      en: ['image', 'resize', 'scale', 'compress', 'batch', 'format', 'conversion', 'dimension'],
      ja: ['画像', 'リサイズ', 'スケール', '圧縮', 'バッチ', 'フォーマット', '変換', 'サイズ'],
    },
    component: ImageResizerTool,
    isNew: true,
  },
  {
    id: 'svg-optimizer',
    name: {
      zh: 'SVG优化工具',
      en: 'SVG Optimizer',
      ja: 'SVGオプティマイザー',
    },
    description: {
      zh: '批量优化SVG文件，支持多种优化选项、预设配置和实时预览对比',
      en: 'Batch optimize SVG files with multiple optimization options, preset configurations and real-time preview comparison',
      ja: '複数の最適化オプション、プリセット設定、リアルタイムプレビュー比較対応のバッチSVG最適化ツール',
    },
    category: 'image',
    icon: Minimize,
    path: '/tools/svg-optimizer',
    keywords: {
      zh: ['svg', '优化', '压缩', '矢量', '文件大小', '批量', '清理', 'optimize'],
      en: ['svg', 'optimize', 'compress', 'vector', 'file size', 'batch', 'cleanup', 'minify'],
      ja: ['svg', '最適化', '圧縮', 'ベクター', 'ファイルサイズ', 'バッチ', 'クリーンアップ', '最小化'],
    },
    component: SvgOptimizerTool,
    isNew: true,
  },
  {
    id: 'font-pairing',
    name: {
      zh: '字体配对工具',
      en: 'Font Pairing Tool',
      ja: 'フォントペアリングツール',
    },
    description: {
      zh: '专业的字体搭配推荐系统，帮助你选择完美的标题和正文字体组合',
      en: 'Professional font pairing recommendation system to help you choose perfect heading and body font combinations',
      ja: 'プロフェッショナルなフォントペアリング推奨システムで、完璧な見出しと本文フォントの組み合わせを選択',
    },
    category: 'developer',
    icon: Type,
    path: '/tools/font-pairing',
    keywords: {
      zh: ['字体', '配对', '搭配', '排版', '设计', '字体族', '组合', '推荐'],
      en: ['font', 'pairing', 'combination', 'typography', 'design', 'font family', 'match', 'recommendation'],
      ja: ['フォント', 'ペアリング', '組み合わせ', 'タイポグラフィ', 'デザイン', 'フォントファミリー', 'マッチ', '推奨'],
    },
    component: FontPairingTool,
    isNew: true,
  },
  {
    id: 'design-token-generator',
    name: {
      zh: '设计令牌生成器',
      en: 'Design Token Generator',
      ja: 'デザイントークンジェネレーター',
    },
    description: {
      zh: '创建和管理设计系统令牌，支持多种导出格式和命名规范',
      en: 'Create and manage design system tokens with multiple export formats and naming conventions',
      ja: 'デザインシステムトークンの作成と管理、複数のエクスポート形式と命名規則をサポート',
    },
    category: 'developer',
    icon: Package,
    path: '/tools/design-token-generator',
    keywords: {
      zh: ['设计令牌', '设计系统', 'design tokens', 'figma', '样式', '主题', 'css变量'],
      en: ['design tokens', 'design system', 'tokens', 'figma', 'styles', 'theme', 'css variables'],
      ja: ['デザイントークン', 'デザインシステム', 'トークン', 'figma', 'スタイル', 'テーマ', 'css変数'],
    },
    component: DesignTokenGeneratorTool,
    isNew: true,
  },
  {
    id: 'audio-converter',
    name: {
      zh: '音频格式转换器',
      en: 'Audio Format Converter',
      ja: 'オーディオフォーマット変換器',
    },
    description: {
      zh: '支持多种音频格式之间的转换，包括MP3、WAV、OGG、AAC、FLAC等，可以批量处理多个文件，并支持音质调节',
      en: 'Convert between various audio formats including MP3, WAV, OGG, AAC, FLAC with batch processing and quality adjustment',
      ja: 'MP3、WAV、OGG、AAC、FLACなど様々なオーディオフォーマット間の変換、バッチ処理と品質調整に対応',
    },
    category: 'converter',
    icon: Volume2,
    path: '/tools/audio-converter',
    keywords: {
      zh: ['音频转换', '音频格式', 'mp3', 'wav', 'ogg', 'aac', 'flac', '批量转换', '音质调节'],
      en: ['audio converter', 'audio format', 'mp3', 'wav', 'ogg', 'aac', 'flac', 'batch convert', 'quality adjustment'],
      ja: ['オーディオ変換', 'オーディオフォーマット', 'mp3', 'wav', 'ogg', 'aac', 'flac', 'バッチ変換', '品質調整'],
    },
    component: AudioConverterTool,
    isNew: true,
  },
  {
    id: 'gif-frame-extractor',
    name: {
      zh: 'GIF帧分割器',
      en: 'GIF Frame Extractor',
      ja: 'GIFフレーム抽出ツール',
    },
    description: {
      zh: '提取GIF动画中的每一帧，支持预览、选择和批量导出为静态图片',
      en: 'Extract individual frames from GIF animations with preview, selection and batch export to static images',
      ja: 'GIFアニメーションから個別のフレームを抽出し、プレビュー、選択、静止画への一括エクスポートをサポート',
    },
    category: 'image',
    icon: Film,
    path: '/tools/gif-frame-extractor',
    keywords: {
      zh: ['gif', '帧', '分割', '提取', '动画', '导出', '批量', 'frame'],
      en: ['gif', 'frame', 'extract', 'split', 'animation', 'export', 'batch', 'sequence'],
      ja: ['gif', 'フレーム', '抽出', '分割', 'アニメーション', 'エクスポート', 'バッチ', 'シーケンス'],
    },
    component: GifFrameExtractorTool,
    isNew: true,
  },
  {
    id: 'video-trimmer',
    name: {
      zh: '视频剪辑工具',
      en: 'Video Trimmer',
      ja: 'ビデオトリマー',
    },
    description: {
      zh: '精确剪辑视频片段，支持预览、多段剪辑和高质量导出',
      en: 'Precise video trimming with preview, multi-segment editing and high-quality export',
      ja: 'プレビュー、マルチセグメント編集、高品質エクスポート対応の精密ビデオトリミング',
    },
    category: 'image',
    icon: Video,
    path: '/tools/video-trimmer',
    keywords: {
      zh: ['视频', '剪辑', '裁剪', '编辑', '片段', '导出', 'trim', 'cut'],
      en: ['video', 'trim', 'cut', 'edit', 'segment', 'export', 'editor', 'clip'],
      ja: ['ビデオ', 'トリム', 'カット', '編集', 'セグメント', 'エクスポート', 'エディター', 'クリップ'],
    },
    component: VideoTrimmerTool,
    isNew: true,
  },
  {
    id: 'image-collage',
    name: {
      zh: '图片拼图工具',
      en: 'Image Collage Tool',
      ja: '画像コラージュツール',
    },
    description: {
      zh: '创建精美的图片拼贴，支持多种布局模板、自由拖拽和丰富的编辑功能',
      en: 'Create beautiful image collages with multiple layout templates, free drag-and-drop and rich editing features',
      ja: '複数のレイアウトテンプレート、自由なドラッグアンドドロップ、豊富な編集機能で美しい画像コラージュを作成',
    },
    category: 'image',
    icon: Layout,
    path: '/tools/image-collage',
    keywords: {
      zh: ['拼图', '拼贴', '布局', '模板', '编辑', '组合', 'collage', 'layout'],
      en: ['collage', 'layout', 'template', 'combine', 'composition', 'mosaic', 'grid', 'design'],
      ja: ['コラージュ', 'レイアウト', 'テンプレート', '組み合わせ', '構成', 'モザイク', 'グリッド', 'デザイン'],
    },
    component: ImageCollageTool,
    isNew: true,
  },
  {
    id: 'time-difference-calculator',
    name: {
      zh: '时间差计算器',
      en: 'Time Difference Calculator',
      ja: '時間差計算ツール',
    },
    description: {
      zh: '精确计算两个时间点之间的差异，支持多种显示格式、工作日统计和历史记录管理，包含快速预设和实时计算功能',
      en: 'Precisely calculate the difference between two time points with multiple display formats, working day statistics, history management, quick presets and real-time calculation',
      ja: '2つの時間点間の差異を正確に計算、複数の表示形式、営業日統計、履歴管理、クイックプリセット、リアルタイム計算機能付き',
    },
    category: 'calculator',
    icon: Clock,
    path: '/tools/time-difference-calculator',
    keywords: {
      zh: ['时间差', '时间计算', '日期差异', '时间间隔', '工作日', '时间统计', '历史记录', '预设'],
      en: ['time difference', 'time calculation', 'date difference', 'time interval', 'working days', 'time statistics', 'history', 'presets'],
      ja: ['時間差', '時間計算', '日付差異', '時間間隔', '営業日', '時間統計', '履歴', 'プリセット'],
    },
    component: TimeDifferenceCalculatorTool,
    isNew: true,
  },
  {
    id: 'timezone-converter',
    name: {
      zh: '时区转换器',
      en: 'Timezone Converter',
      ja: 'タイムゾーン変换器',
    },
    description: {
      zh: '全球时区转换工具，支持世界时钟显示、精确时区计算和夏令时识别，提供28个主流时区选择',
      en: 'Global timezone conversion tool with world clock display, precise timezone calculation and daylight saving time recognition, supports 28 major timezones',
      ja: '世界時計表示、正確なタイムゾーン計算、夏时间認識機能付きのグローバルタイムゾーン変換ツール、28の主要タイムゾーンをサポート',
    },
    category: 'calculator',
    icon: Globe,
    path: '/tools/timezone-converter',
    keywords: {
      zh: ['时区转换', '世界时钟', '时区', '时差', '夏令时', '全球时间', '时区计算', 'UTC'],
      en: ['timezone converter', 'world clock', 'timezone', 'time difference', 'daylight saving', 'global time', 'timezone calculation', 'UTC'],
      ja: ['タイムゾーン変換', '世界時計', 'タイムゾーン', '時差', '夏時間', 'グローバル時間', 'タイムゾーン計算', 'UTC'],
    },
    component: TimezoneConverterTool,
    isNew: true,
  },
  {
    id: 'date-arithmetic-calculator',
    name: {
      zh: '日期加减计算器',
      en: 'Date Arithmetic Calculator',
      ja: '日付加減計算機',
    },
    description: {
      zh: '精确计算日期的加减运算，支持多种时间单位和日期范围计算，提供工作日统计、快速预设和历史记录管理',
      en: 'Precisely calculate date arithmetic with multiple time units and range calculations, provides working day statistics, quick presets and history management',
      ja: '複数の時間単位と範囲計算で日付の加減算を正確に計算、営業日統計、クイックプリセット、履歴管理を提供',
    },
    category: 'calculator',
    icon: Calculator,
    path: '/tools/date-arithmetic-calculator',
    keywords: {
      zh: ['日期计算', '日期加减', '时间计算', '工作日', '日期范围', '日期运算', '时间差', '历史记录'],
      en: ['date calculation', 'date arithmetic', 'time calculation', 'working days', 'date range', 'date operations', 'time difference', 'history'],
      ja: ['日付計算', '日付加減算', '時間計算', '営業日', '日付範囲', '日付演算', '時間差', '履歴'],
    },
    component: DateArithmeticTool,
    isNew: true,
  },
  {
    id: 'business-day-calculator',
    name: {
      zh: '工作日计算器',
      en: 'Business Day Calculator',
      ja: '営業日計算機',
    },
    description: {
      zh: '精确计算两个日期之间的工作日数量，支持多国节假日数据、自定义节假日设置、历史记录管理和结果导出功能',
      en: 'Precisely calculate business days between two dates with multi-country holiday data, custom holiday settings, history management and result export',
      ja: '2つの日付間の営業日数を正確に計算、複数国の祝日データ、カスタム祝日設定、履歴管理、結果エクスポート機能付き',
    },
    category: 'calculator',
    icon: Calendar,
    path: '/tools/business-day-calculator',
    keywords: {
      zh: ['工作日', '营业日', '工作日计算', '节假日', '假期', '日期计算', '排除周末', '历史记录', '导出'],
      en: ['business days', 'working days', 'workday calculation', 'holidays', 'weekends', 'date calculation', 'exclude weekends', 'history', 'export'],
      ja: ['営業日', '稼働日', '営業日計算', '祝日', '休日', '日付計算', '週末除外', '履歴', 'エクスポート'],
    },
    component: BusinessDayCalculatorTool,
    isNew: true,
  },
  {
    id: 'image-blur',
    name: {
      zh: '图片模糊处理工具',
      en: 'Image Blur Tool',
      ja: '画像ぼかしツール',
    },
    description: {
      zh: '智能图片模糊处理工具，支持矩形、圆形、多边形模糊区域绘制，提供隐私保护和敏感信息遮挡功能',
      en: 'Smart image blur tool with rectangle, circle, polygon blur area drawing for privacy protection and sensitive information masking',
      ja: '矩形、円形、多角形のぼかし領域描画に対応したスマート画像ぼかしツール、プライバシー保護と機密情報マスキング機能付き',
    },
    category: 'image',
    icon: Scissors,
    path: '/tools/image-blur',
    keywords: {
      zh: ['图片模糊', '模糊处理', '隐私保护', '敏感信息', '马赛克', '遮挡', '图片编辑', '模糊区域'],
      en: ['image blur', 'blur processing', 'privacy protection', 'sensitive information', 'mosaic', 'masking', 'image editing', 'blur area'],
      ja: ['画像ぼかし', 'ぼかし処理', 'プライバシー保護', '機密情報', 'モザイク', 'マスキング', '画像編集', 'ぼかし領域'],
    },
    component: ImageBlurTool,
    isNew: true,
  },
  {
    id: 'image-mosaic',
    name: {
      zh: '图片马赛克工具',
      en: 'Image Mosaic Tool',
      ja: '画像モザイクツール',
    },
    description: {
      zh: '智能图片马赛克处理工具，支持像素化、模糊、颜色马赛克等多种效果，提供矩形、圆形、多边形区域绘制功能',
      en: 'Smart image mosaic tool with pixelate, blur, color mosaic effects and rectangle, circle, polygon area drawing',
      ja: 'ピクセル化、ぼかし、カラーモザイク効果と矩形、円形、多角形領域描画機能付きのスマート画像モザイクツール',
    },
    category: 'image',
    icon: Grid,
    path: '/tools/image-mosaic',
    keywords: {
      zh: ['图片马赛克', '马赛克处理', '像素化', '模糊', '颜色马赛克', '隐私保护', '图片编辑', '局部处理'],
      en: ['image mosaic', 'mosaic processing', 'pixelate', 'blur', 'color mosaic', 'privacy protection', 'image editing', 'local processing'],
      ja: ['画像モザイク', 'モザイク処理', 'ピクセル化', 'ぼかし', 'カラーモザイク', 'プライバシー保護', '画像編集', '局所処理'],
    },
    component: ImageMosaicTool,
    isNew: true,
  },
  {
    id: 'css-sprite-generator',
    name: {
      zh: 'CSS精灵图生成器',
      en: 'CSS Sprite Generator',
      ja: 'CSSスプライトジェネレーター',
    },
    description: {
      zh: '专业的CSS精灵图生成工具，支持多种布局算法、可视化编辑、批量处理和完整的导出功能，提高网页性能',
      en: 'Professional CSS sprite generator with multiple layout algorithms, visual editing, batch processing and complete export functionality to improve web performance',
      ja: '複数のレイアウトアルゴリズム、ビジュアル編集、バッチ処理、完全なエクスポート機能を備えたプロフェッショナルなCSSスプライトジェネレーター、ウェブパフォーマンスを向上',
    },
    category: 'developer',
    icon: Grid,
    path: '/tools/css-sprite-generator',
    keywords: {
      zh: ['css', 'sprite', '精灵图', '图标合并', '性能优化', '批量处理', '可视化', '导出'],
      en: ['css', 'sprite', 'icon merge', 'performance optimization', 'batch processing', 'visual', 'export'],
      ja: ['css', 'スプライト', 'アイコン結合', 'パフォーマンス最適化', 'バッチ処理', 'ビジュアル', 'エクスポート'],
    },
    component: CssSpriteGeneratorTool,
    isNew: true,
  },
  {
    id: 'image-batch-renamer',
    name: {
      zh: '图片批量重命名工具',
      en: 'Image Batch Renamer',
      ja: '画像一括リネームツール',
    },
    description: {
      zh: '智能批量重命名图片文件，支持多种命名规则、自定义模式、冲突解决和预览功能，提供序号、日期、哈希等多种命名方式',
      en: 'Smart batch rename image files with multiple naming rules, custom patterns, conflict resolution and preview functionality, supports sequential, date, hash and other naming methods',
      ja: '複数の命名規則、カスタムパターン、競合解決、プレビュー機能付きのスマート画像ファイル一括リネーム、連番、日付、ハッシュなど多様な命名方式をサポート',
    },
    category: 'image',
    icon: FileImage,
    path: '/tools/image-batch-renamer',
    keywords: {
      zh: ['图片重命名', '批量重命名', '文件重命名', '智能命名', '命名规则', '序号命名', '日期命名', '哈希命名', '冲突解决'],
      en: ['image rename', 'batch rename', 'file rename', 'smart naming', 'naming rules', 'sequential naming', 'date naming', 'hash naming', 'conflict resolution'],
      ja: ['画像リネーム', '一括リネーム', 'ファイルリネーム', 'スマート命名', '命名規則', '連番命名', '日付命名', 'ハッシュ命名', '競合解決'],
    },
    component: ImageBatchRenamerTool,
    isNew: true,
  },
  {
    id: 'webp-converter',
    name: {
      zh: 'WebP转换器',
      en: 'WebP Converter',
      ja: 'WebP変換器',
    },
    description: {
      zh: '支持图片格式之间的转换（JPEG、PNG、WebP、GIF、BMP），WebP格式具有更小的文件大小和更好的压缩效果，适合用于网页优化。支持批量转换和质量调节。',
      en: 'Convert between image formats (JPEG, PNG, WebP, GIF, BMP). WebP format offers smaller file sizes and better compression for web optimization. Supports batch conversion and quality adjustment.',
      ja: '画像フォーマット間の変換（JPEG、PNG、WebP、GIF、BMP）をサポート。WebPフォーマットはより小さなファイルサイズと優れた圧縮効果でWeb最適化に適している。バッチ変換と品質調整をサポート。',
    },
    category: 'image',
    icon: ImageIcon,
    path: '/tools/webp-converter',
    keywords: {
      zh: ['webp', '图片转换', '格式转换', 'jpeg', 'png', 'gif', 'bmp', '批量转换', '压缩', '网页优化', '质量调节'],
      en: ['webp', 'image converter', 'format conversion', 'jpeg', 'png', 'gif', 'bmp', 'batch convert', 'compression', 'web optimization', 'quality adjustment'],
      ja: ['webp', '画像変換', 'フォーマット変換', 'jpeg', 'png', 'gif', 'bmp', 'バッチ変換', '圧縮', 'Web最適化', '品質調整'],
    },
    component: WebpConverterTool,
    isNew: true,
  },
  {
    id: 'image-metadata-editor',
    name: {
      zh: '图片元数据编辑器',
      en: 'Image Metadata Editor',
      ja: '画像メタデータエディター',
    },
    description: {
      zh: '查看、编辑和管理图片的EXIF元数据信息，包括相机参数、位置信息、描述信息等。支持批量处理和多种格式导出。',
      en: 'View, edit and manage image EXIF metadata including camera parameters, location info, descriptions. Supports batch processing and multiple export formats.',
      ja: '画像のEXIFメタデータを表示、編集、管理。カメラパラメータ、位置情報、説明などを含む。バッチ処理と複数のエクスポート形式をサポート。',
    },
    category: 'image',
    icon: Camera,
    path: '/tools/image-metadata-editor',
    keywords: {
      zh: ['图片元数据', '元数据编辑', 'EXIF', '相机信息', '位置信息', '批量处理', '元数据管理', '图片信息'],
      en: ['image metadata', 'metadata editor', 'EXIF', 'camera info', 'location info', 'batch processing', 'metadata management', 'image info'],
      ja: ['画像メタデータ', 'メタデータエディター', 'EXIF', 'カメラ情報', '位置情報', 'バッチ処理', 'メタデータ管理', '画像情報'],
    },
    component: ImageMetadataEditorTool,
    isNew: true,
  },
  {
    id: 'image-color-extractor',
    name: {
      zh: '图片颜色提取器',
      en: 'Image Color Extractor',
      ja: '画像カラー抽出ツール',
    },
    description: {
      zh: '从图片中提取主色调，生成调色板，支持K-means聚类和颜色直方图算法，提供多种颜色分类和导出格式',
      en: 'Extract dominant colors from images to generate color palettes with K-means clustering and histogram algorithms, multiple color categorization and export formats',
      ja: '画像から主要な色を抽出してカラーパレットを生成、K-meansクラスタリングとヒストグラムアルゴリズム、複数の色分類とエクスポート形式に対応',
    },
    category: 'image',
    icon: Palette,
    path: '/tools/image-color-extractor',
    keywords: {
      zh: ['图片颜色', '颜色提取', '主色调', '调色板', 'K-means', '颜色直方图', '配色方案', '颜色分析', '设计工具'],
      en: ['image color', 'color extraction', 'dominant colors', 'color palette', 'K-means', 'color histogram', 'color scheme', 'color analysis', 'design tool'],
      ja: ['画像カラー', '色抽出', '主要色', 'カラーパレット', 'K-means', 'カラーヒストグラム', '配色', '色分析', 'デザインツール'],
    },
    component: ImageColorExtractorTool,
    isNew: true,
  },
  {
    id: 'advanced-ocr',
    name: {
      zh: '增强版OCR文字识别',
      en: 'Advanced OCR Text Recognition',
      ja: '拡張OCRテキスト認識',
    },
    description: {
      zh: '高精度图片文字识别工具，支持多语言、批量处理和智能预处理，提供详细的识别分析和可视化结果',
      en: 'High-precision image text recognition tool with multi-language support, batch processing, intelligent preprocessing, detailed analysis and visual results',
      ja: '多言語対応、バッチ処理、インテリジェント前処理、詳細分析、視覚的結果を提供する高精度画像テキスト認識ツール',
    },
    category: 'converter',
    icon: FileText,
    path: '/tools/advanced-ocr',
    keywords: {
      zh: ['OCR', '文字识别', '图片转文字', '批量识别', '多语言', '智能预处理', '文档扫描', '光学字符识别', '增强版'],
      en: ['OCR', 'text recognition', 'image to text', 'batch recognition', 'multi-language', 'intelligent preprocessing', 'document scan', 'optical character recognition', 'advanced'],
      ja: ['OCR', 'テキスト認識', '画像からテキスト', 'バッチ認識', '多言語', 'インテリジェント前処理', 'ドキュメントスキャン', '光学文字認識', '拡張'],
    },
    component: AdvancedOcrTool,
    isNew: true,
  },
  {
    id: 'gif-maker',
    name: {
      zh: 'GIF制作工具',
      en: 'GIF Maker Tool',
      ja: 'GIF作成ツール',
    },
    description: {
      zh: '将多张图片转换为GIF动画，支持自定义帧率、尺寸、质量设置和预览功能，提供多种预设配置和实时编辑',
      en: 'Convert multiple images to GIF animation with custom frame rate, size, quality settings and preview, provides multiple presets and real-time editing',
      ja: '複数の画像をGIFアニメーションに変換、カスタムフレームレート、サイズ、品質設定とプレビュー機能、複数のプリセットとリアルタイム編集を提供',
    },
    category: 'image',
    icon: Film,
    path: '/tools/gif-maker',
    keywords: {
      zh: ['gif', 'gif制作', '动画制作', '图片转gif', '帧率', '动画编辑', '预览', '批量处理', '图片合成'],
      en: ['gif', 'gif maker', 'animation maker', 'images to gif', 'frame rate', 'animation editing', 'preview', 'batch processing', 'image composition'],
      ja: ['gif', 'gif作成', 'アニメーション作成', '画像からgif', 'フレームレート', 'アニメーション編集', 'プレビュー', 'バッチ処理', '画像合成'],
    },
    component: GifMakerTool,
    isNew: true,
  },
  {
    id: 'calendar-generator',
    name: {
      zh: '日历生成器',
      en: 'Calendar Generator',
      ja: 'カレンダージェネレーター',
    },
    description: {
      zh: '创建自定义日历，支持事件管理、多种主题样式配置、HTML导出和可视化编辑功能',
      en: 'Create custom calendars with event management, multiple theme configurations, HTML export and visual editing features',
      ja: 'イベント管理、複数のテーマ設定、HTMLエクスポート、ビジュアル編集機能付きのカスタムカレンダーを作成',
    },
    category: 'productivity',
    icon: Calendar,
    path: '/tools/calendar-generator',
    keywords: {
      zh: ['日历', '日历生成', '事件管理', '主题', '样式', 'HTML导出', '可视化', '自定义日历', '月历'],
      en: ['calendar', 'calendar generator', 'event management', 'theme', 'style', 'HTML export', 'visual', 'custom calendar', 'monthly calendar'],
      ja: ['カレンダー', 'カレンダー生成', 'イベント管理', 'テーマ', 'スタイル', 'HTMLエクスポート', 'ビジュアル', 'カスタムカレンダー', '月間カレンダー'],
    },
    component: CalendarGeneratorTool,
    isNew: true,
  },
]

// 根据 ID 获取工具
export function getToolById(id: string): Tool | undefined {
  return tools.find((tool) => tool.id === id)
}

// 根据分类获取工具
export function getToolsByCategory(category: string): Tool[] {
  return tools.filter((tool) => tool.category === category)
}

// 搜索工具
export function searchTools(query: string, locale: 'zh' | 'en' | 'ja' = 'zh'): Tool[] {
  const normalizedQuery = query.toLowerCase().trim()
  
  if (!normalizedQuery) return tools
  
  return tools.filter((tool) => {
    // 搜索工具名称
    if (tool.name[locale].toLowerCase().includes(normalizedQuery)) {
      return true
    }
    
    // 搜索工具描述
    if (tool.description[locale].toLowerCase().includes(normalizedQuery)) {
      return true
    }
    
    // 搜索关键词
    if (tool.keywords[locale].some(keyword => 
      keyword.toLowerCase().includes(normalizedQuery)
    )) {
      return true
    }
    
    return false
  })
}

// 获取所有工具路径（用于生成静态页面）
export function getAllToolPaths(): string[] {
  return tools.map((tool) => tool.path)
}
