# 🌐 PoorLow Tools - 全球化在线工具站

<div align="center">
  <p>
    <strong>100+ 实用工具，纯浏览器端运行，支持中英日三语</strong>
  </p>
  <p>
    <a href="#功能特性">功能特性</a> •
    <a href="#快速开始">快速开始</a> •
    <a href="#项目结构">项目结构</a> •
    <a href="#开发指南">开发指南</a> •
    <a href="#贡献指南">贡献指南</a>
  </p>
</div>

## 📋 项目简介

PoorLow Tools 是一个功能全面的在线工具集合站，提供超过 100 个实用工具，涵盖文本处理、开发工具、加密安全、图像处理等多个类别。所有工具均在浏览器端运行，无需服务器处理，确保用户数据的隐私和安全。

## ✨ 功能特性

- 🛠️ **100+ 实用工具** - 涵盖开发、设计、生活等多个领域
- 🌍 **多语言支持** - 支持中文、英文、日文三种语言
- 📱 **移动端优化** - 完美适配各种屏幕尺寸
- ⚡ **极速体验** - 静态站点生成，首屏加载 < 1秒
- 🔒 **隐私安全** - 所有数据本地处理，不上传服务器
- 🎨 **深色模式** - 支持明暗主题切换
- ♿ **无障碍支持** - 符合 WCAG 2.1 AA 标准
- 🔍 **SEO 优化** - 每个工具独立 URL，利于搜索引擎收录

## 🚀 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 或 pnpm

### 安装步骤

```bash
# 克隆项目
git clone [your-private-repo-url]
cd poorlow-tools

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm run start
```

### 环境变量配置

创建 `.env.local` 文件：

```env
# 站点配置
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_SITE_NAME=PoorLow Tools

# 分析工具（可选）
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

## 📁 项目结构

```
poorlow-tools/
├── docs/                 # 项目文档
│   ├── requirements.md   # 需求文档
│   └── architecture.md   # 架构设计
├── src/                  # 源代码
│   ├── app/             # Next.js App Router
│   ├── components/      # React 组件
│   ├── lib/            # 工具库和工具实现
│   ├── hooks/          # 自定义 Hooks
│   ├── types/          # TypeScript 类型
│   └── i18n/           # 国际化配置
├── public/              # 静态资源
│   └── locales/        # 翻译文件
└── tests/              # 测试文件
```

## 🛠️ 技术栈

- **框架**: [Next.js 14](https://nextjs.org/) (App Router)
- **语言**: [TypeScript](https://www.typescriptlang.org/)
- **样式**: [TailwindCSS](https://tailwindcss.com/) + [shadcn/ui](https://ui.shadcn.com/)
- **状态管理**: [Zustand](https://zustand-demo.pmnd.rs/)
- **国际化**: [next-intl](https://next-intl-docs.vercel.app/)
- **图标**: [Lucide React](https://lucide.dev/)
- **测试**: [Vitest](https://vitest.dev/) + [Testing Library](https://testing-library.com/)

## 📝 开发指南

### 添加新工具

1. 在 `src/lib/tools/[category]/` 创建工具文件
2. 实现工具组件和逻辑
3. 在工具注册表中注册
4. 添加翻译文件
5. 编写单元测试

详细步骤请参考 [开发文档](./docs/development.md)

### 代码规范

- 使用 ESLint 和 Prettier 保持代码风格一致
- 遵循 TypeScript 严格模式
- 组件使用函数式组件 + Hooks
- 提交信息遵循 Conventional Commits

### 测试

```bash
# 运行单元测试
npm run test

# 运行测试覆盖率
npm run test:coverage

# 运行 E2E 测试
npm run test:e2e
```

## 🤝 贡献指南

本项目为私有项目，如需贡献请遵循以下流程：

1. Fork 项目（内部开发者）
2. 创建功能分支 (`git checkout -b feature/amazing-tool`)
3. 提交更改 (`git commit -m 'feat: add amazing tool'`)
4. 推送到分支 (`git push origin feature/amazing-tool`)
5. 创建 Pull Request

## 📄 许可证

本项目为私有项目，版权所有 © 2025 PoorLow Tools

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [shadcn/ui](https://ui.shadcn.com/) - UI 组件库
- [Vercel](https://vercel.com/) - 部署平台

---

<div align="center">
  <p>用 ❤️ 打造，为提高效率而生</p>
</div>
